import Bugsnag from '@bugsnag/js';
import BugsnagPluginExpress from '@bugsnag/plugin-express';
import express from 'express';
import morgan from 'morgan';
import * as path from 'path';
import views from './controllers/views';
import * as BugsnagHelper from './helpers/bugsnag';
import { Environment, environment } from './helpers/environment';

// Configure required middleware
const jsx = require('node-jsx').install({ harmony: true, extension: '.jsx' });
const proxy = require('http-proxy').createProxyServer({});
const staticAsset = require('static-asset');
const app = express();

BugsnagHelper.start([BugsnagPluginExpress]);
const middleware = Bugsnag.getPlugin('express')!;
if (!middleware) {
  console.error('Failed to load Bugsnag express plugin');
}

if (environment().production) {
  // Init bugsnag only in production
  app.use(middleware.requestHandler);
} else if (environment().development) {
  // Configure proxy and logging
  let proxy_url = '';

  if (process.env.USE_STAGING === 'true') {
    // Proxy through Kraj
    proxy_url = 'https://kraj.cardcastle.co';

    // Override API environment variables
    process.env.API_HOST = process.env.APP_HOST;
    process.env.API_PORT = process.env.PORT;
  } else {
    proxy_url = Environment.apiHost();
  }
  console.log('Using API host: ', proxy_url);

  app.all(/\/(api|uploads)\/*/, async function (req, res) {
    proxy.web(req, res, {
      target: proxy_url,
      changeOrigin: true,
    });
  });

  app.use(morgan('dev'));
} else {
  app.use(morgan('short'));
}

// Health check
app.get('/node/health', (req, res) => res.status(200).json({}));

// Configure view engine
app.use(staticAsset(path.join(__dirname, 'public')));
app.use(express.static(path.join(__dirname, 'public')));
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');
app.use(views());

// Configure error handlers
if (environment().production) {
  app.use(middleware.errorHandler);
}

app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  err && console.error(err);
  res.redirect('/oops');
});

// Listen
const server = app.listen(process.env.PORT || '5000', () => {
  const port = server.address().port;
  const address = process.env.APP_HOST || 'http://cardcastle.test';
  console.log(`Server listening: ${address}:${port}`);
  console.log(`Pair with the MockBot: ${address}:${port}/cardbot-control/${process.env.MOCKBOT_UUID}`);
});
