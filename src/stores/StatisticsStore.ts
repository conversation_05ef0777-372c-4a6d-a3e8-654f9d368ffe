import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { StatisticsPayload } from '../actions/StatisticsActions';
import { ColorStatistics } from '../models/ColorStatistics';
import { ManaStatistics } from '../models/ManaStatistics';
import { MTGCardGroup } from '../models/mtg/MTGCardPage';
import { OverviewStatistics } from '../models/OverviewStatistics';
import { Record } from '../models/Records';
import { SetCompletion } from '../models/SetCompletion';
import { StatComponentStatus, StatisticsMeta, StatisticType } from '../models/Statistics';
import { TagStatisticsItem } from '../models/TagStatisticsItem';
import { TypeStatistics } from '../models/TypeStatistics';

interface IState {
  statistics: Immutable.Map<StatisticType, StatisticsMeta>;
}

class State extends Record<IState>({
  statistics: Immutable.Map<StatisticType, StatisticsMeta>({
    overview: new StatisticsMeta({ statisticsRecord: new OverviewStatistics() }),
    color: new StatisticsMeta({ statisticsRecord: new ColorStatistics() }),
    type: new StatisticsMeta({ statisticsRecord: new TypeStatistics() }),
    mana: new StatisticsMeta({ statisticsRecord: new ManaStatistics() }),
    set_completion: new StatisticsMeta({ statisticsRecord: Immutable.OrderedMap<string, SetCompletion>() }),
    tags: new StatisticsMeta({
      statisticsRecord: Immutable.OrderedMap<string, TagStatisticsItem>(),
    }),
    most_valuable: new StatisticsMeta({ statisticsRecord: new MTGCardGroup() }),
  }),
}) {}

export default class CardListStore extends FluxUtils.ReduceStore<State, Action<StatisticsPayload | any>> {
  public getInitialState(): State {
    return new State();
  }

  public reduce(state: State, action: Action<StatisticsPayload>) {
    switch (action.type) {
      case ActionType.STATISTICS_OVERVIEW:
        return state.set(
          'statistics',
          state.get('statistics').set(
            StatisticType.OVERVIEW,
            new StatisticsMeta({
              statisticsRecord: action.payload.get('overviewStatistics'),
              status: state.get('statistics').get(StatisticType.OVERVIEW).get('status'),
            }),
          ),
        );
      case ActionType.STATISTICS_COLOR:
        return state.set(
          'statistics',
          state.get('statistics').set(
            StatisticType.COLOR,
            new StatisticsMeta({
              statisticsRecord: action.payload.get('colorStatistics'),
              status: state.get('statistics').get(StatisticType.COLOR).get('status'),
            }),
          ),
        );
      case ActionType.STATISTICS_MANA:
        return state.set(
          'statistics',
          state.get('statistics').set(
            StatisticType.MANA,
            new StatisticsMeta({
              statisticsRecord: action.payload.get('manaStatistics'),
              status: state.get('statistics').get(StatisticType.MANA).get('status'),
            }),
          ),
        );
      case ActionType.STATISTICS_TYPE:
        return state.set(
          'statistics',
          state.get('statistics').set(
            StatisticType.TYPE,
            new StatisticsMeta({
              statisticsRecord: action.payload.get('typeStatistics'),
              status: state.get('statistics').get(StatisticType.TYPE).get('status'),
            }),
          ),
        );
      case ActionType.STATISTICS_SET_COMPLETION:
        return state.set(
          'statistics',
          state.get('statistics').set(
            StatisticType.SET_COMPLETION,
            new StatisticsMeta({
              statisticsRecord: action.payload.get('setCompletionMap'),
              status: state.get('statistics').get(StatisticType.SET_COMPLETION).get('status'),
            }),
          ),
        );
      case ActionType.STATISTICS_TAG:
        return state.set(
          'statistics',
          state.get('statistics').set(
            StatisticType.TAGS,
            new StatisticsMeta({
              statisticsRecord: action.payload.get('tagCompletionMap'),
              status: state.get('statistics').get(StatisticType.TAGS).get('status'),
            }),
          ),
        );
      case ActionType.STATISTICS_MOST_VALUABLE:
        return state.set(
          'statistics',
          state.get('statistics').set(
            StatisticType.MOST_VALUABLE,
            new StatisticsMeta({
              statisticsRecord: action.payload.get('cardGroup'),
              status: state.get('statistics').get(StatisticType.MOST_VALUABLE).get('status'),
            }),
          ),
        );
      case ActionType.STATISTICS_STATUS:
        const statisticType = action.payload.get('statisticType');
        const newMeta = state
          .get('statistics')
          .get(statisticType as StatisticType)
          .set('status', action.payload.get('status') as StatComponentStatus);
        return state.set('statistics', state.get('statistics').set(statisticType as StatisticType, newMeta));

      case ActionType.STATISTICS_REFRESH:
        const newStatistics = state
          .get('statistics')
          .map((meta: StatisticsMeta) => meta.set('status', StatComponentStatus.INACTIVE))
          .toMap();
        return state.set('statistics', newStatistics);
    }
    return state;
  }
}
