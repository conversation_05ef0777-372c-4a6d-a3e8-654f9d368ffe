import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { CardPayload } from '../actions/CardActions';
import { CardPanelPayload } from '../actions/CardPanelActions';
import Dispatcher from '../dispatcher/Dispatcher';
import { CollectionTotals } from '../models/Cards';
import { MTGCardPage } from '../models/mtg/MTGCardPage';
import { Record } from '../models/Records';

interface IState {
  readonly cardPage: MTGCardPage;
  readonly publicCollection: boolean;
  readonly collectionTotals: CollectionTotals;
  readonly collectionLoaded: boolean;
}

class State extends Record<IState>({
  cardPage: new MTGCardPage(),
  publicCollection: false,
  collectionTotals: new CollectionTotals(),
  collectionLoaded: false,
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<CardPayload | any>> {
  /**
   * @override
   */
  getInitialState(): State {
    const state = new State((this.getDispatcher() as Dispatcher).getInitialState().CardStore);
    const me = (this.getDispatcher() as Dispatcher).UserStore().getState().get('me');
    return state.merge({
      cardPage: state.get('cardPage').merge({
        cardGrouping: me
          ? me.get('preferences').get('collection').get('grouping')
          : state.get('cardPage').get('cardGrouping'),
        cardSorting: me
          ? me.get('preferences').get('collection').get('sorting')
          : state.get('cardPage').get('cardSorting'),
        cardViewing: me
          ? me.get('preferences').get('collection').get('viewing')
          : state.get('cardPage').get('cardViewing'),
      }),
    });
  }

  /**
   * @override
   */
  reduce(state: State, action: Action<CardPayload>): State {
    switch (action.type) {
      case ActionType.FILTER_ACTIVATE:
      case ActionType.FILTER_CLEAR:
      case ActionType.CARD_PAGE:
        return state.set('cardPage', action.payload.get('cardPage')).set('collectionLoaded', true);
      case ActionType.CARD_PAGE_UPDATE:
        const payloadPage = action.payload.get('cardPage');

        // Update all fields to sync state, except for:
        // Any loaded results or totals - this will clear the current data
        // Grouping/sorting - selections should maintain local state
        const updatedCardPage = state
          .get('cardPage')
          .set('ownerUsername', payloadPage.get('ownerUsername'))
          .set('cardViewing', payloadPage.get('cardViewing'))
          .set('filterOptions', payloadPage.get('filterOptions'));

        return state.set('cardPage', updatedCardPage);
      case ActionType.CARD_PANELS_SAVE: {
        const payload = action.payload as CardPanelPayload;
        return state.set(
          'cardPage',
          state.get('cardPage').updateCardInstances(payload.get('cardGroup').getInstances()),
        );
      }

      case ActionType.CARD_PANELS_REMOVE: {
        const payload = action.payload as CardPanelPayload;
        return state.set('cardPage', state.get('cardPage').removeInstances(payload.get('cardGroup').getInstances()));
      }

      case ActionType.CARD_PAGE_CLEAR_ALL:
        return state
          .set(
            'cardPage',
            state.get('cardPage').set('filterOptions', state.get('cardPage').get('filterOptions').setPage(1)),
          )
          .set('collectionLoaded', false);

      case ActionType.USERS_SET_PUBLIC_COLLECTION:
        return state.merge({
          publicCollection: action.payload as any as boolean,
        });

      case ActionType.COLLECTION_TOTAL:
        return state.merge({
          collectionTotals: action.payload.get('collectionTotals'),
        });

      case ActionType.CARD_STAGING_COMMIT:
      case ActionType.INPUT_SESSION_COMMIT:
      case ActionType.INPUT_SESSION_COMMIT_ALL:
      case ActionType.CARD_STAGING_QUICK_ADD:
      case ActionType.USERS_ME:
      case ActionType.DECKS_LINK_UPDATE:
      case ActionType.DECKS_LINK_ADD_CARD:
      case ActionType.COLLECTION_REFRESH:
        return state.merge({
          collectionLoaded: false,
        });

      case ActionType.COLLECTION_LOADED:
        return state.merge({
          collectionLoaded: true,
        });
    }
    return state;
  }
}
