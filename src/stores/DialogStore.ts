import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { Record } from '../models/Records';

export enum DialogStatus {
  CLOSED = 'closed',
  CARD_ENTRY = 'card-entry-dialog',
  QUICK_ADD = 'quick-add',
  TAG = 'tag',
  QUICK_ADD_TAG = 'quick-add-tag',
  COMMIT_ALL_TAG = 'commit-all-tag',
  STAGING_ACTIONS = 'staging-actions',
  ACTION_ALL = 'action-all',
  DELETE_SESSION = 'delete-session',
  DELETE_ALL_SESSIONS = 'delete-all-sessions',
  SCAN_RESTRICTIONS = 'scan-restrictions',
}

interface IState {
  readonly dialogStatus: DialogStatus;
}

class State extends Record<IState>({
  dialogStatus: DialogStatus.CLOSED,
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<DialogStatus | any>> {
  public getInitialState(): State {
    return new State({ dialogStatus: DialogStatus.CLOSED });
  }

  public reduce(state: State, action: Action<DialogStatus>) {
    switch (action.type) {
      case ActionType.DIALOG_OPEN:
        return state.set('dialogStatus', action.payload);
      case ActionType.DIALOG_CLOSE:
        return new State({ dialogStatus: DialogStatus.CLOSED });
    }
    return state;
  }
}
