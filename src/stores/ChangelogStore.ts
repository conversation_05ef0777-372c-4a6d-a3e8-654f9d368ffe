import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { ChangelogPayload } from '../actions/ChangelogActions';
import Dispatcher from '../dispatcher/Dispatcher';
import { Changelog } from '../models/Changelogs';

export default class extends FluxUtils.ReduceStore<Changelog, Action<ChangelogPayload | any>> {
  /**
   * @override
   */
  getInitialState(): Changelog {
    return new Changelog((this.getDispatcher() as Dispatcher).getInitialState().ChangelogStore);
  }

  /**
   * @override
   */
  reduce(state: Changelog, action: Action<ChangelogPayload>): Changelog {
    switch (action.type) {
      case ActionType.CHANGELOG_PAGE:
        return action.payload;

      case ActionType.CHANGELOG_PAGE_NEXT:
        return state.merge({
          events: state.get('events').concat(action.payload.get('events')).toList(),
          nextPage: action.payload.get('nextPage'),
        });
    }
    return state;
  }
}
