import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import Dispatcher from '../dispatcher/Dispatcher';
import { Record } from '../models/Records';
import { User } from '../models/Users';

interface IState {
  readonly me: User;
  readonly usersByUsername: Immutable.Map<string, User>;
}

class State extends Record<IState>({
  me: new User(),
  usersByUsername: Immutable.Map<string, User>(),
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<User | any>> {
  public getInitialState(): State {
    return new State((this.getDispatcher() as Dispatcher).getInitialState().UserStore);
  }

  public reduce(state: State, action: Action<User>): State {
    switch (action.type) {
      case ActionType.USERS:
        if (state.get('me') && state.get('me').get('id') && state.get('me').get('id') === action.payload.get('id')) {
          // update the currently authenticated user in all parts of the store
          return state.merge({
            me: action.payload,
            usersByUsername: state
              .get('usersByUsername')
              .set(action.payload.get('username').toLowerCase(), action.payload),
          });
        } else {
          // update the user
          return state.set(
            'usersByUsername',
            state.get('usersByUsername').set(action.payload.get('username').toLowerCase(), action.payload),
          );
        }

      case ActionType.USERS_LOGIN:
        return state;
      case ActionType.USERS_PASSWORD:
        return state;
      case ActionType.USERS_ME:
        // add the currently authenticated user
        return state.merge({
          me: action.payload,
          usersByUsername: state
            .get('usersByUsername')
            .set(action.payload.get('username').toLowerCase(), action.payload),
        });

      case ActionType.USERS_LOGOUT:
        this.getDispatcher().waitFor([(this.getDispatcher() as Dispatcher).IntercomStore().getDispatchToken()]);
        return state;
    }
    return state;
  }

  /**
   * Return the user associated with this username.
   */
  public findUserByUsername(username: string): User {
    return this.getState().get('usersByUsername').get(username.toLowerCase());
  }
}
