import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { WindowPayload } from '../actions/WindowActions';
import isServerside from '../helpers/serverside';
import { Record } from '../models/Records';

interface IState {
  readonly width: number;
  readonly height: number;
}

class State extends Record<IState>({
  width: isServerside() ? 0 : window.innerWidth,
  height: isServerside() ? 0 : window.innerHeight,
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<WindowPayload | any>> {
  public getInitialState() {
    return new State();
  }

  public reduce(state: State, action: Action<WindowPayload>) {
    switch (action.type) {
      case ActionType.WINDOW_RESIZE:
        return new State({
          width: isServerside() ? 0 : action.payload.get('width'),
          height: isServerside() ? 0 : action.payload.get('height'),
        });
    }
    return state;
  }

  public isExtraSmall() {
    return this.getState().get('width') < 544;
  }

  public isSmall() {
    return 544 <= this.getState().get('width') && this.getState().get('width') < 768;
  }

  public isMedium() {
    return 768 <= this.getState().get('width') && this.getState().get('width') < 992;
  }

  public isLarge() {
    return 992 <= this.getState().get('width') && this.getState().get('width') < 1200;
  }

  public isExtraLarge() {
    return 1200 <= this.getState().get('width');
  }
}
