import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { CardPanelPayload } from '../actions/CardPanelActions';
import { InputSessionPayload } from '../actions/InputSessionActions';
import { SelectValue } from '../helpers/select';
import { CardInstance } from '../models/CardInstances';
import { Game } from '../models/Game';
import { AnyInputSession } from '../models/InputSession';
import { InputSourceType } from '../models/InputSource';
import { Language } from '../models/Language';
import { Record } from '../models/Records';

export enum SessionState {
  LOADING,
  IDLE,
  DELETING,
  COMMITTING,
}

interface ISessionFilter {
  query?: string;
  sourceType: SelectValue<InputSourceType>;
  game: Game;
}
export class SessionFilter extends Record<ISessionFilter>({
  query: undefined,
  sourceType: 'inactive',
  game: Game.MTG,
}) {
  active(): boolean {
    return this.get('query') != undefined && this.get('sourceType') !== 'inactive';
  }

  toAPI(): any {
    let payload = Immutable.OrderedMap<string, any>();
    if (this.get('sourceType') !== 'inactive') {
      payload = payload.set('source_type', this.get('sourceType'));
    }

    const query = this.get('query');
    if (query && query.length > 0) {
      payload = payload.set('source_name', query);
    }

    payload = payload.set('game', this.get('game'));

    return payload.toJS();
  }
}

interface IState {
  filters: SessionFilter;
  sessionListState: SessionState;
  sessionState: SessionState;
  inputSessions: Immutable.List<AnyInputSession>;
  inputSessionIndex: number;
  languageOptions: Immutable.Map<string, Immutable.List<Language>>;
}

class State extends Record<IState>({
  filters: new SessionFilter(),
  sessionListState: SessionState.LOADING,
  sessionState: SessionState.LOADING,
  inputSessions: Immutable.List<AnyInputSession>(),
  inputSessionIndex: 0,
  languageOptions: Immutable.Map<string, Immutable.List<Language>>(),
}) {
  public findSession(uuid: string): AnyInputSession | undefined {
    return this.get('inputSessions').find((session: AnyInputSession) => session.get('uuid') == uuid);
  }

  public getSession(index: number = this.get('inputSessionIndex')): AnyInputSession {
    return this.get('inputSessions').get(index);
  }
}

export default class InputSessionStore extends FluxUtils.ReduceStore<State, Action<InputSessionPayload | any>> {
  /**
   * @override
   */

  public getInitialState(): State {
    return new State();
  }

  public reduce(state: State, action: Action<InputSessionPayload>): State {
    switch (action.type) {
      case ActionType.INPUT_SESSION_LIST_STATE:
        return state.set('sessionListState', action.payload.get('sessionListState'));
      case ActionType.INPUT_SESSION_LIST_LOAD:
        return state
          .set('sessionListState', SessionState.LOADING)
          .set('filters', action.payload.get('filters'))
          .set('sessionState', SessionState.LOADING)
          .set('inputSessions', action.payload.get('inputSessions'))
          .set('inputSessionIndex', 0);
      case ActionType.INPUT_SESSION_LOAD:
        const loadIndex = action.payload.get('inputSessionIndex');
        const loadSession = this.updatePage(state.getSession(loadIndex), action.payload);
        return this.updateSession(loadSession, state, loadIndex).set('sessionState', SessionState.LOADING);
      case ActionType.INPUT_SESSION_STATE:
        return state.set('sessionState', action.payload.get('sessionState'));
      case ActionType.INPUT_SESSION_PAGE:
        const pageIndex = action.payload.get('inputSessionIndex');
        const targetSession = state.getSession(pageIndex);
        if (!targetSession || targetSession.get('uuid') !== action.payload.get('inputSession').get('uuid')) {
          // Session results have changed - likely due to a change in the session filter (e.g game)
          return state;
        }

        const pageSession = this.updatePage(state.getSession(pageIndex), action.payload);
        return this.updateSession(pageSession, state, pageIndex)
          .set('sessionState', SessionState.IDLE)
          .set('languageOptions', state.get('languageOptions').merge(action.payload.get('languageOptions')));
      case ActionType.INPUT_SESSION_SEARCH:
        return state
          .set('inputSessions', action.payload.get('inputSessions'))
          .set('inputSessionIndex', 0)
          .set('sessionListState', SessionState.IDLE);
      case ActionType.INPUT_SESSION_CREATE:
        return state.set('inputSessions', state.get('inputSessions').push(action.payload.get('inputSession')));
      case ActionType.INPUT_SESSION_DELETE:
      case ActionType.INPUT_SESSION_COMMIT:
        const newSessionList = state.get('inputSessions').delete(action.payload.get('inputSessionIndex'));
        let newIndex = state.get('inputSessionIndex');
        if (newIndex >= newSessionList.size) {
          newIndex = newSessionList.size - 1;
        }
        const nextSession = newSessionList.get(newIndex);
        const loadAfterDelete = nextSession && nextSession.get('cardPage')?.get('totalCount') <= 0;
        const loadingState = loadAfterDelete ? SessionState.LOADING : SessionState.IDLE;
        return state
          .set('inputSessions', newSessionList)
          .set('inputSessionIndex', newIndex)
          .set('sessionState', loadingState);
      case ActionType.INPUT_SESSION_DELETE_ALL:
        return state
          .set('inputSessionIndex', 0)
          .set('inputSessions', Immutable.List<AnyInputSession>())
          .set('sessionListState', SessionState.IDLE);
      case ActionType.INPUT_SESSION_ADD_CARDS:
        const addIndex = action.payload.get('inputSessionIndex');
        const addSession = this.addCards(state.getSession(addIndex), action.payload);
        return this.updateSession(addSession, state, addIndex).set('sessionState', SessionState.IDLE);
      case ActionType.INPUT_SESSION_REMOVE_CARDS:
        const removeIndex = action.payload.get('inputSessionIndex');
        const removeSession = this.removeCards(state.getSession(removeIndex), action.payload);
        return this.updateSession(removeSession, state, removeIndex).set('sessionState', SessionState.IDLE);
      case ActionType.INPUT_SESSION_CHANGE_INDEX:
        const newSession = state.getSession(action.payload.get('inputSessionIndex'));
        const requiresLoading = newSession.get('cardPage').get('totalCount') <= 0;
        const sessionState = requiresLoading ? SessionState.LOADING : SessionState.IDLE;
        return state
          .set('inputSessionIndex', action.payload.get('inputSessionIndex'))
          .set('sessionState', sessionState);
      case ActionType.INPUT_SESSION_COMMIT_ALL:
        return state
          .set('inputSessions', Immutable.List<AnyInputSession>())
          .set('inputSessionIndex', 0)
          .set('sessionListState', SessionState.IDLE);
      case ActionType.INPUT_SESSION_GAME:
        return state
          .set('inputSessions', Immutable.List<AnyInputSession>())
          .set('inputSessionIndex', 0)
          .set('sessionListState', SessionState.LOADING);
      case ActionType.INPUT_SESSION_UPDATE_CARDS:
        if (!state.getSession()) return state;

        const updatedSession = this.updateCardInstances(state.getSession(), action.payload);
        return this.updateSession(updatedSession, state).set(
          'languageOptions',
          state.get('languageOptions').merge(action.payload.get('languageOptions')),
        );
    }
    return state;
  }

  private updateSession(session: AnyInputSession, state: State, index = state.get('inputSessionIndex')): State {
    return state.set('inputSessions', state.get('inputSessions').set(index, session));
  }

  private updatePage(session: AnyInputSession, payload: InputSessionPayload): AnyInputSession {
    const newCardPage = payload.get('cardPage');

    return session.set('cardPage', newCardPage);
  }

  private addCards(session: AnyInputSession, payload: InputSessionPayload): AnyInputSession {
    // TODO: Extend when allowing session editing for Pokemon
    const newValue = payload
      .get('cardInstances')
      .reduce((acc: number, card: CardInstance) => acc + card.get('cardPrice'), 0);
    return session
      .set('totalCount', session.get('totalCount') + payload.get('cardInstances').size)
      .set('totalValue', session.get('totalValue') + newValue);
  }

  // TODO: Refactor payload to use CardGroups and better support Pokemon/Yugioh
  private removeCards(session: AnyInputSession, payload: InputSessionPayload): AnyInputSession {
    let removedValue: number;
    switch (session.get('game')) {
      case Game.MTG:
        removedValue = payload
          .get('cardInstances')
          .reduce((acc: number, card: CardInstance) => acc + card.get('cardPrice'), 0);
        break;
      case Game.POKEMON:
      case Game.YUGIOH:
      case Game.LORCANA:
        removedValue = 0;
        break;
    }
    return session
      .set('totalCount', session.get('totalCount') - payload.get('cardInstances').size)
      .set('totalValue', session.get('totalValue') - removedValue);
  }

  private updateCardInstances(session: AnyInputSession, payload: CardPanelPayload): AnyInputSession {
    return session.set(
      'cardPage',
      session.get('cardPage').updateCardInstances(payload.get('cardGroup').getInstances()),
    );
  }
}
