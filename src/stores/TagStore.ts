import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { CardPanelPayload } from '../actions/CardPanelActions';
import { CardStagingPayload } from '../actions/CardStagingActions';
import { TagPayload } from '../actions/TagActions';
import { Record } from '../models/Records';
import { Tag } from '../models/Tags';

interface IState {
  readonly userTags: Immutable.OrderedSet<Tag>;
  readonly cardTags: Immutable.OrderedSet<Tag>;
}

class State
  extends Record<IState>({
    userTags: Immutable.OrderedSet<Tag>(),
    cardTags: Immutable.OrderedSet<Tag>(),
  })
  implements IState
{
  readonly userTags: Immutable.OrderedSet<Tag>;
  readonly cardTags: Immutable.OrderedSet<Tag>;
}

export default class extends FluxUtils.ReduceStore<State, Action<TagPayload | any>> {
  /**
   * @override
   */
  getInitialState(): State {
    return new State();
  }

  /**
   * @override
   */
  reduce(state: State, action: Action<TagPayload>): State {
    switch (action.type) {
      case ActionType.TAGS_ALL:
        return state.set('userTags', action.payload.get('tags'));

      case ActionType.TAGS_LOOKUP:
        return state.set('cardTags', action.payload.get('tags'));

      case ActionType.TAGS_ADD:
        return state.set(
          'userTags',
          action.payload
            .get('tags')
            .reduce((tags: Immutable.OrderedSet<Tag>, tag: Tag) => tags.add(tag), state.get('userTags')),
        );

      case ActionType.TAGS_REMOVE:
        return state;

      case ActionType.CARD_STAGING_COMMIT:
      // TODO: InputSession payload does not contain tags - but should update tags
      // case ActionType.INPUT_SESSION_COMMIT:
      // case ActionType.INPUT_SESSION_COMMIT_ALL:
      case ActionType.CARD_STAGING_QUICK_ADD:
        return state.set(
          'userTags',
          state
            .get('userTags')
            .concat((action.payload as any as CardStagingPayload).get('tags'))
            .toOrderedSet(),
        );

      case ActionType.CARD_PANELS_SAVE:
        const existingTags = state.get('userTags');
        const cardPanelTags = (action.payload as any as CardPanelPayload).get('tags');

        // Filter tags by name as newly created tags will not have an 'ID' and be considered unique
        // They will be considered distinct from existing tags with the same name that have come from the API
        const uniqueTags = cardPanelTags.filter((tag: Tag) => {
          const name = tag.get('name');
          const filtered = existingTags.filter((tag: Tag) => {
            return tag.get('name') === name;
          });
          return filtered.count() === 0;
        });

        return state.set('userTags', existingTags.concat(uniqueTags).toOrderedSet());
    }
    return state;
  }
}
