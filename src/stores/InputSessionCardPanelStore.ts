import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { CardPanelPayload } from '../actions/CardPanelActions';
import Dispatcher from '../dispatcher/Dispatcher';
import { CardPanel } from '../models/CardPanels';
import { intersectLanguageOptions } from '../models/Language';
import { DisplayMode } from '../views/components/cardpanel/DisplayImageSwitch';

export default class extends FluxUtils.ReduceStore<CardPanel, Action<CardPanelPayload | any>> {
  /**
   * @override
   */
  getInitialState(): CardPanel {
    return new CardPanel((this.getDispatcher() as Dispatcher).getInitialState().InputSessionCardPanelStore);
  }

  /**
   * @override
   */
  reduce(state: CardPanel, action: Action<CardPanelPayload>): CardPanel {
    switch (action.type) {
      case ActionType.INPUT_SESSION_CARD_PANEL_OPEN:
        return new CardPanel({
          x: action.payload.get('x'),
          y: action.payload.get('y'),
          game: action.payload.get('game'),
          cardGroup: action.payload.get('cardGroup'),
          tags: action.payload.get('tags'),
          printingOptionsReady: action.payload.get('printingOptionsReady'),
          languageOptions: intersectLanguageOptions(action.payload.get('languageOptions')),
          cardPanelType: action.payload.get('cardPanelType'),
          displayMode: state.get('displayMode'),
          scanViewed: state.get('displayMode') === DisplayMode.SCAN,
        });

      case ActionType.INPUT_SESSION_PAGE:
      case ActionType.INPUT_SESSION_CARD_PANEL_CLOSE:
        return CardPanel.reset(state);

      // Card panel must reflect updates made from the StagedCardRow
      case ActionType.INPUT_SESSION_UPDATE_CARDS:
        const updatedGroup = state.get('cardGroup').updateInstances(action.payload.get('cardGroup').getInstances());
        return state
          .set('cardGroup', updatedGroup)
          .set('languageOptions', intersectLanguageOptions(action.payload.get('languageOptions')));
      case ActionType.INPUT_SESSION_CARD_PANEL_PRINTING_OPTIONS:
        return state.merge({
          cardGroup: action.payload.get('cardGroup'),
          printingOptionsReady: action.payload.get('printingOptionsReady'),
        });
      case ActionType.INPUT_SESSION_CARD_PANEL_DISPLAY_MODE:
        // Toggle scan viewed if user has selected scan display
        const scanViewed = state.get('scanViewed') || action.payload.get('displayMode') === DisplayMode.SCAN;
        return state.merge({ displayMode: action.payload.get('displayMode'), scanViewed: scanViewed });
    }
    return state;
  }
}
