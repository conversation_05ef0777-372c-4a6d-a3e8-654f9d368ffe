import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { FinishPayload } from '../actions/FinishActions';
import { LorcanaFinish } from '../models/lorcana/LorcanaFinish';
import { PokeFinish } from '../models/pokemon/PokeFinish';
import { Record } from '../models/Records';

interface IState {
  readonly finishes: Immutable.OrderedSet<PokeFinish>;
  readonly lorcanaFinishes: Immutable.OrderedSet<LorcanaFinish>;
}

class State extends Record<IState>({
  finishes: Immutable.OrderedSet(),
  lorcanaFinishes: Immutable.OrderedSet(),
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<FinishPayload | any>> {
  public getInitialState(): State {
    return new State();
  }

  public reduce(state: State, action: Action<FinishPayload>) {
    switch (action.type) {
      case ActionType.LOAD_POKE_FINISHES:
        return state.set('finishes', action.payload.get('finishes'));
      case ActionType.LOAD_LORCANA_FINISHES:
        return state.set('lorcanaFinishes', action.payload.get('lorcanaFinishes'));
    }
    return state;
  }
}
