import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { StackLocationPayload } from '../actions/StackLocationActions';
import Dispatcher from '../dispatcher/Dispatcher';
import { Record } from '../models/Records';
import { StackLocation } from '../models/StackLocation';

interface IState {
  readonly locationPath: Immutable.List<StackLocation>;
}

class State extends Record<IState>({
  locationPath: Immutable.List<StackLocation>(),
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<StackLocationPayload | any>> {
  /**
   * @override
   */
  getInitialState(): State {
    return new State((this.getDispatcher() as Dispatcher).getInitialState().StackLocationPayload);
  }

  /**
   * @override
   */
  reduce(state: State, action: Action<StackLocationPayload>): State {
    /* TODO: Need to reconsider how to handle actions and store for locaiton. Currently it is all done through induvidual states.
    /* Stores and actions may need to be completely rewritten.
    /* See src/views/builder/CommitDialog.tsx for more details.
     */
    switch (action.type) {
      case ActionType.LOCATION_UPDATE_PATH:
        return state.set('locationPath', action.payload.get('locationPath'));
      case ActionType.LOCATION_GET_PARENTS:
      case ActionType.LOCATION_GET_CHILDREN:
      case ActionType.LOCATION_REMOVE_LAST_FROM_STORE:
      case ActionType.LOCATION_CREATE_LOCATIONS_IN_PATH:
      case ActionType.LOCATION_GET_ALL_IN_PATH:
      case ActionType.LOCATION_CREATE:
      case ActionType.LOCATION_UPDATE:
      case ActionType.LOCATION_DELETE:
      case ActionType.LOCATION_ADD_PARENT:
        return state;
    }
    return state;
  }
}
