import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { DeckPayload } from '../actions/DeckActions';
import { Arranging } from '../models/Arranging';
import { Deck } from '../models/Decks';
import { ColorFilter } from '../models/filters/ColorFilter';
import { Grouping } from '../models/Grouping';
import { Legality } from '../models/Legality';
import { Record } from '../models/Records';
import { DeckSorting } from '../models/sorting/DeckSorting';
import { Tag } from '../models/Tags';
import { Viewing } from '../models/Viewing';

interface IState {
  decks: Immutable.OrderedMap<string, Deck>;
  deckTags: Immutable.OrderedMap<string, Tag>;
  view: Viewing;
  query: string;
  fullQuery: string;
  queryColor: ColorFilter;
  color: ColorFilter;
  tags: Immutable.Map<string, Tag>;
  notTags: Immutable.Map<string, Tag>;
  legality?: Legality;
  sorting: DeckSorting;
  grouping: Grouping;
  arranging: Arranging;
  numDecksFiltered: number;
  numDecksTotal: number;
  currentDeck?: Deck;
  currentDeckPublic: boolean;
}

class State extends Record<IState>({
  decks: Immutable.OrderedMap<string, Deck>(),
  deckTags: Immutable.OrderedMap<string, Tag>(),
  view: Viewing.GRID,
  query: '',
  fullQuery: '',
  queryColor: new ColorFilter(),
  color: new ColorFilter(),
  tags: Immutable.Map<string, Tag>(),
  notTags: Immutable.Map<string, Tag>(),
  legality: undefined,
  sorting: DeckSorting.DATE_ADDED_DESC,
  grouping: Grouping.NAME,
  arranging: Arranging.CARD_TYPE,
  numDecksFiltered: 0,
  numDecksTotal: 0,
  currentDeck: undefined,
  currentDeckPublic: false,
}) {}

export default class DeckStore extends FluxUtils.ReduceStore<State, Action<DeckPayload | any>> {
  /**
   * @override
   */

  public getInitialState(): State {
    return new State();
  }

  public reduce(state: State, action: Action<DeckPayload>): State {
    switch (action.type) {
      // The decks were refreshed
      case ActionType.DECKS:
        return state.merge({
          decks: (action.payload.get('decks') as Immutable.List<Deck>).reduce(
            (decks: Immutable.OrderedMap<string, Deck>, deck: Deck) => {
              return decks.set(deck.get('uuid'), deck);
            },
            Immutable.OrderedMap<string, Deck>(),
          ),
          numDecksFiltered: action.payload.get('numDecksFiltered'),
          numDecksTotal: action.payload.get('numDecksTotal'),
        });

      case ActionType.DECKS_VIEW:
        return state.set('view', action.payload.get('viewType'));

      case ActionType.DECKS_NEW_DECK:
        const newDeck = action.payload.get('deck');
        const uuid = newDeck.get('uuid');
        return state.set('decks', state.get('decks').set(uuid, newDeck)).set('currentDeck', newDeck);

      case ActionType.DECKS_UPDATE:
        return state.set(
          'decks',
          state.get('decks').set(action.payload.get('deck').get('uuid'), action.payload.get('deck')),
        );

      case ActionType.DECKS_REMOVE:
        return state;

      case ActionType.DECKS_TAGS:
        return state.merge({
          deckTags: this.mapTags(action.payload.get('tags')),
        });

      case ActionType.DECKS_SELECT_TAG:
        return state.merge({
          tags: state.get('tags').set(action.payload.get('tag').get('name'), action.payload.get('tag')),
        });

      case ActionType.DECKS_UNSELECT_TAG:
        return state.merge({
          tags: state.get('tags').remove(action.payload.get('tag').get('name')),
        });

      // The query was changed
      case ActionType.DECKS_QUERY:
        return state.merge({
          query: action.payload.get('query'),
          fullQuery: action.payload.get('fullQuery'),
          queryColor: action.payload.get('queryColor'),
          decks: action.payload.get('decks').reduce((decks: Immutable.OrderedMap<string, Deck>, deck: Deck) => {
            return decks.set(deck.get('uuid'), deck);
          }, Immutable.OrderedMap<string, Deck>()),
          numDecksFiltered: action.payload.get('numDecksFiltered'),
          numDecksTotal: action.payload.get('numDecksTotal'),
        });

      // The color filtering changed
      case ActionType.DECKS_MANA_COLORS:
        return state.merge({
          color: action.payload.get('color'),
          decks: action.payload.get('decks').reduce((decks: Immutable.OrderedMap<string, Deck>, deck: Deck) => {
            return decks.set(deck.get('uuid'), deck);
          }, Immutable.OrderedMap<string, Deck>()),
          numDecksFiltered: action.payload.get('numDecksFiltered'),
          numDecksTotal: action.payload.get('numDecksTotal'),
        });

      // The legality of decks was changed
      case ActionType.DECKS_LEGALITY:
        return state.merge({
          legality: action.payload.get('legality'),
          decks: action.payload.get('decks').reduce((decks: Immutable.OrderedMap<string, Deck>, deck: Deck) => {
            return decks.set(deck.get('uuid'), deck);
          }, Immutable.OrderedMap<string, Deck>()),
          numDecksFiltered: action.payload.get('numDecksFiltered'),
          numDecksTotal: action.payload.get('numDecksTotal'),
        });

      // The sorting of decks was changed
      case ActionType.DECKS_SORTING:
        return state.merge({
          sorting: action.payload.get('sorting'),
          decks: action.payload.get('decks').reduce((decks: Immutable.OrderedMap<string, Deck>, deck: Deck) => {
            return decks.set(deck.get('uuid'), deck);
          }, Immutable.OrderedMap<string, Deck>()),
          numDecksFiltered: action.payload.get('numDecksFiltered'),
          numDecksTotal: action.payload.get('numDecksTotal'),
        });

      // The grouping of deck boards was changed
      case ActionType.DECKS_GROUPING:
        return state.merge({
          grouping: action.payload.get('grouping'),
        });

      // The arranging of deck boards was changed
      case ActionType.DECKS_ARRANGING:
        return state.merge({
          arranging: action.payload.get('arranging'),
        });

      case ActionType.DECKS_CLEAR_CURRENT:
        return state.merge({ currentDeck: undefined });

      case ActionType.DECKS_UPDATE_PUBLIC:
        return state.merge({ currentDeckPublic: action.payload as any as boolean });
    }
    return state;
  }

  private mapTags(tags: Immutable.List<Tag>): Immutable.Map<string, Tag> {
    return Immutable.Map(tags.map((tag: Tag) => [tag.get('name'), tag]));
  }
}
