import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { InputSourcePayload } from '../actions/InputSourceActions';
import { InputSource } from '../models/InputSource';
import { Record } from '../models/Records';

interface IState {
  inputSources: Immutable.OrderedMap<string, InputSource>;
}

class State extends Record<IState>({
  inputSources: Immutable.OrderedMap<string, InputSource>(),
}) {}

export default class InputSourceStore extends FluxUtils.ReduceStore<State, Action<InputSourcePayload | any>> {
  /**
   * @override
   */

  public getInitialState(): State {
    return new State();
  }

  public reduce(state: State, action: Action<InputSourcePayload>): State {
    switch (action.type) {
      case ActionType.INPUT_SOURCE_SEARCH:
        return state.set('inputSources', action.payload.get('inputSources'));
      case ActionType.INPUT_SOURCE_CREATE:
      case ActionType.INPUT_SOURCE_GET:
      case ActionType.INPUT_SOURCE_UPDATE:
        return state.set(
          'inputSources',
          this.getState()
            .get('inputSources')
            .set(action.payload.get('inputSource').get('name'), action.payload.get('inputSource')),
        );
    }
    return state;
  }
}
