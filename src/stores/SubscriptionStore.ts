import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { SubscriptionsPayload } from '../actions/SubscriptionActions';
import { Record } from '../models/Records';
import { Subscription } from '../models/Subscriptions';

interface IState {
  subscription: Subscription;
}

class State extends Record<IState>({
  subscription: new Subscription(),
}) {}

class SubscriptionStore extends FluxUtils.ReduceStore<State, Action<SubscriptionsPayload | any>> {
  public getInitialState() {
    return new State();
  }

  public reduce(state: State, action: Action<SubscriptionsPayload>): State {
    switch (action.type) {
      case ActionType.SUBSCRIPTIONS:
        return state.set('subscription', action.payload.get('subscription'));
    }
    return state;
  }
}

export default SubscriptionStore;
