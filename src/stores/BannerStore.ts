import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { Banner } from '../models/Banners';

export default class extends FluxUtils.ReduceStore<Banner, Action<Banner | any>> {
  public getInitialState() {
    return new Banner();
  }

  public reduce(state: Banner, action: Action<Banner>) {
    switch (action.type) {
      case ActionType.BANNERS:
        return action.payload;
    }
    return state;
  }
}
