import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { DataService } from '../models/ImportExport';
import { Record } from '../models/Records';

interface IState {
  readonly services: Immutable.List<DataService>;
}

class State extends Record<IState>({
  services: Immutable.List<DataService>(),
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<Immutable.List<DataService>> | any> {
  public getInitialState() {
    return new State();
  }

  public reduce(state: State, action: Action<Immutable.List<DataService>>) {
    switch (action.type) {
      case ActionType.IMPORT_EXPORT_SERVICES:
        return state.set('services', action.payload);
    }
    return state;
  }
}
