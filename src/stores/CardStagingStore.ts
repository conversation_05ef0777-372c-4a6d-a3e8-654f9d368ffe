import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { CardStagingPayload } from '../actions/CardStagingActions';
import { Record } from '../models/Records';
import { Tag } from '../models/Tags';

interface IState {
  readonly tags: Immutable.OrderedMap<string, Tag>;
}

class State extends Record<IState>({
  tags: Immutable.OrderedMap<string, Tag>(),
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<CardStagingPayload | any>> {
  public getInitialState(): State {
    return new State();
  }

  public reduce(state: State, action: Action<CardStagingPayload>): State {
    switch (action.type) {
      case ActionType.CARD_STAGING_QUICK_ADD:
        return state.set('tags', Immutable.OrderedMap<string, Tag>());

      case ActionType.CARD_STAGING_ADD_TAG:
        return state.set(
          'tags',
          action.payload.get('tags').reduce((tags: Immutable.OrderedMap<string, Tag>, tag: Tag) => {
            return tags.set(tag.get('name'), tag);
          }, state.get('tags')),
        );

      case ActionType.CARD_STAGING_REMOVE_TAG:
        return state.set(
          'tags',
          action.payload.get('tags').reduce((tags: Immutable.OrderedMap<string, Tag>, tag: Tag) => {
            return tags.remove(tag.get('name'));
          }, state.get('tags')),
        );
    }
    return state;
  }
}
