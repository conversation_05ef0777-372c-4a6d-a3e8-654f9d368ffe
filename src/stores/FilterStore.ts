import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { FilterPayload } from '../actions/FilterActions';
import Dispatcher from '../dispatcher/Dispatcher';
import { MTGFilter } from '../models/filters/mtg/MTGFilters';
import { Record } from '../models/Records';
import { User } from '../models/Users';

interface IState {
  readonly filterOptions: MTGFilter;
  readonly rawQuery?: string;
}

class State extends Record<IState>({
  filterOptions: new MTGFilter(),
  rawQuery: undefined,
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<FilterPayload | any>> {
  public getInitialState() {
    const state = new State();
    const me = (this.getDispatcher() as Dispatcher).UserStore().getState().get('me');
    return state.merge({
      filterOptions: new MTGFilter().mergePreferences(me),
    });
  }

  public reduce(state: State, action: Action<FilterPayload>) {
    switch (action.type) {
      case ActionType.FILTER_UPDATE:
      case ActionType.FILTER_ACTIVATE:
        return state.set('filterOptions', action.payload.get('filterOptions'));

      case ActionType.FILTER_QUERY_UPDATE:
        return state.set('rawQuery', action.payload.get('rawQuery'));

      case ActionType.FILTER_CLEAR:
        return state.set('filterOptions', new MTGFilter()).set('rawQuery', undefined);

      case ActionType.USERS_ME:
        const user = action.payload as User;

        const priceFilter = state
          .get('filterOptions')
          .get('priceFilter')
          .set('source', user.get('preferences').get('pricing').get('source'));
        return state.merge({
          filterOptions: state.get('filterOptions').set('priceFilter', priceFilter),
        });
    }
    return state;
  }
}
