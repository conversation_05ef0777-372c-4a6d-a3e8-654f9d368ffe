import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { CardListPayload } from '../actions/CardListActions';
import { CardList, CardListPage, ListedCard } from '../models/CardList';
import { Record } from '../models/Records';
import { CardListSorting } from '../models/sorting/CardListSorting';

interface IState {
  cardListPage?: CardListPage;
  cardLists: Immutable.OrderedMap<string, CardList>;
  cardListSorting: CardListSorting;
}

class State extends Record<IState>({
  cardListPage: undefined,
  cardLists: Immutable.OrderedMap<string, CardList>(),
  cardListSorting: CardListSorting.DATE_EDITED_DESC,
}) {}

export default class CardListStore extends FluxUtils.ReduceStore<State, Action<CardListPayload | any>> {
  /**
   * @override
   */

  public getInitialState(): State {
    return new State();
  }

  public reduce(state: State, action: Action<CardListPayload>): State {
    switch (action.type) {
      case ActionType.CARD_LIST_CREATE:
        const newCardListPage = action.payload.get('cardListPage') as CardListPage; // should never be undefined
        const cardList = newCardListPage.get('cardList');
        const updatedCardLists = this.getState().get('cardLists').set(cardList.get('uuid'), cardList);
        return state.merge({
          cardListPage: newCardListPage,
          cardLists: updatedCardLists,
        });

      case ActionType.CARD_LIST_ADD_CARDS:
        const addedCards = action.payload.get('listedCards');
        const currentPage = this.currentPage();
        const cardListToAdd = currentPage.get('cardList');
        const originalCards = cardListToAdd.get('listedCards');
        const appendedList = addedCards.concat(originalCards).toList();
        const addedCardList = cardListToAdd.set('listedCards', appendedList);
        return state.merge({
          cardListPage: currentPage
            .set('cardList', addedCardList)
            .set('totalCards', currentPage.get('totalCards') + addedCards.count()),
          cardLists: state.get('cardLists').set(addedCardList.get('uuid'), addedCardList),
        });
      case ActionType.CARD_LIST_REMOVE_CARD:
        const listedCards = action.payload.get('listedCards');
        const currentCardListPage = this.currentPage();
        const currentCardList = currentCardListPage.get('cardList');
        const currentListedCards = currentCardList.get('listedCards');
        let updatedList = currentListedCards;
        listedCards.forEach((listedCard: ListedCard) => {
          const index = currentListedCards.findKey(
            (instance: ListedCard) => instance.get('card').get('jsonID') === listedCard.get('card').get('jsonID'),
          );
          updatedList = updatedList.delete(index);
        });

        const newCardList = currentCardList.set('listedCards', updatedList);

        return state.merge({
          cardListPage: currentCardListPage
            .set('cardList', newCardList)
            .set('totalCards', currentCardListPage.get('totalCards') - 1),
          cardLists: state.get('cardLists').set(newCardList.get('uuid'), newCardList),
        });
      case ActionType.CARD_LIST_COMMIT:
        const cardLists = this.getState().get('cardLists');
        const committedCardList = action.payload.get('cardList');
        return state.merge({
          cardLists: cardLists.set(committedCardList.get('uuid'), committedCardList),
        });
      case ActionType.CARD_LIST_DETAILS:
      case ActionType.CARD_LIST_UPDATE:
        return state.merge({
          cardListPage: action.payload.get('cardListPage'),
        });
      case ActionType.CARD_LIST_REMOVE:
        return state.merge({
          cardLists: action.payload.get('cardLists'),
        });
      case ActionType.CARD_LIST_ALL_DETAILS:
        return state.merge({
          cardLists: action.payload.get('cardLists'),
          cardListSorting: action.payload.get('cardListSorting'),
        });
      case ActionType.CARD_LIST_CLEAR:
        return state.merge({
          cardListPage: undefined,
        });
    }
    return state;
  }

  private currentPage(): CardListPage {
    const cardListPage = this.getState().get('cardListPage');

    if (!cardListPage) {
      console.error('No CardListPage in State');
      return new CardListPage();
    } else {
      return cardListPage;
    }
  }
}
