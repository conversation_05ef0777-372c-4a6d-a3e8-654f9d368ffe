import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { CardPanelPayload } from '../actions/CardPanelActions';
import Dispatcher from '../dispatcher/Dispatcher';
import { CardPanel, CardPanelType } from '../models/CardPanels';
import { intersectLanguageOptions } from '../models/Language';
import { DisplayMode } from '../views/components/cardpanel/DisplayImageSwitch';

export default class extends FluxUtils.ReduceStore<CardPanel, Action<CardPanelPayload | any>> {
  /**
   * @override
   */
  getInitialState(): CardPanel {
    return new CardPanel((this.getDispatcher() as Dispatcher).getInitialState().CardPanelStore);
  }

  /**
   * @override
   */
  reduce(state: CardPanel, action: Action<CardPanelPayload>): CardPanel {
    switch (action.type) {
      case ActionType.CARD_PANELS_OPEN:
        return new CardPanel({
          x: action.payload.get('x'),
          y: action.payload.get('y'),
          game: action.payload.get('game'),
          cardGroup: action.payload.get('cardGroup'),
          tags: action.payload.get('tags'),
          printingOptionsReady: action.payload.get('printingOptionsReady'),
          languageOptions: intersectLanguageOptions(action.payload.get('languageOptions')),
          cardPanelType: action.payload.get('cardPanelType'),
          displayMode: state.get('displayMode'),
          scanViewed: state.get('displayMode') === DisplayMode.SCAN,
        });

      case ActionType.CARD_PAGE:
      case ActionType.CARD_PAGE_CLEAR_ALL:
      case ActionType.CARD_VIEW_GRID:
      case ActionType.CARD_VIEW_LIST:
      case ActionType.FILTER_ACTIVATE:
      case ActionType.FILTER_CLEAR:
      case ActionType.CARD_PANELS_CLOSE:
        return CardPanel.reset(state);
      case ActionType.SELECT_OFF_ALL:
        if (state.get('y') === -1) {
          return CardPanel.reset(state);
        } else {
          return state;
        }

      case ActionType.CARD_PANELS_CANCEL:
        return new CardPanel({
          x: state.get('x'),
          y: state.get('y'),
          game: state.get('game'),
          cardGroup: state.get('cardGroup'),
          tags: state.get('tags'),
          printingOptionsReady: state.get('printingOptionsReady'),
          languageOptions: state.get('languageOptions'),
          displayMode: state.get('displayMode'),
          scanViewed: state.get('displayMode') === DisplayMode.SCAN,
        });

      case ActionType.CARD_PANELS_UNSAVED_CHANGE:
        const payloadAttributes = action.payload.get('cardAttributes');
        const oldAttributes = state.get('unsavedCardAttributes');
        const newAttributes = oldAttributes.merge({
          foil: payloadAttributes.get('foil') === undefined ? oldAttributes.get('foil') : payloadAttributes.get('foil'),
          language: payloadAttributes.get('language') || oldAttributes.get('language'),
          condition: payloadAttributes.get('condition') || oldAttributes.get('condition'),
          printing: payloadAttributes.get('printing') || oldAttributes.get('printing'),
        });
        return state.set('unsavedCardAttributes', newAttributes);

      case ActionType.CARD_PANELS_SAVE:
        const savedGroup = state.get('cardGroup').updateInstances(action.payload.get('cardGroup').getInstances());

        return state.merge({
          tags: action.payload.get('tags'),
          cardGroup: savedGroup,
          cardPanelType: action.payload.get('cardPanelType'),
          languageOptions: intersectLanguageOptions(action.payload.get('languageOptions')),
        });
      case ActionType.CARD_PANELS_ADD_TAGS:
        return state.merge({
          tagsToAdd: action.payload.get('tagUpdate'),
        });
      case ActionType.CARD_PANELS_REMOVE_TAGS:
        return state.merge({
          tagsToRemove: action.payload.get('tagUpdate'),
        });
      // Card panel must reflect updates made from the StagedCardRow
      case ActionType.CARD_PANELS_EDIT:
        return state.merge({
          cardPanelType: CardPanelType.COLLECTION_EDIT,
        });
      case ActionType.CARD_PANELS_REMOVE:
        return CardPanel.reset(state);
      case ActionType.CARD_PANELS_PRINTING_OPTIONS:
        return state.merge({
          cardGroup: action.payload.get('cardGroup'),
          printingOptionsReady: action.payload.get('printingOptionsReady'),
        });
      case ActionType.CARD_PANELS_DISPLAY_MODE:
        // Toggle scan viewed if user has selected scan display
        const scanViewed = state.get('scanViewed') || action.payload.get('displayMode') === DisplayMode.SCAN;
        return state.merge({ displayMode: action.payload.get('displayMode'), scanViewed: scanViewed });
    }
    return state;
  }
}
