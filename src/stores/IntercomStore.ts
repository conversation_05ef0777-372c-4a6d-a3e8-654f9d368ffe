import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import Dispatcher from '../dispatcher/Dispatcher';
import isServerside from '../helpers/serverside';
import { StatisticType } from '../models/Statistics';

class IntercomStore extends FluxUtils.MapStore<string, any, any> {
  public getInitialState() {
    return Immutable.Map<string, any>(
      Immutable.fromJS(
        (this.getDispatcher() as Dispatcher).getInitialState().IntercomStore || {
          started: false,
        },
      ),
    );
  }

  public reduce(state: Immutable.Map<string, any>, action: Action<any>) {
    let intercomAction = (state.get('started') as boolean) ? 'update' : 'boot';

    switch (action.type) {
      case ActionType.WINDOW_INIT:
        const settings = {
          app_id: (window as any).INTERCOM_APP_KEY,
        };

        window['intercomSettings'] = settings;
        if (window['Intercom'] !== undefined) window['Intercom'](intercomAction, settings);
        return state;

      case ActionType.USERS_LOGOUT:
        intercomAction = 'shutdown';
      // we do want to fallthrough
      case ActionType.USERS:
      case ActionType.USERS_LOGIN:
      case ActionType.STATISTICS_OVERVIEW:
        if (action.type !== ActionType.USERS_LOGOUT) {
          this.getDispatcher().waitFor([(this.getDispatcher() as Dispatcher).UserStore().getDispatchToken()]);
        }
        this.getDispatcher().waitFor([(this.getDispatcher() as Dispatcher).StatisticsStore().getDispatchToken()]);
        this.getDispatcher().waitFor([(this.getDispatcher() as Dispatcher).TagStore().getDispatchToken()]);
        this.getDispatcher().waitFor([(this.getDispatcher() as Dispatcher).DeckStore().getDispatchToken()]);
        this.getDispatcher().waitFor([(this.getDispatcher() as Dispatcher).SubscriptionStore().getDispatchToken()]);

        const user = (this.getDispatcher() as Dispatcher).UserStore().getState().get('me');
        const totalCount = (this.getDispatcher() as Dispatcher)
          .StatisticsStore()
          .getState()
          .get('statistics')
          .get(StatisticType.OVERVIEW)
          .get('statisticsRecord')
          .get('totalCount');
        const tags = (this.getDispatcher() as Dispatcher).TagStore().getState().get('userTags');
        const decks = (this.getDispatcher() as Dispatcher).DeckStore().getState().get('decks');
        const subscription = (this.getDispatcher() as Dispatcher).SubscriptionStore().getState().get('subscription');

        if (!isServerside() && window['Intercom']) {
          const settings = {
            app_id: (window as any).INTERCOM_APP_KEY,
            email: user.get('emailAddress'),
            name: user.get('username'),
            user_id: user.get('id'),
            created_at: user.get('createdAt'),
            user_hash: user.get('userHash'),
            subscription: user.get('subscribed'),
            subscription_type: subscription.get('name'),
            tag_count: tags.count(),
            card_count: totalCount,
            deck_count: decks.count(),
            public_collection: user.get('preferences').get('privacy').get('public'),
          };

          window['intercomSettings'] = settings;
          window['Intercom'](intercomAction, settings);
        }

        return state.set('started', true);
    }
    return state;
  }
}

export default IntercomStore;
