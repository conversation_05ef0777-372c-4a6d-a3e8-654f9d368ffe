import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import { SelectPayload } from '../actions/SelectActions';
import Dispatcher from '../dispatcher/Dispatcher';
import { Record } from '../models/Records';

interface IState {
  readonly selections: Immutable.Map<number, boolean>;
}

class State extends Record<IState>({
  selections: Immutable.Map<number, boolean>(),
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<SelectPayload | any>> {
  /**
   * @override
   */
  getInitialState(): State {
    return new State((this.getDispatcher() as Dispatcher).getInitialState().SelectStore);
  }

  /**
   * @override
   */
  reduce(state: State, action: Action<SelectPayload>): State {
    switch (action.type) {
      case ActionType.SELECT_ON:
        return state.set(
          'selections',
          action.payload
            .get('cardInstanceIds')
            .reduce(
              (cardInstanceIds: Immutable.Map<number, boolean>, cardInstanceId: number) =>
                cardInstanceIds.set(cardInstanceId, true),
              state.get('selections'),
            ),
        );

      case ActionType.SELECT_OFF:
        return state.set(
          'selections',
          action.payload
            .get('cardInstanceIds')
            .reduce(
              (cardInstanceIds: Immutable.Map<number, boolean>, cardInstanceId: number) =>
                cardInstanceIds.remove(cardInstanceId),
              state.get('selections'),
            ),
        );

      case ActionType.SELECT_OFF_ALL:
        return new State();
    }
    return state;
  }
}
