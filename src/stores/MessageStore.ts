import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { Message } from '../models/Messages';
import { Record } from '../models/Records';

interface IState {
  readonly info: Message | null;
  readonly success: Message | null;
  readonly error: Message | null;
}

class State extends Record<IState>({
  info: null,
  success: null,
  error: null,
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<Message | any>> {
  public getInitialState() {
    return new State();
  }

  public reduce(state: State, action: Action<Message>) {
    switch (action.type) {
      case ActionType.MESSAGES_INFO:
        return new State({
          info: action.payload,
          success: null,
          error: null,
        });

      case ActionType.MESSAGES_SUCCESS:
        return new State({
          info: null,
          success: action.payload,
          error: null,
        });

      case ActionType.MESSAGES_ERROR:
        return new State({
          info: null,
          success: null,
          error: action.payload,
        });

      case ActionType.MESSAGES_CLEAR:
        return new State();
    }
    return state;
  }
}
