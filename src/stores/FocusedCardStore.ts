import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { FilterPayload } from '../actions/FilterActions';
import { FocusedCardPayload } from '../actions/FocusedCardActions';
import { AnyCard } from '../models/AnyCard';
import { Record } from '../models/Records';

interface IState {
  readonly card?: AnyCard;
}

class State extends Record<IState>({
  card: undefined,
}) {}

export default class extends FluxUtils.ReduceStore<State, Action<FilterPayload | any>> {
  public getInitialState(): State {
    return new State();
  }

  public reduce(state: State, action: Action<FocusedCardPayload>) {
    switch (action.type) {
      case ActionType.FOCUSED_CARD_UPDATE:
        return state.set('card', action.payload.get('card'));
    }
    return state;
  }
}
