import * as FluxUtils from 'flux/utils';
import * as Immutable from 'immutable';
import { Action, ActionType } from '../actions/Actions';
import Dispatcher from '../dispatcher/Dispatcher';
import { Snapshot } from '../models/Snapshots';

export default class extends FluxUtils.ReduceStore<
  Immutable.List<Snapshot>,
  Action<{ snapshots: Immutable.List<Snapshot> } | any>
> {
  public getInitialState() {
    return Immutable.fromJS((this.getDispatcher() as Dispatcher).getInitialState().SnapshotStore || []);
  }

  public reduce(state: Immutable.List<Snapshot>, action: Action<{ snapshots: Immutable.List<Snapshot> }>) {
    switch (action.type) {
      case ActionType.SNAPSHOTS:
        return action.payload.snapshots;
    }
    return state;
  }
}
