import * as FluxUtils from 'flux/utils';
import { Action, ActionType } from '../actions/Actions';
import { CardBotPayload } from '../actions/CardBotActions';
import Dispatcher from '../dispatcher/Dispatcher';
import { CardBot, ConnectionStatus } from '../models/CardBot';
import { MTGFilter } from '../models/filters/mtg/MTGFilters';
import { Game } from '../models/Game';
import { ReportType } from '../models/Report';
import { User } from '../models/Users';

export default class extends FluxUtils.ReduceStore<CardBot, Action<CardBotPayload | any>> {
  /**
   * @override
   */
  getInitialState(): CardBot {
    return new CardBot((this.getDispatcher() as Dispatcher).getInitialState().CardBotStore);
  }

  /**
   * @override
   */
  reduce(state: CardBot, action: Action<CardBotPayload>): CardBot {
    switch (action.type) {
      case ActionType.CARDBOT_PAIR:
      case ActionType.CARDBOT_RESET:
      case ActionType.CARDBOT_PAIRING_ERROR:
        return action.payload.get('cardBot');
      case ActionType.CARDBOT_DISCONNECT:
      case ActionType.CARDBOT_SET_PAIRING:
        return state.set('connectionStatus', action.payload.get('cardBot').get('connectionStatus'));
      case ActionType.CARDBOT_UPDATE_STATUS:
        const newReport = action.payload.get('cardBot').get('latestReport');
        if (newReport.get('reportType') === ReportType.PAIRED) {
          state = state.set('connectionStatus', ConnectionStatus.PAIRED);
        }
        if (newReport.get('scanningInfo')) {
          state = state.set('scanningInfo', newReport.get('scanningInfo'));
        }
        return state
          .set('latestReport', newReport)
          .set(
            'connectionStatus',
            newReport.get('reportType') === ReportType.DISCONNECTED
              ? ConnectionStatus.DISCONNECTED
              : state.get('connectionStatus'),
          );
      case ActionType.CARDBOT_FILTER_UPDATE:
        const game = state.get('options').get('game');

        // TODO: Unify filters
        switch (game) {
          case Game.MTG:
            return state.set('filter', action.payload.get('cardBot').get('filter'));
          case Game.POKEMON:
            return state.set('pokeFilter', action.payload.get('cardBot').get('pokeFilter'));
          case Game.YUGIOH:
            return state.set('yugiFilter', action.payload.get('cardBot').get('yugiFilter'));
          case Game.LORCANA:
            return state.set('lorcanaFilter', action.payload.get('cardBot').get('lorcanaFilter'));
        }
      case ActionType.CARDBOT_OPTION_UPDATE:
        let newOptions = action.payload.get('cardBot').get('options');
        const gameChanged = state.get('options').get('game') !== newOptions.get('game');

        if (gameChanged) {
          newOptions = newOptions.set('scanRestrictions', new MTGFilter());
        }

        return state.set('options', newOptions);
      case ActionType.USERS_ME:
        const me = action.payload as User;
        return state.set('filter', state.get('filter').mergePreferences(me));
    }
    return state;
  }
}
