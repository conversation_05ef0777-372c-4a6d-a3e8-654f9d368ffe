import * as Immutable from 'immutable';
import * as DecksAPI from '../api/Decks';
import Dispatcher from '../dispatcher/Dispatcher';
import { Arranging } from '../models/Arranging';
import { CardInstance } from '../models/CardInstances';
import { Card } from '../models/Cards';
import { Deck, DeckBoard, DeckCard } from '../models/Decks';
import { ColorFilter, ColorOption } from '../models/filters/ColorFilter';
import { Grouping } from '../models/Grouping';
import { Legality } from '../models/Legality';
import { Record } from '../models/Records';
import { DeckSorting, DeckSortingCodes } from '../models/sorting/DeckSorting';
import { Tag } from '../models/Tags';
import { Viewing } from '../models/Viewing';
import DeckStore from '../stores/DeckStore';
import { ActionType } from './Actions';

interface IDeckPayload {
  readonly deck: Deck;
  readonly decks: Immutable.List<Deck>;
  readonly numDecksFiltered: number;
  readonly numDecksTotal: number;
  readonly query: string;
  readonly fullQuery: string;
  readonly queryColor: ColorFilter;
  readonly color: ColorFilter;
  readonly legality?: Legality;
  readonly tags: Immutable.List<Tag>;
  readonly tag: Tag;
  readonly viewType: Viewing;
  readonly grouping: Grouping;
  readonly sorting: DeckSorting;
  readonly arranging: Arranging;
}

export class DeckPayload extends Record<IDeckPayload>({
  deck: new Deck(),
  decks: Immutable.List(),
  numDecksFiltered: 0,
  numDecksTotal: 0,
  viewType: Viewing.GRID,
  grouping: Grouping.NAME,
  sorting: DeckSorting.DATE_ADDED_DESC,
  arranging: Arranging.CARD_TYPE,
  query: '',
  fullQuery: '',
  queryColor: new ColorFilter(),
  color: new ColorFilter(),
  legality: undefined,
  tags: Immutable.List(),
  tag: new Tag(),
}) {}

export async function newDeck(dispatcher: Dispatcher) {
  const newDeck = await DecksAPI.newDeck();
  dispatcher.dispatch({
    type: ActionType.DECKS_NEW_DECK,
    payload: new DeckPayload({
      deck: newDeck,
    }),
  });
}

export async function duplicateDeck(uuid: string, dispatcher: Dispatcher) {
  const duplicate = await DecksAPI.duplicateDeck(uuid);
  dispatcher.dispatch({
    type: ActionType.DECKS_NEW_DECK,
    payload: new DeckPayload({
      deck: duplicate,
    }),
  });
  return duplicate;
}

export async function deck(uuid: string, dispatcher: Dispatcher, cookies?: any) {
  const deck = await DecksAPI.deck(uuid, cookies);
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: deck,
    }),
  });
}

export async function updateDeck(deck: Deck, dispatcher: Dispatcher) {
  const updatedDeck = await DecksAPI.updateDeck(deck);
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: updatedDeck,
    }),
  });
}

export async function removeDeck(deck: Deck, dispatcher: Dispatcher) {
  await DecksAPI.removeDeck(deck);
  dispatcher.dispatch({
    type: ActionType.DECKS_REMOVE,
    payload: new DeckPayload({
      deck: deck,
    }),
  });
}

export async function updateDeckBoard(deck: Deck, deckBoard: DeckBoard, dispatcher: Dispatcher) {
  const updatedDeckBoard = await DecksAPI.updateDeckBoard(deck, deckBoard);
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: deck.set(
        'boards',
        deck
          .get('boards')
          .reduce(
            (deckBoards: Immutable.List<DeckBoard>, deckBoard: DeckBoard) =>
              deckBoards.push(deckBoard.get('id') === updatedDeckBoard.get('id') ? updatedDeckBoard : deckBoard),
            Immutable.List<DeckBoard>(),
          )
          .toList(),
      ),
    }),
  });
}

export async function decks(dispatcher: Dispatcher) {
  const payload = await DecksAPI.decks(optsFromDeckStore(dispatcher.DeckStore()));
  dispatcher.dispatch({
    type: ActionType.DECKS,
    payload: new DeckPayload({
      decks: payload.decks,
      numDecksFiltered: payload.numDecksFiltered,
      numDecksTotal: payload.numDecksTotal,
    }),
  });
}

export async function addDeckCard(deck: Deck, board: DeckBoard, card: Card, num: number, dispatcher: Dispatcher) {
  let nextDeck = deck;
  for (let i = 0; i < num; i++) {
    const response = await DecksAPI.addDeckCard(deck, board, card);
    nextDeck = nextDeck.merge({
      cards: nextDeck.get('cards').set(response.card.get('id'), response.card),
      deckCards: nextDeck.get('deckCards').set(response.deckCard.get('id'), response.deckCard),
    });
  }

  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: nextDeck,
    }),
  });
}

export async function removeDeckCard(deck: Deck, deckCard: DeckCard, dispatcher: Dispatcher) {
  await DecksAPI.removeDeckCard(deck, deckCard);
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: deck.merge({
        deckCards: deck.get('deckCards').remove(deckCard.get('id')),
      }),
    }),
  });
}

export async function removeDeckCards(deck: Deck, deckCards: Immutable.List<DeckCard>, dispatcher: Dispatcher) {
  await DecksAPI.removeDeckCards(deck, deckCards);
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: deck.merge({
        deckCards: deckCards.reduce(
          (deckCards: Immutable.Map<number, DeckCard>, deckCard: DeckCard) => deckCards.remove(deckCard.get('id')),
          deck.get('deckCards'),
        ),
      }),
    }),
  });
}

export async function addDeckBoard(deck: Deck, dispatcher: Dispatcher) {
  const deckBoard = await DecksAPI.addDeckBoard(deck);
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: deck.merge({
        boards: deck.get('boards').push(deckBoard),
      }),
    }),
  });
}

export async function removeDeckBoard(deck: Deck, board: DeckBoard, dispatcher: Dispatcher) {
  await DecksAPI.removeDeckBoard(deck, board);
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: deck.merge({
        boards: deck
          .get('boards')
          .filter((candidate: DeckBoard) => candidate.get('id') !== board.get('id'))
          .toList(),
      }),
    }),
  });
}

export async function addLandCard(deck: Deck, color: ColorOption, dispatcher: Dispatcher) {
  const response = await DecksAPI.addLandCard(deck, color);
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: deck.merge({
        deckCards: deck.get('deckCards').set(response.deckCard.get('id'), response.deckCard),
        cards: deck.get('cards').set(response.card.get('id'), response.card),
      }),
    }),
  });
}

export async function clearAll(dispatcher: Dispatcher) {
  await DecksAPI.clearAll();
  dispatcher.dispatch({
    type: ActionType.DECKS,
    payload: new DeckPayload({
      decks: Immutable.List<Deck>(),
      numDecksFiltered: 0,
      numDecksTotal: 0,
    }),
  });
}

export function addTag(deck: Deck, tag: Tag, dispatcher: Dispatcher) {
  DecksAPI.addTag(deck, tag);
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: deck.set('tags', deck.get('tags').set(tag.get('name'), tag)),
    }),
  });
}

export function removeTag(deck: Deck, tag: Tag, dispatcher: Dispatcher) {
  DecksAPI.removeTag(deck, tag);
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE,
    payload: new DeckPayload({
      deck: deck.set('tags', deck.get('tags').remove(tag.get('name'))),
    }),
  });
}

export function view(view: Viewing, dispatcher: Dispatcher) {
  dispatcher.dispatch({
    type: ActionType.DECKS_VIEW,
    payload: new DeckPayload({
      viewType: view,
    }),
  });
}

export async function query(
  fullQuery: string,
  query: string,
  queryColor: ColorFilter,
  tags: Immutable.Map<string, Tag>,
  notTags: Immutable.Map<string, Tag>,
  dispatcher: Dispatcher,
) {
  const opts = optsFromDeckStore(dispatcher.DeckStore());
  opts.query = query;

  const colorOpts = queryColor.mergeWithMana(dispatcher.DeckStore().getState().get('color'));
  opts.colors = colorOpts.toAPI(ColorFilter.onlyColors());
  opts.tags = {
    with: tags
      .map((tag: Tag) => tag.get('name'))
      .toList()
      .toJS(),
    without: notTags
      .map((tag: Tag) => tag.get('name'))
      .toList()
      .toJS(),
  };

  const payload = await DecksAPI.decks(opts);
  dispatcher.dispatch({
    type: ActionType.DECKS_QUERY,
    payload: new DeckPayload({
      query: query,
      fullQuery: fullQuery,
      queryColor: queryColor,
      decks: payload.decks,
      numDecksFiltered: payload.numDecksFiltered,
      numDecksTotal: payload.numDecksTotal,
    }),
  });
}

export async function color(color: ColorFilter, dispatcher: Dispatcher) {
  const opts = optsFromDeckStore(dispatcher.DeckStore());
  const queryColor = dispatcher.DeckStore().getState().get('queryColor');

  const colorOpts = queryColor.mergeWithMana(color);

  opts.colors = colorOpts.toAPI(ColorFilter.onlyColors());

  const payload = await DecksAPI.decks(opts);
  dispatcher.dispatch({
    type: ActionType.DECKS_MANA_COLORS,
    payload: new DeckPayload({
      color: color,
      decks: payload.decks,
      numDecksFiltered: payload.numDecksFiltered,
      numDecksTotal: payload.numDecksTotal,
    }),
  });
}

export async function arranging(arranging: Arranging, dispatcher: Dispatcher) {
  dispatcher.dispatch({
    type: ActionType.DECKS_ARRANGING,
    payload: new DeckPayload({
      arranging: arranging,
    }),
  });
}

export async function grouping(grouping: Grouping, dispatcher: Dispatcher) {
  dispatcher.dispatch({
    type: ActionType.DECKS_GROUPING,
    payload: new DeckPayload({
      grouping: grouping,
    }),
  });
}

export async function legality(dispatcher: Dispatcher, legality?: Legality) {
  const opts = optsFromDeckStore(dispatcher.DeckStore());
  opts.legality = legality ? legality.toLowerCase() : undefined;
  const payload = await DecksAPI.decks(opts);
  dispatcher.dispatch({
    type: ActionType.DECKS_LEGALITY,
    payload: new DeckPayload({
      legality: legality,
      decks: payload.decks,
      numDecksFiltered: payload.numDecksFiltered,
      numDecksTotal: payload.numDecksTotal,
    }),
  });
}

export async function sorting(sorting: DeckSorting, dispatcher: Dispatcher) {
  const opts = optsFromDeckStore(dispatcher.DeckStore());
  opts.sort = DeckSortingCodes[sorting];
  const payload = await DecksAPI.decks(opts);
  dispatcher.dispatch({
    type: ActionType.DECKS_SORTING,
    payload: new DeckPayload({
      sorting: sorting,
      decks: payload.decks,
      numDecksFiltered: payload.numDecksFiltered,
      numDecksTotal: payload.numDecksTotal,
    }),
  });
}

function optsFromDeckStore(deckStore: DeckStore) {
  const state = deckStore.getState();
  const color = state.get('queryColor').mergeWithMana(state.get('color'));
  const legality = state.get('legality');

  return {
    query: state.get('query'),
    tags: {
      with: state
        .get('tags')
        .map((tag: Tag) => tag.get('name'))
        .toList()
        .toJS(),
      without: state
        .get('notTags')
        .map((tag: Tag) => tag.get('name'))
        .toList()
        .toJS(),
    },
    sort: DeckSortingCodes[state.get('sorting')],
    legality: legality ? legality.toLowerCase() : undefined,
    colors: color.toAPI(ColorFilter.onlyColors()),
  };
}

export async function allMyTags(dispatcher: Dispatcher) {
  const tags = await DecksAPI.allMyTags();
  dispatcher.dispatch({
    type: ActionType.DECKS_TAGS,
    payload: new DeckPayload({
      tags: tags,
    }),
  });
}

export function selectTag(tag: Tag, dispatcher: Dispatcher) {
  dispatcher.dispatch({
    type: ActionType.DECKS_SELECT_TAG,
    payload: new DeckPayload({
      tag: tag,
    }),
  });
}

export function unselectTag(tag: Tag, dispatcher: Dispatcher) {
  dispatcher.dispatch({
    type: ActionType.DECKS_UNSELECT_TAG,
    payload: new DeckPayload({
      tag: tag,
    }),
  });
}

export async function queryByTags(tags: Immutable.Map<string, Tag>, dispatcher: Dispatcher) {
  const opts = optsFromDeckStore(dispatcher.DeckStore());

  opts.tags = {
    with: tags
      .map((tag: Tag) => tag.get('name'))
      .toList()
      .toJS(),
    without: [],
  };

  const payload = await DecksAPI.decks(opts);
  dispatcher.dispatch({
    type: ActionType.DECKS,
    payload: new DeckPayload({
      decks: payload.decks,
      numDecksFiltered: payload.numDecksFiltered,
      numDecksTotal: payload.numDecksTotal,
    }),
  });
}

export async function clearCurrentDeck(dispatcher: Dispatcher) {
  dispatcher.dispatch({
    type: ActionType.DECKS_CLEAR_CURRENT,
    payload: new DeckPayload({}),
  });
}

export async function setDeckPublic(deckPublic: boolean, dispatcher: Dispatcher) {
  dispatcher.dispatch({
    type: ActionType.DECKS_UPDATE_PUBLIC,
    payload: deckPublic,
  });
}

export async function linkCardInstance(
  deck: Deck,
  deckCard: DeckCard,
  cardInstance: CardInstance,
  dispatcher: Dispatcher,
) {
  await DecksAPI.linkCardInstance(deck, deckCard, cardInstance);
  dispatcher.dispatch({
    type: ActionType.DECKS_LINK_UPDATE,
    payload: undefined,
  });
}

export async function unlinkCardInstance(
  deckUUID: string,
  deckCardId: number,
  cardInstance: CardInstance,
  dispatcher: Dispatcher,
) {
  await DecksAPI.unlinkCardInstance(deckUUID, deckCardId, cardInstance);
  dispatcher.dispatch({
    type: ActionType.DECKS_LINK_UPDATE,
    payload: undefined,
  });
}
