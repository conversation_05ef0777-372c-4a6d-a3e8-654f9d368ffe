import * as Immutable from 'immutable';
import Dispatcher from '../../dispatcher/Dispatcher';
import { CardAttributes } from '../../models/CardAttributes';
import { CardSetFilter } from '../../models/filters/CardSetFilter';
import { LorcanaCardInstance } from '../../models/lorcana/LorcanaCardInstances';
import { LorcanaCardGroup, LorcanaCardPage } from '../../models/lorcana/LorcanaCardPage';
import { LorcanaCard } from '../../models/lorcana/LorcanaCards';
import { LorcanaPrinting } from '../../models/lorcana/LorcanaPrinting';
import * as request from '../Requests';

export async function refresh(
  _dispatcher: Dispatcher,
  cardPage: LorcanaCardPage,
  cookies?: any,
): Promise<LorcanaCardPage> {
  const cardPageURL = encodeURI(`/api/v3/collection/lorcana/search`);

  const res = await request.post(cardPageURL, cookies).send(cardPage.toAPI()).end(false);

  const lorcanaCards = Immutable.Map<string, any>(res.body.cards)
    .map((data: any) => LorcanaCard.fromAPI(data))
    .toMap();

  cardPage = cardPage.merge({
    totalCount: res.body.total_count,
    totalGroupCount: res.body.total_groups,
    totalValue: res.body.total_value,
    pageCount: res.body.page_count,
    pageValue: res.body.page_value,
    cardGroups: res.body.collection_items.map((collectionItem: any) =>
      LorcanaCardGroup.fromAPI(collectionItem, lorcanaCards),
    ),
  });
  return cardPage;
}

export async function deleteCards(cardInstances: Immutable.List<LorcanaCardInstance>) {
  return request
    .del('/api/v3/lorcana_card_instances')
    .send({
      uuids: cardInstances.map((cardInstance: LorcanaCardInstance) => cardInstance.get('uuid')),
    })
    .end();
}

export async function getPrintings(uuids: Immutable.List<string>) {
  if (uuids.isEmpty()) return Immutable.List<LorcanaPrinting>();

  const res = await request
    .post('/api/v3/lorcana_cards/printings')
    .send({
      uuids: uuids,
    })
    .end();
  const printingArray: LorcanaPrinting[] = res.body.printings.map(LorcanaPrinting.fromAPI);
  return Immutable.List<LorcanaPrinting>(printingArray);
}

export async function update(
  cardInstances: Immutable.List<LorcanaCardInstance>,
  attributes: CardAttributes,
  annotateScan: boolean,
): Promise<Immutable.List<LorcanaCardInstance>> {
  const data = {
    ...attributes.toAPI(),
    uuids: cardInstances.map((cardInstance: LorcanaCardInstance) => cardInstance.get('uuid')),
    annotate_scan: annotateScan,
  };

  const res = await request.patch('/api/v3/lorcana_card_instances').send(data).end(true, true);

  const lorcanaCards = Immutable.Map<string, any>(res.body.cards)
    .map((data: any) => LorcanaCard.fromAPI(data))
    .toMap();

  const updates = Immutable.List<LorcanaCardInstance>(
    res.body.card_instances.map((cardInstance: any) => LorcanaCardInstance.fromAPI(cardInstance, lorcanaCards)),
  );

  return cardInstances
    .zip(updates)
    .map((pair: LorcanaCardInstance[]) => {
      return pair[0].applyUpdates(pair[1]);
    })
    .toList();
}

export async function changeCard(
  cardInstances: Immutable.List<LorcanaCardInstance>,
  uuid: string,
  annotateScan: boolean,
): Promise<Immutable.List<LorcanaCardInstance>> {
  const data = {
    uuid: uuid,
    lorcana_card_instance_uuids: cardInstances.map((cardInstance: LorcanaCardInstance) => cardInstance.get('uuid')),
    annotate_scan: annotateScan,
  };

  const res = await request.patch('/api/v3/lorcana_card_instances/lorcana_card').send(data).end(true, true);

  const lorcanaCards = Immutable.Map<string, any>(res.body.cards)
    .map((data: any) => LorcanaCard.fromAPI(data))
    .toMap();

  const updates = Immutable.List<LorcanaCardInstance>(
    res.body.card_instances.map((cardInstance: any) => LorcanaCardInstance.fromAPI(cardInstance, lorcanaCards)),
  );

  return cardInstances
    .zip(updates)
    .map((pair: LorcanaCardInstance[]) => {
      return pair[0].applyUpdates(pair[1]);
    })
    .toList();
}

export function search(
  query: string,
  setFilter?: Immutable.Set<CardSetFilter>,
): request.RequestWithPromise<Immutable.List<LorcanaCard>> {
  let requestBody: any = {
    name: query.trim(),
    ownable: true,
  };

  if (setFilter && !setFilter.isEmpty()) {
    requestBody = {
      ...requestBody,
      set_names: CardSetFilter.filtersToAPI(setFilter),
    };
  }

  const req = request.post('/api/v3/lorcana_cards/search').send(requestBody);
  const promise = req.end(false).then((res) =>
    Immutable.List(res.body.cards)
      .map((item: any) => LorcanaCard.fromAPI(item))
      .toList(),
  );

  return new request.RequestWithPromise(req, promise);
}
