import { EnumHelper } from '../helpers/enum';
import { MTGFilter } from './filters/mtg/MTGFilters';
import { Record } from './Records';

export enum ScanStatus {
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
}

interface IProps {
  readonly uuid: string;
  readonly scanStatus: ScanStatus;
  readonly filters?: MTGFilter;
}

export class CardBotSessionMetadata extends Record<IProps>({
  uuid: '',
  scanStatus: ScanStatus.ACCEPTED,
}) {
  static fromAPI(data: any): CardBotSessionMetadata {
    return new CardBotSessionMetadata({
      uuid: data.uuid,
      scanStatus: EnumHelper.match(ScanStatus, data.scan_status) as ScanStatus,
      filters: data.filters && MTGFilter.fromAPI(data.filters),
    });
  }
}
