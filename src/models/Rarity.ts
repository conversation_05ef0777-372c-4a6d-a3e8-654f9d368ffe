import { TextFormat } from '../helpers/fmt';

export enum Rarity {
  BASIC_LAND = 'basic land',
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  MYTHIC_RARE = 'mythic rare',
  SPECIAL = 'special',
}

export type RarityKey = keyof typeof Rarity;

export function rarityFromAPI(s: string): Rarity {
  if (!s) {
    console.error(`Invalid rarity ${s} converted to COMMON`);
    return Rarity.COMMON;
  }
  const rarity = s.toLowerCase() as Rarity;
  if (!rarity) {
    // TODO: Add Bugsnag notifier when we work out how to get that working without causing <PERSON><PERSON> to lose it.
    console.error(`Invalid rarity ${s} converted to COMMON`);
    return Rarity.COMMON;
  }
  return rarity;
}

export function rarityToString(rarity: Rarity) {
  return rarity === Rarity.MYTHIC_RARE ? 'Mythic Rare' : TextFormat.capitalizeWord(rarity as string);
}
