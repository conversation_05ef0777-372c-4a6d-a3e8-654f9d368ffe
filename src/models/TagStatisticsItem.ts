import { tagFromApi } from '../api/Tags';
import { Record } from './Records';
import { Tag } from './Tags';

interface ITagStatisticsItem {
  tag: Tag;
  totalCards?: number;
  totalValue?: number;
}

export class TagStatisticsItem extends Record<ITagStatisticsItem>({
  tag: new Tag(),
  totalCards: undefined,
  totalValue: undefined,
}) {
  static fromAPI(tag_counts: any): TagStatisticsItem {
    return new TagStatisticsItem({
      tag: tagFromApi(tag_counts.tag),
      totalCards: tag_counts.total_cards,
      totalValue: tag_counts.total_value,
    });
  }
}
