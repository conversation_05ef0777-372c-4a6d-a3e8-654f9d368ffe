import * as Immutable from 'immutable';
import * as moment from 'moment';
import displayCurrency from '../helpers/currency_helper';
import { EnumHelper } from '../helpers/enum';
import { PaymentSubscriptionType } from './PaymentEnums';
import { Record } from './Records';

interface ISubscription {
  readonly id: number;
  readonly type: SubscriptionType;
  readonly expiresAt: Date;
  readonly reason: string;
  readonly name: string;
  readonly cost: number;
  readonly credit: number;
  readonly active: boolean;
  readonly provider: PaymentSubscriptionType;
}

export enum SubscriptionType {
  SQUIRE = 'squire',
  KNIGHT = 'knight',
  KNIGHT_PROMOTIONAL = 'promotional',
  MERCHANT = 'merchant',
  MERCHANT_PROMOTIONAL = 'merchant_promotional',
}

export function subscriptionTitle(subscriptionType: SubscriptionType) {
  switch (subscriptionType) {
    case SubscriptionType.SQUIRE:
      return 'Squire';
    case SubscriptionType.KNIGHT:
      return 'Knight';
    case SubscriptionType.KNIGHT_PROMOTIONAL:
      return 'Promotional Knight';
    case SubscriptionType.MERCHANT:
      return 'Merchant';
    case SubscriptionType.MERCHANT_PROMOTIONAL:
      return 'Promotional Merchant';
    default:
      // TODO: Setup proper Bugsnags here.
      console.error(`Invalid subscription type: ${subscriptionType}`);
      return SubscriptionType.SQUIRE;
  }
}

export class FreeLimits {
  static readonly CARDS = 1000;
  static readonly TAGS = 10;
  static readonly DECKS = 3;
}

// Generic token class used to create subscriptions from different providers
interface IToken {
  readonly id: string;
}

export class Token extends Record<IToken>({
  id: '',
}) {
  toAPI(): any {
    return { id: this.get('id') };
  }
}

export class Subscription extends Record<ISubscription>({
  id: 0,
  type: SubscriptionType.KNIGHT,
  expiresAt: new Date(),
  reason: '',
  name: '',
  cost: 0,
  credit: 0,
  active: false,
  provider: PaymentSubscriptionType.NONE,
}) {
  /**
   * Convert from an API blob into a Subscription.
   */
  static fromAPI(subscription: any) {
    return new Subscription({
      id: subscription.id,
      type: Subscription.inferType(subscription.type as SubscriptionType, subscription.name as string),
      expiresAt: moment.utc(subscription.expires_at).toDate(),
      reason: subscription.reason,
      name: subscription.name,
      cost: subscription.cost,
      credit: subscription.credit,
      active: subscription.active,
      provider: Subscription.determineProvider(subscription.provider),
    });
  }

  /**
   * Infer subscription type from API. Unfortunately the backend only
   * currently differentiates merchants from knights by name.
   * TODO: Refactor when the backend is prepared for it.
   */

  static inferType(type: SubscriptionType, name: string): SubscriptionType {
    switch (type) {
      case SubscriptionType.SQUIRE:
        return type;
      case SubscriptionType.KNIGHT_PROMOTIONAL:
        if (name === 'Merchant') {
          return SubscriptionType.MERCHANT_PROMOTIONAL;
        } else {
          return SubscriptionType.KNIGHT_PROMOTIONAL;
        }
      case SubscriptionType.KNIGHT: {
        if (name === 'Merchant') {
          return SubscriptionType.MERCHANT;
        } else {
          return SubscriptionType.KNIGHT;
        }
      }
      default:
        // TODO: Setup proper Bugsnags here.
        console.error(`Invalid subscription type: ${type}`);
        return SubscriptionType.SQUIRE;
    }
  }

  static determineProvider(provider: any) {
    if (provider === null) {
      return PaymentSubscriptionType.NONE;
    }
    return EnumHelper.match(PaymentSubscriptionType, provider) as PaymentSubscriptionType;
  }

  isKnight() {
    return Immutable.List([SubscriptionType.KNIGHT, SubscriptionType.KNIGHT_PROMOTIONAL]).includes(this.get('type'));
  }

  isMerchant() {
    return Immutable.List([SubscriptionType.MERCHANT, SubscriptionType.MERCHANT_PROMOTIONAL]).includes(
      this.get('type'),
    );
  }
}

export enum PlanType {
  KNIGHT = 'knight',
  MERCHANT = 'merchant',
}

export enum PlanLength {
  MONTHLY = 'month',
  YEARLY = 'year',
}

export type SpecialOffer = {
  message: string;
  originalPrice: number;
  finePrint: string;
};

export function specialOffers(_planLength: PlanLength, _planType?: PlanType): SpecialOffer | undefined {
  /*
    Example Offer
      {
        message: 'Limited Offer! Save 55%',
        originalPrice: 9000,
        finePrint: 'Pay only $40 for the first year. Subsequent years will be billed at standard pricing.',
      };
  */
  return undefined;
}

export function planTypeFromSubscriptionType(subscriptionType: SubscriptionType): PlanType | undefined {
  switch (subscriptionType) {
    case SubscriptionType.KNIGHT:
      return PlanType.KNIGHT;
    case SubscriptionType.MERCHANT:
      return PlanType.MERCHANT;
    default:
      return undefined;
  }
}

export function apiFromEnums(type: PlanType, length: PlanLength): string {
  switch (type) {
    case PlanType.KNIGHT:
      switch (length) {
        case PlanLength.MONTHLY:
          return 'guildmage-2022';
        case PlanLength.YEARLY:
          return 'guildmage-annual-2022';
      }
    case PlanType.MERCHANT:
      switch (length) {
        case PlanLength.MONTHLY:
          return 'merchant-2022';
        case PlanLength.YEARLY:
          return 'merchant-annual-2022';
      }
  }
}

// Prices in integer cents
export function priceBuilder(type: PlanType, length: PlanLength): number {
  if (type === PlanType.KNIGHT) {
    return length === PlanLength.MONTHLY ? 900 : 9000;
  } else {
    return length === PlanLength.MONTHLY ? 14500 : 145000;
  }
}

export function priceString(type: SubscriptionType, planLength: PlanLength) {
  if (type === SubscriptionType.SQUIRE) {
    return 'FREE';
  }
  const planType = planTypeFromSubscriptionType(type);
  if (!planType) return '';

  const integerCents = priceBuilder(planType, planLength);
  return planType ? `${displayCurrency(integerCents, false)}/${planLength} USD` : '';
}
