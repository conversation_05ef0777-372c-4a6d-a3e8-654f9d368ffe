import * as Immutable from 'immutable';
import { Rarity } from './Rarity';
import { Record } from './Records';

export const ChangelogEventAction = {
  NONE: '',
  ADD: 'ADD',
  REMOVE: 'REMOVE',
};

export const ChangelogEventType = {
  NONE: '',
  CARD: 'CARD',
  TAG: 'TAG',
};

export const ChangelogEventItemType = {
  NONE: '',
  CARD_USER: 'Card',
  TAG: 'Tag',
};

/**
 * A ChangelogEvent models a change that was made to a collection. It contains
 * the information about the type of change, and a list of ChangelogEventItems
 * that describe items included in the change.
 */
interface IChangelogEvent {
  id: number;
  created_at: Date;
  action: string;
  event_type: string;
  items: Immutable.List<ChangelogEventItem>;
}

export class ChangelogEvent extends Record<IChangelogEvent>({
  id: 0,
  created_at: new Date(),
  action: ChangelogEventAction.NONE,
  event_type: ChangelogEventType.NONE,
  items: Immutable.List<ChangelogEventItem>(),
}) {}

/**
 * A ChangelogEventItem models an item that is part of a ChangelogEvent. The
 * item can represent a tag, or a card.
 */
interface IChangelogEventItem {
  type: string;
  // Cards
  card_name: string | null;
  set_name: string | null;
  set_code: string | null;
  rarity: Rarity | null;
  // Tags
  name: string | null;
}

export class ChangelogEventItem extends Record<IChangelogEventItem>({
  type: ChangelogEventType.NONE,

  // Cards
  card_name: null,
  set_name: null,
  set_code: null,
  rarity: null,

  // Tags
  name: null,
}) {}

/**
 * A Changelog models a group of ChangelogEvents for a user's collection.
 */

interface IChangelog {
  events: Immutable.List<ChangelogEvent>;
  nextPage: string | null;
}

export class Changelog extends Record<IChangelog>({
  events: Immutable.List<ChangelogEvent>(),
  nextPage: null,
}) {}
