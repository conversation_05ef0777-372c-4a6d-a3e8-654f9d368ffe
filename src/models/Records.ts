//@ts-nocheck
import * as Immutable from 'immutable';

export interface IRecord<T> {
  public get<K extends keyof T, V extends T[K]>(key: K): V;
  public set<K extends keyof T, V extends T[K]>(key: K, value: V);
  public merge<K extends keyof T, V extends T[K]>(inner: Partial<T> | { [key in K]: V });
}

export function Record<T>(data: Pick<T, keyof T>) {
  return class extends Immutable.Record(data as any) {
    public constructor(inner?: Partial<T>) {
      super(Immutable.fromJS(inner || {}));
    }

    public get<K extends keyof T, V extends T[K]>(key: K): V {
      return super.get(key as string);
    }

    public set<K extends keyof T, V extends T[K]>(key: K, value: V): this {
      return super.set(key as string, value) as this;
    }

    public merge<K extends keyof T, V extends T[K]>(inner: Partial<T> | { [key in K]: V }): this {
      return super.merge(inner as any) as this;
    }
  };
}
