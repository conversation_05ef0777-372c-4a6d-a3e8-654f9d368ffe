import * as Immutable from 'immutable';
import { Record } from './Records';

export enum TypeStatDisplay {
  QUANTITY = 'Quantity',
  VALUE = 'Value',
}

interface ITypeStat {
  totalValue: number;
  totalCards: number;
}

export class TypeStat extends Record<ITypeStat>({ totalValue: 0, totalCards: 0 }) {
  static fromAPI(card_type: any): TypeStat {
    return new TypeStat({
      totalValue: card_type.total_value,
      totalCards: card_type.total_cards,
    });
  }
}

interface ITypeStatistics {
  totalCards: number;
  landCards: TypeStat;
  creatureCards: TypeStat;
  planeswalkerCards: TypeStat;
  instantCards: TypeStat;
  sorceryCards: TypeStat;
  enchantmentCards: TypeStat;
  artifactCards: TypeStat;
  otherCards: TypeStat;
  multitypeCards: TypeStat;
}

export class TypeStatistics extends Record<ITypeStatistics>({
  totalCards: 0,
  landCards: new TypeStat(),
  creatureCards: new TypeStat(),
  planeswalkerCards: new TypeStat(),
  instantCards: new TypeStat(),
  sorceryCards: new TypeStat(),
  enchantmentCards: new TypeStat(),
  artifactCards: new TypeStat(),
  otherCards: new TypeStat(),
  multitypeCards: new TypeStat(),
}) {
  static fromAPI(card_types: any): TypeStatistics {
    const processed = new TypeStatistics({
      totalCards: card_types.total_cards,
      landCards: TypeStat.fromAPI(card_types.type_counts.Land),
      creatureCards: TypeStat.fromAPI(card_types.type_counts.Creature),
      planeswalkerCards: TypeStat.fromAPI(card_types.type_counts.Planeswalker),
      instantCards: TypeStat.fromAPI(card_types.type_counts.Instant),
      sorceryCards: TypeStat.fromAPI(card_types.type_counts.Sorcery),
      enchantmentCards: TypeStat.fromAPI(card_types.type_counts.Enchantment),
      artifactCards: TypeStat.fromAPI(card_types.type_counts.Artifact),
      otherCards: TypeStat.fromAPI(card_types.type_counts.Other),
      multitypeCards: TypeStat.fromAPI(card_types.type_counts.Multitype),
    });
    return processed;
  }

  static typeKeyToColor = Immutable.Map<string, string>({
    Creature: '#4A3587',
    Planeswalker: '#7B7FAF',
    Artifact: '#8E9DD2',
    Enchantment: '#47899B',
    Instant: '#A3C4CD',
    Sorcery: '#A3CDAC',
    Land: '#C4CDA3',
    Other: '#ACA3CD',
    Multitype: '#CDACA3',
  });

  toPlotlyData(): Immutable.OrderedMap<string, TypeStat> {
    return Immutable.OrderedMap<string, TypeStat>({
      Creature: this.get('creatureCards'),
      Planeswalker: this.get('planeswalkerCards'),
      Artifact: this.get('artifactCards'),
      Enchantment: this.get('enchantmentCards'),
      Instant: this.get('instantCards'),
      Sorcery: this.get('sorceryCards'),
      Land: this.get('landCards'),
      Other: this.get('otherCards'),
      Multitype: this.get('multitypeCards'),
    });
  }
}
