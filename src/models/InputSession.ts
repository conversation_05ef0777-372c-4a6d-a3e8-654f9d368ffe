import * as Immutable from 'immutable';
import { DateFromAPI } from '../helpers/time_helper';
import { CardBotSessionMetadata } from './CardBotSessionMetadata';
import { AnyCardPage } from './CardPage';
import { LorcanaFilter } from './filters/lorcana/LorcanaFilters';
import { MTGFilter } from './filters/mtg/MTGFilters';
import { PokeFilter } from './filters/pokemon/PokeFilters';
import { YugiFilter } from './filters/yugioh/YugiFilters';
import { Game } from './Game';
import { Grouping } from './Grouping';
import { InputSource } from './InputSource';
import { LorcanaCardPage } from './lorcana/LorcanaCardPage';
import { MTGCardPage } from './mtg/MTGCardPage';
import { PokeCardPage } from './pokemon/PokeCardPage';
import { Record } from './Records';
import { Sorting } from './sorting/Sorting';
import { YugiCardPage } from './yugioh/YugiCardPage';
export interface IInputSession {
  id: number;
  uuid: string;
  committedAt?: Date;
  userId: number;
  inputSourceId: number;
  game: Game;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  // These are the total of all the cards in the session
  // The card page total will reflect any filters
  totalValue: number;
  totalCount: number;
  cardPage: AnyCardPage;
  metadata?: CardBotSessionMetadata;
}

// Syntactic convenience for accessing common properties to all InputSessions
export type AnyInputSession = MTGInputSession | PokeInputSession | YugiInputSession;

export function InputSessionBase(data: Pick<IInputSession, keyof IInputSession>) {
  return class extends Record<IInputSession>(data) {
    inputSource(sources: Immutable.OrderedMap<string, InputSource>): InputSource | undefined {
      return sources.find((inputSource: InputSource) => inputSource.get('id') == this.get('inputSourceId'));
    }
  };
}

export class MTGInputSession extends InputSessionBase({
  id: 0,
  uuid: '',
  committedAt: undefined,
  userId: 0,
  inputSourceId: 0,
  game: Game.MTG,
  deletedAt: undefined,
  createdAt: new Date(),
  updatedAt: new Date(),
  totalValue: 0,
  totalCount: 0,
  cardPage: new MTGCardPage(),
  metadata: undefined,
}) {
  static fromAPI(properties: Partial<IInputSession>): MTGInputSession {
    return new MTGInputSession({
      ...properties,
      cardPage: new MTGCardPage({
        cardGrouping: Grouping.NONE,
        cardSorting: Sorting.DATE_ADDED_DESC,
        filterOptions: new MTGFilter({ sessionUUID: properties.uuid, staged: !properties.committedAt }),
      }),
    });
  }

  static sessionsFromAPI(list: any[]): Immutable.List<MTGInputSession> {
    return sessionsFromAPI(list, Game.MTG) as Immutable.List<MTGInputSession>;
  }
}

export class PokeInputSession extends InputSessionBase({
  id: 0,
  uuid: '',
  committedAt: undefined,
  userId: 0,
  inputSourceId: 0,
  game: Game.POKEMON,
  deletedAt: undefined,
  createdAt: new Date(),
  updatedAt: new Date(),
  totalValue: 0,
  totalCount: 0,
  cardPage: new PokeCardPage(),
  metadata: undefined,
}) {
  static fromAPI(properties: Partial<IInputSession>): PokeInputSession {
    return new PokeInputSession({
      ...properties,
      cardPage: new PokeCardPage({
        cardGrouping: Grouping.NONE,
        cardSorting: Sorting.DATE_ADDED_DESC,
        filterOptions: new PokeFilter({ sessionUUID: properties.uuid, staged: !properties.committedAt }),
      }),
    });
  }

  static sessionsFromAPI(list: any[]): Immutable.List<PokeInputSession> {
    return sessionsFromAPI(list, Game.POKEMON) as Immutable.List<PokeInputSession>;
  }
}

export class YugiInputSession extends InputSessionBase({
  id: 0,
  uuid: '',
  committedAt: undefined,
  userId: 0,
  inputSourceId: 0,
  game: Game.YUGIOH,
  deletedAt: undefined,
  createdAt: new Date(),
  updatedAt: new Date(),
  totalValue: 0,
  totalCount: 0,
  cardPage: new YugiCardPage(),
  metadata: undefined,
}) {
  static fromAPI(properties: Partial<IInputSession>): YugiInputSession {
    return new YugiInputSession({
      ...properties,
      cardPage: new YugiCardPage({
        cardGrouping: Grouping.NONE,
        cardSorting: Sorting.DATE_ADDED_DESC,
        filterOptions: new YugiFilter({ sessionUUID: properties.uuid, staged: !properties.committedAt }),
      }),
    });
  }

  static sessionsFromAPI(list: any[]): Immutable.List<YugiInputSession> {
    return sessionsFromAPI(list, Game.YUGIOH) as Immutable.List<YugiInputSession>;
  }
}

export function fromAPI(data: any): AnyInputSession {
  const commonProperties: Partial<IInputSession> = {
    id: data.id,
    uuid: data.uuid,
    committedAt: DateFromAPI.nullable(data.committed_at),
    userId: data.user_id,
    inputSourceId: data.input_source_id,
    game: data.game,
    deletedAt: DateFromAPI.nullable(data.deleted_at),
    createdAt: DateFromAPI.nonNullable(data.created_at),
    updatedAt: DateFromAPI.nonNullable(data.update_at),
    totalValue: data.total_value,
    totalCount: data.total_count,
    metadata: data.metadata && CardBotSessionMetadata.fromAPI(data.metadata),
  };

  switch (data.game) {
    case Game.MTG:
      return MTGInputSession.fromAPI(commonProperties);
    case Game.POKEMON:
      return PokeInputSession.fromAPI(commonProperties);
    case Game.YUGIOH:
      return YugiInputSession.fromAPI(commonProperties);
    case Game.LORCANA:
      return LorcanaInputSession.fromAPI(commonProperties);
    default:
      console.log(`Unknown game (${data.game}) for session (${data.uuid})`);
      return new MTGInputSession();
  }
}

export function sessionsFromAPI(list: any[], game: Game): Immutable.List<AnyInputSession> {
  switch (game) {
    case Game.MTG:
      return Immutable.List<MTGInputSession>(list.map((session: any) => fromAPI(session)));
    case Game.POKEMON:
      return Immutable.List<PokeInputSession>(list.map((session: any) => fromAPI(session)));
    case Game.YUGIOH:
      return Immutable.List<YugiInputSession>(list.map((session: any) => fromAPI(session)));
    case Game.LORCANA:
      return Immutable.List<LorcanaInputSession>(list.map((session: any) => fromAPI(session)));
    default:
      console.log(`Unknown game (${game}) for sessions`);
      return Immutable.List<MTGInputSession>();
  }
}

export class LorcanaInputSession extends InputSessionBase({
  id: 0,
  uuid: '',
  committedAt: undefined,
  userId: 0,
  inputSourceId: 0,
  game: Game.LORCANA,
  deletedAt: undefined,
  createdAt: new Date(),
  updatedAt: new Date(),
  totalValue: 0,
  totalCount: 0,
  cardPage: new LorcanaCardPage(),
  metadata: undefined,
}) {
  static fromAPI(properties: Partial<IInputSession>): LorcanaInputSession {
    return new LorcanaInputSession({
      ...properties,
      cardPage: new LorcanaCardPage({
        cardGrouping: Grouping.NONE,
        cardSorting: Sorting.DATE_ADDED_DESC,
        filterOptions: new LorcanaFilter({ sessionUUID: properties.uuid, staged: !properties.committedAt }),
      }),
    });
  }

  static sessionsFromAPI(list: any[]): Immutable.List<LorcanaInputSession> {
    return sessionsFromAPI(list, Game.LORCANA) as Immutable.List<LorcanaInputSession>;
  }
}
