import { List } from 'immutable';
import { Condition } from './Condition';
import { AnyFilter, FilterContext, Filters } from './filters/Filters';
import { LorcanaFilter } from './filters/lorcana/LorcanaFilters';
import { MTGFilter } from './filters/mtg/MTGFilters';
import { PokeFilter } from './filters/pokemon/PokeFilters';
import { YugiFilter } from './filters/yugioh/YugiFilters';
import { Game } from './Game';
import { Language } from './Language';
import { LorcanaFinish } from './lorcana/LorcanaFinish';
import { PokeFinish } from './pokemon/PokeFinish';
import { Record } from './Records';
import { Report, ReportType, ScanningInfo } from './Report';

export interface ICardBotOptions {
  game: Game;
  condition: Condition;
  language: Language;
  createSessions: CreateSessions;
  foil: boolean;
  pokeFinish?: PokeFinish;
  lorcanaFinish?: LorcanaFinish;
  uploadImages: boolean;
  scanRestrictions: AnyFilter;
}

export enum CreateSessions {
  ALL = 'all_cards',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  NONE = 'none',
}

export type CreateSessionsKey = keyof typeof CreateSessions;

export class CardBotOptions extends Record<ICardBotOptions>({
  game: Game.MTG,
  condition: Condition.NEAR_MINT,
  language: Language.ENGLISH,
  createSessions: CreateSessions.ALL,
  foil: false,
  pokeFinish: undefined,
  lorcanaFinish: undefined,
  uploadImages: true,
  scanRestrictions: new MTGFilter(),
}) {
  toAPI(): any {
    const addingCards = this.get('createSessions') !== CreateSessions.NONE;
    const scanRestrictions = this.get('scanRestrictions').toAPI(FilterContext.CARD);
    const commonOptions = {
      create_sessions: this.get('createSessions'),
      add_cards: addingCards, // Deprecated but required for outdated CardBots
      card_type: this.get('game'),
      scan_restrictions: scanRestrictions,
      use_strandcatcher: scanRestrictions !== null && this.get('game') === Game.MTG,
    };

    if (addingCards) {
      const additionalOptions = {
        ...commonOptions,
        quality: this.get('condition'),
        upload_images: this.get('uploadImages'),
      };
      switch (this.get('game')) {
        case Game.MTG:
          return {
            ...additionalOptions,
            language: this.get('language'),
            foil: this.get('foil'),
          };
        case Game.POKEMON:
          if (this.get('pokeFinish')) {
            return {
              ...additionalOptions,
              finish_uuid: this.get('pokeFinish')?.get('uuid'),
            };
          } else {
            return additionalOptions;
          }
        case Game.YUGIOH:
          return additionalOptions;
        case Game.LORCANA:
          if (this.get('lorcanaFinish')) {
            return {
              ...additionalOptions,
              finish_uuid: this.get('lorcanaFinish')?.get('uuid'),
            };
          } else {
            return additionalOptions;
          }
      }
    }

    return commonOptions;
  }
}

export enum ConnectionStatus {
  AVAILABLE = 'AVAILABLE',
  DISCONNECTED = 'DISCONNECTED',
  PAIRING = 'PAIRING',
  PAIRED = 'PAIRED',
  ERROR = 'ERROR',
}

export interface ICardBot {
  deviceID: string;
  mfaToken?: string;
  connectionStatus: ConnectionStatus;
  latestReport: Report;
  scanningInfo?: ScanningInfo;
  filter: MTGFilter;
  pokeFilter: PokeFilter; // TODO Unify filters - there's only ever one type
  yugiFilter: YugiFilter; // TODO Unify filters - there's only ever one type
  lorcanaFilter: LorcanaFilter; // TODO Unify filters - there's only ever one type
  options: CardBotOptions;
  error: string; // if no error then ''
}

export class CardBot extends Record<ICardBot>({
  deviceID: '',
  mfaToken: undefined,
  connectionStatus: ConnectionStatus.AVAILABLE,
  latestReport: new Report({
    reportType: ReportType.PROCESSING,
  }),
  scanningInfo: undefined,
  filter: new MTGFilter(),
  pokeFilter: new PokeFilter(),
  yugiFilter: new YugiFilter(),
  lorcanaFilter: new LorcanaFilter(),
  options: new CardBotOptions(),
  error: '',
}) {
  shortID(): string {
    const split = this.get('deviceID').split('-')[0];
    return split ? split : 'Unknown';
  }
}

export enum CommandType {
  START = 'START',
  STOP = 'STOP',
  RESET = 'RESET',
  OKAY = 'OKAY', // Only a user action, not a valid command to send to a bot
  DISCONNECT = 'DISCONNECT',
}

export interface ICardBotCommand {
  commandType: CommandType;
  startOptions?: StartOptions;
}

export interface IStartOptions {
  filter: any;
  options: CardBotOptions;
}

export class StartOptions extends Record<IStartOptions>({
  filter: null,
  options: new CardBotOptions(),
}) {
  static fromCardBotControl(options: CardBotOptions, filter: Filters): StartOptions {
    return new StartOptions({
      options: options,
      filter: filter,
    });
  }

  toAPI() {
    return {
      ...this.get('options').toAPI(),
      filter: this.get('filter').toAPI(FilterContext.CARD),
    };
  }
}

export class CardBotCommand extends Record<ICardBotCommand>({
  commandType: CommandType.OKAY,
  startOptions: undefined,
}) {
  public toAPI(): any {
    const startOptions = this.get('startOptions');
    if (startOptions) {
      return {
        command: this.get('commandType'),
        ...startOptions.toAPI(),
      };
    } else {
      return {
        command: this.get('commandType'),
      };
    }
  }
}

export function validCommandsFromReport(report: ReportType): List<CommandType> {
  switch (report) {
    case ReportType.PAIRED:
    case ReportType.IDLE:
    case ReportType.SCAN_COMPLETED:
      return List<CommandType>([CommandType.START, CommandType.DISCONNECT]);
    case ReportType.SCANNING:
      return List<CommandType>([CommandType.STOP]);
    case ReportType.ERROR:
      return List<CommandType>([CommandType.RESET, CommandType.DISCONNECT]);
    case ReportType.FATAL_ERROR:
      return List<CommandType>([CommandType.OKAY]);
    default:
      return List<CommandType>([CommandType.DISCONNECT]);
  }
}

export function ignoreReportsFromCommand(command: CommandType): List<ReportType> {
  switch (command) {
    case CommandType.STOP:
      return List<ReportType>([ReportType.SCANNING]);
    case CommandType.DISCONNECT:
      return List<ReportType>([
        ReportType.IDLE,
        ReportType.PAIRED,
        ReportType.PROCESSING,
        ReportType.SCAN_COMPLETED,
        ReportType.SCANNING,
      ]);
    case CommandType.RESET:
      return List<ReportType>([ReportType.ERROR]);
    default:
      return List<ReportType>([]);
  }
}
