import * as Immutable from 'immutable';
import { TextFormat } from '../helpers/fmt';
import { Record } from './Records';

export enum ColorStatDisplay {
  OVERVIEW = 'Overview',
  SINGLE_COLOR = 'Single Color',
  DUAL_COLOR = 'Dual Color',
  TRI_COLOR = 'Tri Color',
  FOUR_FIVE_COLOR = 'Four/Five Color',
}

export interface IColorStatistics {
  totalCards: number;
  white: number;
  blue: number;
  black: number;
  red: number;
  green: number;
  colorless: number;
  white_blue: number;
  white_red: number;
  blue_black: number;
  black_green: number;
  red_green: number;
  blue_red: number;
  white_black: number;
  black_red: number;
  white_green: number;
  blue_green: number;
  white_black_green: number;
  white_blue_green: number;
  white_blue_black: number;
  blue_black_red: number;
  white_blue_red: number;
  black_red_green: number;
  white_black_red: number;
  white_red_green: number;
  blue_black_green: number;
  blue_red_green: number;
  blue_black_red_green: number;
  white_black_red_green: number;
  white_blue_red_green: number;
  white_blue_black_green: number;
  white_blue_black_red: number;
  white_blue_black_red_green: number;
}

export class ColorStatistics extends Record<IColorStatistics>({
  totalCards: 0,
  white: 0,
  blue: 0,
  black: 0,
  red: 0,
  green: 0,
  colorless: 0,
  white_blue: 0,
  white_red: 0,
  blue_black: 0,
  black_green: 0,
  red_green: 0,
  blue_red: 0,
  white_black: 0,
  black_red: 0,
  white_green: 0,
  blue_green: 0,
  white_black_green: 0,
  white_blue_green: 0,
  white_blue_black: 0,
  blue_black_red: 0,
  white_blue_red: 0,
  black_red_green: 0,
  white_black_red: 0,
  white_red_green: 0,
  blue_black_green: 0,
  blue_red_green: 0,
  blue_black_red_green: 0,
  white_black_red_green: 0,
  white_blue_red_green: 0,
  white_blue_black_green: 0,
  white_blue_black_red: 0,
  white_blue_black_red_green: 0,
}) {
  static fromAPI(body: any): ColorStatistics {
    return Immutable.OrderedMap<string, string>(body).reduce((reducer: ColorStatistics, value: string, key: string) => {
      if (key === 'total_cards') {
        return reducer.set('totalCards', Number.parseInt(value));
      }
      return reducer.set(key as keyof IColorStatistics, Number.parseInt(value));
    }, new ColorStatistics());
  }

  static formatTitle(title: string): string {
    const splitString = Immutable.List<string>(title.split('_')).map((value: string) => {
      return TextFormat.capitalizeWord(value);
    });
    if (splitString.size < 2) {
      return splitString.join(' / ');
    }
    return splitString.reduce((reduction: string, value: string) => {
      if (value === 'Blue') {
        return reduction + 'U';
      }
      return reduction + value[0];
    }, '');
  }

  static formatColorCombo(key: string) {
    return Immutable.List<string>(key.split('_')).join('');
  }

  static colorMap = Immutable.OrderedMap<string, string>({
    white: '#FFE48B',
    blue: '#45C3FF',
    black: '#676767',
    red: '#FF5C35',
    green: '#1AC655',
    colorless: '#C5C5C5',
    multicolor: '#EEBC1D',
    // Unfortunately, gradients are not currently supported by plotly.js
    // We use blended colors to represent the simpler multicolors instead
    // https://github.com/plotly/plotly.js/issues/5238
    white_blue: '#A2D5C5',
    blue_black: '#5695B3',
    black_red: '#AB6250',
    red_green: '#8C9145',
    white_green: '#8CD570',
    white_black: '#B3A679',
    blue_red: '#A2909A',
    black_green: '#40965E',
    white_red: '#FFA060',
    blue_green: '#30C4AA',
  });

  public static getColorFromMap(color: string) {
    if (ColorStatistics.colorMap.has(color)) {
      return ColorStatistics.colorMap.get(color);
    } else {
      // Handles three, four and five color combinations.
      return '#EEBC1D';
    }
  }

  public generateMap(display: ColorStatDisplay): Immutable.OrderedMap<string, number> {
    switch (display) {
      case ColorStatDisplay.OVERVIEW:
        const multiColored = this.toMap().reduce((reduction: number, value: number, key: string) => {
          switch (key) {
            case 'totalCards':
            case 'white':
            case 'blue':
            case 'black':
            case 'red':
            case 'green':
            case 'colorless':
              return reduction;
            default:
              return reduction + value;
          }
        }, 0);
        return Immutable.OrderedMap<string, number>({
          white: this.get('white'),
          blue: this.get('blue'),
          black: this.get('black'),
          red: this.get('red'),
          green: this.get('green'),
          colorless: this.get('colorless'),
          multicolor: multiColored,
        });
      case ColorStatDisplay.SINGLE_COLOR:
        return Immutable.OrderedMap<string, number>({
          white: this.get('white'),
          blue: this.get('blue'),
          black: this.get('black'),
          red: this.get('red'),
          green: this.get('green'),
          colorless: this.get('colorless'),
        });
      case ColorStatDisplay.DUAL_COLOR:
        return Immutable.OrderedMap<string, number>({
          white_blue: this.get('white_blue'),
          white_black: this.get('white_black'),
          white_red: this.get('white_red'),
          white_green: this.get('white_green'),
          blue_black: this.get('blue_black'),
          blue_red: this.get('blue_red'),
          blue_green: this.get('blue_green'),
          black_red: this.get('black_red'),
          black_green: this.get('black_green'),
          red_green: this.get('red_green'),
        });
      case ColorStatDisplay.TRI_COLOR:
        return Immutable.OrderedMap<string, number>({
          white_blue_black: this.get('white_blue_black'),
          white_blue_red: this.get('white_blue_red'),
          white_blue_green: this.get('white_blue_green'),
          white_black_red: this.get('white_black_red'),
          white_black_green: this.get('white_black_green'),
          white_red_green: this.get('white_red_green'),
          blue_black_red: this.get('blue_black_red'),
          blue_black_green: this.get('blue_black_green'),
          blue_red_green: this.get('blue_red_green'),
          black_red_green: this.get('black_red_green'),
        });
      case ColorStatDisplay.FOUR_FIVE_COLOR:
        return Immutable.OrderedMap<string, number>({
          white_blue_black_red: this.get('white_blue_black_red'),
          white_blue_black_green: this.get('white_blue_black_green'),
          white_blue_red_green: this.get('white_blue_red_green'),
          white_black_red_green: this.get('white_black_red_green'),
          blue_black_red_green: this.get('blue_black_red_green'),
          white_blue_black_red_green: this.get('white_blue_black_red_green'),
        });
    }
  }
}
