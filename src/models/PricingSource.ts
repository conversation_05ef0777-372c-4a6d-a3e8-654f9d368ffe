export enum PricingSource {
  TCG_PLAYER = 'tcg_player',
  TCG_PLAYER_LOW = 'tcg_player_low',
  TCG_PLAYER_MID = 'tcg_player_mid',
  TCG_PLAYER_HIGH = 'tcg_player_high',
  TCG_PLAYER_DIRECT_LOW = 'tcg_player_direct_low',
  CARD_KINGDOM = 'card_kingdom',
  CARD_MARKET = 'card_market',
}

export function pricingSourceDisplayName(pricingSource: PricingSource): string {
  switch (pricingSource) {
    case PricingSource.TCG_PLAYER:
      return 'TCGplayer: Market';
    case PricingSource.TCG_PLAYER_LOW:
      return 'TCGplayer: Low';
    case PricingSource.TCG_PLAYER_MID:
      return 'TCGplayer: Mid';
    case PricingSource.TCG_PLAYER_HIGH:
      return 'TCGplayer: High';
    case PricingSource.TCG_PLAYER_DIRECT_LOW:
      return 'TCGplayer: Direct Low';
    case PricingSource.CARD_KINGDOM:
      return 'Card Kingdom';
    case PricingSource.CARD_MARKET:
      return 'Card Market';
  }
}
