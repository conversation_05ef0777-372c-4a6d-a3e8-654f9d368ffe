export enum Game {
  MTG = 'mtg',
  POKEMON = 'poke',
  YUGIOH = 'yugi',
  LORCANA = 'lorcana',
}

export type GameKey = keyof typeof Game;

export function inBeta(game: Game): boolean {
  switch (game) {
    case Game.POKEMON:
    case Game.YUGIOH:
    case Game.LORCANA:
      return true;
    default:
      return false;
  }
}

export function isDisabled(game: Game): boolean {
  switch (game) {
    default:
      return false;
  }
}

export function gameName(game: Game): string {
  switch (game) {
    case Game.MTG:
      return 'Magic';
    case Game.POKEMON:
      return 'Pokémon';
    case Game.YUGIOH:
      return 'Yu-Gi-Oh!';
    case Game.LORCANA:
      return 'Lorcana';
    default:
      return game;
  }
}
