import * as Immutable from 'immutable';
import { CardPanel } from './CardPanels';
import { MTGCardGroup } from './mtg/MTGCardPage';

export enum Language {
  ENGLISH = 'en',
  SPANISH = 'es',
  FRENCH = 'fr',
  GERMAN = 'de',
  ITALIAN = 'it',
  PORTUGUESE = 'pt',
  JAPANESE = 'jp',
  KOREAN = 'ko',
  RUSSIAN = 'ru',
  CHINESE_SIMPLIFIED = 'cn',
  CHINESE_TRADITIONAL = 'tw',
  HEBREW = 'he',
  LATIN = 'la',
  ANCIENT_GREEK = 'grc',
  ARABIC = 'ar',
  SANSKRIT = 'sa',
  PHYREXIAN = 'px',
}

export type LanguageKey = keyof typeof Language;

export function AllLanguages(): Immutable.OrderedSet<Language> {
  return Immutable.OrderedSet(Object.keys(Language).map((key) => Language[key as LanguageKey]));
}

export function SortLanguages(languages: Immutable.Set<Language>): Immutable.OrderedSet<Language> {
  return AllLanguages()
    .filter((lang: Language) => languages.contains(lang))
    .toOrderedSet();
}

export function LanguageName(language: Language): string {
  const languageKey =
    Object.keys(Language).find((key) => Language[key as LanguageKey] === language) || Language.ENGLISH;
  if (languageKey) {
    return languageKey
      .split('_')
      .map((word) => {
        return word.charAt(0) + word.slice(1).toLowerCase();
      })
      .join(' ');
  } else {
    return 'English';
  }
}

export function generateLanguageOptions(
  languageOptions: Immutable.Map<string, Immutable.List<Language>>,
  cardGroup: MTGCardGroup,
  cardPanel?: CardPanel,
): Immutable.Set<Language> {
  const newLanguageOptions =
    cardGroup.get('cardInstances').size === 0
      ? undefined
      : languageOptions.get(cardGroup.get('cardInstances').first().get('cardJsonID'));
  if (newLanguageOptions !== undefined) {
    return Immutable.Set(newLanguageOptions);
  }
  if (cardPanel !== undefined) {
    return cardPanel.get('languageOptions');
  }
  return Immutable.Set<Language>();
}

export function validateLanguages(setOfLanguages: Immutable.Set<Language>, currentLanguage: string) {
  if (currentLanguage === 'Multiple') {
    return Immutable.Map<string, any>({ languageValid: true, setOfLanguages: setOfLanguages });
  } else {
    let newSetOfLanguages = setOfLanguages;
    const languageValid = newSetOfLanguages.contains(currentLanguage as Language);
    // Stops an invalid selection defaulting to English.
    if (!languageValid) {
      newSetOfLanguages = newSetOfLanguages.add(currentLanguage as Language);
    }
    return Immutable.Map<string, any>({ languageValid: languageValid, setOfLanguages: newSetOfLanguages });
  }
}

export function intersectLanguageOptions(
  options: Immutable.Map<string, Immutable.List<Language>>,
): Immutable.Set<Language> {
  if (options.isEmpty()) return Immutable.Set();

  const values = options.valueSeq();

  return values.reduce((reduction: Immutable.Set<Language>, value: Immutable.List<Language>) => {
    return reduction.intersect(Immutable.Set(value));
  }, Immutable.Set<Language>(values.first()));
}
