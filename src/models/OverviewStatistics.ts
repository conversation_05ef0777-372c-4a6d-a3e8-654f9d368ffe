import { Record } from './Records';

interface IOverviewStatistics {
  readonly totalCount: number;
  readonly totalValue: number;
  readonly stagingTotalCount: number;
  readonly stagingTotalValue: number;
  readonly deckValue: number;
  readonly deckCount: number;
  readonly tagCount: number;
}

export class OverviewStatistics extends Record<IOverviewStatistics>({
  totalCount: 0,
  totalValue: 0,
  stagingTotalCount: 0,
  stagingTotalValue: 0,
  deckValue: 0,
  deckCount: 0,
  tagCount: 0,
}) {
  /**
   * Convert from an API blob into OverviewStatistics.
   */
  static fromAPI(overview: any) {
    return new OverviewStatistics({
      totalCount: overview.card_count,
      totalValue: overview.card_value,
      stagingTotalCount: overview.staged_count,
      stagingTotalValue: overview.staged_value,
      deckValue: overview.deck_value,
      deckCount: overview.deck_count,
      tagCount: overview.tag_count,
    });
  }
}
