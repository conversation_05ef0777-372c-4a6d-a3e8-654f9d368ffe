import * as Immutable from 'immutable';
import { AnyCardGroup } from './CardGroup';
import { CardInstance } from './CardInstances';
import { Game } from './Game';
import { LorcanaCardInstance } from './lorcana/LorcanaCardInstances';
import { LorcanaCardGroup } from './lorcana/LorcanaCardPage';
import { MTGCardGroup } from './mtg/MTGCardPage';
import { PokeCardInstance } from './pokemon/PokeCardInstances';
import { PokeCardGroup } from './pokemon/PokeCardPage';
import { Rarity } from './Rarity';
import { Record } from './Records';
import { YugiCardInstance } from './yugioh/YugiCardInstances';
import { YugiCardGroup } from './yugioh/YugiCardPage';
import { YugiCardSet } from './yugioh/YugiCards';

export interface ISetIconInfo {
  readonly setCode: string;
  readonly setName: string;
  readonly rarities: Immutable.Set<string>;
}

export class SetIconInfo extends Record<ISetIconInfo>({
  setCode: '',
  setName: '',
  rarities: Immutable.Set<string>(),
}) {
  static fromPokeCardInstance(pokeInstance: PokeCardInstance): SetIconInfo {
    const card = pokeInstance.get('card');
    return new SetIconInfo({
      setName: card.get('setName'),
      setCode: card.get('setUUID'),
      rarities: Immutable.Set<string>([card.get('rarity')]),
    });
  }

  static fromCardInstance(cardInstance: CardInstance): SetIconInfo {
    return new SetIconInfo({
      setName: cardInstance.get('cardSetName'),
      setCode: cardInstance.get('cardSetCode'),
      rarities: Immutable.Set<string>([cardInstance.get('cardRarity')]),
    });
  }

  static fromYugiCardSet(yugiCardSet: YugiCardSet): SetIconInfo {
    return new SetIconInfo({
      setName: yugiCardSet.get('set').get('name'),
      setCode: yugiCardSet.get('set').get('uuid'),
      rarities: Immutable.Set<string>([yugiCardSet.get('rarity')]),
    });
  }

  static fromLorcanaCardInstance(lorcanaInstance: LorcanaCardInstance): SetIconInfo {
    const card = lorcanaInstance.get('card');
    return new SetIconInfo({
      setName: card.get('setName'),
      setCode: card.get('setUUID'),
      rarities: Immutable.Set<string>([card.get('rarity')]),
    });
  }
}

/*
FIXME: Seems like an overcomplicated switch statement.
Seems as though it's trying to find the unique set of set names and rarities
Rarities are contained within SetIconInfo so each value needs to be amended
*/
export function setIconsFromCardGroup(game: Game, cardGroup: AnyCardGroup): Immutable.Set<SetIconInfo> {
  switch (game) {
    case Game.MTG:
      const mtgGroup = cardGroup as MTGCardGroup;
      return mtgGroup
        .getInstances()
        .reduce<Immutable.OrderedMap<string, SetIconInfo>>(
          (reduction: Immutable.OrderedMap<string, SetIconInfo>, cardInstance: CardInstance) => {
            const setName = cardInstance.get('cardSetName');
            if (reduction.has(setName)) {
              return reduction.set(
                setName,
                reduction
                  .get(setName)
                  .set('rarities', reduction.get(setName).get('rarities').add(cardInstance.get('cardRarity'))),
              );
            }
            return reduction.set(setName, SetIconInfo.fromCardInstance(cardInstance));
          },
          Immutable.OrderedMap<string, SetIconInfo>({}),
        )
        .valueSeq()
        .toSet();
    case Game.POKEMON:
      const pokeGroup = cardGroup as PokeCardGroup;
      return pokeGroup
        .getInstances()
        .reduce<Immutable.OrderedMap<string, SetIconInfo>>(
          (reduction: Immutable.OrderedMap<string, SetIconInfo>, cardInstance: PokeCardInstance) => {
            const setName = cardInstance.get('card').get('setName');
            if (reduction.has(setName)) {
              return reduction.set(
                setName,
                reduction.get(setName).set(
                  'rarities',
                  reduction
                    .get(setName)
                    .get('rarities')
                    .add(cardInstance.get('card').get('rarity') || Rarity.COMMON),
                ),
              );
            }
            return reduction.set(setName, SetIconInfo.fromPokeCardInstance(cardInstance));
          },
          Immutable.OrderedMap<string, SetIconInfo>({}),
        )
        .valueSeq()
        .toSet();
    case Game.YUGIOH:
      const yugiGroup = cardGroup as YugiCardGroup;
      return yugiGroup
        .getInstances()
        .reduce<Immutable.OrderedMap<string, SetIconInfo>>(
          (reduction: Immutable.OrderedMap<string, SetIconInfo>, cardInstance: YugiCardInstance) => {
            const cardSet = cardInstance.get('cardSet');
            if (!cardSet) return reduction;

            const setName = cardSet.get('code');
            if (reduction.has(setName)) {
              return reduction.set(
                setName,
                reduction
                  .get(setName)
                  .set('rarities', reduction.get(setName).get('rarities').add(cardSet.get('rarity'))),
              );
            }
            return reduction.set(setName, SetIconInfo.fromYugiCardSet(cardSet));
          },
          Immutable.OrderedMap<string, SetIconInfo>({}),
        )
        .valueSeq()
        .toSet();
    case Game.LORCANA:
      const lorcanaGroup = cardGroup as LorcanaCardGroup;
      return lorcanaGroup
        .getInstances()
        .reduce<Immutable.OrderedMap<string, SetIconInfo>>(
          (reduction: Immutable.OrderedMap<string, SetIconInfo>, cardInstance: LorcanaCardInstance) => {
            const setName = cardInstance.get('card').get('setName');
            if (reduction.has(setName)) {
              return reduction.set(
                setName,
                reduction.get(setName).set(
                  'rarities',
                  reduction
                    .get(setName)
                    .get('rarities')
                    .add(cardInstance.get('card').get('rarity') || Rarity.COMMON),
                ),
              );
            }
            return reduction.set(setName, SetIconInfo.fromLorcanaCardInstance(cardInstance));
          },
          Immutable.OrderedMap<string, SetIconInfo>({}),
        )
        .valueSeq()
        .toSet();
  }
}
