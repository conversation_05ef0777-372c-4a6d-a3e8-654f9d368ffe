import * as Immutable from 'immutable';
import { AnyCardGroup } from './CardGroup';
import { MTGFilter } from './filters/mtg/MTGFilters';
import { PokeFilter } from './filters/pokemon/PokeFilters';
import { Grouping } from './Grouping';
import { MTGCardPage } from './mtg/MTGCardPage';
import { PokeCardPage } from './pokemon/PokeCardPage';
import { Record } from './Records';
import { Sorting } from './sorting/Sorting';
import { Viewing } from './Viewing';

type AnyFilter = MTGFilter | PokeFilter;

export interface ICardPage {
  readonly ownerUsername: string;
  readonly pageCount: number;
  readonly pageValue: number;
  readonly totalCount: number;
  readonly totalGroupCount: number;
  readonly totalValue: number;
  readonly cardGrouping: Grouping;
  readonly cardSorting: Sorting;
  readonly cardViewing: Viewing;
  readonly cardGroups: Immutable.List<AnyCardGroup>;
  readonly filterOptions: AnyFilter;
}

export type AnyCardPage = MTGCardPage | PokeCardPage;

export function CardPage(data: Pick<ICardPage, keyof ICardPage>) {
  return class extends Record<ICardPage>({
    ...data,
    ownerUsername: '',
    totalCount: 0,
    totalGroupCount: 0,
    totalValue: 0,
    pageCount: 0,
    pageValue: 0,
    cardGrouping: Grouping.PRINTING,
    cardSorting: Sorting.DATE_ADDED_DESC,
    cardViewing: Viewing.GRID,
    cardGroups: Immutable.List(),
    filterOptions: new MTGFilter(),
  }) {};
}

export interface ICardPageMethods<CardPage, GameInstances> {
  toAPI(): any;
  updateCardInstances(cardInstances: Immutable.List<GameInstances>): CardPage;
  removeInstances(cardInstances: Immutable.List<GameInstances>): CardPage;
}
