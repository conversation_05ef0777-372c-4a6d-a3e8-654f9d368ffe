import { TextFormat } from '../helpers/fmt';
import { CardSetMethods } from './AnyCardSet';
import { Record } from './Records';

export enum CardSetType {
  CORE = 'core',
  MASTERS = 'masters',
  EXPANSION = 'expansion',
  MEMORABILIA = 'memorabilia',
  COMMANDER = 'commander',
  ARCHENEMY = 'archenemy',
  BOX = 'box',
  DRAFT_INNOVATION = 'draft_innovation',
  FUNNY = 'funny',
  STARTER = 'starter',
  DUEL_DECK = 'duel_deck',
  FROM_THE_VALUT = 'from_the_vault',
  MASTERPIECE = 'masterpiece',
  PROMO = 'promo',
  PREMIUM_DECK = 'premium_deck',
  PLANECHASE = 'planechase',
  TOKEN = 'token',
  VANGUARD = 'vanguard',
  SPELLBOOK = 'spellbook',
  ARSENAL = 'arsenal',
}

/**
 * A representation of a "Magic: The Gathering" card set.
 */
interface ICardSet {
  readonly id: number;
  readonly name: string;
  readonly releasedAt: Date;
  readonly setCode: string;
  readonly setType: CardSetType;
}

// TODO: Move to mtg directory - in a clean MR
export class CardSet
  extends Record<ICardSet>({
    id: 0,
    name: '',
    releasedAt: new Date(),
    setCode: '',
    setType: CardSetType.CORE,
  })
  implements CardSetMethods
{
  name(): string {
    return this.get('name');
  }

  setTypeToString(): string {
    switch (this.get('setType')) {
      case CardSetType.FROM_THE_VALUT:
        return 'From the Vault Set';
      case CardSetType.DUEL_DECK:
      case CardSetType.PREMIUM_DECK:
        return TextFormat.humanizeKey(this.get('setType').replace('_', ' '));
      default:
        return `${TextFormat.humanizeKey(this.get('setType').replace('_', ' '))} Set`;
    }
  }

  static fromAPI(data: any) {
    return new CardSet({
      id: data.id,
      name: data.name,
      releasedAt: data.release_date,
      setCode: data.set_code,
      setType: data.set_type,
    });
  }
}
