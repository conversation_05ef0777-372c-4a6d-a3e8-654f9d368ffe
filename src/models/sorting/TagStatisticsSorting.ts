// TODO: Merge with other files in this folder into a unified Sorting model.
/**
 * Human readable sorting values.
 */
export enum TagStatisticsSorting {
  NAME_ASC = 'Tag Name (A-Z)',
  NAME_DESC = 'Tag Name (Z-A)',
  TOTAL_CARDS_DESC = 'Total Cards (Highest)',
  TOTAL_CARDS_ASC = 'Total Cards (Lowest)',
  TOTAL_VALUE_DESC = 'Total Value (Highest)',
  TOTAL_VALUE_ASC = 'Total Value (Lowest)',
}

export type TagStatisticsSortingKey = keyof typeof TagStatisticsSorting;

/**
 * Sorting codes for the API.
 */
export const TagStatisticsSortingCodes = {
  [TagStatisticsSorting.NAME_DESC]: 'name',
  [TagStatisticsSorting.NAME_ASC]: 'name',
  [TagStatisticsSorting.TOTAL_CARDS_DESC]: 'total_cards',
  [TagStatisticsSorting.TOTAL_CARDS_ASC]: 'total_cards',
  [TagStatisticsSorting.TOTAL_VALUE_DESC]: 'total_value',
  [TagStatisticsSorting.TOTAL_VALUE_ASC]: 'total_value',
};

/**
 * Ordering codes for the API.
 */
export const TagStatisticsOrderingCodes = {
  [TagStatisticsSorting.NAME_DESC]: 'desc',
  [TagStatisticsSorting.NAME_ASC]: 'asc',
  [TagStatisticsSorting.TOTAL_CARDS_DESC]: 'desc',
  [TagStatisticsSorting.TOTAL_CARDS_ASC]: 'asc',
  [TagStatisticsSorting.TOTAL_VALUE_DESC]: 'desc',
  [TagStatisticsSorting.TOTAL_VALUE_ASC]: 'asc',
};
