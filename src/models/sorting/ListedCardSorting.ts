// TODO: Merge with other files in this folder into a unified Sorting model.
/**
 * Human readable sorting values.
 */

export enum ListedCardSorting {
  NAME_ASC = 'Name (A-Z)',
  NAME_DESC = 'Name (Z-A)',
  SET_NAME_ASC = 'Set Name (A-Z)',
  SET_NAME_DESC = 'Set Name (Z-A)',
  DATE_ADDED_DESC = 'Date Added (Newest)',
  DATE_ADDED_ASC = 'Date Added (Oldest)',
}

export type ListedCardSortingKey = keyof typeof ListedCardSorting;

/**
 * Sorting codes for the API.
 */
export const ListedCardSortingCodes = {
  [ListedCardSorting.NAME_DESC]: 'name',
  [ListedCardSorting.NAME_ASC]: 'name',
  [ListedCardSorting.DATE_ADDED_DESC]: 'created_at',
  [ListedCardSorting.DATE_ADDED_ASC]: 'created_at',
  [ListedCardSorting.SET_NAME_DESC]: 'set_name',
  [ListedCardSorting.SET_NAME_ASC]: 'set_name',
};

/**
 * Ordering codes for the API.
 */
export const ListedCardOrderingCodes = {
  [ListedCardSorting.NAME_DESC]: 'desc',
  [ListedCardSorting.NAME_ASC]: 'asc',
  [ListedCardSorting.DATE_ADDED_DESC]: 'desc',
  [ListedCardSorting.DATE_ADDED_ASC]: 'asc',
  [ListedCardSorting.SET_NAME_DESC]: 'desc',
  [ListedCardSorting.SET_NAME_ASC]: 'asc',
};
