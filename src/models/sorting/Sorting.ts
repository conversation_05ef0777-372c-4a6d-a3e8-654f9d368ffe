// TODO: Merge with other files in this folder into a unified Sorting model.
export enum Sorting {
  DATE_ADDED_DESC = 'Date Added (Newest)',
  DATE_ADDED_ASC = 'Date Added (Oldest)',
  DATE_CREATED_DESC = 'Date Created (Newest)',
  DATE_CREATED_ASC = 'Date Created (Oldest)',
  NAME_ASC = 'Name (A-Z)',
  NAME_DESC = 'Name (Z-A)',
  SET_NAME_ASC = 'Set Name (A-Z)',
  SET_NAME_DESC = 'Set Name (Z-A)',
  RELEASE_DESC = 'Release (Newest)',
  RELEASE_ASC = 'Release (Oldest)',
  PRICE_DESC = 'Price (Highest)',
  PRICE_ASC = 'Price (Lowest)',
  MANA_VALUE_DESC = 'Mana Value (Highest)',
  MANA_VALUE_ASC = 'Mana Value (Lowest)',
  POWER_DESC = 'Power (Highest)',
  POWER_ASC = 'Power (Lowest)',
  TOUGHNESS_DESC = 'Toughness (Highest)',
  TOUGHNESS_ASC = 'Toughness (Lowest)',
  COUNT_DESC = 'Count (Highest)',
  COUNT_ASC = 'Count (Lowest)',
  RARITY_DESC = 'Rarity (Mythics First)',
  RARITY_ASC = 'Rarity (Commons First)',
  COLLECTOR_NUMBER_DESC = 'Collector Number (Highest)',
  COLLECTOR_NUMBER_ASC = 'Collector Number (Lowest)',
  POSITION_DESC = 'Position (Highest)',
  POSITION_ASC = 'Position (Lowest)',
  CONFIDENCE_DESC = 'Scan Confidence (Highest)',
  CONFIDENCE_ASC = 'Scan Confidence (Lowest)',
}

export type SortingKey = keyof typeof Sorting;

/**
 * Sorting codes for the API.
 */
export const SortingCodes = {
  [Sorting.DATE_ADDED_DESC]: 'committed_at',
  [Sorting.DATE_ADDED_ASC]: 'committed_at',
  [Sorting.DATE_CREATED_DESC]: 'created_at',
  [Sorting.DATE_CREATED_ASC]: 'created_at',
  [Sorting.NAME_DESC]: 'name',
  [Sorting.NAME_ASC]: 'name',
  [Sorting.SET_NAME_DESC]: 'set_name',
  [Sorting.SET_NAME_ASC]: 'set_name',
  [Sorting.RELEASE_DESC]: 'release_date',
  [Sorting.RELEASE_ASC]: 'release_date',
  [Sorting.PRICE_DESC]: 'price',
  [Sorting.PRICE_ASC]: 'price',
  [Sorting.MANA_VALUE_DESC]: 'cmc',
  [Sorting.MANA_VALUE_ASC]: 'cmc',
  [Sorting.POWER_DESC]: 'power',
  [Sorting.POWER_ASC]: 'power',
  [Sorting.TOUGHNESS_DESC]: 'toughness',
  [Sorting.TOUGHNESS_ASC]: 'toughness',
  [Sorting.COUNT_DESC]: 'count',
  [Sorting.COUNT_ASC]: 'count',
  [Sorting.RARITY_DESC]: 'rarity',
  [Sorting.RARITY_ASC]: 'rarity',
  [Sorting.COLLECTOR_NUMBER_DESC]: 'collector_number',
  [Sorting.COLLECTOR_NUMBER_ASC]: 'collector_number',
  [Sorting.POSITION_DESC]: 'position',
  [Sorting.POSITION_ASC]: 'position',
  [Sorting.CONFIDENCE_DESC]: 'scan_confidence',
  [Sorting.CONFIDENCE_ASC]: 'scan_confidence',
};

/**
 * Ordering codes for the API.
 */
export const OrderingCodes = {
  [Sorting.DATE_ADDED_DESC]: 'desc',
  [Sorting.DATE_ADDED_ASC]: 'asc',
  [Sorting.DATE_CREATED_DESC]: 'desc',
  [Sorting.DATE_CREATED_ASC]: 'asc',
  [Sorting.NAME_DESC]: 'desc',
  [Sorting.NAME_ASC]: 'asc',
  [Sorting.SET_NAME_DESC]: 'desc',
  [Sorting.SET_NAME_ASC]: 'asc',
  [Sorting.RELEASE_DESC]: 'desc',
  [Sorting.RELEASE_ASC]: 'asc',
  [Sorting.PRICE_DESC]: 'desc',
  [Sorting.PRICE_ASC]: 'asc',
  [Sorting.MANA_VALUE_DESC]: 'desc',
  [Sorting.MANA_VALUE_ASC]: 'asc',
  [Sorting.POWER_DESC]: 'desc',
  [Sorting.POWER_ASC]: 'asc',
  [Sorting.TOUGHNESS_DESC]: 'desc',
  [Sorting.TOUGHNESS_ASC]: 'asc',
  [Sorting.COUNT_DESC]: 'desc',
  [Sorting.COUNT_ASC]: 'asc',
  [Sorting.RARITY_DESC]: 'asc',
  [Sorting.RARITY_ASC]: 'desc',
  [Sorting.COLLECTOR_NUMBER_DESC]: 'desc',
  [Sorting.COLLECTOR_NUMBER_ASC]: 'asc',
  [Sorting.POSITION_DESC]: 'desc',
  [Sorting.POSITION_ASC]: 'asc',
  [Sorting.CONFIDENCE_DESC]: 'desc',
  [Sorting.CONFIDENCE_ASC]: 'asc',
};

/**
 * Return a Sorting based on a sorting code and ordering code. If either code
 * is invalid the "Date Added (Newest)" Sorting will be returned.
 */
export function sorting(sortingCode: string, orderingCode: string): Sorting {
  if (orderingCode === 'desc') {
    switch (sortingCode) {
      case 'committed_at':
        return Sorting.DATE_ADDED_DESC;
      case 'created_at':
        return Sorting.DATE_CREATED_DESC;
      case 'name':
        return Sorting.NAME_ASC;
      case 'set_name':
        return Sorting.SET_NAME_ASC;
      case 'release_date':
        return Sorting.RELEASE_DESC;
      case 'price':
        return Sorting.PRICE_DESC;
      case 'cmc':
        return Sorting.MANA_VALUE_DESC;
      case 'power':
        return Sorting.POWER_DESC;
      case 'toughness':
        return Sorting.TOUGHNESS_DESC;
      case 'count':
        return Sorting.COUNT_DESC;
      case 'rarity':
        return Sorting.RARITY_ASC;
      case 'collector_number':
        return Sorting.COLLECTOR_NUMBER_DESC;
      case 'position':
        return Sorting.POSITION_DESC;
    }
  } else if (orderingCode === 'asc') {
    switch (sortingCode) {
      case 'committed_at':
        return Sorting.DATE_ADDED_ASC;
      case 'created_at':
        return Sorting.DATE_CREATED_ASC;
      case 'name':
        return Sorting.NAME_DESC;
      case 'set_name':
        return Sorting.SET_NAME_DESC;
      case 'release_date':
        return Sorting.RELEASE_ASC;
      case 'price':
        return Sorting.PRICE_ASC;
      case 'cmc':
        return Sorting.MANA_VALUE_ASC;
      case 'power':
        return Sorting.POWER_ASC;
      case 'toughness':
        return Sorting.TOUGHNESS_ASC;
      case 'count':
        return Sorting.COUNT_ASC;
      case 'rarity':
        return Sorting.RARITY_DESC;
      case 'collector_number':
        return Sorting.COLLECTOR_NUMBER_ASC;
      case 'position':
        return Sorting.POSITION_ASC;
    }
  }
  return Sorting.DATE_CREATED_DESC;
}
