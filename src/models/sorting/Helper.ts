import { Grouping } from '../Grouping';
import { Sorting } from './Sorting';

export class SortingGroupingHelper {
  public static validCombo(grouping: Grouping, sorting: Sorting) {
    switch (sorting) {
      case Sorting.COLLECTOR_NUMBER_ASC:
      case Sorting.COLLECTOR_NUMBER_DESC:
        return grouping !== Grouping.NAME;
      case Sorting.POSITION_ASC:
      case Sorting.POSITION_DESC:
        return grouping === Grouping.NONE;
      case Sorting.CONFIDENCE_ASC:
      case Sorting.CONFIDENCE_DESC:
        return grouping === Grouping.NONE;
      default:
        return true;
    }
  }
}
