// TODO: Merge with other files in this folder into a unified Sorting model.
/**
 * Human readable sorting values.
 */

export enum CardListSorting {
  NAME_ASC = 'Name (A-Z)',
  NAME_DESC = 'Name (Z-A)',
  DATE_ADDED_DESC = 'Date Added (Newest)',
  DATE_ADDED_ASC = 'Date Added (Oldest)',
  DATE_EDITED_DESC = 'Date Edited (Newest)',
  DATE_EDITED_ASC = 'Date Edited (Oldest)',
}

/**
 * Sorting codes for the API.
 */
export const CardListSortingCodes = {
  [CardListSorting.NAME_DESC]: 'name',
  [CardListSorting.NAME_ASC]: 'name',
  [CardListSorting.DATE_ADDED_DESC]: 'created_at',
  [CardListSorting.DATE_ADDED_ASC]: 'created_at',
  [CardListSorting.DATE_EDITED_DESC]: 'updated_at',
  [CardListSorting.DATE_EDITED_ASC]: 'updated_at',
};

/**
 * Ordering codes for the API.
 */
export const CardListOrderingCodes = {
  [CardListSorting.NAME_DESC]: 'desc',
  [CardListSorting.NAME_ASC]: 'asc',
  [CardListSorting.DATE_ADDED_DESC]: 'desc',
  [CardListSorting.DATE_ADDED_ASC]: 'asc',
  [CardListSorting.DATE_EDITED_DESC]: 'desc',
  [CardListSorting.DATE_EDITED_ASC]: 'asc',
};
