// TODO: Merge with other files in this folder into a unified Sorting model.
/**
 * Human readable sorting values.
 */

export enum DeckSorting {
  DATE_ADDED_DESC = 'Date Added (Newest)',
  DATE_ADDED_ASC = 'Date Added (Oldest)',
  DATE_EDITED_DESC = 'Date Edited (Newest)',
  DATE_EDITED_ASC = 'Date Edited (Oldest)',
  PRICE_DESC = 'Price (Highest)',
  PRICE_ASC = 'Price (Lowest)',
  NAME_ASC = 'Name (A-Z)',
  NAME_DESC = 'Name (Z-A)',
}

export type DeckSortingKey = keyof typeof DeckSorting;

/**
 * Sorting codes for the API.
 */
export const DeckSortingCodes = {
  [DeckSorting.DATE_ADDED_DESC]: 'date_added_newest',
  [DeckSorting.DATE_ADDED_ASC]: 'date_added_oldest',
  [DeckSorting.DATE_EDITED_DESC]: 'date_edited_newest',
  [DeckSorting.DATE_EDITED_ASC]: 'date_edited_oldest',
  [DeckSorting.PRICE_DESC]: 'price_highest',
  [DeckSorting.PRICE_ASC]: 'price_lowest',
  [DeckSorting.NAME_DESC]: 'name_za',
  [DeckSorting.NAME_ASC]: 'name_az',
};
