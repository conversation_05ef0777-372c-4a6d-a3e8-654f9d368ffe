// TODO: Merge with other files in this folder into a unified Sorting model.
/**
 * Human readable sorting values.
 */
export enum SetCompletionSorting {
  COMPLETION_DESC = 'Completion (Highest)',
  COMPLETION_ASC = 'Completion (Lowest)',
  SET_NAME_ASC = 'Set Name (A-Z)',
  SET_NAME_DESC = 'Set Name (Z-A)',
  SET_CODE_ASC = 'Set Code (A-Z)',
  SET_CODE_DESC = 'Set Code (Z-A)',
  RELEASE_DATE_DESC = 'Release Date (Newest)',
  RELEASE_DATE_ASC = 'Release Date (Oldest)',
}

export type SetCompletionSortingKey = keyof typeof SetCompletionSorting;

/**
 * Sorting codes for the API.
 */
export const SetCompletionSortingCodes = {
  [SetCompletionSorting.COMPLETION_DESC]: 'completion',
  [SetCompletionSorting.COMPLETION_ASC]: 'completion',
  [SetCompletionSorting.SET_NAME_DESC]: 'set_name',
  [SetCompletionSorting.SET_NAME_ASC]: 'set_name',
  [SetCompletionSorting.SET_CODE_DESC]: 'set_code',
  [SetCompletionSorting.SET_CODE_ASC]: 'set_code',
  [SetCompletionSorting.RELEASE_DATE_DESC]: 'release_date',
  [SetCompletionSorting.RELEASE_DATE_ASC]: 'release_date',
};

/**
 * Ordering codes for the API.
 */
export const SetCompletionOrderingCodes = {
  [SetCompletionSorting.COMPLETION_DESC]: 'desc',
  [SetCompletionSorting.COMPLETION_ASC]: 'asc',
  [SetCompletionSorting.SET_NAME_DESC]: 'desc',
  [SetCompletionSorting.SET_NAME_ASC]: 'asc',
  [SetCompletionSorting.SET_CODE_DESC]: 'desc',
  [SetCompletionSorting.SET_CODE_ASC]: 'asc',
  [SetCompletionSorting.RELEASE_DATE_DESC]: 'desc',
  [SetCompletionSorting.RELEASE_DATE_ASC]: 'asc',
};
