import { Record } from './Records';

class Point {
  x: number;
  y: number;

  constructor(x: number, y: number) {
    this.x = x;
    this.y = y;
  }

  static fromArray(arr: number[]): Point {
    return new Point(arr[0], arr[1]);
  }
}

export interface IScanCrop {
  readonly topLeft: Point;
  readonly topRight: Point;
  readonly bottomLeft: Point;
  readonly bottomRight: Point;
}

class ScanCrop extends Record<IScanCrop>({
  topLeft: new Point(0, 0),
  topRight: new Point(0, 0),
  bottomLeft: new Point(0, 0),
  bottomRight: new Point(0, 0),
}) {
  static fromAPI(data: any): ScanCrop {
    return new ScanCrop({
      topLeft: Point.fromArray(data.tl),
      topRight: Point.fromArray(data.tr),
      bottomLeft: Point.fromArray(data.bl),
      bottomRight: Point.fromArray(data.br),
    });
  }
}

export interface IScanMetadata {
  readonly confidence: number;
  readonly crop: ScanCrop;
  readonly flipped: boolean;
  readonly mcrVersion: string;
  readonly evermindVersion: string;
}

export class ScanMetadata extends Record<IScanMetadata>({
  confidence: 0,
  crop: new ScanCrop(),
  flipped: false,
  mcrVersion: 'unknown',
  evermindVersion: 'unknown',
}) {
  static fromAPI(data: any): ScanMetadata {
    return new ScanMetadata({
      confidence: data.confidence,
      crop: ScanCrop.fromAPI(data.crop),
      flipped: data.flipped,
      mcrVersion: data.mcr_version,
      evermindVersion: data.evermind_version,
    });
  }
}
