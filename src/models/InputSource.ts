import * as Immutable from 'immutable';
import moment from 'moment';
import { TextFormat } from '../helpers/fmt';
import { Record } from './Records';

export enum InputSourceType {
  MANUAL = 'manual',
  CARDBOT = 'cardbot',
  IMPORT = 'import',
  APP = 'app',
  UNKNOWN = 'unknown',
}

export type InputSourceTypeKey = keyof typeof InputSourceType;

export interface IInputSource {
  id: number;
  uuid: string;
  name: string;
  sourceType: InputSourceType;
  userId: number;
  legacy: boolean;
  ancilliaryId: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * InputSources tracks the input sources associated with each user.
 * Currently, this is mainly used to track what CardBots are associated
 * with each user.
 */
export class InputSource extends Record<IInputSource>({
  id: 0,
  uuid: '',
  name: '',
  sourceType: InputSourceType.MANUAL,
  userId: 0,
  legacy: false,
  ancilliaryId: '',
  createdAt: new Date(),
  updatedAt: new Date(),
}) {
  toAPI(): any {
    return {
      id: this.get('id'),
      uuid: this.get('uuid'),
      name: this.get('name'),
      source_type: this.get('sourceType'),
      user_id: this.get('userId'),
      legacy: this.get('legacy'),
      ancilliaryId: this.get('ancilliaryId'),
      created_at: this.get('createdAt').toString(),
      updated_at: this.get('updatedAt').toString(),
    };
  }

  static mapFromAPI(input_sources: any): Immutable.OrderedMap<string, InputSource> {
    return Immutable.List<any>(input_sources)
      .toOrderedMap()
      .mapEntries((entry: any[]) => {
        const inputSource = InputSource.fromAPI(entry[1]);
        return [inputSource.get('uuid'), inputSource];
      })
      .toOrderedMap() as Immutable.OrderedMap<string, InputSource>;
  }

  static fromAPI(input_source: any): InputSource {
    return new InputSource({
      id: input_source.id,
      uuid: input_source.uuid,
      name: input_source.name,
      sourceType: input_source.source_type,
      userId: input_source.user_id,
      legacy: input_source.legacy,
      ancilliaryId: input_source.ancilliary_id,
      createdAt: moment.utc(input_source.created_at).toDate(),
      updatedAt: moment.utc(input_source.updated_at).toDate(),
    });
  }

  static typeToString(inputSourceType: InputSourceType) {
    if (inputSourceType === InputSourceType.CARDBOT) {
      return 'CardBot';
    }
    return TextFormat.humanizeKey(inputSourceType);
  }
}
