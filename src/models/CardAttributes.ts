import { GroupValue } from './CardInstances';
import { Condition } from './Condition';
import { Foil } from './Foil';
import { Language } from './Language';
import { LorcanaFinish } from './lorcana/LorcanaFinish';
import { PokeFinish } from './pokemon/PokeFinish';
import { Record } from './Records';

// TODO: Unify these two objects
export interface ICardAttributes {
  readonly condition?: Condition;
  readonly language?: Language;
  readonly foil?: Foil;
  readonly pokeFinish?: PokeFinish;
  readonly lorcanaFinish?: LorcanaFinish;
  readonly printing?: string;
}

export class CardAttributes extends Record<ICardAttributes>({
  condition: undefined,
  language: undefined,
  foil: undefined,
  pokeFinish: undefined,
  lorcanaFinish: undefined,
  printing: undefined,
}) {
  toAPI(): any {
    const data: any = {};
    if (this.get('condition')) data['condition'] = this.get('condition');
    if (this.get('language')) data['language'] = this.get('language');
    if (this.get('foil')) data['foil'] = this.get('foil');
    if (this.get('pokeFinish')) data['finish_uuid'] = this.get('pokeFinish')?.get('uuid');
    if (this.get('lorcanaFinish')) data['finish_uuid'] = this.get('lorcanaFinish')?.get('uuid');
    if (this.get('printing')) data['printing'] = this.get('printing');
    return data;
  }
}

export interface ICardFormAttributes {
  readonly condition: GroupValue<Condition>;
  readonly language: GroupValue<Language>;
  readonly foil: Foil;
  readonly pokeFinish?: GroupValue<PokeFinish>;
  readonly lorcanaFinish?: GroupValue<LorcanaFinish>;
}
export class CardFormAttributes extends Record<ICardFormAttributes>({
  condition: Condition.NEAR_MINT,
  language: Language.ENGLISH,
  foil: Foil.Off,
  pokeFinish: undefined,
  lorcanaFinish: undefined,
}) {
  // FIXME: JS object equality doesn't exist and missing finish UUIDs would cause inequality
  equals(other: CardFormAttributes): boolean {
    const foilEqual = this.get('foil') === other.get('foil');
    const conditionEqual = this.get('condition') === other.get('condition');
    const languageEqual = this.get('language') === other.get('language');

    let pokeFinishEqual = false;
    if (this.get('pokeFinish') === 'Multiple') {
      pokeFinishEqual = other.get('pokeFinish') === 'Multiple';
    } else if (other.get('pokeFinish') === 'Multiple') {
      pokeFinishEqual = this.get('pokeFinish') === 'Multiple';
    } else {
      pokeFinishEqual =
        (this.get('pokeFinish') as PokeFinish)?.get('name') === (other.get('pokeFinish') as PokeFinish)?.get('name');
    }

    let lorcanaFinishEqual = false;
    if (this.get('lorcanaFinish') === 'Multiple') {
      lorcanaFinishEqual = other.get('lorcanaFinish') === 'Multiple';
    } else if (other.get('lorcanaFinish') === 'Multiple') {
      lorcanaFinishEqual = this.get('lorcanaFinish') === 'Multiple';
    } else {
      lorcanaFinishEqual =
        (this.get('lorcanaFinish') as PokeFinish)?.get('name') ===
        (other.get('lorcanaFinish') as PokeFinish)?.get('name');
    }
    return foilEqual && conditionEqual && languageEqual && pokeFinishEqual && lorcanaFinishEqual;
  }
}
