import * as Immutable from 'immutable';
import moment from 'moment';
import { EnumHelper } from '../helpers/enum';
import { CardListOption } from '../views/components/CardListSuggestionItem';
import { Card } from './Cards';
import { Language } from './Language';
import { Record } from './Records';

export interface IListedCard {
  card: Card;
  foil: boolean;
  language: Language;
  createdAt: Date;
}

export class ListedCard extends Record<IListedCard>({
  card: new Card(),
  foil: false,
  language: Language.ENGLISH,
  createdAt: new Date(),
}) {
  static fromAPI(res: any, cards: Immutable.Map<string, Card>): ListedCard {
    const card = cards.get(res.json_id);
    return new ListedCard({
      card: card,
      foil: res.foil,
      language: EnumHelper.match(Language, res.language) as Language,
      createdAt: moment.utc(res.created_at).toDate(),
    });
  }
}

export interface ICardList {
  name: string;
  uuid: string;
  createdAt: Date;
  updatedAt: Date;
  cardCount: number;
  description: string;
  listedCards: Immutable.List<ListedCard>;
}

export class CardList extends Record<ICardList>({
  name: '',
  uuid: '',
  createdAt: new Date(),
  updatedAt: new Date(),
  cardCount: 0,
  description: '',
  listedCards: Immutable.List<ListedCard>(),
}) {
  // Only used for getting all CardLists, hence why listedCards and cards are left blank.
  static fromAPI(cardList: any): CardList {
    return new CardList({
      name: cardList.name,
      uuid: cardList.uuid,
      createdAt: moment.utc(cardList.created_at).toDate(),
      updatedAt: moment.utc(cardList.updated_at).toDate(),
      cardCount: cardList.count,
      description: cardList.description,
    });
  }

  static indexedByUUID(cardLists: Immutable.List<CardList>): Immutable.OrderedMap<string, CardList> {
    return Immutable.OrderedMap<string, CardList>(
      cardLists.map((cardList: CardList) => {
        return [cardList.get('uuid'), cardList];
      }),
    );
  }

  static fromCardListOption(cardListOption: CardListOption) {
    return new CardList({
      name: cardListOption['label'],
      uuid: cardListOption['value'],
      cardCount: cardListOption['cardCount'],
    });
  }
}

interface ICardListPage {
  pageNumber: number;
  totalPages: number;
  totalCards: number;
  query: string;
  cardList: CardList;
  nextPage?: string;
  previousPage?: string;
}

export class CardListPage extends Record<ICardListPage>({
  pageNumber: 0,
  totalPages: 0,
  totalCards: 0,
  query: '',
  cardList: new CardList(),
}) {
  static fromAPI(cardList: any, query: string, header?: any, page = 1): CardListPage {
    let cards = Immutable.Map<string, Card>();

    if (cardList.cards) {
      cards = Immutable.Map<string, any>(cardList.cards)
        .map((data: any) => Card.fromAPI(data))
        .toMap();
    }

    let listedCards = Immutable.List<ListedCard>();

    if (cardList.listed_cards) {
      listedCards = Immutable.List<ListedCard>(
        cardList.listed_cards.map((listedCard: any) => ListedCard.fromAPI(listedCard, cards)),
      );
    }

    const totalPages = Number.parseInt(header['x-total-pages']);
    const totalCards = Number.parseInt(header['x-total']);
    return new CardListPage({
      pageNumber: page,
      totalPages: Number.isNaN(totalPages) ? 0 : totalPages,
      totalCards: Number.isNaN(totalCards) ? 0 : totalCards,
      query: query,
      cardList: new CardList({
        name: cardList.name,
        uuid: cardList.uuid,
        createdAt: moment.utc(cardList.created_at).toDate(),
        cardCount: Number.parseInt(cardList.count),
        description: cardList.description,
        listedCards: listedCards,
      }),
    });
  }
}

// This is in a record as the card lists and the loaded boolean should be updated simultaneously.
// This approach should not be needed for the majority of functional components in the future.
interface IDropdown {
  loaded: boolean;
  cardLists: Immutable.OrderedMap<string, CardList>;
}

export class DropdownState extends Record<IDropdown>({
  loaded: false,
  cardLists: Immutable.OrderedMap<string, CardList>(),
}) {}
