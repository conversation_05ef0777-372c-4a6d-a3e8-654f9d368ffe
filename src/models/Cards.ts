import * as Immutable from 'immutable';
import { CoreAssets, ImageQuality } from '../helpers/core_assets';
import { CardMethods } from './AnyCard';
import { CardLayout } from './CardLayout';
import { Language, SortLanguages } from './Language';
import { Rarity, rarityFromAPI } from './Rarity';
import { Record } from './Records';

/**
 * A representation of a "Magic: The Gathering" card. Several instances of
 * this card will be owned by different users, and will have different
 * prices, foils, and qualities.
 */
interface ICard {
  readonly id: number;
  readonly jsonID: string;
  readonly name: string;
  readonly description: string;
  readonly power?: number;
  readonly toughness?: number;
  readonly rarity: Rarity;
  readonly manaCost: string;
  readonly manaValue: number;
  readonly superTypes: Immutable.List<string>;
  readonly types: Immutable.List<string>;
  readonly subTypes: Immutable.List<string>;
  readonly layout: CardLayout;
  readonly userPrice: number;
  readonly userPriceFoil: number;
  readonly tcgPlayerPrice: number;
  readonly tcgPlayerPriceFoil: number;
  readonly ckPrice: number;
  readonly ckPriceFoil: number;
  readonly cmPrice: number;
  readonly cmPriceFoil: number;
  readonly setCode: string;
  readonly setName: string;

  readonly redCount: number;
  readonly whiteCount: number;
  readonly blueCount: number;
  readonly blackCount: number;
  readonly greenCount: number;

  readonly colorRed: boolean;
  readonly colorWhite: boolean;
  readonly colorBlue: boolean;
  readonly colorBlack: boolean;
  readonly colorGreen: boolean;
  readonly colorMulti: boolean;

  readonly legalities: Immutable.Map<string, string>;
  readonly foreignData: Immutable.Map<Language, Immutable.Map<string, string>>;
  readonly ownedCount?: number;
  readonly collectorNumber?: string;
}

export class Card
  extends Record<ICard>({
    id: 0,
    jsonID: '',
    name: '',
    description: '',
    power: undefined,
    toughness: undefined,
    rarity: Rarity.COMMON,
    manaCost: '',
    manaValue: 0,
    superTypes: Immutable.List<string>(),
    types: Immutable.List<string>(),
    subTypes: Immutable.List<string>(),
    layout: CardLayout.NORMAL,
    userPrice: 0,
    userPriceFoil: 0,
    tcgPlayerPrice: 0,
    tcgPlayerPriceFoil: 0,
    ckPrice: 0,
    ckPriceFoil: 0,
    cmPrice: 0,
    cmPriceFoil: 0,
    setCode: '',
    setName: '',
    redCount: 0,
    whiteCount: 0,
    blueCount: 0,
    blackCount: 0,
    greenCount: 0,
    colorRed: false,
    colorWhite: false,
    colorBlue: false,
    colorBlack: false,
    colorGreen: false,
    colorMulti: false,
    legalities: Immutable.Map<string, string>(),
    foreignData: Immutable.Map<Language, Immutable.Map<string, string>>(),
    ownedCount: undefined,
    collectorNumber: undefined,
  })
  implements CardMethods
{
  displayLabel(): string {
    return `${this.get('name')} - ${this.get('setName')} (${this.get('collectorNumber')})`;
  }

  languageOptions(): Immutable.OrderedSet<Language> {
    return SortLanguages(this.get('foreignData').keySeq().toSet());
  }

  static fromAPI(card: any, ownedCount = 0): Card {
    return new Card({
      id: card.id,
      jsonID: card.json_id,
      name: card.name,
      superTypes: Immutable.List<string>(card.super_types || []),
      types: Immutable.List<string>(card.types || []),
      layout: card.layout,
      userPrice: card.user_price,
      userPriceFoil: card.user_price_foil,
      tcgPlayerPrice: card.price,
      tcgPlayerPriceFoil: card.price_foil,
      ckPrice: card.ck_price,
      ckPriceFoil: card.ck_price_foil,
      manaCost: card.mana_cost,
      manaValue: parseInt(card.converted_mana_cost || 0),
      collectorNumber: card.collector_number,
      rarity: rarityFromAPI(card.rarity),
      setCode: card.set_code,
      setName: card.set_name,
      redCount: card.red_count || 0,
      whiteCount: card.white_count || 0,
      blueCount: card.blue_count || 0,
      blackCount: card.black_count || 0,
      greenCount: card.green_count || 0,
      colorBlack: card.color_black || false,
      colorRed: card.color_red || false,
      colorWhite: card.color_white || false,
      colorBlue: card.color_blue || false,
      colorGreen: card.color_green || false,
      colorMulti:
        [
          card.color_red || false,
          card.color_blue || false,
          card.color_black || false,
          card.color_green || false,
          card.color_white || false,
        ].filter((color) => color).length > 1,
      legalities: card.legalities,
      foreignData: card.foreign_data,
      ownedCount: ownedCount,
    });
  }

  public imageURL(imageQuality: ImageQuality, language: Language): string {
    const imagePath = language + '/' + this.get('jsonID') + '.jpg';
    let qualityPath = 'card_images';
    if (imageQuality == ImageQuality.HQ) qualityPath = `${qualityPath}_hq`;

    return `${CoreAssets.imageHost()}/${qualityPath}/${imagePath}`;
  }
}

interface ICollectionTotals {
  readonly count: number;
  readonly value: number;
}
export class CollectionTotals extends Record<ICollectionTotals>({
  count: 0,
  value: 0,
}) {
  static fromAPI(data: any): CollectionTotals {
    return new CollectionTotals({
      count: data.count,
      value: data.value,
    });
  }
}
