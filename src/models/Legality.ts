export enum Legality {
  STANDARD = 'standard',
  PIONEER = 'pioneer',
  MODERN = 'modern',
  LEGACY = 'legacy',
  VINTAGE = 'vintage',
  COMMANDER = 'commander',
  BRAWL = 'brawl',
  TINY_LEADERS = 'tiny leaders',
  PAUPER = 'pauper',
  DUEL = 'duel',
  OLD_SCHOOL = 'oldschool',
  PREMODERN = 'premodern',
  FREEFORM = 'freeform',
}

export type LegalityKey = keyof typeof Legality;

export enum LegalityState {
  LEGAL = 'Legal',
  RESTRICTED = 'Restricted',
  BANNED = 'Banned',
}
