import { Record } from './Records';

interface IReport {
  readonly reportType: ReportType;
  readonly rawMessage?: string;
  readonly scanningInfo?: ScanningInfo;
}

export enum ReportType {
  PAIRED = 'PAIRED',
  IDLE = 'IDLE',
  PROCESSING = 'PROCESSING',
  SCANNING = 'SCANNING',
  SCAN_COMPLETED = 'SCAN_COMPLETED',
  DISCONNECTED = 'DISCONNECTED',
  ERROR = 'ERROR',
  FATAL_ERROR = 'FATAL_ERROR',
}

export class Report extends Record<IReport>({
  reportType: ReportType.PROCESSING,
  rawMessage: undefined,
  scanningInfo: undefined,
}) {
  static fromAPI(reportAPI: any): Report | undefined {
    if (reportAPI && reportAPI.type) {
      const reportType = reportAPI.type as ReportType;
      switch (reportType) {
        case ReportType.FATAL_ERROR:
        case ReportType.ERROR:
          return new Report({
            reportType: reportType,
            rawMessage: reportAPI.info.message,
          });
        case ReportType.SCANNING:
          return new Report({
            reportType: reportType,
            scanningInfo: new ScanningInfo({
              acceptCount: parseInt(reportAPI.info.acceptCount as string),
              rejectCount: parseInt(reportAPI.info.rejectCount as string),
            }),
          });
        default:
          return new Report({
            reportType: reportType,
          });
      }
    } else {
      return undefined;
    }
  }
}

interface IScanningInfo {
  readonly acceptCount: number;
  readonly rejectCount: number;
}

export class ScanningInfo extends Record<IScanningInfo>({
  acceptCount: 0,
  rejectCount: 0,
}) {
  totalCount(): number {
    return this.get('acceptCount') + this.get('rejectCount');
  }
  percentage(key: 'acceptCount' | 'rejectCount'): number {
    return Math.round(this.totalCount() > 0 ? (this.get(key) / this.totalCount()) * 100 : 0);
  }
}
