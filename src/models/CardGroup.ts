import * as Immutable from 'immutable';
import { CardInstance } from './CardInstances';
import { Language } from './Language';
import { LorcanaCardInstance } from './lorcana/LorcanaCardInstances';
import { LorcanaCardGroup } from './lorcana/LorcanaCardPage';
import { LorcanaPrinting } from './lorcana/LorcanaPrinting';
import { MTGCardGroup } from './mtg/MTGCardPage';
import { MTGPrinting } from './mtg/MTGPrinting';
import { PokeCardInstance } from './pokemon/PokeCardInstances';
import { PokeCardGroup } from './pokemon/PokeCardPage';
import { PokePrinting } from './pokemon/PokePrinting';
import { ScanMetadata } from './ScanMetadata';
import { YugiCardInstance } from './yugioh/YugiCardInstances';
import { YugiCardGroup } from './yugioh/YugiCardPage';
import { YugiPrinting } from './yugioh/YugiPrinting';

interface ICardGroup<TCardInstance, TPrinting> {
  readonly cardInstanceLatest: Date;
  readonly cardInstanceCount: number;
  readonly cardInstancePrice: number;
  readonly cardInstances: Immutable.List<TCardInstance>;
  readonly printingOptions: Immutable.List<TPrinting>;
}

export interface CardGroupMethods<ICardInstance, TCardInstance, TPrinting> {
  getInstances(): Immutable.List<TCardInstance>;
  setInstances(cardInstances: Immutable.List<TCardInstance>): this;
  setScanMetadata(scanMetadata: Immutable.Map<string, ScanMetadata>): this;
  updateInstances(updates: Immutable.List<TCardInstance>): this;
  removeInstances(removals: Immutable.List<TCardInstance>): this;
  selectedPrinting(printingID?: string, languageOptions?: Immutable.OrderedSet<Language>): TPrinting;
  count(): number;
  priceSummary(): number;
  timeSummary(timezone: string): string | undefined;
  collect<K extends keyof ICardInstance, V extends ICardInstance[K]>(propertyName: K): Immutable.Set<V>;
  groupValue<K extends keyof ICardInstance, V extends ICardInstance[K], M>(
    propertyName: K,
    multipleValue: M,
    defaultValue: V,
  ): V | M;
}

export interface IPokeCardGroup extends ICardGroup<PokeCardInstance, PokePrinting> {}
export interface IMTGCardGroup extends ICardGroup<CardInstance, MTGPrinting> {}
export interface IYugiCardGroup extends ICardGroup<YugiCardInstance, YugiPrinting> {}
export interface ILorcanaCardGroup extends ICardGroup<LorcanaCardInstance, LorcanaPrinting> {}
export type AnyCardGroup = MTGCardGroup | PokeCardGroup | YugiCardGroup | LorcanaCardGroup;
