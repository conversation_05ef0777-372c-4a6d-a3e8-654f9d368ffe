import * as Immutable from 'immutable';
import { Record } from './Records';

interface IStackLocation {
  id: number;
  uuid: string;
  name: string;
  parentId: number;
  userId: number;
  createdAt: Date;
  updatedAt: Date;
  childLocations: Immutable.Map<string, StackLocation>;
}

/**
 * StackLocation is called that due to Location being the name of an interface in Typescript.
 */
export class StackLocation extends Record<IStackLocation>({
  id: 0,
  uuid: '',
  name: '',
  parentId: 0,
  userId: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
  childLocations: Immutable.Map<string, StackLocation>(),
}) {
  static fromAPI(location: any): StackLocation | undefined {
    if (location === undefined || location === null) {
      return undefined;
    }
    return new StackLocation({
      id: location.id,
      uuid: location.uuid,
      name: location.name,
      parentId: location.parent_id === null ? undefined : location.parent_id,
      userId: location.user_id,
      createdAt: location.created_at,
      updatedAt: location.updated_at,
      childLocations: StackLocation.mapFromAPI(location.children),
    });
  }

  static pathFromAPI(locationPath: any): Immutable.List<StackLocation> {
    return Immutable.List<any>(locationPath)
      .map((location: any) => StackLocation.fromAPI(location))
      .filterNot((value?: StackLocation) => value === undefined)
      .toList() as Immutable.List<StackLocation>;
  }

  static listFromAPI(locations: any): Immutable.List<StackLocation> {
    return StackLocation.mapFromAPI(locations).toList();
  }

  static mapFromAPI(locations: any): Immutable.Map<string, StackLocation> {
    return Immutable.Map<string, any>(locations)
      .mapEntries((entry: any) => [entry[0], StackLocation.fromAPI(entry[1])])
      .toMap() as Immutable.Map<string, StackLocation>;
  }

  // TODO: Delete unused function when StackLocation.test.ts is moved to card panel tests.
  public static resolveMultipleLocations(
    locationMap: Immutable.Map<string, StackLocation | undefined>,
    cardCount: number,
  ): string | undefined {
    // No cards have locations
    if (locationMap.size === 0) {
      return undefined;
    }
    // Some cards have locations, others do not
    if (cardCount > locationMap.size) {
      return 'Multiple Locations';
    }

    let returnValue: string | undefined = undefined;
    const locations = locationMap.toList();
    // Use a for loop instead of a Immutable.Map.reduce function so we can take advantage of the break command
    for (let i = 0; i < locations.size; i++) {
      const targetLocation = locations.get(i);
      if (returnValue === undefined) {
        returnValue = targetLocation === undefined ? undefined : targetLocation.get('uuid');
      } else if (targetLocation === undefined || returnValue !== targetLocation.get('uuid')) {
        returnValue = 'Multiple Locations';
        break;
      }
    }
    return returnValue;
  }

  static pathToString(path: Immutable.List<StackLocation>): string {
    return path
      .map((location: StackLocation) => location.get('name'))
      .toArray()
      .join('/');
  }
}
