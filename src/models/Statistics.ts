import * as Immutable from 'immutable';
import { OverviewStatistics } from './OverviewStatistics';
import { Record } from './Records';

export enum StatComponentStatus {
  INACTIVE = 'inactive',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error',
}

export enum StatisticType {
  OVERVIEW = 'overview',
  COLOR = 'color',
  MANA = 'mana',
  TYPE = 'type',
  SET_COMPLETION = 'set_completion',
  TAGS = 'tags',
  MOST_VALUABLE = 'most_valuable',
}

interface IStatisticsMeta {
  status: StatComponentStatus;
  statisticsRecord: Record<any, any> | Immutable.OrderedMap<string, any>;
}

export class StatisticsMeta extends Record<IStatisticsMeta>({
  status: StatComponentStatus.INACTIVE,
  statisticsRecord: new OverviewStatistics(),
}) {}
