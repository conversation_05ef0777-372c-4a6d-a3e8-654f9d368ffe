import * as Immutable from 'immutable';
import moment from 'moment';
import { DateFormat } from '../../helpers/fmt';
import { CardGroupMethods, IMTGCardGroup } from '../CardGroup';
import { CardInstance, ICardInstance } from '../CardInstances';
import { CardPage, ICardPageMethods } from '../CardPage';
import { MTGFilter } from '../filters/mtg/MTGFilters';
import { Grouping } from '../Grouping';
import { Language } from '../Language';
import { Record } from '../Records';
import { ScanMetadata } from '../ScanMetadata';
import { OrderingCodes, Sorting, SortingCodes } from '../sorting/Sorting';
import { Viewing } from '../Viewing';
import { MTGPrinting } from './MTGPrinting';

export class MTGCardGroup
  extends Record<IMTGCardGroup>({
    cardInstanceLatest: new Date(),
    cardInstanceCount: 0,
    cardInstancePrice: 0,
    cardInstances: Immutable.List<CardInstance>(),
    printingOptions: Immutable.List<MTGPrinting>(),
  })
  implements CardGroupMethods<ICardInstance, CardInstance, MTGPrinting>
{
  setScanMetadata(scanMetadata: Immutable.Map<string, ScanMetadata>): this {
    return this.setInstances(
      this.getInstances()
        .map((cardInstance: CardInstance) =>
          cardInstance.set('scanMetadata', scanMetadata.get(cardInstance.get('uuid'))),
        )
        .toList(),
    );
  }

  selectedPrinting(
    printingID?: string,
    languageOptions = Immutable.OrderedSet<Language>([Language.ENGLISH]),
  ): MTGPrinting {
    let matchedPrinting: MTGPrinting | undefined = undefined;

    if (printingID) {
      matchedPrinting = MTGPrinting.match(printingID, this.get('printingOptions'));
    }

    return (
      matchedPrinting ||
      new MTGPrinting({
        jsonID: printingID,
        languages: languageOptions,
        setName: this.groupValue('cardSetName', 'Multiple', ''),
        setCode: this.groupValue('cardSetCode', 'Multiple', ''),
        // TODO: Add rarity?
        collectorNumber: this.groupValue('cardCollectorNumber', 'Multiple', ''),
      })
    );
  }

  priceSummary(): number {
    return this.get('cardInstances').reduce(
      (price: number, cardInstance: CardInstance) => price + cardInstance.get('cardPrice'),
      0,
    );
  }
  timeSummary(timezone: string): string | undefined {
    if (this.get('cardInstances').size > 0) {
      const latest = this.get('cardInstances').get(0).get('createdAt');
      return DateFormat.human(latest, timezone);
    }

    return undefined;
  }

  collect<K extends keyof ICardInstance, V extends ICardInstance[K]>(propertyName: K): Immutable.Set<V> {
    return Immutable.Set(this.get('cardInstances').map((instance: CardInstance) => instance.get(propertyName)));
  }

  groupValue<K extends keyof ICardInstance, V extends ICardInstance[K], M>(
    propertyName: K,
    multipleValue: M,
    defaultValue: V,
  ): V | M {
    const uniqueValues = this.get('cardInstances')
      .map((instance: CardInstance) => instance.get(propertyName) as V)
      .toSet();

    if (uniqueValues.size > 0) {
      if (uniqueValues.size == 1) {
        return uniqueValues.first();
      } else {
        return multipleValue;
      }
    } else {
      return defaultValue;
    }
  }

  getInstances(): Immutable.List<CardInstance> {
    return this.get('cardInstances');
  }

  setInstances(cardInstances: Immutable.List<CardInstance>): this {
    return this.set('cardInstances', cardInstances);
  }

  updateInstances(updated: Immutable.List<CardInstance>): this {
    const appliedUpdates = this.get('cardInstances')
      .map((cardInstance: CardInstance) => {
        const match = updated.find((update: CardInstance) => cardInstance.get('id') === update.get('id'));
        if (match) {
          return cardInstance.applyUpdates(match);
        }
        return cardInstance;
      })
      .toList();

    return this.set('cardInstances', appliedUpdates);
  }

  removeInstances(removals: Immutable.List<CardInstance>): this {
    const removedIDs = removals.map((removal: CardInstance) => removal.get('id'));
    const appliedRemovals = this.get('cardInstances')
      .filterNot((cardInstance: CardInstance) => removedIDs.contains(cardInstance.get('id')))
      .toList();

    return this.set('cardInstances', appliedRemovals);
  }

  count(): number {
    return this.get('cardInstances').count();
  }

  static fromAPI(data: any, scanMetadata?: Immutable.Map<string, ScanMetadata>): MTGCardGroup {
    if (data === undefined) {
      return new MTGCardGroup({});
    }

    return new MTGCardGroup({
      cardInstanceLatest: moment.utc(data.latest).toDate(),
      cardInstanceCount: data.group_count,
      cardInstancePrice: data.group_price,
      cardInstances: data.card_users.map((cardUser: any) => CardInstance.fromAPI(cardUser, scanMetadata)),
    });
  }
}

export class MTGCardPage
  extends CardPage({
    ownerUsername: '',
    totalCount: 0,
    totalGroupCount: 0,
    totalValue: 0,
    pageCount: 0,
    pageValue: 0,
    cardGroups: Immutable.List<MTGCardGroup>(),
    cardGrouping: Grouping.PRINTING,
    cardSorting: Sorting.DATE_ADDED_DESC,
    cardViewing: Viewing.GRID,
    filterOptions: new MTGFilter(),
  })
  implements ICardPageMethods<MTGCardPage, CardInstance>
{
  /**
   * Convert from a MTGCardPage into an API url for querying the API.
   */
  toAPI(): any {
    const payload: any = this.get('filterOptions').toAPI() || {};
    payload.sort_by = SortingCodes[this.get('cardSorting')];
    payload.order = OrderingCodes[this.get('cardSorting')];
    payload.group_by = this.get('cardGrouping');
    return payload;
  }

  updateCardInstances(updates: Immutable.List<CardInstance>): MTGCardPage {
    const updatedGroups = this.get('cardGroups')
      .map((cardGroup: MTGCardGroup) => cardGroup.updateInstances(updates))
      .toList();
    return this.set('cardGroups', updatedGroups);
  }

  removeInstances(removals: Immutable.List<CardInstance>): MTGCardPage {
    const updatedGroups = this.get('cardGroups')
      .map((cardGroup: MTGCardGroup) => cardGroup.removeInstances(removals))
      .filter((cardGroup: MTGCardGroup) => cardGroup.count() > 0)
      .toList();
    return this.set('cardGroups', updatedGroups);
  }
}
