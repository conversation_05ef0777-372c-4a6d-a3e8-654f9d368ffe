import { Record } from './Records';

interface ICellBorders {
  top: boolean;
  right: boolean;
  bottom: boolean;
  left: boolean;
}

export class CellBorders extends Record<ICellBorders>({
  top: false,
  right: false,
  bottom: false,
  left: false,
}) {
  static default(bottom: boolean): CellBorders {
    return new CellBorders({ left: true, bottom: bottom });
  }

  static rightCell(bottom: boolean): CellBorders {
    return new CellBorders({ left: true, right: true, bottom: bottom });
  }
}
