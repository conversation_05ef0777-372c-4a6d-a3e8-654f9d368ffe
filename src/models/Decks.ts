import * as Immutable from 'immutable';
import { Card } from './Cards';
import { Legality } from './Legality';
import { Rarity } from './Rarity';
import { Record } from './Records';
import { Tag } from './Tags';

interface IDeckCard {
  id: number;
  boardId: number;
  cardId: number;
  cardInstanceId: number;
}

export class DeckCard extends Record<IDeckCard>({
  id: 0,
  boardId: 0,
  cardId: 0,
  cardInstanceId: 0,
}) {}

interface IDeckBoard {
  id: number;
  name: string;
}

export class DeckBoard extends Record<IDeckBoard>({
  id: 0,
  name: 'Untitled',
}) {}

export interface IDeck {
  dateEdited: Date;
  dateCreated: Date;
  id: number;
  userId: number;
  uuid: string;
  image: string | undefined;
  name: string;
  legality: Legality;
  isPublic: boolean;
  numCards: number;
  price: number;
  boards: Immutable.List<DeckBoard>;
  cards: Immutable.Map<number, Card>;
  deckCards: Immutable.Map<number, DeckCard>;

  colorWhite: boolean;
  colorBlue: boolean;
  colorBlack: boolean;
  colorRed: boolean;
  colorGreen: boolean;

  tags: Immutable.Map<string, Tag>;
}

export class Deck extends Record<IDeck>({
  dateEdited: new Date(),
  dateCreated: new Date(),
  id: 0,
  userId: 0,
  uuid: '',
  image: undefined,
  name: 'Untitled Deck',
  legality: Legality.FREEFORM,
  isPublic: false,
  numCards: 0,
  price: 0,
  boards: Immutable.List<DeckBoard>(),
  cards: Immutable.Map<number, Card>(), // TODO: Update to use uuid (string) instead of card id (number)
  deckCards: Immutable.Map<number, DeckCard>(), // TODO: Update to use uuid (string) instead of card id (number)

  colorWhite: false,
  colorBlue: false,
  colorBlack: false,
  colorRed: false,
  colorGreen: false,

  tags: Immutable.Map<string, Tag>(),
}) {
  boardById(id: number) {
    for (const board of this.get('boards').toArray()) {
      if (board.get('id') === id) {
        return board;
      }
    }
    return null;
  }

  getLinkedCardsForCardKingdom(): Immutable.Map<string, number> {
    return this.get('deckCards')
      .filter((value: DeckCard) => {
        // Ignore unlinked cards, only linked cards have a cardInstanceId.
        // Have to check null due to oddities with DeckCards.
        return value.get('cardInstanceId') !== undefined && value.get('cardInstanceId') !== null;
      })
      .reduce((reduction: Immutable.Map<string, number>, value: DeckCard) => {
        const candidate = this.get('cards').get(value.get('cardId'), undefined);
        if (candidate === undefined) {
          return reduction;
        }
        return reduction.set(candidate.get('name'), reduction.get(candidate.get('name'), 0) + 1);
      }, Immutable.Map<string, number>());
  }
}

export const calculateCardTypeRatios = (deck: Deck) => {
  if (!deck.get('deckCards').size) {
    return Immutable.Map<string, number>({
      disabled: 1,
    });
  }

  let totalCount = 0;
  let groupCounts = Immutable.Map<string, number>();

  const mainBoard = findDeckBoardByName(deck, 'Main')!;
  deck
    .get('deckCards')
    .filter((deckCard: DeckCard) => deckCard.get('boardId') === mainBoard.get('id'))
    .forEach((deckCard: DeckCard) => {
      // Total count always goes up by one
      totalCount += 1;

      // Get the card type and increment the count
      const cardTypes = deck.get('cards').get(deckCard.get('cardId')).get('types');
      if (cardTypes && cardTypes.size) {
        cardTypes.forEach((cardType: string) => {
          if (groupCounts.has(cardType)) {
            groupCounts = groupCounts.set(cardType, groupCounts.get(cardType) + 1);
          } else {
            groupCounts = groupCounts.set(cardType, 1);
          }
        });
      }
    });

  return groupCounts
    .keySeq()
    .reduce(
      (statistics: Immutable.Map<string, number>, key: string) =>
        statistics.set(key, groupCounts.get(key) / Math.max(totalCount, 1)),
      Immutable.Map<string, number>(),
    );
};

export function calculateManaSymbolRatios(deck: Deck): Immutable.Map<string, number> {
  let totalCount = 0;
  let red = 0;
  let white = 0;
  let black = 0;
  let blue = 0;
  let green = 0;

  const mainBoard = findDeckBoardByName(deck, 'Main')!;
  deck
    .get('deckCards')
    .filter((deckCard: DeckCard) => deckCard.get('boardId') === mainBoard.get('id'))
    .forEach((deckCard: DeckCard) => {
      const card = deck.get('cards').get(deckCard.get('cardId'));
      // Increment total counts
      totalCount +=
        card.get('redCount') +
        card.get('blueCount') +
        card.get('blackCount') +
        card.get('whiteCount') +
        card.get('greenCount');
      // Increment individual color counts
      red += card.get('redCount');
      white += card.get('whiteCount');
      black += card.get('blackCount');
      blue += card.get('blueCount');
      green += card.get('greenCount');
    });

  return Immutable.Map<string, number>({
    totalCount: totalCount,
    red: red / Math.max(totalCount, 1),
    white: white / Math.max(totalCount, 1),
    black: black / Math.max(totalCount, 1),
    blue: blue / Math.max(totalCount, 1),
    green: green / Math.max(totalCount, 1),
    disabled: red || white || black || blue || green ? 0 : 1,
  })
    .filter((value: number) => value > 0)
    .toMap();
}

export const calculateManaSymbolStack = (deck: Deck) => {
  let white = Immutable.Map<number, number>();
  let blue = Immutable.Map<number, number>();
  let black = Immutable.Map<number, number>();
  let red = Immutable.Map<number, number>();
  let green = Immutable.Map<number, number>();
  let multi = Immutable.Map<number, number>();
  let colorless = Immutable.Map<number, number>();

  const mainBoard = findDeckBoardByName(deck, 'Main')!;
  deck
    .get('deckCards')
    .filter(
      (deckCard: DeckCard) =>
        deck.get('cards').get(deckCard.get('cardId')).get('types').indexOf('Land') < 0 &&
        deckCard.get('boardId') === mainBoard.get('id'),
    )
    .forEach((deckCard: DeckCard) => {
      const card = deck.get('cards').get(deckCard.get('cardId'));
      if (card.get('rarity') === Rarity.BASIC_LAND) {
        return;
      }
      // Increment individual color counts
      const manaValue = card.get('manaValue');
      if (card.get('colorMulti')) {
        multi = incrementStack(multi, manaValue);
        return;
      }
      if (card.get('colorWhite')) {
        white = incrementStack(white, manaValue);
        return;
      }
      if (card.get('colorBlue')) {
        blue = incrementStack(blue, manaValue);
        return;
      }
      if (card.get('colorBlack')) {
        black = incrementStack(black, manaValue);
        return;
      }
      if (card.get('colorRed')) {
        red = incrementStack(red, manaValue);
        return;
      }
      if (card.get('colorGreen')) {
        green = incrementStack(green, manaValue);
        return;
      }

      colorless = incrementStack(colorless, manaValue);
    });

  return Immutable.Map<string, Immutable.Map<number, number>>({
    red: red.filter((value: number) => value > 0),
    white: white.filter((value: number) => value > 0),
    black: black.filter((value: number) => value > 0),
    blue: blue.filter((value: number) => value > 0),
    green: green.filter((value: number) => value > 0),
    multi: multi.filter((value: number) => value > 0),
    colorless: colorless.filter((value: number) => value > 0),
  });
};

function incrementStack(map: Immutable.Map<number, number>, manaValue: number): Immutable.Map<number, number> {
  if (map.has(manaValue)) {
    return map.set(manaValue, map.get(manaValue) + 1);
  } else {
    return map.set(manaValue, 1);
  }
}

export enum DeckCardGrouping {
  NONE = 'None',
  NAME = 'Name',
  CARD_TYPE = 'Card Type',
  MANA_COST = 'Mana Cost',
}

export type DeckCardGroupingKey = keyof typeof DeckCardGrouping;

export function calculateTurnProbabilities(
  deck: Deck,
  grouping: DeckCardGrouping,
  separateLands?: boolean,
): Immutable.Map<string, number> {
  let totalCount = 0;
  let groupCounts = Immutable.Map<string, number>();

  const mainBoard = findDeckBoardByName(deck, 'Main')!;
  deck
    .get('deckCards')
    .filter((deckCard: DeckCard) => deckCard.get('boardId') === mainBoard.get('id'))
    .forEach((deckCard: DeckCard) => {
      // Total count always goes up by one
      totalCount += 1;

      const setToLand =
        separateLands && deck.get('cards').get(deckCard.get('cardId')).get('types').indexOf('Land') >= 0;

      switch (grouping) {
        case DeckCardGrouping.NONE: {
          if (groupCounts.has('' + deckCard.get('id'))) {
            groupCounts = groupCounts.set('' + deckCard.get('id'), groupCounts.get('' + deckCard.get('id')) + 1);
          } else {
            groupCounts = groupCounts.set('' + deckCard.get('id'), 1);
          }
          break;
        }

        // Name based grouping
        case DeckCardGrouping.NAME: {
          const name = deck.get('cards').get(deckCard.get('cardId')).get('name');
          if (groupCounts.has(name)) {
            groupCounts = groupCounts.set(name, groupCounts.get(name) + 1);
          } else {
            groupCounts = groupCounts.set(name, 1);
          }
          break;
        }

        // Type based grouping
        case DeckCardGrouping.CARD_TYPE: {
          const cardTypes = deck.get('cards').get(deckCard.get('cardId')).get('types');
          let cardType = cardTypes && cardTypes.size ? cardTypes.join(' ') : '-';
          if (cardTypes.contains('Land')) {
            cardType = 'Land';
          }
          if (groupCounts.has(cardType)) {
            groupCounts = groupCounts.set(cardType, groupCounts.get(cardType) + 1);
          } else {
            groupCounts = groupCounts.set(cardType, 1);
          }
          break;
        }

        // Mana based grouping
        case DeckCardGrouping.MANA_COST: {
          const manaCost = '' + deck.get('cards').get(deckCard.get('cardId')).get('manaValue');
          if (groupCounts.has(manaCost)) {
            groupCounts = groupCounts.set(
              setToLand ? 'Land' : manaCost,
              groupCounts.get(setToLand ? 'Land' : manaCost) + 1,
            );
          } else {
            groupCounts = groupCounts.set(setToLand ? 'Land' : manaCost, 1);
          }
          break;
        }
      }
    });

  return groupCounts
    .keySeq()
    .reduce(
      (statistics: Immutable.Map<string, number>, key: string) =>
        statistics.set(key, groupCounts.get(key) / Math.max(totalCount, 1)),
      Immutable.Map<string, number>(),
    );
}

export const findDeckBoardByName = (deck: Deck, name: string): DeckBoard | undefined => {
  return deck.get('boards').find((board: DeckBoard) => board.get('name') === name);
};
