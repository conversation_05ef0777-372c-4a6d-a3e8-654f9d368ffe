import moment from 'moment';
import { CardSetMethods } from '../AnyCardSet';
import { Record } from '../Records';

/**
 * A representation of a Pokemon card set.
 */
interface IPokeSet {
  readonly uuid: string;
  readonly name: string;
  readonly setCode: string;
  readonly releaseDate: Date;
}

export class PokeSet
  extends Record<IPokeSet>({
    uuid: '',
    name: '',
    setCode: '',
    releaseDate: new Date(),
  })
  implements CardSetMethods
{
  name(): string {
    return this.get('name');
  }

  static fromAPI(data: any): PokeSet {
    return new PokeSet({
      uuid: data.uuid,
      name: data.name,
      setCode: data.pokemontcgio_id,
      releaseDate: moment.utc(data.release_date).toDate(),
    });
  }
}
