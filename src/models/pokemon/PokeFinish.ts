import { Record } from '../Records';

/**
 * A representation of a Pokemon card. Several instances of
 * this card will be owned by different users, and will have different
 * prices, foils, and qualities.
 */
export interface IPokeFinish {
  readonly uuid: string;
  readonly name: string;
  readonly fallbackOrder: number;
}

export class PokeFinish extends Record<IPokeFinish>({
  uuid: '',
  name: 'Unknown',
  fallbackOrder: 0,
}) {
  static fromAPI(data: any): PokeFinish {
    return new PokeFinish({
      uuid: data.uuid,
      name: data.name,
      fallbackOrder: data.fallback_order,
    });
  }
}
