import * as Immutable from 'immutable';
import { Language, validateLanguages } from '../Language';
import { PrintingMethods } from '../Printing';
import { Record } from '../Records';
import { PokeRarity, rarityFromAPI } from './PokeRarity';

export interface IPokePrinting {
  readonly uuid: string;
  readonly setUUID: string;
  readonly setName: string;
  readonly collectorNumber?: string;
  readonly rarity?: PokeRarity;
  readonly languages: Immutable.Set<Language>;
}

export class PokePrinting
  extends Record<IPokePrinting>({
    uuid: '',
    setUUID: '',
    setName: '',
    collectorNumber: '',
    rarity: undefined,
    languages: Immutable.OrderedSet<Language>([Language.ENGLISH]),
  })
  implements PrintingMethods
{
  validateLanguage(language: string): Immutable.Map<string, any> {
    return validateLanguages(this.get('languages'), language);
  }

  identifier(): string {
    return this.get('uuid');
  }

  displayLabel(): string {
    return this.get('setName') + ' (' + this.get('collectorNumber') + ')';
  }

  static fromAPI(data: any): PokePrinting {
    return new PokePrinting({
      uuid: data.uuid,
      setUUID: data.set_uuid,
      setName: data.set_name,
      collectorNumber: data.collector_number,
      rarity: rarityFromAPI(data.rarity),
    });
  }

  static match(uuid: string, printingOptions: Immutable.List<PokePrinting>): PokePrinting | undefined {
    return printingOptions.find((printing: PokePrinting) => printing.get('uuid') === uuid);
  }

  displayText() {
    if (this.get('collectorNumber')) {
      return `${this.get('setName')} (${this.get('collectorNumber')})`;
    } else {
      return this.get('setName');
    }
  }
}
