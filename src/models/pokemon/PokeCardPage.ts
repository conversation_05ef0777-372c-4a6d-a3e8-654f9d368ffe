import * as Immutable from 'immutable';
import * as moment from 'moment';
import { DateFormat } from '../../helpers/fmt';
import { CardGroupMethods, IPokeCardGroup } from '../CardGroup';
import { CardPage, ICardPageMethods } from '../CardPage';
import { PokeFilter } from '../filters/pokemon/PokeFilters';
import { Grouping } from '../Grouping';
import { Language } from '../Language';
import { Record } from '../Records';
import { ScanMetadata } from '../ScanMetadata';
import { OrderingCodes, Sorting, SortingCodes } from '../sorting/Sorting';
import { Viewing } from '../Viewing';
import { IPokeCardInstance, PokeCardInstance } from './PokeCardInstances';
import { IPokeCard, PokeCard } from './PokeCards';
import { PokePrinting } from './PokePrinting';

export class PokeCardGroup
  extends Record<IPokeCardGroup>({
    cardInstanceLatest: new Date(),
    cardInstanceCount: 0,
    cardInstancePrice: 0,
    cardInstances: Immutable.List<PokeCardInstance>(),
    printingOptions: Immutable.List<PokePrinting>(),
  })
  implements CardGroupMethods<IPokeCardInstance, PokeCardInstance, PokePrinting>
{
  setScanMetadata(scanMetadata: Immutable.Map<string, ScanMetadata>): this {
    return this.setInstances(
      this.getInstances()
        .map((cardInstance: PokeCardInstance) =>
          cardInstance.set('scanMetadata', scanMetadata.get(cardInstance.get('uuid'))),
        )
        .toList(),
    );
  }

  selectedPrinting(
    printingID?: string,
    languageOptions = Immutable.OrderedSet<Language>([Language.ENGLISH]),
  ): PokePrinting {
    let matchedPrinting: PokePrinting | undefined = undefined;

    if (printingID) {
      matchedPrinting = PokePrinting.match(printingID, this.get('printingOptions'));
    }

    return (
      matchedPrinting ||
      new PokePrinting({
        uuid: this.cardValues('uuid', 'Multiple', ''),
        setName: this.cardValues('setName', 'Multiple', ''),
        setUUID: this.cardValues('setUUID', 'Multiple', ''),
        collectorNumber: this.cardValues('collectorNumber', 'Multiple', ''),
        languages: languageOptions,
      })
    );
  }

  priceSummary(): number {
    return this.get('cardInstances').reduce(
      (price: number, cardInstance: PokeCardInstance) => price + cardInstance.get('price'),
      0,
    );
  }

  timeSummary(timezone: string): string | undefined {
    if (this.get('cardInstances').size > 0) {
      const latest = this.get('cardInstances').get(0).get('createdAt');
      return DateFormat.human(latest, timezone);
    }

    return undefined;
  }

  collect<K extends keyof IPokeCardInstance, V extends IPokeCardInstance[K]>(propertyName: K): Immutable.Set<V> {
    return Immutable.Set(this.get('cardInstances').map((instance: PokeCardInstance) => instance.get(propertyName)));
  }

  groupValue<K extends keyof IPokeCardInstance, V extends IPokeCardInstance[K], M>(
    propertyName: K,
    multipleValue: M,
    defaultValue: V,
  ): V | M {
    const uniqueValues = this.get('cardInstances')
      .map((instance: PokeCardInstance) => instance.get(propertyName) as V)
      .toSet();

    if (uniqueValues.size > 0) {
      if (uniqueValues.size == 1) {
        return uniqueValues.first();
      } else {
        return multipleValue;
      }
    } else {
      return defaultValue;
    }
  }

  cardValues<K extends keyof IPokeCard, V extends IPokeCard[K], M>(
    propertyName: K,
    multipleValue: M,
    defaultValue: V,
  ): V | M {
    const uniqueValues = this.get('cardInstances')
      .map((instance: PokeCardInstance) => instance.get('card').get(propertyName) as V)
      .toSet();

    if (uniqueValues.size > 0) {
      if (uniqueValues.size == 1) {
        return uniqueValues.first();
      } else {
        return multipleValue;
      }
    } else {
      return defaultValue;
    }
  }

  getInstances(): Immutable.List<PokeCardInstance> {
    return this.get('cardInstances');
  }

  setInstances(cardInstances: Immutable.List<PokeCardInstance>): this {
    return this.set('cardInstances', cardInstances);
  }

  updateInstances(updated: Immutable.List<PokeCardInstance>): this {
    const appliedUpdates = this.get('cardInstances')
      .map((cardInstance: PokeCardInstance) => {
        const match = updated.find((candidate: PokeCardInstance) => cardInstance.get('uuid') === candidate.get('uuid'));
        if (match) {
          return cardInstance.applyUpdates(match);
        }
        return cardInstance;
      })
      .toList();

    return this.set('cardInstances', appliedUpdates);
  }

  removeInstances(removals: Immutable.List<PokeCardInstance>): this {
    const removedIDs = removals.map((removal: PokeCardInstance) => removal.get('uuid'));
    const appliedRemovals = this.get('cardInstances')
      .filterNot((cardInstance: PokeCardInstance) => removedIDs.contains(cardInstance.get('uuid')))
      .toList();

    return this.set('cardInstances', appliedRemovals);
  }

  count(): number {
    return this.get('cardInstances').count();
  }

  static fromAPI(
    collection_item: any,
    cards: Immutable.Map<string, PokeCard>,
    scanMetadata?: Immutable.Map<string, ScanMetadata>,
  ): PokeCardGroup {
    if (collection_item === undefined) {
      return new PokeCardGroup({});
    }
    return new PokeCardGroup({
      cardInstanceLatest: moment.utc(collection_item.latest_created).toDate(),
      cardInstanceCount: collection_item.group_count,
      cardInstancePrice: collection_item.group_price,
      cardInstances: collection_item.group_elements.map((element: any) =>
        PokeCardInstance.fromAPI(element, cards, scanMetadata),
      ),
    });
  }
}

export class PokeCardPage
  extends CardPage({
    ownerUsername: '',
    totalCount: 0,
    totalGroupCount: 0,
    totalValue: 0,
    pageCount: 0,
    pageValue: 0,
    cardGroups: Immutable.List<PokeCardGroup>(),
    cardGrouping: Grouping.PRINTING,
    cardSorting: Sorting.DATE_ADDED_DESC,
    cardViewing: Viewing.GRID,
    filterOptions: new PokeFilter(),
  })
  implements ICardPageMethods<PokeCardPage, PokeCardInstance>
{
  toAPI(): any {
    const payload: any = this.get('filterOptions').toAPI() || {};
    payload.sort_by = SortingCodes[this.get('cardSorting')];
    payload.order = OrderingCodes[this.get('cardSorting')];
    payload.group_by = this.get('cardGrouping');
    return payload;
  }

  updateCardInstances(updates: Immutable.List<PokeCardInstance>): PokeCardPage {
    const updatedGroups = this.get('cardGroups')
      .map((cardGroup: PokeCardGroup) => cardGroup.updateInstances(updates))
      .toList();
    return this.set('cardGroups', updatedGroups);
  }

  removeInstances(removals: Immutable.List<PokeCardInstance>): PokeCardPage {
    const updatedGroups = this.get('cardGroups')
      .map((cardGroup: PokeCardGroup) => cardGroup.removeInstances(removals))
      .filter((cardGroup: PokeCardGroup) => cardGroup.count() > 0)
      .toList();
    return this.set('cardGroups', updatedGroups);
  }
}
