import { EnumHelper } from '../../helpers/enum';

export enum PokeRarity {
  ACE_SPEC_RARE = 'ACE SPEC Rare',
  AMAZING_RARE = 'Amazing Rare',
  CLASSIC_COLLECTION = 'Classic Collection',
  COMMON = 'Common',
  DOUBLE_RARE = 'Double Rare',
  HYPER_RARE = 'Hyper Rare',
  ILLUSTRATION_RARE = 'Illustration Rare',
  LEGEND = 'LEGEND',
  // PROMO = 'Promo',
  RADIANT_RARE = 'Radiant Rare',
  RARE = 'Rare',
  RARE_ACE = 'Rare ACE',
  RARE_BREAK = 'Rare BREAK',
  RARE_HOLO = 'Rare Holo',
  RARE_HOLO_EX = 'Rare Holo EX',
  RARE_HOLO_GX = 'Rare Holo GX',
  RARE_HOLO_LVX = 'Rare Holo LV.X',
  RARE_HOLO_STAR = 'Rare Holo Star',
  RARE_HOLO_V = 'Rare Holo V',
  RARE_HOLO_VMAX = 'Rare Holo VMAX',
  RARE_HOLO_VSTAR = 'Rare Holo VSTAR',
  RARE_PRIME = 'Rare Prime',
  RARE_PRISM_STAR = 'Rare Prism Star',
  RARE_RAINBOW = 'Rare Rainbow',
  RARE_SECRET = 'Rare Secret',
  RARE_SHINING = 'Rare Shining',
  RARE_SHINY = 'Rare Shiny',
  RARE_SHINY_GX = 'Rare Shiny GX',
  RARE_ULTRA = 'Rare Ultra',
  SHINY_RARE = 'Shiny Rare',
  SHINY_ULTRA_RARE = 'Shiny Ultra Rare',
  SPECIAL_ILLUSTRATION_RARE = 'Special Illustration Rare',
  TRAINER_GALLERY_RARE_HOLO = 'Trainer Gallery Rare Holo',
  ULTRA_RARE = 'Ultra Rare',
  UNCOMMON = 'Uncommon',
}

export function rarityFromAPI(rawValue: string): PokeRarity | undefined {
  if (!rawValue) {
    return undefined;
  }

  const rarity = EnumHelper.match(PokeRarity, rawValue) as PokeRarity;
  if (!rarity) {
    // TODO: Add Bugsnag notifier when we work out how to get that working without causing Jest to lose it.
    console.error(`Invalid rarity ${rawValue} converted to COMMON`);
    return PokeRarity.COMMON;
  }
  return rarity;
}
