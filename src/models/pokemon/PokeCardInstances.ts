import * as Immutable from 'immutable';
import moment from 'moment';
import { CoreAssets, ImageQuality } from '../../helpers/core_assets';
import { EnumHelper } from '../../helpers/enum';
import { Condition } from '../Condition';
import { Language } from '../Language';
import { Record } from '../Records';
import { ScanMetadata } from '../ScanMetadata';
import { PokeCard } from './PokeCards';
import { PokeFinish } from './PokeFinish';

/**
 * An instance of a Pokemon card
 */
export interface IPokeCardInstance {
  readonly uuid: string;
  readonly condition: Condition;
  readonly createdAt: Date;
  readonly card: PokeCard;
  price: number;
  readonly inputSessionUUID: string;
  readonly finish: PokeFinish;
  readonly scannedImageURL?: string;
  readonly scanMetadata?: ScanMetadata;
}

export class PokeCardInstance extends Record<IPokeCardInstance>({
  uuid: '',
  condition: Condition.NEAR_MINT,
  createdAt: new Date(),
  card: new PokeCard(),
  price: 0,
  inputSessionUUID: '',
  scannedImageURL: undefined,
  scanMetadata: undefined,
  finish: new PokeFinish(),
}) {
  // Convert from an API blob into a CardInstance.
  static fromAPI(
    data: any,
    cards: Immutable.Map<string, PokeCard>,
    scanMetadata?: Immutable.Map<string, ScanMetadata>,
  ): PokeCardInstance {
    const matchedMetadata = scanMetadata?.get(data.uuid);
    return new PokeCardInstance({
      uuid: data.uuid,
      condition: (EnumHelper.match(Condition, data.condition) as Condition) || Condition.NEAR_MINT,
      createdAt: moment.utc(data.created_at).toDate(),
      card: cards.get(data.poke_card_uuid),
      price: data.price || 0,
      inputSessionUUID: data.input_session_uuid,
      scannedImageURL: data.scanned_image_url || undefined,
      scanMetadata: matchedMetadata || (data.scan_metadata && ScanMetadata.fromAPI(data.scan_metadata)),
      finish: new PokeFinish({ name: data.finish }),
    });
  }

  // Named as such as the name "update" conflicts with a immutable-js Record function.
  public applyUpdates(updated: PokeCardInstance): PokeCardInstance {
    return (this as PokeCardInstance)
      .set('condition', updated.get('condition'))
      .set('card', updated.get('card'))
      .set('price', updated.get('price'))
      .set('finish', updated.get('finish'));
  }

  // TODO: Migrate to PokeCard imageURL() once Printing has been refactored to be the PokeCard model
  public imageURL(
    imageQuality: ImageQuality,
    _language = Language.ENGLISH,
    printingID = this.get('card').get('uuid'),
  ): string {
    return `${CoreAssets.imageHost()}/pokemon/${imageQuality}/${printingID.charAt(0)}/${printingID.charAt(
      1,
    )}/${printingID}.jpg`;
  }
}

export type PokeCardInstanceKey = keyof IPokeCardInstance;

export function updateMatches(
  existing: Immutable.List<PokeCardInstance>,
  updates: Immutable.List<PokeCardInstance>,
): Immutable.List<PokeCardInstance> {
  return existing
    .map((cardInstance: PokeCardInstance) => {
      const match = updates
        .filter((update: PokeCardInstance) => cardInstance.get('uuid') === update.get('uuid'))
        .first();
      if (match) {
        cardInstance = cardInstance.applyUpdates(match);
      }
      return cardInstance;
    })
    .toList();
}
