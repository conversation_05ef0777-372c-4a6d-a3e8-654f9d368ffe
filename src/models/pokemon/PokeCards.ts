import * as Immutable from 'immutable';
import { CoreAssets, ImageQuality } from '../../helpers/core_assets';
import { CardMethods } from '../AnyCard';
import { Language } from '../Language';
import { Record } from '../Records';
import { PokeRarity, rarityFromAPI } from './PokeRarity';

/**
 * A representation of a Pokemon card. Several instances of
 * this card will be owned by different users, and will have different
 * prices, foils, and qualities.
 */
export interface IPokeCard {
  readonly uuid: string;
  readonly name: string;
  readonly setCode: string;
  readonly setName: string;
  readonly setUUID: string;
  readonly collectorNumber: string;
  readonly rarity?: PokeRarity;
  readonly level: string;
  readonly hp: number;
  readonly superType: string;
  readonly types: Immutable.List<string>;
  readonly subTypes: Immutable.List<string>;
  readonly flavorText: string;
}

export class PokeCard
  extends Record<IPokeCard>({
    uuid: '',
    name: '',
    setCode: '',
    setName: '',
    setUUID: '',
    collectorNumber: '',
    rarity: undefined,
    level: 'Basic',
    hp: 0,
    superType: '',
    types: Immutable.List<string>(),
    subTypes: Immutable.List<string>(),
    flavorText: '',
  })
  implements CardMethods
{
  displayLabel(): string {
    return `${this.get('name')} - ${this.get('setName')} (${this.get('collectorNumber')})`;
  }

  static fromAPI(data: any): PokeCard {
    return new PokeCard({
      uuid: data.uuid,
      name: data.name,
      setCode: data.set_pokemontcgio_id,
      setUUID: data.set_uuid,
      setName: data.set_name,
      collectorNumber: data.collector_number,
      rarity: rarityFromAPI(data.rarity),
      level: data.level,
      hp: data.hp,
      superType: data.super_type,
      types: Immutable.List<string>(data.types),
      subTypes: Immutable.List<string>(data.sub_types),
      flavorText: data.flavor_text,
    });
  }

  public imageURL(imageQuality: ImageQuality, _language = Language.ENGLISH): string {
    return `${CoreAssets.imageHost()}/pokemon/${imageQuality}/${this.get('uuid').charAt(0)}/${this.get('uuid').charAt(
      1,
    )}/${this.get('uuid')}.jpg`;
  }
}
