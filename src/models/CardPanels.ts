import * as Immutable from 'immutable';
import { DisplayMode } from '../views/components/cardpanel/DisplayImageSwitch';
import { CardAttributes } from './CardAttributes';
import { AnyCardGroup } from './CardGroup';
import { Game } from './Game';
import { Language } from './Language';
import { MTGCardGroup } from './mtg/MTGCardPage';
import { Record } from './Records';
import { Tag } from './Tags';

export enum CardPanelType {
  STAGING = 'staging',
  COLLECTION_VIEW = 'view',
  COLLECTION_EDIT = 'edit',
}

export interface ICardPanel {
  readonly x: number | null;
  readonly y: number | null;
  readonly game: Game;
  readonly cardGroup: AnyCardGroup;
  readonly tags: Immutable.OrderedSet<Tag>;
  readonly languageOptions: Immutable.Set<Language>;
  readonly printingOptionsReady: boolean;
  readonly cardPanelType: CardPanelType;
  readonly unsavedCardAttributes: CardAttributes;
  readonly displayMode: DisplayMode;
  readonly scanViewed: boolean;
  readonly tagsToAdd: Immutable.OrderedMap<string, Tag>;
  readonly tagsToRemove: Immutable.OrderedMap<string, Tag>;
}

export class CardPanel extends Record<ICardPanel>({
  x: null,
  y: null,
  game: Game.MTG,
  cardGroup: new MTGCardGroup(),
  tags: Immutable.OrderedSet<Tag>(),
  languageOptions: Immutable.Set<Language>(),
  printingOptionsReady: false,
  cardPanelType: CardPanelType.COLLECTION_VIEW,
  unsavedCardAttributes: new CardAttributes(),
  displayMode: DisplayMode.SOURCE,
  scanViewed: false,
  tagsToAdd: Immutable.OrderedMap<string, Tag>(),
  tagsToRemove: Immutable.OrderedMap<string, Tag>(),
}) {
  // Returns a fresh CardPanel with some state persisting from the previous record
  static reset(cardPanel: CardPanel): CardPanel {
    return new CardPanel({
      displayMode: cardPanel.get('displayMode'),
      scanViewed: cardPanel.get('displayMode') === DisplayMode.SCAN,
    });
  }
}
