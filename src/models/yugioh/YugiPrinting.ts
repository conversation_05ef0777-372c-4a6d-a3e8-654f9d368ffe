import * as Immutable from 'immutable';
import { CoreAssets, ImageQuality } from '../../helpers/core_assets';
import { Language, validateLanguages } from '../Language';
import { PrintingMethods } from '../Printing';
import { Record } from '../Records';
import { rarityFromAPI, YugiRarity } from './YugiRarity';

export interface IYugiPrinting {
  readonly uuid: string;
  readonly setUUID: string;
  readonly setName: string;
  readonly collectorNumber?: string;
  readonly rarity: YugiRarity;
  readonly languages: Immutable.Set<Language>;
}

export class YugiPrinting
  extends Record<IYugiPrinting>({
    uuid: '',
    setUUID: '',
    setName: '',
    collectorNumber: '',
    rarity: YugiRarity.COMMON,
    languages: Immutable.OrderedSet<Language>([Language.ENGLISH]),
  })
  implements PrintingMethods
{
  validateLanguage(language: string): Immutable.Map<string, any> {
    return validateLanguages(this.get('languages'), language);
  }

  identifier(): string {
    return this.get('uuid');
  }

  displayLabel(): string {
    if (this.get('collectorNumber')) {
      return this.get('setName') + ' (' + this.get('collectorNumber') + ')';
    } else {
      return this.get('setName');
    }
  }

  static fromAPI(data: any): YugiPrinting {
    return new YugiPrinting({
      uuid: data.json_id,
      setUUID: data.set_code,
      setName: data.set_name,
      collectorNumber: data.collector_number,
      rarity: rarityFromAPI(data.rarity),
    });
  }

  static match(uuid: string, printingOptions: Immutable.List<YugiPrinting>): YugiPrinting | undefined {
    return printingOptions.find((printing: YugiPrinting) => printing.get('uuid') === uuid);
  }

  displayText() {
    if (this.get('collectorNumber')) {
      return `${this.get('setName')} (${this.get('collectorNumber')})`;
    } else {
      return this.get('setName');
    }
  }

  // TODO: Migrate to YugiCardSet once images are seeded
  imageURL(imageQuality: ImageQuality, _language: Language): string {
    return `${CoreAssets.imageHost()}/yugioh/${imageQuality}/${this.get('uuid').charAt(0)}/${this.get('uuid').charAt(
      1,
    )}/${this.get('uuid')}.jpg`;
  }
}
