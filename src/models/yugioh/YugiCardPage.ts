import * as Immutable from 'immutable';
import * as moment from 'moment';
import { DateFormat } from '../../helpers/fmt';
import { CardGroupMethods, IYugiCardGroup } from '../CardGroup';
import { CardPage, ICardPageMethods } from '../CardPage';
import { YugiFilter } from '../filters/yugioh/YugiFilters';
import { Grouping } from '../Grouping';
import { Language } from '../Language';
import { Record } from '../Records';
import { ScanMetadata } from '../ScanMetadata';
import { OrderingCodes, Sorting, SortingCodes } from '../sorting/Sorting';
import { Viewing } from '../Viewing';
import { IYugiCardInstance, YugiCardInstance } from './YugiCardInstances';
import { IYugiCard, IYugiCardSet, YugiCard, YugiCardSet } from './YugiCards';
import { YugiPrinting } from './YugiPrinting';
import { IYugiSet } from './YugiSet';

export class YugiCardGroup
  extends Record<IYugiCardGroup>({
    cardInstanceLatest: new Date(),
    cardInstanceCount: 0,
    cardInstancePrice: 0,
    cardInstances: Immutable.List<YugiCardInstance>(),
    printingOptions: Immutable.List<YugiPrinting>(),
  })
  implements CardGroupMethods<IYugiCardInstance, YugiCardInstance, YugiCardSet>
{
  setScanMetadata(scanMetadata: Immutable.Map<string, ScanMetadata>): this {
    return this.setInstances(
      this.getInstances()
        .map((cardInstance: YugiCardInstance) =>
          cardInstance.set('scanMetadata', scanMetadata.get(cardInstance.get('uuid'))),
        )
        .toList(),
    );
  }
  selectedPrinting(
    printingID?: string,
    languageOptions = Immutable.OrderedSet<Language>([Language.ENGLISH]),
  ): YugiPrinting {
    let matchedPrinting: YugiPrinting | undefined = undefined;

    if (printingID) {
      matchedPrinting = YugiPrinting.match(printingID, this.get('printingOptions'));
    }

    if (matchedPrinting) {
      return matchedPrinting;
    }

    // cardSetValues requires a defined string, but printing supports undefined
    let collectorNumber: string | undefined = this.cardSetValues('code', 'Multiple', '');

    if (collectorNumber.length == 0) {
      collectorNumber = undefined;
    }

    return new YugiPrinting({
      uuid: this.cardSetValues('uuid', 'Multiple', ''),
      setName: this.cardSetNestedValues('name', 'Multiple', 'Unknown'),
      setUUID: this.cardSetNestedValues('uuid', 'Multiple', 'Unknown'),
      collectorNumber: collectorNumber,
      languages: languageOptions,
    });
  }

  printingOptions(): Immutable.List<YugiPrinting> {
    const allOptions = this.getInstances()
      .flatMap((cardInstance: YugiCardInstance) => cardInstance.get('card').get('setOptions').values())
      .toList();

    const printings = allOptions
      .toSet()
      .map((cardSet: YugiCardSet) => {
        return new YugiPrinting({
          uuid: cardSet.get('uuid'),
          setName: cardSet.get('set').get('name'),
          setUUID: cardSet.get('set').get('uuid'),
          collectorNumber: cardSet.get('code'),
        });
      })
      .toList();

    return printings;
  }

  priceSummary(): number {
    return this.get('cardInstances').reduce(
      (price: number, cardInstance: YugiCardInstance) => price + cardInstance.get('price'),
      0,
    );
  }

  timeSummary(timezone: string): string | undefined {
    if (this.get('cardInstances').size > 0) {
      const latest = this.get('cardInstances').get(0).get('createdAt');
      return DateFormat.human(latest, timezone);
    }

    return undefined;
  }

  collect<K extends keyof IYugiCardInstance, V extends IYugiCardInstance[K]>(propertyName: K): Immutable.Set<V> {
    return Immutable.Set(this.get('cardInstances').map((instance: YugiCardInstance) => instance.get(propertyName)));
  }

  groupValue<K extends keyof IYugiCardInstance, V extends IYugiCardInstance[K], M>(
    propertyName: K,
    multipleValue: M,
    defaultValue: V,
  ): V | M {
    const uniqueValues = this.get('cardInstances')
      .map((instance: YugiCardInstance) => instance.get(propertyName) as V)
      .toSet();

    if (uniqueValues.size > 0) {
      if (uniqueValues.size == 1) {
        return uniqueValues.first();
      } else {
        return multipleValue;
      }
    } else {
      return defaultValue;
    }
  }

  cardValues<K extends keyof IYugiCard, V extends IYugiCard[K], M>(
    propertyName: K,
    multipleValue: M,
    defaultValue: V,
  ): V | M {
    const uniqueValues = this.get('cardInstances')
      .map((instance: YugiCardInstance) => instance.get('card').get(propertyName) as V)
      .toSet();

    if (uniqueValues.size > 0) {
      if (uniqueValues.size == 1) {
        return uniqueValues.first();
      } else {
        return multipleValue;
      }
    } else {
      return defaultValue;
    }
  }

  cardSetValues<K extends keyof IYugiCardSet, V extends IYugiCardSet[K], M>(
    propertyName: K,
    multipleValue: M,
    defaultValue: V,
  ): V | M {
    const uniqueValues = this.get('cardInstances')
      .map((instance: YugiCardInstance) => {
        const cardSet = instance.get('cardSet');
        return cardSet && (cardSet.get(propertyName) as V);
      })
      .toSet();

    // Count undefined as a value in terms of displaying multipleValue, but ignore if it's the only value
    if (uniqueValues.size > 0) {
      if (uniqueValues.size == 1) {
        const firstValue = uniqueValues.first();
        if (firstValue) {
          return firstValue;
        } else {
          return defaultValue;
        }
      } else {
        return multipleValue;
      }
    } else {
      return defaultValue;
    }
  }

  cardSetNestedValues<K extends keyof IYugiSet, V extends IYugiSet[K], M>(
    propertyName: K,
    multipleValue: M,
    defaultValue: V,
  ): V | M {
    const uniqueValues = this.get('cardInstances')
      .map((instance: YugiCardInstance) => {
        const set = instance.get('cardSet')?.get('set');
        return set && (set.get(propertyName) as V);
      })
      .toSet();

    // Count undefined as a value in terms of displaying multipleValue, but ignore if it's the only value
    if (uniqueValues.size > 0) {
      if (uniqueValues.size == 1) {
        const firstValue = uniqueValues.first() as V;
        if (firstValue) {
          return firstValue;
        } else {
          return defaultValue;
        }
      } else {
        return multipleValue;
      }
    } else {
      return defaultValue;
    }
  }

  getInstances(): Immutable.List<YugiCardInstance> {
    return this.get('cardInstances');
  }

  setInstances(cardInstances: Immutable.List<YugiCardInstance>): this {
    return this.set('cardInstances', cardInstances);
  }

  updateInstances(updated: Immutable.List<YugiCardInstance>): this {
    const appliedUpdates = this.get('cardInstances')
      .map((cardInstance: YugiCardInstance) => {
        const match = updated.find((candidate: YugiCardInstance) => cardInstance.get('uuid') === candidate.get('uuid'));
        if (match) {
          return cardInstance.applyUpdates(match);
        }
        return cardInstance;
      })
      .toList();

    return this.set('cardInstances', appliedUpdates);
  }

  removeInstances(removals: Immutable.List<YugiCardInstance>): this {
    const removedIDs = removals.map((removal: YugiCardInstance) => removal.get('uuid'));
    const appliedRemovals = this.get('cardInstances')
      .filterNot((cardInstance: YugiCardInstance) => removedIDs.contains(cardInstance.get('uuid')))
      .toList();

    return this.set('cardInstances', appliedRemovals);
  }

  count(): number {
    return this.get('cardInstances').count();
  }

  static fromAPI(
    collection_item: any,
    cards: Immutable.Map<string, YugiCard>,
    scanMetadata?: Immutable.Map<string, ScanMetadata>,
  ): YugiCardGroup {
    if (collection_item === undefined) {
      return new YugiCardGroup({});
    }
    return new YugiCardGroup({
      cardInstanceLatest: moment.utc(collection_item.latest_created).toDate(),
      cardInstanceCount: collection_item.group_count,
      cardInstancePrice: collection_item.group_price,
      cardInstances: collection_item.group_elements.map((element: any) =>
        YugiCardInstance.fromAPI(element, cards, scanMetadata),
      ),
    });
  }
}

export class YugiCardPage
  extends CardPage({
    ownerUsername: '',
    totalCount: 0,
    totalGroupCount: 0,
    totalValue: 0,
    pageCount: 0,
    pageValue: 0,
    cardGroups: Immutable.List<YugiCardGroup>(),
    cardGrouping: Grouping.PRINTING,
    cardSorting: Sorting.DATE_ADDED_DESC,
    cardViewing: Viewing.GRID,
    filterOptions: new YugiFilter(),
  })
  implements ICardPageMethods<YugiCardPage, YugiCardInstance>
{
  toAPI(): any {
    const payload: any = this.get('filterOptions').toAPI() || {};
    payload.sort_by = SortingCodes[this.get('cardSorting')];
    payload.order = OrderingCodes[this.get('cardSorting')];
    payload.group_by = this.get('cardGrouping');
    return payload;
  }

  updateCardInstances(updates: Immutable.List<YugiCardInstance>): YugiCardPage {
    const updatedGroups = this.get('cardGroups')
      .map((cardGroup: YugiCardGroup) => cardGroup.updateInstances(updates))
      .toList();
    return this.set('cardGroups', updatedGroups);
  }

  removeInstances(removals: Immutable.List<YugiCardInstance>): YugiCardPage {
    const updatedGroups = this.get('cardGroups')
      .map((cardGroup: YugiCardGroup) => cardGroup.removeInstances(removals))
      .filter((cardGroup: YugiCardGroup) => cardGroup.count() > 0)
      .toList();
    return this.set('cardGroups', updatedGroups);
  }
}
