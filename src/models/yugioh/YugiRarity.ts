export enum YugiRarity {
  COMMON = 'Common',
}

export function rarityFromAPI(rawValue: string): YugiRarity {
  if (!rawValue) {
    console.error(`Invalid rarity ${rawValue} converted to COMMON`);
    return YugiRarity.COMMON;
  }

  const rarity = rawValue.toLowerCase() as YugiRarity;
  if (!rarity) {
    // TODO: Add Bugsnag notifier when we work out how to get that working without causing Je<PERSON> to lose it.
    console.error(`Invalid rarity ${rawValue} converted to COMMON`);
    return YugiRarity.COMMON;
  }
  return rarity;
}
