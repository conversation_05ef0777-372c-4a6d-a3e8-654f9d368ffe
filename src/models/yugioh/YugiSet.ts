import moment from 'moment';
import { CardSetMethods } from '../AnyCardSet';
import { Record } from '../Records';

/**
 * A representation of a Yugimon card set.
 */
export interface IYugiSet {
  readonly uuid: string;
  readonly name: string;
  readonly releaseDate: Date;
  readonly setCode: string;
}

export class YugiSet
  extends Record<IYugiSet>({
    uuid: '',
    name: '',
    releaseDate: new Date(),
    setCode: '',
  })
  implements CardSetMethods
{
  name(): string {
    return this.get('name');
  }

  static fromAPI(data: any) {
    return new YugiSet({
      uuid: data.uuid,
      name: data.name,
      releaseDate: moment.utc(data.release_date).toDate(),
      setCode: data.set_code,
    });
  }
}
