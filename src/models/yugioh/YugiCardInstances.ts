import * as Immutable from 'immutable';
import moment from 'moment';
import { CoreAssets, ImageQuality } from '../../helpers/core_assets';
import { EnumHelper } from '../../helpers/enum';
import { Condition } from '../Condition';
import { Game } from '../Game';
import { Language } from '../Language';
import { Record } from '../Records';
import { ScanMetadata } from '../ScanMetadata';
import { YugiCard, YugiCardPrinting, YugiCardSet } from './YugiCards';

/**
 * An instance of a Yugimon card
 */
export interface IYugiCardInstance {
  readonly uuid: string;
  readonly condition: Condition;
  readonly createdAt: Date;
  readonly card: YugiCard;
  readonly cardSet?: YugiCardSet;
  readonly printing?: YugiCardPrinting;
  readonly price: number;
  readonly inputSessionUUID: string;
  readonly scannedImageURL?: string;
  readonly scanMetadata?: ScanMetadata;
}

export class YugiCardInstance extends Record<IYugiCardInstance>({
  uuid: '',
  condition: Condition.NEAR_MINT,
  createdAt: new Date(),
  card: new YugiCard(),
  cardSet: undefined,
  printing: undefined,
  price: 0,
  inputSessionUUID: '',
  scannedImageURL: undefined,
  scanMetadata: undefined,
}) {
  // Convert from an API blob into a CardInstance.
  static fromAPI(
    data: any,
    cards: Immutable.Map<string, YugiCard>,
    scanMetadata?: Immutable.Map<string, ScanMetadata>,
  ): YugiCardInstance {
    const card = cards.get(data.card_uuid);
    const cardSet = (data.card_set_uuid && card.get('setOptions').get(data.card_set_uuid)) || undefined;
    const printing = (data.card_printing_uuid && card.get('printingOptions').get(data.card_printing_uuid)) || undefined;
    const matchedMetadata = scanMetadata?.get(data.uuid);

    return new YugiCardInstance({
      uuid: data.uuid,
      condition: (EnumHelper.match(Condition, data.condition) as Condition) || Condition.NEAR_MINT,
      createdAt: moment.utc(data.created_at).toDate(),
      card: card,
      cardSet: cardSet,
      printing: printing,
      price: data.price || 0,
      inputSessionUUID: data.input_session_uuid,
      scannedImageURL: data.scanned_image_url || undefined,
      scanMetadata: matchedMetadata || (data.scan_metadata && ScanMetadata.fromAPI(data.scan_metadata)),
    });
  }

  // Named as such as the name "update" conflicts with a immutable-js Record function.
  public applyUpdates(updated: YugiCardInstance): YugiCardInstance {
    return (this as YugiCardInstance)
      .set('condition', updated.get('condition'))
      .set('card', updated.get('card'))
      .set('cardSet', updated.get('cardSet'))
      .set('price', updated.get('price'))
      .set('printing', updated.get('printing'));
  }

  // TODO: Migrate to YugiCard imageURL() once Printing is migrated to be a YugiCardSet
  public imageURL(imageQuality: ImageQuality, language = Language.ENGLISH, printingOverride?: string): string {
    let printingID = printingOverride;
    if (!printingID) {
      const printing = this.get('printing');
      if (printing) {
        printingID = printing.get('uuid');
      } else {
        printingID = this.get('card').get('printingOptions').first()?.get('uuid');
      }
    }

    if (!printingID) {
      return CoreAssets.cardBack(Game.YUGIOH, imageQuality);
    }

    return `${CoreAssets.imageHost()}/yugioh/${imageQuality}/${printingID.charAt(0)}/${printingID.charAt(
      1,
    )}/${printingID}.jpg`;
  }
}

export type YugiCardInstanceKey = keyof IYugiCardInstance;

export function updateMatches(
  existing: Immutable.List<YugiCardInstance>,
  updates: Immutable.List<YugiCardInstance>,
): Immutable.List<YugiCardInstance> {
  return existing
    .map((cardInstance: YugiCardInstance) => {
      const match = updates
        .filter((update: YugiCardInstance) => cardInstance.get('uuid') === update.get('uuid'))
        .first();
      if (match) {
        cardInstance = cardInstance.applyUpdates(match);
      }
      return cardInstance;
    })
    .toList();
}
