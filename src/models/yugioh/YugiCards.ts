import * as Immutable from 'immutable';
import { CoreAssets, ImageQuality } from '../../helpers/core_assets';
import { CardMethods } from '../AnyCard';
import { Language } from '../Language';
import { Record } from '../Records';
import { rarityFromAPI, YugiRarity } from './YugiRarity';
import { YugiSet } from './YugiSet';

/**
 * A representation of a Yugimon card. Several instances of
 * this card will be owned by different users, and will have different
 * prices, foils, and qualities.
 */
export interface IYugiCard {
  readonly uuid: string;
  readonly name: string;
  readonly level: number;
  readonly attack: number;
  readonly defense: number;
  readonly type: string;
  readonly race?: string;
  readonly description: string;
  readonly setOptions: Immutable.Map<string, YugiCardSet>;
  readonly printingOptions: Immutable.Map<string, YugiCardPrinting>;
}

export class YugiCard extends Record<IYugiCard>({
  uuid: '',
  name: '',
  level: 0,
  attack: 0,
  defense: 0,
  type: '',
  description: '',
  setOptions: Immutable.Map<string, YugiCardSet>(),
  printingOptions: Immutable.Map<string, YugiCardPrinting>(),
}) {
  static fromAPI(data: any, sets: Immutable.Map<string, YugiSet>): YugiCard {
    const card = new YugiCard({
      uuid: data.uuid,
      name: data.name,
      level: data.level,
      attack: data.attack,
      defense: data.defense,
      type: data.type,
      description: data.description,
      race: data.race,
      printingOptions: Immutable.Map<string, any>(data.printings)
        .map((data: any) => YugiCardPrinting.fromAPI(data))
        .toMap(),
    });

    const setOptions = Immutable.Map<string, any>(data.card_sets)
      .map((data: any) => YugiCardSet.fromAPICard(data, sets, card))
      .toMap();

    return card.set('setOptions', setOptions);
  }

  matchSet(uuid?: string): YugiCardSet | undefined {
    if (uuid) {
      return this.get('setOptions').find((cardSet: YugiCardSet) => cardSet.get('uuid') === uuid);
    } else {
      this.get('setOptions').first();
    }
  }

  matchPrinting(uuid?: string): YugiCardPrinting | undefined {
    if (uuid) {
      return this.get('printingOptions').find((cardSet: YugiCardPrinting) => cardSet.get('uuid') === uuid);
    } else {
      this.get('printingOptions').first();
    }
  }
}

export interface IYugiCardSet {
  readonly uuid: string;
  readonly set: YugiSet;
  readonly card: YugiCard;
  readonly rarity: YugiRarity;
  readonly code: string;
}

export class YugiCardSet
  extends Record<IYugiCardSet>({
    uuid: '',
    card: new YugiCard({}),
    set: new YugiSet({}),
    rarity: YugiRarity.COMMON,
    code: '',
  })
  implements CardMethods
{
  displayLabel(): string {
    return `${this.get('card').get('name')} - ${this.get('set').get('name')} (${this.get('code')})`;
  }

  static fromAPI(data: any, sets: Immutable.Map<string, YugiSet>, cards: Immutable.Map<string, YugiCard>) {
    return new YugiCardSet({
      uuid: data.uuid,
      set: sets.get(data.set_uuid),
      card: cards.get(data.card_uuid),
      rarity: rarityFromAPI(data.rarity),
      code: data.code,
    });
  }

  static fromAPICard(data: any, sets: Immutable.Map<string, YugiSet>, card: YugiCard) {
    return new YugiCardSet({
      uuid: data.uuid,
      set: sets.get(data.set_uuid),
      card: card,
      rarity: rarityFromAPI(data.rarity),
      code: data.code,
    });
  }

  imageURL(imageQuality: ImageQuality, _language: Language): string {
    return `${CoreAssets.imageHost()}/yugioh/${imageQuality}/${this.get('uuid').charAt(0)}/${this.get('uuid').charAt(
      1,
    )}/${this.get('uuid')}.jpg`;
  }
}

// API model, as opposed to internal YugiPrinting which is essentially used as a stand-in for a card
export interface IYugiCardPrinting {
  readonly uuid: string;
  readonly index: number;
}

export class YugiCardPrinting extends Record<IYugiCardPrinting>({
  uuid: '',
  index: 0,
}) {
  static fromAPI(data: any) {
    return new YugiCardPrinting({
      uuid: data.uuid,
      index: data.printing_idx,
    });
  }
}
