import * as Immutable from 'immutable';
import moment from 'moment';
import { CoreAssets, ImageQuality } from '../helpers/core_assets';
import { DateFormat } from '../helpers/fmt';
import { Condition } from './Condition';
import { Foil } from './Foil';
import { Language } from './Language';
import { LinkedResources } from './LinkedResources';
import { Rarity, rarityFromAPI } from './Rarity';
import { Record } from './Records';
import { ScanMetadata } from './ScanMetadata';

/**
 * An instance of a "Magic: The Gathering" card. Each instance has different
 * properties for details such as price, foil, and condition.
 */

export interface ICardInstance {
  readonly id: number;
  readonly uuid: string;
  readonly foil: Foil;
  readonly condition: Condition;
  readonly language: Language;
  readonly foreignName: string;
  readonly createdAt: Date;

  /**
   * Card details are flattened into the card instance
   */
  readonly cardID: number;
  readonly cardJsonID: string;
  readonly cardName: string;
  readonly cardSetName: string;
  readonly cardSetCode: string;
  readonly cardCollectorNumber: string;
  readonly cardPrice: number;
  readonly cardManaCost: string;
  readonly cardTypes: Immutable.List<string>;
  readonly cardSubTypes: Immutable.List<string>;
  readonly cardRarity: Rarity;
  readonly linkedResources?: LinkedResources;
  readonly scannedImageURL?: string;
  readonly scanMetadata?: ScanMetadata;
}

export class CardInstance extends Record<ICardInstance>({
  id: 0,
  uuid: '',
  cardID: 0,
  foil: Foil.Off,
  condition: Condition.NEAR_MINT,
  language: Language.ENGLISH,
  createdAt: new Date(),
  foreignName: '',
  cardJsonID: '',
  cardName: '',
  cardSetName: '',
  cardSetCode: '',
  cardCollectorNumber: '',
  cardPrice: 0,
  cardManaCost: '',
  cardTypes: Immutable.List<string>(),
  cardSubTypes: Immutable.List<string>(),
  cardRarity: Rarity.COMMON,
  linkedResources: undefined,
  scannedImageURL: undefined,
  scanMetadata: undefined,
}) {
  // Convert from an API blob into a CardInstance.
  static fromAPI(card_user: any, scanMetadata?: Immutable.Map<string, ScanMetadata>): CardInstance {
    const matchedMetadata = scanMetadata?.get(card_user.uuid);

    return new CardInstance({
      id: card_user.id,
      uuid: card_user.uuid,
      foil: card_user.foil ? Foil.On : Foil.Off,
      condition: card_user.quality && card_user.quality.length ? card_user.quality : Condition.NEAR_MINT,
      language: card_user.language,
      foreignName: card_user.foreign_name,
      createdAt: moment.utc(card_user.created_at).toDate(),
      cardID: card_user.card_id,
      cardJsonID: card_user.json_id,
      cardName: card_user.name,
      cardPrice: card_user.price,
      cardSetCode: card_user.set_code,
      cardSetName: card_user.set_name,
      cardCollectorNumber: card_user.collector_number,
      cardTypes: Immutable.List<string>(card_user.types),
      cardSubTypes: Immutable.List<string>(card_user.sub_type),
      cardRarity: rarityFromAPI(card_user.rarity),
      cardManaCost: card_user.mana_cost,
      linkedResources: LinkedResources.fromCardUser(card_user),
      scannedImageURL: card_user.scanned_image_url || undefined, // Raw API response will result in a `null` value
      scanMetadata: matchedMetadata || (card_user.scan_metadata && ScanMetadata.fromAPI(card_user.scan_metadata)),
    });
  }

  // Named as such as the name "update" conflicts with a immutable-js Record function.
  public applyUpdates(updated: CardInstance): CardInstance {
    return (this as CardInstance)
      .set('foil', updated.get('foil'))
      .set('condition', updated.get('condition'))
      .set('language', updated.get('language'))
      .set('foreignName', updated.get('foreignName'))
      .set('cardPrice', updated.get('cardPrice'))
      .set('cardRarity', updated.get('cardRarity'))
      .set('cardSetCode', updated.get('cardSetCode'))
      .set('cardSetName', updated.get('cardSetName'))
      .set('cardCollectorNumber', updated.get('cardCollectorNumber'))
      .set('cardJsonID', updated.get('cardJsonID'))
      .set('cardName', updated.get('cardName'));
  }

  // TODO: Refactor to use Card imageURL() function once data is no longer flattened into CardInstance
  public imageURL(
    imageQuality: ImageQuality,
    language = this.get('language'),
    printingID = this.get('cardJsonID'),
  ): string {
    const imagePath = language + '/' + printingID + '.jpg';
    let qualityPath = 'card_images';
    if (imageQuality == ImageQuality.HQ) qualityPath = `${qualityPath}_hq`;

    return `${CoreAssets.imageHost()}/${qualityPath}/${imagePath}`;
  }
}

export type CardInstances = Immutable.List<CardInstance>;
export type CardInstanceKey = keyof ICardInstance;

// DEPRECATED - Use cardGroup.groupValue()
/* This function takes a list of 'cardProperties' and one of the properties of 'cardProperties'
 * (see type definition of 'property'). It returns either the property (if all of 'property' of
 * each 'cardProperty is identical and not undefined), undefined (if all of 'property' of each
 *  'cardProperty' is undefined) or the string 'Multiple' (if at least two of 'property' of each 'cardProperty'
 * are not identical.)
 */

export function ResolveGroupValue(
  cardInstances: CardInstances,
  propertyName: 'cardSetCode' | 'cardSetName' | 'cardCollectorNumber' | 'cardJsonID' | 'condition' | 'language',
): Condition | Language | /*jsonID or cardSetName or cardSetCode 'Multiple'*/ string {
  return (
    cardInstances.reduce(
      (
        property: Condition | Language | /*jsonID or cardSetName or cardSetCode 'Multiple'*/ string | undefined,
        cardInstance: CardInstance,
      ) => {
        if (property === undefined) {
          return cardInstance.get(propertyName);
        } else if (property === cardInstance.get(propertyName)) {
          return cardInstance.get(propertyName);
        } else {
          return 'Multiple';
        }
      },
      undefined,
    ) || '' //if cardInstances is empty then .reduce() returns undefined
  );
}

// Provides a standard return type when resolving group attributes
export type GroupValue<V> = V | 'Multiple';

// DEPRECATED - Use cardGroup.groupValue()
// Generic implementation of ResolveGroupValue
export function ResolveValue<K extends keyof ICardInstance, V extends ICardInstance[K], M>(
  cardInstances: CardInstances,
  propertyName: K,
  multipleValue: M,
  defaultValue: V,
): V | M {
  const uniqueValues = cardInstances.map((instance: CardInstance) => instance.get(propertyName) as V).toSet();

  if (uniqueValues.size > 0) {
    if (uniqueValues.size == 1) {
      return uniqueValues.first();
    } else {
      return multipleValue;
    }
  } else {
    return defaultValue;
  }
}

// DEPRECATED - Use cardGroup.groupValue()
// Deals with edge cases that cannot be easily handled by ResolveGroupValue
export function ResolveFoil(cardInstances: CardInstances): Foil {
  return cardInstances.reduce((foil: Foil | undefined, cardInstance: CardInstance) => {
    if (foil === undefined) {
      return cardInstance.get('foil');
    } else if (foil === cardInstance.get('foil')) {
      return foil;
    } else {
      return Foil.Partial;
    }
  }, undefined);
}

// DEPRECATED: Use priceSummary on CardGroups
export function ResolvePrice(cardInstances: CardInstances) {
  return cardInstances.reduce((price: number, cardInstance: CardInstance) => {
    if (!cardInstance) {
      return price;
    }
    return price + cardInstance.get('cardPrice');
  }, 0);
}

// DEPRECATED: Use timeSummary on CardGroups
export function ResolveTimeAdded(cardInstances: CardInstances, timezone: string) {
  if (cardInstances.size > 0) {
    const latest = cardInstances.get(0).get('createdAt');
    return DateFormat.human(latest, timezone);
  }

  return null;
}
