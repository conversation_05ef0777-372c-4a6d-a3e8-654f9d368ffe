// TODO: Delete this file when an API to extract all the valid subtypes from the backend is implemented and use that instead.
export const validSubtypes = [
  'Abian',
  '<PERSON>',
  'Advisor',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Army',
  'Art',
  'Artificer',
  'Ash<PERSON><PERSON>',
  'Assassin',
  'Assembly-Worker',
  'Astar<PERSON>',
  'At<PERSON>',
  'Attraction',
  '<PERSON>ra',
  'Aurochs',
  'Autobot',
  'Ava<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'B.O.B.',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>,',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  'Barbarian',
  'Bard',
  'Basil<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  'Be<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>,',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  "<PERSON>las's Meditation Realm",
  '<PERSON><PERSON>',
  'Bringer',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  "<PERSON>'tan",
  'Calix',
  '<PERSON>l',
  'Carrier',
  'Cartouche',
  'Cat',
  'Centaur',
  'Cephalid',
  'Chameleon',
  'Chandra',
  'Chicken',
  'Child',
  'Chimera',
  'Citizen',
  'Clamfolk',
  'Class',
  'Cleric',
  '<PERSON>',
  'Clown',
  'Clue',
  'Cockatrice',
  'Comet',
  '<PERSON>struct',
  'Contraption',
  'Cow',
  'Coward',
  'Crab',
  'Crocodile',
  'Curse',
  'Custodes',
  'Cyborg',
  'Cyclops',
  'Dack',
  'Dakkon',
  'Daretti',
  'Dauthi',
  'Davriel',
  'Deer',
  'Demigod',
  'Demon',
  'Desert',
  'Designer',
  'Devil',
  'Dihada',
  'Dinosaur',
  'Djinn',
  'Dog',
  'Dominaria',
  'Domri',
  'Donkey',
  'Dovin',
  'Dragon',
  'Drake',
  'Dreadnought',
  'Drone',
  'Druid',
  'Dryad',
  'Duck',
  'Dungeon',
  'Dwarf',
  'Efreet',
  'Egg',
  'Elder',
  'Eldrazi',
  'Elemental',
  'Elemental?',
  'Elephant',
  'Elf',
  'Elk',
  'Ellywick',
  'Elminster',
  'Elspeth',
  'Elves',
  'Employee',
  'Equilor',
  'Equipment',
  'Ergamon',
  'Ersta',
  'Estrid',
  'Etiquette',
  'Eye',
  'Fabacin',
  'Faerie',
  'Ferret',
  'Fire',
  'Fish',
  'Flagbearer',
  'Food',
  'Forest',
  'Fortification',
  'Fox',
  'Fractal',
  'Freyalise',
  'Frog',
  'Fungus',
  'Gamer',
  'Gargoyle',
  'Garruk',
  'Gate',
  'Germ',
  'Giant',
  'Gideon',
  'Gith',
  'Gnoll',
  'Gnome',
  'Goat',
  'Goblin',
  'God',
  'Gold',
  'Golem',
  'Gorgon',
  'Grandchild',
  'Gremlin',
  'Griffin',
  'Grist',
  'Guest',
  'Gus',
  'Hag',
  'Halfling',
  'Hamster',
  'Harpy',
  'Hatificer',
  'Head',
  'Hellion',
  'Hero',
  'Hippo',
  'Hippogriff',
  'Homarid',
  'Homunculus',
  'Horror',
  'Horse',
  'Huatli',
  'Human',
  'Hydra',
  'Hyena',
  'Igpay',
  'Illusion',
  'Imp',
  'Incarnation',
  'Inkling',
  'Innistrad',
  'Inquisitor',
  'Insect',
  'Inzerva',
  'Iquatana',
  'Ir',
  'Island',
  'Jace',
  'Jackal',
  'Jared',
  'Jaya',
  'Jellyfish',
  'Jeska',
  'Juggernaut',
  'Kaito',
  'Kaldheim',
  'Kamigawa',
  'Kangaroo',
  'Karn',
  'Karsus',
  'Kasmina',
  'Kavu',
  'Kaya',
  'Kephalai',
  'Key',
  'Killbot',
  'Kinshala',
  'Kiora',
  'Kirin',
  'Kithkin',
  'Knight',
  'Kobold',
  'Kolbahan',
  'Kor',
  'Koth',
  'Kraken',
  'Kyneth',
  'Lady',
  'Lair',
  'Lamia',
  'Lammasu',
  'Leech',
  'Legend',
  'Lesson',
  'Leviathan',
  'Lhurgoyf',
  'Licid',
  'Liliana',
  'Lizard',
  'Lobster',
  'Locus',
  'Lolth',
  'Lorwyn',
  'Lukka',
  'Luvion',
  'Mammoth',
  'Manticore',
  'Master',
  'Masticore',
  'Mercadia',
  'Mercenary',
  'Merfolk',
  'Metathran',
  'Mime',
  'Mine',
  'Minion',
  'Minotaur',
  'Minsc',
  'Mirrodin',
  'Mite',
  'Moag',
  'Mole',
  'Monger',
  'Mongoose',
  'Mongseng',
  'Monk',
  'Monkey',
  'Moonfolk',
  'Mordenkainen',
  'Mountain',
  'Mouse',
  'Mummy',
  'Muraganda',
  'Mutant',
  'Myr',
  'Mystic',
  'Naga',
  'Nahiri',
  'Narset',
  'Nastiest,',
  'Nautilus',
  'Necron',
  'Nephilim',
  'New Phyrexia',
  'Nightmare',
  'Nightstalker',
  'Niko',
  'Ninja',
  'Nissa',
  'Nixilis',
  'Noble',
  'Noggle',
  'Nomad',
  'Nymph',
  'Octopus',
  'Of',
  'Ogre',
  'Oko',
  'Ooze',
  'Orc',
  'Orgg',
  'Otter',
  'Ouphe',
  'Ox',
  'Oyster',
  'Pangolin',
  'Paratrooper',
  'Peasant',
  'Pegasus',
  'Penguin',
  'Pentavite',
  'Performer',
  'Pest',
  'Phelddagrif',
  'Phoenix',
  'Phyrexia',
  'Phyrexian',
  'Pilot',
  'Pirate',
  'Plains',
  'Plant',
  'Porcupine',
  'Power-plant',
  'Powerstone',
  'Praetor',
  'Primarch',
  'Processor',
  'Proper',
  'Pyrulea',
  'Rabbit',
  'Rabiah',
  'Raccoon',
  'Ral',
  'Ranger',
  'Rat',
  'Rath',
  'Ravnica',
  'Rebel',
  'Reflection',
  'Regatha',
  'Reveler',
  'Rhino',
  'Rigger',
  'Robot',
  'Rogue',
  'Rowan',
  'Rune',
  'Sable',
  'Saga',
  'Saheeli',
  'Salamander',
  'Samurai',
  'Samut',
  'Sand',
  'Saproling',
  'Sarkhan',
  'Satyr',
  'Scarecrow',
  'Scientist',
  'Scion',
  'Scorpion',
  'Scout',
  'Sculpture',
  'Segovia',
  'Serf',
  'Serpent',
  'Serra',
  "Serra's Realm",
  'Servo',
  'Shade',
  'Shadowmoor',
  'Shaman',
  'Shandalar',
  'Shapeshifter',
  'Shard',
  'Shark',
  'Sheep',
  'Ship',
  'Shrine',
  'Siren',
  'Sivitri',
  'Skeleton',
  'Slith',
  'Sliver',
  'Slug',
  'Snake',
  'Soldier',
  'Soltari',
  'Sorin',
  'Spawn',
  'Specter',
  'Spellshaper',
  'Sphere',
  'Sphinx',
  'Spider',
  'Spike',
  'Spirit',
  'Sponge',
  'Spy',
  'Squid',
  'Squirrel',
  'Starfish',
  'Surrakar',
  'Survivor',
  'Swamp',
  'Szat',
  'Tamiyo',
  'Tasha',
  'Teddy',
  'Teferi',
  'Tentacle',
  'Teyo',
  'Tezzeret',
  'Thalakos',
  'The',
  'Thopter',
  'Thrull',
  'Tibalt',
  'Tiefling',
  'Tower',
  'Townsfolk',
  'Trap',
  'Treasure',
  'Treefolk',
  'Trilobite',
  'Triskelavite',
  'Troll',
  'Turtle',
  'Tyranid',
  'Tyvar',
  'Ugin',
  'Ulgrotha',
  'Undercity',
  'Unicorn',
  'Urza',
  "Urza's",
  'Valla',
  'Vampire',
  'Vampyre',
  'Vedalken',
  'Vehicle',
  'Venser',
  'Viashino',
  'Villain',
  'Vivien',
  'Volver',
  'Vraska',
  'Vryn',
  'Waiter',
  'Wall',
  'Walrus',
  'Warlock',
  'Warrior',
  'Weird',
  'Werewolf',
  'Whale',
  'Wildfire',
  'Will',
  'Windgrace',
  'Wizard',
  'Wolf',
  'Wolverine',
  'Wombat',
  'Worm',
  'Wraith',
  'Wrenn',
  'Wrestler',
  'Wurm',
  'Xenagos',
  'Xerex',
  'Yanggu',
  'Yanling',
  'Yeti',
  'Zariel',
  'Zendikar',
  'Zombie',
  'Zubera',
];
