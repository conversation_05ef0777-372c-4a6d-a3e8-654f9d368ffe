import { cardSet<PERSON>romApi } from '../api/CardSets';
import { CardSet } from './CardSets';
import { Record } from './Records';

interface ISetCompletion {
  cardSet: CardSet;
  totalCards: number;
  totalValue: number;
  totalOwned: number;
  uniqueOwned: number;
  completion: number;
}

export class SetCompletion extends Record<ISetCompletion>({
  cardSet: new CardSet(),
  totalCards: 0,
  totalValue: 0,
  totalOwned: 0,
  uniqueOwned: 0,
  completion: 0,
}) {
  static fromAPI(set_completion: any): SetCompletion {
    return new SetCompletion({
      cardSet: cardSetFromApi(set_completion.card_set),
      totalCards: set_completion.total_cards,
      totalValue: set_completion.total_value,
      totalOwned: set_completion.total_owned,
      uniqueOwned: set_completion.unique_owned,
      completion: set_completion.completion,
    });
  }
}
