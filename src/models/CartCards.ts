import clamp from 'clamp';
import * as Immutable from 'immutable';
import { CartTableGrouping } from '../lib/carts/CartService';
import { AnyCardGroup } from './CardGroup';
import { CardInstance, CardInstances } from './CardInstances';
import { Card } from './Cards';
import { Deck, DeckCard } from './Decks';
import { Game } from './Game';
import { MTGCardGroup } from './mtg/MTGCardPage';
import { Record } from './Records';

interface ICartData {
  data: Immutable.Map<string, CartCardRecord>;
}

export class CartData extends Record<ICartData>({
  data: Immutable.Map<string, CartCardRecord>(),
}) {
  // Named groupCartData so that it isn't confused with the immutable-js function .group()
  groupCartData(grouping: CartTableGrouping): Immutable.Map<string, Immutable.List<CartCardRecord>> {
    // cartData is initially sorted by Printing
    if (grouping === CartTableGrouping.PRINTING) {
      return this.get('data')
        .map((value: CartCardRecord) => Immutable.List<CartCardRecord>([value]))
        .toMap();
    }
    return this.get('data')
      .groupBy((record: CartCardRecord) => {
        switch (grouping) {
          case CartTableGrouping.NAME:
            return record.get('cartCard').get('name');
          case CartTableGrouping.SET:
            return record.get('cartCard').get('name') + record.get('cartCard').get('setCode');
        }
      })
      .map((iterable: Immutable.Iterable<string, CartCardRecord>) => iterable.toList())
      .toMap();
  }
}

interface ICartCard {
  jsonID: string;
  name: string;
  setName?: string;
  setCode?: string;
  collectorNumber?: string;
}

/**
 * Subset of Card that is used to store information needed for the functionality
 * of CardCastle and TCGPlayer buylinks. Not every field is used for each service
 * (e.g CardCastle does not differentiate card by set or collection number).
 * A subset of Card is used instead of a Card as theoretically a store may add features
 * that are tracked in CardInstances (e.g Foil, Language). Having a seperate object
 * future proofs this eventuality.
 */
export class CartCard extends Record<ICartCard>({
  jsonID: '',
  name: '',
  setName: undefined,
  setCode: undefined,
  collectorNumber: undefined,
}) {
  sanitizeCardName(): string {
    const name = this.get('name');
    return name.includes(' //') ? name.split(' //')[0] : name;
  }
}

interface ICartCardRecord {
  cartCard: CartCard;
  ownedCount: number;
  deckCount: number;
  quantity?: number; // to handle NaN issues in input elements
}

export class CartCardRecord extends Record<ICartCardRecord>({
  cartCard: new CartCard(),
  quantity: 0,
  deckCount: 0,
  ownedCount: 0,
}) {
  static mapFromCardInstances(game: Game, cardGroup: AnyCardGroup): CartData {
    if (game != Game.MTG) return new CartData({});

    const mtgGroup = cardGroup as MTGCardGroup;

    return new CartData({
      data: mtgGroup
        .getInstances()
        .groupBy((cardInstance: CardInstance) => cardInstance.get('cardJsonID'))
        .map((iterable: Immutable.Iterable<number, CardInstance>) => iterable.toList())
        .map((cardInstances: CardInstances, key: string) => {
          const firstCardInstance = cardInstances.first();
          const size = cardInstances.size;
          return new CartCardRecord({
            cartCard: new CartCard({
              jsonID: firstCardInstance.get('cardJsonID'),
              name: firstCardInstance.get('cardName'),
              setName: firstCardInstance.get('cardSetName'),
              setCode: firstCardInstance.get('cardSetCode'),
              collectorNumber: firstCardInstance.get('cardCollectorNumber'),
            }),
            quantity: clamp(size, 0, 100),
            deckCount: size,
          });
        })
        .toMap(),
    });
  }

  static mapFromDeck(deck: Deck): CartData {
    const records = deck
      .get('deckCards')
      .groupBy((deckCard: DeckCard) => {
        return deckCard.get('cardId');
      })
      .map((deckCards: Immutable.Iterable<number, DeckCard>) => {
        return deckCards.toList();
      })
      .map((deckCardList: Immutable.List<DeckCard>) => {
        const firstDeckCard = deckCardList.first();
        const size = deckCardList.size;
        const card = deck.get('cards').find((card: Card) => {
          return card.get('id') === firstDeckCard.get('cardId');
        });
        return new CartCardRecord({
          cartCard: new CartCard({
            jsonID: card.get('jsonID'),
            name: card.get('name'),
            setName: card.get('setName'),
            setCode: card.get('setCode'),
            collectorNumber: card.get('collectorNumber'),
          }),
          quantity: clamp(size, 0, 100),
          deckCount: size,
        });
      })
      .toMap()
      .mapKeys((key: number, record: CartCardRecord) => {
        return record.get('cartCard').get('jsonID');
      })
      .toMap();
    return new CartData({ data: records });
  }

  static extendMapFromQuantity(cartData: CartData, cardQuantityMap: Immutable.Map<string, CardQuantity>): CartData {
    const out = cartData
      .get('data')
      .map((record: CartCardRecord, key: string) => {
        const cardQuantity = cardQuantityMap.get(record.get('cartCard').get('jsonID'));
        return record.set('ownedCount', cardQuantity?.get('cardQuantity') || 0);
      })
      .toMap();
    return new CartData({ data: out });
  }

  static initializeDeckTable(initalCartData: CartData, cartData?: CartData): CartData {
    if (cartData === undefined || cartData.size === 0) {
      return initalCartData;
    }
    return new CartData({
      data: cartData
        .get('data')
        .map((record: CartCardRecord) => {
          const initialdeckCount = initalCartData
            .get('data')
            .get(record.get('cartCard').get('jsonID'))
            .get('deckCount');
          const ownedCount = Number.isNaN(record.get('ownedCount')) ? 0 : record.get('ownedCount');
          const newValue = initialdeckCount === undefined ? record.get('ownedCount') : initialdeckCount - ownedCount;
          return record.set('quantity', clamp(newValue, 0, 100));
        })
        .toMap(),
    });
  }

  static purchaseAllCards(cartData?: CartData): CartData | undefined {
    if (cartData === undefined) {
      return undefined;
    }
    return new CartData({
      data: Immutable.Map<string, CartCardRecord>(
        cartData.get('data').map((record: CartCardRecord) => {
          return record.set('quantity', clamp(record.get('deckCount'), 0, 100));
        }),
      ),
    });
  }

  static sanitizeCardName(name: string) {
    return name.includes(' //') ? name.split(' //')[0] : name;
  }
}

interface ICardQuantity {
  cardName: string;
  jsonID: string;
  cardQuantity: number;
}

export class CardQuantity extends Record<ICardQuantity>({
  cardName: '',
  jsonID: '',
  cardQuantity: 0,
}) {}

interface ICartCardQuantity {
  cartCard: CartCard;
  quantity: number;
}

export class CartCardQuantity extends Record<ICartCardQuantity>({
  cartCard: new CartCard(),
  quantity: 0,
}) {}
