export enum FilterComponentType {
  QUERY = 'card_name',
  COLOR = 'color',
  CARD_TYPES = 'type',
  RARITIES = 'rarity',
  TAGS = 'tag',
  CARD_SET = 'set',
  CARD_SET_TYPE = 'set_type',
  FOIL = 'foil',
  PRICE = 'price_range',
  CARD_LIST = 'card_list',
  CONDITION = 'condition',
  SUPERTYPE = 'supertype',
  SUBTYPE = 'subtype',
  LEGALITY = 'legality',
  RESERVED = 'reserved',
  STARTS_WITH = 'starts_with',
  SET_NAME_STARTS_WITH = 'set_name_starts_with',
}
