import * as Immutable from 'immutable';
import { Language } from './Language';
import { MTGPrinting } from './mtg/MTGPrinting';
import { PokePrinting } from './pokemon/PokePrinting';
import { YugiPrinting } from './yugioh/YugiPrinting';

export interface PrintingMethods {
  displayLabel(): string;
  identifier(): string;
  validateLanguage(language: Language): Immutable.Map<string, any>;
}

export type AnyPrinting = MTGPrinting | PokePrinting | YugiPrinting;
