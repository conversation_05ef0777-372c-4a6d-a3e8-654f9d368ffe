import { deck<PERSON>rom<PERSON><PERSON> } from '../api/Decks';
import { DateFromAPI } from '../helpers/time_helper';
import { Deck } from './Decks';
import { Game } from './Game';
import { Record } from './Records';
import { StackedCard } from './StackedCard';
import { StackLocation } from './StackLocation';

interface ILinkedDeckCard {
  id: number;
  cardID: number;
  deckID: number;
  game: Game;
  createdAt: Date;
  updatedAt: Date;
  cardUserID: number;
  deckBoardID: number;
}

export class LinkedDeckCard extends Record<ILinkedDeckCard>({
  id: 0,
  cardID: 0,
  deckID: 0,
  game: Game.MTG,
  createdAt: new Date(),
  updatedAt: new Date(),
  cardUserID: 0,
  deckBoardID: 0,
}) {
  static fromAPI(card_deck: any): LinkedDeckCard | undefined {
    if (card_deck === undefined || card_deck === null) {
      return undefined;
    }
    return new LinkedDeckCard({
      id: card_deck.id,
      cardID: card_deck.card_id,
      deckID: card_deck.deck_id,
      game: card_deck.card_type,
      createdAt: DateFromAPI.nonNullable(card_deck.created_at),
      updatedAt: DateFromAPI.nonNullable(card_deck.updated_at),
      cardUserID: card_deck.card_user_id,
      deckBoardID: card_deck.deck_board_id,
    });
  }
}

interface LinkedResourcesParams {
  deck?: Deck;
  location?: StackLocation;
  linkedDeckCard?: LinkedDeckCard;
  stackedCard?: StackedCard;
}

export class LinkedResources extends Record<LinkedResourcesParams>({
  deck: new Deck(),
  location: new StackLocation(),
  linkedDeckCard: new LinkedDeckCard(),
  stackedCard: new StackedCard(),
}) {
  static entryFromAPI(entry: any[] /* string, any */): any[] /* string, LinkedResources */ {
    return [entry[0], LinkedResources.fromAPI(entry[1])];
  }

  static fromAPI(data: any): LinkedResources {
    return new LinkedResources({
      deck: data.deck === undefined || data.deck === null ? undefined : deckFromAPI(data.deck),
      location: StackLocation.fromAPI(data.location),
      linkedDeckCard: LinkedDeckCard.fromAPI(data.card_deck),
      stackedCard: StackedCard.fromAPI(data.stacked_card),
    });
  }

  static fromCardUser(linkable_card: any): LinkedResources {
    const newDeck = new Deck({
      uuid: linkable_card.deck_uuid,
      id: linkable_card.deck_id,
      name: linkable_card.deck_name,
    });
    const newLinkedDeckCard = new LinkedDeckCard({
      id: linkable_card.id,
      cardID: linkable_card.card_deck_id,
      deckID: linkable_card.deck_id,
    });
    return new LinkedResources({
      deck: newDeck,
      linkedDeckCard: newLinkedDeckCard,
    });
  }
}
