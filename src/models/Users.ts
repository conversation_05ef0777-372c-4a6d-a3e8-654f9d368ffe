import { Condition } from './Condition';
import { Grouping } from './Grouping';
import { PricingSource } from './PricingSource';
import { Record } from './Records';
import { Sorting } from './sorting/Sorting';
import { Viewing } from './Viewing';

interface IUserAvatar {
  readonly large: string;
  readonly medium: string;
  readonly thumb: string;
}

export class UserAvatar extends Record<IUserAvatar>({
  large: '',
  medium: '',
  thumb: '',
}) {}

interface ILocalizationPreferences {
  readonly currency: string;
  readonly timezone: string;
}

export class LocalizationPreferences extends Record<ILocalizationPreferences>({
  currency: 'USD',
  timezone: 'Australia/Canberra',
}) {}

interface IPrivacyPreferences {
  readonly public: boolean;
}

export class PrivacyPreferences extends Record<IPrivacyPreferences>({
  public: false,
}) {}

interface ICollectionPreferences {
  readonly viewing: Viewing;
  readonly grouping: Grouping;
  readonly sorting: Sorting;
  readonly condition: Condition;
}

export class CollectionPreferences extends Record<ICollectionPreferences>({
  viewing: Viewing.GRID,
  grouping: Grouping.PRINTING,
  sorting: Sorting.DATE_ADDED_DESC,
  condition: Condition.NEAR_MINT,
}) {}

interface IPricingPreferences {
  readonly source: PricingSource;
}

export class PricingPreferences extends Record<IPricingPreferences>({
  source: PricingSource.TCG_PLAYER,
}) {}

interface IUserPreferences {
  readonly localization: LocalizationPreferences;
  readonly privacy: PrivacyPreferences;
  readonly collection: CollectionPreferences;
  readonly pricing: PricingPreferences;
}

export class UserPreferences extends Record<IUserPreferences>({
  localization: new LocalizationPreferences(),
  privacy: new PrivacyPreferences(),
  collection: new CollectionPreferences(),
  pricing: new PricingPreferences(),
}) {}

interface IUser {
  readonly id: number;
  readonly username: string;
  readonly emailAddress: string;
  readonly referralLink: string;
  readonly subscribed: boolean;
  readonly confirmedAt: Date;
  readonly createdAt: Date;
  readonly avatar: UserAvatar;
  readonly preferences: UserPreferences;
  readonly userHash: string;
  readonly credit: number;
}

export class User extends Record<IUser>({
  id: 0,
  username: '',
  emailAddress: '',
  referralLink: '',
  subscribed: false,
  confirmedAt: new Date(),
  createdAt: new Date(),
  avatar: new UserAvatar(),
  preferences: new UserPreferences(),
  userHash: '',
  credit: 0,
}) {
  /**
   * Defining this as an instance function leads to a TypeError for unknown reasons.
   */
  static loggedIn(user: User): boolean {
    return user.get('id') !== 0;
  }
}
