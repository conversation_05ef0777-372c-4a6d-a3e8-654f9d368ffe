import moment from 'moment';
import { Record } from './Records';

interface IStackedCard {
  id: number;
  uuid: string;
  position: number;
  cardUserId: number;
  locationId: number;
  locationUUID: string;
  createdAt: Date;
  updatedAt: Date;
}

export class StackedCard extends Record<IStackedCard>({
  id: 0,
  uuid: '',
  position: 0,
  cardUserId: 0,
  locationId: 0,
  locationUUID: '',
  createdAt: new Date(),
  updatedAt: new Date(),
}) {
  static fromAPI(stacked_card: any) {
    if (stacked_card === null || stacked_card === undefined) {
      return undefined;
    }
    return new StackedCard({
      id: stacked_card.id,
      uuid: stacked_card.uuid,
      position: stacked_card.position,
      cardUserId: stacked_card.card_user_id,
      locationId: stacked_card.location_id,
      locationUUID: stacked_card.location_uuid,
      createdAt: moment.utc(stacked_card.created_at).toDate(),
      updatedAt: moment.utc(stacked_card.updated_at).toDate(),
    });
  }
}
