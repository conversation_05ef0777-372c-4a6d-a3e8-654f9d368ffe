import * as Immutable from 'immutable';
import { Record } from './Records';

export interface IManaStatistics {
  totalMana: number;
  white: number;
  blue: number;
  black: number;
  red: number;
  green: number;
  colorless: number;
}

export class ManaStatistics extends Record<IManaStatistics>({
  totalMana: 0,
  white: 0,
  blue: 0,
  black: 0,
  red: 0,
  green: 0,
  colorless: 0,
}) {
  static fromAPI(mana: any): ManaStatistics {
    const manaStatistics = new ManaStatistics({ totalMana: mana.mana_count as number });
    return Immutable.Map<string, string>(mana.mana_colors).reduce(
      (reducer: ManaStatistics, value: string, key: string) => {
        return reducer.set(key as keyof IManaStatistics, Number.parseInt(value));
      },
      manaStatistics,
    );
  }

  public userHasBasicLands() {
    return this.some((value: number) => {
      return value > 0;
    });
  }
  public toPlotlyData(): Immutable.Map<string, number> {
    return Immutable.Map<string, number>({
      white: this.get('white'),
      blue: this.get('blue'),
      black: this.get('black'),
      red: this.get('red'),
      green: this.get('green'),
      colorless: this.get('colorless'),
    });
  }
}
