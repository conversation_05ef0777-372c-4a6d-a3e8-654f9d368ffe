// npm
import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardsAPI from '../../api/Cards';
import * as HasMe from '../../containers/HasMe';
// Containers
import * as HasTags from '../../containers/HasTags';
// Dispatcher
import Dispatcher from '../../dispatcher/Dispatcher';
import { CardInstance } from '../../models/CardInstances';
// Models
import { Card } from '../../models/Cards';
import { Condition } from '../../models/Condition';
import { Foil } from '../../models/Foil';
import { Language } from '../../models/Language';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { Dialog, DialogSize } from '../components/Dialog';
import { DialogStatus } from './BuilderManual';
// Components
import CardEntryPanel from './CardEntryPanel';
import { CardEntryTable } from './CardEntryTable';

export interface CardEntryDialogProps {
  dispatcher: Dispatcher;
  currentStatus: DialogStatus;
  targetStatus: DialogStatus.CARD_ENTRY | DialogStatus.QUICK_ADD;
  title: string;
  subtitle: string;
  onDismiss: () => void;
  cardGroups: Immutable.OrderedMap<string, MTGCardGroup>;
  updateCardGroups: (cardGroups: Immutable.OrderedMap<string, MTGCardGroup>) => void;
}

interface IState {
  languageOptions: Immutable.Map<string, Immutable.List<Language>>;
}

export const CardEntryDialog = HasTags.Attach<CardEntryDialogProps>(
  HasMe.Attach<CardEntryDialogProps & HasTags.IProps>(
    class extends React.Component<CardEntryDialogProps & HasTags.IProps & HasMe.IProps, IState> {
      constructor(props: CardEntryDialogProps & HasTags.IProps & HasMe.IProps) {
        super(props);
        this.state = {
          languageOptions: Immutable.Map<string, Immutable.List<Language>>(),
        };
      }

      public render() {
        return (
          <Dialog
            className="dialog-left-align card-entry-dialog"
            isOpen={this.props.targetStatus === this.props.currentStatus}
            onDismiss={this.props.onDismiss}
            size={DialogSize.LARGE_DYNAMIC}
          >
            <div className="dialog-card-entry-grid col-xs-12">
              <div>
                <div className="dialog-heading" style={{ marginBottom: 0 }}>
                  {this.props.title}
                </div>
                <div className="dialog-subheading">{this.props.subtitle}</div>
              </div>
              <div className="card-entry-dialog-divider col-xs-12">
                <CardEntryPanel
                  addCards={this.addCards.bind(this)}
                  dispatcher={this.props.dispatcher}
                  pricingSource={this.props.me.get('preferences').get('pricing').get('source')}
                  defaultCondition={this.props.me.get('preferences').get('collection').get('condition')}
                />
              </div>
              {this.props.cardGroups.size === 0 ? (
                <div className="row justify-center">
                  <span className="card-entry-placeholder">No cards</span>
                </div>
              ) : (
                <div style={{ overflowX: 'scroll' }}>
                  <CardEntryTable
                    dispatcher={this.props.dispatcher}
                    me={this.props.me}
                    cardGroups={this.props.cardGroups}
                    userTags={this.props.userTags}
                    languageOptions={this.state.languageOptions}
                    remove={this.removeCard.bind(this)}
                    removeAll={this.removeAll.bind(this)}
                  />
                </div>
              )}
              {this.props.children}
            </div>
          </Dialog>
        );
      }

      private async addCards(card: Card, quantity: number, foil: Foil, condition: Condition, language: Language) {
        // Generate cardGroupKey. If cardGroupKey is already a key in this.props.cardGroups, add the new quantity to the cardGroup.
        const cardGroupKey = card.get('jsonID') + foil.toString() + condition + language;
        const cardGroupTarget = this.props.cardGroups.get(cardGroupKey, undefined);
        if (cardGroupTarget !== undefined) {
          const newQuantity = cardGroupTarget.get('cardInstanceCount') + quantity;
          const newCardGroup = cardGroupTarget
            .set('cardInstanceCount', newQuantity)
            .set('cardInstancePrice', card.get('userPrice') * newQuantity);
          this.props.updateCardGroups(this.props.cardGroups.set(cardGroupKey, newCardGroup));
          return;
        }

        // Else, generate a new card group.
        const newCardInstance = new CardInstance({
          id: card.get('id'),
          foil: foil,
          condition: condition,
          language: language,
          cardJsonID: card.get('jsonID'),
          cardName: card.get('name'),
          cardSetName: card.get('setName'),
          cardSetCode: card.get('setCode'),
          cardCollectorNumber: card.get('collectorNumber'),
          cardPrice: foil === Foil.On ? card.get('userPriceFoil') : card.get('userPrice'),
          cardManaCost: card.get('manaCost'),
          cardTypes: card.get('types'),
          cardSubTypes: card.get('subTypes'),
          cardRarity: card.get('rarity'),
        });

        const newCardGroups = this.props.cardGroups.set(
          cardGroupKey,
          new MTGCardGroup({
            cardInstanceCount: quantity,
            cardInstancePrice: newCardInstance.get('cardPrice') * quantity,
            cardInstances: Immutable.List<CardInstance>([newCardInstance]),
          }),
        );

        // This is fine as every card in each card group will be the same as we group by printing
        const cardJsonIDs = newCardGroups
          .map((cardGroup: MTGCardGroup) => cardGroup.get('cardInstances').first().get('cardJsonID'))
          .toList();
        this.setState({
          languageOptions: await CardsAPI.getLanguageOptions(cardJsonIDs),
        });
        this.props.updateCardGroups(newCardGroups);
      }

      private removeCard(cardGroupID: string) {
        this.props.updateCardGroups(this.props.cardGroups.delete(cardGroupID));
      }

      private removeAll(evt: React.SyntheticEvent<HTMLElement>) {
        evt.preventDefault();
        this.props.updateCardGroups(Immutable.OrderedMap<string, MTGCardGroup>());
      }
    },
  ),
);
