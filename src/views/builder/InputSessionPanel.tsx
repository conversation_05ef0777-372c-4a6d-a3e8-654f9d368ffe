// npm
// Iconify
import plus from '@iconify/icons-ic/baseline-plus';
import * as React from 'react';
// Actions
import * as InputSessionActions from '../../actions/InputSessionActions';
import * as MessageActions from '../../actions/MessageActions';
// Containers
import * as HasInputSessionCardPanel from '../../containers/HasInputSessionCardPanel';
import * as HasInputSessions from '../../containers/HasInputSessions';
import * as HasMe from '../../containers/HasMe';
import * as HasTags from '../../containers/HasTags';
// Dispatcher
import Dispatcher from '../../dispatcher/Dispatcher';
// Helpers
import history from '../../helpers/history';
import { AnyCardGroup } from '../../models/CardGroup';
import { Game } from '../../models/Game';
import { Grouping } from '../../models/Grouping';
import { AnyInputSession } from '../../models/InputSession';
import { MTGCardPage } from '../../models/mtg/MTGCardPage';
import { Sorting } from '../../models/sorting/Sorting';
import { FreeLimits } from '../../models/Subscriptions';
import { SessionState } from '../../stores/InputSessionStore';
import { Button, ButtonClass } from '../components/filters/UpdateFiltersButton';
import { CardControls } from './CardControls';
import { InputSessionControls } from './InputSessionControls';
import { InputSessionMessage } from './InputSessionMessage';
import { SessionCards } from './SessionCards';

interface IProps {
  dispatcher: Dispatcher;
  onClickAddToCollection: (evt: React.SyntheticEvent<HTMLElement>) => void;
  onClickAddCards: (evt: React.SyntheticEvent<HTMLElement>) => void;
  onClickDelete: (evt: React.SyntheticEvent<HTMLElement>) => void;
  createNewSession: (evt: React.SyntheticEvent<HTMLElement>) => void;
  openActions: () => void;
}

interface IState {}

export const InputSessionPanel = HasMe.Attach<IProps>(
  HasInputSessionCardPanel.Attach<IProps & HasMe.IProps>(
    HasTags.Attach<IProps & IProps & HasInputSessionCardPanel.IProps & HasMe.IProps>(
      HasInputSessions.Attach<IProps & HasInputSessionCardPanel.IProps & HasTags.IProps & HasMe.IProps>(
        class extends React.Component<
          IProps & HasInputSessionCardPanel.IProps & HasTags.IProps & HasMe.IProps & HasInputSessions.IProps,
          IState
        > {
          public async componentDidMount() {
            const inputSession = this.getInputSession();
            if (inputSession && this.props.sessionState != SessionState.IDLE) {
              InputSessionActions.loadCards(inputSession, this.props.inputSessionIndex, this.props.dispatcher);
            }
          }

          getInputSession(): AnyInputSession | undefined {
            return this.props.inputSessions.get(this.props.inputSessionIndex) as AnyInputSession;
          }

          /**
           * @override
           */

          render() {
            const inputSession = this.getInputSession();
            if (!inputSession) return null;

            return (
              <div
                className="input-session-border"
                style={{
                  padding: 0,
                }}
              >
                <section className="section">
                  <InputSessionControls
                    dispatcher={this.props.dispatcher}
                    me={this.props.me}
                    inputSession={inputSession}
                    status={this.props.sessionState}
                    onClickAddToCollection={this.props.onClickAddToCollection}
                    onClickDelete={this.props.onClickDelete}
                    openActions={this.props.openActions}
                  />
                  <div className="row">
                    <div
                      className="flex col-lg-12 col-xl-12"
                      style={{ marginBottom: '1rem', gap: '2rem', flexWrap: 'wrap' }}
                    >
                      <CardControls
                        loading={this.props.sessionState == SessionState.LOADING}
                        inputSession={inputSession}
                        onClickAddCards={this.props.onClickAddCards}
                        onSearchCards={this.onSearchCards.bind(this)}
                      />
                    </div>
                  </div>
                  {this.renderBody()}
                </section>
              </div>
            );
          }

          private spinnerMessage() {
            switch (this.props.sessionState) {
              case SessionState.DELETING:
                return 'Deleting';
              case SessionState.COMMITTING:
                return 'Committing';
              default:
                return 'Loading';
            }
          }

          private renderBody() {
            switch (this.props.sessionState) {
              case SessionState.IDLE:
                return this.renderTable();
              case SessionState.LOADING:
                return (
                  <InputSessionMessage>
                    <div className="flex">
                      <div className="spinner-xs"></div>
                      <p style={{ marginLeft: '0.5rem' }}>{this.spinnerMessage()}</p>
                    </div>
                  </InputSessionMessage>
                );
            }
          }

          private sessionCount(): number {
            return this.getInputSession()?.get('cardPage').get('totalCount') || 0;
          }

          private renderTable() {
            const queryActive =
              (this.getInputSession()?.get('cardPage')?.get('filterOptions')?.getQuery()?.length || 0) > 0;
            return (
              <>
                {this.sessionCount() > 0 ? (
                  <div className="justify-center">
                    <SessionCards
                      dispatcher={this.props.dispatcher}
                      me={this.props.me}
                      game={this.getInputSession()?.get('game') || Game.MTG}
                      cardPage={this.getInputSession()?.get('cardPage') || new MTGCardPage()}
                      cardPanel={this.props.cardPanel}
                      userTags={this.props.userTags}
                      onClickPage={this.onClickPage.bind(this)}
                      updateSorting={this.updateSorting.bind(this)}
                      remove={this.remove.bind(this)}
                      languageOptions={this.props.languageOptions}
                      onClickListItem={this.onClickListItem.bind(this)}
                    />
                  </div>
                ) : (
                  <InputSessionMessage>
                    {queryActive ? (
                      <div className="lato-N4">
                        <p>No results</p>
                      </div>
                    ) : (
                      <>
                        <div className="lato-N4">
                          <p>This session has no cards </p>
                          {!this.props.me.get('subscribed') ? (
                            <div style={{ textAlign: 'center' }}>
                              You're able to add an unlimited amount of cards, but you'll be limited to browsing the
                              first {FreeLimits.CARDS} while on the free Squire account. This gives you the chance to
                              try out the platform before
                              <div className="lato-N6 inline-link" onClick={this.onClickUpgrade.bind(this)}>
                                upgrading to Knight
                              </div>
                              and accessing all our features.
                            </div>
                          ) : null}
                        </div>
                        {this.getInputSession()?.get('game') === Game.MTG && (
                          <div className="row justify-center" style={{ marginTop: '1rem' }}>
                            <Button
                              class={ButtonClass.PRIMARY}
                              icon={plus}
                              title="Insert Cards"
                              onClick={this.props.onClickAddCards}
                            />
                          </div>
                        )}
                      </>
                    )}
                  </InputSessionMessage>
                )}
              </>
            );
          }

          async updateSorting(sorting: Sorting) {
            const inputSession = this.getInputSession();
            if (!inputSession) return;

            this.onSearchCards(
              inputSession.get('cardPage').get('cardGrouping'),
              sorting,
              inputSession.get('cardPage').get('filterOptions').getQuery(),
            );
          }

          private onSearchCards(grouping: Grouping, sorting: Sorting, searchQuery?: string) {
            const inputSession = this.getInputSession();
            if (!inputSession) return;

            let cardPage = inputSession.get('cardPage');
            const filterOptions = cardPage.get('filterOptions').setQuery(searchQuery);
            cardPage = cardPage
              .set('cardGrouping', grouping)
              .set('cardSorting', sorting)
              .set('filterOptions', filterOptions);

            InputSessionActions.setSessionLoading(this.props.inputSessionIndex, cardPage, this.props.dispatcher);
            InputSessionActions.loadCards(
              inputSession.set('cardPage', cardPage.set('filterOptions', filterOptions)),
              this.props.inputSessionIndex,
              this.props.dispatcher,
            );
          }

          private onClickUpgrade(evt: React.SyntheticEvent<HTMLElement>) {
            evt.preventDefault();
            history.push('/settings/subscription');
          }

          private onClickPage(page: number) {
            let inputSession = this.getInputSession();
            if (!inputSession) return;

            inputSession = inputSession.set(
              'cardPage',
              inputSession
                .get('cardPage')
                .set('filterOptions', inputSession.get('cardPage').get('filterOptions').setPage(page)),
            );

            InputSessionActions.setSessionLoading(
              this.props.inputSessionIndex,
              inputSession.get('cardPage'),
              this.props.dispatcher,
            );
            window.scrollTo(0, 0);
            InputSessionActions.loadCards(inputSession, this.props.inputSessionIndex, this.props.dispatcher);
          }

          private async onClickListItem(y: number, game: Game, cardGroup: AnyCardGroup) {
            const panelY = this.props.cardPanel.get('y');
            if (y === panelY) {
              InputSessionActions.closeCardPanel(this.props.dispatcher);
            } else {
              InputSessionActions.openCardPanel(0, y, game, cardGroup, false, this.props.dispatcher);
            }
          }

          private remove(cardGroup: AnyCardGroup) {
            const inputSession = this.getInputSession();
            if (!inputSession) return;

            InputSessionActions.closeCardPanel(this.props.dispatcher);
            InputSessionActions.removeCards(
              inputSession.get('uuid'),
              this.props.inputSessionIndex,
              cardGroup,
              this.props.dispatcher,
            ).catch((err) => {
              MessageActions.error(
                'Failed to remove card. Please contact us if this error persists.',
                this.props.dispatcher,
                err,
              );
            });
          }
        },
      ),
    ),
  ),
);
