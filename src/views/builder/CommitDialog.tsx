// npm
// Iconify
import plus from '@iconify/icons-ic/baseline-plus';
import * as Immutable from 'immutable';
import * as React from 'react';
// Containers, Dispatcher and Actions
import * as CardStagingActions from '../../actions/CardStagingActions';
import * as StackLocationActions from '../../actions/StackLocationActions';
import * as HasMe from '../../containers/HasMe';
import * as HasStagingCards from '../../containers/HasStagingCards';
import * as HasSubscription from '../../containers/HasSubscription';
import * as HasTags from '../../containers/HasTags';
import Dispatcher from '../../dispatcher/Dispatcher';
// Helpers
import { TextFormat } from '../../helpers/fmt';
import { StackLocation } from '../../models/StackLocation';
import { Tag } from '../../models/Tags';
// Components
import { Dialog, DialogSize } from '../components/Dialog';
import { Button, ButtonClass } from '../components/filters/UpdateFiltersButton';
import { TagsBuilder } from '../components/TagsBuilder';
import { DialogStatus as BuilderDialogStatus } from './BuilderManual';

export enum CommitDialogMode {
  TAG = 'tag',
  QUICK_ADD_TAG = 'quick-add-tag',
  COMMIT_ALL_TAG = 'commit-all-tag',
}

interface IProps {
  readonly dispatcher: Dispatcher;
  readonly locationPath: Immutable.List<StackLocation>;
  readonly totalCards: number;
  readonly mode: CommitDialogMode;
  readonly status: BuilderDialogStatus;
  readonly onSave: () => void;
  readonly onClose: () => void;
}

interface IState {
  locationError: boolean;
}

export const CommitDialog = HasTags.Attach<IProps>(
  HasMe.Attach<IProps & HasTags.IProps>(
    HasSubscription.Attach<IProps & HasTags.IProps & HasMe.IProps>(
      HasStagingCards.Attach<IProps & HasTags.IProps & HasMe.IProps & HasSubscription.IProps>(
        class extends React.Component<
          IProps & HasTags.IProps & HasMe.IProps & HasSubscription.IProps & HasStagingCards.IProps,
          IState
        > {
          constructor(props: IProps & HasTags.IProps & HasMe.IProps & HasSubscription.IProps & HasStagingCards.IProps) {
            super(props);
            this.state = {
              locationError: false,
            };
          }

          public render() {
            if (this.props.mode.toString() !== this.props.status.toString()) {
              return null;
            }

            return (
              <Dialog
                isOpen={this.props.mode.toString() === this.props.status.toString()}
                onDismiss={this.props.onClose}
                size={DialogSize.MEDIUM}
              >
                <div className={'commit-dialog-grid'}>
                  <div className="dialog-heading" style={{ marginBottom: 0 }}>
                    Commit to Collection
                  </div>
                  <div className="heading-divider-wrapper">
                    <div className="heading-sm" style={{ marginLeft: '1rem', textAlign: 'left' }}>
                      {`${this.generateSubtitle.bind(this)()}Tagging ${TextFormat.pluralizedCardsCapitalized(
                        this.props.totalCards,
                      )}`}
                    </div>
                  </div>
                  <>
                    <div style={{ minHeight: '10rem' }}>
                      <div className="dialog-info-heading" style={{ marginBottom: 0, padding: '0 1rem' }}>
                        Tags
                      </div>
                      <TagsBuilder
                        dispatcher={this.props.dispatcher}
                        me={this.props.me}
                        userTags={this.props.userTags}
                        tags={this.props.stagingTags}
                        onAdd={this.onAddTag.bind(this)}
                        onRemove={this.onRemoveTag.bind(this)}
                        publicViewing={false}
                        background={false}
                        dialog={true}
                      />
                      {/* {this.props.subscription.isMerchant() ? (
                          <LocationPathInput
                            dispatcher={this.props.dispatcher}
                            locationPath={this.props.locationPath}
                            deleteLastLocation={this.deleteLastLocation.bind(this)}
                            locationError={this.state.locationError}
                            clearError={this.clearError.bind(this)}
                          />
                        ) : null} */}
                    </div>
                    <div className="col-xs-12 flex justify-center" style={{ marginTop: '1rem', gap: '1rem' }}>
                      <Button
                        class={ButtonClass.PRIMARY}
                        icon={plus}
                        title="Commit to Collection"
                        onClick={this.onSave.bind(this)}
                      />
                      <Button class={ButtonClass.ALERT} title="Cancel" onClick={() => this.props.onClose()} />
                    </div>
                  </>
                </div>
              </Dialog>
            );
          }

          private onAddTag(tag: Tag) {
            CardStagingActions.addTag(tag, this.props.dispatcher);
          }

          private onRemoveTag(tag: Tag) {
            CardStagingActions.removeTag(tag, this.props.dispatcher);
          }

          private deleteLastLocation() {
            StackLocationActions.updateLocationPath(
              this.props.locationPath.delete(this.props.locationPath.size - 1),
              this.props.dispatcher,
            );
          }

          private onSave(evt: React.SyntheticEvent<HTMLElement>) {
            evt.preventDefault();
            if (this.props.locationPath.size > 0 && this.props.locationPath.last().get('childLocations').size > 0) {
              this.setState({ locationError: true });
              return;
            }
            this.props.onSave();
          }

          private generateSubtitle() {
            switch (this.props.mode) {
              case CommitDialogMode.TAG:
                return 'Commit - ';
              case CommitDialogMode.QUICK_ADD_TAG:
                return 'Quick Add - ';
              case CommitDialogMode.COMMIT_ALL_TAG:
                return 'Commit All - ';
            }
          }

          private clearError() {
            this.setState({ locationError: false });
          }
        },
      ),
    ),
  ),
);
