import * as Immutable from 'immutable';
import * as React from 'react';
import * as StackLocationActions from '../../actions/StackLocationActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import { StackLocation } from '../../models/StackLocation';
import { LocationInput } from './LocationInput';

interface IProps {
  dispatcher: Dispatcher;
  locationPath: Immutable.List<StackLocation>;
  deleteLastLocation: () => void;
  clearError: () => void;
  locationError: boolean;
}

interface IState {}

export class LocationPathInput extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  private displayInputs() {
    let inputs = Immutable.List<JSX.Element>();
    inputs = inputs.push(
      <LocationInput
        key={0}
        index={0}
        dispatcher={this.props.dispatcher}
        parentLocation={undefined}
        value={this.props.locationPath.get(0)}
        update={(location: StackLocation) => this.onUpdate.bind(this)(0, location)}
        last={this.props.locationPath.size === 0}
        remove={this.props.locationPath.size === 1 ? this.props.deleteLastLocation.bind(this) : undefined}
        clearable={this.props.locationPath.size === 1}
        onLoad={this.initLocations.bind(this)}
        locationError={this.props.locationError}
      />,
    );
    for (let i = 1; i < this.props.locationPath.size + 1; i++) {
      const parent = this.props.locationPath.get(i - 1);
      const last = this.props.locationPath.size === i;
      const clearable = this.props.locationPath.size - 1 === i;
      inputs = inputs.push(
        <LocationInput
          key={i}
          index={i}
          dispatcher={this.props.dispatcher}
          parentLocation={parent}
          value={this.props.locationPath.get(i)}
          update={(location: StackLocation) => this.onUpdate.bind(this)(i, location)}
          remove={clearable ? this.props.deleteLastLocation.bind(this) : undefined}
          last={last}
          clearable={clearable}
          onLoad={this.onLoad.bind(this)}
          locationError={this.props.locationError}
        />,
      );
    }
    return inputs;
  }

  render() {
    return (
      <>
        <div className="col-xs-12">
          <div
            className="dialog-info-heading"
            style={{ marginBottom: 0, color: this.props.locationError ? '#c11f1f' : '' }}
          >
            {`Location ${this.props.locationError ? ' - Cards can only be placed at the end of a location path' : ''}`}
          </div>
        </div>
        <div>
          <div className="col-xs-12" style={{ marginBottom: '1rem', padding: 0 }}>
            <div className="col-xs-12 flex" style={{ flexFlow: 'row wrap' }}>
              {this.displayInputs()}
            </div>
          </div>
        </div>
      </>
    );
  }

  private async initLocations(index: number, location?: StackLocation) {
    return await StackLocationActions.getParents(this.props.dispatcher);
  }

  private async onUpdate(index: number, location: StackLocation) {
    await StackLocationActions.updateLocationPath(this.props.locationPath.set(index, location), this.props.dispatcher);
    this.props.clearError();
  }

  private async onLoad(index: number, location?: StackLocation): Promise<Immutable.List<StackLocation>> {
    if (location === undefined || location.get('id') === -1) {
      return Immutable.List<StackLocation>();
    }
    return (await StackLocationActions.getChildren(location.get('uuid'), index, this.props.dispatcher)).toList();
  }
}
