import clear from '@iconify/icons-ic/clear';
import * as Immutable from 'immutable';
import * as React from 'react';
import displayCurrency from '../../../helpers/currency_helper';
import { DateFormat } from '../../../helpers/fmt';
import { CardAttributes, CardFormAttributes } from '../../../models/CardAttributes';
import { CellBorders } from '../../../models/CellBorder';
import { Condition } from '../../../models/Condition';
import { Grouping } from '../../../models/Grouping';
import { Language } from '../../../models/Language';
import { User } from '../../../models/Users';
import { YugiCardGroup } from '../../../models/yugioh/YugiCardPage';
import { ConditionSelector } from '../../components/selectors/ConditionSelector';
import * as Table from '../../components/Table';

interface IProps {
  index: number;
  me: User;
  languageOptions: Immutable.Set<Language>;
  final: boolean;
  onUpdateAttributes: (cardGroup: YugiCardGroup, attributes: CardAttributes) => void;
  grouping: Grouping;
  cardGroup: YugiCardGroup;
  onRemove: (cardGroup: YugiCardGroup) => void;
  onClick?: () => void;
}

interface IState {
  attributes: CardFormAttributes;
}

export class YugiStagedCardRow extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      attributes: this.groupAttributes(this.props),
    };
  }

  groupAttributes(props: IProps): CardFormAttributes {
    return new CardFormAttributes({
      condition: this.condition(props),
    });
  }

  condition(props: IProps) {
    return props.cardGroup.groupValue('condition', 'Multiple', Condition.NEAR_MINT);
  }

  // Attributes can be changed from CardPanel - override state
  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<IState>, snapshot?: any): void {
    if (!this.groupAttributes(prevProps).equals(this.groupAttributes(this.props))) {
      this.setState({
        attributes: this.groupAttributes(this.props),
      });
    }
  }

  onClick(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.props.onClick && this.props.onClick();
  }

  onUpdateCondition(condition: Condition) {
    this.setState({ attributes: this.state.attributes.set('condition', condition) });
    this.props.onUpdateAttributes(this.props.cardGroup, new CardAttributes({ condition: condition }));
  }

  render() {
    const price = this.props.cardGroup.priceSummary();
    const priceString = price
      ? displayCurrency(price, true, this.props.me.get('preferences').get('localization').get('currency'))
      : '-';
    const dark = this.props.index % 2 === 0 ? true : false;
    const timezone = this.props.me.get('preferences').get('localization').get('timezone');
    const cardInstance = this.props.cardGroup.get('cardInstances').first();

    return (
      <React.Fragment key={cardInstance.get('uuid')}>
        {this.props.grouping === Grouping.NONE ? null : (
          <Table.AmountCell
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            amount={this.props.cardGroup.get('cardInstanceCount')}
            large={true}
            onClick={this.onClick.bind(this)}
          />
        )}
        <Table.YugiCardInfoCell
          cellStyle={{
            dark: dark,
            cellBorders: CellBorders.default(this.props.final),
            large: true,
            onClick: this.onClick.bind(this),
          }}
          cardInstance={cardInstance}
          grouping={this.props.grouping}
          displayConfidence
        />
        <Table.SelectorCell dark={dark} cellBorders={CellBorders.default(this.props.final)}>
          <ConditionSelector
            condition={this.state.attributes.get('condition')}
            onChange={this.onUpdateCondition.bind(this)}
            hideTitle={true}
          />
        </Table.SelectorCell>
        <Table.TextCell
          dark={dark}
          centerCell={true}
          cellBorders={CellBorders.default(this.props.final)}
          text={priceString}
          large={true}
          onClick={this.onClick.bind(this)}
        />
        <Table.TextCell
          dark={dark}
          centerCell={true}
          cellBorders={CellBorders.default(this.props.final)}
          text={DateFormat.human(this.props.cardGroup.get('cardInstanceLatest'), timezone)}
          large={true}
          onClick={this.onClick.bind(this)}
        />
        <Table.MaterialIcon
          dark={dark}
          centerCell={true}
          materialIconText={clear}
          large={true}
          cellBorders={CellBorders.rightCell(this.props.final)}
          onClick={() => {
            this.props.onRemove(this.props.cardGroup);
          }}
        />
      </React.Fragment>
    );
  }
}
