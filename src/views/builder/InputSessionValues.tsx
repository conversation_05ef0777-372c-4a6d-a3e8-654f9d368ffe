import * as React from 'react';
import displayCurrency from '../../helpers/currency_helper';
import { NumberFormat, TextFormat } from '../../helpers/fmt';
import { User } from '../../models/Users';

interface IProps {
  me: User;
  totalCount?: number;
  totalValue?: number;
}

export const InputSessionValues = (props: IProps) => {
  const displayCount = (props.totalCount && NumberFormat.commaSeparated(props.totalCount)) || '-';
  const displayValue =
    (props.totalValue &&
      displayCurrency(props.totalValue, true, props.me.get('preferences').get('localization').get('currency'))) ||
    '-';
  return (
    <>
      <span className="builder-count">{TextFormat.quantityOf('Card', props.totalCount || 0)}</span>
      <span className="builder-value">{displayValue}</span>
    </>
  );
};
