import * as React from 'react';
import * as ImportExportActions from '../../actions/ImportExportActions';
import * as MessageActions from '../../actions/MessageActions';
import * as CardsAPI from '../../api/Cards';
import * as HasImportExport from '../../containers/HasImportExport';
import Dispatcher from '../../dispatcher/Dispatcher';
import history from '../../helpers/history';
import { ImageHelper } from '../../helpers/img';
import { Game } from '../../models/Game';
import { DataService, Format, Method } from '../../models/ImportExport';
import { FreeLimits } from '../../models/Subscriptions';
import { User } from '../../models/Users';
import ImportExportRadio from '../components/ImportExportRadio';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
}

interface IState {
  method?: Method;
  format?: Format;
  service?: DataService;
}

export const BuilderImportExport = HasImportExport.Attach<IProps>(
  class extends React.Component<IProps & HasImportExport.IProps, IState> {
    constructor(props: IProps & HasImportExport.IProps) {
      super(props);
      this.state = {
        method: undefined,
        format: undefined,
        service: undefined,
      };
    }

    /**
     * @override
     */
    public componentDidMount() {
      ImportExportActions.refresh(this.props.dispatcher);
    }

    public render() {
      this.preloadImages();

      return (
        <div className="container-fluid">
          <section className="section">
            <p className="paragraph-sm is-centered">Import/export your entire collection in a variety of formats.</p>

            {/* method */}
            <div className="builder-import-export-row">
              <input
                id="import"
                type="radio"
                checked={this.state.method === Method.IMPORT}
                onChange={this.onClickMethod.bind(this, Method.IMPORT)}
              />
              <label htmlFor="import" className="builder-import-export-label">
                Import
              </label>
              <input
                id="export"
                type="radio"
                checked={this.state.method === Method.EXPORT}
                onChange={this.onClickMethod.bind(this, Method.EXPORT)}
              />
              <label htmlFor="export" className="builder-import-export-label">
                Export
              </label>
            </div>

            {/* Select Format */}
            {this.state.method === Method.IMPORT ? (
              <div className="builder-import-export-row">
                <input
                  id="csv"
                  type="radio"
                  checked={this.state.format === Format.CSV}
                  onChange={this.onClickFormat.bind(this, Format.CSV)}
                />
                <label htmlFor="csv" className="builder-import-export-label">
                  CSV
                </label>
                <input
                  id="text"
                  type="radio"
                  checked={this.state.format === Format.TEXT}
                  onChange={this.onClickFormat.bind(this, Format.TEXT)}
                />
                <label htmlFor="text" className="builder-import-export-label">
                  Text
                </label>
              </div>
            ) : null}

            {/* Select Type */}
            <div className="builder-import-export-row">
              {this.props.services
                .filter((service: DataService) => service.supports(this.state.method, this.state.format, Game.MTG))
                .map((service: DataService) => (
                  <ImportExportRadio
                    service={service}
                    key={service.get('type')}
                    format={this.state.format!}
                    selected={
                      this.state.service !== undefined && service.get('type') === this.state.service.get('type')
                    }
                    onSelected={() => this.onSelectService(service)}
                  />
                ))}
            </div>

            {this.state.method !== undefined && this.state.format !== undefined && this.state.service !== undefined && (
              <div>
                {this.state.service
                  .instructions(this.state.method, this.state.format)
                  .map((instruction: string, index: number) => (
                    <p key={index} className="builder-import-export-header-format">
                      {instruction}
                    </p>
                  ))}

                {this.state.method === Method.IMPORT && (
                  <div>
                    {this.state.format === Format.TEXT && (
                      <div>
                        {/* Input Text */}
                        <div className="builder-import-export-textarea">
                          <textarea ref="importText" className="input" />
                        </div>
                      </div>
                    )}
                    <div className="flex justify-center">
                      <form id="importForm" style={{ display: 'none' }}>
                        <input id="importInput" name="upload" type="file" onChange={this.onChangeImport.bind(this)} />
                      </form>
                      <button className="button-primary" onClick={this.onClickImport.bind(this)}>
                        Import
                      </button>
                    </div>
                    {/* Show import subscription warning */}
                    {!this.props.me.get('subscribed') && this.renderUpgradeMessage(Method.IMPORT)}
                  </div>
                )}
                {this.state.method === Method.EXPORT && (
                  <div className="flex vertical align-center">
                    <button className="button-primary" onClick={this.onClickExport.bind(this)}>
                      Export
                    </button>
                    {/* Show export subscription warning */}
                    {!this.props.me.get('subscribed') && this.renderUpgradeMessage(Method.EXPORT)}
                  </div>
                )}
              </div>
            )}
          </section>
        </div>
      );
    }

    private onClickMethod(method: Method, evt: React.SyntheticEvent<HTMLInputElement>) {
      if (method === Method.EXPORT) {
        this.setState({
          method: method,
          format: Format.CSV,
          service: undefined,
        });
      } else {
        this.setState({
          method: method,
          format: undefined,
          service: undefined,
        });
      }
    }

    private onClickFormat(format: Format, evt: React.SyntheticEvent<HTMLInputElement>) {
      this.setState({
        format: format,
        service: undefined,
      });
    }

    private onSelectService(service: DataService) {
      this.setState({
        service: service,
      });
    }

    private onChangeImport(evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();

      if (this.state.format === Format.CSV) {
        const importForm: HTMLFormElement = $('#importForm')[0] as any;
        if (!this.state.service) {
          return MessageActions.error('Please select a CSV format', this.props.dispatcher);
        }
        // import action
        CardsAPI.importCards(new FormData(importForm), this.state.service.get('type'), 'csv')
          .then(() => {
            importForm.reset();
          })
          .then(() => {
            MessageActions.success(
              'Your import is being staged. You will receive an email when it is complete.',
              this.props.dispatcher,
            );
          })
          .catch((err) => {
            importForm.reset();
            err && console.error(err);
            MessageActions.error(
              this.state.service
                ? `Could not import ${this.state.service.get('displayName')}. Please contact us if this error persists.`
                : `Invalid import service. Please contact us if this error persists.`,
              this.props.dispatcher,
            );
          });
      } else {
        return MessageActions.error('Please select an import format', this.props.dispatcher);
      }
    }

    private onClickImport(evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();
      if (this.state.format === Format.CSV) {
        $('#importInput').click();
      } else {
        if (!this.state.service) {
          return MessageActions.error('Please select a text format', this.props.dispatcher);
        }
        // import action
        const form = new FormData();
        const importText = this.refs['importText'] as HTMLInputElement;
        form.append('upload', importText.value);
        CardsAPI.importCards(form, this.state.service.get('type'), 'txt')
          .then(() => {
            MessageActions.success(
              'Your import is in progress. You will receive an email when it is complete.',
              this.props.dispatcher,
            );
            importText.value = '';
          })
          .catch((err) => {
            err && console.error(err);
            MessageActions.error(
              this.state.service
                ? `Could not import ${this.state.service.get('displayName')}. Please contact us if this error persists.`
                : `Invalid export service. Please contact us if this error persists.`,
              this.props.dispatcher,
            );
          });
      }
    }

    private onClickExport(evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();
      // export based on the format and the provider
      if (this.state.format === Format.CSV) {
        if (!this.state.service) {
          return MessageActions.error('Please select a CSV format', this.props.dispatcher);
        }
        // export action
        CardsAPI.exportCards(this.state.service.get('type'), 'csv')
          .then(() => {
            MessageActions.success('Check your inbox for an email containing your export', this.props.dispatcher);
          })
          .catch((err) => {
            err && console.error(err);
            MessageActions.error(
              this.state.service
                ? `Could not export ${this.state.service.get('displayName')}. Please contact us if this error persists.`
                : `Invalid export service. Please contact us if this error persists.`,
              this.props.dispatcher,
            );
          });
      } else {
        MessageActions.error('Please select an export format', this.props.dispatcher);
      }
    }

    private onClickUpgrade(evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();
      history.push('/settings/subscription');
    }

    private renderUpgradeMessage(method: Method): JSX.Element | null {
      let message;
      switch (method) {
        case Method.IMPORT:
          const upgradingToKnight = 'upgrading to Knight';
          message = `Note: You can import an unlimited amount of cards, 
          but you'll be limited to browsing the first ${FreeLimits.CARDS} while on the free Squire account. 
          This gives you the chance to try out the platform before ${upgradingToKnight} and accessing all our features.`;
          return this.generateUpgradeMessage(message, upgradingToKnight);
        case Method.EXPORT:
          const upgradeToKnight = 'Upgrade to Knight';
          message = `Note: Free accounts can only export up to ${FreeLimits.CARDS} cards. 
          ${upgradeToKnight} for unlimited card exports, and more!`;
          return this.generateUpgradeMessage(message, upgradeToKnight);
        default:
          return null;
      }
    }

    private generateUpgradeMessage(message: string, buttonText: string) {
      const [start, end] = message.split(buttonText);

      const upgradeButton = (
        <div className="button-text" onClick={this.onClickUpgrade.bind(this)}>
          {buttonText}
        </div>
      );

      return (
        <div className="lato-N4" style={{ textAlign: 'center', marginTop: '2rem' }}>
          {start}
          {upgradeButton}
          {end}
        </div>
      );
    }

    private preloadImages() {
      this.props.services.forEach((service: DataService) => ImageHelper.preload(service.get('iconURL')));
    }
  },
);
