// npm
import * as Immutable from 'immutable';
import * as React from 'react';
// Actions
import * as InputSessionActions from '../../actions/InputSessionActions';
import * as MessageActions from '../../actions/MessageActions';
// Containers
import * as HasInputSessions from '../../containers/HasInputSessions';
// This component needs to know about input sources because of private async createNewSession
import * as HasInputSources from '../../containers/HasInputSources';
import * as HasMe from '../../containers/HasMe';
import * as HasStackedLocations from '../../containers/HasStackedLocations';
// Dispatcher
import Dispatcher from '../../dispatcher/Dispatcher';
import { CoreAssets } from '../../helpers/core_assets';
// Models
import displayCurrency from '../../helpers/currency_helper';
import { TextFormat } from '../../helpers/fmt';
import { CardInstances } from '../../models/CardInstances';
import { AnyInputSession } from '../../models/InputSession';
import { InputSourceType } from '../../models/InputSource';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { StackLocation } from '../../models/StackLocation';
import { SessionState } from '../../stores/InputSessionStore';
// Components
import { Dialog } from '../components/Dialog';
import { NavPager } from '../components/NavPager';
import { ResultsContent, ResultsSummary } from '../components/ResultsSummary';
import { CommitDialog, CommitDialogMode } from './CommitDialog';
import { InputSessionFilter } from './InputSessionFilter';
import { InputSessionPanel } from './InputSessionPanel';
import { InputSessionsPlaceholder } from './InputSessionsPlaceholder';
import { InsertCardsDialog } from './InsertCardsDialog';
import { QuickAddDialog } from './QuickAddDialog';
import { StagingActionsPanel } from './StagingActionsPanel';

export enum DialogStatus {
  CLOSED = 'closed',
  CARD_ENTRY = 'card-entry-dialog',
  QUICK_ADD = 'quick-add',
  TAG = 'tag',
  QUICK_ADD_TAG = 'quick-add-tag',
  COMMIT_ALL_TAG = 'commit-all-tag',
  STAGING_ACTIONS = 'staging-actions',
  ACTION_ALL = 'action-all',
  DELETE_SESSION = 'delete-session',
  DELETE_ALL_SESSIONS = 'delete-all-sessions',
}

interface IProps {
  dispatcher: Dispatcher;
}

interface IState {
  dialogStatus: DialogStatus;
  quickAddCardGroups: Immutable.OrderedMap<string, MTGCardGroup>;
}

type AllProps = IProps & HasInputSessions.IProps & HasInputSources.IProps & HasMe.IProps & HasStackedLocations.IProps;

export default HasInputSessions.Attach<IProps>(
  HasInputSources.Attach<IProps & HasInputSessions.IProps>(
    HasMe.Attach<IProps & HasInputSessions.IProps & HasInputSources.IProps>(
      HasStackedLocations.Attach<IProps & HasInputSessions.IProps & HasInputSources.IProps & HasMe.IProps>(
        class extends React.Component<AllProps, IState> {
          constructor(props: AllProps) {
            super(props);
            this.state = {
              dialogStatus: DialogStatus.CLOSED,
              quickAddCardGroups: Immutable.OrderedMap<string, MTGCardGroup>(),
            };
          }

          public async componentDidMount() {
            // Don't load sessions when navigating back to a populated list
            // To trigger a reload call InputSessionActions.setLoading()
            if (this.props.sessionListState == SessionState.LOADING) {
              this.loadSessions();
            }
            CoreAssets.preload();
          }

          public render() {
            const inputSession = this.getInputSession();
            const inputSource = inputSession?.inputSource(this.props.inputSources);
            const loadingSessions = this.props.sessionListState != SessionState.IDLE;
            const sourceType =
              this.props.filters.get('sourceType') === 'inactive'
                ? undefined
                : (this.props.filters.get('sourceType') as InputSourceType);

            return (
              <>
                <Dialog
                  isOpen={this.state.dialogStatus === DialogStatus.DELETE_SESSION}
                  onDismiss={this.closeDialog.bind(this)}
                >
                  <h3 className="query-dialog-heading">Are you sure?</h3>
                  <p className="query-dialog-subheading">
                    {`You are deleting a session with ${TextFormat.quantityOf(
                      'card',
                      inputSession === undefined ? 0 : inputSession.get('totalCount'),
                      false,
                    )}. This decision cannot be reversed!`}
                  </p>
                  <div className="flex justify-center">
                    <input
                      type="submit"
                      className="button-alert"
                      value="Remove"
                      autoComplete="off"
                      onClick={(evt: React.SyntheticEvent<HTMLElement>) => {
                        evt.preventDefault();
                        this.onDeleteInputSession();
                      }}
                    />
                  </div>
                </Dialog>
                <Dialog
                  isOpen={this.state.dialogStatus === DialogStatus.DELETE_ALL_SESSIONS}
                  onDismiss={this.closeDialog.bind(this)}
                >
                  <h3 className="query-dialog-heading">Are you sure?</h3>
                  <p className="query-dialog-subheading">{this.generateDeleteAllMessage()}</p>
                  <div className="flex justify-center">
                    <input
                      type="submit"
                      className="button-alert"
                      value="Remove"
                      autoComplete="off"
                      onClick={(evt: React.SyntheticEvent<HTMLElement>) => {
                        evt.preventDefault();
                        this.onDeleteAllConfirm();
                      }}
                    />
                  </div>
                </Dialog>
                {inputSession && inputSource && (
                  <InsertCardsDialog
                    inputSession={inputSession}
                    inputSource={inputSource}
                    dispatcher={this.props.dispatcher}
                    onSubmit={this.onInsertCards.bind(this)}
                    onDismiss={this.closeDialog.bind(this)}
                    currentStatus={this.state.dialogStatus}
                  />
                )}
                <QuickAddDialog
                  dispatcher={this.props.dispatcher}
                  onDismiss={this.closeDialog.bind(this)}
                  onSubmit={this.toQuickAddTags.bind(this)}
                  currentStatus={this.state.dialogStatus}
                  cardGroups={this.state.quickAddCardGroups}
                  updateCardGroups={this.updateQuickAddCards.bind(this)}
                />
                <CommitDialog
                  mode={CommitDialogMode.TAG}
                  totalCards={this.getInputSession()?.get('totalCount') || 0}
                  status={this.state.dialogStatus}
                  dispatcher={this.props.dispatcher}
                  locationPath={this.props.locationPath}
                  onClose={this.closeDialog.bind(this)}
                  onSave={this.commitInputSession.bind(this)}
                />
                <CommitDialog
                  mode={CommitDialogMode.QUICK_ADD_TAG}
                  totalCards={this.state.quickAddCardGroups.reduce(
                    (acc: number, cardGroup: MTGCardGroup) => acc + cardGroup.get('cardInstanceCount'),
                    0,
                  )}
                  status={this.state.dialogStatus}
                  dispatcher={this.props.dispatcher}
                  locationPath={this.props.locationPath}
                  onClose={this.closeQuickAddTags.bind(this)}
                  onSave={this.onSaveQuickAdd.bind(this)}
                />
                <CommitDialog
                  mode={CommitDialogMode.COMMIT_ALL_TAG}
                  totalCards={this.totalCards()}
                  status={this.state.dialogStatus}
                  dispatcher={this.props.dispatcher}
                  locationPath={this.props.locationPath}
                  onClose={this.closeDialog.bind(this)}
                  onSave={this.onCommitAll.bind(this)}
                />
                <StagingActionsPanel
                  isOpen={this.state.dialogStatus === DialogStatus.ACTION_ALL}
                  game={this.props.filters.get('game')}
                  sourceType={sourceType}
                  totalCount={this.totalCards()}
                  onDismiss={this.closeDialog.bind(this)}
                  dispatcher={this.props.dispatcher}
                  onClose={this.closeDialog.bind(this)}
                />
                <StagingActionsPanel
                  isOpen={this.state.dialogStatus === DialogStatus.STAGING_ACTIONS}
                  game={this.getInputSession()?.get('game') || this.props.filters.get('game')}
                  totalCount={this.getInputSession()?.get('totalCount') || this.totalCards()}
                  sourceType={inputSource?.get('sourceType') || sourceType}
                  inputSession={this.getInputSession()}
                  onDismiss={this.closeDialog.bind(this)}
                  dispatcher={this.props.dispatcher}
                  onClose={this.closeDialog.bind(this)}
                />
                <InputSessionFilter
                  dispatcher={this.props.dispatcher}
                  loading={this.props.sessionListState == SessionState.LOADING}
                  filters={this.props.filters}
                  createNewSession={this.createNewSession.bind(this)}
                  currentIndex={this.props.inputSessionIndex}
                  totalSessions={this.props.inputSessions.size}
                  onSubmit={this.loadSessions.bind(this)}
                  onDeleteAllSessions={this.onClickDeleteAll.bind(this)}
                  onCommitAllSessions={this.onClickCommitAll.bind(this)}
                  onActionAllSessions={this.onClickActionAll.bind(this)}
                />
                <div style={{ padding: 0, marginTop: '1rem' }}>
                  <div className="row">
                    <ResultsSummary content={this.resultsContent()} />
                    <div className="col-sm-12 col-md-8 flex align-center justify-end" style={{ marginBottom: '1rem' }}>
                      <NavPager
                        loading={this.props.sessionListState === SessionState.LOADING}
                        nextDisabled={this.props.inputSessionIndex + 1 >= this.props.inputSessions.size}
                        previousDisabled={this.props.inputSessionIndex <= 0}
                        onChangePage={(direction) => {
                          InputSessionActions.changeIndex(this.props.dispatcher, direction);
                        }}
                      />
                    </div>
                  </div>
                </div>
                <InputSessionsPlaceholder
                  sessionCount={this.props.inputSessions.size}
                  filters={this.props.filters}
                  createNewSession={this.createNewSession.bind(this)}
                  sessionListState={this.props.sessionListState}
                />
                {!loadingSessions && this.props.inputSessions.size > 0 && (
                  <>
                    <InputSessionPanel
                      dispatcher={this.props.dispatcher}
                      onClickAddToCollection={this.onClickAddToCollection.bind(this)}
                      onClickAddCards={this.onClickAddCards.bind(this)}
                      openActions={this.openActions.bind(this)}
                      createNewSession={this.createNewSession.bind(this)}
                      onClickDelete={this.onConfirmRemoveSession.bind(this)}
                    />
                    <div style={{ marginTop: '1rem', padding: 0 }}>
                      <div className="flex justify-end">
                        <NavPager
                          loading={false}
                          nextDisabled={this.props.inputSessionIndex + 1 >= this.props.inputSessions.size}
                          previousDisabled={this.props.inputSessionIndex <= 0}
                          onChangePage={(direction) => {
                            InputSessionActions.changeIndex(this.props.dispatcher, direction);
                          }}
                        />
                      </div>
                    </div>
                  </>
                )}
              </>
            );
          }

          private getInputSession(): AnyInputSession | undefined {
            if (this.props.inputSessions.size === 0 || !this.props.inputSessions.has(this.props.inputSessionIndex)) {
              return undefined;
            }
            return this.props.inputSessions.get(this.props.inputSessionIndex) as AnyInputSession;
          }

          private totalCards(): number {
            return this.props.inputSessions.reduce(
              (sum: number, session: AnyInputSession) => (sum += session.get('totalCount')),
              0,
            );
          }

          private totalValue(): number {
            return this.props.inputSessions.reduce(
              (sum: number, session: AnyInputSession) => (sum += session.get('totalValue')),
              0,
            );
          }

          private resultsContent(): ResultsContent {
            switch (this.props.sessionListState) {
              case SessionState.LOADING:
                return new ResultsContent('Loading...');
              default:
                let prefix = '';
                if (this.props.inputSessions.size > 0) prefix = `${this.props.inputSessionIndex + 1} of `;
                const title = `${prefix}${TextFormat.quantityOf('Session', this.props.inputSessions.size)}`;

                const cards = TextFormat.quantityOf('Card', this.totalCards());
                const totalValue = displayCurrency(
                  this.totalValue(),
                  true,
                  this.props.me.get('preferences').get('localization').get('currency'),
                );
                const subtitle = `${cards} • ${totalValue}`;
                return new ResultsContent(title, subtitle);
            }
          }

          private getUUID(): string | undefined {
            const inputSession = this.getInputSession();
            return inputSession === undefined ? undefined : inputSession.get('uuid');
          }

          private async createNewSession(evt: React.SyntheticEvent<HTMLElement>) {
            evt.preventDefault();
            this.setState({ dialogStatus: DialogStatus.QUICK_ADD });
          }

          updateQuickAddCards(quickAddCardGroups: Immutable.OrderedMap<string, MTGCardGroup>) {
            this.setState({ quickAddCardGroups: quickAddCardGroups });
          }

          private async toQuickAddTags(): Promise<void> {
            this.setState({ dialogStatus: DialogStatus.QUICK_ADD_TAG });
          }

          private onClickAddToCollection(evt: React.SyntheticEvent<HTMLElement>) {
            evt.preventDefault();
            const inputSession = this.getInputSession();
            if (inputSession && inputSession.get('cardPage').get('totalCount') > 0) {
              InputSessionActions.closeCardPanel(this.props.dispatcher);
              this.setState({ dialogStatus: DialogStatus.TAG });
            }
          }

          private onClickAddCards(evt: React.SyntheticEvent<HTMLElement>) {
            this.setState({ dialogStatus: DialogStatus.CARD_ENTRY });
          }

          private onInsertCards(cardGroups: Immutable.OrderedMap<string, MTGCardGroup>) {
            const inputSession = this.getInputSession();
            if (inputSession) {
              InputSessionActions.addCards(
                inputSession.get('uuid'),
                this.props.inputSessionIndex,
                cardGroups.reverse().toList(),
                this.props.dispatcher,
              );
            }

            this.closeDialog();
          }

          private async onDeleteInputSession() {
            const inputSession = this.getInputSession();
            if (inputSession) {
              this.setState({ dialogStatus: DialogStatus.CLOSED });
              InputSessionActions.closeCardPanel(this.props.dispatcher);
              await InputSessionActions.deleteSession(
                this.props.dispatcher,
                this.props.inputSessionIndex,
                inputSession,
              );
            }
          }

          private async onClickCommitAll() {
            if (this.totalCards() <= 0) {
              return;
            }
            this.setState({ dialogStatus: DialogStatus.COMMIT_ALL_TAG });
          }

          private async onClickActionAll() {
            if (this.totalCards() <= 0) {
              return;
            }
            this.setState({ dialogStatus: DialogStatus.ACTION_ALL });
          }

          private async onCommitAll() {
            InputSessionActions.setSessionListState(SessionState.COMMITTING, this.props.dispatcher);
            this.closeDialog();
            InputSessionActions.commitAll(this.props.filters, this.totalCards(), this.props.dispatcher);
          }

          private onClickDeleteAll() {
            this.setState({ dialogStatus: DialogStatus.DELETE_ALL_SESSIONS });
          }

          private async onDeleteAllConfirm() {
            InputSessionActions.setSessionListState(SessionState.DELETING, this.props.dispatcher);
            InputSessionActions.clearAll(this.props.filters, this.props.dispatcher);
            this.setState({ dialogStatus: DialogStatus.CLOSED });
          }

          private closeDialog() {
            this.setState({
              dialogStatus: DialogStatus.CLOSED,
            });
          }

          private closeQuickAddTags() {
            this.setState({
              dialogStatus: DialogStatus.QUICK_ADD,
            });
          }

          private openActions() {
            const inputSession = this.getInputSession();
            if (inputSession && inputSession.get('cardPage').get('totalCount') > 0) {
              InputSessionActions.closeCardPanel(this.props.dispatcher);
              this.setState({
                dialogStatus: DialogStatus.STAGING_ACTIONS,
              });
            }
          }

          private async onSaveQuickAdd() {
            InputSessionActions.setSessionListState(SessionState.IDLE, this.props.dispatcher);
            await InputSessionActions.quickAdd(
              this.state.quickAddCardGroups.toList(),
              this.props.locationPath.map((location: StackLocation) => location.get('name')).toList(),
              this.props.dispatcher,
            )
              .then((cardInstances: CardInstances) => {
                MessageActions.success(
                  `${TextFormat.quantityOf('card', cardInstances.size)} added to collection`,
                  this.props.dispatcher,
                );
                this.setState(
                  { dialogStatus: DialogStatus.CLOSED },
                  // Seperating this from the main setState prevents number of cards being tagged being displayed incorrectly
                  () => {
                    this.setState({ quickAddCardGroups: Immutable.OrderedMap<string, MTGCardGroup>() });
                  },
                );
              })
              .catch((err) => {
                err && console.error(err.err);
                MessageActions.error(
                  'There was an error committing this session, please try again.',
                  this.props.dispatcher,
                );
                this.setState({
                  dialogStatus: DialogStatus.CLOSED,
                  quickAddCardGroups: Immutable.OrderedMap<string, MTGCardGroup>(),
                });
              });
          }

          private async commitInputSession() {
            const inputSession = this.getInputSession();
            if (!inputSession) {
              return;
            }
            InputSessionActions.setSessionState(SessionState.COMMITTING, this.props.dispatcher);
            this.setState({ dialogStatus: DialogStatus.CLOSED });
            // Commit the staged session
            InputSessionActions.commit(
              this.props.dispatcher,
              inputSession,
              this.props.inputSessionIndex,
              this.props.locationPath.map((location: StackLocation) => location.get('name')).toList(),
            );
          }

          private loadSessions(filters = this.props.filters) {
            InputSessionActions.setSessionListLoading(this.props.dispatcher, filters);
            InputSessionActions.closeCardPanel(this.props.dispatcher);
            InputSessionActions.search(this.props.dispatcher, true, filters);
          }

          private onConfirmRemoveSession(evt: React.SyntheticEvent<HTMLElement>) {
            evt.preventDefault();
            if (this.props.inputSessions.size === 0 || !this.props.inputSessions.has(this.props.inputSessionIndex)) {
              return;
            } else if (this.getInputSession()?.get('totalCount') === 0) {
              this.onDeleteInputSession();
            } else {
              this.setState({ dialogStatus: DialogStatus.DELETE_SESSION });
            }
          }

          private generateDeleteAllMessage(): string {
            if (this.props.inputSessions.size === 1) {
              return `You are deleting a session with ${TextFormat.quantityOf(
                'card',
                this.getInputSession()?.get('totalCount') || 0,
                false,
              )}. This decision cannot be reversed!`;
            }

            const sessionText = TextFormat.quantityOf('Session', this.props.inputSessions.size, false);

            const cardsText = TextFormat.quantityOf('Card', this.totalCards());

            return `You are removing ${sessionText} and ${cardsText} from your staging area. This cannot be reversed!`;
          }
        },
      ),
    ),
  ),
);
