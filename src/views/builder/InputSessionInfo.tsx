import * as React from 'react';
import * as HasInputSessions from '../../containers/HasInputSessions';
import * as HasInputSources from '../../containers/HasInputSources';
import Dispatcher from '../../dispatcher/Dispatcher';
import { TextFormat } from '../../helpers/fmt';
import { CardBotSessionMetadata, ScanStatus } from '../../models/CardBotSessionMetadata';
import { AnyInputSession } from '../../models/InputSession';
import { InputSource, InputSourceType } from '../../models/InputSource';
import { FeaturePill, PillColor } from '../components/FeaturePill';

interface IProps {
  dispatcher: Dispatcher;
  inputSession: AnyInputSession;
}

interface IState {}

export const InputSessionInfo = HasInputSessions.Attach<IProps>(
  HasInputSources.Attach<IProps & HasInputSessions.IProps>(
    class extends React.Component<IProps & HasInputSessions.IProps & HasInputSources.IProps, IState> {
      constructor(props: IProps & HasInputSessions.IProps & HasInputSources.IProps) {
        super(props);
        this.state = {};
      }

      public render() {
        const inputSource = this.props.inputSources.find(
          (source: InputSource) => source.get('id') === this.props.inputSession.get('inputSourceId'),
        );

        let sourceInfo = 'Unknown';
        if (inputSource) {
          sourceInfo = `${inputSource.get('name')} • ${InputSource.typeToString(inputSource.get('sourceType'))}`;
        }

        const dateTime = Intl.DateTimeFormat('en-US', {
          weekday: 'short',
          month: 'short',
          day: 'numeric',
          hour: 'numeric',
          minute: 'numeric',
          hour12: true,
        }).format(this.props.inputSession.get('createdAt'));

        let metadataContent: JSX.Element | null = null;
        if (this.props.inputSession.get('metadata')) {
          if (inputSource && inputSource.get('sourceType') == InputSourceType.CARDBOT) {
            metadataContent = this.cardBotMetadataContent(this.props.inputSession.get('metadata'));
          }
        }

        return (
          <>
            <span className="builder-count">{sourceInfo}</span>
            <span className="builder-value">{dateTime}</span>
            <div style={{ marginTop: '0.5rem' }}>{metadataContent}</div>
          </>
        );
      }

      cardBotMetadataContent(metadata: CardBotSessionMetadata): JSX.Element {
        const scanStatus = metadata.get('scanStatus');
        let color = PillColor.GREEN;

        switch (metadata.get('scanStatus')) {
          case ScanStatus.ACCEPTED:
            color = PillColor.GREEN;
            break;
          case ScanStatus.REJECTED:
            color = PillColor.BLUE;
            break;
        }

        return <FeaturePill text={TextFormat.capitalize(scanStatus)} color={color} />;
      }
    },
  ),
);
