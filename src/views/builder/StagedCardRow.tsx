import clear from '@iconify/icons-ic/clear';
import * as Immutable from 'immutable';
import * as React from 'react';
import displayCurrency from '../../helpers/currency_helper';
import { DateFormat } from '../../helpers/fmt';
import { CardAttributes, CardFormAttributes } from '../../models/CardAttributes';
import { ResolvePrice, ResolveValue } from '../../models/CardInstances';
import { CellBorders } from '../../models/CellBorder';
import { Condition } from '../../models/Condition';
import { Foil } from '../../models/Foil';
import { Grouping } from '../../models/Grouping';
import { Language } from '../../models/Language';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { User } from '../../models/Users';
import { ConditionSelector } from '../components/selectors/ConditionSelector';
import { LanguageSelector } from '../components/selectors/LanguageSelector';
import * as Table from '../components/Table';

interface IProps {
  index: number;
  me: User;
  languageOptions: Immutable.Set<Language>;
  final: boolean;
  onUpdateAttributes: (cardGroup: MTGCardGroup, attributes: CardAttributes) => void;
  grouping: Grouping;
  cardGroup: MTGCardGroup;
  onRemove: (cardGroup: MTGCardGroup) => void;
  onClick?: (evt: React.SyntheticEvent<HTMLElement>) => void;
}

interface IState {
  attributes: CardFormAttributes;
}

export class StagedCardRow extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      attributes: this.groupAttributes(this.props),
    };
  }

  cardInstances(props: IProps) {
    return props.cardGroup.get('cardInstances');
  }

  groupAttributes(props: IProps): CardFormAttributes {
    return new CardFormAttributes({
      foil: this.groupFoil(props),
      condition: this.groupCondition(props),
      language: this.groupLanguage(props),
    });
  }

  groupFoil(props: IProps) {
    return ResolveValue(this.cardInstances(props), 'foil', Foil.Partial, Foil.Off);
  }

  groupCondition(props: IProps) {
    return ResolveValue(this.cardInstances(props), 'condition', 'Multiple', Condition.NEAR_MINT);
  }

  groupLanguage(props: IProps) {
    return ResolveValue(this.cardInstances(props), 'language', 'Multiple', Language.ENGLISH);
  }

  // Attributes can be changed from CardPanel - override state
  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<IState>, snapshot?: any): void {
    if (!this.groupAttributes(prevProps).equals(this.groupAttributes(this.props))) {
      this.setState({
        attributes: this.groupAttributes(this.props),
      });
    }
  }

  onUpdateFoil(value: boolean) {
    const newFoil = value ? Foil.On : Foil.Off;
    this.setState({ attributes: this.state.attributes.set('foil', newFoil) });
    this.props.onUpdateAttributes(this.props.cardGroup, new CardAttributes({ foil: newFoil }));
  }

  onUpdateCondition(condition: Condition) {
    this.setState({ attributes: this.state.attributes.set('condition', condition) });
    this.props.onUpdateAttributes(this.props.cardGroup, new CardAttributes({ condition: condition }));
  }

  onUpdateLanguage(language: Language) {
    this.setState({ attributes: this.state.attributes.set('language', language) });
    this.props.onUpdateAttributes(this.props.cardGroup, new CardAttributes({ language: language }));
  }

  onClick(evt: React.SyntheticEvent<HTMLElement>) {
    this.props.onClick && this.props.onClick(evt);
  }

  render() {
    const cardInstances = this.props.cardGroup.get('cardInstances');
    const price = ResolvePrice(cardInstances);
    const priceString = price
      ? displayCurrency(price, true, this.props.me.get('preferences').get('localization').get('currency'))
      : '-';
    const dark = this.props.index % 2 === 0 ? true : false;
    const timezone = this.props.me.get('preferences').get('localization').get('timezone');
    const cardInstance = this.props.cardGroup.get('cardInstances').first();

    return (
      <React.Fragment key={cardInstance.get('id')}>
        {this.props.grouping === Grouping.NONE ? null : (
          <Table.AmountCell
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            amount={this.props.cardGroup.get('cardInstanceCount')}
            large={true}
            onClick={this.onClick.bind(this)}
          />
        )}
        <Table.MTGCardInstanceInfoCell
          cellStyle={{
            dark: dark,
            cellBorders: CellBorders.default(this.props.final),
            large: true,
            onClick: this.onClick.bind(this),
          }}
          cardInstance={cardInstance}
          grouping={this.props.grouping}
          displayConfidence={this.props.grouping == Grouping.NONE}
        />
        <Table.CheckBoxCell
          checked={this.state.attributes.get('foil') == Foil.On}
          dark={dark}
          centerCell={true}
          cellBorders={CellBorders.default(this.props.final)}
          large={true}
          onChange={this.onUpdateFoil.bind(this)}
        />
        <Table.SelectorCell dark={dark} cellBorders={CellBorders.default(this.props.final)}>
          <ConditionSelector
            condition={this.state.attributes.get('condition')}
            onChange={this.onUpdateCondition.bind(this)}
            hideTitle={true}
          />
        </Table.SelectorCell>
        <Table.SelectorCell dark={dark} cellBorders={CellBorders.default(this.props.final)}>
          <LanguageSelector
            language={this.state.attributes.get('language')}
            options={this.props.languageOptions}
            onChange={this.onUpdateLanguage.bind(this)}
            hideTitle={true}
          />
        </Table.SelectorCell>
        <Table.TextCell
          dark={dark}
          centerCell={true}
          cellBorders={CellBorders.default(this.props.final)}
          text={priceString}
          large={true}
          onClick={this.onClick.bind(this)}
        />
        <Table.TextCell
          dark={dark}
          centerCell={true}
          cellBorders={CellBorders.default(this.props.final)}
          text={DateFormat.human(this.props.cardGroup.get('cardInstanceLatest'), timezone)}
          large={true}
          onClick={this.onClick.bind(this)}
        />
        <Table.MaterialIcon
          dark={dark}
          centerCell={true}
          materialIconText={clear}
          large={true}
          cellBorders={CellBorders.rightCell(this.props.final)}
          onClick={() => {
            this.props.onRemove(this.props.cardGroup);
          }}
        />
      </React.Fragment>
    );
  }
}
