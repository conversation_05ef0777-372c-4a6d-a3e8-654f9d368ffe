import clear from '@iconify/icons-ic/clear';
import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import displayCurrency from '../../../helpers/currency_helper';
import { DateFormat } from '../../../helpers/fmt';
import { CardAttributes, CardFormAttributes } from '../../../models/CardAttributes';
import { CellBorders } from '../../../models/CellBorder';
import { Condition } from '../../../models/Condition';
import { Grouping } from '../../../models/Grouping';
import { Language } from '../../../models/Language';
import { PokeCardGroup } from '../../../models/pokemon/PokeCardPage';
import { PokeFinish } from '../../../models/pokemon/PokeFinish';
import { User } from '../../../models/Users';
import { ConditionSelector } from '../../components/selectors/ConditionSelector';
import { PokeFinishSelector } from '../../components/selectors/PokeFinishSelector';
import * as Table from '../../components/Table';

interface IProps {
  index: number;
  me: User;
  languageOptions: Immutable.Set<Language>;
  final: boolean;
  onUpdateAttributes: (cardGroup: PokeCardGroup, attributes: CardAttributes) => void;
  grouping: Grouping;
  cardGroup: PokeCardGroup;
  onRemove: (cardGroup: PokeCardGroup) => void;
  onClick?: () => void;
  dispatcher: Dispatcher;
}

interface IState {
  attributes: CardFormAttributes;
}

export class PokeStagedCardRow extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      attributes: this.groupAttributes(this.props),
    };
  }

  groupAttributes(props: IProps): CardFormAttributes {
    return new CardFormAttributes({
      condition: props.cardGroup.groupValue('condition', 'Multiple', Condition.NEAR_MINT),
      pokeFinish: props.cardGroup.groupValue('finish', 'Multiple', new PokeFinish()),
    });
  }

  // Attributes can be changed from CardPanel - override state
  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<IState>, snapshot?: any): void {
    if (!this.groupAttributes(prevProps).equals(this.groupAttributes(this.props))) {
      this.setState({
        attributes: this.groupAttributes(this.props),
      });
    }
  }

  onClick(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.props.onClick && this.props.onClick();
  }

  onUpdateCondition(condition: Condition) {
    this.setState({ attributes: this.state.attributes.set('condition', condition) });
    this.props.onUpdateAttributes(this.props.cardGroup, new CardAttributes({ condition: condition }));
  }

  onUpdatePokeFinish(finish: PokeFinish) {
    this.setState({ attributes: this.state.attributes.set('pokeFinish', finish) });
    this.props.onUpdateAttributes(this.props.cardGroup, new CardAttributes({ pokeFinish: finish }));
  }

  render() {
    const price = this.props.cardGroup.priceSummary();
    const priceString = price
      ? displayCurrency(price, true, this.props.me.get('preferences').get('localization').get('currency'))
      : '-';
    const dark = this.props.index % 2 === 0 ? true : false;
    const timezone = this.props.me.get('preferences').get('localization').get('timezone');
    const cardInstance = this.props.cardGroup.get('cardInstances').first();

    return (
      <React.Fragment key={cardInstance.get('uuid')}>
        {this.props.grouping === Grouping.NONE ? null : (
          <Table.AmountCell
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            amount={this.props.cardGroup.get('cardInstanceCount')}
            large={true}
            onClick={this.onClick.bind(this)}
          />
        )}
        <Table.PokeCardInfoCell
          cellStyle={{
            dark: dark,
            cellBorders: CellBorders.default(this.props.final),
            large: true,
            onClick: this.onClick.bind(this),
          }}
          cardInstance={cardInstance}
          grouping={this.props.grouping}
          displayConfidence
        />
        <Table.SelectorCell dark={dark} cellBorders={CellBorders.default(this.props.final)}>
          <ConditionSelector
            condition={this.state.attributes.get('condition')}
            onChange={this.onUpdateCondition.bind(this)}
            hideTitle={true}
          />
        </Table.SelectorCell>
        <Table.SelectorCell dark={dark} cellBorders={CellBorders.default(this.props.final)}>
          <PokeFinishSelector
            finish={this.state.attributes.get('pokeFinish')}
            onChange={this.onUpdatePokeFinish.bind(this)}
            hideTitle={true}
            dispatcher={this.props.dispatcher}
          />
        </Table.SelectorCell>
        <Table.TextCell
          dark={dark}
          centerCell={true}
          cellBorders={CellBorders.default(this.props.final)}
          text={priceString}
          large={true}
          onClick={this.onClick.bind(this)}
        />
        <Table.TextCell
          dark={dark}
          centerCell={true}
          cellBorders={CellBorders.default(this.props.final)}
          text={DateFormat.human(this.props.cardGroup.get('cardInstanceLatest'), timezone)}
          large={true}
          onClick={this.onClick.bind(this)}
        />
        <Table.MaterialIcon
          dark={dark}
          centerCell={true}
          materialIconText={clear}
          large={true}
          cellBorders={CellBorders.rightCell(this.props.final)}
          onClick={() => {
            this.props.onRemove(this.props.cardGroup);
          }}
        />
      </React.Fragment>
    );
  }
}
