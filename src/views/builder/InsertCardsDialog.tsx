import playlist_add from '@iconify/icons-ic/baseline-playlist-add';
import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../dispatcher/Dispatcher';
import { AnyInputSession } from '../../models/InputSession';
import { InputSource } from '../../models/InputSource';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { Button, ButtonClass } from '../components/filters/UpdateFiltersButton';
import { DialogStatus as BuilderDialogStatus, DialogStatus } from './BuilderManual';
import { CardEntryDialog } from './CardEntryDialog';

interface IProps {
  dispatcher: Dispatcher;
  inputSession: AnyInputSession;
  inputSource: InputSource;
  currentStatus: DialogStatus;
  onDismiss: () => void;
  onSubmit: (cardGroups: Immutable.OrderedMap<string, MTGCardGroup>) => void;
}

export function InsertCardsDialog(props: IProps) {
  const [cardGroups, setCardGroups] = React.useState<Immutable.OrderedMap<string, MTGCardGroup>>(
    Immutable.OrderedMap<string, MTGCardGroup>(),
  );

  React.useEffect(() => {
    setCardGroups(Immutable.OrderedMap<string, MTGCardGroup>());
  }, [props.inputSession.get('uuid')]);

  function addCards(cardGroups: Immutable.OrderedMap<string, MTGCardGroup>): void {
    setCardGroups(cardGroups);
  }

  function onClickAddCards(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    props.onSubmit(cardGroups);
    setCardGroups(Immutable.OrderedMap<string, MTGCardGroup>());
  }

  const dateTime =
    ' • ' +
    Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    }).format(props.inputSession.get('createdAt'));

  return (
    <CardEntryDialog
      dispatcher={props.dispatcher}
      targetStatus={BuilderDialogStatus.CARD_ENTRY}
      title={'Insert Cards'}
      subtitle={`${props.inputSource.get('name')} • ${InputSource.typeToString(
        props.inputSource.get('sourceType'),
      )}${dateTime}`}
      currentStatus={props.currentStatus}
      onDismiss={props.onDismiss}
      cardGroups={cardGroups}
      updateCardGroups={addCards}
    >
      <div className="col-xs-12 flex justify-center" style={{ marginTop: '1rem', gap: '1rem' }}>
        <Button
          class={ButtonClass.PRIMARY}
          disabled={cardGroups.size === 0}
          icon={playlist_add}
          title="Insert"
          onClick={onClickAddCards.bind(this)}
        />
        <Button class={ButtonClass.ALERT} title="Cancel" onClick={props.onDismiss} />
      </div>
    </CardEntryDialog>
  );
}
