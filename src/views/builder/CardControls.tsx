// npm
// Iconify
import plus from '@iconify/icons-ic/baseline-plus';
import sort from '@iconify/icons-ic/sort';
import style from '@iconify/icons-ic/style';
import * as React from 'react';
// Helpers
import { TextFormat } from '../../helpers/fmt';
import { Game } from '../../models/Game';
// Models
import { Grouping, type GroupingKey } from '../../models/Grouping';
import { AnyInputSession } from '../../models/InputSession';
import { SortingGroupingHelper } from '../../models/sorting/Helper';
import { Sorting, type SortingKey } from '../../models/sorting/Sorting';
import { Button, ButtonClass } from '../components/filters/UpdateFiltersButton';
import { IconSelect } from '../components/IconSelect';

interface IProps {
  loading: boolean;
  inputSession: AnyInputSession;
  searchQuery?: string;
  onClickAddCards: (evt: React.SyntheticEvent<HTMLElement>) => void;
  onSearchCards: (grouping: Grouping, sorting: Sorting, query?: string) => void;
}

export const CardControls = (props: IProps) => {
  const propsQuery = props.inputSession.get('cardPage').get('filterOptions').getQuery();
  const propsGrouping = props.inputSession.get('cardPage').get('cardGrouping');
  const propsSorting = props.inputSession.get('cardPage').get('cardSorting');

  const [searchQuery, setSearchQuery] = React.useState<string | undefined>(propsQuery);
  const [grouping, setGrouping] = React.useState<Grouping>(propsGrouping);
  const [sorting, setSorting] = React.useState<Sorting>(propsSorting);

  // Reset the internal state to the props when it changes
  React.useEffect(() => {
    setSearchQuery(propsQuery);
    setGrouping(propsGrouping);
    setSorting(propsSorting);
  }, [props]);

  function updateQuery(evt: any) {
    evt.preventDefault();
    setSearchQuery(evt.currentTarget.value as string);
  }

  function updateGrouping(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    const newGrouping = evt.currentTarget.value as Grouping;
    setGrouping(newGrouping);
    props.onSearchCards(newGrouping, sorting, searchQuery);
  }

  function updateSorting(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    const newSorting = evt.currentTarget.value as Sorting;
    setSorting(newSorting);
    props.onSearchCards(grouping, newSorting, searchQuery);
  }

  function submit(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    props.onSearchCards(grouping, sorting, searchQuery);
  }

  return (
    <>
      <div className={'flex vertical grow-xs-1'}>
        <form onSubmit={submit}>
          <div className="info-heading">Search</div>
          <div>
            <input
              className="advanced-search-input"
              style={{ textOverflow: 'ellipsis' }}
              type="text"
              placeholder="Search by card name"
              value={searchQuery || ''}
              onChange={updateQuery}
            />
          </div>
        </form>
      </div>
      <div className={'flex vertical'}>
        <div className="info-heading">Sort By</div>
        <IconSelect className="icon-container--card-input" icon={sort} onChange={updateSorting} value={sorting}>
          <>
            <option disabled>Sort By</option>
            {Object.keys(Sorting).map((key) => {
              const grouping = props.inputSession.get('cardPage').get('cardGrouping');
              if (SortingGroupingHelper.validCombo(grouping, Sorting[key as SortingKey])) {
                return (
                  <option key={key} value={Sorting[key as SortingKey]}>
                    {Sorting[key as SortingKey]}
                  </option>
                );
              }
            })}
          </>
        </IconSelect>
      </div>
      <div className={'flex vertical'}>
        <div className="info-heading">Group By</div>
        <IconSelect className="icon-container--card-input" icon={style} onChange={updateGrouping} value={grouping}>
          <>
            <option disabled>Group By</option>
            {Object.keys(Grouping).map((key) => (
              <option key={key} value={Grouping[key as GroupingKey]}>
                {TextFormat.capitalize(Grouping[key as GroupingKey])}
              </option>
            ))}
          </>
        </IconSelect>
      </div>
      {props.inputSession.get('game') == Game.MTG && (
        <div className={'flex grow-xs-1'} style={{ alignItems: 'end', justifyContent: 'end' }}>
          <Button
            class={ButtonClass.PRIMARY}
            icon={plus}
            disabled={props.loading}
            title="Insert Cards"
            onClick={props.onClickAddCards}
          />
        </div>
      )}
    </>
  );
};
