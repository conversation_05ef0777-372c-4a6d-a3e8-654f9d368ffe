import * as Immutable from 'immutable';
import * as React from 'react';
import * as ImportExportActions from '../../../actions/ImportExportActions';
import * as MessageActions from '../../../actions/MessageActions';
import * as HasImportExport from '../../../containers/HasImportExport';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { TextFormat } from '../../../helpers/fmt';
import { ImageHelper } from '../../../helpers/img';
import { Game, gameName } from '../../../models/Game';
import { DataService, Format, Method } from '../../../models/ImportExport';
import { AnyInputSession } from '../../../models/InputSession';
import { InputSourceType } from '../../../models/InputSource';
import { Subscription, SubscriptionType } from '../../../models/Subscriptions';
import ImportExportRadio from '../../components/ImportExportRadio';
import { SendButtons } from './SendButtons';

interface IProps {
  isMerchant: boolean;
  dispatcher: Dispatcher;
  subscription: Subscription;
  inputSession?: AnyInputSession;
  game: Game;
  totalCount: number;
  sourceType?: InputSourceType;
  onClose: () => void;
}

interface IState {
  selectedService?: DataService;
}

export const SendToService = HasImportExport.Attach<IProps>(
  class extends React.Component<IProps & HasImportExport.IProps, IState> {
    /**
     * @override
     * @constructor
     */
    constructor(props: IProps & HasImportExport.IProps) {
      super(props);
      this.state = { selectedService: undefined };
    }

    /**
     * @override
     */
    public componentDidMount() {
      ImportExportActions.refresh(this.props.dispatcher);
    }

    render() {
      this.preloadIcons();

      if (this.props.isMerchant) {
        return (
          <>
            <div className="centered-row">
              <div className="paragraph-sm" style={{ maxWidth: '50rem' }}>
                {`Export ${TextFormat.quantityOf(`${gameName(this.props.game)} card`, this.props.totalCount, false)}`}
              </div>
            </div>
            {this.renderRadios()}
            <SendButtons
              isMerchant={this.props.isMerchant}
              onClickSend={this.onClickSend.bind(this)}
              onClose={this.props.onClose}
            />
            {this.state.selectedService &&
              this.state.selectedService
                .instructions(Method.EXPORT, Format.CSV)
                .map((instruction: string, index: number) => (
                  <p key={index} className="builder-import-export-header-format">
                    {instruction}
                  </p>
                ))}
          </>
        );
      } else {
        return (
          <>
            <div className="centered-row">
              <div className="paragraph-sm" style={{ maxWidth: '50rem' }}>
                Exporting staged cards is only available to Merchants. Upgrade to enable this feature.
              </div>
            </div>
            {this.renderLabels()}
            <SendButtons
              isMerchant={this.props.isMerchant}
              onClickSend={this.onClickSend.bind(this)}
              onClose={this.props.onClose}
            />
          </>
        );
      }
    }

    renderRadios(): JSX.Element {
      return (
        <div className="centered-row">
          {this.validServices().map((service: DataService) => {
            return (
              <ImportExportRadio
                key={service.get('type')}
                service={service}
                format={Format.CSV}
                selected={
                  this.state.selectedService !== undefined &&
                  this.state.selectedService.get('type') === service.get('type')
                }
                onSelected={() => this.onSelectService(service)}
              />
            );
          })}
        </div>
      );
    }

    renderLabels(): JSX.Element {
      return (
        <div className="centered-row">
          {this.validServices().map((service: DataService) => {
            const displayName = service.get('displayName');
            return (
              <label key={displayName} className="builder-import-export-label">
                <img src={service.get('iconURL')} />
                <span>{displayName}</span>
              </label>
            );
          })}
        </div>
      );
    }

    preloadIcons() {
      this.props.services.forEach((service: DataService) => ImageHelper.preload(service.get('iconURL')));
    }

    validServices(): Immutable.Iterable<number, DataService> {
      return this.props.services.filter((service: DataService) =>
        service.supports(Method.EXPORT_STAGED, Format.CSV, this.props.game),
      );
    }

    onSelectService(service: DataService) {
      this.setState({ selectedService: service });
    }

    onClickSend() {
      if (this.state.selectedService) {
        this.sendCards(this.state.selectedService);
      }
    }

    private sendCards(service: DataService) {
      const subscriptionType = this.props.subscription.get('type');
      if (!this.validServices().includes(service)) {
        const error = 'Invalid service for Send To feature.';
        console.error(error);
        MessageActions.error(error, this.props.dispatcher);
        return;
      } else if (
        subscriptionType !== SubscriptionType.MERCHANT &&
        subscriptionType !== SubscriptionType.MERCHANT_PROMOTIONAL
      ) {
        const error = `You are a ${this.props.subscription.get(
          'type',
        )}. To use this feature you must have a Merchant account.`;
        console.error(error);
        MessageActions.error(error, this.props.dispatcher);
        return;
      }

      if (this.props.inputSession) {
        ImportExportActions.exportSession(service.get('type'), Format.CSV, this.props.game, this.props.inputSession)
          .then(() => {
            MessageActions.success(`You have been emailed a ${service.get('displayName')} CSV`, this.props.dispatcher);
            this.props.onClose();
          })
          .catch((err) => {
            err && console.error(err);
            MessageActions.error(
              `Could not export ${service.get('displayName')}. Please contact us if this error persists.`,
              this.props.dispatcher,
            );
          });
      } else {
        ImportExportActions.exportCards(service.get('type'), Format.CSV, true, this.props.game, this.props.sourceType)
          .then(() => {
            MessageActions.success(`You have been emailed a ${service.get('displayName')} CSV`, this.props.dispatcher);
            this.props.onClose();
          })
          .catch((err) => {
            MessageActions.error(
              `Could not export ${service.get('displayName')}. Please contact us if this error persists.`,
              this.props.dispatcher,
              err,
            );
          });
      }
    }
  },
);
