import * as React from 'react';
import Select, { components, OptionsType } from 'react-select';
import * as CardListActions from '../../../actions/CardListActions';
import * as MessageActions from '../../../actions/MessageActions';
import * as HasCardLists from '../../../containers/HasCardLists';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { TextFormat } from '../../../helpers/fmt';
import { CardList } from '../../../models/CardList';
import { gameName } from '../../../models/Game';
import { AnyInputSession } from '../../../models/InputSession';
import { CardListSorting } from '../../../models/sorting/CardListSorting';
import { Subscription, SubscriptionType } from '../../../models/Subscriptions';
import { CardListOption } from '../../components/CardListSuggestionItem';
import { ClearFilterButton } from '../../components/filters/ClearFilterButton';
import { FilterTitle } from '../../components/filters/FilterTitle';
import { SimpleNoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';
import { SendButtons } from './SendButtons';

interface IProps {
  dispatcher: Dispatcher;
  subscription: Subscription;
  isMerchant: boolean;
  inputSession: AnyInputSession;
  onClose: () => void;
}

interface IState {
  loaded: boolean;
  cardList?: CardList;
  newCardListName?: string;
}

export const SendToCardList = HasCardLists.Attach<IProps>(
  class extends React.Component<IProps & HasCardLists.IProps, IState> {
    /**
     * @override
     * @constructor
     */
    constructor(props: IProps & HasCardLists.IProps) {
      super(props);
      this.state = {
        cardList: undefined,
        newCardListName: undefined,
        loaded: false,
      };
    }
    private SingleValueCard(props: any) {
      return (
        <components.SingleValue {...props}>
          <div className="text__suggestion">
            <div className="react-select__suggestion__label">
              <div className="react-select__suggestion__label__value">{props.data.label}</div>
            </div>
          </div>
        </components.SingleValue>
      );
    }

    async componentDidMount() {
      await CardListActions.all(CardListSorting.NAME_ASC, this.props.dispatcher);
      this.setState({
        loaded: true,
      });
    }

    render() {
      let placeholder = 'Select...';
      if (!this.state.loaded) {
        placeholder = 'Loading...';
      }

      if (this.props.isMerchant) {
        return (
          <>
            <div className="centered-row">
              <div className="paragraph-sm" style={{ maxWidth: '50rem' }}>
                {`Create a list from ${TextFormat.quantityOf(
                  `${gameName(this.props.inputSession.get('game'))} card`,
                  this.props.inputSession.get('totalCount'),
                  false,
                )}`}
              </div>
            </div>
            <div className="centered-row">
              <div className="col-lg-6 col-md-8 col-xs-10" style={{ textAlign: 'left' }}>
                <FilterTitle name="Card List">
                  <ClearFilterButton
                    label="Clear"
                    onClick={() => {
                      this.onSelectCardList(undefined);
                    }}
                  />
                </FilterTitle>
                <div style={{ cursor: 'text' }}>
                  <Select
                    components={{
                      SingleValue: this.SingleValueCard,
                      NoOptionsMessage: SimpleNoOptionsMessage,
                    }}
                    formatOptionLabel={CardListOption.formatOptionLabel}
                    isDisabled={!this.state.loaded || !this.props.subscription.isMerchant()}
                    placeholder={placeholder}
                    styles={styleOverride}
                    className={'react-select-thin'}
                    classNamePrefix="react-select-thin"
                    options={this.cardListOptions()}
                    handleInputChange={this.onSelectCardList.bind(this)}
                    onChange={this.onChange.bind(this)}
                    value={(this.state.cardList && new CardListOption(this.state.cardList)) || null} // Using 'undefined' will not clear the value
                    defaultOptions
                  />
                </div>
              </div>
            </div>
            {this.state.cardList !== undefined && this.state.cardList.get('uuid') === 'newCardList' ? (
              <div className="centered-row">
                <div className="col-xs-9" style={{ marginTop: '1rem', textAlign: 'left' }}>
                  <div className="info-heading">Name</div>
                  <div>
                    <input
                      className="advanced-search-input"
                      style={{ minHeight: '50px' }}
                      type="text"
                      placeholder="Untitled List"
                      value={this.state.newCardListName ? this.state.newCardListName : ''}
                      onChange={this.onChangeCardListName.bind(this)}
                    />
                  </div>
                </div>
              </div>
            ) : null}
            <div className="centered-row">
              <SendButtons
                isMerchant={this.props.isMerchant}
                onClickSend={this.onClickSend.bind(this)}
                onClose={this.props.onClose}
              />
            </div>
          </>
        );
      } else {
        return (
          <>
            <div className="centered-row">
              <div className="paragraph-sm" style={{ maxWidth: '50rem' }}>
                Card lists are only available with a Merchant account. Upgrade to enable this feature.
              </div>
            </div>
            <div className="centered-row">
              <SendButtons
                isMerchant={this.props.isMerchant}
                onClickSend={this.onClickSend.bind(this)}
                onClose={this.props.onClose}
              />
            </div>
          </>
        );
      }
    }

    private cardListOptions(): OptionsType<CardListOption> {
      const additionalOptions: CardListOption[] = [
        {
          label: 'Create Card List',
          value: 'newCardList',
          cardCount: 0,
          icon: true,
        },
      ];
      return additionalOptions.concat(
        this.props.cardLists
          .valueSeq()
          .map((cardList: CardList) => {
            return new CardListOption(cardList);
          })
          .toArray(),
      );
    }

    private onChange(cardListOption: CardListOption) {
      // We have to check for null instead of undefined due to a quirk of react-select
      this.onSelectCardList(cardListOption === null ? undefined : CardList.fromCardListOption(cardListOption));
    }
    private onChangeCardListName(evt: React.SyntheticEvent<HTMLInputElement>) {
      this.setState({ newCardListName: evt.currentTarget.value });
    }

    private async onClickSend() {
      let cardListName = '';
      if (this.state.cardList === undefined) {
        return;
      } else if (this.state.cardList.get('uuid') === 'newCardList') {
        const cardListPage = await CardListActions.create(
          this.state.newCardListName ? this.state.newCardListName : 'Untitled List',
          '',
          this.props.dispatcher,
        );
        await CardListActions.commit(
          cardListPage.get('cardList').get('uuid'),
          this.props.inputSession.get('uuid'),
          this.props.dispatcher,
        );
        cardListName = cardListPage.get('cardList').get('name');
      } else {
        await CardListActions.commit(
          this.state.cardList.get('uuid'),
          this.props.inputSession.get('uuid'),
          this.props.dispatcher,
        );
        cardListName = this.props.cardLists.get(this.state.cardList.get('uuid')).get('name');
      }
      MessageActions.success(`Cards successfully added to ${cardListName}`, this.props.dispatcher);
      this.props.onClose();
    }

    private onSelectCardList(cardList?: CardList) {
      if (
        this.props.subscription.get('type') === SubscriptionType.MERCHANT ||
        this.props.subscription.get('type') === SubscriptionType.MERCHANT_PROMOTIONAL
      ) {
        this.setState({
          cardList: cardList,
        });
      }
    }
  },
);
