import * as React from 'react';
import history from '../../../helpers/history';

interface IProps {
  isMerchant: boolean;
  onClickSend: () => void;
  onClose: () => void;
  disabled?: boolean;
}

export const SendButtons = (props: IProps) => {
  function onClickUpgrade(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    history.push('/settings/subscription');
  }

  if (props.isMerchant) {
    return (
      <div className="centered-row flex" style={{ gap: '1rem' }}>
        <button className="button-primary" onClick={() => props.onClickSend()}>
          Send
        </button>
        <button className="button-grey" onClick={() => props.onClose()}>
          Cancel
        </button>
      </div>
    );
  } else {
    return (
      <div className="centered-row flex" style={{ gap: '1rem' }}>
        <button className="button-primary" onClick={onClickUpgrade}>
          Upgrade
        </button>
        <button className="button-grey" onClick={() => props.onClose()}>
          Close
        </button>
      </div>
    );
  }
};
