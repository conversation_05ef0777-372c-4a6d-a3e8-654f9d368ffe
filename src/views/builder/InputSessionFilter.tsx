import apps from '@iconify/icons-ic/baseline-apps';
import offline_bolt from '@iconify/icons-ic/baseline-offline-bolt';
import plus from '@iconify/icons-ic/baseline-plus';
import workspaces from '@iconify/icons-ic/baseline-workspaces';
import clear from '@iconify/icons-ic/clear';
import refresh from '@iconify/icons-ic/refresh';
import * as React from 'react';
import Dispatcher from '../../dispatcher/Dispatcher';
import { SelectValue } from '../../helpers/select';
import { Game } from '../../models/Game';
import { InputSource, InputSourceType, type InputSourceTypeKey } from '../../models/InputSource';
import { SessionFilter } from '../../stores/InputSessionStore';
import { SecondaryButton } from '../components/buttons/SecondaryButton';
import { Button, ButtonClass } from '../components/filters/UpdateFiltersButton';
import { FormLabel } from '../components/FormLabel';
import { IconSelect } from '../components/IconSelect';
import { GameSelector } from '../components/selectors/GameSelector';

interface IProps {
  dispatcher: Dispatcher;
  loading: boolean;
  currentIndex: number;
  totalSessions: number;
  filters: SessionFilter;
  createNewSession: (evt: React.SyntheticEvent<HTMLElement>) => void;
  onSubmit: (filters: SessionFilter) => void;
  onDeleteAllSessions: () => void;
  onCommitAllSessions: () => void;
  onActionAllSessions: () => void;
}

export const InputSessionFilter = (props: IProps) => {
  const [searchQuery, setSearchQuery] = React.useState<string | undefined>(props.filters.get('query'));
  const [game, setGame] = React.useState<Game>(props.filters.get('game'));
  const [sourceType, setSourceType] = React.useState<SelectValue<InputSourceType>>(props.filters.get('sourceType'));

  function onSubmit(evt: React.SyntheticEvent<HTMLInputElement>) {
    evt.preventDefault();
    const newFilters = props.filters.set('query', searchQuery);
    props.onSubmit(newFilters);
  }

  function updateQuery(evt: React.SyntheticEvent<HTMLInputElement>) {
    evt.preventDefault();
    setSearchQuery(evt.currentTarget.value);
  }

  function updateSourceType(evt: React.SyntheticEvent<HTMLInputElement>) {
    evt.preventDefault();
    const newSourceType = evt.currentTarget.value as SelectValue<InputSourceType>;
    setSourceType(newSourceType);
    const newFilters = props.filters
      .set('query', searchQuery)
      .set('sourceType', evt.currentTarget.value as SelectValue<InputSourceType>);
    props.onSubmit(newFilters);
  }

  function updateGame(game: Game) {
    setGame(game);
    const newFilters = props.filters.set('query', searchQuery).set('game', game);
    props.onSubmit(newFilters);
  }

  // Reset the internal state to the props when it changes
  React.useEffect(() => {
    setSearchQuery(props.filters.get('query'));
    setGame(props.filters.get('game'));
    setSourceType(props.filters.get('sourceType'));
  }, [props.filters]);

  return (
    <>
      <div style={{ marginTop: '1rem', padding: 0 }}>
        <div className="row">
          <div className="col-xs-12 col-sm-12 col-md-12 col-lg-6 col-xl-4">
            <form onSubmit={onSubmit.bind(this)}>
              <div className="filter-no-padding col-xs-12">
                <FormLabel heading="Source Name" />
                <input
                  value={searchQuery || ''}
                  className="advanced-search-input"
                  type="text"
                  placeholder="Search sessions by source name"
                  onChange={updateQuery.bind(this)}
                />
              </div>
            </form>
          </div>
          <div className="col-xs-12 col-sm-6 col-md-6 col-lg-3 col-xl-2">
            <div className="filter-no-padding col-xs-12">
              <FormLabel heading="Type" />
              <IconSelect
                className={'icon-container--card-input'}
                icon={workspaces}
                value={sourceType}
                onChange={updateSourceType.bind(this)}
              >
                <>
                  <option disabled>Source</option>
                  {/* 'inactive' is used here to get around the undefined option issue */}
                  <option value={'inactive'}>Any</option>
                  {Object.keys(InputSourceType).map((key) => (
                    <option key={key} value={InputSourceType[key as InputSourceTypeKey]}>
                      {InputSource.typeToString(InputSourceType[key as InputSourceTypeKey])}
                    </option>
                  ))}
                </>
              </IconSelect>
            </div>
          </div>
          <div className="col-xs-12 col-sm-6 col-md-6 col-lg-3 col-xl-2">
            <GameSelector game={game} onChange={updateGame.bind(this)} />
          </div>
          <div className="col-xs-12 col-lg-12 col-xl-4 flex justify-end" style={{ marginTop: '2rem' }}>
            <div className="flex vertical">
              <div className="flex justify-end" style={{ gap: '1rem' }}>
                <Button
                  class={ButtonClass.PRIMARY}
                  icon={refresh}
                  title="Refresh"
                  onClick={onSubmit.bind(this)}
                  disabled={props.loading}
                />
                {props.filters.get('game') == Game.MTG && (
                  <Button
                    class={ButtonClass.GREEN}
                    icon={offline_bolt}
                    title="Quick Add"
                    onClick={props.createNewSession}
                    disabled={props.loading}
                  />
                )}
              </div>
              <div className="flex" style={{ margin: '0.5rem 0 0 auto' }}>
                {props.filters.get('game') == Game.MTG && (
                  <SecondaryButton
                    icon={plus}
                    text="Commit All"
                    onClick={() => props.onCommitAllSessions()}
                    disabled={props.loading}
                  />
                )}
                <SecondaryButton
                  icon={apps}
                  text="Action All"
                  onClick={() => props.onActionAllSessions()}
                  disabled={props.loading}
                />
                <SecondaryButton
                  icon={clear}
                  text="Clear All"
                  onClick={() => props.onDeleteAllSessions()}
                  disabled={props.loading}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
