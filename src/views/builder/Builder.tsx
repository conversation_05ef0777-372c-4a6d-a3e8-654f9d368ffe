import * as Immutable from 'immutable';
import * as React from 'react';
import { Helmet } from 'react-helmet';
import { match } from 'react-router';
import * as MessageActions from '../../actions/MessageActions';
import * as SubscriptionActions from '../../actions/SubscriptionActions';
import * as HasCardLists from '../../containers/HasCardLists';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import * as HasSubscription from '../../containers/HasSubscription';
import Dispatcher from '../../dispatcher/Dispatcher';
import { EnumHelper } from '../../helpers/enum';
import { TextFormat } from '../../helpers/fmt';
import history from '../../helpers/history';
import isServerside from '../../helpers/serverside';
import { TabItem } from '../../models/TabItem';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';
import SecondaryNavBar from '../components/SecondaryNavBar';
import { BuilderCardLists } from './BuilderCardLists';
import BuilderChangelog from './BuilderChangelog';
import { BuilderImportExport } from './BuilderImportExport';
import BuilderManual from './BuilderManual';
import BuilderScan from './BuilderScan';

interface IProps {
  location?: {
    search?: string;
  };
  match?: match<BuilderParams>;
}

interface BuilderParams {
  page: string;
}

interface IState {
  page?: BuilderPage;
}

enum BuilderPage {
  STAGING = 'staging',
  IMPORT_EXPORT = 'import-export',
  SCAN = 'scan',
  ACTIVITY_LOG = 'activity-log',
  CARD_LISTS = 'card-lists',
}

export default function (dispatcher: Dispatcher) {
  class Builder extends React.Component<
    HasDispatcher.IProps & HasMe.IProps & HasSubscription.IProps & HasCardLists.IProps & IProps,
    IState
  > {
    constructor(props: HasDispatcher.IProps & HasMe.IProps & HasSubscription.IProps & HasCardLists.IProps & IProps) {
      super(props);

      // Validate page
      const propsPage = (this.props.match && this.props.match.params.page) || BuilderPage.STAGING;
      const page = EnumHelper.match(BuilderPage, propsPage) as BuilderPage;

      this.state = {
        page: page,
      };
    }

    public componentDidMount() {
      if (this.state.page === undefined) {
        history.push('/404');
      }
      SubscriptionActions.subscription(this.props.dispatcher);
      if (this.props.location) {
        const params = new URLSearchParams(this.props.location.search);
        if (params.get('from_email') === 'true') {
          MessageActions.info('Your imported cards can be added to your collection below.', this.props.dispatcher);
        }
      }
      if (this.props.cardListPage) {
        const cardListUUID = this.props.cardListPage.get('cardList').get('uuid');
        if (cardListUUID !== '' && this.state.page !== BuilderPage.CARD_LISTS) {
          history.push(`/builder/card-lists/${cardListUUID}`);
        }
      }
    }

    public render() {
      if (isServerside()) {
        Helmet.renderStatic();
      }
      const secondaryPages = Immutable.List<TabItem>([
        new TabItem({ title: BuilderPage.STAGING }),
        new TabItem({ title: BuilderPage.IMPORT_EXPORT }),
        new TabItem({ title: BuilderPage.SCAN }),
        new TabItem({ title: BuilderPage.ACTIVITY_LOG }),
        new TabItem({ title: BuilderPage.CARD_LISTS }),
      ]);

      return (
        <div>
          <Helmet>
            <title>{`CardCastle: ${
              this.state.page ? this.pageName(this.state.page) : this.pageName(BuilderPage.STAGING)
            }`}</title>
          </Helmet>
          <Navbar dispatcher={this.props.dispatcher} me={this.props.me} selection="build" isRibbon={true} />
          <div className="section has-footer has-navbar">
            <div className="container-fluid">
              <div className="center-row">
                <SecondaryNavBar
                  pages={secondaryPages}
                  selectedPage={this.state.page || BuilderPage.STAGING}
                  displayName={this.pageName}
                  openPage={this.openPage}
                />
              </div>
            </div>
            <div className="col-xs-12">
              {this.state.page === BuilderPage.STAGING ? <BuilderManual dispatcher={this.props.dispatcher} /> : null}
              {this.state.page === BuilderPage.IMPORT_EXPORT ? (
                <BuilderImportExport dispatcher={this.props.dispatcher} me={this.props.me} />
              ) : null}
              {this.state.page === BuilderPage.SCAN ? <BuilderScan dispatcher={this.props.dispatcher} /> : null}
              {this.state.page === BuilderPage.ACTIVITY_LOG ? (
                <BuilderChangelog dispatcher={this.props.dispatcher} me={this.props.me} />
              ) : null}
              {this.state.page === BuilderPage.CARD_LISTS ? (
                <BuilderCardLists
                  dispatcher={this.props.dispatcher}
                  me={this.props.me}
                  cardLists={this.props.cardLists}
                  cardListSorting={this.props.cardListSorting}
                  subscriptionType={this.props.subscription.get('type')}
                />
              ) : null}
            </div>
          </div>

          <Footer />
        </div>
      );
    }

    private openPage = (page: string) => {
      // Find corresponding page
      const match = EnumHelper.match(BuilderPage, page) as BuilderPage;

      if (match) {
        history.push('/builder/' + match);
        this.setState({
          page: match,
        });
      }
    };

    private pageName = (builderPage: string) => {
      const match = EnumHelper.match(BuilderPage, builderPage) as BuilderPage;
      switch (match) {
        case BuilderPage.IMPORT_EXPORT:
          return 'Import / Export';
        case undefined:
          return '';
        default:
          return TextFormat.capitalize(match.split('-').join(' '));
      }
    };
  }

  return HasDispatcher.Attach<IProps>(
    HasMe.Attach<HasDispatcher.IProps & IProps>(
      HasSubscription.Attach<HasDispatcher.IProps & HasMe.IProps & IProps>(
        HasCardLists.Attach<HasDispatcher.IProps & HasMe.IProps & HasSubscription.IProps & IProps>(Builder),
      ),
    ),
    dispatcher,
  );
}
