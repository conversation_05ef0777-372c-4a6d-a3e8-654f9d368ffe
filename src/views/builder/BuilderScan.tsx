import * as React from 'react';
import Dispatcher from '../../dispatcher/Dispatcher';

interface IProps {
  dispatcher: Dispatcher;
}

interface IState {}

export default class extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  public render() {
    return (
      <div className="container-fluid">
        <section className="section">
          <div className="flex justify-center row">
            <div className="flex justify-end col-xs-12 col-md-5 col-xl-4">
              <img className="apps-device-image small" src="/images/apps-iphone.png" />
            </div>
            <div className="flex justify-start col-md-5 col-xl-4 visible-md">
              <img className="apps-device-image small" src="/images/apps-android.png" />
            </div>
          </div>
          <p className="paragraph-sm is-centered">
            Use your phone to quickly and effortlessly construct a digital reflection of your physical card collection,
            a browsable and manageable catalogue on the CardCastle Web Platform.
          </p>
          <h2 className="heading-md is-centered">
            Download on iOS and Android for <i>free</i> today!
          </h2>
          <div className="flex justify-center">
            <a className="button-primary" href="/apps" target="_blank">
              Download
            </a>
          </div>
        </section>
      </div>
    );
  }
}
