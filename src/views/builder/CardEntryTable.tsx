// npm
import clear from '@iconify/icons-ic/clear';
// Iconify
import { Icon } from '@iconify/react';
import * as Immutable from 'immutable';
import * as React from 'react';
// Dispatcher
import Dispatcher from '../../dispatcher/Dispatcher';
// Helpers
import displayCurrency from '../../helpers/currency_helper';
import { CardInstance, ResolveGroupValue, ResolvePrice } from '../../models/CardInstances';
// Models
import { CellBorders } from '../../models/CellBorder';
import { Foil } from '../../models/Foil';
import { Grouping } from '../../models/Grouping';
import { Language, LanguageName } from '../../models/Language';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { Tag } from '../../models/Tags';
import { User } from '../../models/Users';
// Components
import * as Table from '../components/Table';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  cardGroups: Immutable.OrderedMap<string, MTGCardGroup>;
  userTags: Immutable.OrderedSet<Tag>;
  languageOptions: Immutable.Map<string, Immutable.List<Language>>;
  remove: (cardGroupID: string) => void;
  removeAll: (evt: React.SyntheticEvent<HTMLElement>) => void;
}

interface IState {
  deleteAllConfirmation: boolean;
}

export const CardEntryTable = class extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      deleteAllConfirmation: false,
    };
  }

  private confirmDelete(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({ deleteAllConfirmation: true });
  }

  private removeCancel(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({ deleteAllConfirmation: false });
  }

  public render() {
    const indexedCardGroups = this.props.cardGroups.toIndexedSeq().reverse();
    return (
      <div className="col-xs-12" style={{ margin: '0 auto' }}>
        {this.state.deleteAllConfirmation ? (
          <div className="card-entry-tooltip-container">
            <div className="card-entry-tooltip">
              <div className="filter-tooltip-hover-text">Are you sure you want to remove all cards?</div>
              <div className="filter-tooltip-hover-text flex" style={{ justifyContent: 'center' }}>
                <div onClick={this.props.removeAll.bind(this)} style={{ cursor: 'pointer', marginRight: '1rem' }}>
                  Yes
                </div>
                <div onClick={this.removeCancel.bind(this)} style={{ cursor: 'pointer' }}>
                  No
                </div>
              </div>
            </div>
          </div>
        ) : null}
        <div
          className="card-entry-table"
          style={{ margin: '1rem auto', height: this.heightCalculation(this.props.cardGroups.size) }}
        >
          {['Count', 'Card', 'Foil', 'Condition', 'Language', 'Price'].map((label: string) => {
            return (
              <div key={label} className="table__header collection-cell-justification cell-border-left">
                <div className="class-center">{label}</div>
              </div>
            );
          })}
          <div className="table__header collection-cell-justification cell-border-left border-right">
            <div className="class-center">
              <div className="flex align-center justify-center">
                <div style={{ cursor: 'pointer' }} onClick={this.confirmDelete.bind(this)}>
                  <Icon height={'24px'} width={'24px'} icon={clear} />
                </div>
              </div>
            </div>
          </div>
          {indexedCardGroups.map((cardGroup: MTGCardGroup, index: number) => {
            const final = indexedCardGroups.size - 1 === index;
            const cardInstances = cardGroup.get('cardInstances');
            const foil = cardGroup
              .get('cardInstances')
              .reduce(
                (foil: boolean, cardInstance: CardInstance) => foil || cardInstance.get('foil') === Foil.On,
                false,
              );
            const price = ResolvePrice(cardInstances);
            const priceString = price
              ? displayCurrency(price, true, this.props.me.get('preferences').get('localization').get('currency'))
              : '-';

            const condition = ResolveGroupValue(cardInstances, 'condition');
            const resolvedLanguage = ResolveGroupValue(cardInstances, 'language');
            const dark = index % 2 === 0 ? true : false;
            const cardInstance = cardGroup.get('cardInstances').first();
            return (
              <React.Fragment key={index}>
                <Table.AmountCell
                  dark={dark}
                  cellBorders={CellBorders.default(final)}
                  amount={cardGroup.get('cardInstanceCount')}
                  large={true}
                />
                <Table.MTGCardInstanceInfoCell
                  cellStyle={{ dark: dark, cellBorders: CellBorders.default(final), large: true }}
                  cardInstance={cardInstance}
                  grouping={Grouping.PRINTING}
                />
                <Table.CheckCell
                  checked={foil}
                  dark={dark}
                  centerCell={true}
                  cellBorders={CellBorders.default(final)}
                  large={true}
                />
                <Table.TextCell dark={dark} text={condition} cellBorders={CellBorders.default(final)} large={true} />
                <Table.TextCell
                  dark={dark}
                  cellBorders={CellBorders.default(final)}
                  text={LanguageName(resolvedLanguage as Language)}
                  large={true}
                />
                <Table.TextCell dark={dark} cellBorders={CellBorders.default(final)} text={priceString} large={true} />
                <Table.MaterialIcon
                  dark={dark}
                  centerCell={true}
                  materialIconText={clear}
                  large={true}
                  cellBorders={CellBorders.rightCell(final)}
                  onClick={() => {
                    this.props.remove(cardInstance.get('cardJsonID') + (foil ? 1 : 0) + condition + resolvedLanguage);
                  }}
                />
              </React.Fragment>
            );
          })}
        </div>
      </div>
    );
  }

  private heightCalculation(cardCount: number): string {
    switch (cardCount) {
      case 1:
        return '10rem';
      case 2:
        return '16.25rem';
      case 3:
        return '22.75rem';
      case 4:
        return '29.25rem';
      default:
        return 'unset';
    }
  }
};
