import plus from '@iconify/icons-ic/baseline-plus';
import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../dispatcher/Dispatcher';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { Button, ButtonClass } from '../components/filters/UpdateFiltersButton';
import { DialogStatus } from './BuilderManual';
import { CardEntryDialog } from './CardEntryDialog';

interface IProps {
  dispatcher: Dispatcher;
  currentStatus: DialogStatus;
  cardGroups: Immutable.OrderedMap<string, MTGCardGroup>;
  updateCardGroups: (cardGroups: Immutable.OrderedMap<string, MTGCardGroup>) => void;
  onSubmit: () => void;
  onDismiss: () => void;
}

// This class exists because cardGroups for quick add need to be accessible to BuilderManual.tsx
// so it can clear the cardGroups when a session is quick added to Collection.
export function QuickAddDialog(props: IProps) {
  return (
    <CardEntryDialog
      dispatcher={props.dispatcher}
      targetStatus={DialogStatus.QUICK_ADD}
      title={'Add Cards to Collection'}
      subtitle={''}
      currentStatus={props.currentStatus}
      onDismiss={props.onDismiss}
      cardGroups={props.cardGroups}
      updateCardGroups={props.updateCardGroups}
    >
      <div className="col-xs-12 flex justify-center" style={{ marginTop: '1rem', gap: '1rem' }}>
        <Button
          class={ButtonClass.PRIMARY}
          disabled={props.cardGroups.size === 0}
          icon={plus}
          title="Commit to Collection"
          onClick={props.onSubmit}
        />
        <Button class={ButtonClass.ALERT} title="Cancel" onClick={props.onDismiss} />
      </div>
    </CardEntryDialog>
  );
}
