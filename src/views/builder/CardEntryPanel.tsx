import plus from '@iconify/icons-ic/baseline-plus';
import * as Immutable from 'immutable';
import * as React from 'react';
import * as InputSessionActions from '../../actions/InputSessionActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import track from '../../helpers/ga_helper';
import { Card } from '../../models/Cards';
import { Condition } from '../../models/Condition';
import { Foil } from '../../models/Foil';
import { AllLanguages, Language } from '../../models/Language';
import { PricingSource } from '../../models/PricingSource';
import CardSearcher from '../components/CardSearcher';
import { MTGCardSearcher } from '../components/cardSearcher/MTGCardSearcher';
import { Checkbox } from '../components/Checkbox';
import { Button, ButtonClass } from '../components/filters/UpdateFiltersButton';
import { ConditionSelector } from '../components/selectors/ConditionSelector';
import { LanguageSelector } from '../components/selectors/LanguageSelector';

interface IProps {
  dispatcher: Dispatcher;
  pricingSource: PricingSource;
  defaultCondition: Condition;
  addCards: (card: Card, quantity: number, foil: Foil, condition: Condition, language: Language) => void;
}

interface IState {
  condition: Condition;
  foil: Foil;
  language: Language;
  count?: number;
  chosenCard?: Card;
  suggestions?: Immutable.List<Card>;
  suggestionsIndex?: number;
}

export default class CardEntryPanel extends React.Component<IProps, IState> {
  private readonly cardSearcher: React.RefObject<CardSearcher>;

  constructor(props: IProps) {
    super(props);
    this.cardSearcher = React.createRef<CardSearcher>();
    this.state = {
      condition: Condition.NEAR_MINT,
      foil: Foil.Off,
      count: 1,
      language: Language.ENGLISH,
    };
  }

  componentDidMount() {
    this.setState({ condition: this.props.defaultCondition });
  }

  componentDidUpdate(prevProps: IProps) {
    if (this.props.defaultCondition !== prevProps.defaultCondition) {
      this.setState({ condition: this.props.defaultCondition });
    }
  }

  public render() {
    const languageOptions = this.state.chosenCard ? this.state.chosenCard.languageOptions() : AllLanguages();

    return (
      <form style={{ position: 'relative', height: 'fit-content' }}>
        <div className="row">
          <div className="col-xs-12 col-sm-12 col-md-9 col-lg-10 col-xl-10" style={{ marginBottom: '0.5rem' }}>
            <div className="info-heading">Card Name</div>
            <MTGCardSearcher
              searcherRef={this.cardSearcher}
              dispatcher={this.props.dispatcher}
              chooseSuggestion={this.chooseSuggestion.bind(this)}
              language={this.state.language}
              foil={this.state.foil}
            />
          </div>
          <div className="col-xs-6 col-md-3 col-lg-2 col-xl-2" style={{ marginBottom: '0.5rem' }}>
            <div className="info-heading">Quantity</div>
            <input
              type="number"
              className="card-number-input input is-number--small"
              value={this.state.count}
              min="1"
              max="100"
              onChange={this.onChangeCount.bind(this)}
              onBlur={this.onBlurCount.bind(this)}
            />
          </div>
          <div className="col-xs-6 col-sm-3 col-md-1 col-lg-1 col-xl-1">
            <div className="info-heading">Foil</div>
            <Checkbox checked={this.state.foil === Foil.On} onChange={this.onChangeFoil.bind(this)} />
          </div>
          <div className="col-xs-12 col-sm-10 col-md-5 col-lg-4 col-xl-3">
            <div className="info-heading">Condition</div>
            <ConditionSelector
              condition={this.state.condition}
              onChange={this.onChangeCondition.bind(this)}
              hideTitle={true}
            />
          </div>
          <div className="col-xs-12 col-sm-10 col-md-5 col-lg-4 col-xl-3">
            <div className="info-heading">Language</div>
            <LanguageSelector
              onChange={this.onChangeLanguage.bind(this)}
              language={this.state.language}
              options={languageOptions}
              hideTitle={true}
            />
          </div>
          <div className="flex justify-end col-xs-12 col-sm-12 col-md-12 flex col-lg-3 col-xl-5 add-card-container">
            <Button class={ButtonClass.PRIMARY} icon={plus} title="Add" onClick={this.onClickAdd.bind(this)} />
          </div>
        </div>
      </form>
    );
  }

  private onChangeCondition(condition: Condition) {
    this.setState({ condition: condition });
  }

  private onChangeFoil(foil: boolean) {
    this.setState({ foil: foil ? Foil.On : Foil.Off });
  }

  private onChangeLanguage(language: Language) {
    this.setState({ language: language });
  }

  private onClickAdd(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    // Check for missing card
    if (!this.state.chosenCard) {
      return;
    }

    if (this.state.count !== undefined) {
      track('collection', 'add', this.state.foil ? 'foil' : 'non-foil', this.state.count);
      InputSessionActions.closeCardPanel(this.props.dispatcher);
      this.props.addCards(
        this.state.chosenCard!,
        this.state.count,
        this.state.foil,
        this.state.condition,
        this.state.language!,
      );
    }
    if (this.cardSearcher.current) {
      this.cardSearcher.current.focus();
    }
  }

  private chooseSuggestion(suggestion?: Card) {
    if (!suggestion) {
      this.setState({
        chosenCard: undefined,
      });
    } else {
      const languageSet = suggestion.languageOptions();

      // Attempt to retain language selection from last card, otherwise default to english
      let language = Language.ENGLISH;
      if (languageSet.contains(this.state.language!)) {
        language = this.state.language!;
      } else if (!languageSet.contains(Language.ENGLISH)) {
        // Rare case: If card is not printed in English, use its first language
        language = languageSet.first() || Language.ENGLISH;
      }

      this.setState({
        chosenCard: suggestion,
        suggestions: Immutable.List(),
        suggestionsIndex: -1,
        language: language,
      });
    }
  }

  private onChangeCount(evt: React.SyntheticEvent<HTMLInputElement>) {
    if (evt.currentTarget.value !== '') {
      this.setState({
        count: Number(evt.currentTarget.value),
      });
    } else {
      this.setState({
        count: undefined,
      });
    }
  }

  private onBlurCount(evt: React.SyntheticEvent<HTMLInputElement>) {
    evt.preventDefault();
    // Clamp new count value between 0 and 100
    if (evt.currentTarget.value !== undefined) {
      this.setState({
        count: Math.min(Math.max(Number(evt.currentTarget.value), 1), 100),
      });
    } else {
      this.setState({
        count: undefined,
      });
    }
  }
}
