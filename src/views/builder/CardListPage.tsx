import playlist_add from '@iconify/icons-ic/baseline-playlist-add';
import plus from '@iconify/icons-ic/baseline-plus';
import search from '@iconify/icons-ic/baseline-search';
import clear from '@iconify/icons-ic/clear';
import { Icon } from '@iconify/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import Helmet from 'react-helmet';
import { match } from 'react-router-dom';
import * as CardListActions from '../../actions/CardListActions';
import * as MessageActions from '../../actions/MessageActions';
import * as HasCardLists from '../../containers/HasCardLists';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import { EnumHelper } from '../../helpers/enum';
import { DateFormat } from '../../helpers/fmt';
import history from '../../helpers/history';
import isServerside from '../../helpers/serverside';
import { CardListPage as CardListPageData, ListedCard } from '../../models/CardList';
import { Card } from '../../models/Cards';
import { CellBorders } from '../../models/CellBorder';
import { Grouping } from '../../models/Grouping';
import { ListedCardSorting } from '../../models/sorting/ListedCardSorting';
import { TabItem } from '../../models/TabItem';
import { MTGCardSearcher } from '../components/cardSearcher/MTGCardSearcher';
import { Checkbox } from '../components/Checkbox';
import { EditableTitle } from '../components/EditableTitle';
import { Button, ButtonClass } from '../components/filters/UpdateFiltersButton';
import { Footer } from '../components/Footer';
import { FormLabel } from '../components/FormLabel';
import { Navbar } from '../components/Navbar';
import { Paginator } from '../components/Paginator';
import { Placeholder } from '../components/Placeholder';
import { SecondaryIconType, SecondaryNavButton } from '../components/SecondaryNavButton';
import { ListedCardSortingSelector } from '../components/selectors/ListedCardSortingSelector';
import { SplitDivider } from '../components/SplitDivider';
import { TabBar } from '../components/TabBar';
import * as Table from '../components/Table';

interface IProps {
  match?: match<CardListParams>;
}

interface CardListParams {
  id: string;
}

interface IState {
  chosenCard?: Card;
  sorting: ListedCardSorting;
  currentTab: CardListTab;
  searchQuery: string;
  loading: boolean;
  allPrintings: boolean;
}

enum CardListTab {
  ADD_CARDS = 'Add Cards',
  SEARCH_CARDS = 'Search Cards',
}

export const CardListPage = HasMe.Attach<HasDispatcher.IProps & IProps>(
  HasCardLists.Attach<HasDispatcher.IProps & HasMe.IProps & IProps>(
    class extends React.Component<HasDispatcher.IProps & HasMe.IProps & HasCardLists.IProps & IProps, IState> {
      constructor(props: HasDispatcher.IProps & HasMe.IProps & HasCardLists.IProps & IProps) {
        super(props);
        this.state = {
          sorting: ListedCardSorting.DATE_ADDED_DESC,
          currentTab: CardListTab.ADD_CARDS,
          searchQuery: '',
          loading: true,
          allPrintings: false,
        };
      }

      /**
       * @override
       */
      componentDidMount() {
        const uuid = this.getUUID();
        if (uuid === undefined) {
          history.push('/404');
        } else {
          this.loadList(uuid);
        }
      }

      render() {
        if (isServerside()) {
          Helmet.renderStatic();
        }
        const cardListPageData = this.props.cardListPage;
        const cardList = cardListPageData ? cardListPageData.get('cardList') : cardListPageData;
        const cardListTabs = Immutable.List<TabItem>([
          new TabItem({ title: CardListTab.ADD_CARDS }),
          new TabItem({ title: CardListTab.SEARCH_CARDS }),
        ]);

        return (
          <div>
            <Helmet>
              <title>{`CardCastle: ${cardList ? cardList.get('name') : 'Loading...'}`}</title>
            </Helmet>
            <Navbar dispatcher={this.props.dispatcher} me={this.props.me} selection="build" isRibbon={true} />
            <div className="section has-footer has-navbar">
              <div className="container-fluid">
                <div className="col-xs-12" style={{ marginBottom: '2rem' }}>
                  <div style={{ minWidth: '120px', width: '10%' }}>
                    <SecondaryNavButton
                      href={'Back'}
                      iconType={SecondaryIconType.CHEVRON_LEFT}
                      onClick={this.onClickBack}
                      detached={true}
                    />
                  </div>
                </div>
                <EditableTitle
                  title={cardList ? cardList.get('name') : 'Loading...'}
                  onSubmitTitle={this.onSubmitName.bind(this)}
                  maxCharacter={100}
                />
                <EditableTitle
                  title={cardList ? cardList.get('description') : ''}
                  subtitle={true}
                  onSubmitTitle={this.onSubmitDescription.bind(this)}
                  emptyAllowed={true}
                  placeholder="Add a description"
                  isLoading={cardList === undefined}
                />
                <div className="row" style={{ marginTop: '1rem' }}>
                  <SplitDivider>
                    <TabBar
                      pages={cardListTabs}
                      selectedTab={this.state.currentTab}
                      onChangeTab={this.onChangeTab.bind(this)}
                    />
                  </SplitDivider>
                </div>
                <section className="section" style={{ paddingLeft: '0rem' }}>
                  <form style={{ position: 'relative' }}>
                    {this.state.currentTab === CardListTab.SEARCH_CARDS
                      ? this.renderSearchCards()
                      : this.renderAddCards()}
                  </form>
                </section>
                <section className="section" style={{ paddingLeft: '0rem' }}>
                  <div className="offset-lg-9 col-lg-3">
                    <ListedCardSortingSelector
                      sorting={this.state.sorting}
                      onChange={(sorting) => {
                        if (cardList !== undefined) {
                          this.onClickSortingHeader(cardList.get('uuid'), sorting);
                        }
                      }}
                    />
                  </div>
                  {cardListPageData === undefined || this.state.loading ? (
                    <div className="spinner-md" style={{ marginTop: '4rem' }}></div>
                  ) : (
                    this.renderCardLists(cardListPageData as CardListPageData)
                  )}
                </section>
              </div>
              <Footer />
            </div>
          </div>
        );
      }

      private renderSearchCards = () => {
        return (
          <div className="flex" style={{ gap: '1rem', alignItems: 'start', flexWrap: 'wrap' }}>
            <div className="grow-xs-1">
              <div className="info-heading">Search</div>
              <input
                className="advanced-search-input"
                style={{ minHeight: '50px' }}
                ref="advancedSearchInput"
                type="text"
                placeholder="Search cards by name. Filter cards already in your card list."
                value={this.state.searchQuery ? this.state.searchQuery : ''}
                onChange={this.onChangeSearchQuery.bind(this)}
              />
            </div>
            <div style={{ alignSelf: 'end' }}>
              <Button
                class={ButtonClass.PRIMARY}
                icon={search}
                title="Search"
                onClick={this.onSearchListedCards.bind(this)}
              />
            </div>
          </div>
        );
      };

      private renderAddCards = () => {
        return (
          <div className="flex" style={{ gap: '1rem', alignItems: 'start', flexWrap: 'wrap' }}>
            <div className="grow-xs-1">
              <FormLabel heading="Card Name" />
              <MTGCardSearcher dispatcher={this.props.dispatcher} chooseSuggestion={this.chooseSuggestion.bind(this)} />
            </div>
            <div className="grow-xs-1">
              <FormLabel
                heading="All Printings"
                subheading="Add all printings of the selected card"
                addMargin={false}
              />
              <Checkbox checked={this.state.allPrintings} onChange={this.onChangeAllPrintings.bind(this)} />
            </div>
            <div style={{ alignSelf: 'center' }}>
              <Button class={ButtonClass.PRIMARY} icon={plus} title="Add" onClick={this.onClickAddCard.bind(this)} />
            </div>
          </div>
        );
      };

      private renderCardLists = (cardListPage: CardListPageData) => {
        const cardList = cardListPage.get('cardList');
        if (cardListPage.get('totalCards') === 0) {
          const message =
            this.state.currentTab === CardListTab.SEARCH_CARDS && cardListPage.get('query').length > 0
              ? 'No cards found.'
              : "You haven't added any cards to this list. Use the search box above to start adding cards.";
          return <Placeholder message={message} button={null} href="/" />;
        } else {
          return (
            <>
              <div className="listed-card__table" style={{ marginTop: '1rem' }}>
                <Table.MultiSortingHeader
                  title="Card"
                  sorting={this.state.sorting}
                  asc={[ListedCardSorting.NAME_ASC, ListedCardSorting.SET_NAME_ASC]}
                  desc={[ListedCardSorting.NAME_DESC, ListedCardSorting.SET_NAME_DESC]}
                  cellBorders={CellBorders.default(false)}
                />
                <Table.SortingHeader
                  title="Time Added"
                  sorting={this.state.sorting}
                  asc={ListedCardSorting.DATE_ADDED_ASC}
                  desc={ListedCardSorting.DATE_ADDED_DESC}
                  cellBorders={CellBorders.default(false)}
                  onClick={(sorting) => {
                    this.onClickSortingHeader(cardList.get('uuid'), sorting as ListedCardSorting);
                  }}
                />
                <div className="table__header cell-border-left cell-border-right">
                  <Icon height={'24px'} width={'24px'} icon={clear} />
                </div>
                {cardList
                  .get('listedCards')
                  .valueSeq()
                  .map((listedCard: ListedCard, index: number) => {
                    const card = listedCard.get('card');
                    const dark = index % 2 === 0 ? true : false;
                    const final = cardList.get('listedCards').size - 1 === index;
                    return (
                      <React.Fragment key={listedCard.get('card').get('jsonID')}>
                        <Table.MTGCardInfoCell
                          cellStyle={{ dark: dark, cellBorders: CellBorders.default(final), large: true }}
                          card={card}
                          grouping={Grouping.NONE}
                          button={
                            <Button
                              class={ButtonClass.PRIMARY}
                              icon={playlist_add}
                              title="Add All Printings"
                              onClick={(evt) => {
                                evt.preventDefault();
                                this.addAllPrintings(listedCard);
                              }}
                            />
                          }
                        />
                        <Table.TextCell
                          dark={dark}
                          cellBorders={CellBorders.default(final)}
                          large={true}
                          text={DateFormat.human(
                            listedCard.get('createdAt'),
                            this.props.me.get('preferences').get('localization').get('timezone'),
                          )}
                        />
                        <Table.MaterialIcon
                          dark={dark}
                          materialIconText={clear}
                          large={true}
                          onClick={() => {
                            this.onDelete(cardList.get('uuid'), listedCard);
                          }}
                          cellBorders={CellBorders.rightCell(final)}
                        />
                      </React.Fragment>
                    );
                  })}
              </div>
              {cardListPage.get('totalPages') > 1 ? (
                <Paginator
                  page={cardListPage.get('pageNumber')}
                  totalItems={cardListPage.get('totalCards')}
                  onClickPage={(pageNumber: number) => this.reloadList(cardList.get('uuid'), pageNumber)}
                />
              ) : null}
            </>
          );
        }
      };

      private getUUID(): string | undefined {
        return this.props.match && this.props.match.params.id;
      }

      private onClickBack(evt: React.SyntheticEvent<HTMLElement>) {
        evt.preventDefault();
        history.push('/builder/card-lists');
      }

      private onClickAddCard(evt: React.SyntheticEvent<HTMLElement>) {
        evt.preventDefault();
        if (this.state.chosenCard && this.props.cardListPage) {
          const uuid = this.props.cardListPage.get('cardList').get('uuid');
          CardListActions.addListedCard(uuid, this.state.chosenCard, this.state.allPrintings, this.props.dispatcher);
        }
      }

      private addAllPrintings(listedCard: ListedCard) {
        if (this.props.cardListPage) {
          const uuid = this.props.cardListPage.get('cardList').get('uuid');
          CardListActions.addListedCard(uuid, listedCard.get('card'), true, this.props.dispatcher);
        }
      }

      private onDelete(uuid: string, listedCard: ListedCard) {
        CardListActions.removeListedCard(uuid, listedCard, this.props.dispatcher);
      }

      // Seperated from loadList to account for initial load.
      private async reloadList(uuid: string, pageNumber?: number, searchQuery?: string) {
        this.setState({ loading: true });
        await this.loadList(uuid, pageNumber, searchQuery);
      }

      private async loadList(uuid: string, pageNumber?: number, searchQuery?: string) {
        CardListActions.get(uuid, this.state.sorting, this.props.dispatcher, pageNumber, false, searchQuery)
          .then(() => {
            this.setState({ loading: false });
          })
          .catch((err) => {
            console.error('Could not retrieve Card List');
            err && console.error(err);
            MessageActions.error('Could not retrieve Card List', this.props.dispatcher);
          });
      }

      private chooseSuggestion(suggestion?: Card) {
        if (!suggestion) {
          this.setState({
            chosenCard: undefined,
          });
        } else {
          this.setState({
            chosenCard: suggestion,
          });
        }
      }

      private onSubmitName = (newName: string) => {
        if (this.props.cardListPage) {
          const newCardList = this.props.cardListPage.get('cardList').set('name', newName);
          if (newCardList.get('name').length > 100) {
            MessageActions.error('Cannot give card list a name over 100 characters', this.props.dispatcher);
            return;
          }
          CardListActions.update(newCardList, this.props.dispatcher);
        }
      };

      private onSubmitDescription = (newDescription: string) => {
        if (this.props.cardListPage) {
          const newCardList = this.props.cardListPage.get('cardList').set('description', newDescription);
          CardListActions.update(newCardList, this.props.dispatcher);
        }
      };

      private onChangeAllPrintings = (allPrintings: boolean) => {
        this.setState({
          allPrintings: allPrintings,
        });
      };

      private onClickSortingHeader = (uuid: string, sorting: ListedCardSorting) => {
        const newSorting = sorting as ListedCardSorting;
        CardListActions.get(uuid, newSorting, this.props.dispatcher, 1, false).then(() => {
          this.setState({
            sorting: newSorting,
          });
        });
      };

      private onChangeSearchQuery(evt: React.SyntheticEvent<HTMLInputElement>) {
        evt.preventDefault();
        this.setState({
          searchQuery: evt.currentTarget.value,
        });
      }

      private async onSearchListedCards(evt: React.SyntheticEvent<HTMLElement>) {
        evt.preventDefault();
        await CardListActions.get(
          (this.props.cardListPage as CardListPageData).get('cardList').get('uuid'),
          this.state.sorting,
          this.props.dispatcher,
          1,
          false,
          this.state.searchQuery,
        );
      }

      private async onChangeTab(nextTabString: string) {
        const nextTabEnum = EnumHelper.match(CardListTab, nextTabString);
        if (this.state.currentTab !== nextTabEnum) {
          this.setState({
            currentTab: nextTabEnum ? (nextTabEnum as CardListTab) : CardListTab.ADD_CARDS,
          });
          const uuid = (this.props.cardListPage as CardListPageData).get('cardList').get('uuid');
          if (nextTabEnum === CardListTab.SEARCH_CARDS && this.state.searchQuery !== undefined) {
            this.reloadList(uuid, 1, this.state.searchQuery);
          } else {
            this.reloadList(uuid, 1);
          }
        }
      }
    },
  ),
);
