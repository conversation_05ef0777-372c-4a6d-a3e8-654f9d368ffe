import * as Immutable from 'immutable';
import * as React from 'react';
import { components, OptionsType, ValueType } from 'react-select';
import CreatableSelect from 'react-select/creatable';
import Dispatcher from '../../dispatcher/Dispatcher';
import { StackLocation } from '../../models/StackLocation';
import { ClearFilterButton } from '../components/filters/ClearFilterButton';
import { LocationReactSelectInput, styleOverride } from '../shared/ReactSelectHelper';
import { LocationInputOption } from './LocationInputOption';

interface IProps {
  dispatcher: Dispatcher;
  index: number;
  onLoad: (index: number, location?: StackLocation) => Promise<Immutable.List<StackLocation>>;
  remove?: () => void;
  parentLocation?: StackLocation;
  value?: StackLocation;
  update: (location: StackLocation) => void;
  last: boolean;
  clearable: boolean;
  locationError: boolean;
}

interface IState {
  options: Immutable.List<StackLocation>;
}

const SingleValueCard = (props: any) => {
  return <components.SingleValue {...props}>{props.data.label}</components.SingleValue>;
};

export const LocationInput = class extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      options: Immutable.List<StackLocation>(),
    };
  }

  async componentDidMount() {
    this.setState({ options: await this.props.onLoad(this.props.index, this.props.parentLocation) });
  }

  private onCreateOption(inputValue: string) {
    const newLocation = new StackLocation({ id: -1, name: inputValue });
    this.setState({ options: this.state.options.push(newLocation) });
    this.props.update.bind(this)(newLocation);
  }

  // false === excluded, true === included
  private customFilter(option: LocationInputOption, inputValue: string) {
    if (inputValue.includes('/')) {
      return false;
    }
    if (option['data']['__isNew__']) {
      return true;
    }
    return (option['label'] as string).toLowerCase().includes(inputValue.toLowerCase());
  }

  private handleIsValidNewOption = (
    inputValue: string,
    _value: ValueType<LocationInputOption, false>,
    options: OptionsType<LocationInputOption>,
  ) => {
    // Check for the same value --> ASD === ASD ?
    const exactValueExists = options.find((option: LocationInputOption) => option['label'] === inputValue);
    // Check if the value has a valid length.
    // Without this, it will show create option for empty values.
    const valueIsNotEmpty = inputValue.trim().length;
    // If true show create option.
    return !exactValueExists && valueIsNotEmpty;
  };

  private onClear() {
    function myCallback() {
      if (this.props.remove) {
        this.props.remove();
      }
    }

    const newOptions = this.state.options.filterNot((option: StackLocation) => option.get('id') === -1).toList();
    this.setState({ options: newOptions }, myCallback.bind(this)());
  }

  // props is any because react-select's typings are not very good
  NoOptionsMessage = (props: any) => {
    const message =
      // Determines if the input is empty. There might be an easier way to do this.
      // TODO: Investigate if there is an easier way to do this. Not a huge priority.
      // If we ever need to access characters to display what characters are illegal, use props.options
      props.options.length > 0 ? 'Location name cannot include "/"' : 'Create ""';
    return (
      <components.NoOptionsMessage {...props}>
        <div className="react-select__suggestion">{message}</div>
      </components.NoOptionsMessage>
    );
  };

  render() {
    const className = `react-select-thin${this.props.locationError ? '--error' : ''}`;

    return (
      <>
        {this.props.index !== 0 ? (
          <div className="location-node-seperator">
            <span>/</span>
          </div>
        ) : null}
        <div style={{ width: '190px', flexShrink: 0 }}>
          {this.props.remove === undefined ? null : (
            <div className="flex justify-end">
              <ClearFilterButton onClick={this.onClear.bind(this)} label={'Clear'} />
            </div>
          )}
          <div
            style={{
              cursor: 'text',
              marginTop: this.props.clearable ? '' : '1.5rem',
            }}
          >
            <CreatableSelect
              components={{
                SingleValue: SingleValueCard,
                NoOptionsMessage: this.NoOptionsMessage,
                Input: LocationReactSelectInput,
              }}
              formatOptionLabel={LocationInputOption.formatOptionLabel.bind(this)}
              isDisabled={!this.props.last}
              placeholder={''}
              styles={styleOverride}
              className={className}
              classNamePrefix={className}
              options={this.state.options.map((location: StackLocation) => new LocationInputOption(location)).toArray()}
              // value expects null otherwise the option won't be cleared properly
              value={this.props.value === undefined ? null : new LocationInputOption(this.props.value)}
              onChange={(value: LocationInputOption) => this.props.update.bind(this)(value['value'])}
              onCreateOption={this.onCreateOption.bind(this)}
              defaultOptions
              filterOption={this.customFilter.bind(this)}
              isValidNewOption={this.handleIsValidNewOption.bind(this)}
              autoFocus
            />
          </div>
        </div>
      </>
    );
  }
};
