import clear from '@iconify/icons-ic/clear';
import * as Immutable from 'immutable';
import * as React from 'react';
import { Helmet } from 'react-helmet';
import * as CardListActions from '../../actions/CardListActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import { DateFormat, TextFormat } from '../../helpers/fmt';
import history from '../../helpers/history';
import isServerside from '../../helpers/serverside';
import { CardList } from '../../models/CardList';
import { CellBorders } from '../../models/CellBorder';
import { CardListSorting } from '../../models/sorting/CardListSorting';
import { SubscriptionType } from '../../models/Subscriptions';
import { User } from '../../models/Users';
import { Dialog } from '../components/Dialog';
import { Footer } from '../components/Footer';
import { Placeholder } from '../components/Placeholder';
import * as Table from '../components/Table';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  cardLists: Immutable.OrderedMap<string, CardList>;
  cardListSorting: CardListSorting;
  subscriptionType: SubscriptionType;
}

interface IState {
  loading: boolean;
  removingCardList?: CardList;
}

export class BuilderCardLists extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);

    this.state = {
      loading: true,
    };
  }

  /**
   * @override
   */
  // WARNING: Awaiting actions in componentDidMount risks running setState after the first render, or after component has unmounted
  // TODO: Refactor so this uses the store.
  async componentDidMount() {
    await CardListActions.clearCardList(this.props.dispatcher);
    await CardListActions.all(this.props.cardListSorting, this.props.dispatcher);
    this.setState({ loading: false });
  }

  render() {
    if (isServerside()) {
      Helmet.renderStatic();
    }
    const subscribed =
      this.props.subscriptionType === SubscriptionType.MERCHANT ||
      this.props.subscriptionType === SubscriptionType.MERCHANT_PROMOTIONAL;
    return (
      <div>
        <div className="container-fluid" style={{ marginTop: '2rem' }}>
          <Dialog
            isOpen={this.state.removingCardList !== undefined}
            onDismiss={() => {
              this.setState({ removingCardList: undefined });
            }}
          >
            <h3 className="query-dialog-heading">Are you sure?</h3>
            <p className="query-dialog-subheading">
              You are deleting card list titled
              <strong>{` ${this.state.removingCardList ? this.state.removingCardList.get('name') : 'ERROR'}`}</strong>
              <span>. This decision cannot be reversed!</span>
            </p>
            <div className="flex justify-center">
              <input
                type="submit"
                className="button-alert"
                value="Remove"
                autoComplete="off"
                onClick={(evt: React.SyntheticEvent<HTMLElement>) =>
                  this.onConfirmRemoveCardList(evt, this.state.removingCardList)
                }
              />
            </div>
          </Dialog>
          {subscribed ? this.renderCardListContent() : this.renderUpgradeMessage()}
        </div>
        <Footer />
      </div>
    );
  }

  private renderCardListContent = () => {
    return (
      <div className="row">
        {/* actions and info */}
        <div className="col-xs-12">
          <div className="row">
            {/* action buttons */}
            <div className="col-xs-12 col-sm-6">
              <button className="button-primary" onClick={this.onClickCreateNewCardList}>
                Create New
              </button>
            </div>

            {/* display count */}
            {this.displayCount()}
          </div>
        </div>
        <div className="col-xs-12">{this.renderList()}</div>
      </div>
    );
  };

  private displayCount = () => {
    const displayText = this.state.loading ? 'Loading...' : `Displaying ${this.props.cardLists.size} card lists.`;
    return (
      <>
        <div className="hidden-md flex align-center selection-display-heading col-xs-12">{displayText}</div>
        <div className="visible-md flex align-center justify-end selection-display-heading col-sm-6">{displayText}</div>
      </>
    );
  };

  private renderList = () => {
    if (this.props.cardLists.size === 0) {
      return (
        <Placeholder
          message={"Looks like you have no card lists. Let's add one."}
          button={'Create New'}
          onClick={this.onClickCreateNewCardList}
        />
      );
    }
    return (
      <div className="card-list__table" style={{ marginTop: '2rem' }}>
        <Table.SortingHeader
          title="Name"
          sorting={this.props.cardListSorting}
          asc={CardListSorting.NAME_ASC}
          desc={CardListSorting.NAME_DESC}
          defaultOrder={'asc'}
          cellBorders={CellBorders.default(false)}
          onClick={(sorting) => {
            this.onClickSortingHeader(sorting as CardListSorting);
          }}
        />
        <div className="table__header cell-border-left">Description</div>
        <div className="table__header cell-border-left">Card Count</div>
        <Table.SortingHeader
          title="Date Edited"
          sorting={this.props.cardListSorting}
          asc={CardListSorting.DATE_EDITED_ASC}
          desc={CardListSorting.DATE_EDITED_DESC}
          cellBorders={CellBorders.default(false)}
          onClick={(sorting) => {
            this.onClickSortingHeader(sorting as CardListSorting);
          }}
        />
        <Table.SortingHeader
          title="Date Created"
          sorting={this.props.cardListSorting}
          asc={CardListSorting.DATE_ADDED_ASC}
          desc={CardListSorting.DATE_ADDED_DESC}
          cellBorders={CellBorders.default(false)}
          onClick={(sorting) => {
            this.onClickSortingHeader(sorting as CardListSorting);
          }}
        />
        <div className="table__header cell-border-left"></div>
        {this.props.cardLists.valueSeq().map((cardList: CardList, index: number) => {
          const dark = index % 2 === 0 ? true : false;
          const final = this.props.cardLists.size - 1 === index;
          return (
            <React.Fragment key={cardList.get('uuid')}>
              <Table.TextCell
                text={cardList.get('name')}
                dark={dark}
                cellBorders={CellBorders.default(final)}
                onClick={() => this.openCardList(cardList.get('uuid'))}
              />
              <Table.TextCell
                text={cardList.get('description') === '' ? '-' : cardList.get('description')}
                dark={dark}
                cellBorders={CellBorders.default(final)}
                onClick={() => this.openCardList(cardList.get('uuid'))}
              />
              <Table.TextCell
                text={TextFormat.pluralizedCards(cardList.get('cardCount'))}
                dark={dark}
                cellBorders={CellBorders.default(final)}
                onClick={() => this.openCardList(cardList.get('uuid'))}
              />
              <Table.TextCell
                text={DateFormat.human(
                  cardList.get('updatedAt'),
                  this.props.me.get('preferences').get('localization').get('timezone'),
                )}
                dark={dark}
                cellBorders={CellBorders.default(final)}
                onClick={() => this.openCardList(cardList.get('uuid'))}
              />
              <Table.TextCell
                text={DateFormat.human(
                  cardList.get('createdAt'),
                  this.props.me.get('preferences').get('localization').get('timezone'),
                )}
                dark={dark}
                cellBorders={CellBorders.default(final)}
                onClick={() => this.openCardList(cardList.get('uuid'))}
              />
              <Table.MaterialIcon
                dark={dark}
                cellBorders={CellBorders.rightCell(final)}
                materialIconText={clear}
                onClick={() => {
                  this.onClickDelete(cardList);
                }}
              />
            </React.Fragment>
          );
        })}
      </div>
    );
  };

  private renderUpgradeMessage = () => {
    return (
      <section className="section">
        <p className="paragraph-sm is-centered">
          Card lists are only available with a Merchant account. Upgrade to enable this feature.
        </p>
        <div className="flex justify-center">
          <button className="button-primary" onClick={this.onClickUpgrade.bind(this)}>
            Upgrade
          </button>
        </div>
      </section>
    );
  };

  private openCardList = (uuid: string) => {
    history.push(`/builder/card-lists/${uuid.toString()}`);
  };

  private onClickCreateNewCardList = async (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    const newCardList = await CardListActions.create('Untitled List', '', this.props.dispatcher);
    history.push(`/builder/card-lists/${newCardList.get('cardList').get('uuid').toString()}`);
  };

  private onClickDelete(cardList: CardList) {
    this.setState({
      removingCardList: cardList,
    });
  }

  private onClickSortingHeader(cardListSorting: CardListSorting) {
    CardListActions.all(cardListSorting, this.props.dispatcher);
  }

  private onConfirmRemoveCardList(evt: React.SyntheticEvent<HTMLElement>, removingCardList?: CardList) {
    evt.preventDefault();
    if (removingCardList) {
      CardListActions.remove(removingCardList.get('uuid'), this.props.dispatcher);
      this.setState({
        removingCardList: undefined,
      });
    }
  }

  private onClickUpgrade(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    history.push('/settings/subscription');
  }
}
