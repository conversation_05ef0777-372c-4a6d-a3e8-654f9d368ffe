import * as React from 'react';

interface IProps {
  children: JSX.Element;
}

export const InputSessionMessage = (props: IProps) => {
  return (
    <div className="container">
      <div className="row justify-center">
        <div className="col-xs-12 flex vertical align-center">
          <span className="builder-placeholder">{props.children}</span>
        </div>
      </div>
    </div>
  );
};
