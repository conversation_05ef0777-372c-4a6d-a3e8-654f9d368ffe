import offline_bolt from '@iconify/icons-ic/baseline-offline-bolt';
import * as React from 'react';
import { TextFormat } from '../../helpers/fmt';
import { Game } from '../../models/Game';
import { SessionFilter, SessionState } from '../../stores/InputSessionStore';
import { Button, ButtonClass } from '../components/filters/UpdateFiltersButton';

interface IProps {
  sessionCount: number;
  createNewSession: (evt: any) => void;
  filters: SessionFilter;
  sessionListState: SessionState;
}

export function InputSessionsPlaceholder(props: IProps) {
  let content = null;

  function spinnerText() {
    switch (props.sessionListState) {
      case SessionState.DELETING:
        return `Deleting ${TextFormat.quantityOf('session', props.sessionCount)}`;
      case SessionState.COMMITTING:
        return `Committing ${TextFormat.quantityOf('session', props.sessionCount)}`;
      default:
        return 'Loading';
    }
  }

  switch (props.sessionListState) {
    case SessionState.LOADING:
    case SessionState.DELETING:
    case SessionState.COMMITTING:
      content = (
        <span className="builder-placeholder">
          <div className="flex">
            <div className="spinner-xs"></div>
            <p style={{ marginLeft: '0.5rem' }}>{spinnerText()}</p>
          </div>
        </span>
      );
      break;
    case SessionState.IDLE:
      if (props.sessionCount === 0) {
        let title = 'You have no staged sessions matching your current filters';
        if (props.filters.get('game') == Game.MTG) title += '- but you can add cards directly';

        content = (
          <>
            <span className="builder-placeholder-quick-add">{title}</span>
            {props.filters.get('game') == Game.MTG && (
              <div className="row justify-center" style={{ marginTop: '1rem', marginBottom: '5rem' }}>
                <Button
                  class={ButtonClass.GREEN}
                  icon={offline_bolt}
                  title="Quick Add"
                  onClick={props.createNewSession.bind(this)}
                />
              </div>
            )}
          </>
        );
      }
      break;
  }

  if (content) {
    return (
      <div className="row justify-center">
        <div className="col-xs-12 flex vertical align-center">{content}</div>
      </div>
    );
  } else {
    return null;
  }
}
