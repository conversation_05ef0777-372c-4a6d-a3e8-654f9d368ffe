import * as moment from 'moment-timezone';
import * as React from 'react';
import * as ChangelogActions from '../../actions/ChangelogActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import { NumberFormat, TextFormat } from '../../helpers/fmt';
import {
  Changelog,
  ChangelogEvent,
  ChangelogEventAction,
  ChangelogEventItemType,
  ChangelogEventType,
} from '../../models/Changelogs';
import { Rarity } from '../../models/Rarity';
import { User } from '../../models/Users';
import { Dialog } from '../components/Dialog';
import { SetSymbol } from '../components/SetSymbol';

interface IProps {
  me: User;
  dispatcher: Dispatcher;
}

interface IState {
  isShowingMore?: boolean;
  eventShowingMore?: ChangelogEvent;
  changelog: Changelog;
}

export default class extends React.Component<IProps, IState> {
  private token: { remove: () => void };
  private isLoadingPage = false;

  constructor(props: IProps) {
    super(props);
    this.state = {
      isShowingMore: false,
      eventShowingMore: undefined,
      changelog: props.dispatcher.ChangelogStore().getState(),
    };
  }

  public componentDidMount() {
    this.token = this.props.dispatcher.ChangelogStore().addListener(this.onChangeStore.bind(this));
    this.isLoadingPage = true;

    ChangelogActions.page(this.props.dispatcher)
      .then(() => {
        this.isLoadingPage = false;
      })
      .catch((err) => {
        this.isLoadingPage = false;
        console.error(err);
      });

    const self = this;
    $('#changelog').on('scroll', function () {
      if (
        !self.isLoadingPage &&
        self.state.changelog.get('nextPage') &&
        $(this).prop('scrollHeight') - $(this).scrollTop() < 1000
      ) {
        self.isLoadingPage = true;
        ChangelogActions.pageNext(self.props.dispatcher)
          .then(() => {
            self.isLoadingPage = false;
          })
          .catch((err) => {
            self.isLoadingPage = false;
            console.error(err);
          });
      }
    });
  }

  public componentWillUnmount() {
    this.token.remove();
    $('#changelog').off('scroll');
  }

  public render() {
    return (
      <div className="container-fluid">
        <Dialog isOpen={this.state.isShowingMore ? true : false} onDismiss={this.onDismiss.bind(this)}>
          {this.state.eventShowingMore
            ? (() => {
                const cardUsers = this.state.eventShowingMore
                  .get('items')
                  .filter(
                    (event_item) =>
                      event_item !== undefined && event_item.get('type') === ChangelogEventItemType.CARD_USER,
                  );
                const tags = this.state.eventShowingMore
                  .get('items')
                  .filter(
                    (event_item) => event_item !== undefined && event_item.get('type') === ChangelogEventItemType.TAG,
                  );
                if (!cardUsers.size) {
                  return null;
                }
                let pill = TextFormat.pluralizedCards(cardUsers.size);
                if (tags.size) {
                  pill += ' #' + tags.get(0).get('name');
                }
                return (
                  <div className="flex align-center" style={{ margin: '2rem 0.5rem 0rem 0.5rem' }}>
                    {this.state.eventShowingMore.get('action') === ChangelogEventAction.ADD &&
                    this.state.eventShowingMore.get('event_type') === ChangelogEventType.CARD ? (
                      <div className="builder-changelog-pill is-add-card">Added</div>
                    ) : null}
                    {this.state.eventShowingMore.get('action') === ChangelogEventAction.ADD &&
                    this.state.eventShowingMore.get('event_type') === ChangelogEventType.TAG ? (
                      <div className="builder-changelog-pill is-add-tag">Tagged</div>
                    ) : null}
                    {this.state.eventShowingMore.get('action') === ChangelogEventAction.REMOVE &&
                    this.state.eventShowingMore.get('event_type') === ChangelogEventType.CARD ? (
                      <div className="builder-changelog-pill is-remove-card">Removed</div>
                    ) : null}
                    {this.state.eventShowingMore.get('action') === ChangelogEventAction.REMOVE &&
                    this.state.eventShowingMore.get('event_type') === ChangelogEventType.TAG ? (
                      <div className="builder-changelog-pill is-remove-tag">Untagged</div>
                    ) : null}
                    {pill}
                    <span className="builder-changelog-event-date">
                      {moment
                        .tz(this.state.eventShowingMore.get('created_at'), 'UTC')
                        .tz(this.props.me.get('preferences').get('localization').get('timezone'))
                        .format('HH:mm - YYYY/MM/DD')}
                    </span>
                  </div>
                );
              })()
            : null}
          <div className="builder-changelog" style={{ margin: '1rem 0.5rem 0rem 0.5rem' }}>
            {this.state.eventShowingMore
              ? this.state.eventShowingMore.get('items').map((item, key) => {
                  if (!item) {
                    return null;
                  }
                  if (item.get('type') !== ChangelogEventItemType.CARD_USER) {
                    return null;
                  }

                  // Appeases TypeScript
                  const rarity = item.get('rarity') === null ? undefined : (item.get('rarity') as Rarity);
                  return (
                    <div key={'builder-changelog-event-' + key} className="builder-changelog-event">
                      <div className="set-symbol-container" style={{ marginRight: '0.5rem' }}>
                        <SetSymbol
                          setName={item.get('set_name')}
                          setCode={item.get('set_code')}
                          rarity={rarity}
                          hoverText={true}
                        />
                      </div>
                      <div style={{ maxWidth: '70%' }}>{item.get('card_name')}</div>
                    </div>
                  );
                })
              : null}
          </div>
        </Dialog>
        <section className="section">
          <div id="changelog" className="builder-changelog">
            {this.state.changelog.get('events').map((event, key) => {
              if (!event) {
                return null;
              }
              const cardUsers = event
                .get('items')
                .filter(
                  (event_item) =>
                    event_item !== undefined && event_item.get('type') === ChangelogEventItemType.CARD_USER,
                );
              const tags = event
                .get('items')
                .filter(
                  (event_item) => event_item !== undefined && event_item.get('type') === ChangelogEventItemType.TAG,
                );
              if (!cardUsers.size) {
                return null;
              }
              const cardUser = cardUsers.get(0);
              let pill = cardUser.get('card_name');
              if (tags.size) {
                pill += ' #' + tags.get(0).get('name');
              }

              let event_count = event.get('items').count();
              if (event.get('event_type') === ChangelogEventType.TAG) {
                event_count -= 1;
              }

              // Appeases TypeScript
              const rarity = cardUser.get('rarity') === null ? undefined : (cardUser.get('rarity') as Rarity);
              return (
                <div
                  id={'builder-changelog-event-' + key}
                  key={'builder-changelog-event-' + key}
                  className="builder-changelog-event"
                >
                  {event.get('action') === ChangelogEventAction.ADD &&
                  event.get('event_type') === ChangelogEventType.CARD ? (
                    <div className="builder-changelog-pill is-add-card">Added</div>
                  ) : null}
                  {event.get('action') === ChangelogEventAction.ADD &&
                  event.get('event_type') === ChangelogEventType.TAG ? (
                    <div className="builder-changelog-pill is-add-tag">Tagged</div>
                  ) : null}
                  {event.get('action') === ChangelogEventAction.REMOVE &&
                  event.get('event_type') === ChangelogEventType.CARD ? (
                    <div className="builder-changelog-pill is-remove-card">Removed</div>
                  ) : null}
                  {event.get('action') === ChangelogEventAction.REMOVE &&
                  event.get('event_type') === ChangelogEventType.TAG ? (
                    <div className="builder-changelog-pill is-remove-tag">Untagged</div>
                  ) : null}
                  <div className="set-symbol-container" style={{ marginRight: '0.5rem' }}>
                    <SetSymbol
                      setName={cardUser.get('set_name')}
                      setCode={cardUser.get('set_code')}
                      rarity={rarity}
                      hoverText={true}
                    />
                  </div>
                  <div style={{ maxWidth: '70%' }}>
                    {pill}
                    {event_count > 1 ? (
                      <span className="builder-changelog-event-extras">
                        {' and ' +
                          NumberFormat.commaSeparated(event_count - 1) +
                          ' other ' +
                          (event_count > 2 ? ' cards' : ' card')}
                      </span>
                    ) : null}
                  </div>
                  <span className="builder-changelog-event-date">{this.displayTime(event.get('created_at'))}</span>
                </div>
              );
            })}
          </div>
        </section>
      </div>
    );
  }

  // TODO: Investigate this feature. Currently there is no "click show more" button for BuilderChangelog.
  // What would happen if this button were to be added? Would this feature work if that were to happen and if so how well?
  private onClickShowMore(event: ChangelogEvent, evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({
      isShowingMore: true,
      eventShowingMore: event,
    });
  }

  private displayTime(timestamp: Date): string {
    const timezone = this.props.me.get('preferences').get('localization').get('timezone');

    return moment.tz(timestamp, timezone).format('HH:mm - YYYY/MM/DD');
  }

  private onDismiss() {
    this.setState({
      isShowingMore: false,
      eventShowingMore: undefined,
    });
  }

  private onChangeStore() {
    this.setState({
      changelog: this.props.dispatcher.ChangelogStore().getState(),
    });
  }
}
