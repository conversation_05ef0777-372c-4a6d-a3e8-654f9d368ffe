// npm
import * as Immutable from 'immutable';
import * as React from 'react';
import * as FinishActions from '../../actions/FinishActions';
import * as InputSessionActions from '../../actions/InputSessionActions';
import * as MessageActions from '../../actions/MessageActions';
// Dispatcher
import Dispatcher from '../../dispatcher/Dispatcher';
import { CardAttributes } from '../../models/CardAttributes';
import { AnyCardGroup } from '../../models/CardGroup';
import { AnyCardPage } from '../../models/CardPage';
import { CardPanel as CardPanelModel } from '../../models/CardPanels';
import { CellBorders } from '../../models/CellBorder';
import { Game } from '../../models/Game';
import { Grouping } from '../../models/Grouping';
import { generateLanguageOptions, Language } from '../../models/Language';
import { LorcanaCardGroup } from '../../models/lorcana/LorcanaCardPage';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { PokeCardGroup } from '../../models/pokemon/PokeCardPage';
import { Sorting } from '../../models/sorting/Sorting';
import { Tag } from '../../models/Tags';
import { User } from '../../models/Users';
import { YugiCardGroup } from '../../models/yugioh/YugiCardPage';
import { CardPanel, CSSGridContainer } from '../components/cardpanel/CardPanel';
import { Paginator } from '../components/Paginator';
import * as Table from '../components/Table';
import { ColumnSize, TableConfiguration } from '../components/Table';
import { LorcanaStagedCardRow } from './lorcana/LorcanaStagedCardRow';
import { PokeStagedCardRow } from './pokemon/PokeStagedCardRow';
import { StagedCardRow } from './StagedCardRow';
import { YugiStagedCardRow } from './yugioh/YugiStagedCardRow';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  game: Game;
  cardPage: AnyCardPage;
  cardPanel: CardPanelModel;
  userTags: Immutable.OrderedSet<Tag>;
  languageOptions?: Immutable.Map<string, Immutable.List<Language>>;
  onClickPage: (page: number) => void;
  updateSorting: (sorting: Sorting) => void;
  remove: (cardGroup: AnyCardGroup) => void;
  onClickListItem: (y: number, game: Game, cardGroup: AnyCardGroup) => void;
}

interface IState {}

export const SessionCards = class extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  componentDidMount(): void {
    // Ensures each finish selector in the list does not fire a request for finishes
    FinishActions.loadPokeFinishes(this.props.dispatcher);
  }

  public render() {
    const tableConfiguration = new TableConfiguration(this.tableConfiguration());

    let attributes: Immutable.List<string>;
    let cardRows: JSX.Element[];

    switch (this.props.game) {
      case Game.MTG:
        attributes = Immutable.List(['Foil', 'Condition', 'Language']);
        cardRows = this.renderMTGCards(this.props.cardPage.get('cardGroups'));
        break;
      case Game.POKEMON:
        // TODO: Add language support for pokemon
        attributes = Immutable.List(['Condition', 'Finish']);
        cardRows = this.renderPokeCards(this.props.cardPage.get('cardGroups'));
        break;
      case Game.YUGIOH:
        // TODO: Add finish and language support for yugioh + handle optional set
        attributes = Immutable.List(['Condition']);
        cardRows = this.renderYugiCards(this.props.cardPage.get('cardGroups'));
        break;
      case Game.LORCANA:
        // TODO: Add language support for Lorcana
        attributes = Immutable.List(['Condition', 'Finish']);
        cardRows = this.renderLorcanaCards(this.props.cardPage.get('cardGroups'));
        break;
    }

    return (
      <>
        <div className={'base-table'} style={{ ...tableConfiguration.style(), marginTop: '0' }}>
          {this.props.cardPage.get('cardGrouping') !== Grouping.NONE ? (
            <Table.SortingHeader
              title="Count"
              collectionCell={true}
              sorting={this.props.cardPage.get('cardSorting')}
              asc={Sorting.COUNT_ASC}
              desc={Sorting.COUNT_DESC}
              cellBorders={CellBorders.default(false)}
              onClick={(sorting) => {
                this.props.updateSorting.bind(this)(sorting);
              }}
            />
          ) : null}
          <Table.MultiSortingHeader
            title="Card"
            collectionCell={true}
            sorting={this.props.cardPage.get('cardSorting')}
            cellBorders={CellBorders.default(false)}
            asc={[Sorting.NAME_ASC, Sorting.SET_NAME_ASC]}
            desc={[Sorting.NAME_DESC, Sorting.SET_NAME_DESC]}
          />
          {attributes.map((label: string) => {
            return (
              <Table.Header key={label} title={label} cellBorders={CellBorders.default(false)} collectionCell={true} />
            );
          })}
          <Table.SortingHeader
            title={'Price (' + this.props.me.get('preferences').get('localization').get('currency') + ')'}
            collectionCell={true}
            sorting={this.props.cardPage.get('cardSorting')}
            asc={Sorting.PRICE_ASC}
            desc={Sorting.PRICE_DESC}
            cellBorders={CellBorders.default(false)}
            onClick={(sorting) => {
              this.props.updateSorting.bind(this)(sorting);
            }}
          />
          <Table.SortingHeader
            title={'Staged At'}
            collectionCell={true}
            sorting={this.props.cardPage.get('cardSorting')}
            asc={Sorting.DATE_ADDED_ASC}
            desc={Sorting.DATE_ADDED_DESC}
            cellBorders={CellBorders.default(false)}
            onClick={(sorting) => {
              this.props.updateSorting.bind(this)(sorting);
            }}
          />
          <Table.Header title={''} cellBorders={CellBorders.default(false)} collectionCell={true} />
          {cardRows}
        </div>
        <Paginator
          page={this.props.cardPage.get('filterOptions').getPage() || 0}
          totalItems={this.props.cardPage.get('totalGroupCount')}
          onClickPage={(page) => this.props.onClickPage(page)}
        />
      </>
    );
  }

  private tableConfiguration(): string[] {
    const columnSizes = [];

    // Optional count
    if (this.props.cardPage.get('cardGrouping') !== Grouping.NONE) columnSizes.push(ColumnSize.MAX_CONTENT);
    // Card details
    columnSizes.push(ColumnSize.MINMAX_100);

    // Attribute columns (condition, language, price, etc.)
    let attributeCount = 0;
    switch (this.props.game) {
      case Game.MTG:
        // Foil checkbox
        columnSizes.push(ColumnSize.MAX_CONTENT);
        attributeCount = 4;
        break;
      case Game.POKEMON:
        attributeCount = 4;
        break;
    }

    columnSizes.push(TableConfiguration.repeat(attributeCount, ColumnSize.MINMAX_1FR));

    // Remove column
    columnSizes.push(ColumnSize.MAX_CONTENT);

    return columnSizes;
  }

  private renderMTGCards(cardGroups: Immutable.List<MTGCardGroup>): JSX.Element[] {
    const cardGrouping = this.props.cardPage.get('cardGrouping');
    const colSpan = 7 + (cardGrouping !== Grouping.NONE ? 1 : 0);

    const cardRows = cardGroups
      .map((cardGroup: MTGCardGroup, index: number) => {
        const final = cardGroups.size - 1 === index;
        const languageOptions = generateLanguageOptions(
          this.props.languageOptions || Immutable.Map<string, Immutable.List<Language>>(),
          cardGroup,
          this.props.cardPanel,
        );
        return (
          <React.Fragment key={index}>
            <StagedCardRow
              index={index}
              key={cardGroup ? `row-${cardGroup.get('cardInstances').first().get('id')}` : 'row-0'}
              me={this.props.me}
              onRemove={this.props.remove}
              onClick={this.props.onClickListItem.bind(this, index, Game.MTG, cardGroup)}
              cardGroup={cardGroup}
              grouping={cardGrouping}
              final={final}
              onUpdateAttributes={this.updateAttributes.bind(this)}
              languageOptions={languageOptions}
            />
            <div style={{ gridColumnEnd: `span ${colSpan}` }} key={'list-row-card-panel-' + index}>
              <div
                className="list-panel-container builder-list-panel"
                style={{
                  borderTop: this.props.cardPanel.get('y') === index && !final ? '1px solid #CDCDCD' : 'none',
                  borderBottom: this.props.cardPanel.get('y') === index && !final ? '1px solid #CDCDCD' : 'none',
                }}
              >
                <CardPanel
                  key={'card-panel-' + index}
                  dispatcher={this.props.dispatcher}
                  me={this.props.me}
                  gridContainer={CSSGridContainer.BUILDER}
                  usernamePublic={true}
                  cardPage={this.props.cardPage}
                  cardPanel={this.props.cardPanel}
                  cardPanelRow={index}
                  userTags={this.props.userTags}
                  isGrid={false}
                />
              </div>
            </div>
          </React.Fragment>
        );
      })
      .toJS();
    return cardRows;
  }

  private renderPokeCards(cardGroups: Immutable.List<PokeCardGroup>): JSX.Element[] {
    const cardGrouping = this.props.cardPage.get('cardGrouping');
    const colSpan = 6 + (cardGrouping !== Grouping.NONE ? 1 : 0);

    const cardRows = cardGroups
      .map((cardGroup: PokeCardGroup, index: number) => {
        const final = cardGroups.size - 1 === index;
        // TODO: Support pokemon language options
        // const languageOptions = generateLanguageOptions(
        //   this.props.languageOptions || Immutable.Map<string, Immutable.Set<Language>>(),
        //   cardGroup,
        //   this.props.cardPanel,
        // );
        return (
          <React.Fragment key={index}>
            <PokeStagedCardRow
              index={index}
              key={cardGroup ? `row-${cardGroup.get('cardInstances').first().get('uuid')}` : 'row-0'}
              me={this.props.me}
              onRemove={this.props.remove}
              onClick={this.props.onClickListItem.bind(this, index, Game.POKEMON, cardGroup)}
              cardGroup={cardGroup}
              grouping={cardGrouping}
              final={final}
              onUpdateAttributes={this.updateAttributes.bind(this)}
              languageOptions={Immutable.Set<Language>()} // TODO: Support pokemon language options
              dispatcher={this.props.dispatcher}
            />
            <div style={{ gridColumnEnd: `span ${colSpan}` }} key={'list-row-card-panel-' + index}>
              <div
                className="list-panel-container builder-list-panel"
                style={{
                  borderTop: this.props.cardPanel.get('y') === index && !final ? '1px solid #CDCDCD' : 'none',
                  borderBottom: this.props.cardPanel.get('y') === index && !final ? '1px solid #CDCDCD' : 'none',
                }}
              >
                <CardPanel
                  key={'card-panel-' + index}
                  dispatcher={this.props.dispatcher}
                  me={this.props.me}
                  gridContainer={CSSGridContainer.BUILDER}
                  usernamePublic={true}
                  cardPage={this.props.cardPage}
                  cardPanel={this.props.cardPanel}
                  cardPanelRow={index}
                  userTags={this.props.userTags}
                  isGrid={false}
                />
              </div>
            </div>
          </React.Fragment>
        );
      })
      .toJS();

    return cardRows;
  }

  private renderYugiCards(cardGroups: Immutable.List<YugiCardGroup>): JSX.Element[] {
    const cardGrouping = this.props.cardPage.get('cardGrouping');
    const colSpan = 5 + (cardGrouping !== Grouping.NONE ? 1 : 0);

    const cardRows = cardGroups
      .map((cardGroup: YugiCardGroup, index: number) => {
        const final = cardGroups.size - 1 === index;
        // TODO: Support pokemon language options
        // const languageOptions = generateLanguageOptions(
        //   this.props.languageOptions || Immutable.Map<string, Immutable.Set<Language>>(),
        //   cardGroup,
        //   this.props.cardPanel,
        // );
        return (
          <React.Fragment key={index}>
            <YugiStagedCardRow
              index={index}
              key={cardGroup ? `row-${cardGroup.get('cardInstances').first().get('uuid')}` : 'row-0'}
              me={this.props.me}
              onRemove={this.props.remove}
              onClick={this.props.onClickListItem.bind(this, index, Game.YUGIOH, cardGroup)}
              cardGroup={cardGroup}
              grouping={cardGrouping}
              final={final}
              onUpdateAttributes={this.updateAttributes.bind(this)}
              languageOptions={Immutable.Set<Language>()} // TODO: Support pokemon language options
            />
            <div style={{ gridColumnEnd: `span ${colSpan}` }} key={'list-row-card-panel-' + index}>
              <div
                className="list-panel-container builder-list-panel"
                style={{
                  borderTop: this.props.cardPanel.get('y') === index && !final ? '1px solid #CDCDCD' : 'none',
                  borderBottom: this.props.cardPanel.get('y') === index && !final ? '1px solid #CDCDCD' : 'none',
                }}
              >
                <CardPanel
                  key={'card-panel-' + index}
                  dispatcher={this.props.dispatcher}
                  me={this.props.me}
                  gridContainer={CSSGridContainer.BUILDER}
                  usernamePublic={true}
                  cardPage={this.props.cardPage}
                  cardPanel={this.props.cardPanel}
                  cardPanelRow={index}
                  userTags={this.props.userTags}
                  isGrid={false}
                />
              </div>
            </div>
          </React.Fragment>
        );
      })
      .toJS();

    return cardRows;
  }

  private renderLorcanaCards(cardGroups: Immutable.List<LorcanaCardGroup>): JSX.Element[] {
    const cardGrouping = this.props.cardPage.get('cardGrouping');
    const colSpan = 6 + (cardGrouping !== Grouping.NONE ? 1 : 0);

    const cardRows = cardGroups
      .map((cardGroup: LorcanaCardGroup, index: number) => {
        const final = cardGroups.size - 1 === index;
        // TODO: Support lorcana language options
        // const languageOptions = generateLanguageOptions(
        //   this.props.languageOptions || Immutable.Map<string, Immutable.Set<Language>>(),
        //   cardGroup,
        //   this.props.cardPanel,
        // );
        return (
          <React.Fragment key={index}>
            <LorcanaStagedCardRow
              index={index}
              key={cardGroup ? `row-${cardGroup.get('cardInstances').first().get('uuid')}` : 'row-0'}
              me={this.props.me}
              onRemove={this.props.remove}
              onClick={this.props.onClickListItem.bind(this, index, Game.LORCANA, cardGroup)}
              cardGroup={cardGroup}
              grouping={cardGrouping}
              final={final}
              onUpdateAttributes={this.updateAttributes.bind(this)}
              languageOptions={Immutable.Set<Language>()} // TODO: Support lorcana language options
              dispatcher={this.props.dispatcher}
            />
            <div style={{ gridColumnEnd: `span ${colSpan}` }} key={'list-row-card-panel-' + index}>
              <div
                className="list-panel-container builder-list-panel"
                style={{
                  borderTop: this.props.cardPanel.get('y') === index && !final ? '1px solid #CDCDCD' : 'none',
                  borderBottom: this.props.cardPanel.get('y') === index && !final ? '1px solid #CDCDCD' : 'none',
                }}
              >
                <CardPanel
                  key={'card-panel-' + index}
                  dispatcher={this.props.dispatcher}
                  me={this.props.me}
                  gridContainer={CSSGridContainer.BUILDER}
                  usernamePublic={true}
                  cardPage={this.props.cardPage}
                  cardPanel={this.props.cardPanel}
                  cardPanelRow={index}
                  userTags={this.props.userTags}
                  isGrid={false}
                />
              </div>
            </div>
          </React.Fragment>
        );
      })
      .toJS();

    return cardRows;
  }

  updateAttributes(cardGroup: AnyCardGroup, attributes: CardAttributes): void {
    InputSessionActions.updateCards(this.props.game, cardGroup, attributes, false, this.props.dispatcher).catch(
      (err: any) => {
        MessageActions.error(
          'Failed to update card attributes. Please contact us if this error persists.',
          this.props.dispatcher,
          err,
        );
      },
    );
  }
};
