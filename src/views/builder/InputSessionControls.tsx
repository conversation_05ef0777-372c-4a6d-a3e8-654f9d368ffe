// npm
// Iconify
import apps from '@iconify/icons-ic/baseline-apps';
import library_add_check from '@iconify/icons-ic/baseline-library-add-check';
import close from '@iconify/icons-ic/close';
import * as React from 'react';
// Containers
// Other
import Dispatcher from '../../dispatcher/Dispatcher';
import { Game } from '../../models/Game';
import { AnyInputSession } from '../../models/InputSession';
import { User } from '../../models/Users';
import { SessionState } from '../../stores/InputSessionStore';
import { Button, ButtonClass } from '../components/filters/UpdateFiltersButton';
import { InputSessionInfo } from './InputSessionInfo';
import { InputSessionValues } from './InputSessionValues';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  inputSession: AnyInputSession;
  status: SessionState;
  onClickAddToCollection: (evt: React.SyntheticEvent<HTMLElement>) => void;
  onClickDelete: (evt: React.SyntheticEvent<HTMLElement>) => void;
  openActions: () => void;
}

interface IState {}

export class InputSessionControls extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }
  render() {
    const totalCount = this.props.inputSession.get('cardPage').get('totalCount');
    const totalValue = this.props.inputSession.get('cardPage').get('totalValue');
    const buttonsDisabled = this.props.status === SessionState.LOADING || !(totalCount && totalCount > 0);

    return (
      <div className="row justify-center" style={{ marginBottom: '1rem' }}>
        <div className="col-xs-12 flex" style={{ flexWrap: 'wrap', gap: '1rem' }}>
          <div className="flex vertical grow-xs-1">
            {<InputSessionInfo dispatcher={this.props.dispatcher} inputSession={this.props.inputSession} />}
          </div>
          <div className="flex vertical" style={{ textAlign: 'right' }}>
            <InputSessionValues me={this.props.me} totalCount={totalCount} totalValue={totalValue} />
          </div>
          <div className="input-session-flex justify-end">
            {this.props.inputSession.get('game') == Game.MTG && (
              <Button
                class={ButtonClass.GREEN}
                icon={library_add_check}
                title="Commit to Collection"
                onClick={this.props.onClickAddToCollection.bind(this)}
                disabled={buttonsDisabled}
              />
            )}
            <Button
              class={ButtonClass.GREY}
              icon={apps}
              title="Other Actions"
              onClick={() => this.props.openActions()}
              disabled={buttonsDisabled}
            />
            <Button
              class={ButtonClass.ALERT}
              icon={close}
              title="Delete"
              onClick={this.props.onClickDelete}
              disabled={this.props.status !== SessionState.IDLE}
            />
          </div>
        </div>
      </div>
    );
  }
}
