import * as Immutable from 'immutable';
import * as React from 'react';
import * as HasStagingCards from '../../containers/HasStagingCards';
import * as HasSubscription from '../../containers/HasSubscription';
import Dispatcher from '../../dispatcher/Dispatcher';
import { Game } from '../../models/Game';
import { DataService } from '../../models/ImportExport';
import { AnyInputSession } from '../../models/InputSession';
import { InputSourceType } from '../../models/InputSource';
import { TabItem } from '../../models/TabItem';
import { Dialog, DialogSize } from '../components/Dialog';
import { SplitDivider } from '../components/SplitDivider';
import { TabBar } from '../components/TabBar';
import { SendToCardList } from './components/SendToCardList';
import { SendToService } from './components/SendToService';

interface IProps {
  readonly dispatcher: Dispatcher;
  readonly game: Game;
  readonly totalCount: number;
  readonly sourceType?: InputSourceType;
  readonly inputSession?: AnyInputSession;
  readonly isOpen: boolean;
  readonly onClose: () => void;
  readonly onDismiss: () => void;
}

export enum StagingActionTab {
  EXPORT = 'Export',
  CARD_LIST = 'Create List',
}

interface IState {
  tab: StagingActionTab;
  selectedService?: DataService;
  uuidCardList?: string;
  newCardListName?: string;
}

export const StagingActionsPanel = HasSubscription.Attach<IProps>(
  HasStagingCards.Attach<IProps & HasSubscription.IProps>(
    class extends React.Component<IProps & HasSubscription.IProps & HasStagingCards.IProps, IState> {
      constructor(props: IProps & HasSubscription.IProps & HasStagingCards.IProps) {
        super(props);
        this.state = {
          tab: StagingActionTab.EXPORT,
          selectedService: undefined,
        };
      }

      getInputSession(): AnyInputSession | undefined {
        return this.props.inputSession && (this.props.inputSession as AnyInputSession);
      }

      public render() {
        const inputSession = this.getInputSession();
        if (!this.props.isOpen) {
          return null;
        }

        const game = inputSession ? inputSession.get('game') : this.props.game;

        const merchantUser = this.props.subscription.isMerchant();
        const secondaryPages = Immutable.List<TabItem>([
          new TabItem({ title: StagingActionTab.EXPORT }),
          new TabItem({ title: StagingActionTab.CARD_LIST, disabled: !inputSession || game !== Game.MTG }),
        ]);

        return (
          <Dialog
            isOpen={this.props.isOpen}
            onDismiss={this.props.onDismiss}
            heading="Other Actions"
            size={DialogSize.MEDIUM}
          >
            <div className="row">
              <SplitDivider inDialog={true}>
                <TabBar pages={secondaryPages} selectedTab={this.state.tab} onChangeTab={this.onChangeTab.bind(this)} />
              </SplitDivider>
            </div>
            {this.state.tab === StagingActionTab.CARD_LIST && inputSession ? (
              <SendToCardList
                dispatcher={this.props.dispatcher}
                subscription={this.props.subscription}
                isMerchant={merchantUser}
                inputSession={inputSession}
                onClose={this.props.onClose}
              />
            ) : (
              <SendToService
                dispatcher={this.props.dispatcher}
                subscription={this.props.subscription}
                isMerchant={merchantUser}
                inputSession={inputSession}
                game={this.props.game}
                sourceType={this.props.sourceType}
                totalCount={this.props.totalCount}
                onClose={this.props.onClose}
              />
            )}
          </Dialog>
        );
      }

      onSelectService(service: DataService) {
        this.setState({ selectedService: service });
      }

      onChangeTab(page: string) {
        this.setState({
          tab: page as StagingActionTab,
        });
      }
    },
  ),
);
