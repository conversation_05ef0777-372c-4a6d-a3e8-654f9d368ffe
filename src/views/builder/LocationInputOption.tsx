import * as React from 'react';
import { OptionTypeBase } from 'react-select';
import { StackLocation } from '../../models/StackLocation';

export class LocationInputOption implements OptionTypeBase {
  label: string;
  value: StackLocation;
  data: {
    __isNew__: boolean;
  };
  constructor(location: StackLocation) {
    this['label'] = location.get('name');
    this['value'] = location;
  }

  static formatOptionLabel = (location: LocationInputOption) => {
    return <div className="react-select__suggestion">{location['label']}</div>;
  };
}
