import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../dispatcher/Dispatcher';
import { CoreAssets } from '../../helpers/core_assets';
import importPlotly from '../../helpers/plotly';
import isServerside from '../../helpers/serverside';
import { ColorStatistics } from '../../models/ColorStatistics';
import { StatComponentStatus } from '../../models/Statistics';
import { ChartLoadingScreen } from './ChartLoadingScreen';

const Plotly = importPlotly();

interface IProps {
  dispatcher: Dispatcher;
  statistics: Immutable.OrderedMap<string, number>;
  status: StatComponentStatus;
  reload: () => void;
}

export class ColorBreakdownChart extends React.Component<IProps> {
  private handleWindowResize: EventListener | null;
  private readonly titleRef: React.RefObject<HTMLDivElement>;

  constructor(props: IProps) {
    super(props);
    this.titleRef = React.createRef<HTMLDivElement>();
    this.state = {};
  }
  public componentDidMount() {
    this.renderChart();
    this.handleWindowResize = () => {
      if (this.titleRef.current && this.titleRef.current.id !== '') {
        Plotly.purge(this.titleRef.current.id);
      }
      this.renderChart();
    };
    window.addEventListener('resize', this.handleWindowResize);
  }

  public componentWillUnmount() {
    if (this.handleWindowResize != null) window.removeEventListener('resize', this.handleWindowResize);
    this.handleWindowResize = null;
  }

  public componentDidUpdate() {
    this.renderChart();
  }

  public render() {
    return (
      <div
        className={`account-chart-container${this.props.status !== StatComponentStatus.LOADED ? '--loading' : ''}`}
        style={{ width: '100%' }}
      >
        <ChartLoadingScreen status={this.props.status} reload={this.props.reload} />
        {this.props.status === StatComponentStatus.LOADED ? (
          <div
            style={{
              position: 'relative',
              width: '100%',
              minWidth: '0',
              maxWidth: '100%',
              height: '30rem',
              maxHeight: '30rem',
            }}
          >
            <div style={{ width: '100%', maxWidth: '100%', height: '100%', maxHeight: '100%' }} ref={this.titleRef} />
          </div>
        ) : null}
      </div>
    );
  }

  private renderChart() {
    if (isServerside() || this.props.status !== StatComponentStatus.LOADED) {
      return;
    }

    const layout = {
      margin: {
        l: 80,
        r: 80,
        b: 70,
        t: 15,
        pad: 0,
      },
      font: {
        family: 'lato-bold',
        src: {
          url: '/fonts/lato-bold-webfont.svg#latobold',
          format: 'svg',
        },
        size: 18,
        color: '#3e3e3e',
      },
      showlegend: false,
      xaxis: {
        fixedrange: true,
        tick0: 0,
        dtick: 1,
      },
      yaxis: {
        fixedrange: true,
        tickmode: 'auto',
        tick0: 0,
        nticks: 8,
        rangemode: 'tozero',
        autorange: true,
        hoverformat: ',d',
        tickformat: ',d',
        automargin: true,
      },
      marker: {
        color: this.markerColor(this.props.statistics),
      },
      images: this.images(this.props.statistics),
    };

    const config: any = {
      staticPlot: false,
      editable: false,
      scrollZoom: false,
      showTips: false,
      showLink: false,
      sendData: false,
      showSources: false,
      displayModeBar: false,
      modeBarButtons: false,
      logging: false,
    };

    if (this.titleRef.current) {
      Plotly.newPlot(this.titleRef.current, this.data(this.props.statistics), layout, config);
    }
  }

  private data = (statistics: Immutable.Map<string, number>) => {
    const keys = statistics
      .keySeq()
      .map((name: string) => {
        return ColorStatistics.formatTitle(name);
      })
      .toArray();
    const values = statistics.valueSeq().toArray();
    const colors = statistics
      .keySeq()
      .map((color: string) => {
        return ColorStatistics.getColorFromMap(color);
      })
      .toArray();
    return [
      {
        x: keys,
        y: values,
        type: 'bar',
        marker: {
          color: colors,
        },
        width: 0.5,
        hoverinfo: 'y',
      },
    ];
  };

  private markerColor = (statistics: Immutable.Map<string, number>) => {
    return statistics
      .keySeq()
      .map((color: string) => {
        return ColorStatistics.getColorFromMap(color);
      })
      .toArray();
  };

  private images = (statistics: Immutable.Map<string, number>) => {
    return statistics
      .keySeq()
      .reduce((reduction: Immutable.List<any>, value: string, key: number, iter: Immutable.List<string>) => {
        const urlKey = Immutable.List<string>(value.split('_'))
          .map((value: string) => {
            if (value === 'blue') {
              return 'u';
            }
            return value[0];
          })
          .join('');
        return reduction.push({
          source: `${CoreAssets.iconHost()}/ci/${urlKey}_128px.png`,
          x: (key * 2 + 1) / (iter.size * 2),
          y: -0.2,
          sizex: 0.1,
          sizey: 0.1,
          xanchor: 'center',
          xref: 'paper',
          yanchor: 'bottom',
          yref: 'paper',
        });
      }, Immutable.List<any>())
      .toArray();
  };
}
