import * as React from 'react';
import * as FilterActions from '../../actions/FilterActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import displayCurrency from '../../helpers/currency_helper';
import { NumberFormat } from '../../helpers/fmt';
import history from '../../helpers/history';
import { MTGCardPage } from '../../models/mtg/MTGCardPage';
import { TagStatisticsItem } from '../../models/TagStatisticsItem';
import { User } from '../../models/Users';

interface IProps {
  tagRecord: TagStatisticsItem;
  me: User;
  dispatcher: Dispatcher;
  disabled?: boolean;
}

export const TagStatisticsCell = (props: IProps) => {
  const totalValue = props.tagRecord.get('totalValue');
  const totalCards = NumberFormat.commaSeparated(props.tagRecord.get('totalCards'));
  return (
    <div className="col-xs-12 col-sm-12 col-md-12 col-lg-6 col-xl-4" style={{ marginTop: '2rem' }}>
      <div className={`tag-statistics-container${props.disabled ? '--disabled' : ''}`} onClick={filterByTag.bind(this)}>
        <div className="tag-statistics-name">#{props.tagRecord.get('tag').get('name')}</div>
      </div>
      <div className="flex">
        <div className="flex grow-xs-1" style={{ marginRight: '1rem' }}>
          <div className="col-xs-12 col-sm-6" style={{ padding: 0 }}>
            <div className="info-heading">Total Cards</div>
            <div className="info-block">{totalCards}</div>
          </div>
        </div>
        <div className="flex grow-xs-1">
          <div className="col-xs-12 col-sm-6">
            <div className="info-heading">Total Value</div>
            <div className="info-block">
              {totalValue === undefined
                ? '-'
                : `${displayCurrency(
                    totalValue,
                    true,
                    props.me.get('preferences').get('localization').get('currency'),
                  )}`}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  async function filterByTag() {
    if (props.disabled) {
      return;
    }
    const cardPage = new MTGCardPage({ ownerUsername: props.me.get('username') });
    await FilterActions.overrideWithTag(props.tagRecord.get('tag'), cardPage, props.dispatcher);
    history.push('/collection');
  }
};
