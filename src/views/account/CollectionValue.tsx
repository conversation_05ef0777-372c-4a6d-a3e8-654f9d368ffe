// npm
import classNames from 'classnames';
import * as Immutable from 'immutable';
import moment from 'moment';
import * as React from 'react';
// Containers
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasStatistics from '../../containers/HasStatistics';
// Dispatcher
import Dispatcher from '../../dispatcher/Dispatcher';
// Helpers
import { symbolForCurrency } from '../../helpers/currency_helper';
import history from '../../helpers/history';
import { generateNTicks } from '../../helpers/plotly';
import isServerside from '../../helpers/serverside';
import { Snapshot } from '../../models/Snapshots';
import { StatComponentStatus } from '../../models/Statistics';
// Models
import { User } from '../../models/Users';
import { AccountHeading } from './AccountHeading';
// Components
import { ChartLoadingScreen } from './ChartLoadingScreen';

interface IProps {
  me: User;
  dispatcher: Dispatcher;
  snapshots: Immutable.List<Snapshot>;
  totalValue: number;
  status: StatComponentStatus;
  reload: () => Promise<void>;
}

interface IState {
  loaded: boolean;
}

export const CollectionValue = class extends React.Component<IProps, IState> {
  public refs: {
    [key: string]: Element;
    chartMount: HTMLElement;
  };

  private Plotly: any = isServerside() ? null : require('plotly.js');
  private handleWindowResize: EventListener | null;

  constructor(props: IProps & HasDispatcher.IProps & HasStatistics.IProps) {
    super(props);
    this.state = { loaded: false };
  }

  /**
   * @override
   */
  async componentDidMount() {
    // componentDidMount loads a lot of data so that it forces the plotly graph to re-render to the correct size.
    this.handleWindowResize = () => {
      if (this.props.status === StatComponentStatus.LOADED) {
        this.purge();
      }
    };
    window.addEventListener('resize', this.handleWindowResize);
  }

  /**
   * @override
   */
  componentWillUnmount() {
    if (this.handleWindowResize != null) {
      window.removeEventListener('resize', this.handleWindowResize);
    }
    this.handleWindowResize = null;
  }

  private plotConfig = {
    staticPlot: false,
    editable: false,
    scrollZoom: false,
    showTips: false,
    showLink: false,
    sendData: false,
    showSources: false,
    displayModeBar: false,
    modeBarButtons: false,
    logging: false,
  };

  private plotData = (xAxisData: number[], yAxisData: number[]) => {
    return [
      {
        x: xAxisData.reverse(),
        y: yAxisData.reverse(),
        mode: 'lines+markers',
        name: 'Collection Value',
        line: {
          color: 'rgb(72,53,135)',
        },
        marker: {
          size: 8,
        },
        hoverinfo: 'y',
        hoverformat: ',.2f',
        tickformat: ',.2s',
      },
    ];
  };

  private layout = (xAxisData: number[], yAxisData: number[], xAxisLabels: string[], currencySymbol: string) => {
    return {
      margin: {
        l: 80,
        r: 80,
        b: 25,
        t: 5,
        pad: 0,
      },
      font: {
        family: 'lato-regular',
        src: {
          url: '/fonts/lato-regular-webfont.svg#latoregular',
          format: 'svg',
        },
        size: 18,
        color: '#3e3e3e',
      },
      xaxis: {
        fixedrange: true,
        tickvals: xAxisData.reverse(),
        ticktext: xAxisLabels,
      },
      yaxis: {
        tickprefix: currencySymbol,
        autorange: true,
        automargin: true, // Stops y-axis label from colliding with the graph.
        tick0: 0,
        rangemode: 'tozero',
        fixedrange: true,
        nticks: generateNTicks(yAxisData),
      },
    };
  };

  public render() {
    if (this.props.status === StatComponentStatus.LOADED && !this.state.loaded) {
      this.renderChart(this.props.snapshots);
    }
    const localStatus =
      !this.state.loaded && this.props.status === StatComponentStatus.LOADED
        ? StatComponentStatus.LOADING
        : this.props.status;

    const chartMountClass = classNames('chart-mount', {
      'blur-overlay': !this.props.me.get('subscribed'),
      'plotly-loading': this.props.me.get('subscribed') && localStatus !== StatComponentStatus.LOADED,
    });
    return (
      <>
        {this.props.me.get('subscribed') ? (
          <AccountHeading
            title="Collection Value"
            spinnerActive={false}
            loading={localStatus === StatComponentStatus.LOADING}
            refresh={this.props.reload}
          />
        ) : (
          <div style={{ marginTop: '2rem' }} />
        )}
        <div className={`account-chart-container${localStatus !== StatComponentStatus.LOADED ? '--loading' : ''}`}>
          <div id="chart-mount" ref="chartMount" className={chartMountClass} />
          <ChartLoadingScreen status={localStatus} reload={this.props.reload} />
          {this.props.me.get('subscribed') || localStatus !== StatComponentStatus.LOADED ? null : (
            <div className="account-chart-overlay">
              <div className="col-xs-10 account-chart-text">
                <div className="heading-lg">Upgrade your account to access collection statistics</div>
                <div className="centered-list-container">
                  <ul className="centered-list">
                    <li>
                      <span style={{ fontWeight: 'bold' }}>Collection Value</span> - See how the value of your
                      collection changes over time
                    </li>
                    <li>
                      <span style={{ fontWeight: 'bold' }}>Tag Counts</span> - View the total cards and value for each
                      of your tags
                    </li>
                    <li>
                      <span style={{ fontWeight: 'bold' }}>Set Completion</span> - How close are you to completing a
                      full set of cards?
                    </li>
                    <li>
                      <span style={{ fontWeight: 'bold' }}>And More</span> - Satisfy your curiosity and gain insights
                      into your collection
                    </li>
                  </ul>
                </div>
              </div>
              {/* Acceptable use of in-line styling as this is a specific tweak for this page. */}
              <button className="button-primary" style={{ marginTop: '16px' }} onClick={this.onClickUpgrade.bind(this)}>
                Upgrade
              </button>
            </div>
          )}
        </div>
      </>
    );
  }

  private purge() {
    if (this.refs.chartMount && this.refs.chartMount.id !== '') {
      this.Plotly.purge(this.refs.chartMount.id);
    }
  }

  private renderChart(snapshots: Immutable.List<Snapshot>) {
    if (isServerside() || this.refs.chartMount === undefined || this.refs.chartMount.id === '') {
      return;
    }
    const today = moment().startOf('day');
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const dayOffset = today.toDate().getDay() + 1;
    const xAxisLabels = days.slice(dayOffset, 7).concat(days.slice(0, dayOffset));

    // There will always be seven data points so this is a constant.
    const xAxisData = [1, 2, 3, 4, 5, 6, 7];
    let yAxisData: number[] = [];
    if (this.props.me.get('subscribed')) {
      yAxisData = generateYAxis(
        today,
        snapshots,
        dayOffset,
        Number.parseFloat((this.props.totalValue / 100).toString()),
      );
    } else {
      yAxisData = [1146, 1076, 1072, 1010, 1028, 995, 940];
    }

    this.Plotly.newPlot(
      this.refs.chartMount.id,
      this.plotData(xAxisData, yAxisData),
      this.layout(
        xAxisData,
        yAxisData,
        xAxisLabels,
        symbolForCurrency(this.props.me.get('preferences').get('localization').get('currency')),
      ),
      this.plotConfig,
    );
    if (!this.state.loaded) {
      this.setState({ loaded: true });
    }
  }

  private onClickUpgrade(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    history.push('/settings/subscription');
  }
};

// This algorithm could be marginally better but the API will get rewritten at some point so its not worth the effort.
export function generateYAxis(
  today: moment.Moment,
  snapshots: Immutable.List<Snapshot>,
  dayOffset: number,
  todaysValue: number,
): number[] {
  const yAxisData = [0, 0, 0, 0, 0, 0, 0];
  const sixDaysAgo = today.subtract(6, 'days');
  // Gets all snapshots from now to six days ago.
  for (let i = 0; i < snapshots.size; i++) {
    const day = moment(snapshots.get(i).get('created_at')).startOf('day');
    if (day.isBefore(sixDaysAgo)) {
      break;
    }
    yAxisData[(7 + day.day() - dayOffset) % 7] = Number.parseFloat(
      (snapshots.get(i).get('card_value') / 100).toString(),
    );
  }
  // Adds today's value to beginning of the list and corrects order.
  yAxisData[6] = todaysValue;
  return yAxisData.reverse();
}
