import { formatNumber } from 'accounting';
import * as React from 'react';
import { Waypoint } from 'react-waypoint';
import * as StatisticsActions from '../../actions/StatisticsActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import { NumberFormat, TextFormat } from '../../helpers/fmt';
import importPlotly from '../../helpers/plotly';
import { ColorStatistics } from '../../models/ColorStatistics';
import { ManaStatistics } from '../../models/ManaStatistics';
import { StatComponentStatus, StatisticType } from '../../models/Statistics';
import { AccountHeading } from './AccountHeading';
import { ChartLoadingScreen } from './ChartLoadingScreen';
import { ManaDonutChart } from './ManaDonutChart';

const Plotly = importPlotly();

interface IProps {
  dispatcher: Dispatcher;
  manaStatistics: ManaStatistics;
  status: StatComponentStatus;
}

export class ManaStatisticsContainer extends React.Component<IProps> {
  constructor(props: IProps) {
    super(props);
    this.state = { status: StatComponentStatus.INACTIVE };
  }

  private async loadStatistics() {
    StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADING, StatisticType.MANA);
    await StatisticsActions.manaStatistics(this.props.dispatcher)
      .then(() => {
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADED, StatisticType.MANA);
      })
      .catch((err) => {
        err && console.error(err);
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.ERROR, StatisticType.MANA);
      });
  }

  private async waypointActivated() {
    if (this.props.status === StatComponentStatus.INACTIVE || this.props.status === StatComponentStatus.ERROR) {
      await this.loadStatistics.bind(this)();
    }
  }

  private async refresh(evt: React.SyntheticEvent<HTMLButtonElement, Event>) {
    evt.preventDefault();
    await this.loadStatistics.bind(this)();
  }

  private generatePills(): JSX.Element[] {
    return this.props.manaStatistics
      .toPlotlyData()
      .map((value: number, key: string) => {
        const percentage = `${((value / this.props.manaStatistics.get('totalMana')) * 100).toFixed(2)}%`;
        return (
          <div className="col-xs-4" style={{ marginBottom: '3rem' }} key={key}>
            <div
              className="chart-pill"
              style={{
                backgroundColor: ColorStatistics.getColorFromMap(key),
                marginTop: '0',
              }}
            >
              <div className="chart-pill__ratio">{this.props.manaStatistics === undefined ? '-' : percentage}</div>
              <div className="chart-pill__label">{TextFormat.capitalizeWord(key)}</div>
              <div
                className="chart-pill__hover"
                style={{
                  backgroundColor: ColorStatistics.getColorFromMap(key),
                }}
              >
                <div className="chart-pill__ratio">
                  {this.props.manaStatistics === undefined ? '-' : NumberFormat.commaSeparated(value)}
                </div>
                <div className="chart-pill__label">{TextFormat.capitalizeWord(key)}</div>
              </div>
            </div>
          </div>
        );
      })
      .toArray();
  }

  public render() {
    const message = this.props.manaStatistics.userHasBasicLands()
      ? `If you tapped all your basic lands, you would produce ${formatNumber(
          this.props.manaStatistics.get('totalMana'),
        )} mana`
      : 'You have no basic lands in your collection';
    return (
      <Waypoint onEnter={this.waypointActivated.bind(this)}>
        <div>
          <AccountHeading
            title="Mana"
            loading={this.props.status === StatComponentStatus.LOADING}
            refresh={this.refresh.bind(this)}
          />
          {this.props.status === StatComponentStatus.LOADED ? (
            <>
              <div className="paragraph-sm" style={{ marginBottom: '1rem' }}>
                {message}
              </div>
              <div className="row">
                <div
                  className={
                    this.props.manaStatistics.userHasBasicLands()
                      ? 'col-xs-12 col-sm-12 col-md-12 col-lg-5'
                      : 'col-xs-12'
                  }
                >
                  <ManaDonutChart
                    dispatcher={this.props.dispatcher}
                    manaStatistics={this.props.manaStatistics}
                    status={this.props.status}
                  />
                </div>
                {this.props.manaStatistics.userHasBasicLands() ? (
                  <div className="row col-md-12 col-lg-7" style={{ width: '100%', margin: 'auto 0' }}>
                    {this.generatePills.bind(this)()}
                  </div>
                ) : null}
              </div>
            </>
          ) : (
            <div className="statistics-non-plotly-error-container col-xs-12">
              <ChartLoadingScreen status={this.props.status} reload={this.loadStatistics.bind(this)} />
            </div>
          )}
        </div>
      </Waypoint>
    );
  }
}
