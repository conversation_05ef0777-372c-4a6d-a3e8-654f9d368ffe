import * as Immutable from 'immutable';
import * as React from 'react';
import { Waypoint } from 'react-waypoint';
import * as StatisticsActions from '../../actions/StatisticsActions';
// Containers, actions, and dispatcher
import Dispatcher from '../../dispatcher/Dispatcher';
import { generateNTicks } from '../../helpers/plotly';
import { StatComponentStatus, StatisticType } from '../../models/Statistics';
// Models
import { TabItem } from '../../models/TabItem';
import { TypeStat, TypeStatDisplay, TypeStatistics } from '../../models/TypeStatistics';
// Components
import SecondaryNavBar from '../components/SecondaryNavBar';
import { AccountHeading } from './AccountHeading';
import { TypeBreakdownChart } from './TypeBreakdownChart';

interface IProps {
  dispatcher: Dispatcher;
  currencySymbol: string;
  typeStatistics: TypeStatistics;
  status: StatComponentStatus;
}

interface IState {
  display: TypeStatDisplay;
}

export class TypeBreakdownContainer extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {
      display: TypeStatDisplay.QUANTITY,
    };
  }

  /**
   * @override
   */
  render() {
    const secondaryPages = Immutable.List<TabItem>([
      new TabItem({ title: TypeStatDisplay.QUANTITY }),
      new TabItem({ title: TypeStatDisplay.VALUE }),
    ]);
    const plotlyData = this.props.typeStatistics.toPlotlyData();
    const yAxis = this.generateYAxis(plotlyData);
    return (
      <Waypoint onEnter={this.waypointActivated.bind(this)}>
        <div>
          <AccountHeading
            title="Type Breakdown"
            loading={this.props.status === StatComponentStatus.LOADING}
            refresh={this.refresh.bind(this)}
          />
          {this.props.status === StatComponentStatus.LOADED ? (
            <div className="col-xs-12 flex vertical align-center">
              <div style={{ marginBottom: '1rem' }}>
                <SecondaryNavBar
                  pages={secondaryPages}
                  selectedPage={this.state.display}
                  displayName={(page: string) => {
                    return page;
                  }}
                  openPage={this.openPage.bind(this)}
                />
              </div>
            </div>
          ) : null}
          <TypeBreakdownChart
            dispatcher={this.props.dispatcher}
            data={this.data(yAxis, plotlyData)}
            layout={this.layout(yAxis)}
            reload={this.loadStatistics.bind(this)}
            status={this.props.status}
          />
        </div>
      </Waypoint>
    );
  }

  private openPage(page: string) {
    this.setState({ display: page as TypeStatDisplay });
  }

  private async waypointActivated() {
    if (this.props.status === StatComponentStatus.INACTIVE || this.props.status === StatComponentStatus.ERROR) {
      await this.loadStatistics.bind(this)();
    }
  }

  private async refresh(evt: React.SyntheticEvent<HTMLButtonElement, Event>) {
    evt.preventDefault();
    await this.loadStatistics.bind(this)();
  }

  private async loadStatistics() {
    StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADING, StatisticType.TYPE);
    await StatisticsActions.typesStatistics(this.props.dispatcher)
      .then(() => {
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADED, StatisticType.TYPE);
      })
      .catch((err: any) => {
        err && console.error(err);
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.ERROR, StatisticType.TYPE);
      });
  }

  private layout(yAxis: number[]) {
    let tickformat: string;
    if (this.state.display === TypeStatDisplay.QUANTITY) {
      tickformat = ',d';
    } else if (Math.max(...yAxis) < 1) {
      // Displays decimals for values less than $1 as SI units are nonsensical for <$1
      tickformat = ',.2f';
    } else {
      tickformat = ',.2s';
    }
    return {
      margin: {
        l: 80,
        r: 80,
        b: 25,
        t: 10,
        pad: 0,
      },
      font: {
        family: 'lato-bold',
        src: {
          url: '/fonts/lato-bold-webfont.svg#latobold',
          format: 'svg',
        },
        size: 18,
        color: '#3e3e3e',
      },
      showlegend: false,
      xaxis: {
        fixedrange: true,
        tick0: 0,
        dtick: 1,
      },
      yaxis: {
        tickprefix: this.state.display === TypeStatDisplay.QUANTITY ? '' : this.props.currencySymbol,
        hoverformat: this.state.display === TypeStatDisplay.QUANTITY ? ',d' : ',.2f',
        tickformat: tickformat,
        fixedrange: true,
        tickmode: 'auto',
        tick0: 0,
        dtick: 2,
        nticks: generateNTicks(yAxis),
        rangemode: 'tozero',
        autorange: true,
      },
      marker: {
        color: '#000000',
      },
    };
  }

  private data(yAxis: number[], plotlyData: Immutable.OrderedMap<string, TypeStat>) {
    const marker = {
      color: plotlyData
        .keySeq()
        .map((color: string) => {
          return TypeStatistics.typeKeyToColor.get(color);
        })
        .toArray(),
    };
    return [
      {
        name: this.state.display.toString(),
        x: plotlyData.keySeq().toArray(),
        y: yAxis,
        type: 'bar',
        marker: marker,
        hoverinfo: 'y',
      },
    ];
  }

  private generateYAxis(plotlyData: Immutable.OrderedMap<string, TypeStat>): number[] {
    if (this.state.display === TypeStatDisplay.QUANTITY) {
      return plotlyData
        .map((tuple: TypeStat) => {
          return tuple.get('totalCards');
        })
        .toArray();
    } else {
      return plotlyData
        .map((tuple: TypeStat) => {
          return tuple.get('totalValue') / 100;
        })
        .toArray();
    }
  }
}
