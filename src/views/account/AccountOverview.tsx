import refresh from '@iconify/icons-ic/refresh';
import { Icon } from '@iconify/react';
import { formatNumber } from 'accounting';
import * as Immutable from 'immutable';
import * as React from 'react';
import { Waypoint } from 'react-waypoint';
import * as SnapshotActions from '../../actions/SnapshotActions';
import * as StatisticsActions from '../../actions/StatisticsActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import displayCurrency from '../../helpers/currency_helper';
import history from '../../helpers/history';
import { OverviewStatistics } from '../../models/OverviewStatistics';
import { Snapshot } from '../../models/Snapshots';
import { StatComponentStatus, StatisticType } from '../../models/Statistics';
import { User } from '../../models/Users';
import { ChartLoadingScreen } from './ChartLoadingScreen';
import { CollectionValue } from './CollectionValue';

interface IProps {
  overviewStatistics: OverviewStatistics;
  overviewStatus: StatComponentStatus;
  me: User;
  dispatcher: Dispatcher;
  snapshots: Immutable.List<Snapshot>;
}

export const AccountOverview = class extends React.Component<IProps> {
  constructor(props: IProps) {
    super(props);
  }

  private renderProfileImage() {
    const src = this.props.me.get('avatar').get('large');
    // Good use of in-line styling.
    return <div className="account-profile-image" style={{ backgroundImage: 'url(' + src + ')' }} />;
  }

  private onClickEditProfile(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    history.push('/settings/account');
  }

  private onClickUpgradeToSee(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    history.push('/settings/subscription');
  }
  private async waypointActivated() {
    if (
      this.props.overviewStatus === StatComponentStatus.INACTIVE ||
      this.props.overviewStatus === StatComponentStatus.ERROR
    ) {
      await this.loadStatistics.bind(this)();
    }
  }

  private async loadStatistics() {
    await StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADING, StatisticType.OVERVIEW);
    Promise.all([StatisticsActions.overview(this.props.dispatcher), SnapshotActions.snapshots(this.props.dispatcher)])
      .catch((err) => {
        err && console.error(err);
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.ERROR, StatisticType.OVERVIEW);
      })
      .then(() => {
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADED, StatisticType.OVERVIEW);
      });
  }

  private refreshAll() {
    StatisticsActions.refreshAll(this.props.dispatcher);
    this.loadStatistics.bind(this)();
  }

  render() {
    const displayLoadingData =
      this.props.overviewStatus === StatComponentStatus.INACTIVE ||
      this.props.overviewStatus === StatComponentStatus.LOADING;

    const totalCount = displayLoadingData ? '-' : formatNumber(this.props.overviewStatistics.get('totalCount'));

    const accountValue = displayCurrency(
      this.props.overviewStatistics.get('totalValue'),
      true,
      this.props.me.get('preferences').get('localization').get('currency'),
    );

    const deckValue = displayCurrency(
      this.props.overviewStatistics.get('deckValue'),
      true,
      this.props.me.get('preferences').get('localization').get('currency'),
    );
    return (
      <Waypoint onEnter={this.waypointActivated.bind(this)}>
        <div>
          <div className="row account-container">
            {this.props.overviewStatus === StatComponentStatus.ERROR ? (
              <div className="statistics-non-plotly-error-container col-xs-12">
                <ChartLoadingScreen
                  status={this.props.overviewStatus}
                  reload={this.loadStatistics.bind(this)}
                  customLoadingScreen={true}
                />
              </div>
            ) : (
              <>
                <div className="col-xs-12 col-md-2" style={{ marginTop: '1rem', padding: 0 }}>
                  {this.renderProfileImage()}
                  <button
                    className="button-primary"
                    style={{ marginTop: '1rem', width: '100%' }}
                    onClick={this.onClickEditProfile.bind(this)}
                  >
                    Edit Profile
                  </button>
                </div>
                <div className="account-heading col-xs-12 col-md-10">
                  <div className="flex col-xs-12">
                    <div className="account-username">@{this.props.me.get('username')}</div>
                    <button
                      className="button-primary"
                      style={{ margin: 'auto 0 auto auto' }}
                      onClick={this.refreshAll.bind(this)}
                    >
                      <div className="flex center">
                        <div style={{ marginTop: '0.1rem' }}>
                          <Icon height={'18px'} width={'18px'} icon={refresh} />
                        </div>
                        <span style={{ margin: '0 0.5rem' }}>Refresh All</span>
                      </div>
                    </button>
                  </div>
                  <div className="flex" style={{ marginTop: '1rem' }}>
                    <div className="col-xs-4">
                      <div className="info-heading">Cards</div>
                      <div className="info-block">{totalCount}</div>
                    </div>
                    {this.props.me.get('subscribed') ? (
                      <div className="col-xs-4">
                        <div className="info-heading">Collection Value</div>
                        <div className="info-block">{displayLoadingData ? '-' : accountValue}</div>
                      </div>
                    ) : (
                      <div className="col-xs-4">
                        <div className="info-heading">Value</div>
                        <div className="info-block--clickable" onClick={this.onClickUpgradeToSee.bind(this)}>
                          Upgrade
                        </div>
                      </div>
                    )}
                    <div className="col-xs-4">
                      <div className="info-heading">Tags</div>
                      <div className="info-block">
                        {displayLoadingData ? '-' : formatNumber(this.props.overviewStatistics.get('tagCount'))}
                      </div>
                    </div>
                  </div>
                  <div className="flex" style={{ marginTop: '1rem' }}>
                    <div className="col-xs-4">
                      <div className="info-heading">Decks</div>
                      <div className="info-block">{this.props.overviewStatistics.get('deckCount')}</div>
                    </div>
                    {this.props.me.get('subscribed') ? (
                      <div className="col-xs-4">
                        <div className="info-heading">Deck Value</div>
                        <div className="info-block">{displayLoadingData ? '-' : deckValue}</div>
                      </div>
                    ) : (
                      <div className="col-xs-4">
                        <div className="info-heading">Deck Value</div>
                        <div className="info-block--clickable" onClick={this.onClickUpgradeToSee.bind(this)}>
                          Upgrade
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
          <CollectionValue
            me={this.props.me}
            dispatcher={this.props.dispatcher}
            totalValue={this.props.overviewStatistics.get('totalValue')}
            snapshots={this.props.snapshots}
            status={this.props.overviewStatus}
            reload={this.loadStatistics.bind(this)}
          />
        </div>
      </Waypoint>
    );
  }
};
