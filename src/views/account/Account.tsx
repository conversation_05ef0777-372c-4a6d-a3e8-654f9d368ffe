// Node Modules
import * as React from 'react';
import { Helmet } from 'react-helmet';
// Hases
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import * as HasStatistics from '../../containers/HasStatistics';
import { symbolForCurrency } from '../../helpers/currency_helper';
// Helpers
import isServerside from '../../helpers/serverside';
import { StatisticType } from '../../models/Statistics';
import { Footer } from '../components/Footer';
// Other components
import { Navbar } from '../components/Navbar';
// Account components
import { AccountOverview } from './AccountOverview';
import { ColorBreakdownContainer } from './ColorBreakdownContainer';
import { ManaStatisticsContainer } from './ManaStatisticsContainer';
import { MostValuable } from './MostValuable';
import { SetCompletionTable } from './SetCompletionTable';
import { TagStatisticsTable } from './TagStatisticsTable';
import { TypeBreakdownContainer } from './TypeBreakdownContainer';

export const Account = HasMe.Attach<HasDispatcher.IProps>(
  HasStatistics.Attach<HasDispatcher.IProps & HasMe.IProps>(
    class extends React.Component<HasStatistics.IProps & HasDispatcher.IProps & HasMe.IProps> {
      /**
       * @override
       */
      constructor(props: HasStatistics.IProps & HasDispatcher.IProps & HasMe.IProps) {
        super(props);
      }

      /**
       * @override
       */
      render() {
        if (isServerside()) {
          Helmet.renderStatic();
        }

        return (
          <div>
            <Helmet>
              <title>CardCastle: Statistics</title>
            </Helmet>
            <Navbar dispatcher={this.props.dispatcher} me={this.props.me} selection="statistics" isRibbon={true} />
            <div className="section has-footer has-navbar">
              <div className="container-fluid">
                <AccountOverview
                  me={this.props.me}
                  overviewStatistics={this.props.statistics.get(StatisticType.OVERVIEW).get('statisticsRecord')}
                  overviewStatus={this.props.statistics.get(StatisticType.OVERVIEW).get('status')}
                  dispatcher={this.props.dispatcher}
                  snapshots={this.props.snapshots}
                />
                <div className="col-xs-12">
                  {this.props.me.get('subscribed') ? (
                    <>
                      <SetCompletionTable
                        setCompletionMap={this.props.statistics
                          .get(StatisticType.SET_COMPLETION)
                          .get('statisticsRecord')}
                        status={this.props.statistics.get(StatisticType.SET_COMPLETION).get('status')}
                        dispatcher={this.props.dispatcher}
                        me={this.props.me}
                      />
                      <ColorBreakdownContainer
                        dispatcher={this.props.dispatcher}
                        colorStatistics={this.props.statistics.get(StatisticType.COLOR).get('statisticsRecord')}
                        status={this.props.statistics.get(StatisticType.COLOR).get('status')}
                      />
                      <TypeBreakdownContainer
                        dispatcher={this.props.dispatcher}
                        typeStatistics={this.props.statistics.get(StatisticType.TYPE).get('statisticsRecord')}
                        status={this.props.statistics.get(StatisticType.TYPE).get('status')}
                        currencySymbol={symbolForCurrency(
                          this.props.me.get('preferences').get('localization').get('currency'),
                        )}
                      />
                      <ManaStatisticsContainer
                        manaStatistics={this.props.statistics.get(StatisticType.MANA).get('statisticsRecord')}
                        status={this.props.statistics.get(StatisticType.MANA).get('status')}
                        dispatcher={this.props.dispatcher}
                      />
                      <TagStatisticsTable
                        dispatcher={this.props.dispatcher}
                        me={this.props.me}
                        tagCompletionMap={this.props.statistics.get(StatisticType.TAGS).get('statisticsRecord')}
                        status={this.props.statistics.get(StatisticType.TAGS).get('status')}
                      />
                      <MostValuable
                        dispatcher={this.props.dispatcher}
                        cardGroup={this.props.statistics.get(StatisticType.MOST_VALUABLE).get('statisticsRecord')}
                        status={this.props.statistics.get(StatisticType.MOST_VALUABLE).get('status')}
                        currencySymbol={symbolForCurrency(
                          this.props.me.get('preferences').get('localization').get('currency'),
                        )}
                      />
                    </>
                  ) : null}
                </div>
              </div>
            </div>
            <Footer />
          </div>
        );
      }
    },
  ),
);
