import * as Immutable from 'immutable';
import moment from 'moment';
import React from 'react';
import { components, InputActionMeta, OptionsType } from 'react-select';
import CreatableSelect from 'react-select/creatable';
import { cardSetFromApi } from '../../api/CardSets';
import * as request from '../../api/Requests';
import Dispatcher from '../../dispatcher/Dispatcher';
import { CardSet } from '../../models/CardSets';
import { CardSetSuggestion } from '../components/react-select/CardSetSuggestion';
import { SetSearchSuggestion, SetSearchType } from '../components/react-select/SetSearchSuggestion';
import {
  formatStringSuggestion,
  SetStringFilterSuggestion,
} from '../components/react-select/SetStringFilterSuggestion';
import { ClearIndicator, LoadingMessage, NoOptionsMessage, styleOverride } from '../shared/ReactSelectHelper';

interface IProps {
  query?: string;
  dispatcher: Dispatcher;
  updateQuery: (query?: string) => void;
  updateTarget: (query?: string, set?: CardSet) => void;
}

interface IState {
  reactSelectValue: any;
  forceMenuOpen: boolean;
  isLoading: boolean;
  options?: OptionsType<SetSearchSuggestion>;
  savedQueries?: OptionsType<SetStringFilterSuggestion>;
}

const SingleValueCard = (props: any) => <components.SingleValue {...props}>{props.data.label}</components.SingleValue>;

export default class SetSearcher extends React.Component<IProps, IState> {
  private readonly searchBox: React.RefObject<CreatableSelect<SetSearchSuggestion>>;

  constructor(props: IProps) {
    super(props);
    this.searchBox = React.createRef<CreatableSelect<SetSearchSuggestion>>();
    this.state = {
      reactSelectValue: undefined,
      isLoading: false,
      // Set to true when set is value, otherwise menu automatically closes until next user input.
      forceMenuOpen: false,
    };
  }

  private lastQuery: request.Request;
  private lastQueryTime: Date = new Date();

  render() {
    const query = this.state.reactSelectValue ? undefined : this.props.query;
    return (
      <CreatableSelect
        ref={this.searchBox}
        components={{
          SingleValue: SingleValueCard,
          ClearIndicator: ClearIndicator,
          NoOptionsMessage,
          LoadingMessage,
        }}
        formatOptionLabel={this.formatOptionLabel.bind(this)}
        styles={styleOverride}
        className={'react-select-thin'}
        classNamePrefix="react-select-thin"
        placeholder="Search sets by name"
        onInputChange={this.onChangeQuery.bind(this)}
        onChange={this.onChangeSuggestion.bind(this)}
        onBlur={() => this.onBlurFocus.bind(this, query)}
        onFocus={() => this.onBlurFocus.bind(this, query)}
        inputValue={query}
        noOptionsMessage={this.noOptionsHandler}
        options={this.state.options === undefined ? [] : this.state.options}
        value={this.state.reactSelectValue}
        filterOption={() => true}
        menuIsOpen={this.state.forceMenuOpen ? true : undefined}
        isLoading={this.state.isLoading}
        onCreateOption={this.onCreateOption.bind(this)}
        createOptionPosition={'first'}
        isClearable={true}
      />
    );
  }

  private formatOptionLabel(option: SetSearchSuggestion) {
    if (option['__isNew__'] && typeof option['value'] === 'string') {
      return formatStringSuggestion(option['value']);
    }
    return option.formatOptionLabel();
  }

  private onCreateOption(inputValue: string) {
    this.props.updateTarget(inputValue);
    const newSuggestion = new SetStringFilterSuggestion(inputValue);
    this.setState({
      reactSelectValue: new SetSearchSuggestion(new SetStringFilterSuggestion(inputValue)),
      savedQueries: [newSuggestion],
    });
  }

  private onChangeSuggestion(value: any) {
    // Resetting options here prevents users accidentally adding multiple sets in quick succession.
    this.setState({
      options: [],
    });
    if (value === undefined || value === null) {
      this.setState({
        reactSelectValue: undefined,
        forceMenuOpen: false,
      });
      this.props.updateTarget('', undefined);
      return;
    }
    // Continues if not undefined or null
    let query = '';
    if (value['type'] === SetSearchType.STRING) {
      this.props.updateTarget(value['value']);
      query = value['value'];
    } else {
      this.props.updateTarget(undefined, value['value']);
    }
    this.setState({
      reactSelectValue: value,
      forceMenuOpen: false,
    });
  }

  focus() {
    if (this.searchBox.current) {
      this.searchBox.current.focus();
    }
  }

  private onChangeQuery(query: string, evt: InputActionMeta) {
    if (evt.action === 'input-blur' || evt.action === 'menu-close') {
      return;
    }

    // Partial state reset
    // Very important that this is not deleted, will cause unacceptable lag otherwise.
    this.setState({
      forceMenuOpen: false,
      options: [],
    });
    this.props.updateQuery(query);

    // Abort previous query
    this.lastQuery && this.lastQuery.abort();
    this.lastQueryTime = new Date();

    this.handleSetSearch(query);
  }

  private onBlurFocus(query: string, evt: React.SyntheticEvent<HTMLInputElement>) {
    this.props.updateQuery(query);
    this.setState({
      forceMenuOpen: false,
    });
  }

  private noOptionsHandler(wrapper: { inputValue: string }) {
    const query = wrapper.inputValue;
    if (query.length < 3) {
      return null;
    } else {
      return 'No Results';
    }
  }

  private async handleSetSearch(setName: string) {
    // We only want to run a request every 150ms at most otherwise we spam the
    // network and everything slows down
    setTimeout(() => {
      const lastQueryTime = moment(this.lastQueryTime);
      if (moment(new Date()).diff(lastQueryTime, 'milliseconds', true) > 150) {
        // This logic was copied from search() in CardSets.ts and adapted to work with this.lastQuery.
        // TODO: Rewrite search() in CardSets.ts to handle this sort of thing.
        const url = '/api/v3/card_sets?query=' + setName;
        this.lastQuery = request.get(url);
        this.lastQuery
          .end()
          .then((res) => {
            const suggestions = Immutable.List<SetSearchSuggestion>(
              Immutable.List<CardSet>(res.body.card_sets.map((item: any) => cardSetFromApi(item))).map(
                (cardSet: CardSet) => {
                  return new SetSearchSuggestion(new CardSetSuggestion(cardSet));
                },
              ),
            ).toArray() as OptionsType<SetSearchSuggestion>;
            this.setState({
              options: suggestions,
              isLoading: false,
            });
          })
          .catch((err) => {
            console.error('catch', err);
            this.setState({
              options: undefined,
              isLoading: false,
            });
          });
      }
    }, 200);
  }
}
