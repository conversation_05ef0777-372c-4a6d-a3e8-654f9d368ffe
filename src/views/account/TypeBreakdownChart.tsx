import * as React from 'react';
import Dispatcher from '../../dispatcher/Dispatcher';
import importPlotly from '../../helpers/plotly';
import isServerside from '../../helpers/serverside';
import { StatComponentStatus } from '../../models/Statistics';
import { ChartLoadingScreen } from './ChartLoadingScreen';

const Plotly = importPlotly();

interface IProps {
  dispatcher: Dispatcher;
  data: any;
  layout: any;
  status: StatComponentStatus;
  reload: () => void;
}

export class TypeBreakdownChart extends React.Component<IProps> {
  private handleWindowResize: EventListener | null;
  private readonly titleRef: React.RefObject<HTMLDivElement>;

  constructor(props: IProps) {
    super(props);
    this.titleRef = React.createRef<HTMLDivElement>();
  }

  public componentDidMount() {
    this.renderChart();
    this.handleWindowResize = () => {
      if (this.titleRef.current && this.titleRef.current.id !== '') {
        Plotly.purge(this.titleRef.current.id);
      }
      this.renderChart();
    };
    window.addEventListener('resize', this.handleWindowResize);
  }

  public componentWillUnmount() {
    if (this.handleWindowResize != null) window.removeEventListener('resize', this.handleWindowResize);
    this.handleWindowResize = null;
  }

  public componentDidUpdate() {
    this.renderChart();
  }

  public render() {
    return (
      <div className="row">
        <div className="col-xs-12 flex vertical align-center">
          <div
            className={`account-chart-container${this.props.status !== StatComponentStatus.LOADED ? '--loading' : ''}`}
            style={{ width: '100%' }}
          >
            <ChartLoadingScreen status={this.props.status} reload={this.props.reload} />
            {this.props.status === StatComponentStatus.LOADED ? (
              <div
                style={{
                  position: 'relative',
                  width: '100%',
                  minWidth: '0',
                  maxWidth: '100%',
                  height: '400px',
                  maxHeight: '400px',
                }}
              >
                <div
                  style={{ width: '100%', maxWidth: '100%', height: '100%', maxHeight: '100%' }}
                  ref={this.titleRef}
                />
              </div>
            ) : null}
          </div>
        </div>
      </div>
    );
  }
  private renderChart() {
    if (isServerside()) {
      return;
    }

    const config: any = {
      staticPlot: false,
      editable: false,
      scrollZoom: false,
      showTips: false,
      showLink: false,
      sendData: false,
      showSources: false,
      displayModeBar: false,
      modeBarButtons: false,
      logging: false,
    };

    if (this.titleRef.current) {
      Plotly.newPlot(this.titleRef.current, this.props.data, this.props.layout, config);
    }
  }
}
