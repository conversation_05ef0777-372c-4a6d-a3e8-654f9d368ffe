import warning from '@iconify/icons-ic/round-warning-amber';
import { Icon } from '@iconify/react';
import * as React from 'react';
import { StatComponentStatus } from '../../models/Statistics';

interface IProps {
  status: StatComponentStatus;
  reload: () => void;
  customLoadingScreen?: boolean;
}

export const ChartLoadingScreen = (props: IProps) => {
  switch (props.status) {
    case StatComponentStatus.LOADING:
    case StatComponentStatus.INACTIVE:
      const customLoadingScreen = props.customLoadingScreen === undefined ? false : props.customLoadingScreen;
      if (customLoadingScreen) {
        return null;
      }
      return (
        <div className="account-chart-overlay">
          <div className="flex">
            <div className="spinner-sm" />
          </div>
        </div>
      );
    case StatComponentStatus.ERROR:
      return (
        <div className="account-chart-overlay">
          <div className="flex">
            <h1 className="heading-md">
              Error
              <Icon icon={warning} style={{ margin: '0 0.25rem -0.25rem 0.25rem' }} />
              Something went wrong while loading
            </h1>
          </div>
          <button className="button-primary" onClick={props.reload}>
            Reload
          </button>
        </div>
      );
    case StatComponentStatus.LOADED:
      return null;
  }
};
