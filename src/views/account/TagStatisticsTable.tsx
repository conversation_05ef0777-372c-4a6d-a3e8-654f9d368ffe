// npm
import sort from '@iconify/icons-ic/sort';
import classNames from 'classnames';
import * as Immutable from 'immutable';
import * as React from 'react';
import { Waypoint } from 'react-waypoint';
// Actions and Dispatcher
import * as StatisticsActions from '../../actions/StatisticsActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import {
  TagStatisticsOrderingCodes,
  TagStatisticsSorting,
  TagStatisticsSortingCodes,
  type TagStatisticsSortingKey,
} from '../../models/sorting/TagStatisticsSorting';
import { StatComponentStatus, StatisticType } from '../../models/Statistics';
import { Tag } from '../../models/Tags';
// Models
import { TagStatisticsItem } from '../../models/TagStatisticsItem';
import { User } from '../../models/Users';
// Iconify
import { IconSelect } from '../components/IconSelect';
import { Placeholder } from '../components/Placeholder';
import { AccountHeading } from './AccountHeading';
import { ChartLoadingScreen } from './ChartLoadingScreen';
// Components
import { TagStatisticsCell } from './TagStatisticsCell';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  tagCompletionMap: Immutable.OrderedMap<string, TagStatisticsItem>;
  status: StatComponentStatus;
}

interface IState {
  currentSearchQuery: string;
  tagSearchQuery: string;
  tagStatisticsSorting: TagStatisticsSorting;
}

export class TagStatisticsTable extends React.Component<IProps, IState> {
  /**
   * @override
   */
  constructor(props: IProps) {
    super(props);
    this.state = {
      currentSearchQuery: '',
      tagSearchQuery: '',
      tagStatisticsSorting: TagStatisticsSorting.NAME_ASC,
    };
  }

  /**
   * @override
   */
  render() {
    const buttonClass = classNames({
      'button-primary': this.props.status === StatComponentStatus.LOADED,
      'button-grey': this.props.status !== StatComponentStatus.LOADED,
    });
    const loading =
      this.props.status === StatComponentStatus.INACTIVE || this.props.status === StatComponentStatus.LOADING;
    return (
      <Waypoint onEnter={this.waypointActivated.bind(this)}>
        <div>
          <AccountHeading title="Tags" loading={loading} refresh={this.refresh.bind(this)} />
          {this.props.status === StatComponentStatus.ERROR ? (
            <div className="statistics-non-plotly-error-container">
              <ChartLoadingScreen
                status={this.props.status}
                reload={this.refresh.bind(this)}
                customLoadingScreen={true}
              />
            </div>
          ) : (
            <>
              <form className="row">
                <IconSelect
                  className="icon-container col-xs-12 col-sm-6 col-md-4"
                  icon={sort}
                  value={this.state.tagStatisticsSorting}
                  onChange={this.onChangeTagSorting.bind(this)}
                  disabled={loading}
                >
                  <>
                    <option disabled>Sorting</option>
                    {Object.keys(TagStatisticsSorting).map((key) => (
                      <option key={key} value={TagStatisticsSorting[key as TagStatisticsSortingKey]}>
                        {TagStatisticsSorting[key as TagStatisticsSortingKey]}
                      </option>
                    ))}
                  </>
                </IconSelect>
                <div className="col-xs-12 col-sm-6 col-md-4" style={{ marginBottom: '1rem' }}>
                  <input
                    className="advanced-search-input"
                    type="text"
                    placeholder="Search tags by name"
                    value={this.state.currentSearchQuery}
                    onChange={this.updateQuery.bind(this)}
                    onSubmit={this.onSubmit.bind(this)}
                  />
                </div>
                <div className="col-xs-12 col-sm-6 col-md-4" style={{ marginBottom: '1rem' }}>
                  <button
                    className={buttonClass}
                    disabled={loading}
                    style={{ height: '3rem' }}
                    onClick={this.submitQuery.bind(this)}
                  >
                    Search
                  </button>
                </div>
              </form>
              <div className="row">
                {loading ? this.generateTagPlaceholder() : this.generateTagComponents(this.props.tagCompletionMap)}
              </div>
            </>
          )}
        </div>
      </Waypoint>
    );
  }

  private onChangeTagSorting(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    const tagStatisticsSorting = evt.currentTarget.value as TagStatisticsSorting;
    this.setState(
      {
        tagStatisticsSorting: tagStatisticsSorting,
      },
      async () => {
        await this.onUpdateTagCompletion.bind(this)(tagStatisticsSorting, this.state.tagSearchQuery);
      },
    );
  }

  private onSubmit(query: string) {
    this.setState(
      {
        tagSearchQuery: query,
      },
      () => {
        this.onUpdateTagCompletion.bind(this)(this.state.tagStatisticsSorting, query);
      },
    );
  }

  private async waypointActivated() {
    if (this.props.status === StatComponentStatus.INACTIVE || this.props.status === StatComponentStatus.ERROR) {
      this.onUpdateTagCompletion(TagStatisticsSorting.NAME_ASC, '');
    }
  }

  private async refresh(evt: React.SyntheticEvent<HTMLButtonElement, Event>) {
    evt.preventDefault();
    await this.onUpdateTagCompletion.bind(this)(this.state.tagStatisticsSorting, this.state.tagSearchQuery);
  }

  private async onUpdateTagCompletion(tagStatisticsSorting: TagStatisticsSorting, searchQuery: string) {
    StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADING, StatisticType.TAGS);
    StatisticsActions.tagCounts(
      this.props.dispatcher,
      searchQuery,
      10,
      TagStatisticsSortingCodes[tagStatisticsSorting],
      TagStatisticsOrderingCodes[tagStatisticsSorting],
    )
      .then(() => {
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADED, StatisticType.TAGS);
      })
      .catch((err: any) => {
        err && console.error(err);
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.ERROR, StatisticType.TAGS);
      });
  }

  private generateTagPlaceholder() {
    const placeholder = new TagStatisticsItem({ tag: new Tag({ name: 'Loading...' }) });
    let components = Immutable.List<JSX.Element>();
    for (let i = 0; i < 10; i++) {
      components = components.push(
        <TagStatisticsCell
          key={i}
          tagRecord={placeholder}
          me={this.props.me}
          dispatcher={this.props.dispatcher}
          disabled={true}
        />,
      );
    }
    return components.toArray();
  }

  private generateTagComponents(tagStatMap: Immutable.OrderedMap<string, TagStatisticsItem>) {
    if (tagStatMap.size === 0) {
      if (this.state.tagSearchQuery === '') {
        return (
          <Placeholder
            button={'Show Collection'}
            href="/collection"
            message={'You have no tags in your collection. Why not add some?'}
          />
        );
      } else {
        return <Placeholder button={null} message={'No tags matching query found'} />;
      }
    }
    return tagStatMap
      .map((value: TagStatisticsItem) => {
        return (
          <TagStatisticsCell
            key={value.get('tag').get('name')}
            tagRecord={value}
            me={this.props.me}
            dispatcher={this.props.dispatcher}
          />
        );
      })
      .toArray();
  }

  private updateQuery(evt: any) {
    evt.preventDefault();
    this.setState({ currentSearchQuery: evt.currentTarget.value as string });
  }

  private submitQuery(evt: any) {
    evt.preventDefault();
    this.onSubmit(this.state.currentSearchQuery);
  }
}
