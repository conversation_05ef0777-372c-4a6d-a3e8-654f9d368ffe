import refresh from '@iconify/icons-ic/refresh';
import { Icon } from '@iconify/react';
import * as React from 'react';

interface IProps {
  title: string;
  loading: boolean;
  refresh: (evt: React.SyntheticEvent<HTMLButtonElement>) => void;
  spinnerActive?: boolean;
}

export const AccountHeading = (props: IProps) => {
  return (
    <div className="account-subheading">
      <div className="flex">
        <h1 className="heading-md">{props.title}</h1>
        {props.spinnerActive && props.loading ? (
          <div className="account-spinner-container">
            <div className="spinner-sm" />
          </div>
        ) : null}
        <button
          className={props.loading ? 'button-grey' : 'button-primary'}
          style={{ margin: 'auto 0 auto auto' }}
          onClick={props.refresh}
        >
          <div className="flex center">
            <div style={{ marginTop: '0.1rem' }}>
              <Icon height={'18px'} width={'18px'} icon={refresh} />
            </div>
            <span style={{ margin: '0 0.5rem' }}>Refresh</span>
          </div>
        </button>
      </div>
    </div>
  );
};
