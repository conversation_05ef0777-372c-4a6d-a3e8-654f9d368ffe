import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../dispatcher/Dispatcher';
import importPlotly from '../../helpers/plotly';
import isServerside from '../../helpers/serverside';
import { ColorStatistics } from '../../models/ColorStatistics';
import { ManaStatistics } from '../../models/ManaStatistics';
import { StatComponentStatus } from '../../models/Statistics';

const Plotly = importPlotly();

interface IProps {
  dispatcher: Dispatcher;
  manaStatistics: ManaStatistics;
  status: StatComponentStatus;
}

export class ManaDonutChart extends React.Component<IProps> {
  private handleWindowResize: EventListener | null;
  private readonly titleRef: React.RefObject<HTMLDivElement>;

  constructor(props: IProps) {
    super(props);
    this.titleRef = React.createRef<HTMLDivElement>();
    this.state = {};
  }

  public componentDidMount() {
    this.renderChart();
    this.handleWindowResize = () => {
      if (this.titleRef.current && this.titleRef.current.id !== '') {
        Plotly.purge(this.titleRef.current.id);
      }
      this.renderChart();
    };
    window.addEventListener('resize', this.handleWindowResize);
  }

  public componentWillUnmount() {
    if (this.handleWindowResize != null) window.removeEventListener('resize', this.handleWindowResize);
    this.handleWindowResize = null;
  }

  public componentDidUpdate() {
    this.renderChart();
  }

  public render() {
    return (
      <div
        className={`account-chart-container${this.props.status !== StatComponentStatus.LOADED ? '--loading' : ''}`}
        style={{ width: '100%' }}
      >
        <div
          style={{
            position: 'relative',
            width: '100%',
            minWidth: '0',
            maxWidth: '100%',
            height: '30rem',
            maxHeight: '30rem',
          }}
        >
          <div style={{ width: '100%', maxWidth: '100%', height: '100%', maxHeight: '100%' }} ref={this.titleRef} />
        </div>
      </div>
    );
  }

  private renderChart() {
    if (isServerside()) {
      return;
    }

    const plotlyData = this.props.manaStatistics.userHasBasicLands()
      ? this.props.manaStatistics.toPlotlyData()
      : Immutable.Map<string, number>({ empty: 1 });
    const keys = plotlyData
      .keySeq()
      .toJS()
      .map((key: string) => key.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()));
    const values = plotlyData.valueSeq().toJS();
    const textTemplates = this.props.manaStatistics.userHasBasicLands()
      ? '<b>%{label}</b><br>%{value:,.0f}<br>%{percent:.2%}<extra></extra>'
      : 'You have no basic lands in your collection<extra></extra>';
    const data = [
      {
        labels: keys,
        values: values,
        type: 'pie',
        marker: {
          colors: plotlyData
            .keySeq()
            .map((color: string) => {
              if (color === 'empty') {
                return '#a9a9a9';
              }
              return ColorStatistics.getColorFromMap(color);
            })
            .toArray(),
        },
        domain: {
          x: [0, 1],
          y: [0, 1],
        },
        hovertemplate: textTemplates,
        textinfo: 'none',
        hole: 0.6,
      },
    ];
    const layout = {
      margin: {
        l: 80,
        r: 80,
        b: 25,
        t: 5,
        pad: 0,
      },
      font: {
        family: 'lato-bold',
        src: {
          url: '/fonts/lato-bold-webfont.svg#latobold',
          format: 'svg',
        },
        size: 18,
        color: '#3e3e3e',
      },
      showlegend: false,
    };

    const config: any = {
      staticPlot: false,
      editable: false,
      scrollZoom: false,
      showTips: false,
      showLink: false,
      sendData: false,
      showSources: false,
      displayModeBar: false,
      modeBarButtons: false,
      logging: false,
    };

    if (this.titleRef.current) {
      Plotly.newPlot(this.titleRef.current, data, layout, config);
    }
  }
}
