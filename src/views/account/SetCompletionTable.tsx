// npm
import sort from '@iconify/icons-ic/sort';
import classNames from 'classnames';
import * as Immutable from 'immutable';
import * as React from 'react';
import { Waypoint } from 'react-waypoint';
// Actions and Dispatcher
import * as StatisticsActions from '../../actions/StatisticsActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import { CardSet } from '../../models/CardSets';
// Models
import { SetCompletion } from '../../models/SetCompletion';
import {
  SetCompletionOrderingCodes,
  SetCompletionSorting,
  SetCompletionSortingCodes,
  type SetCompletionSortingKey,
} from '../../models/sorting/SetCompletionSorting';
import { StatComponentStatus, StatisticType } from '../../models/Statistics';
import { User } from '../../models/Users';
import { Checkbox } from '../components/Checkbox';
// Iconify
import { IconSelect } from '../components/IconSelect';
import { Placeholder } from '../components/Placeholder';
import { AccountHeading } from './AccountHeading';
import { ChartLoadingScreen } from './ChartLoadingScreen';
// Components
import { SetCompletionCell } from './SetCompletionCell';
import SetSearcher from './SetSearcher';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  setCompletionMap: Immutable.OrderedMap<string, SetCompletion>;
  status: StatComponentStatus;
}

interface IState {
  setCompletionSorting: SetCompletionSorting;
  includeEmpty: boolean;
  query?: string;
  cardSetID?: number;
}

export class SetCompletionTable extends React.Component<IProps, IState> {
  /**
   * @override
   */
  constructor(props: IProps) {
    super(props);
    this.state = {
      query: undefined,
      cardSetID: undefined,
      setCompletionSorting: SetCompletionSorting.COMPLETION_DESC,
      includeEmpty: false,
    };
  }

  private async onChangeSetSorting(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    const setCompletionSorting = evt.currentTarget.value as SetCompletionSorting;
    this.setState(
      {
        setCompletionSorting: setCompletionSorting,
      },
      async () => {
        await this.loadStatistics.bind(this)();
      },
    );
  }

  private async waypointActivated() {
    if (this.props.status === StatComponentStatus.INACTIVE || this.props.status === StatComponentStatus.ERROR) {
      await this.loadStatistics.bind(this)();
    }
  }

  private async refresh(evt: React.SyntheticEvent<HTMLButtonElement, Event>) {
    evt.preventDefault();
    await this.loadStatistics.bind(this)();
  }

  private async loadStatistics() {
    StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADING, StatisticType.SET_COMPLETION);
    await StatisticsActions.setCompletion(
      this.props.dispatcher,
      10,
      SetCompletionSortingCodes[this.state.setCompletionSorting],
      SetCompletionOrderingCodes[this.state.setCompletionSorting],
      this.state.includeEmpty,
      this.state.query,
      this.state.cardSetID,
    )
      .then(() => {
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADED, StatisticType.SET_COMPLETION);
      })
      .catch((err: any) => {
        err && console.error(err);
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.ERROR, StatisticType.SET_COMPLETION);
      });
  }

  private async updateTarget(query?: string, cardSet?: CardSet) {
    if (cardSet !== undefined) {
      this.setState({ cardSetID: cardSet.get('id'), query: undefined }, async () => {
        await this.loadStatistics.bind(this)();
      });
      return;
    } else {
      this.setState({ query: query, cardSetID: undefined }, async () => {
        await this.loadStatistics.bind(this)();
      });
    }
  }

  render() {
    let options = Immutable.List<JSX.Element>();
    for (const value in SetCompletionSorting) {
      options = options.push(<option>{SetCompletionSorting[value as SetCompletionSortingKey]}</option>);
    }
    const buttonClass = classNames({
      'button-primary': this.props.status === StatComponentStatus.LOADED,
      'button-grey': this.props.status !== StatComponentStatus.LOADED,
    });
    const loading =
      this.props.status === StatComponentStatus.INACTIVE || this.props.status === StatComponentStatus.LOADING;
    return (
      <Waypoint onEnter={this.waypointActivated.bind(this)}>
        <div>
          <AccountHeading
            title="Set Completion"
            spinnerActive={true}
            loading={loading}
            refresh={this.refresh.bind(this)}
          />
          {this.props.status === StatComponentStatus.ERROR ? (
            <div className="statistics-non-plotly-error-container">
              <ChartLoadingScreen
                status={this.props.status}
                reload={this.loadStatistics.bind(this)}
                customLoadingScreen={true}
              />
            </div>
          ) : (
            <>
              <div className="row">
                <IconSelect
                  className="icon-container--set-completion set-completion-option col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-3"
                  onChange={this.onChangeSetSorting.bind(this)}
                  value={this.state.setCompletionSorting}
                  disabled={loading}
                  icon={sort}
                >
                  <>
                    <option disabled>Sorting</option>
                    {Object.keys(SetCompletionSorting).map((key) => (
                      <option key={key} value={SetCompletionSorting[key as SetCompletionSortingKey]}>
                        {SetCompletionSorting[key as SetCompletionSortingKey]}
                      </option>
                    ))}
                  </>
                </IconSelect>
                <div className="set-completion-option col-xs-12 col-sm-6 col-md-6 col-lg-6 col-xl-4">
                  <SetSearcher
                    dispatcher={this.props.dispatcher}
                    query={this.state.query ? this.state.query : ''}
                    updateQuery={this.updateQuery.bind(this)}
                    updateTarget={this.updateTarget.bind(this)}
                  />
                </div>
                <div className="set-completion-option col-xs-12 col-sm-3 col-md-3 col-lg-3 col-xl-2">
                  <button
                    className={buttonClass}
                    disabled={loading}
                    style={{ height: '3rem' }}
                    onClick={() => this.updateTarget.bind(this)(this.state.query)}
                  >
                    Search
                  </button>
                </div>
                <div className="form-label col-xs-12 col-sm-3 col-md-3 col-lg-3 col-xl-3" style={{ margin: 0 }}>
                  <div>Display empty sets</div>
                  <Checkbox checked={this.state.includeEmpty} onChange={this.onChangeCheckbox.bind(this)} />
                </div>
              </div>
              <div className="row" style={{ marginTop: '2rem' }}>
                {loading ? (
                  <>{this.generatePlaceholder()}</>
                ) : (
                  <>
                    {this.props.setCompletionMap.size === 0 ? (
                      <Placeholder button={null} message={'No sets matching query found'} />
                    ) : (
                      this.generateSetComponents(this.props.setCompletionMap)
                    )}
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </Waypoint>
    );
  }

  private generatePlaceholder() {
    const placeholderSetCompletion = new SetCompletion({
      cardSet: new CardSet({ name: 'Loading...', setCode: 'SLD' }),
      totalCards: 1,
    });
    let components = Immutable.List<JSX.Element>();
    for (let i = 0; i < 10; i++) {
      components = components.push(
        <SetCompletionCell
          key={i}
          setCompletion={placeholderSetCompletion}
          dispatcher={this.props.dispatcher}
          username={this.props.me.get('username')}
          disabled={true}
        />,
      );
    }
    return components.toArray();
  }

  private generateSetComponents(setCompletionMap: Immutable.OrderedMap<string, SetCompletion>) {
    return setCompletionMap
      .map((value: SetCompletion) => {
        return (
          <SetCompletionCell
            key={value.get('cardSet').get('setCode')}
            setCompletion={value}
            username={this.props.me.get('username')}
            dispatcher={this.props.dispatcher}
          />
        );
      })
      .toArray();
  }

  private async onChangeCheckbox() {
    this.setState({ includeEmpty: !this.state.includeEmpty }, async () => {
      await this.loadStatistics.bind(this)();
    });
  }

  private updateQuery(query?: string) {
    this.setState({ query: query });
  }
}
