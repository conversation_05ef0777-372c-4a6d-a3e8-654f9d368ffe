import * as React from 'react';
import { Waypoint } from 'react-waypoint';
import * as StatisticsActions from '../../actions/StatisticsActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import { CoreAssets, ImageQuality } from '../../helpers/core_assets';
import displayCurrency from '../../helpers/currency_helper';
import { CardInstance } from '../../models/CardInstances';
import { Foil } from '../../models/Foil';
import { Game } from '../../models/Game';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { StatComponentStatus, StatisticType } from '../../models/Statistics';
import { Placeholder } from '../components/Placeholder';
import { AccountHeading } from './AccountHeading';
import { ChartLoadingScreen } from './ChartLoadingScreen';

interface IProps {
  dispatcher: Dispatcher;
  currencySymbol: string;
  cardGroup: MTGCardGroup;
  status: StatComponentStatus;
}

export class MostValuable extends React.Component<IProps> {
  /**
   * @override
   */
  constructor(props: IProps) {
    super(props);
  }

  private async loadStatistics() {
    StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADING, StatisticType.MOST_VALUABLE);
    await StatisticsActions.mostValuableCard(this.props.dispatcher)
      .then(() => {
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADED, StatisticType.MOST_VALUABLE);
      })
      .catch((err) => {
        err && console.error(err);
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.ERROR, StatisticType.MOST_VALUABLE);
      });
  }

  private async waypointActivated() {
    if (this.props.status === StatComponentStatus.INACTIVE || this.props.status === StatComponentStatus.ERROR) {
      await this.loadStatistics.bind(this)();
    }
  }

  private async refresh(evt: React.SyntheticEvent<HTMLButtonElement, Event>) {
    evt.preventDefault();
    await this.loadStatistics.bind(this)();
  }

  render() {
    const loading =
      this.props.status === StatComponentStatus.INACTIVE || this.props.status === StatComponentStatus.LOADING;
    const cardInstance: CardInstance | undefined =
      this.props.cardGroup.get('cardInstances').size > 0
        ? this.props.cardGroup.get('cardInstances').first()
        : undefined;
    let imageUrl = CoreAssets.cardBack(Game.MTG, ImageQuality.HQ);
    let foil = false;
    if (this.props.status === StatComponentStatus.LOADED && cardInstance) {
      imageUrl = cardInstance.imageURL(ImageQuality.HQ);
      if (cardInstance.get('foil') === Foil.On) {
        foil = true;
      }
    }
    return (
      <Waypoint onEnter={this.waypointActivated.bind(this)}>
        <div>
          <AccountHeading title="Most Valuable Card" loading={loading} refresh={this.refresh.bind(this)} />
          {this.props.status === StatComponentStatus.ERROR ? (
            <div className="statistics-non-plotly-error-container">
              <ChartLoadingScreen
                status={this.props.status}
                reload={this.loadStatistics.bind(this)}
                customLoadingScreen={true}
              />
            </div>
          ) : (
            <div className="flex" style={{ marginTop: '2rem' }}>
              <div className="col-xs-3" style={{ position: 'relative' }}>
                <div className="collection-grid-item-container">
                  <div className="collection-grid-item-image" style={{ cursor: 'default' }}>
                    <img
                      src={imageUrl}
                      alt={cardInstance === undefined ? '' : cardInstance.get('cardName')}
                      height={310}
                      width={223}
                    />
                    {foil ? <div className="foil-overlay" /> : null}
                  </div>
                </div>
              </div>
              <div className="col-xs-9">
                {this.props.status === StatComponentStatus.LOADED &&
                this.props.cardGroup.get('cardInstances').size === 0 ? (
                  <Placeholder
                    message={"Looks like your collection is empty, let's add some cards."}
                    button={'Add Cards'}
                    href="/builder/staging"
                  />
                ) : (
                  <>
                    <div className="info-heading">Card Name</div>
                    <div className="info-block">
                      {this.props.status !== StatComponentStatus.LOADED || cardInstance === undefined
                        ? '-'
                        : cardInstance.get('cardName')}
                    </div>
                    <div className="info-heading">Card Value</div>
                    <div className="info-block">
                      {this.props.status !== StatComponentStatus.LOADED || cardInstance === undefined
                        ? '-'
                        : `${displayCurrency(cardInstance.get('cardPrice'), true, this.props.currencySymbol)}`}
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </Waypoint>
    );
  }
}
