import * as Immutable from 'immutable';
import * as React from 'react';
import { Waypoint } from 'react-waypoint';
import * as StatisticsActions from '../../actions/StatisticsActions';
// Containers, actions, and dispatcher
import Dispatcher from '../../dispatcher/Dispatcher';
import { ColorStatDisplay, ColorStatistics } from '../../models/ColorStatistics';
import { StatComponentStatus, StatisticType } from '../../models/Statistics';
// Models
import { TabItem } from '../../models/TabItem';
// Components
import SecondaryNavBar from '../components/SecondaryNavBar';
import { AccountHeading } from './AccountHeading';
import { ColorBreakdownChart } from './ColorBreakdownChart';

interface IProps {
  dispatcher: Dispatcher;
  colorStatistics: ColorStatistics;
  status: StatComponentStatus;
}

interface IState {
  display: ColorStatDisplay;
}

export class ColorBreakdownContainer extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {
      display: ColorStatDisplay.OVERVIEW,
    };
  }

  /**
   * @override
   */
  render() {
    const colorStatisticsMap = this.props.colorStatistics.generateMap(this.state.display);
    const secondaryPages = Immutable.List<TabItem>([
      new TabItem({ title: ColorStatDisplay.OVERVIEW }),
      new TabItem({ title: ColorStatDisplay.SINGLE_COLOR }),
      new TabItem({ title: ColorStatDisplay.DUAL_COLOR }),
      new TabItem({ title: ColorStatDisplay.TRI_COLOR }),
      new TabItem({ title: ColorStatDisplay.FOUR_FIVE_COLOR }),
    ]);

    return (
      <Waypoint onEnter={this.waypointActivated.bind(this)}>
        <div>
          <AccountHeading
            title="Color Breakdown"
            loading={this.props.status === StatComponentStatus.LOADING}
            refresh={this.refresh.bind(this)}
          />
          <div className="col-xs-12 flex vertical align-center">
            {this.props.status === StatComponentStatus.LOADED ? (
              <div style={{ marginBottom: '1rem' }}>
                <SecondaryNavBar
                  pages={secondaryPages}
                  selectedPage={this.state.display}
                  displayName={(page: string) => {
                    return page;
                  }}
                  openPage={this.openPage.bind(this)}
                />
              </div>
            ) : null}
            <ColorBreakdownChart
              dispatcher={this.props.dispatcher}
              statistics={colorStatisticsMap}
              status={this.props.status}
              reload={this.loadStatistics.bind(this)}
            />
          </div>
        </div>
      </Waypoint>
    );
  }

  private openPage(page: string) {
    this.setState({ display: page as ColorStatDisplay });
  }

  private async waypointActivated() {
    if (this.props.status === StatComponentStatus.INACTIVE || this.props.status === StatComponentStatus.ERROR) {
      await this.loadStatistics.bind(this)();
    }
  }

  private async refresh(evt: React.SyntheticEvent<HTMLButtonElement, Event>) {
    evt.preventDefault();
    await this.loadStatistics.bind(this)();
  }

  private async loadStatistics() {
    StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADING, StatisticType.COLOR);
    await StatisticsActions.colorStatistics(this.props.dispatcher)
      .then(() => {
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.LOADED, StatisticType.COLOR);
      })
      .catch((err: any) => {
        err && console.error(err);
        StatisticsActions.changeStatus(this.props.dispatcher, StatComponentStatus.ERROR, StatisticType.COLOR);
      });
  }
}
