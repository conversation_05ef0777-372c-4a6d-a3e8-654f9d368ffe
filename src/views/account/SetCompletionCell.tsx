import * as React from 'react';
import * as FilterActions from '../../actions/FilterActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import history from '../../helpers/history';
import { MTGCardPage } from '../../models/mtg/MTGCardPage';
import { SetCompletion } from '../../models/SetCompletion';
import { ProgressBar } from '../components/ProgressBar';
import { SetSymbol, SetSymbolSize } from '../components/SetSymbol';

interface IProps {
  setCompletion: SetCompletion;
  username: string;
  dispatcher: Dispatcher;
  disabled?: boolean;
}

export const SetCompletionCell = (props: IProps) => {
  const percentage = toPercentage(props.setCompletion.get('completion'));
  const cardSet = props.setCompletion.get('cardSet');
  return (
    <div className="col-xs-12 col-lg-6" style={{ margin: '1rem 0' }}>
      <div className="flex" onClick={filterBySet.bind(this)} style={{ cursor: props.disabled ? 'default' : 'pointer' }}>
        <div style={{ marginRight: '1rem' }}>
          <SetSymbol
            setName={cardSet.get('name')}
            setCode={cardSet.get('setCode')}
            hoverText={false}
            size={SetSymbolSize.XL}
          />
        </div>
        <div>
          <div className="account-set-name">{cardSet.get('name')}</div>
          <div className="account-set-type">{props.disabled ? '' : cardSet.setTypeToString()}</div>
        </div>
      </div>
      <div style={{ marginTop: '1rem' }}>
        <ProgressBar percentage={percentage} />
      </div>
      <div className="flex col-xs-12" style={{ padding: 0, marginTop: '1rem', fontSize: '1rem' }}>
        <div className="account-set-type flex col-xs-6" style={{ padding: 0 }}>{`${props.setCompletion.get(
          'uniqueOwned',
        )} / ${props.setCompletion.get('totalCards')}`}</div>
        <div
          className="account-set-type flex col-xs-6"
          style={{ padding: 0, justifyContent: 'flex-end' }}
        >{`${percentage}%`}</div>
      </div>
    </div>
  );

  // For ease of display, toPercentage aggressively rounds upwards.
  function toPercentage(completion: number) {
    if (completion > 1) {
      console.error(`toPercentage completion === ${completion}, which is greater than 1`);
      return 100;
    } else if (completion === 1) {
      return 100;
    }
    // Ensures that an incomplete set will never be displayed as more than 99% complete.
    return Math.trunc(completion * 10000) / 100;
  }

  async function filterBySet() {
    if (props.disabled) {
      return;
    }
    const cardPage = new MTGCardPage({ ownerUsername: props.username });
    await FilterActions.overrideWithCardSet(props.setCompletion.get('cardSet'), cardPage, props.dispatcher);
    history.push('/collection');
  }
};
