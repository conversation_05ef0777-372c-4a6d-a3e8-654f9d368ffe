import * as Immutable from 'immutable';
import * as React from 'react';
import { RequestWithPromise } from '../../../api/Requests';
import * as YugiCardsAPI from '../../../api/yugioh/YugiCards';
import * as YugiSetsAPI from '../../../api/yugioh/YugiSets';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { CardSetFilter } from '../../../models/filters/CardSetFilter';
import { Foil } from '../../../models/Foil';
import { Game } from '../../../models/Game';
import { Language } from '../../../models/Language';
import { YugiCardSet } from '../../../models/yugioh/YugiCards';
import { YugiSet } from '../../../models/yugioh/YugiSet';
import CardSearcher from '../CardSearcher';

interface IProps {
  dispatcher: Dispatcher;
  chooseSuggestion: (suggestion?: YugiCardSet) => void;
  searcherRef?: React.RefObject<CardSearcher>;
  language?: Language;
  foil?: Foil;
}

interface IState {
  cardSetFilters: Immutable.Set<CardSetFilter>;
}

export class YugiCardSearcher extends React.Component<IProps, IState> {
  public constructor(props: IProps) {
    super(props);
    this.state = {
      cardSetFilters: Immutable.Set<CardSetFilter>(),
    };
  }

  render() {
    return (
      <CardSearcher
        ref={this.props.searcherRef}
        dispatcher={this.props.dispatcher}
        game={Game.YUGIOH}
        chooseSuggestion={this.props.chooseSuggestion.bind(this)}
        searchCards={this.searchCards.bind(this)}
        searchSets={this.searchSets.bind(this)}
        updateSetFilters={(setFilters: Immutable.Set<CardSetFilter>) => this.setState({ cardSetFilters: setFilters })}
      />
    );
  }

  searchCards(query: string): RequestWithPromise<Immutable.List<YugiCardSet>> {
    return YugiCardsAPI.search(query, this.state.cardSetFilters);
  }

  searchSets(query: string): RequestWithPromise<Immutable.List<YugiSet>> {
    const requestPromise = YugiSetsAPI.search(query);

    return new RequestWithPromise(
      requestPromise.request,
      requestPromise.promise.then((sets: Immutable.List<YugiSet>) =>
        sets
          .filter(
            (set: YugiSet) =>
              !this.state.cardSetFilters
                .map((cardSetFilter: CardSetFilter) => cardSetFilter.get('cardSet').name())
                .contains(set.get('name')),
          )
          .toList(),
      ),
    );
  }
}
