import { screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { typeInCardSearcher } from '../../../../tests/views/components/cardSearcher/helpers';
import * as PokeCardsAPI from '../../../api/pokemon/PokeCards';
import * as PokeSetsAPI from '../../../api/pokemon/PokeSets';
import { PokeCardSearcher } from './PokeCardSearcher';

// Mock the API calls
vi.mock('../../../api/pokemon/PokeCards', () => ({
  search: vi.fn().mockImplementation(() => ({
    request: { abort: vi.fn() },
    promise: Promise.resolve(Immutable.List([])),
  })),
}));

vi.mock('../../../api/pokemon/PokeSets', () => ({
  search: vi.fn().mockImplementation(() => ({
    request: { abort: vi.fn() },
    promise: Promise.resolve(Immutable.List([])),
  })),
}));

const mockChooseSuggestion = vi.fn();

const renderPokeCardSearcher = () => {
  return renderWithDispatcher(PokeCardSearcher, {
    chooseSuggestion: mockChooseSuggestion,
  });
};

describe('PokeCardSearcher', () => {
  describe('component render', () => {
    it('renders the search input', () => {
      renderPokeCardSearcher();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('search functionality', () => {
    describe('when searching for cards', () => {
      it('calls Cards API', async () => {
        renderPokeCardSearcher();
        typeInCardSearcher('test');

        await vi.waitFor(() => {
          expect(PokeCardsAPI.search).toHaveBeenCalledWith('test', Immutable.Set());
        });
      });
    });

    describe('when searching for sets', () => {
      it('calls CardSets API when "s:" prefix is typed', async () => {
        renderPokeCardSearcher();
        typeInCardSearcher('s:test');

        await vi.waitFor(() => {
          expect(PokeSetsAPI.search).toHaveBeenCalledWith('test');
        });
      });
    });
  });
});
