import { screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { typeInCardSearcher } from '../../../../tests/views/components/cardSearcher/helpers';
import * as CardsAPI from '../../../api/Cards';
import * as CardSetsAPI from '../../../api/CardSets';
import { MTGCardSearcher } from './MTGCardSearcher';

// Mock the API calls
vi.mock('../../../api/Cards', () => ({
  search: vi.fn().mockImplementation(() => ({
    request: { abort: vi.fn() },
    promise: Promise.resolve(Immutable.List([])),
  })),
}));

vi.mock('../../../api/CardSets', () => ({
  searchRequest: vi.fn().mockImplementation(() => ({
    request: { abort: vi.fn() },
    promise: Promise.resolve(Immutable.List([])),
  })),
}));

const mockChooseSuggestion = vi.fn();

const renderMTGCardSearcher = () => {
  return renderWithDispatcher(MTGCardSearcher, {
    chooseSuggestion: mockChooseSuggestion,
  });
};

describe('MTGCardSearcher', () => {
  describe('component render', () => {
    it('renders the search input', () => {
      renderMTGCardSearcher();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('search functionality', () => {
    describe('when searching for cards', () => {
      it('calls Cards API', async () => {
        renderMTGCardSearcher();
        typeInCardSearcher('test');

        await vi.waitFor(() => {
          expect(CardsAPI.search).toHaveBeenCalledWith('test', Immutable.Set());
        });
      });
    });

    describe('when searching for sets', () => {
      it('calls CardSets API when "s:" prefix is typed', async () => {
        renderMTGCardSearcher();
        typeInCardSearcher('s:test');

        await vi.waitFor(() => {
          expect(CardSetsAPI.searchRequest).toHaveBeenCalledWith('test');
        });
      });
    });
  });
});
