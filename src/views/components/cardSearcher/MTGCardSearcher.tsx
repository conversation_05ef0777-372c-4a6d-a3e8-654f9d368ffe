import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardsAPI from '../../../api/Cards';
import * as CardSetsAPI from '../../../api/CardSets';
import { RequestWithPromise } from '../../../api/Requests';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { Card } from '../../../models/Cards';
import { CardSet } from '../../../models/CardSets';
import { CardSetFilter } from '../../../models/filters/CardSetFilter';
import { Foil } from '../../../models/Foil';
import { Game } from '../../../models/Game';
import { Language } from '../../../models/Language';
import CardSearcher from '../CardSearcher';

interface IProps {
  dispatcher: Dispatcher;
  chooseSuggestion: (suggestion?: Card) => void;
  searcherRef?: React.RefObject<CardSearcher>;
  language?: Language;
  foil?: Foil;
}

interface IState {
  cardSetFilters: Immutable.Set<CardSetFilter>;
}

export class MTGCardSearcher extends React.Component<IProps, IState> {
  public constructor(props: IProps) {
    super(props);
    this.state = {
      cardSetFilters: Immutable.Set<CardSetFilter>(),
    };
  }

  render() {
    return (
      <CardSearcher
        ref={this.props.searcherRef}
        dispatcher={this.props.dispatcher}
        chooseSuggestion={this.props.chooseSuggestion.bind(this)}
        searchCards={this.searchCards.bind(this)}
        searchSets={this.searchSets.bind(this)}
        updateSetFilters={(setFilters: Immutable.Set<CardSet>) => this.setState({ cardSetFilters: setFilters })}
        game={Game.MTG}
      />
    );
  }

  searchCards(query: string): RequestWithPromise<Immutable.List<Card>> {
    return CardsAPI.search(query, this.state.cardSetFilters);
  }

  searchSets(query: string): RequestWithPromise<Immutable.List<CardSet>> {
    const requestPromise = CardSetsAPI.searchRequest(query);

    return new RequestWithPromise(
      requestPromise.request,
      requestPromise.promise.then((sets: Immutable.List<CardSet>) =>
        sets
          .filter(
            (set: CardSet) =>
              !this.state.cardSetFilters
                .map((cardSetFilter: CardSetFilter) => cardSetFilter.get('cardSet').name())
                .contains(set.get('name')),
          )
          .toList(),
      ),
    );
  }
}
