import { screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { typeInCardSearcher } from '../../../../tests/views/components/cardSearcher/helpers';
import * as YugiCardsAPI from '../../../api/yugioh/YugiCards';
import * as YugiSetsAPI from '../../../api/yugioh/YugiSets';
import { YugiCardSearcher } from './YugiCardSearcher';

// Mock the API calls
vi.mock('../../../api/yugioh/YugiCards', () => ({
  search: vi.fn().mockImplementation(() => ({
    request: { abort: vi.fn() },
    promise: Promise.resolve(Immutable.List([])),
  })),
}));

vi.mock('../../../api/yugioh/YugiSets', () => ({
  search: vi.fn().mockImplementation(() => ({
    request: { abort: vi.fn() },
    promise: Promise.resolve(Immutable.List([])),
  })),
}));

const mockChooseSuggestion = vi.fn();

const renderYugiCardSearcher = () => {
  return renderWithDispatcher(YugiCardSearcher, {
    chooseSuggestion: mockChooseSuggestion,
  });
};

describe('YugiCardSearcher', () => {
  describe('component render', () => {
    it('renders the search input', () => {
      renderYugiCardSearcher();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('search functionality', () => {
    describe('when searching for cards', () => {
      it('calls Cards API', async () => {
        renderYugiCardSearcher();
        typeInCardSearcher('test');

        await vi.waitFor(() => {
          expect(YugiCardsAPI.search).toHaveBeenCalledWith('test', Immutable.Set());
        });
      });
    });

    describe('when searching for sets', () => {
      it('calls CardSets API when "s:" prefix is typed', async () => {
        renderYugiCardSearcher();
        typeInCardSearcher('s:test');

        await vi.waitFor(() => {
          expect(YugiSetsAPI.search).toHaveBeenCalledWith('test');
        });
      });
    });
  });
});
