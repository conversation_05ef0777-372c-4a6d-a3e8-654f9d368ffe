import * as Immutable from 'immutable';
import * as React from 'react';
import * as LorcanaCardsAPI from '../../../api/lorcana/LorcanaCards';
import * as LorcanaSetsAPI from '../../../api/lorcana/LorcanaSets';
import { RequestWithPromise } from '../../../api/Requests';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { CardSetFilter } from '../../../models/filters/CardSetFilter';
import { Foil } from '../../../models/Foil';
import { Game } from '../../../models/Game';
import { Language } from '../../../models/Language';
import { LorcanaCard } from '../../../models/lorcana/LorcanaCards';
import { LorcanaSet } from '../../../models/lorcana/LorcanaSet';
import CardSearcher from '../CardSearcher';

interface IProps {
  dispatcher: Dispatcher;
  chooseSuggestion: (suggestion?: LorcanaCard) => void;
  searcherRef?: React.RefObject<CardSearcher>;
  language?: Language;
  foil?: Foil;
}

interface IState {
  cardSetFilters: Immutable.Set<CardSetFilter>;
}

export class LorcanaCardSearcher extends React.Component<IProps, IState> {
  public constructor(props: IProps) {
    super(props);
    this.state = {
      cardSetFilters: Immutable.Set<CardSetFilter>(),
    };
  }

  render() {
    return (
      <CardSearcher
        ref={this.props.searcherRef}
        dispatcher={this.props.dispatcher}
        game={Game.LORCANA}
        chooseSuggestion={this.props.chooseSuggestion.bind(this)}
        searchCards={this.searchCards.bind(this)}
        searchSets={this.searchSets.bind(this)}
        updateSetFilters={(setFilters: Immutable.Set<CardSetFilter>) => this.setState({ cardSetFilters: setFilters })}
      />
    );
  }

  searchCards(query: string): RequestWithPromise<Immutable.List<LorcanaCard>> {
    return LorcanaCardsAPI.search(query, this.state.cardSetFilters);
  }

  searchSets(query: string): RequestWithPromise<Immutable.List<LorcanaSet>> {
    const requestPromise = LorcanaSetsAPI.search(query);

    return new RequestWithPromise(
      requestPromise.request,
      requestPromise.promise.then((sets: Immutable.List<LorcanaSet>) =>
        sets
          .filter(
            (set: LorcanaSet) =>
              !this.state.cardSetFilters
                .map((cardSetFilter: CardSetFilter) => cardSetFilter.get('cardSet').name())
                .contains(set.get('name')),
          )
          .toList(),
      ),
    );
  }
}
