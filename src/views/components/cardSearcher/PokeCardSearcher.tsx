import * as Immutable from 'immutable';
import * as React from 'react';
import * as PokeCardsAPI from '../../../api/pokemon/PokeCards';
import * as PokeSetsAPI from '../../../api/pokemon/PokeSets';
import { RequestWithPromise } from '../../../api/Requests';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { CardSetFilter } from '../../../models/filters/CardSetFilter';
import { Foil } from '../../../models/Foil';
import { Game } from '../../../models/Game';
import { Language } from '../../../models/Language';
import { PokeCard } from '../../../models/pokemon/PokeCards';
import { PokeSet } from '../../../models/pokemon/PokeSet';
import CardSearcher from '../CardSearcher';

interface IProps {
  dispatcher: Dispatcher;
  chooseSuggestion: (suggestion?: PokeCard) => void;
  searcherRef?: React.RefObject<CardSearcher>;
  language?: Language;
  foil?: Foil;
}

interface IState {
  cardSetFilters: Immutable.Set<CardSetFilter>;
}

export class PokeCardSearcher extends React.Component<IProps, IState> {
  public constructor(props: IProps) {
    super(props);
    this.state = {
      cardSetFilters: Immutable.Set<CardSetFilter>(),
    };
  }

  render() {
    return (
      <CardSearcher
        ref={this.props.searcherRef}
        dispatcher={this.props.dispatcher}
        game={Game.POKEMON}
        chooseSuggestion={this.props.chooseSuggestion.bind(this)}
        searchCards={this.searchCards.bind(this)}
        searchSets={this.searchSets.bind(this)}
        updateSetFilters={(setFilters: Immutable.Set<CardSetFilter>) => this.setState({ cardSetFilters: setFilters })}
      />
    );
  }

  searchCards(query: string): RequestWithPromise<Immutable.List<PokeCard>> {
    return PokeCardsAPI.search(query, this.state.cardSetFilters);
  }

  searchSets(query: string): RequestWithPromise<Immutable.List<PokeSet>> {
    const requestPromise = PokeSetsAPI.search(query);

    return new RequestWithPromise(
      requestPromise.request,
      requestPromise.promise.then((sets: Immutable.List<PokeSet>) =>
        sets
          .filter(
            (set: PokeSet) =>
              !this.state.cardSetFilters
                .map((cardSetFilter: CardSetFilter) => cardSetFilter.get('cardSet').name())
                .contains(set.get('name')),
          )
          .toList(),
      ),
    );
  }
}
