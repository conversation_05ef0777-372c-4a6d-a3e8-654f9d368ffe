import * as Immutable from 'immutable';
import * as React from 'react';
import TruncateMarkup from 'react-truncate-markup';

export interface TruncatedTextProps {
  listOfStrings: Immutable.List<string>;
}

export const TruncatedText: React.SFC<TruncatedTextProps> = (props) => {
  let text = props.listOfStrings.toArray(); // WARNING: Must be Array type, otherwise React will crash!

  text = text.map((s, i, a) => {
    if (i < a.length - 1) {
      return s + ', ';
    } else {
      return s;
    }
  });

  const namesLeftEllipsis = (node: React.ReactNode) => {
    if (node) {
      const element = node as React.ReactElement;
      const namesRendered = element.props.children;
      return `and ${text.length - namesRendered.length} more`;
    }

    return '';
  };

  return (
    // Line height auto-detected, set lineHeight=something if that causes problems.
    <TruncateMarkup lines={1} ellipsis={namesLeftEllipsis}>
      <div className="truncated-text">
        {text.map((s, key) => (
          <TruncateMarkup.Atom key={key}>{s}</TruncateMarkup.Atom>
        ))}
      </div>
    </TruncateMarkup>
  );
};
