import add from '@iconify/icons-ic/add';
import { Icon } from '@iconify/react';
import * as React from 'react';
import { OptionTypeBase } from 'react-select';
import { TextFormat } from '../../helpers/fmt';
import { CardList } from '../../models/CardList';

export class CardListOption implements OptionTypeBase {
  label: string;
  value: string;
  cardCount: number;
  icon?: boolean;

  constructor(cardList: CardList) {
    this['label'] = cardList.get('name');
    this['value'] = cardList.get('uuid');
    this['cardCount'] = cardList.get('cardCount');
    this['icon'] = undefined;
  }

  static formatOptionLabel = (cardListOption: CardListOption) => {
    return (
      <div className="card-list__suggestion">
        {cardListOption['icon'] ? (
          <Icon height={'24px'} width={'24px'} icon={add} style={{ marginRight: '1rem' }} />
        ) : null}
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__name">{cardListOption['label']}</div>
          <div className="react-select__suggestion__label__set">
            {TextFormat.pluralizedCards(cardListOption['cardCount'])}
          </div>
        </div>
      </div>
    );
  };
}
