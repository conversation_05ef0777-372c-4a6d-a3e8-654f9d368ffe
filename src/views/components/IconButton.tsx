import * as React from 'react';
import { Size } from '../../models/Size';

export enum Position {
  LEFT = 'left',
  RIGHT = 'right',
}

interface IProps {
  buttonCSS: 'button-paypal' | 'button-stripe' | 'button-cart' | 'button-cart--card-panel';
  imgSrc: string;
  size: Size;
  position: Position;
  onClick: (evt: React.SyntheticEvent<HTMLElement>) => void;
  text?: string;
  imgMargin?: string;
}

export const IconButton = (props: IProps) => {
  return (
    <button className={props.buttonCSS} onClick={props.onClick}>
      <div className="flex">
        {props.position === Position.LEFT ? (
          <img
            src={props.imgSrc}
            width={props.size.get('width')}
            height={props.size.get('height')}
            style={{ margin: props.imgMargin }}
          />
        ) : null}
        {props.text === undefined ? null : (
          <span className="flex" style={{ margin: 'auto' }}>
            {props.text}
          </span>
        )}
        {props.position === Position.RIGHT ? (
          <img
            src={props.imgSrc}
            width={props.size.get('width')}
            height={props.size.get('height')}
            style={{ margin: props.imgMargin }}
          />
        ) : null}
      </div>
    </button>
  );
};
