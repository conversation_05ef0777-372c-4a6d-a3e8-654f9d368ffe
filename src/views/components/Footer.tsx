import * as React from 'react';

interface IProps {}

interface IState {}

export class Footer extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  public render() {
    return (
      <footer className="footer">
        <div className="footer-container">
          <div className="col-xs-12 col-sm-3">
            <a className="footer-sprite" href="https://www.facebook.com/CardCastleHQ" target="_blank">
              <img src="/images/sprite-social-facebook.png" />
            </a>
            <a className="footer-sprite" href="https://www.twitter.com/CardCastleHQ" target="_blank">
              <img src="/images/sprite-social-twitter.png" />
            </a>
            <a className="footer-sprite" href="mailto:<EMAIL>">
              {' '}
              <img src="/images/sprite-social-email.png" />
            </a>
          </div>

          <div className="col-xs-12 col-sm-6">
            <ul>
              <li>
                <a href="/apps">Apps</a>
              </li>
              <li>
                <a href="/tutorial">Tutorial</a>
              </li>
              <li>
                <a href="https://support.cardcastle.co" target="_blank">
                  Support
                </a>
              </li>
              <li>
                <a href="/privacy">Privacy Policy</a>
              </li>
            </ul>
            <p>
              Magic: The Gathering is a trademark and copyright of Wizards of the Coast, Inc, a subsidiary of Hasbro,
              Inc. All rights reserved. This site is unaffiliated with these entities.
            </p>
            <p className="footer-price-paragraph">
              Prices sourced from{' '}
              <a href="https://www.tcgplayer.com/" target="_blank">
                TCGplayer
              </a>{' '}
              and{' '}
              <a href="https://www.cardkingdom.com/" target="_blank">
                Card Kingdom
              </a>{' '}
            </p>
          </div>
        </div>
      </footer>
    );
  }
}
