import * as React from 'react';
import { OptionTypeBase } from 'react-select';
import { Game } from '../../models/Game';
import { LorcanaPrinting } from '../../models/lorcana/LorcanaPrinting';
import { MTGPrinting } from '../../models/mtg/MTGPrinting';
import { PokePrinting } from '../../models/pokemon/PokePrinting';
import { AnyPrinting } from '../../models/Printing';
import { YugiPrinting } from '../../models/yugioh/YugiPrinting';
import { LorcanaSetSymbol } from './lorcana/LorcanaSetSymbol';
import { PokeSetSymbol } from './pokemon/PokeSetSymbol';
import { SetSymbol } from './SetSymbol';
import { YugiSetSymbol } from './yugioh/YugiSetSymbol';

export class PrintingSuggestion implements OptionTypeBase {
  label: string;
  value: AnyPrinting;
  game: Game;

  constructor(game: Game, printing: AnyPrinting) {
    this['label'] = printing.displayLabel();
    this['value'] = printing;
    this['game'] = game;
  }

  static formatOptionLabel = (suggestion: PrintingSuggestion) => {
    const printing: AnyPrinting = suggestion.value;
    let setSymbol: JSX.Element;

    switch (suggestion.game as Game) {
      case Game.MTG:
        const mtgPrinting = printing as MTGPrinting;
        setSymbol = (
          <SetSymbol
            setName={mtgPrinting.get('setName')}
            setCode={mtgPrinting.get('setCode')}
            rarity={mtgPrinting.get('rarity')}
            hoverText={false}
          />
        );
        break;
      case Game.POKEMON:
        const pokePrinting = printing as PokePrinting;
        setSymbol = (
          <PokeSetSymbol
            setName={pokePrinting.get('setName')}
            setUUID={pokePrinting.get('setUUID')}
            rarity={pokePrinting.get('rarity')}
            hoverText={false}
          />
        );
        break;
      case Game.YUGIOH:
        const yugiPrinting = printing as YugiPrinting;
        setSymbol = (
          <YugiSetSymbol
            setName={yugiPrinting.get('setName')}
            setUUID={yugiPrinting.get('setUUID')}
            rarity={yugiPrinting.get('rarity')}
            hoverText={false}
          />
        );
        break;
      case Game.LORCANA:
        const lorcanaPrinting = printing as LorcanaPrinting;
        setSymbol = (
          <LorcanaSetSymbol
            setName={lorcanaPrinting.get('setName')}
            setUUID={lorcanaPrinting.get('setUUID')}
            rarity={lorcanaPrinting.get('rarity')}
            hoverText={false}
          />
        );
        break;
    }
    return (
      <div className="react-select__suggestion">
        <div className="react-select__suggestion__set">{setSymbol}</div>
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__name">{suggestion.label}</div>
        </div>
      </div>
    );
  };
}
