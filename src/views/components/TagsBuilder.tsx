import close from '@iconify/icons-ic/close';
import { Icon } from '@iconify/react';
import classNames from 'classnames';
import * as Immutable from 'immutable';
import moment from 'moment';
import * as React from 'react';
import InputAutoSize from 'react-input-autosize';
import * as Request from '../../api/Requests';
import Dispatcher from '../../dispatcher/Dispatcher';
import history from '../../helpers/history';
import { FreeLimits } from '../../models/Subscriptions';
import { Tag } from '../../models/Tags';
import { User } from '../../models/Users';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  tags: Immutable.OrderedSet<Tag>;
  userTags: Immutable.OrderedSet<Tag>;
  onAdd: (tag: Tag) => void;
  onRemove: (tag: Tag) => void;
  immutable?: boolean;
  publicViewing: boolean;
  placeholder?: string;
  isTaggingDeck?: boolean;
  background?: boolean; // TODO: Remove - unify styling
  dialog?: boolean; // TODO: Remove - unify styling
}

interface IState {
  tag?: string;
  tagQueryInitialized?: boolean;
  isFocus?: boolean;
  suggestions?: Array<string>;
  suggestionsIndex?: number;
}

export class TagsBuilder extends React.Component<IProps, IState> {
  private ignoreDocumentClick = false;
  private handleDocumentClick: EventListener;
  private lastQuery: Request.Request;
  private lastQueryTime: Date = new Date();

  public refs: {
    autocompleter: HTMLInputElement;
    [key: string]: HTMLElement;
  };

  constructor(props: IProps) {
    super(props);
    this.state = {
      tag: '',
      tagQueryInitialized: false,
      isFocus: false,
      suggestions: [],
      suggestionsIndex: -1,
    };
  }

  public componentDidMount() {
    this.handleDocumentClick = () => {
      if (this.ignoreDocumentClick) {
        this.ignoreDocumentClick = false;
        return;
      }
      this.setState({
        isFocus: false,
      });
    };
    document.addEventListener('click', this.handleDocumentClick);
  }

  public componentWillUnmount() {
    document.removeEventListener('click', this.handleDocumentClick);
  }

  public componentDidUpdate() {
    // reset the initialisation rules if necessary
    if (!this.refs['autocompleter'] && this.state.tagQueryInitialized) {
      this.setState({
        tagQueryInitialized: false,
      });
    }
  }

  public render() {
    const limited =
      this.props.isTaggingDeck || this.props.publicViewing
        ? false
        : this.props.userTags.size >= FreeLimits.TAGS && !this.props.me.get('subscribed');

    const containerClassName = classNames('tags-builder', {
      'tb-background': this.props.background,
      'tb-dialog': this.props.dialog,
    });

    const tagClassName = 'tags-builder-tag';

    return (
      <div className={containerClassName}>
        {(this.props.tags && this.props.tags.size) || !this.props.immutable ? (
          <div className={'tags-builder-tags' + (this.props.background ? '--dialog' : '')}>
            {this.props.tags.map((tag, key) => {
              if (!tag) {
                return null;
              }
              return (
                <div key={'tag-' + tag.get('name') + '-' + key} className={tagClassName}>
                  #{tag.get('name')}
                  {!this.props.immutable ? (
                    <div onClick={this.onClickRemoveTag.bind(this, tag)}>
                      <Icon style={{ margin: '5px 0 0 8px' }} height={'18px'} width={'18px'} icon={close} />
                    </div>
                  ) : null}
                </div>
              );
            })}

            {!this.props.immutable ? (
              <form style={{ position: 'relative', display: !this.props.immutable ? 'block' : 'hidden' }}>
                <InputAutoSize
                  ref="autocompleter"
                  type="text"
                  className={`${tagClassName} is-add`}
                  value={this.state.tag}
                  placeholder="#add tag"
                  onKeyDown={this.onKeyDown.bind(this)}
                  onChange={this.onChangeTag.bind(this)}
                  onFocus={this.onFocus.bind(this)}
                  onClick={this.onClickInput.bind(this)}
                />

                <input type="submit" onClick={this.onClickAddTag.bind(this)} style={{ display: 'none' }} />

                {this.state.isFocus && this.state.suggestions && this.state.suggestions.length ? (
                  <div
                    className="advanced-search-input__suggestions"
                    style={{
                      top: 'calc(100% + 0.25rem)',
                      left: '0.25rem',
                      right: 'none',
                      minWidth: 'max-content',
                      backgroundColor: 'white',
                    }}
                  >
                    {(this.state.suggestions || []).map((suggestion, index) => (
                      <div
                        key={'suggestion-' + suggestion + '-' + index}
                        className={
                          'advanced-search-input__suggestion' +
                          (index === this.state.suggestionsIndex ? ' is-selected' : '')
                        }
                        style={{ textAlign: 'left' }}
                        onClick={this.onClickSuggestion.bind(this, suggestion)}
                      >
                        {'#' + suggestion}
                      </div>
                    ))}
                  </div>
                ) : null}
              </form>
            ) : null}

            {limited ? (
              <div style={{ width: '100%' }}>
                <p className="lato-N4">You've reached the tag limit for Squire accounts. Become a Knight today.</p>
                <button
                  className="button-primary"
                  onClick={() => {
                    history.push('/settings/subscription');
                    window.scrollTo(0, 0);
                  }}
                >
                  Upgrade
                </button>
              </div>
            ) : null}
          </div>
        ) : (
          <div className="tags-builder-placeholder">
            {this.props.placeholder && this.props.placeholder.length
              ? this.props.placeholder
              : 'Tags of selected cards will appear here.'}
          </div>
        )}
      </div>
    );
  }

  private onClickInput(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.ignoreDocumentClick = true;
  }

  private onChangeTag(evt: React.SyntheticEvent<HTMLInputElement>) {
    evt.preventDefault();

    const newTag = evt.currentTarget.value.replace(/ /g, '');
    if (evt.currentTarget.value.length === 0) {
      this.setState({ tag: '', suggestions: [] });
    } else if (evt.currentTarget.value.length > 65) {
      // 65 because it includes #
      return;
    } else if (evt.currentTarget.value[0] !== '#') {
      this.setState({ tag: '#' + newTag, suggestions: [] });
    } else {
      this.setState({ tag: newTag, suggestions: [] });
    }

    this.lastQuery && this.lastQuery.abort();
    this.lastQueryTime = new Date();

    window.setTimeout(() => {
      const lastQueryTime = moment(this.lastQueryTime);
      if (moment(new Date()).diff(lastQueryTime, 'milliseconds', true) > 150) {
        if (newTag.length > 1) {
          // encodeURIComponent is correct to use for queries.
          if (this.props.isTaggingDeck) {
            this.lastQuery = Request.get('/api/v3/deck_tags?query=' + encodeURIComponent(newTag.substring(1)));
          } else {
            this.lastQuery = Request.get('/api/v3/tags?query=' + encodeURIComponent(newTag.substring(1)));
          }
          this.lastQuery
            .end()
            .then((res) => {
              if (this.state.tag && this.state.tag.length) {
                this.setState({ suggestions: (res.body.tags || res.body.deck_tags || []).map((tag: any) => tag.name) });
              } else {
                this.setState({ suggestions: [] });
              }
            })
            .catch((err) => {
              console.error(err);
              this.setState({ suggestions: [] });
            });
        } else {
          this.setState({ suggestions: [] });
        }
      }
    }, 200);
  }

  private onClickAddTag(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    if (this.state.tag) {
      this.addTag(this.state.tag.substring(1));
    }
  }

  private addTag(name?: string) {
    const duplicateTag = this.props.tags.find((tag: Tag) => tag.get('name') === name);
    if (name && name.length && !duplicateTag) {
      this.lastQuery && this.lastQuery.abort();

      const existingTag = this.props.userTags.find((tag: Tag) => tag.get('name') === name);
      this.props.onAdd && this.props.onAdd(existingTag || new Tag({ name: name }));
    }

    this.setState({ tag: '', suggestions: [], suggestionsIndex: -1 });
  }

  private onClickRemoveTag(tag: Tag, evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.props.onRemove && this.props.onRemove(tag);
  }

  private onFocus(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({ isFocus: true });
  }

  private onClickSuggestion(suggestion: string, evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    evt.stopPropagation();
    this.addTag(suggestion);
    this.ignoreDocumentClick = true;
  }

  private onKeyDown(evt: React.KeyboardEvent<HTMLInputElement>) {
    switch (evt.keyCode) {
      case 13: // ENTER
        evt.preventDefault();
        evt.stopPropagation();

        // Replace query with suggestion
        const tag =
          (this.state.suggestions || [])[Math.max(this.state.suggestionsIndex!, -1)] ||
          (this.state.tag || '#').substr(1);
        this.addTag(tag);
        break;

      case 38: // UP
        evt.preventDefault();
        evt.stopPropagation();

        if (this.state.suggestions && this.state.suggestions.length) {
          this.setState({
            suggestionsIndex: Math.max(this.state.suggestionsIndex! - 1, -1),
          });
        }
        break;

      case 40: // DOWN
        evt.preventDefault();
        evt.stopPropagation();

        if (this.state.suggestions && this.state.suggestions.length) {
          this.setState({
            suggestionsIndex: Math.min(this.state.suggestionsIndex! + 1, this.state.suggestions.length - 1),
          });
        }
        break;
    }
  }
}
