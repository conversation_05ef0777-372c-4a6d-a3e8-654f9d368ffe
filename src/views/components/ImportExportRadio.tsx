import * as React from 'react';
import { DataService, Format } from '../../models/ImportExport';

interface IProps {
  format: Format;
  service: DataService;
  selected: boolean;
  onSelected: () => void;
}

export default function ImportExportRadio(props: IProps) {
  const baseID = `${props.format}-${props.service.get('type')}`;
  return (
    <div className="builder-import-export-option">
      <input id={baseID} key={baseID} type="radio" checked={props.selected} onChange={() => props.onSelected()} />
      <label key={baseID + '-label'} htmlFor={baseID} className="builder-import-export-label">
        <img src={props.service.get('iconURL')} />
        <span>{props.service.get('displayName')}</span>
      </label>
    </div>
  );
}
