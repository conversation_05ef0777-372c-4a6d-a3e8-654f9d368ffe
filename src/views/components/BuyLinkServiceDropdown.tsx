import * as React from 'react';
import Select, { components } from 'react-select';
import { ImageHelper } from '../../helpers/img';
import { CardKingdomService } from '../../lib/carts/CardKingdom';
import { CartService } from '../../lib/carts/CartService';
import { TCGPlayerService } from '../../lib/carts/TCGPlayer';
import { SimpleNoOptionsMessage, styleOverride } from '../shared/ReactSelectHelper';
import { BuyLinkItem } from './BuyLinkServiceSuggestionItem';

interface IProps {
  onSelect: (service: CartService) => void;
}

const SingleValueCard = (props: any) => (
  <components.SingleValue {...props}>
    <div className="text__suggestion">
      <div className="react-select__suggestion__label">
        <div className="react-select__suggestion__label__value">{props.data.label}</div>
      </div>
    </div>
  </components.SingleValue>
);

export const BuyLinkServiceDropdown = (props: IProps) => {
  function onChange(buyLinkItem: BuyLinkItem) {
    props.onSelect(buyLinkItem['value']);
  }

  const options = [new BuyLinkItem(new CardKingdomService()), new BuyLinkItem(new TCGPlayerService())];
  options.forEach((item: BuyLinkItem) => ImageHelper.preload(item['value'].iconURL));
  return (
    <div style={{ cursor: 'text' }}>
      <Select
        className={'react-select-thin'}
        classNamePrefix="react-select-thin"
        components={{
          SingleValue: SingleValueCard,
          NoOptionsMessage: SimpleNoOptionsMessage,
        }}
        formatOptionLabel={(item: BuyLinkItem) => item.formatOptionLabel()}
        styles={styleOverride}
        options={options}
        isClearable={false}
        defaultValue={options[0]}
        onChange={onChange}
        defaultOptions
      />
    </div>
  );
};
