import * as Immutable from 'immutable';
import * as React from 'react';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { FilterState } from '../../../models/filters/FilterState';
import { Rarity, rarityToString, type RarityKey } from '../../../models/Rarity';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';
import { GateFilterIcon } from './GateFilterIcon';

interface IProps {
  rarityFilters?: Immutable.OrderedSet<Rarity>;
  onUpdateFilter: (rarityFilters: Immutable.OrderedSet<Rarity>) => void;
  disabled?: boolean;
  displayType: FilterDisplay;
  onClear?: () => void;
}

export const RarityFilter = (props: IProps) => {
  const disabled = props.disabled === undefined ? false : props.disabled;

  return (
    <>
      <FilterTitle name="Rarity">
        <ClearFilterButton
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          disabled={disabled}
          onClick={() => {
            props.onUpdateFilter(Immutable.OrderedSet<Rarity>());
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <div className="filter-symbol-container">
        {Object.keys(Rarity).map((key) => {
          const rarity = Rarity[key as RarityKey];
          if (rarity === Rarity.BASIC_LAND) {
            return null;
          }
          let filterState: FilterState;
          if (props.rarityFilters && props.rarityFilters.contains(rarity)) {
            filterState = FilterState.ON;
          } else {
            filterState = FilterState.INACTIVE;
          }

          return (
            <GateFilterIcon
              disabled={disabled}
              key={rarity}
              hoverText={rarityToString(rarity)}
              iconPath={`/svg/rarity-${Rarity[key as RarityKey].replace(/\s/g, '-')}.svg`}
              // Rarity filters are only true/false but we abuse the common component
              filterState={filterState}
              onClick={() => onChange(Rarity[key as RarityKey])}
            />
          );
        })}
      </div>
    </>
  );

  function onChange(rarity: Rarity) {
    const raritySet =
      props.rarityFilters === undefined ? Immutable.OrderedSet<Rarity>() : props.rarityFilters!.toOrderedSet();
    let newSet: Immutable.OrderedSet<Rarity> = raritySet.remove(rarity);

    if (newSet.count() === raritySet.count()) {
      newSet = raritySet.add(rarity);
    }

    props.onUpdateFilter(newSet);
  }
};
