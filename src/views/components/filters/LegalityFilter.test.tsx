import { cleanup, fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { TextFormat } from '../../../helpers/fmt';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { Legality, LegalityKey } from '../../../models/Legality';
import { LegalityFilter } from './LegalityFilter';

const mockOnChange = vi.fn();
const mockOnClear = vi.fn();

const DEFAULT_PROPS: ComponentProps<typeof LegalityFilter> = {
  onChange: mockOnChange,
  currentLegality: Legality.STANDARD,
  displayType: FilterDisplay.REMOVEABLE,
  disabled: false,
  onClear: mockOnClear,
};

const renderLegalityFilter = (props: Partial<ComponentProps<typeof LegalityFilter>> = {}) => {
  return render(<LegalityFilter {...DEFAULT_PROPS} {...props} />);
};

// Helper to get capitalized legality values
const getFormattedLegalityOptions = () => {
  return Object.keys(Legality).map((key) => TextFormat.capitalizeWord(Legality[key as LegalityKey]));
};

describe('LegalityFilter', () => {
  describe('when rendered', () => {
    describe('with FilterTitle', () => {
      it('renders title', () => {
        renderLegalityFilter();
        expect(screen.getByText('Legality')).toBeInTheDocument();
      });
    });
    describe('when on mount', () => {
      test('onPropChange triggered', () => {
        renderLegalityFilter({
          currentLegality: 'inactive',
        });
        expect(mockOnChange).toHaveBeenCalledWith(Legality.STANDARD);
      });
      test('onPropChange not triggered', () => {
        renderLegalityFilter({
          displayType: FilterDisplay.PERMANENT,
        });
        expect(mockOnChange).not.toHaveBeenCalled();
      });
    });
  });
  describe('select element', () => {
    it('renders select element', () => {
      renderLegalityFilter();
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });
    it('renders select options', () => {
      renderLegalityFilter();
      const expectedOptions = getFormattedLegalityOptions();
      expectedOptions.forEach((optionText) => {
        expect(screen.getByRole('option', { name: optionText })).toBeInTheDocument();
      });
    });
    test('renders with default currentLegality prop', () => {
      renderLegalityFilter({ currentLegality: Legality.MODERN });
      const select = screen.getByRole<HTMLSelectElement>('combobox');
      expect(select.value).toBe(Legality.MODERN);
    });

    test('onChange prop is triggered', () => {
      renderLegalityFilter();
      fireEvent.change(screen.getByRole('combobox'), { target: { value: Legality.MODERN } });
      expect(mockOnChange).toHaveBeenCalledWith(Legality.MODERN);
    });

    it('renders no filter options', () => {
      // when displayType is PERMANENT, should add 'No Filter' option
      renderLegalityFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByRole('option', { name: 'No Filter' })).toBeInTheDocument();

      cleanup();
      renderLegalityFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.queryByRole('option', { name: 'No Filter' })).not.toBeInTheDocument();
    });

    it('disabled legality select element', () => {
      renderLegalityFilter({ disabled: true });
      expect(screen.getByRole('combobox')).toBeDisabled();
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders a remove button', () => {
      renderLegalityFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders a clear button', () => {
      renderLegalityFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    test('onClear prop is triggered', () => {
      renderLegalityFilter();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);

      // when ClearFilterButton is clicked, change default value to inactive
      expect(mockOnChange).toHaveBeenCalledExactlyOnceWith('inactive');
    });

    it('disabled clear button', () => {
      renderLegalityFilter({ disabled: true });
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).not.toHaveBeenCalled();
    });
  });
});
