import * as React from 'react';
import { TextFormat } from '../../../helpers/fmt';
import { SelectValue } from '../../../helpers/select';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { Legality, type LegalityKey } from '../../../models/Legality';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IProps {
  onChange: (legality: SelectValue<Legality>) => void;
  currentLegality: SelectValue<Legality>;
  onClear?: () => void;
  displayType: FilterDisplay;
  disabled?: boolean;
}

export const LegalityFilter = (props: IProps) => {
  React.useEffect(() => {
    if (props.currentLegality === 'inactive' && props.displayType === FilterDisplay.REMOVEABLE) {
      props.onChange(Legality.STANDARD);
    }
  }, []);

  function onChangeFilter(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    props.onChange(evt.currentTarget.value as SelectValue<Legality>);
  }

  return (
    <>
      <FilterTitle name="Legality">
        <ClearFilterButton
          disabled={props.disabled}
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          onClick={() => {
            props.onChange('inactive');
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <select
        className="select"
        disabled={props.disabled}
        value={props.currentLegality}
        onChange={onChangeFilter.bind(this)}
      >
        {/* Seting value={undefined} does not return undefined - it returns the text in the <option> tag.*/}
        {props.displayType === FilterDisplay.PERMANENT ? <option value={'inactive'}>No Filter</option> : null}
        {Object.keys(Legality).map((key) => (
          <option key={key} value={Legality[key as LegalityKey]}>
            {TextFormat.capitalizeWord(Legality[key as LegalityKey])}
          </option>
        ))}
      </select>
    </>
  );
};
