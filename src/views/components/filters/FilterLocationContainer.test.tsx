import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import { ComponentProps } from 'react';
import { beforeEach, describe, expect, it, test, vi } from 'vitest';
import { FakeStackLocation } from '../../../../tests/fake/FakeStackLocation';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as StackLocationActions from '../../../actions/StackLocationActions';
import { StackLocation } from '../../../models/StackLocation';
import { FilterLocationContainer } from './FilterLocationContainer';

vi.mock('../../../actions/StackLocationActions');

const mockUpdatePath = vi.fn();
const mockDeleteLastLocation = vi.fn();
const mockOnClear = vi.fn();

const fakeStackLocation = new FakeStackLocation();

const createLocationPath = ({ locations = [] as StackLocation[] } = {}) => {
  return Immutable.List(locations);
};

const DEFAULT_PROPS = {
  locationPath: createLocationPath(),
  updatePath: mockUpdatePath,
  deleteLastLocation: mockDeleteLastLocation,
  onClear: mockOnClear,
};

const renderFilterLocationContainer = (
  props: Partial<Omit<ComponentProps<typeof FilterLocationContainer>, 'dispatcher'>> = {},
) => {
  return renderWithDispatcher(FilterLocationContainer, {
    ...DEFAULT_PROPS,
    ...props,
  });
};

// Helper functions
const expectFilterLocationCount = ({ count }: { count: number }) => {
  const filterLocations = screen.getAllByRole('textbox');
  expect(filterLocations).toHaveLength(count);
};

describe('FilterLocationContainer', () => {
  beforeEach(() => {
    // Mock StackLocationActions to return empty results by default
    vi.mocked(StackLocationActions.getParents).mockResolvedValue(Immutable.List<StackLocation>());
    vi.mocked(StackLocationActions.getChildren).mockResolvedValue(Immutable.Map<string, StackLocation>());
  });

  describe('with FilterTitle', () => {
    it('renders title', () => {
      renderFilterLocationContainer();
      expect(screen.getByText('Location')).toBeInTheDocument();
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders remove button', () => {
      renderFilterLocationContainer();
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    test('triggers onClear prop', () => {
      renderFilterLocationContainer();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });
  });

  describe('with FilterLocation', () => {
    describe('with locationPath prop', () => {
      it('handles empty path', () => {
        renderFilterLocationContainer({ locationPath: createLocationPath() });
        expectFilterLocationCount({ count: 1 });
      });

      it('handles single location', () => {
        const location = fakeStackLocation.fake();
        const locationPath = createLocationPath({ locations: [location] });
        renderFilterLocationContainer({ locationPath });
        expectFilterLocationCount({ count: 2 });
      });

      it('handles multiple locations', () => {
        const locations = [fakeStackLocation.fake(), fakeStackLocation.fake()];
        const locationPath = createLocationPath({ locations });
        renderFilterLocationContainer({ locationPath });
        expectFilterLocationCount({ count: 3 });
      });
    });

    describe('with clearable prop', () => {
      it('sets clearable=true for single location path', () => {
        const location = fakeStackLocation.fake();
        const locationPath = createLocationPath({ locations: [location] });
        renderFilterLocationContainer({ locationPath });
        expect(screen.getAllByText('Clear')).toHaveLength(1);
      });

      it('sets clearable=false except for last location', () => {
        const locations = [fakeStackLocation.fake(), fakeStackLocation.fake()];
        const locationPath = createLocationPath({ locations });
        renderFilterLocationContainer({ locationPath });
        expect(screen.getAllByText('Clear')).toHaveLength(1);
      });
    });

    describe('with last prop', () => {
      it('sets last=true for empty path', () => {
        renderFilterLocationContainer({ locationPath: createLocationPath() });
        expectFilterLocationCount({ count: 1 });
      });

      it('sets last=true only for final location', () => {
        const locations = [fakeStackLocation.fake(), fakeStackLocation.fake()];
        const locationPath = createLocationPath({ locations });
        renderFilterLocationContainer({ locationPath });
        expectFilterLocationCount({ count: 3 });
      });
    });

    describe('with onLoad prop', () => {
      it('calls initLocations for first FilterLocation', () => {
        renderFilterLocationContainer();
        expect(vi.mocked(StackLocationActions.getParents)).toHaveBeenCalledWith(expect.any(Object));
      });

      it('handles location with id=-1', () => {
        const location = new StackLocation({ id: -1 });
        const locationPath = createLocationPath({ locations: [location] });
        renderFilterLocationContainer({ locationPath });
        expectFilterLocationCount({ count: 2 });
      });

      it('handles valid location', () => {
        const location = new StackLocation({ id: 1 });
        const locationPath = createLocationPath({ locations: [location] });
        renderFilterLocationContainer({ locationPath });
        expectFilterLocationCount({ count: 2 });
      });
    });
  });
});
