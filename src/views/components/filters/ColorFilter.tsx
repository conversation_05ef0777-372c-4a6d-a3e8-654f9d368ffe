import * as React from 'react';
import { TextFormat } from '../../../helpers/fmt';
import { ColorFilter as ColorFilterModel, ColorOption, type ColorOptionKey } from '../../../models/filters/ColorFilter';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { FilterState, nextFilterState } from '../../../models/filters/FilterState';
import { ClearFilterButton } from './ClearFilterButton';
import { ExcludeUnselectedButton } from './ExcludeUnselectedButton';
import { FilterTitle } from './FilterTitle';
import { GateFilterIcon } from './GateFilterIcon';

interface IProps {
  colorFilters?: ColorFilterModel;
  onUpdateFilter: (manaFilters: ColorFilterModel) => void;
  disabled?: boolean;
  displayType: FilterDisplay;
  onClear?: () => void;
}

export const ColorFilter = (props: IProps) => {
  const disabled = props.disabled === true;
  return (
    <>
      <FilterTitle name="Color">
        <ExcludeUnselectedButton disabled={disabled} onClick={() => onExcludeUnselected()} />
        <ClearFilterButton
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          disabled={disabled}
          onClick={() => {
            props.onClear && props.onClear();
          }}
        />
      </FilterTitle>
      <div className="filter-symbol-container">
        {Object.keys(ColorOption).map((key) => (
          <GateFilterIcon
            disabled={disabled}
            key={ColorOption[key as ColorOptionKey]}
            hoverText={TextFormat.capitalizeWord(ColorOption[key as ColorOptionKey])}
            iconPath={`/svg/mana-${ColorOption[key as ColorOptionKey]}.svg`}
            filterState={
              props.colorFilters ? props.colorFilters.get(ColorOption[key as ColorOptionKey]) : FilterState.INACTIVE
            }
            onClick={() => onChange(ColorOption[key as ColorOptionKey])}
          />
        ))}
      </div>
    </>
  );

  function onChange(inputColor: ColorOption) {
    let newColor = props.colorFilters || new ColorFilterModel();
    const nextState = nextFilterState(newColor.get(inputColor));
    newColor = newColor.set(inputColor, nextState);
    props.onUpdateFilter(newColor);
  }

  function onExcludeUnselected() {
    let color = props.colorFilters || new ColorFilterModel();
    [...ColorFilterModel.onlyColors(), ColorOption.COLORLESS]
      .filter((manaOpt) => color.get(manaOpt) === FilterState.INACTIVE)
      .forEach((manaOpt) => {
        color = color.set(manaOpt, FilterState.OFF);
      });
    props.onUpdateFilter(color);
  }
};
