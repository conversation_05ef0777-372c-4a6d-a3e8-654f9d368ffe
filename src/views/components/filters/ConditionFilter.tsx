import image_search from '@iconify/icons-ic/baseline-image-search';
import * as React from 'react';
import { SelectValue } from '../../../helpers/select';
import { Condition, type ConditionKey } from '../../../models/Condition';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { IconSelect } from '../IconSelect';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IProps {
  onChange: (condition: SelectValue<Condition>) => void;
  currentCondition: SelectValue<Condition>;
  className: string;
  displayType: FilterDisplay;
  disabled?: boolean;
  onClear?: () => void;
}

export const ConditionFilter = (props: IProps) => {
  React.useEffect(() => {
    if (props.currentCondition === 'inactive') {
      props.onChange(Condition.NEAR_MINT);
    }
  }, []);

  function onChangeFilter(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    props.onChange(evt.currentTarget.value as SelectValue<Condition>);
  }

  return (
    <>
      <FilterTitle name="Condition">
        <ClearFilterButton
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          onClick={() => {
            props.onChange('inactive');
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <IconSelect
        className={props.className}
        disabled={props.disabled}
        icon={image_search}
        value={props.currentCondition}
        onChange={onChangeFilter}
      >
        <>
          <option disabled>Condition</option>
          {Object.keys(Condition).map((key) => (
            <option key={key} value={Condition[key as ConditionKey]}>
              {Condition[key as ConditionKey]}
            </option>
          ))}
        </>
      </IconSelect>
    </>
  );
};
