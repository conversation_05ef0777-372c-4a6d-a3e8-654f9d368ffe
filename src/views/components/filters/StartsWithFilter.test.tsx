import { faker } from '@faker-js/faker';
import { cleanup, fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { StartsWithFilter } from './StartsWithFilter';

const mockUpdate = vi.fn();
const mockOnClear = vi.fn();

const DEFAULT_PROPS: ComponentProps<typeof StartsWithFilter> = {
  displayType: FilterDisplay.REMOVEABLE,
  update: mockUpdate,
  onClear: mockOnClear,
};

const renderStartsWithFilter = (props: Partial<ComponentProps<typeof StartsWithFilter>> = {}) => {
  return render(<StartsWithFilter {...DEFAULT_PROPS} {...props} />);
};

describe('StartsWithFilter', () => {
  describe('when rendered', () => {
    it('renders input', () => {
      renderStartsWithFilter();
      const input = screen.getByRole<HTMLInputElement>('textbox');
      expect(input).toBeInTheDocument();
      expect(input.value).toBe('');
    });

    it('renders input with correct placeholder', () => {
      renderStartsWithFilter();
      const input = screen.getByPlaceholderText('Enter letter');
      expect(input).toBeInTheDocument();
    });

    it('renders input with maxLength attribute', () => {
      renderStartsWithFilter();
      const input = screen.getByRole<HTMLInputElement>('textbox');
      expect(input).toHaveAttribute('maxLength', '1');
    });

    test('uses startsWith prop as initial value', () => {
      const startsWith = faker.string.alpha(1);
      renderStartsWithFilter({ startsWith });
      const input = screen.getByRole<HTMLInputElement>('textbox');
      expect(input.value).toBe(startsWith);
    });
  });

  describe('with FilterTitle', () => {
    it('renders default title', () => {
      renderStartsWithFilter();
      expect(screen.getByText('Starts With')).toBeInTheDocument();
    });

    it('renders custom title', () => {
      const customTitle = faker.lorem.words(2);
      renderStartsWithFilter({ titleOverride: customTitle });
      expect(screen.getByText(customTitle)).toBeInTheDocument();
    });
  });

  describe('with input element', () => {
    test('triggers update prop on change', () => {
      renderStartsWithFilter();
      const input = screen.getByRole('textbox');
      const value = faker.string.alpha(1);
      fireEvent.change(input, { target: { value } });
      expect(mockUpdate).toHaveBeenCalledWith(value);
    });

    test('triggers update props on change and clear', () => {
      const initialValue = faker.string.alpha(1);
      renderStartsWithFilter({ startsWith: initialValue });
      const input = screen.getByRole('textbox');
      const newValue = faker.string.alpha(1);

      // Change to a new value
      fireEvent.change(input, { target: { value: newValue } });
      expect(mockUpdate).toHaveBeenNthCalledWith(1, newValue);

      // Clear the input
      fireEvent.change(input, { target: { value: '' } });
      expect(mockUpdate).toHaveBeenNthCalledWith(2, '');
      expect(mockUpdate).toHaveBeenCalledTimes(2);
    });

    describe('disabled input element', () => {
      it('uses disabled prop', () => {
        renderStartsWithFilter({ disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();
      });

      it('uses advancedQuery prop', () => {
        renderStartsWithFilter({ disabled: false, advancedQuery: faker.lorem.word() });
        expect(screen.getByRole('textbox')).toBeDisabled();
      });

      it('not disabled when advancedQuery is empty', () => {
        renderStartsWithFilter({ disabled: false, advancedQuery: '' });
        expect(screen.getByRole('textbox')).not.toBeDisabled();
      });
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders Remove button', () => {
      renderStartsWithFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders Clear button', () => {
      renderStartsWithFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    test('onClear prop is triggered', () => {
      renderStartsWithFilter();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });

    it('enabled when advancedQuery is empty', () => {
      renderStartsWithFilter({ advancedQuery: '' });
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });

    it('disabled ClearFilterButton', () => {
      renderStartsWithFilter({ disabled: true });
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).not.toHaveBeenCalled();

      cleanup();
      renderStartsWithFilter({ advancedQuery: faker.lorem.word() });
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).not.toHaveBeenCalled();
    });
  });
});
