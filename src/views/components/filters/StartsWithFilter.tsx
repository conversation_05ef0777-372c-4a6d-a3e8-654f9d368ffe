import * as React from 'react';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IProps {
  disabled?: boolean;
  startsWith?: string;
  advancedQuery?: string;
  titleOverride?: string;
  displayType: FilterDisplay;
  update: (query: string) => void;
  onClear?: () => void;
}

export const StartsWithFilter = (props: IProps) => {
  function onChangeQuery(evt: React.SyntheticEvent<HTMLInputElement>) {
    const query = evt.currentTarget.value || '';
    props.update(query);
  }

  // Stops React throwing an "uncontrolled input" warning
  const query = props.startsWith || '';
  return (
    <>
      <FilterTitle name={props.titleOverride || 'Starts With'}>
        <ClearFilterButton
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          disabled={props.disabled === true || (props.advancedQuery !== undefined && props.advancedQuery !== '')}
          onClick={() => {
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <input
        className="col-xs-12 filter-no-padding advanced-search-input"
        type="text"
        placeholder="Enter letter"
        value={query}
        onChange={onChangeQuery}
        disabled={props.disabled === true || (props.advancedQuery !== undefined && props.advancedQuery !== '')}
        maxLength={1}
      />
    </>
  );
};
