import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { CardList } from '../../../models/CardList';
import { FilterComponentType } from '../../../models/FilterComponent';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { MTGFilter } from '../../../models/filters/mtg/MTGFilters';
import { Game } from '../../../models/Game';
import { Dialog, DialogSize } from '../Dialog';
import { AddFilterComponent } from './AddFilterComponent';
import { ComposableFilter } from './ComposableFilter';
import { UpdateFiltersButton } from './UpdateFiltersButton';

interface IProps {
  title: string;
  dispatcher: Dispatcher;
  isOpen: boolean;
  filters: MTGFilter;
  cardLists: Immutable.OrderedMap<string, CardList>;
  onUpdate: (filters: MTGFilter) => void;
  onDismiss: () => void;
  description?: React.ReactNode;
  filterOverride?: MTGFilter;
  disabledFilters?: Immutable.List<FilterComponentType>;
}

export const FilterDialog = (props: IProps) => {
  const [portal, setPortal] = React.useState<HTMLDivElement | undefined>(undefined);

  const refCallback = React.useCallback((node: HTMLDivElement) => {
    setPortal(node || undefined);
  }, []);

  const [localFilter, updateLocalFilter] = React.useState<MTGFilter>(props.filters);

  React.useEffect(() => {
    if (!props.isOpen) {
      updateLocalFilter(props.filters);
    }
  }, [props.filters]);

  return (
    <Dialog
      overflowRef={refCallback}
      className="dialog-left-align"
      isOpen={props.isOpen}
      isClosable
      isDismissible
      onDismiss={(evt: React.SyntheticEvent<HTMLElement>) => {
        evt.preventDefault();
        props.onDismiss();
      }}
      size={DialogSize.LARGE_DYNAMIC}
    >
      <div className="col-xs-12">
        <div className="dialog-heading" style={{ marginLeft: 0 }}>
          {props.title}
        </div>
        {props.description && (
          <div className="dialog-subheading-xs" style={{ marginLeft: 0 }}>
            {props.description}
          </div>
        )}
        <div className="section-divider" />
        <div className="filter-dialog-container">
          <div className="row" style={{ padding: '1rem', display: 'flex', gap: '1rem' }}>
            {portal && generateComponents()}
            {portal && (
              <AddFilterComponent
                onUpdate={(newFilter: MTGFilter) => updateLocalFilter(newFilter)}
                localFilter={localFilter}
                addFilter={addFilter}
                restrictedOptions={props.disabledFilters}
                menuPortal={portal}
              />
            )}
          </div>
        </div>
      </div>
      <div className="filter-no-padding col-xs-12" style={{ borderTop: '1px solid #CDCDCD' }}>
        <div className="flex" style={{ marginTop: '1rem' }}>
          <UpdateFiltersButton activeFiltersCount={localFilter.countActiveFilters()} onClick={updateFilters} />
          <button
            className="button-alert filter-dialog-cancel"
            onClick={(evt: React.SyntheticEvent<HTMLElement>) => {
              evt.preventDefault();
              props.onDismiss();
            }}
          >
            Cancel
          </button>
        </div>
      </div>
    </Dialog>
  );

  function generateComponents(): JSX.Element[] {
    return localFilter
      .get('filterOrder')
      .reduce((reduction: Immutable.List<JSX.Element>, filterType: FilterComponentType) => {
        return reduction.push(
          <ComposableFilter
            key={filterType}
            filter={localFilter}
            filterOverride={props.filterOverride}
            dispatcher={props.dispatcher}
            type={filterType}
            displayType={FilterDisplay.REMOVEABLE}
            game={Game.MTG}
            onUpdate={(filter: MTGFilter) => {
              updateLocalFilter(filter);
            }}
            menuPortal={portal}
          />,
        );
      }, Immutable.List<JSX.Element>())
      .toArray();
  }

  function addFilter(type: FilterComponentType) {
    updateLocalFilter(localFilter.set('filterOrder', localFilter.get('filterOrder').add(type)));
  }

  function updateFilters(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    props.onUpdate(localFilter);
    props.onDismiss();
  }
};
