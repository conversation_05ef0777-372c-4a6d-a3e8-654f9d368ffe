import * as Immutable from 'immutable';
import * as React from 'react';
import * as StackLocationActions from '../../../actions/StackLocationActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { StackLocation } from '../../../models/StackLocation';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterLocation } from './FilterLocation';
import { FilterTitle } from './FilterTitle';

interface IProps {
  dispatcher: Dispatcher;
  locationPath: Immutable.List<StackLocation>;
  updatePath: (index: number, location: StackLocation) => void;
  deleteLastLocation: () => void;
  onClear: () => void;
}

interface IState {}

export class FilterLocationContainer extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  private async initLocations(index: number, location?: StackLocation) {
    return await StackLocationActions.getParents(this.props.dispatcher);
  }

  private async onLoad(index: number, location?: StackLocation): Promise<Immutable.List<StackLocation>> {
    if (location === undefined || location.get('id') === -1) {
      return Immutable.List<StackLocation>();
    }
    return (await StackLocationActions.getChildren(location.get('uuid'), index, this.props.dispatcher)).toList();
  }

  private displayInputs() {
    let inputs = Immutable.List<JSX.Element>();
    inputs = inputs.push(
      <FilterLocation
        key={0}
        index={0}
        dispatcher={this.props.dispatcher}
        parentLocation={undefined}
        value={this.props.locationPath.get(0)}
        update={(location: StackLocation) => this.props.updatePath.bind(this)(0, location)}
        remove={this.props.locationPath.size === 1 ? this.props.deleteLastLocation.bind(this) : undefined}
        last={this.props.locationPath.size === 0}
        clearable={this.props.locationPath.size === 1}
        onLoad={this.initLocations.bind(this)}
      />,
    );
    for (let i = 1; i < this.props.locationPath.size + 1; i++) {
      const parent = this.props.locationPath.get(i - 1);
      const clearable = this.props.locationPath.size - 1 === i;
      const last = this.props.locationPath.size === i;
      inputs = inputs.push(
        <FilterLocation
          key={i}
          index={i}
          dispatcher={this.props.dispatcher}
          parentLocation={parent}
          value={this.props.locationPath.get(i)}
          update={(location: StackLocation) => this.props.updatePath.bind(this)(i, location)}
          remove={clearable ? this.props.deleteLastLocation.bind(this) : undefined}
          last={last}
          clearable={clearable}
          onLoad={this.onLoad.bind(this)}
        />,
      );
    }
    return inputs;
  }

  render() {
    return (
      <>
        <FilterTitle name="Location">
          <ClearFilterButton
            label={'Remove'}
            onClick={() => {
              this.props.onClear();
            }}
          />
        </FilterTitle>
        <div style={{ overflow: 'hidden scroll' }}>
          <div>
            <div className="col-xs-12" style={{ marginBottom: '1rem', padding: 0 }}>
              <div className="flex" style={{ flexFlow: 'row wrap' }}>
                {this.displayInputs()}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}
