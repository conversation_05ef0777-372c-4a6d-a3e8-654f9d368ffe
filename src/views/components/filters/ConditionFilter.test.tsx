import { faker } from '@faker-js/faker';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { SelectValue } from '../../../helpers/select';
import { Condition } from '../../../models/Condition';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { ConditionFilter } from './ConditionFilter';

const mockOnChange = vi.fn();
const mockOnClear = vi.fn();

type ConditionFilterProps = ComponentProps<typeof ConditionFilter>;

const DEFAULT_PROPS: ConditionFilterProps = {
  onChange: mockOnChange,
  currentCondition: 'inactive' as SelectValue<Condition>,
  className: faker.lorem.word(),
  displayType: FilterDisplay.REMOVEABLE,
  onClear: mockOnClear,
};

const renderConditionFilter = (props: Partial<ConditionFilterProps> = {}) => {
  return render(<ConditionFilter {...DEFAULT_PROPS} {...props} />);
};

describe('ConditionFilter', () => {
  describe('when rendered', () => {
    describe('with FilterTitle', () => {
      it('renders title', () => {
        renderConditionFilter();
        expect(screen.getByText('Condition', { selector: ':not(option)' })).toBeInTheDocument();
      });
    });

    test('renders select', () => {
      renderConditionFilter();
      const select = screen.getByRole<HTMLSelectElement>('combobox');
      expect(select).toBeInTheDocument();
      expect(select).not.toBeDisabled();
    });

    test('renders condition options', () => {
      renderConditionFilter();
      Object.keys(Condition).forEach((key) => {
        const condition = Condition[key as keyof typeof Condition];
        expect(screen.getByRole('option', { name: condition })).toBeInTheDocument();
      });
    });
    describe('with currentCondition props', () => {
      test('sets to NEAR_MINT on mount', async () => {
        renderConditionFilter({ currentCondition: 'inactive' });
        await waitFor(() => {
          expect(mockOnChange).toHaveBeenCalledWith(Condition.NEAR_MINT);
        });
      });

      test('does not call onChange when not inactive', () => {
        renderConditionFilter({ currentCondition: Condition.LIGHTLY_PLAYED });
        expect(mockOnChange).not.toHaveBeenCalled();
      });
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders Remove button', () => {
      renderConditionFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders Clear button', () => {
      renderConditionFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    test('sets to inactive when clicked', () => {
      renderConditionFilter();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnChange).toHaveBeenCalledWith('inactive');
    });

    test('triggers onClear prop', () => {
      renderConditionFilter();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });
  });

  describe('with IconSelect', () => {
    test('displays current value', () => {
      renderConditionFilter({ currentCondition: Condition.HEAVILY_PLAYED });
      const select = screen.getByRole<HTMLSelectElement>('combobox');
      expect(select.value).toBe(Condition.HEAVILY_PLAYED);
    });

    test('disables when disabled', () => {
      renderConditionFilter({ disabled: true });
      expect(screen.getByRole('combobox')).toBeDisabled();
    });

    test('triggers onChange prop', () => {
      renderConditionFilter({ currentCondition: Condition.NEAR_MINT });

      fireEvent.change(screen.getByRole('combobox'), {
        target: { value: Condition.DAMAGED },
      });

      expect(mockOnChange).toHaveBeenCalledWith(Condition.DAMAGED);
    });
  });
});
