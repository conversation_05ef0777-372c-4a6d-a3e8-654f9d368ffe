import classNames from 'classnames';
import * as React from 'react';
import { FilterState } from '../../../models/filters/FilterState';

interface IProps {
  readonly iconPath: string;
  readonly filterState: FilterState;
  readonly hoverText: string;
  readonly onClick: () => void;
  readonly disabled?: boolean;
}

export const GateFilterIcon = (props: IProps) => {
  const [hover, setHover] = React.useState<boolean>(false);
  const [delayHandler, setDelayHandler] = React.useState<NodeJS.Timer | undefined>(undefined);
  const imageClassName = classNames({
    'filter-symbol': props.filterState !== FilterState.INACTIVE,
    'filter-symbol__faded': props.filterState === FilterState.INACTIVE,
  });
  const buttonClassName = classNames('card-filter-button', { 'filter-cross': props.filterState === FilterState.OFF });
  return (
    <button
      disabled={props.disabled === undefined ? false : props.disabled}
      onClick={() => props.onClick()}
      className={buttonClassName}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <img src={props.iconPath} className={imageClassName} width="32px" height="32px" />
      {hover ? (
        <div className="filter-tooltip-container">
          <div className="filter-tooltip">
            <div className="filter-tooltip-hover-text">{props.hoverText}</div>
          </div>
        </div>
      ) : null}
    </button>
  );

  function onMouseEnter(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    setDelayHandler(
      setTimeout(() => {
        setHover(true);
      }, 500),
    );
  }

  function onMouseLeave(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    if (delayHandler) {
      clearTimeout(delayHandler);
    }
    setHover(false);
  }
};
