import { fireEvent, render, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { TextFormat } from '../../../helpers/fmt';
import { CardSetType } from '../../../models/CardSets';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { SetTypeFilter } from '../../../models/filters/SetTypeFilter';
import { SelectSetType } from './SelectSetType';

const mockUpdate = vi.fn();
const mockOnClear = vi.fn();

type SelectSetTypeProps = Partial<ComponentProps<typeof SelectSetType>>;

const createSetTypeFilter = (setType: CardSetType, include = true) => {
  return new SetTypeFilter({ include, setType });
};

const createSetTypesMap = (setTypes: { type: CardSetType; include?: boolean }[] = []) => {
  return Immutable.OrderedMap<string, SetTypeFilter>(
    setTypes.map(({ type, include = true }) => [type, createSetTypeFilter(type, include)]),
  );
};

const mockSetTypes = createSetTypesMap([
  { type: CardSetType.CORE, include: true },
  { type: CardSetType.EXPANSION, include: false },
]);

const capitalize = (str: string) => {
  return TextFormat.capitalize(str);
};

const DEFAULT_PROPS = {
  setTypes: Immutable.OrderedMap<string, SetTypeFilter>(),
  update: mockUpdate,
  displayType: FilterDisplay.REMOVEABLE,
  onClear: mockOnClear,
};
const renderSelectSetType = (props: SelectSetTypeProps = {}) => {
  return render(<SelectSetType {...DEFAULT_PROPS} {...props} />);
};

describe('SelectSetType', () => {
  describe('when rendered', () => {
    describe('with FilterTitle', () => {
      it('renders title', () => {
        renderSelectSetType();
        expect(screen.getByText('Set Type')).toBeInTheDocument();
      });
    });
    it('shows placeholder when empty', () => {
      renderSelectSetType();
      expect(screen.getByText('Select...')).toBeInTheDocument();
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders a remove button', () => {
      renderSelectSetType({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders a clear button', () => {
      renderSelectSetType({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    it('disabled ClearFilterButton', () => {
      renderSelectSetType({ disabled: true });
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).not.toHaveBeenCalled();
      expect(mockUpdate).not.toHaveBeenCalled();
    });

    test('onClear prop is triggered', () => {
      renderSelectSetType();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });
  });

  describe('with react-select component', () => {
    it('displays selected items', () => {
      renderSelectSetType({ setTypes: mockSetTypes });
      expect(screen.getByText(capitalize(CardSetType.CORE))).toBeInTheDocument();
      expect(screen.getByText(capitalize(CardSetType.EXPANSION))).toBeInTheDocument();
    });

    it('shows excluded items with strikethrough', () => {
      renderSelectSetType({ setTypes: mockSetTypes });
      expect(screen.getByText(capitalize(CardSetType.EXPANSION))).toHaveStyle({ textDecorationLine: 'line-through' });
    });

    it('shows included items without strikethrough', () => {
      renderSelectSetType({ setTypes: mockSetTypes });
      expect(screen.getByText(capitalize(CardSetType.CORE))).toHaveStyle({ textDecorationLine: '' });
    });

    test('toggles include/exclude on click', async () => {
      renderSelectSetType({ setTypes: mockSetTypes });

      fireEvent.click(screen.getByText(capitalize(CardSetType.CORE)));

      // Get the first argument from the first call to mockUpdate (the updated Immutable.OrderedMap)
      const updateCall = mockUpdate.mock.calls[0][0];
      expect(updateCall.has(CardSetType.CORE)).toBe(true);
    });

    test('adds selected option', () => {
      renderSelectSetType();

      fireEvent.mouseDown(screen.getByRole('textbox'));

      expect(screen.getByText(capitalize(CardSetType.CORE))).toBeInTheDocument();

      fireEvent.click(screen.getByText(capitalize(CardSetType.CORE)));

      expect(mockUpdate).toHaveBeenCalled();
      // Get the first argument from the first call to mockUpdate (the updated Immutable.OrderedMap)
      const updateCall = mockUpdate.mock.calls[0][0];
      expect(updateCall.has(CardSetType.CORE)).toBe(true);
    });

    it('filters out selected options from menu', () => {
      const setTypes = createSetTypesMap([{ type: CardSetType.CORE }]);
      const { container } = renderSelectSetType({ setTypes });

      fireEvent.mouseDown(screen.getByRole('textbox'));

      expect(screen.getByText(capitalize(CardSetType.EXPANSION))).toBeInTheDocument();
      expect(screen.getByText(capitalize(CardSetType.CORE))).toBeInTheDocument();
      const dropdownMenu = container.querySelector('.react-select-type__menu');
      if (dropdownMenu) {
        expect(dropdownMenu).not.toHaveTextContent(capitalize(CardSetType.CORE));
      }
    });

    describe('with disabled prop', () => {
      it('disables select component', () => {
        renderSelectSetType({ disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();
      });

      it('prevents toggling when clicking selected items', () => {
        const setTypes = createSetTypesMap([{ type: CardSetType.CORE }]);
        renderSelectSetType({ setTypes, disabled: true });

        fireEvent.click(screen.getByText(capitalize(CardSetType.CORE)));

        expect(mockUpdate).not.toHaveBeenCalled();
      });
    });
  });
});
