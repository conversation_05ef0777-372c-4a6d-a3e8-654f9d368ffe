import { faker } from '@faker-js/faker';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import * as request from '../../../../src/api/Requests';
import { CardSetFilter } from '../../../../src/models/filters/CardSetFilter';
import { FilterDisplay } from '../../../../src/models/filters/FilterDisplay';
import { create, createArray, FakeCardSetFilter } from '../../../../tests/fake/Fake';
import SetFilter from './SetFilter';

const mockUpdateSetFilter = vi.fn();
const mockOnClear = vi.fn();

vi.mock('../../../../src/api/Requests', () => ({
  get: vi.fn(),
}));

// Use faker for API response data
const createMockApiSet = () => ({
  id: faker.number.int({ min: 1, max: 1000 }),
  name: faker.commerce.productName(),
  release_date: faker.date.past().toISOString().split('T')[0],
  set_code: faker.string.alpha({ length: 3 }).toUpperCase(),
  set_type: faker.helpers.arrayElement(['expansion', 'core', 'supplement']),
});

const mockApiSets = Array.from({ length: 3 }, createMockApiSet);
const mockCardSetFilter = create(FakeCardSetFilter);
const mockCardSetFilters = createArray(FakeCardSetFilter, 3);

const DEFAULT_PROPS = {
  disabled: false,
  cardSetFilters: Immutable.OrderedMap<string, CardSetFilter>(),
  updateSetFilter: mockUpdateSetFilter,
  displayType: FilterDisplay.PERMANENT,
};

// Helper functions
const renderSetFilter = (props: Partial<ComponentProps<typeof SetFilter>> = {}) => {
  return render(<SetFilter {...DEFAULT_PROPS} {...props} />);
};

const getSelect = () => screen.getByRole('textbox');
const typeInSearch = (text: string) => fireEvent.change(getSelect(), { target: { value: text } });

const mockApiResponse = (cardSets = mockApiSets) => {
  const mockRequest = {
    end: vi.fn().mockResolvedValue({ body: { card_sets: cardSets } }),
    abort: vi.fn(),
  };
  (request.get as any).mockReturnValue(mockRequest);
  return mockRequest;
};

const mockApiError = (error = new Error('API Error')) => {
  const mockRequest = {
    end: vi.fn().mockRejectedValue(error),
    abort: vi.fn(),
  };
  (request.get as any).mockReturnValue(mockRequest);
  return mockRequest;
};

const createFilterMap = (filters: CardSetFilter[]) => {
  return Immutable.OrderedMap<string, CardSetFilter>(filters.map((filter) => [filter.get('cardSet').name(), filter]));
};

const setupMockBlur = () => {
  const mockBlur = vi.fn();
  Object.defineProperty(document, 'activeElement', {
    value: { blur: mockBlur },
    writable: true,
  });
  return mockBlur;
};

describe('SetFilter', () => {
  describe('when rendered', () => {
    it('displays all required elements', () => {
      renderSetFilter();
      expect(screen.getByText('Set')).toBeInTheDocument();
      expect(getSelect()).toBeInTheDocument();
      expect(screen.getByText('Search by set name...')).toBeInTheDocument();
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    describe('with disabled prop', () => {
      it('disables input', () => {
        renderSetFilter({ disabled: true });
        expect(getSelect()).toBeDisabled();
      });
    });
  });

  describe('when user interacts with input', () => {
    it('accepts and displays typed input', () => {
      renderSetFilter();
      typeInSearch('test');
      expect(getSelect()).toHaveValue('test');
    });

    it('clears options on empty query', () => {
      renderSetFilter();
      typeInSearch('test');
      typeInSearch('');
      expect(getSelect()).toHaveValue('');
    });

    it('does not show No Results for short queries', () => {
      renderSetFilter();
      typeInSearch('ab');
      expect(screen.queryByText('No Results')).not.toBeInTheDocument();
    });

    it('shows loading state during search', () => {
      renderSetFilter();
      typeInSearch('test');
      expect(screen.getByText('Searching...')).toBeInTheDocument();
    });
  });

  describe('with API integration', () => {
    it('when user types', async () => {
      mockApiResponse();
      renderSetFilter();

      typeInSearch('test');

      await waitFor(() => {
        expect(request.get).toHaveBeenCalledWith('/api/v3/card_sets?query=test');
      });
    });

    describe('when error', () => {
      it('handles errors gracefully', async () => {
        mockApiError();
        renderSetFilter();
        typeInSearch('test');

        await waitFor(() => {
          expect(screen.queryByText('Searching...')).not.toBeInTheDocument();
        });
      });
    });

    describe('when empty results', () => {
      it('shows No Results message', async () => {
        mockApiResponse([]);
        renderSetFilter();

        typeInSearch('xyz');

        await waitFor(() => {
          expect(screen.getByText('No Results')).toBeInTheDocument();
        });
      });
    });

    describe('with existing selected sets', () => {
      it('filters out already selected sets from results', async () => {
        const existingFilters = createFilterMap([mockCardSetFilter]);

        mockApiResponse();
        renderSetFilter({ cardSetFilters: existingFilters });
        typeInSearch('test');

        await waitFor(() => {
          // Should show API results but filter is working
          expect(screen.getByText(mockApiSets[0].name)).toBeInTheDocument();
        });
      });
    });
  });

  describe('with CardSetFilterSuggestion', () => {
    it('displays search results', async () => {
      mockApiResponse();
      renderSetFilter();

      typeInSearch('test');

      await waitFor(() => {
        expect(screen.getByText(mockApiSets[0].name)).toBeInTheDocument();
      });
    });

    describe('when suggestion is selected', () => {
      it('calls updateSetFilter', async () => {
        mockApiResponse();
        renderSetFilter();

        typeInSearch('test');

        await waitFor(() => {
          expect(screen.getByText(mockApiSets[0].name)).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText(mockApiSets[0].name));
        expect(mockUpdateSetFilter).toHaveBeenCalled();
      });

      it('resets input state', async () => {
        mockApiResponse();
        renderSetFilter();

        typeInSearch('test');

        await waitFor(() => {
          expect(screen.getByText(mockApiSets[0].name)).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText(mockApiSets[0].name));
        expect(getSelect()).toHaveValue('');
      });
    });
  });

  describe('with selected sets displayed', () => {
    it('shows selected card sets', () => {
      const filters = createFilterMap([mockCardSetFilter]);

      renderSetFilter({ cardSetFilters: filters });
      expect(screen.getByText(mockCardSetFilter.get('cardSet').name())).toBeInTheDocument();
    });

    describe('when multiple sets are selected', () => {
      it('displays all selected sets', () => {
        const filters = createFilterMap([mockCardSetFilters[0], mockCardSetFilters[1]]);

        renderSetFilter({ cardSetFilters: filters });
        expect(screen.getByText(mockCardSetFilters[0].get('cardSet').name())).toBeInTheDocument();
        expect(screen.getByText(mockCardSetFilters[1].get('cardSet').name())).toBeInTheDocument();
      });
    });

    describe('when selected set is clicked', () => {
      it('toggles include state', () => {
        const filters = createFilterMap([mockCardSetFilter]);

        renderSetFilter({ cardSetFilters: filters });
        fireEvent.click(screen.getByText(mockCardSetFilter.get('cardSet').name()));
        expect(mockUpdateSetFilter).toHaveBeenCalled();
      });

      it('blurs active element', () => {
        const filters = createFilterMap([mockCardSetFilter]);
        const mockBlur = setupMockBlur();

        renderSetFilter({ cardSetFilters: filters });
        fireEvent.click(screen.getByText(mockCardSetFilter.get('cardSet').name()));
        expect(mockBlur).toHaveBeenCalled();
      });
    });
  });

  describe('with ClearFilterButton', () => {
    it('render Clear button', () => {
      renderSetFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    it('render Remove button', () => {
      renderSetFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('trigger onClear prop', () => {
      renderSetFilter({ onClear: mockOnClear });
      fireEvent.click(screen.getByText('Clear'));
      expect(mockOnClear).toHaveBeenCalled();
    });
  });

  describe('with menu portal prop', () => {
    it('accepts portal element', () => {
      const portal = document.createElement('div');
      renderSetFilter({ menuPortal: portal });
      expect(getSelect()).toBeInTheDocument();
    });
  });

  describe('with debouncing enabled', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    describe('when user types rapidly', () => {
      it('debounces API calls', async () => {
        mockApiResponse([]);
        renderSetFilter();

        typeInSearch('t');
        typeInSearch('te');
        typeInSearch('test');

        vi.advanceTimersByTime(200);
        expect(request.get).toHaveBeenCalled();
      });

      it('handles rapid inputs gracefully', () => {
        mockApiResponse([]);
        renderSetFilter();

        typeInSearch('test1');
        vi.advanceTimersByTime(100);
        typeInSearch('test2');
        vi.advanceTimersByTime(200);

        expect(getSelect()).toHaveValue('test2');
      });
    });
  });
});
