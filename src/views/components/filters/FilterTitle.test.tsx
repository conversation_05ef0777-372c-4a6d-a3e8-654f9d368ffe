import { faker } from '@faker-js/faker';
import { render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it } from 'vitest';
import { FilterTitle } from './FilterTitle';

const DEFAULT_PROPS = {
  name: faker.string.sample(),
};

const childText = faker.string.sample();
const ChildComponent = () => <div>{childText}</div>;

const renderFilterTitle = (props: Partial<ComponentProps<typeof FilterTitle>> = {}) => {
  return render(<FilterTitle {...DEFAULT_PROPS} {...props} />);
};

describe('FilterTitle', () => {
  it('displays the provided name', () => {
    renderFilterTitle({ children: <ChildComponent /> });
    expect(screen.getByText(DEFAULT_PROPS.name)).toBeInTheDocument();
  });

  it('displays the provided children', () => {
    renderFilterTitle({ children: <ChildComponent /> });
    expect(screen.getByText(childText)).toBeInTheDocument();
  });
});
