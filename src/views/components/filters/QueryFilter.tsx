import * as React from 'react';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IProps {
  localQuery?: string;
  advancedQuery?: string;
  displayType: FilterDisplay;
  updateQuery: (query: string) => void;
  onClear?: () => void;
  disabled?: boolean;
}

export const QueryFilter = (props: IProps) => {
  function onChangeQuery(evt: React.SyntheticEvent<HTMLInputElement>) {
    const query = evt.currentTarget.value || '';
    props.updateQuery(query);
  }

  // Stops React throwing an "uncontrolled input" warning
  const query = props.localQuery || '';
  return (
    <>
      <FilterTitle name="Card Name">
        <ClearFilterButton
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          disabled={props.advancedQuery !== undefined && props.advancedQuery !== ''}
          onClick={() => {
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <input
        className="col-xs-12 filter-no-padding advanced-search-input"
        type="text"
        placeholder="Search card names"
        value={query}
        onChange={onChangeQuery}
        disabled={props.disabled === true || (props.advancedQuery !== undefined && props.advancedQuery !== '')}
      />
    </>
  );
};
