import done from '@iconify/icons-ic/done';
import { Icon, IconifyIcon } from '@iconify/react';
import * as React from 'react';

export enum ButtonClass {
  PRIMARY = 'button-primary',
  DISABLED = 'button-disabled',
  ALERT = 'button-alert',
  GREEN = 'button-dark-green',
  GREY = 'button-grey',
}
interface IProps {
  class: ButtonClass;
  title: string;
  onClick: (evt: React.SyntheticEvent<HTMLElement>) => void;
  icon?: string | IconifyIcon;
  children?: JSX.Element;
  disabled?: boolean;
}

interface IFiltersProps {
  activeFiltersCount: number;
  onClick: (evt: React.SyntheticEvent<HTMLElement>) => void;
}

export const Button = (props: IProps) => {
  const disabled = props.disabled === true;
  const buttonClass = disabled ? ButtonClass.DISABLED : props.class;
  return (
    <button className={buttonClass} onClick={onClick} disabled={disabled}>
      {props.icon && (
        <div className="button-icon-container">
          <Icon height={'15px'} width={'15px'} icon={props.icon} />
        </div>
      )}
      {props.title}
      {props.children && props.children}
    </button>
  );

  function onClick(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    evt.currentTarget.blur();
    props.onClick(evt);
  }
};

export const UpdateFiltersButton = (props: IFiltersProps) => {
  let filterCount: JSX.Element | undefined = undefined;
  if (props.activeFiltersCount !== 0) {
    filterCount = (
      <div className="filter-amount-container">
        <span className="collection-grid-item-name-inner">{props.activeFiltersCount}</span>
      </div>
    );
  }
  return (
    <Button class={ButtonClass.PRIMARY} icon={done} title="Apply" onClick={props.onClick}>
      {filterCount}
    </Button>
  );
};
