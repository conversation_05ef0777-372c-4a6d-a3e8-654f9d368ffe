import { fireEvent, render, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { Rarity } from '../../../models/Rarity';
import { RarityFilter } from './RarityFilter';

const mockOnUpdateFilter = vi.fn();
const mockOnClear = vi.fn();

const allRarityData = Object.keys(Rarity)
  .map((key) => Rarity[key as keyof typeof Rarity])
  // remove basic land
  .filter((rarity) => rarity !== Rarity.BASIC_LAND);

const DEFAULT_PROPS: ComponentProps<typeof RarityFilter> = {
  onUpdateFilter: mockOnUpdateFilter,
  displayType: FilterDisplay.REMOVEABLE,
  onClear: mockOnClear,
};

const findImageByRarity = (rarity: Rarity, container: HTMLElement) => {
  const rarityName = rarity.replace(/\s/g, '-').toLowerCase();
  return container.querySelector(`img[src*="rarity-${rarityName}"]`);
};

const clickRarityButton = (rarity: Rarity, container: HTMLElement) => {
  // because right now there no indicator for the rarity, we need to use the image src to find the button
  const img = findImageByRarity(rarity, container);
  const button = img?.closest('button');
  if (img) {
    fireEvent.click(img);
  }
};

const renderRarityFilter = (props: Partial<ComponentProps<typeof RarityFilter>> = {}) => {
  return render(<RarityFilter {...DEFAULT_PROPS} {...props} />);
};

describe('RarityFilter', () => {
  describe('when rendered', () => {
    describe('with FilterTitle', () => {
      it('renders title', () => {
        renderRarityFilter();
        expect(screen.getByText('Rarity')).toBeInTheDocument();
      });
    });

    it('renders rarity icons', () => {
      const { container } = renderRarityFilter();

      allRarityData.forEach((rarity) => {
        const img = findImageByRarity(rarity, container);
        expect(img).toBeInTheDocument();
      });
    });

    it('renders specific rarity images', () => {
      renderRarityFilter();

      // Check for common rarity image
      const commonImg = document.querySelector('img[src*="rarity-common"]');
      expect(commonImg).toBeInTheDocument();

      // Check for rare rarity image
      const rareImg = document.querySelector('img[src*="rarity-rare"]');
      expect(rareImg).toBeInTheDocument();
    });
  });

  describe('with GateFilterIcon', () => {
    test('adds rarity when not in set', () => {
      const { container } = renderRarityFilter({ rarityFilters: Immutable.OrderedSet() });
      clickRarityButton(Rarity.RARE, container);

      // Verify it contains the RARE rarity
      expect(mockOnUpdateFilter).toHaveBeenCalledWith(Immutable.OrderedSet([Rarity.RARE]));
    });

    test('adds rarity when rarityFilters prop is undefined', () => {
      const { container } = renderRarityFilter({ rarityFilters: undefined });
      clickRarityButton(Rarity.COMMON, container);

      // When rarityFilters is undefined, should create empty OrderedSet and add the rarity
      expect(mockOnUpdateFilter).toHaveBeenCalledWith(Immutable.OrderedSet([Rarity.COMMON]));
    });

    test('removes rarity when already in set', () => {
      // Start with all rarities selected
      const allRarities = Immutable.OrderedSet(allRarityData);
      const { container } = renderRarityFilter({ rarityFilters: allRarities });

      clickRarityButton(Rarity.UNCOMMON, container);

      const expectedRarities = allRarityData.filter((rarity) => rarity !== Rarity.UNCOMMON);
      expect(mockOnUpdateFilter).toHaveBeenCalledWith(Immutable.OrderedSet(expectedRarities));
    });

    describe('disabled rarity icons', () => {
      it('uses disabled prop', () => {
        renderRarityFilter({ disabled: true });
        const buttons = screen.getAllByRole('button');
        const rarityButtons = buttons.filter((button) => !button.textContent?.includes('Remove'));
        rarityButtons.forEach((button) => {
          expect(button).toBeDisabled();
        });
      });

      it('not disabled when disabled prop is false', () => {
        renderRarityFilter({ disabled: false });
        const buttons = screen.getAllByRole('button');
        const rarityButtons = buttons.filter((button) => !button.textContent?.includes('Remove'));
        rarityButtons.forEach((button) => {
          expect(button).not.toBeDisabled();
        });
      });
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders Remove button', () => {
      renderRarityFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders Clear button', () => {
      renderRarityFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    test('onClear prop is triggered', () => {
      renderRarityFilter();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);
      expect(mockOnUpdateFilter).toHaveBeenCalledWith(Immutable.OrderedSet());
    });

    it('disabled ClearFilterButton', () => {
      renderRarityFilter({ disabled: true });
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).not.toHaveBeenCalled();
      expect(mockOnUpdateFilter).not.toHaveBeenCalled();
    });
  });
});
