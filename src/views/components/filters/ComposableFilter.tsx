import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { FilterComponentType } from '../../../models/FilterComponent';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { AnyFilter } from '../../../models/filters/Filters';
import { LorcanaFilter } from '../../../models/filters/lorcana/LorcanaFilters';
import { MTGFilter } from '../../../models/filters/mtg/MTGFilters';
import { PokeFilter } from '../../../models/filters/pokemon/PokeFilters';
import { YugiFilter } from '../../../models/filters/yugioh/YugiFilters';
import { Game } from '../../../models/Game';
import { LorcanaFilterComponent } from '../lorcana/LorcanaFilterComponent';
import { MTGFilterComponent } from '../mtg/MTGFilterComponent';
import { PokeFilterComponent } from '../pokemon/PokeFilterComponent';
import { YugiFilterComponent } from '../yugioh/YugiFilterComponent';

interface IProps {
  dispatcher: Dispatcher;
  filter: AnyFilter;
  game: Game;
  type: FilterComponentType;
  displayType: FilterDisplay;
  filterOverride?: AnyFilter;
  disabled?: boolean;
  menuPortal?: HTMLDivElement;
  onUpdate: (newFilter: AnyFilter, game: Game) => void;
}

export const ComposableFilter = (props: IProps) => {
  let component: JSX.Element | null;
  switch (props.game) {
    case Game.MTG:
      const mtgFilter = props.filter as MTGFilter;
      const mtgFilterOverride = props.filterOverride as MTGFilter | undefined;
      component = (
        <MTGFilterComponent
          dispatcher={props.dispatcher}
          filter={mtgFilter}
          filterOverride={mtgFilterOverride}
          type={props.type}
          displayType={props.displayType}
          onUpdate={(filter: MTGFilter) => props.onUpdate(filter, Game.MTG)}
          disabled={props.disabled}
          menuPortal={props.menuPortal}
        />
      );
      break;
    case Game.POKEMON:
      const pokeFilter = props.filter as PokeFilter;
      const pokeFilterOverride = props.filterOverride as PokeFilter | undefined;
      component = (
        <PokeFilterComponent
          dispatcher={props.dispatcher}
          filter={pokeFilter}
          filterOverride={pokeFilterOverride}
          type={props.type}
          displayType={props.displayType}
          onUpdate={(filter: PokeFilter) => props.onUpdate(filter, Game.POKEMON)}
          disabled={props.disabled}
        />
      );
      break;
    case Game.YUGIOH:
      const yugiFilter = props.filter as YugiFilter;
      const yugiFilterOverride = props.filterOverride as YugiFilter | undefined;
      component = (
        <YugiFilterComponent
          dispatcher={props.dispatcher}
          filter={yugiFilter}
          filterOverride={yugiFilterOverride}
          type={props.type}
          displayType={props.displayType}
          onUpdate={(filter: YugiFilter) => props.onUpdate(filter, Game.YUGIOH)}
          disabled={props.disabled}
        />
      );
      break;
    case Game.LORCANA:
      const lorcanaFilter = props.filter as LorcanaFilter;
      const lorcanaFilterOverride = props.filterOverride as LorcanaFilter | undefined;
      component = (
        <LorcanaFilterComponent
          dispatcher={props.dispatcher}
          filter={lorcanaFilter}
          filterOverride={lorcanaFilterOverride}
          type={props.type}
          displayType={props.displayType}
          onUpdate={(filter: PokeFilter) => props.onUpdate(filter, Game.LORCANA)}
          disabled={props.disabled}
        />
      );
      break;
    default:
      component = null;
  }

  if (component) {
    return <div className="filter-composable-container">{component}</div>;
  } else {
    return null;
  }
};
