import * as Immutable from 'immutable';
import * as React from 'react';
import Select, { components } from 'react-select';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { StackLocation } from '../../../models/StackLocation';
import { LocationInputOption } from '../../builder/LocationInputOption';
import { ClearFilterButton } from '../../components/filters/ClearFilterButton';
import { LocationReactSelectInput, styleOverride } from '../../shared/ReactSelectHelper';

interface IProps {
  dispatcher: Dispatcher;
  index: number;
  onLoad: (index: number, location?: StackLocation) => Promise<Immutable.List<StackLocation>>;
  remove?: () => void;
  parentLocation?: StackLocation;
  value?: StackLocation;
  update: (location: StackLocation) => void;
  clearable: boolean;
  last: boolean;
}

interface IState {
  options: Immutable.List<StackLocation>;
}

const SingleValueCard = (props: any) => {
  return <components.SingleValue {...props}>{props.data.label}</components.SingleValue>;
};

export const FilterLocation = class extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      options: Immutable.List<StackLocation>(),
    };
  }

  async componentDidMount() {
    this.setState({ options: await this.props.onLoad(this.props.index, this.props.parentLocation) });
  }

  NoOptionsMessage = (props: any) => {
    return (
      <components.NoOptionsMessage {...props}>
        <div className="react-select__suggestion">{'No existing locations'}</div>
      </components.NoOptionsMessage>
    );
  };

  render() {
    return (
      <>
        {this.props.index !== 0 ? (
          <div className="location-node-seperator">
            <span>/</span>
          </div>
        ) : null}
        <div style={{ width: '190px', flexShrink: 0 }}>
          {this.props.remove === undefined ? null : (
            <div className="flex justify-end">
              <ClearFilterButton onClick={this.props.remove} label={'Clear'} />
            </div>
          )}
          <div
            style={{
              cursor: 'text',
              marginTop: this.props.clearable ? '' : '1.5rem',
            }}
          >
            <Select
              components={{
                SingleValue: SingleValueCard,
                NoOptionsMessage: this.NoOptionsMessage,
                Input: LocationReactSelectInput,
              }}
              formatOptionLabel={LocationInputOption.formatOptionLabel.bind(this)}
              isDisabled={!this.props.last}
              placeholder={''}
              styles={styleOverride}
              className={'react-select-thin'}
              classNamePrefix="react-select-thin"
              options={this.state.options.map((location: StackLocation) => new LocationInputOption(location)).toArray()}
              // value expects null otherwise the option won't be cleared properly
              value={this.props.value === undefined ? null : new LocationInputOption(this.props.value)}
              onChange={(value: LocationInputOption) => this.props.update.bind(this)(value['value'])}
              defaultOptions
            />
          </div>
        </div>
      </>
    );
  }
};
