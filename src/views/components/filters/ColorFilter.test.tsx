import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { ColorFilter as ColorFilterModel, ColorOption } from '../../../models/filters/ColorFilter';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { FilterState } from '../../../models/filters/FilterState';
import { ColorFilter } from './ColorFilter';

const mockOnUpdateFilter = vi.fn();
const mockOnClear = vi.fn();

type ColorFilterProps = ComponentProps<typeof ColorFilter>;

const findImageByColor = (color: ColorOption, container: HTMLElement) => {
  const image = container.querySelector(`img[src*="mana-${color}"]`);
  if (!image) {
    throw new Error(`Could not find image for color: ${color}`);
  }
  return image;
};

const clickColorButton = (color: ColorOption, container: HTMLElement) => {
  const button = findImageByColor(color, container);
  fireEvent.click(button);
};

const getExcludeUnselectedButton = () => screen.getByText('Exclude unselected');

const createColorFilters = (filters: { color: ColorOption; state: FilterState }[] = []) => {
  let colorFilters = new ColorFilterModel();
  filters.forEach(({ color, state }) => {
    colorFilters = colorFilters.set(color, state);
  });
  return colorFilters;
};

const DEFAULT_PROPS: ColorFilterProps = {
  displayType: FilterDisplay.REMOVEABLE,
  onUpdateFilter: mockOnUpdateFilter,
  onClear: mockOnClear,
};

const renderColorFilter = (props: Partial<ColorFilterProps> = {}) => {
  return render(<ColorFilter {...DEFAULT_PROPS} {...props} />);
};

describe('ColorFilter', () => {
  describe('when rendered', () => {
    describe('with FilterTitle', () => {
      it('renders title', () => {
        renderColorFilter();
        expect(screen.getByText('Color')).toBeInTheDocument();
      });
    });
    test('renders color filter icons', () => {
      const { container } = renderColorFilter();
      Object.keys(ColorOption).forEach((key) => {
        const color = ColorOption[key as keyof typeof ColorOption];
        expect(findImageByColor(color, container)).toBeInTheDocument();
      });
    });
  });

  describe('when color icons are clicked', () => {
    test('trigger onUpdateFilter props', () => {
      const { container } = renderColorFilter();
      clickColorButton(ColorOption.RED, container);
      expect(mockOnUpdateFilter).toHaveBeenCalledWith(expect.objectContaining({ red: FilterState.ON }));
    });

    test('cycles filter states on repeated clicks', () => {
      const colorFilters = createColorFilters([{ color: ColorOption.BLUE, state: FilterState.ON }]);
      const { container } = renderColorFilter({ colorFilters });

      clickColorButton(ColorOption.BLUE, container);
      expect(mockOnUpdateFilter).toHaveBeenCalledWith(expect.objectContaining({ blue: FilterState.OFF }));
    });

    test('preserves existing filter states', () => {
      const colorFilters = createColorFilters([
        { color: ColorOption.RED, state: FilterState.ON },
        { color: ColorOption.BLUE, state: FilterState.OFF },
      ]);
      const { container } = renderColorFilter({ colorFilters });

      clickColorButton(ColorOption.GREEN, container);
      expect(mockOnUpdateFilter).toHaveBeenCalledWith(
        expect.objectContaining({
          red: FilterState.ON,
          blue: FilterState.OFF,
          green: FilterState.ON,
        }),
      );
    });
  });

  describe('with ExcludeUnselectedButton', () => {
    it('renders ExcludeUnselectedButton', () => {
      renderColorFilter();
      expect(screen.getByText('Exclude unselected')).toBeInTheDocument();
    });
    it('sets inactive colors to OFF', () => {
      const colorFilters = createColorFilters([
        { color: ColorOption.WHITE, state: FilterState.ON },
        { color: ColorOption.BLACK, state: FilterState.INACTIVE },
      ]);
      renderColorFilter({ colorFilters });

      fireEvent.click(getExcludeUnselectedButton());
      expect(mockOnUpdateFilter).toHaveBeenCalledWith(
        expect.objectContaining({
          white: FilterState.ON,
          black: FilterState.OFF,
          blue: FilterState.OFF,
          red: FilterState.OFF,
          green: FilterState.OFF,
          colorless: FilterState.OFF,
        }),
      );
    });

    test('does not affect multicolored', () => {
      renderColorFilter();
      fireEvent.click(getExcludeUnselectedButton());
      expect(mockOnUpdateFilter).toHaveBeenCalledWith(expect.objectContaining({ multicolored: FilterState.INACTIVE }));
    });

    test('disables ExcludeUnselectedButton', () => {
      renderColorFilter({ disabled: true });
      fireEvent.click(getExcludeUnselectedButton());
      expect(mockOnUpdateFilter).not.toHaveBeenCalled();
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders Remove button', () => {
      renderColorFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders Clear button', () => {
      renderColorFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    test('triggers onClear prop', () => {
      renderColorFilter();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });

    it('disables ClearFilterButton', () => {
      renderColorFilter({ disabled: true });
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).not.toHaveBeenCalled();
    });
  });

  describe('with disabled prop', () => {
    test('disables color icons', () => {
      const { container } = renderColorFilter({ disabled: true });
      clickColorButton(ColorOption.WHITE, container);
      expect(mockOnUpdateFilter).not.toHaveBeenCalled();
    });
  });
});
