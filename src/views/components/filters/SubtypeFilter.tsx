import * as Immutable from 'immutable';
import * as React from 'react';
import Select, { components, OptionsType, OptionTypeBase } from 'react-select';
import { TextFormat } from '../../../helpers/fmt';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { SubtypeFilter } from '../../../models/filters/SubtypeFilter';
import { validSubtypes } from '../../../models/Subtypes';
import { SimpleNoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IProps {
  subtypes: Immutable.OrderedMap<string, SubtypeFilter>;
  displayType: FilterDisplay;
  update: (subtypes: Immutable.OrderedMap<string, SubtypeFilter>) => void;
  onClear?: () => void;
  menuPortal?: HTMLDivElement;
  disabled?: boolean;
}

class SubtypeItem implements OptionTypeBase {
  label: string;
  value: SubtypeFilter;
  constructor(filter: SubtypeFilter) {
    this['label'] = TextFormat.humanizeKey(filter.get('subtype'));
    this['value'] = filter;
  }

  formatOptionLabel = () => {
    return <div className="react-select__suggestion">{this['label']}</div>;
  };
}

export const SubtypeFilterComponent = (props: IProps) => {
  const MultiValueCard = (props: any) => {
    const subtypeFilter = props.data.value as SubtypeFilter;
    return (
      <components.MultiValue {...props}>
        <div
          style={{
            textDecorationLine: subtypeFilter.get('include') ? '' : 'line-through',
            cursor: 'pointer',
          }}
          onClick={() => {
            updateSubtype(subtypeFilter);
          }}
        >
          {props.data.label}
        </div>
      </components.MultiValue>
    );
  };

  function updateSubtype(subtypeFilter: SubtypeFilter) {
    document.activeElement && (document.activeElement as HTMLElement).blur();
    const target = props.subtypes.get(subtypeFilter.get('subtype'));
    props.update(
      props.subtypes.set(subtypeFilter.get('subtype'), target.set('include', !target.get('include'))).toOrderedMap(),
    );
  }

  function onChange(subtypeItemArray: OptionsType<SubtypeItem> | null) {
    props.update(
      Immutable.OrderedMap<string, SubtypeFilter>(
        subtypeItemArray === null
          ? []
          : subtypeItemArray.map((item: SubtypeItem) => {
              return [(item['value'] as SubtypeFilter).get('subtype'), item['value'] as SubtypeFilter];
            }),
      ),
    );
  }

  function generateOptions(): SubtypeItem[] {
    return Immutable.List<string>(validSubtypes)
      .filter((value: string) => {
        return props.subtypes.get(value) === undefined;
      })
      .map((value: string) => {
        return new SubtypeItem(new SubtypeFilter({ include: true, subtype: value }));
      })
      .toArray();
  }

  const value = props.subtypes
    .map((filter: SubtypeFilter) => {
      return new SubtypeItem(filter);
    })
    .toArray();

  return (
    <>
      <FilterTitle name="Subtype">
        <ClearFilterButton
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          onClick={() => {
            props.update(Immutable.OrderedMap<string, SubtypeFilter>());
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <div style={{ cursor: 'text', minWidth: '20rem' }}>
        <Select
          components={{
            MultiValue: MultiValueCard,
            NoOptionsMessage: SimpleNoOptionsMessage,
          }}
          formatOptionLabel={(SubtypeItem: SubtypeItem) => SubtypeItem.formatOptionLabel()}
          styles={styleOverride}
          className={'react-select-type'}
          classNamePrefix="react-select-type"
          options={generateOptions()}
          value={value}
          onChange={onChange}
          isMulti={true}
          menuPortalTarget={props.menuPortal}
          defaultOptions
          isDisabled={props.disabled === true}
        />
      </div>
    </>
  );
};
