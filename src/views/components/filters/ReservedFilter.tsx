import * as React from 'react';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { FilterState } from '../../../models/filters/FilterState';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IProps {
  filterState: FilterState;
  changeReservedStatus: (filterState: FilterState) => void;
  displayType: FilterDisplay;
  onClear?: () => void;
  disabled?: boolean;
}

// TODO: When implementing composable filters for the CardBot, refactor so that it is in-line with the foil filter.
export const ReservedFilter = (props: IProps) => {
  React.useEffect(() => {
    if (props.filterState === FilterState.INACTIVE && props.displayType === FilterDisplay.REMOVEABLE) {
      props.changeReservedStatus(FilterState.ON);
    }
  }, []);

  function onChangeFilter(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    props.changeReservedStatus(evt.currentTarget.value as FilterState);
  }

  return (
    <>
      <FilterTitle name="Reserved">
        <ClearFilterButton
          disabled={props.disabled}
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          onClick={() => {
            props.changeReservedStatus(FilterState.INACTIVE);
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <select
        className="select"
        disabled={props.disabled}
        value={props.filterState}
        onChange={onChangeFilter.bind(this)}
      >
        {props.displayType === FilterDisplay.PERMANENT ? <option value={FilterState.INACTIVE}>No Filter</option> : null}
        <option value={FilterState.ON}>Reserved</option>
        <option value={FilterState.OFF}>Not Reserved</option>
      </select>
    </>
  );
};
