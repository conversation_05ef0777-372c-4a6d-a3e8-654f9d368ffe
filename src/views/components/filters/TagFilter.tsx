import * as Immutable from 'immutable';
import * as React from 'react';
import Select, { components } from 'react-select';
import * as TagsAPI from '../../../api/Tags';
import * as HasCards from '../../../containers/HasCards';
import * as HasTags from '../../../containers/HasTags';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { TagFilter } from '../../../models/filters/TagFilter';
import { TagFilterData } from '../../../models/filters/TagFilterData';
import { Tag } from '../../../models/Tags';
import { TagItem } from '../../components/react-select/TagItem';
import { SimpleNoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IProps {
  dispatcher: Dispatcher;
  tags: TagFilterData;
  displayType: FilterDisplay;
  update: (tags: TagFilterData) => void;
  onClear?: () => void;
  menuPortal?: HTMLDivElement;
  disabled?: boolean;
}

interface IState {
  options: Immutable.OrderedSet<Tag>;
}

export const TagFilterComponent = HasTags.Attach<IProps>(
  HasCards.Attach<IProps & HasTags.IProps>(
    class extends React.Component<IProps & HasTags.IProps & HasCards.IProps, IState> {
      /**
       * @override
       */
      constructor(props: IProps & HasTags.IProps & HasCards.IProps) {
        super(props);
        this.state = { options: Immutable.OrderedSet<Tag>() };
      }

      /**
       * @override
       */
      async componentDidMount() {
        const load = async () => {
          const ownerUsername = this.props.cardPage.get('ownerUsername');

          if (ownerUsername.length > 0) {
            const options = await TagsAPI.all(this.props.cardPage.get('ownerUsername'));
            this.setState({ options: options });
          }
        };
        load();
      }

      /**
       * @override
       */
      public render() {
        const value = this.props.tags
          .get('tagFilter')
          .map((filter: TagFilter) => new TagItem(filter))
          .toArray();

        return (
          <>
            <FilterTitle name="Tags">
              <ClearFilterButton
                label={this.props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
                disabled={this.props.disabled}
                onClick={() => {
                  this.props.update(new TagFilterData());
                  if (this.props.onClear !== undefined) {
                    this.props.onClear();
                  }
                }}
              />
            </FilterTitle>
            <div className="flex" style={{ gap: '1rem' }}>
              <select
                style={{ width: 'auto' }}
                className="select"
                value={this.props.tags.get('excludeUnselected').toString()}
                disabled={this.props.disabled}
                onChange={this.onChangeUnselected.bind(this)}
              >
                <option value={'false'}>has any of</option>
                <option value={'true'}>has exactly</option>
              </select>
              <div style={{ cursor: 'text', flexGrow: 1, minWidth: '20rem' }}>
                <Select
                  components={{
                    MultiValue: this.MultiValueCard,
                    NoOptionsMessage: SimpleNoOptionsMessage,
                  }}
                  formatOptionLabel={(tagItem: TagItem) => tagItem.formatOptionLabel()}
                  styles={styleOverride}
                  className={'react-select-type'}
                  classNamePrefix="react-select-type"
                  options={this.generateOptions.bind(this)()}
                  value={value}
                  onChange={this.onChange.bind(this)}
                  isMulti={true}
                  isDisabled={this.props.disabled}
                  menuPortalTarget={this.props.menuPortal}
                  defaultOptions
                />
              </div>
            </div>
          </>
        );
      }

      MultiValueCard = (props: any) => {
        const tagFilter = props.data.value as TagFilter;
        return (
          <components.MultiValue {...props}>
            <div
              style={{
                textDecorationLine: tagFilter.get('include') ? '' : 'line-through',
                cursor: 'pointer',
              }}
              onClick={() => {
                this.updateTag(tagFilter);
              }}
            >
              {props.data.label}
            </div>
          </components.MultiValue>
        );
      };

      // Uses this.props.tags as this should never fire if the advanced filter is active.
      private updateTag(tagFilter: TagFilter) {
        document.activeElement && (document.activeElement as HTMLElement).blur();
        const target = this.props.tags.get('tagFilter').get(tagFilter.get('tag').get('name'));
        const newMap = this.props.tags
          .get('tagFilter')
          .set(tagFilter.get('tag').get('name'), target.set('include', !target.get('include')))
          .toOrderedMap();
        this.props.update(this.props.tags.set('tagFilter', newMap));
      }

      private onChange(tagItemArray: TagItem[] | null) {
        const tagFilter = Immutable.OrderedMap<string, TagFilter>(
          tagItemArray === null
            ? []
            : tagItemArray.map((item: TagItem) => {
                return [(item['value'] as TagFilter).get('tag').get('name'), item['value'] as TagFilter];
              }),
        );
        this.props.update(this.props.tags.set('tagFilter', tagFilter));
      }

      private onChangeUnselected(evt: React.SyntheticEvent<HTMLSelectElement>) {
        let excludeUnselected: boolean;
        switch (evt.currentTarget.value) {
          case 'true':
            excludeUnselected = true;
            break;
          case 'false':
            excludeUnselected = false;
            break;
          default:
            console.error(`Invalid selection: ${evt.currentTarget.value}`);
            excludeUnselected = false;
        }

        this.props.update(this.props.tags.set('excludeUnselected', excludeUnselected));
      }

      private generateOptions(): TagItem[] {
        return this.state.options
          .filter((tag: Tag) => !this.props.tags.get('tagFilter').has(tag.get('name')))
          .map((tag: Tag) => new TagItem(new TagFilter({ include: true, tag: tag })))
          .toArray();
      }
    },
  ),
);
