import { faker } from '@faker-js/faker';
import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { PriceFilter } from '../../../models/filters/PriceFilter';
import { PriceMinMaxFilters } from './PriceMinMaxFilters';

const mockProcessValue = vi.fn();
const mockOnClear = vi.fn();

const mockMinPrice = faker.number.float({ min: 1, max: 50, fractionDigits: 2 }).toString();
const mockMaxPrice = faker.number.float({ min: 51, max: 100, fractionDigits: 2 }).toString();

const createPriceFilter = ({ min = '', max = '' } = {}) => {
  return new PriceFilter({ min, max });
};

const renderPriceMinMaxFilters = (props: Partial<ComponentProps<typeof PriceMinMaxFilters>> = {}) => {
  return render(
    <PriceMinMaxFilters
      filter={createPriceFilter()}
      processValue={mockProcessValue}
      onClear={mockOnClear}
      displayType={FilterDisplay.REMOVEABLE}
      {...props}
    />,
  );
};

// Helper functions
const getMinInput = () => screen.getByPlaceholderText('Min Price');
const getMaxInput = () => screen.getByPlaceholderText('Max Price');

const changeInput = ({ input, value }: { input: HTMLElement; value: string }) => {
  // Use focus and blur events to simulate CurrencyInput behavior
  fireEvent.focus(input);
  fireEvent.change(input, { target: { value } });
  fireEvent.blur(input);
};

const expectInputValue = ({ input, value }: { input: HTMLElement; value: string }) => {
  // CurrencyInput displays values with $ prefix
  const expectedValue = value ? `$${value}` : '';
  expect(input).toHaveValue(expectedValue);
};

const expectProcessValueCall = ({ type, value }: { type: 'min' | 'max'; value: string }) => {
  expect(mockProcessValue).toHaveBeenCalledWith(type, value);
};

describe('PriceMinMaxFilters', () => {
  describe('when rendered', () => {
    it('renders min input', () => {
      renderPriceMinMaxFilters();
      expect(getMinInput()).toBeInTheDocument();
    });

    it('renders max input', () => {
      renderPriceMinMaxFilters();
      expect(getMaxInput()).toBeInTheDocument();
    });

    it('reflects filter values in inputs', () => {
      const filter = createPriceFilter({ min: mockMinPrice, max: mockMaxPrice });

      renderPriceMinMaxFilters({ filter });

      expectInputValue({ input: getMinInput(), value: mockMinPrice });
      expectInputValue({ input: getMaxInput(), value: mockMaxPrice });
    });
  });

  describe('with FilterTitle', () => {
    it('renders title', () => {
      renderPriceMinMaxFilters();
      expect(screen.getByText('Price Range')).toBeInTheDocument();
    });
  });

  describe('with input elements', () => {
    test('triggers processValue on min change', () => {
      renderPriceMinMaxFilters();

      changeInput({ input: getMinInput(), value: mockMinPrice });

      expectProcessValueCall({ type: 'min', value: mockMinPrice });
    });

    test('triggers processValue on max change', () => {
      renderPriceMinMaxFilters();

      changeInput({ input: getMaxInput(), value: mockMaxPrice });

      expectProcessValueCall({ type: 'max', value: mockMaxPrice });
    });

    describe('with disabled prop', () => {
      it('disables both inputs', () => {
        renderPriceMinMaxFilters({ disabled: true });
        expect(getMinInput()).toBeDisabled();
        expect(getMaxInput()).toBeDisabled();
      });
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders remove button', () => {
      renderPriceMinMaxFilters({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders clear button', () => {
      renderPriceMinMaxFilters({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    test('triggers onClear prop', () => {
      renderPriceMinMaxFilters();

      fireEvent.click(screen.getByText('Remove'));

      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });

    test('disabled ClearFilterButton', () => {
      renderPriceMinMaxFilters({ disabled: true });

      fireEvent.click(screen.getByText('Remove'));

      expect(mockOnClear).not.toHaveBeenCalled();
    });
  });

  describe('with PriceFilter', () => {
    test('handles valid price range', () => {
      const filter = createPriceFilter({ min: mockMinPrice, max: mockMaxPrice });
      renderPriceMinMaxFilters({ filter });
      expect(getMinInput()).not.toHaveClass('min-max--error');
      expect(getMaxInput()).not.toHaveClass('min-max--error');
    });

    test('handles invalid price range', () => {
      // max < min
      const filter = createPriceFilter({ min: mockMaxPrice, max: mockMinPrice });
      renderPriceMinMaxFilters({ filter });
      expect(getMinInput()).toHaveClass('min-max--error');
      expect(getMaxInput()).toHaveClass('min-max--error');
    });

    test('handles partial values', () => {
      const filter = createPriceFilter({ min: mockMinPrice, max: '' });
      renderPriceMinMaxFilters({ filter });

      expectInputValue({ input: getMinInput(), value: mockMinPrice });
      expectInputValue({ input: getMaxInput(), value: '' });
    });
  });
});
