import { fireEvent, render, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import React, { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { EnumHelper } from '../../../helpers/enum';
import { FilterComponentType } from '../../../models/FilterComponent';
import { MTGFilter } from '../../../models/filters/mtg/MTGFilters';
import { AddFilterComponent } from './AddFilterComponent';

const mockOnUpdate = vi.fn();
const mockAddFilter = vi.fn();

type AddFilterComponentProps = ComponentProps<typeof AddFilterComponent>;

const createMTGFilter = (activeFilters: FilterComponentType[] = []) => {
  return new MTGFilter({
    filterOrder: Immutable.OrderedSet(activeFilters),
  });
};

const createRestrictedOptions = (restrictedTypes: FilterComponentType[] = []) => {
  return Immutable.List(restrictedTypes);
};

const DEFAULT_PROPS: AddFilterComponentProps = {
  localFilter: createMTGFilter(),
  onUpdate: mockOnUpdate,
  addFilter: mockAddFilter,
};

const renderAddFilterComponent = (props: Partial<AddFilterComponentProps> = {}) => {
  return render(<AddFilterComponent {...DEFAULT_PROPS} {...props} />);
};

// Helper functions
const openDropdown = () => {
  fireEvent.mouseDown(screen.getByRole('textbox'));
};
// select an option from the dropdown
const selectOption = (optionText: string) => {
  openDropdown();
  fireEvent.click(screen.getByText(optionText));
};

// expect options to be visible in the dropdown
const expectOptionsVisible = (options: string[]) => {
  options.forEach((option) => {
    expect(screen.getByText(option)).toBeInTheDocument();
  });
};

// expect options to be hidden in the dropdown
const expectOptionsHidden = (options: string[]) => {
  options.forEach((option) => {
    expect(screen.queryByText(option)).not.toBeInTheDocument();
  });
};

// expect options to be partially visible in the dropdown
const expectOptionsPartiallyVisible = ({ visible, hidden }: { visible: string[]; hidden: string[] }) => {
  expectOptionsHidden(hidden);
  expectOptionsVisible(visible);
};

describe('AddFilterComponent', () => {
  describe('when rendered', () => {
    describe('with FilterTitle', () => {
      it('renders title', () => {
        renderAddFilterComponent();
        expect(screen.getByText('Add Filter')).toBeInTheDocument();
      });
    });

    describe('with react-select component', () => {
      it('renders select', () => {
        renderAddFilterComponent();
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });

      it('shows placeholder', () => {
        renderAddFilterComponent();
        expect(screen.getByText('Select...')).toBeInTheDocument();
      });

      test('shows all options', () => {
        renderAddFilterComponent();
        openDropdown();
        expectOptionsVisible(['Card Name', 'Color', 'Type']);
      });

      test('excludes restricted options', () => {
        const restrictedOptions = createRestrictedOptions([FilterComponentType.QUERY, FilterComponentType.COLOR]);
        renderAddFilterComponent({ restrictedOptions });
        openDropdown();
        expectOptionsPartiallyVisible({ visible: ['Type'], hidden: ['Card Name', 'Color'] });
      });

      test('excludes active filters', () => {
        const localFilter = createMTGFilter([FilterComponentType.QUERY, FilterComponentType.COLOR]);
        renderAddFilterComponent({ localFilter });
        openDropdown();
        expectOptionsPartiallyVisible({ visible: ['Type'], hidden: ['Card Name', 'Color'] });
      });

      test('excludes both restricted and active filters', () => {
        const localFilter = createMTGFilter([FilterComponentType.QUERY]);
        const restrictedOptions = createRestrictedOptions([FilterComponentType.COLOR]);
        renderAddFilterComponent({ localFilter, restrictedOptions });
        openDropdown();
        expectOptionsPartiallyVisible({ visible: ['Type'], hidden: ['Card Name', 'Color'] });
      });

      test('triggers addFilter prop', () => {
        renderAddFilterComponent();
        selectOption('Card Name');
        expect(mockAddFilter).toHaveBeenCalledWith(FilterComponentType.QUERY);
      });

      test('resets after selection', () => {
        renderAddFilterComponent();
        selectOption('Color');
        expect(screen.getByText('Select...')).toBeInTheDocument();
      });

      test('handles empty state', () => {
        const allFilterTypes = EnumHelper.allCases(FilterComponentType).toArray() as FilterComponentType[];
        const localFilter = createMTGFilter(allFilterTypes);
        renderAddFilterComponent({ localFilter });
        openDropdown();
        expect(screen.getByText('No Options')).toBeInTheDocument();
      });

      describe('with menuPortal prop', () => {
        test('accepts menuPortal prop', () => {
          const menuPortal = document.createElement('div');
          expect(() => {
            renderAddFilterComponent({ menuPortal });
          }).not.toThrow();
        });
      });
    });
  });
});
