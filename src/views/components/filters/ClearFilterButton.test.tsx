import { faker } from '@faker-js/faker';
import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ClearFilterButton } from './ClearFilterButton';

const DEFAULT_PROPS = {
  onClick: vi.fn(),
  label: faker.string.sample(),
};

const renderClearFilterButton = (props: Partial<ComponentProps<typeof ClearFilterButton>> = {}) => {
  // The component itself is a div with className secondary-button acting as the button
  return render(<ClearFilterButton {...DEFAULT_PROPS} {...props} />);
};

describe('ClearFilterButton', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when rendered', () => {
    it('displays the label', () => {
      renderClearFilterButton();
      expect(screen.getByText(DEFAULT_PROPS.label)).toBeInTheDocument();
    });

    it('displays an icon', () => {
      renderClearFilterButton();
      const button = screen.getByText(DEFAULT_PROPS.label);

      // Check if an SVG element (commonly used for icons) is a child of the button
      const svgIcon = button.previousSibling;
      expect(svgIcon).toBeInstanceOf(SVGElement);
    });
  });

  describe('when enabled', () => {
    it('calls onClick when clicked', () => {
      renderClearFilterButton({ disabled: false });
      const button = screen.getByText(DEFAULT_PROPS.label);

      fireEvent.click(button);
      expect(DEFAULT_PROPS.onClick).toHaveBeenCalledTimes(1);
    });

    it('does not have disabled styles', () => {
      renderClearFilterButton({ disabled: false });
      const button = screen.getByText(DEFAULT_PROPS.label);

      expect(button).not.toHaveStyle('cursor: default');
    });
  });

  describe('when disabled', () => {
    it('does not call onClick when clicked', () => {
      renderClearFilterButton({ disabled: true });
      const button = screen.getByText(DEFAULT_PROPS.label);

      fireEvent.click(button);
      expect(DEFAULT_PROPS.onClick).not.toHaveBeenCalled();
    });

    it('has disabled styles', () => {
      renderClearFilterButton({ disabled: true });
      const button = screen.getByText(DEFAULT_PROPS.label).parentElement;

      expect(button).toHaveStyle('cursor: default');
    });
  });
});
