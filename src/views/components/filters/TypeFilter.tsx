import * as React from 'react';
import { TextFormat } from '../../../helpers/fmt';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { FilterState, nextFilterState } from '../../../models/filters/FilterState';
import { TypeFilters, TypeOption } from '../../../models/filters/TypeFilters';
import { ClearFilterButton } from './ClearFilterButton';
import { ExcludeUnselectedButton } from './ExcludeUnselectedButton';
import { FilterTitle } from './FilterTitle';
import { GateFilterIcon } from './GateFilterIcon';

interface IProps {
  typeFilters?: TypeFilters;
  onUpdateFilter: (typeFilters: TypeFilters) => void;
  disabled?: boolean;
  displayType: FilterDisplay;
  onClear?: () => void;
}

export const TypeFilter = (props: IProps) => {
  const disabled = props.disabled === undefined ? false : props.disabled;
  return (
    <>
      <FilterTitle name="Type">
        <ExcludeUnselectedButton disabled={disabled} onClick={() => onExcludeUnselected()} />
        <ClearFilterButton
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          disabled={disabled}
          onClick={() => {
            props.onUpdateFilter(new TypeFilters());
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <div className="filter-symbol-container">
        {TypeFilters.quickFilters().map((type) => (
          <GateFilterIcon
            disabled={disabled}
            key={type}
            hoverText={TextFormat.capitalizeWord(type as string)}
            iconPath={`/svg/type-${type}.svg`}
            filterState={props.typeFilters ? props.typeFilters.get(type) : FilterState.INACTIVE}
            onClick={() => changeType(type)}
          />
        ))}
      </div>
    </>
  );

  function changeType(type: TypeOption) {
    let cardTypes = props.typeFilters || new TypeFilters();
    const nextState = nextFilterState(cardTypes.get(type));
    cardTypes = cardTypes.set(type, nextState);
    props.onUpdateFilter(cardTypes);
  }

  function onExcludeUnselected() {
    let cardTypes = props.typeFilters || new TypeFilters();

    TypeFilters.quickFilters()
      .filter((type) => type !== TypeOption.MULTITYPE && cardTypes.get(type) === FilterState.INACTIVE)
      .forEach((type) => {
        cardTypes = cardTypes.set(type, FilterState.OFF);
      });

    props.onUpdateFilter(cardTypes);
  }
};
