import * as React from 'react';
import CurrencyInput from 'react-currency-input-field';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { PriceFilter } from '../../../models/filters/PriceFilter';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IPriceMinMaxFilters {
  filter: PriceFilter;
  processValue: (minmax: 'min' | 'max', value?: string) => void;
  disabled?: boolean;
  onClear: () => void;
  displayType: FilterDisplay;
}

export const PriceMinMaxFilters = (props: IPriceMinMaxFilters) => {
  const className = 'min-max' + (props.filter.isValid() ? '' : '--error');
  const step = 0.25;
  return (
    <>
      <FilterTitle name="Price Range">
        <ClearFilterButton
          disabled={props.disabled === true}
          onClick={props.onClear}
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
        />
      </FilterTitle>
      <div className="flex" style={{ gap: '1rem' }}>
        <CurrencyInput
          disabled={props.disabled}
          className={className}
          min={0} // allowNegativeValue={false} is buggy, using this instead.
          step={step}
          value={props.filter.get('min')}
          onValueChange={(value?: string) => {
            props.processValue('min', value);
          }}
          decimalsLimit={2}
          prefix={'$'}
          disableAbbreviations={true}
          placeholder="Min Price"
        />
        <CurrencyInput
          disabled={props.disabled}
          className={className}
          min={0} // allowNegativeValue={false} is buggy, using this instead.
          step={step}
          value={props.filter.get('max')}
          onValueChange={(value?: string) => {
            props.processValue('max', value);
          }}
          decimalsLimit={2}
          prefix={'$'}
          disableAbbreviations={true}
          placeholder="Max Price"
        />
      </div>
    </>
  );
};
