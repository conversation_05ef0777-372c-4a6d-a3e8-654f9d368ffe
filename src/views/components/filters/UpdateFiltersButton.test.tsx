import { faker } from '@faker-js/faker';
import plus from '@iconify/icons-ic/baseline-plus';
import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { Button, ButtonClass, UpdateFiltersButton } from './UpdateFiltersButton';

const mockOnClick = vi.fn();

type ButtonProps = ComponentProps<typeof Button>;
type UpdateFiltersButtonProps = ComponentProps<typeof UpdateFiltersButton>;

const DEFAULT_BUTTON_PROPS: ButtonProps = {
  class: ButtonClass.PRIMARY,
  title: 'Test Button',
  onClick: mockOnClick,
};

const DEFAULT_UPDATE_PROPS: UpdateFiltersButtonProps = {
  activeFiltersCount: 0,
  onClick: mockOnClick,
};

const renderButton = (props: Partial<ButtonProps> = {}) => {
  return render(<Button {...DEFAULT_BUTTON_PROPS} {...props} />);
};

const renderUpdateFiltersButton = (props: Partial<UpdateFiltersButtonProps> = {}) => {
  return render(<UpdateFiltersButton {...DEFAULT_UPDATE_PROPS} {...props} />);
};

// Helper functions
const clickButton = () => {
  fireEvent.click(screen.getByRole('button'));
};

describe('UpdateFiltersButton', () => {
  describe('when rendered', () => {
    it('renders apply button', () => {
      renderUpdateFiltersButton();
      expect(screen.getByText('Apply')).toBeInTheDocument();
    });

    it('shows no count when zero filters', () => {
      renderUpdateFiltersButton({ activeFiltersCount: 0 });
      const countElement = screen.queryByText(/^\d+$/);
      expect(countElement).not.toBeInTheDocument();
    });

    it('shows count when filters active', () => {
      const count = faker.number.int({ min: 1, max: 99 });
      renderUpdateFiltersButton({ activeFiltersCount: count });
      expect(screen.getByText(count.toString())).toBeInTheDocument();
    });

    test('triggers onClick prop', () => {
      renderUpdateFiltersButton();
      clickButton();
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    describe('with Button component', () => {
      it('renders primary button by default', () => {
        renderUpdateFiltersButton();
        expect(screen.getByRole('button')).toBeEnabled();
      });
    });
  });
  describe('Button', () => {
    describe('when rendered', () => {
      it('renders button element', () => {
        renderButton();
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      it('displays title text', () => {
        const title = faker.lorem.word();
        renderButton({ title });
        expect(screen.getByText(title)).toBeInTheDocument();
      });

      test('triggers onClick prop', () => {
        renderButton();
        clickButton();
        expect(mockOnClick).toHaveBeenCalledTimes(1);
      });

      describe('with disabled prop', () => {
        it('disables button', () => {
          renderButton({ disabled: true });
          expect(screen.getByRole('button')).toBeDisabled();
          clickButton();
          expect(mockOnClick).not.toHaveBeenCalled();
        });
      });

      describe('with icon prop', () => {
        test('accepts string icon', () => {
          const icon = faker.lorem.word();
          expect(() => {
            renderButton({ icon });
          }).not.toThrow();
        });

        test('accepts IconifyIcon object', () => {
          const { container } = renderButton({ icon: plus });
          const svgIcon = container.querySelector('svg');
          expect(svgIcon).toBeInTheDocument();
        });
      });

      describe('with children prop', () => {
        test('renders children content', () => {
          const childText = faker.lorem.word();
          const children = <span>{childText}</span>;
          renderButton({ children });
          expect(screen.getByText(childText)).toBeInTheDocument();
        });
      });

      describe('with class prop', () => {
        test('accepts ButtonClass enum values', () => {
          Object.keys(ButtonClass).forEach((key) => {
            const buttonClass = ButtonClass[key as keyof typeof ButtonClass];
            expect(() => {
              renderButton({ class: buttonClass });
            }).not.toThrow();
          });
        });
      });
    });
  });
});
