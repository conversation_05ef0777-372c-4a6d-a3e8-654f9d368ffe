import close from '@iconify/icons-ic/close';
import { Icon } from '@iconify/react';
import * as React from 'react';

interface IClearFilterButton {
  disabled?: boolean;
  onClick: () => void;
  label: string;
}

// Deprecated: Use SecondaryButton component
export const ClearFilterButton = (props: IClearFilterButton) => {
  return (
    <>
      <div
        className="secondary-button"
        style={props.disabled ? { cursor: 'default' } : {}}
        onClick={() => {
          if (!props.disabled) {
            props.onClick();
          }
        }}
      >
        <Icon height={'15px'} width={'15px'} icon={close} style={{ margin: '0rem 0.25rem -0.2rem 0rem' }} />
        <span>{props.label}</span>
      </div>
    </>
  );
};
