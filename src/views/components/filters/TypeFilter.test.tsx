import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { FilterState } from '../../../models/filters/FilterState';
import { TypeFilters, TypeOption } from '../../../models/filters/TypeFilters';
import { TypeFilter } from './TypeFilter';

const mockOnUpdateFilter = vi.fn();
const mockOnClear = vi.fn();

type TypeFilterProps = ComponentProps<typeof TypeFilter>;

const findImageByType = (type: TypeOption, container: HTMLElement) => {
  const typeName = type.replace(/\s/g, '-').toLowerCase();
  return container.querySelector(`img[src*="type-${typeName}"]`);
};

const clickTypeButton = (type: TypeOption, container: HTMLElement) => {
  // because right now there no indicator for the type, we need to use the image src to find the button
  const img = findImageByType(type, container);
  const button = img?.closest('button');
  if (img) {
    fireEvent.click(img);
  }
  return button;
};

const getExcludeUnselectedButton = () => {
  return screen.getByText('Exclude unselected');
};

const createTypeFilters = (filters: { type: TypeOption; state: FilterState }[] = []) => {
  let typeFilters = new TypeFilters();
  filters.forEach(({ type, state }) => {
    typeFilters = typeFilters.set(type, state);
  });
  return typeFilters;
};

const DEFAULT_PROPS: TypeFilterProps = {
  displayType: FilterDisplay.REMOVEABLE,
  onUpdateFilter: mockOnUpdateFilter,
  onClear: mockOnClear,
};

const renderTypeFilter = (props: Partial<TypeFilterProps> = {}) => {
  return render(<TypeFilter {...DEFAULT_PROPS} {...props} />);
};

describe('TypeFilter', () => {
  describe('when rendered', () => {
    describe('with FilterTitle', () => {
      it('renders title', () => {
        renderTypeFilter();
        expect(screen.getByText('Type')).toBeInTheDocument();
      });
    });

    it('renders type filter icons', () => {
      const { container } = renderTypeFilter();

      // check that all type filter icons are rendered
      for (const type of TypeFilters.quickFilters()) {
        const image = findImageByType(type, container);
        expect(image).toBeInTheDocument();
      }
    });
  });

  describe('with ExcludeUnselectedButton', () => {
    it('renders exclude unselected button', () => {
      renderTypeFilter();
      const button = screen.getByText('Exclude unselected');
      expect(button).toBeInTheDocument();
    });

    it('sets inactive types to OFF when clicked', () => {
      const typeFilters = createTypeFilters([
        { type: TypeOption.CREATURE, state: FilterState.ON },
        { type: TypeOption.ARTIFACT, state: FilterState.INACTIVE },
      ]);
      renderTypeFilter({ typeFilters });

      const button = getExcludeUnselectedButton();
      fireEvent.click(button);

      expect(mockOnUpdateFilter).toHaveBeenCalledWith(
        expect.objectContaining({
          creature: FilterState.ON,
          artifact: FilterState.OFF,
        }),
      );
    });

    it('does not affect multitype when excluding unselected', () => {
      renderTypeFilter();

      const button = getExcludeUnselectedButton();
      fireEvent.click(button);

      expect(mockOnUpdateFilter).toHaveBeenCalledWith(
        expect.objectContaining({
          multitype: FilterState.INACTIVE,
        }),
      );
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders a remove button', () => {
      renderTypeFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders a clear button', () => {
      renderTypeFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    it('clears all filters when clicked', () => {
      const typeFilters = createTypeFilters([
        { type: TypeOption.CREATURE, state: FilterState.ON },
        { type: TypeOption.ARTIFACT, state: FilterState.OFF },
      ]);
      renderTypeFilter({ typeFilters });

      fireEvent.click(screen.getByText('Remove'));

      expect(mockOnUpdateFilter).toHaveBeenCalledWith(new TypeFilters());
    });

    it('onClear prop is triggered', () => {
      renderTypeFilter();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });
  });

  describe('with GateFilterIcon components', () => {
    it('cycles through filter states when type icon clicked', () => {
      const { container } = renderTypeFilter();

      clickTypeButton(TypeOption.CREATURE, container);

      expect(mockOnUpdateFilter).toHaveBeenCalledWith(
        expect.objectContaining({
          creature: FilterState.ON,
        }),
      );
    });

    it('cycles from INACTIVE to ON to OFF to INACTIVE', () => {
      let typeFilters = new TypeFilters();
      const { container } = renderTypeFilter({ typeFilters });

      // First click: INACTIVE -> ON
      clickTypeButton(TypeOption.CREATURE, container);
      expect(mockOnUpdateFilter).toHaveBeenCalledWith(
        expect.objectContaining({
          creature: FilterState.ON,
        }),
      );

      // Simulate ON state and click again: ON -> OFF
      typeFilters = typeFilters.set(TypeOption.CREATURE, FilterState.ON);
      const { container: container2 } = renderTypeFilter({ typeFilters });
      clickTypeButton(TypeOption.CREATURE, container2);

      expect(mockOnUpdateFilter).toHaveBeenCalledWith(
        expect.objectContaining({
          creature: FilterState.OFF,
        }),
      );
    });

    it('displays current filter state for type icons', () => {
      const typeFilters = createTypeFilters([
        { type: TypeOption.CREATURE, state: FilterState.ON },
        { type: TypeOption.ARTIFACT, state: FilterState.OFF },
      ]);
      const { container } = renderTypeFilter({ typeFilters });

      // The icons should display their current state (implementation would show different visual states)
      const creatureImg = findImageByType(TypeOption.CREATURE, container);
      const artifactImg = findImageByType(TypeOption.ARTIFACT, container);

      expect(creatureImg).toBeInTheDocument();
      expect(artifactImg).toBeInTheDocument();
    });
  });

  describe('when disabled', () => {
    it('disables ExcludeUnselectedButton', () => {
      renderTypeFilter({ disabled: true });
      const button = getExcludeUnselectedButton();
      fireEvent.click(button!);
      expect(mockOnUpdateFilter).not.toHaveBeenCalled();
    });

    it('disabled ClearFilterButton', () => {
      renderTypeFilter({ disabled: true });
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).not.toHaveBeenCalled();
      expect(mockOnUpdateFilter).not.toHaveBeenCalled();
    });

    it('disables type filter icons', () => {
      const { container } = renderTypeFilter({ disabled: true });
      const creatureButton = clickTypeButton(TypeOption.CREATURE, container);
      expect(creatureButton).toBeDisabled();
    });

    it('does not update filters when disabled icons are clicked', () => {
      const { container } = renderTypeFilter({ disabled: true });

      clickTypeButton(TypeOption.CREATURE, container);

      expect(mockOnUpdateFilter).not.toHaveBeenCalled();
    });
  });
});
