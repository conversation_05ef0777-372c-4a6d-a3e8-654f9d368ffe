import * as React from 'react';

interface IProps {
  name: string;
  children?: React.ReactNode;
}

export const FilterTitle = (props: IProps) => {
  return (
    <div className="filter-title">
      <div className="grow-xs-1 info-heading">{props.name}</div>
      <div className="flex justify-end filter-no-padding" style={{ minWidth: 'fit-content' }}>
        {props.children}
      </div>
    </div>
  );
};
