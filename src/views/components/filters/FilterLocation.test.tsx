import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { FakeStackLocation } from '../../../../tests/fake/FakeStackLocation';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { StackLocation } from '../../../models/StackLocation';
import { FilterLocation } from './FilterLocation';

const mockOnLoad = vi.fn();
const mockUpdate = vi.fn();
const mockRemove = vi.fn();

const fakeStackLocation = new FakeStackLocation();

const createLocationList = ({ locations = [] as StackLocation[] } = {}) => {
  return Immutable.List(locations);
};

const DEFAULT_PROPS = {
  index: 0,
  onLoad: mockOnLoad,
  update: mockUpdate,
  clearable: false,
  last: true,
};

const renderFilterLocation = (props: Partial<Omit<ComponentProps<typeof FilterLocation>, 'dispatcher'>> = {}) => {
  return renderWithDispatcher(FilterLocation, {
    ...DEFAULT_PROPS,
    ...props,
  });
};

// Helper functions
const openDropdown = () => {
  const selectContainer = screen.getByRole('textbox');
  fireEvent.mouseDown(selectContainer);
};

const expectOptionVisible = ({ optionText }: { optionText: string }) => {
  expect(screen.getByText(optionText)).toBeInTheDocument();
};

const mockOnLoadWithLocations = (locations: StackLocation[] = []) => {
  mockOnLoad.mockResolvedValue(createLocationList({ locations }));
};

describe('FilterLocation', () => {
  beforeEach(() => {
    mockOnLoadWithLocations();
  });

  describe('when rendered', () => {
    it('renders select', () => {
      renderFilterLocation();

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('loads options on mount', () => {
      const locations = [fakeStackLocation.fake(), fakeStackLocation.fake()];
      mockOnLoadWithLocations(locations);

      renderFilterLocation();

      expect(mockOnLoad).toHaveBeenCalledWith(0, undefined);
    });

    it('renders separator', () => {
      // render with index > 0
      renderFilterLocation({ index: 1 });

      expect(screen.getByText('/')).toBeInTheDocument();
    });

    it('hides separator', () => {
      // render with index = 0
      renderFilterLocation({ index: 0 });

      expect(screen.queryByText('/')).not.toBeInTheDocument();
    });

    describe('with clearable prop', () => {
      it('renders correctly', () => {
        renderFilterLocation({ clearable: true });
      });
    });
  });

  describe('with react-select component', () => {
    test('displays loaded options', async () => {
      const location = fakeStackLocation.fake();
      const locations = [location];
      mockOnLoadWithLocations(locations);

      renderFilterLocation();

      await waitFor(() => {
        openDropdown();
      });
      expectOptionVisible({ optionText: location.get('name') });
    });

    test('shows no options message when empty', async () => {
      renderFilterLocation();

      await waitFor(() => {
        openDropdown();
      });
      expect(screen.getByText('No existing locations')).toBeInTheDocument();
    });

    test('triggers update on selection', async () => {
      const location = fakeStackLocation.fake();
      const locations = [location];
      mockOnLoadWithLocations(locations);

      renderFilterLocation();

      await waitFor(() => {
        openDropdown();
      });
      fireEvent.click(screen.getByText(location.get('name')));
      expect(mockUpdate).toHaveBeenCalledWith(location);
    });

    test('displays selected value', () => {
      const location = fakeStackLocation.fake();

      renderFilterLocation({ value: location });

      expect(screen.getByText(location.get('name'))).toBeInTheDocument();
    });

    describe('with last prop', () => {
      it('disables select', () => {
        renderFilterLocation({ last: false });

        expect(screen.getByRole('textbox')).toBeDisabled();
      });
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders clear button', () => {
      renderFilterLocation({ remove: mockRemove });

      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    it('hides clear button', () => {
      renderFilterLocation();

      expect(screen.queryByText('Clear')).not.toBeInTheDocument();
    });

    test('triggers remove on click', () => {
      renderFilterLocation({ remove: mockRemove });

      fireEvent.click(screen.getByText('Clear'));
      expect(mockRemove).toHaveBeenCalledTimes(1);
    });
  });

  describe('with onLoad prop', () => {
    test('passes index and parentLocation', () => {
      const parentLocation = fakeStackLocation.fake();

      renderFilterLocation({ index: 2, parentLocation });

      expect(mockOnLoad).toHaveBeenCalledWith(2, parentLocation);
    });

    test('handles empty response', async () => {
      expect(() => {
        renderFilterLocation();
      }).not.toThrow();
    });

    test('handles multiple locations', async () => {
      const location1 = fakeStackLocation.fake();
      const location2 = fakeStackLocation.fake();
      const location3 = fakeStackLocation.fake();
      const locations = [location1, location2, location3];
      mockOnLoadWithLocations(locations);

      renderFilterLocation();

      await waitFor(() => {
        openDropdown();
      });
      expectOptionVisible({ optionText: location1.get('name') });
      expectOptionVisible({ optionText: location2.get('name') });
      expectOptionVisible({ optionText: location3.get('name') });
    });
  });
});
