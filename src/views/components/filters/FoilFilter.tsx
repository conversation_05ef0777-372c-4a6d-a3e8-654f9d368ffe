import * as React from 'react';
import { stringToBoolean } from '../../../helpers/boolean_helper';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IProps {
  include?: boolean;
  advancedFoil?: boolean;
  displayType: FilterDisplay;
  updateFoil: (include: boolean) => void;
  onClear: () => void;
  disabled?: boolean;
}

export const FoilFilter = (props: IProps) => {
  React.useEffect(() => {
    if (props.include === undefined) {
      props.updateFoil(true);
    }
  }, []);

  function onChangeFoil(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    props.updateFoil(stringToBoolean(evt.currentTarget.value as 'true' | 'false'));
  }

  return (
    <>
      <FilterTitle name="Foil">
        <ClearFilterButton
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          onClick={() => {
            props.onClear();
          }}
        />
      </FilterTitle>
      {props.include === undefined ? null : (
        <select
          className="select"
          disabled={props.disabled == true || props.advancedFoil !== undefined}
          value={props.include.toString()}
          onChange={onChangeFoil}
        >
          <option value={'true'}>Only Foils</option>
          <option value={'false'}>No Foils</option>
        </select>
      )}
    </>
  );
};
