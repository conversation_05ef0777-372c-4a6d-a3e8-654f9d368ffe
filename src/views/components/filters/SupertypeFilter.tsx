import * as Immutable from 'immutable';
import * as React from 'react';
import Select, { components, OptionsType, OptionTypeBase } from 'react-select';
import { EnumHelper } from '../../../helpers/enum';
import { TextFormat } from '../../../helpers/fmt';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { SupertypeFilter } from '../../../models/filters/SupertypeFilter';
import { Supertype } from '../../../models/Supertype';
import { SimpleNoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IProps {
  supertypes: Immutable.OrderedMap<string, SupertypeFilter>;
  displayType: FilterDisplay;
  update: (supertypes: Immutable.OrderedMap<string, SupertypeFilter>) => void;
  onClear?: () => void;
  disabled?: boolean;
  menuPortal?: HTMLDivElement;
}

class SupertypeItem implements OptionTypeBase {
  label: string;
  value: SupertypeFilter;
  constructor(filter: SupertypeFilter) {
    this['label'] = TextFormat.humanizeKey(filter.get('supertype'));
    this['value'] = filter;
  }

  formatOptionLabel = () => {
    return <div className="react-select__suggestion">{this['label']}</div>;
  };
}

export const SupertypeFilterComponent = (props: IProps) => {
  const MultiValueCard = (props: any) => {
    const supertypeFilter = props.data.value as SupertypeFilter;
    return (
      <components.MultiValue {...props}>
        <div
          style={{
            textDecorationLine: supertypeFilter.get('include') ? '' : 'line-through',
            cursor: 'pointer',
          }}
          onClick={() => {
            updateSupertype(supertypeFilter);
          }}
        >
          {props.data.label}
        </div>
      </components.MultiValue>
    );
  };

  function updateSupertype(supertypeFilter: SupertypeFilter) {
    document.activeElement && (document.activeElement as HTMLElement).blur();
    const target = props.supertypes.get(supertypeFilter.get('supertype'));
    props.update(
      props.supertypes
        .set(supertypeFilter.get('supertype'), target.set('include', !target.get('include')))
        .toOrderedMap(),
    );
  }

  function onChange(supertypeItemArray: OptionsType<SupertypeItem> | null) {
    props.update(
      Immutable.OrderedMap<string, SupertypeFilter>(
        supertypeItemArray === null
          ? []
          : supertypeItemArray.map((item: SupertypeItem) => {
              return [(item['value'] as SupertypeFilter).get('supertype'), item['value'] as SupertypeFilter];
            }),
      ),
    );
  }

  function generateOptions(): SupertypeItem[] {
    return Immutable.List<string>(['None'])
      .merge(EnumHelper.allCases(Supertype))
      .filter((value: Supertype) => {
        return props.supertypes.get(value) === undefined;
      })
      .map((value: Supertype) => {
        return new SupertypeItem(new SupertypeFilter({ include: true, supertype: value }));
      })
      .toArray();
  }

  const value = props.supertypes
    .map((filter: SupertypeFilter) => {
      return new SupertypeItem(filter);
    })
    .toArray();

  return (
    <>
      <FilterTitle name="Supertype">
        <ClearFilterButton
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          onClick={() => {
            props.update(Immutable.OrderedMap<string, SupertypeFilter>());
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <div style={{ cursor: 'text', minWidth: '20rem' }}>
        <Select
          components={{
            MultiValue: MultiValueCard,
            NoOptionsMessage: SimpleNoOptionsMessage,
          }}
          formatOptionLabel={(supertypeItem: SupertypeItem) => supertypeItem.formatOptionLabel()}
          styles={styleOverride}
          className={'react-select-type'}
          classNamePrefix="react-select-type"
          options={generateOptions()}
          value={value}
          onChange={onChange}
          isMulti={true}
          menuPortalTarget={props.menuPortal}
          defaultOptions
          isDisabled={props.disabled == true}
        />
      </div>
    </>
  );
};
