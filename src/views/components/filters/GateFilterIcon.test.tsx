import { faker } from '@faker-js/faker';
import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { FilterState } from '../../../models/filters/FilterState';
import { GateFilterIcon } from './GateFilterIcon';

const mockOnClick = vi.fn();

type GateFilterIconProps = ComponentProps<typeof GateFilterIcon>;

const DEFAULT_PROPS: GateFilterIconProps = {
  iconPath: faker.internet.url(),
  filterState: FilterState.INACTIVE,
  hoverText: faker.lorem.sentence(),
  onClick: mockOnClick,
};

const renderGateFilterIcon = (props: Partial<GateFilterIconProps> = {}) => {
  return render(<GateFilterIcon {...DEFAULT_PROPS} {...props} />);
};

describe('GateFilterIcon', () => {
  describe('when rendered', () => {
    it('renders button', () => {
      renderGateFilterIcon();
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('renders image', () => {
      renderGateFilterIcon();
      expect(screen.getByRole('img')).toBeInTheDocument();
    });

    test('trigger onClick prop', () => {
      renderGateFilterIcon();
      fireEvent.click(screen.getByRole('button'));
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('with disabled prop', () => {
    it('is disabled when disabled prop is true', () => {
      renderGateFilterIcon({ disabled: true });
      expect(screen.getByRole('button')).toBeDisabled();
    });

    it('is not disabled when disabled prop is false', () => {
      renderGateFilterIcon({ disabled: false });
      expect(screen.getByRole('button')).not.toBeDisabled();
    });
  });

  describe('with tooltip', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('does not show tooltip initially', () => {
      const hoverText = faker.lorem.sentence();
      renderGateFilterIcon({ hoverText });
      expect(screen.queryByText(hoverText)).not.toBeInTheDocument();
    });

    test('shows tooltip', async () => {
      const hoverText = faker.lorem.sentence();
      renderGateFilterIcon({ hoverText });
      const button = screen.getByRole('button');

      fireEvent.mouseEnter(button);
      expect(screen.queryByText(hoverText)).not.toBeInTheDocument();

      vi.advanceTimersByTime(500);
      expect(await screen.findByText(hoverText)).toBeInTheDocument();
    });

    test('hides tooltip', async () => {
      const hoverText = faker.lorem.sentence();
      renderGateFilterIcon({ hoverText });
      const button = screen.getByRole('button');

      fireEvent.mouseEnter(button);
      vi.advanceTimersByTime(500);

      expect(await screen.findByText(hoverText)).toBeInTheDocument();

      fireEvent.mouseLeave(button);
      expect(screen.queryByText(hoverText)).not.toBeInTheDocument();
    });

    test('cancels tooltip when mouse leave before delay', () => {
      const hoverText = faker.lorem.sentence();
      renderGateFilterIcon({ hoverText });
      const button = screen.getByRole('button');

      fireEvent.mouseEnter(button);
      fireEvent.mouseLeave(button);

      vi.advanceTimersByTime(500);
      expect(screen.queryByText(hoverText)).not.toBeInTheDocument();
    });
  });
});
