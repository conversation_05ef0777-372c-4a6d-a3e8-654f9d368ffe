import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { ExcludeUnselectedButton } from './ExcludeUnselectedButton';

const mockOnClick = vi.fn();

const DEFAULT_PROPS: ComponentProps<typeof ExcludeUnselectedButton> = {
  onClick: mockOnClick,
  disabled: false,
};

const renderExcludeUnselectedButton = (props: Partial<ComponentProps<typeof ExcludeUnselectedButton>> = {}) => {
  return render(<ExcludeUnselectedButton {...DEFAULT_PROPS} {...props} />);
};

describe('ExcludeUnselectedButton', () => {
  describe('when rendered', () => {
    it('renders button text', () => {
      renderExcludeUnselectedButton();
      expect(screen.getByText('Exclude unselected')).toBeInTheDocument();
    });

    it('renders icon', () => {
      renderExcludeUnselectedButton();
      const button = screen.getByText('Exclude unselected');

      expect(button.previousSibling).toBeInstanceOf(SVGElement);
    });

    test('triggers onClick prop when clicked', () => {
      renderExcludeUnselectedButton();
      fireEvent.click(screen.getByText('Exclude unselected'));
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('disabled button', () => {
    test('not trigger onClick', () => {
      renderExcludeUnselectedButton({ disabled: true });
      fireEvent.click(screen.getByText('Exclude unselected'));

      // Testing click behavior rather than .toBeDisabled() because this component
      // doesn't use a <button> element, so we verify the disabled functionality
      // by ensuring the onClick handler is not triggered when disabled
      expect(mockOnClick).not.toHaveBeenCalled();
    });
  });
});
