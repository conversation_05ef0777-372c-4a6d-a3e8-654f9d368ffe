import { faker } from '@faker-js/faker';
import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { QueryFilter } from './QueryFilter';

const mockUpdateQuery = vi.fn();
const mockOnClear = vi.fn();

const renderQueryFilter = (props: Partial<ComponentProps<typeof QueryFilter>> = {}) => {
  return render(
    <QueryFilter
      displayType={FilterDisplay.REMOVEABLE}
      onClear={mockOnClear}
      updateQuery={mockUpdateQuery}
      {...props}
    />,
  );
};

describe('QueryFilter', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when rendered', () => {
    it('renders input', () => {
      renderQueryFilter();
      const input = screen.getByRole<HTMLInputElement>('textbox');
      expect(input).toBeInTheDocument();
      expect(input.value).toBe('');
    });

    it('reflects localQuery in input value', () => {
      const query = faker.lorem.words(3);
      renderQueryFilter({ localQuery: query });
      const input = screen.getByRole<HTMLInputElement>('textbox');
      expect(input.value).toBe(query);
    });
  });

  describe('with FilterTitle', () => {
    it('renders title', () => {
      renderQueryFilter();
      expect(screen.getByText('Card Name')).toBeInTheDocument();
    });
  });

  describe('with input element', () => {
    test('calls updateQuery on change', () => {
      renderQueryFilter();
      const input = screen.getByRole('textbox');
      const value = faker.lorem.words(2);
      fireEvent.change(input, { target: { value } });
      expect(mockUpdateQuery).toHaveBeenCalledWith(value);
    });

    describe('disabled input element', () => {
      it('uses disabled prop', () => {
        renderQueryFilter({ disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();
      });

      it('uses advancedQuery prop', () => {
        renderQueryFilter({ disabled: false, advancedQuery: faker.lorem.word() });
        expect(screen.getByRole('textbox')).toBeDisabled();
      });
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders a remove button', () => {
      renderQueryFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders a clear button', () => {
      renderQueryFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    test('onClear prop is triggered', () => {
      renderQueryFilter();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });

    it('disabled when advancedQuery is not empty', () => {
      renderQueryFilter({ advancedQuery: faker.lorem.word() });
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).not.toHaveBeenCalled();
    });
  });
});
