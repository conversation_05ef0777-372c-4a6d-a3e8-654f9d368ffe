import * as Immutable from 'immutable';
import * as React from 'react';
import Select, { components, OptionsType } from 'react-select';
import { EnumHelper } from '../../../helpers/enum';
import { CardSetType } from '../../../models/CardSets';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { SetTypeFilter } from '../../../models/filters/SetTypeFilter';
import { SimpleNoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';
import { SetTypeFilterSuggestion } from '../react-select/SetTypeFilterSuggestion';
import { ClearFilterButton } from './ClearFilterButton';
import { FilterTitle } from './FilterTitle';

interface IProps {
  setTypes: Immutable.OrderedMap<string, SetTypeFilter>;
  update: (setTypeFilters: Immutable.OrderedMap<string, SetTypeFilter>) => void;
  disabled?: boolean;
  onClear?: () => void;
  menuPortal?: HTMLDivElement;
  displayType: FilterDisplay;
}

export const SelectSetType = (props: IProps) => {
  const MultiValueCard = (props: any) => {
    const setTypeFilter = props.data.value as SetTypeFilter;
    return (
      <components.MultiValue {...props}>
        <div
          style={{ textDecorationLine: setTypeFilter.get('include') ? '' : 'line-through', cursor: 'pointer' }}
          onClick={() => {
            updateSetType(setTypeFilter);
          }}
        >
          {props.data.label}
        </div>
      </components.MultiValue>
    );
  };

  function updateSetType(setTypeFilter: SetTypeFilter) {
    document.activeElement && (document.activeElement as HTMLElement).blur();
    if (!props.disabled) {
      const target = props.setTypes.get(setTypeFilter.get('setType'));
      props.update(
        props.setTypes.set(setTypeFilter.get('setType'), target.set('include', !target.get('include'))).toOrderedMap(),
      );
    }
  }

  function onChange(supertypeItemArray: OptionsType<SetTypeFilterSuggestion> | null) {
    props.update(
      Immutable.OrderedMap<string, SetTypeFilter>(
        supertypeItemArray === null
          ? []
          : supertypeItemArray.map((value: SetTypeFilterSuggestion) => {
              return [(value['value'] as SetTypeFilter).get('setType'), value['value'] as SetTypeFilter];
            }),
      ),
    );
  }

  function generateOptions(): SetTypeFilterSuggestion[] {
    return EnumHelper.allCases(CardSetType)
      .filter((value: CardSetType) => {
        return props.setTypes.get(value) === undefined;
      })
      .map((value: CardSetType) => {
        return new SetTypeFilterSuggestion(new SetTypeFilter({ include: true, setType: value }));
      })
      .toArray();
  }

  const value = props.setTypes
    .map((setTypeFilter: SetTypeFilter) => {
      return new SetTypeFilterSuggestion(setTypeFilter);
    })
    .toArray();

  return (
    <>
      <FilterTitle name="Set Type">
        <ClearFilterButton
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          disabled={props.disabled}
          onClick={() => {
            props.update(Immutable.OrderedMap<string, SetTypeFilter>());
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <div style={{ cursor: 'text', minWidth: '20rem' }}>
        <Select
          components={{
            MultiValue: MultiValueCard,
            NoOptionsMessage: SimpleNoOptionsMessage,
          }}
          formatOptionLabel={(suggestion: SetTypeFilterSuggestion) => suggestion.formatOptionLabel()}
          styles={styleOverride}
          className={'react-select-type'}
          classNamePrefix="react-select-type"
          options={generateOptions()}
          value={value}
          onChange={onChange}
          isMulti={true}
          isDisabled={props.disabled}
          menuPortalTarget={props.menuPortal}
          defaultOptions
        />
      </div>
    </>
  );
};
