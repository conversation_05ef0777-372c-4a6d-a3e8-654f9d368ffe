import block from '@iconify/icons-ic/block';
import { Icon } from '@iconify/react';
import * as React from 'react';

interface IClearFilterButton {
  className?: string;
  disabled?: boolean;
  onClick: () => void;
}

// Deprecated: Use SecondaryButton component
export const ExcludeUnselectedButton = (props: IClearFilterButton) => {
  return (
    <>
      <div
        className="secondary-button--separator"
        style={props.disabled ? { cursor: 'default' } : {}}
        onClick={() => {
          if (!props.disabled) {
            props.onClick();
          }
        }}
      >
        <Icon height={'15px'} width={'15px'} icon={block} style={{ margin: '0rem 0.25rem -0.2rem 0rem' }} />
        <span>Exclude unselected</span>
      </div>
    </>
  );
};
