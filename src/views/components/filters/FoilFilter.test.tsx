import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { FoilFilter } from './FoilFilter';

const mockUpdateFoil = vi.fn();
const mockOnClear = vi.fn();

type FoilFilterProps = Partial<Omit<ComponentProps<typeof FoilFilter>, 'include'>>;
type FoilFilterWithIncludeProps = Partial<ComponentProps<typeof FoilFilter>>;

const renderFoilFilter = (props: FoilFilterProps = {}) => {
  return render(
    <FoilFilter displayType={FilterDisplay.REMOVEABLE} updateFoil={mockUpdateFoil} onClear={mockOnClear} {...props} />,
  );
};

const renderFoilFilterWithInclude = (props: FoilFilterWithIncludeProps = {}) => {
  return render(
    <FoilFilter
      displayType={FilterDisplay.REMOVEABLE}
      updateFoil={mockUpdateFoil}
      onClear={mockOnClear}
      include
      {...props}
    />,
  );
};

describe('FoilFilter', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when include is undefined', () => {
    it('render FilterTitle', () => {
      renderFoilFilter();
      expect(screen.getByText('Foil')).toBeInTheDocument();
    });

    it('does not render select', () => {
      renderFoilFilter();
      expect(screen.queryByRole('combobox')).not.toBeInTheDocument();
    });

    test('calls updateFoil on mount', async () => {
      renderFoilFilter();
      await waitFor(() => {
        expect(mockUpdateFoil).toHaveBeenCalledWith(true);
      });
    });

    describe('with ClearFilterButton', () => {
      it('renders a remove button', () => {
        renderFoilFilter({ displayType: FilterDisplay.REMOVEABLE });
        expect(screen.getByText('Remove')).toBeInTheDocument();
      });

      it('renders a clear button', () => {
        renderFoilFilter({ displayType: FilterDisplay.PERMANENT });
        expect(screen.getByText('Clear')).toBeInTheDocument();
      });

      test('onClear prop is triggered', () => {
        renderFoilFilter();
        fireEvent.click(screen.getByText('Remove'));
        expect(mockOnClear).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('when include is defined', () => {
    it('render FilterTitle', () => {
      renderFoilFilterWithInclude();
      expect(screen.getByText('Foil')).toBeInTheDocument();
    });

    test('not calls updateFoil on mount', () => {
      renderFoilFilterWithInclude();
      expect(mockUpdateFoil).not.toHaveBeenCalled();
    });

    describe('with include prop true', () => {
      it('renders select', () => {
        renderFoilFilterWithInclude();
        const select = screen.getByRole<HTMLSelectElement>('combobox');
        expect(select.value).toBe('true');
      });
    });

    describe('with include prop false', () => {
      it('renders select', () => {
        renderFoilFilterWithInclude({ include: false });
        const select = screen.getByRole<HTMLSelectElement>('combobox');
        expect(select.value).toBe('false');
      });
    });

    describe('with ClearFilterButton', () => {
      it('renders a remove button ', () => {
        renderFoilFilterWithInclude({ displayType: FilterDisplay.REMOVEABLE });
        expect(screen.getByText('Remove')).toBeInTheDocument();
      });

      it('renders a clear button', () => {
        renderFoilFilterWithInclude({ displayType: FilterDisplay.PERMANENT });
        expect(screen.getByText('Clear')).toBeInTheDocument();
      });

      test('onClear prop is triggered', () => {
        renderFoilFilterWithInclude();
        fireEvent.click(screen.getByText('Remove'));
        expect(mockOnClear).toHaveBeenCalledTimes(1);
      });
    });

    it('should call updateFoil when onChange fired', () => {
      renderFoilFilterWithInclude();
      fireEvent.change(screen.getByRole('combobox'), { target: { value: 'false' } });
      expect(mockUpdateFoil).toHaveBeenCalledWith(false);

      fireEvent.change(screen.getByRole('combobox'), { target: { value: 'true' } });
      expect(mockUpdateFoil).toHaveBeenCalledWith(true);
    });

    describe('disabled foil select element', () => {
      it('uses disabled prop', () => {
        renderFoilFilterWithInclude({ include: false, disabled: true });
        expect(screen.getByRole('combobox')).toBeDisabled();
      });

      it('uses advancedFoil prop', () => {
        renderFoilFilterWithInclude({ include: true, disabled: false, advancedFoil: true });
        expect(screen.getByRole('combobox')).toBeDisabled();
      });
    });
  });
});
