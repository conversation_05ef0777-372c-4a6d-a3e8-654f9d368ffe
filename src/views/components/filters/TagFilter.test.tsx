import { faker } from '@faker-js/faker';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import * as TagsAPI from '../../../../src/api/Tags';
import { FilterDisplay } from '../../../../src/models/filters/FilterDisplay';
import { TagFilter } from '../../../../src/models/filters/TagFilter';
import { TagFilterData } from '../../../../src/models/filters/TagFilterData';
import { Tag } from '../../../../src/models/Tags';
import { createArray, FakeTag } from '../../../../tests/fake/Fake';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { TagFilterComponent } from './TagFilter';

const mockUpdate = vi.fn();
const mockOnClear = vi.fn();

// Mock APIs and containers
vi.mock('../../../../src/api/Tags', () => ({
  all: vi.fn(),
}));

vi.mock('../../../../src/containers/HasCards', () => ({
  Attach: vi.fn((Component) => Component),
}));

const createTagFilter = (tag: Tag, include = true) => new TagFilter({ tag, include });
const createTagFilterData = (tagFilters: TagFilter[] = [], excludeUnselected = false) => {
  const tagFilterMap = Immutable.OrderedMap<string, TagFilter>(
    tagFilters.map((filter) => [filter.get('tag').get('name'), filter]),
  );
  return new TagFilterData({ tagFilter: tagFilterMap, excludeUnselected });
};

const mockTags = createArray(FakeTag, 3);
const mockTagFilter = createTagFilter(mockTags[0]);
const mockTagFilters = mockTags.map((tag) => createTagFilter(tag));

const DEFAULT_PROPS = {
  tags: new TagFilterData(),
  displayType: FilterDisplay.PERMANENT,
  update: mockUpdate,
  cardPage: Immutable.Map({
    ownerUsername: faker.internet.userName(),
  }),
  tagPage: Immutable.Map(),
};

// Helper functions for DRY
const renderTagFilter = (props: Partial<ComponentProps<typeof TagFilterComponent>> = {}) => {
  return renderWithDispatcher(TagFilterComponent, { ...DEFAULT_PROPS, ...props });
};

const getSelect = () => screen.getByRole('textbox');
const getDropdown = () => screen.getByRole('combobox');
const typeInSearch = (text: string) => fireEvent.change(getSelect(), { target: { value: text } });

const mockTagsAPI = (tags = mockTags) => {
  (TagsAPI.all as any).mockResolvedValue(Immutable.OrderedSet(tags));
};

const setupMockBlur = () => {
  const mockBlur = vi.fn();
  Object.defineProperty(document, 'activeElement', {
    value: { blur: mockBlur },
    writable: true,
  });
  return mockBlur;
};

describe('TagFilter', () => {
  beforeEach(() => {
    mockTagsAPI();
  });

  describe('when rendered', () => {
    it('displays all required elements', () => {
      renderTagFilter();

      expect(screen.getByText('Tags')).toBeInTheDocument();
      expect(getDropdown()).toBeInTheDocument();
      expect(getSelect()).toBeInTheDocument();
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    describe('with disabled prop', () => {
      it('disables inputs', () => {
        renderTagFilter({ disabled: true });

        expect(getDropdown()).toBeDisabled();
        expect(getSelect()).toBeDisabled();
      });
    });

    it('handles empty username', async () => {
      const emptyUserProps = {
        ...DEFAULT_PROPS,
        cardPage: Immutable.Map({ ownerUsername: '' }),
      };

      renderTagFilter(emptyUserProps);

      // Should not call API with empty username
      expect(TagsAPI.all).not.toHaveBeenCalled();
    });
  });

  describe('with exclude dropdown', () => {
    it('shows "has any of" by default', () => {
      renderTagFilter();

      expect(screen.getByText('has any of')).toBeInTheDocument();
    });

    it('shows "has exactly"', () => {
      // should show "has exactly" when excludeUnselected is true
      const tagsWithExclude = createTagFilterData([], true);
      renderTagFilter({ tags: tagsWithExclude });

      expect(screen.getByText('has exactly')).toBeInTheDocument();
    });

    describe('when dropdown changes', () => {
      it('updates excludeUnselected to true', () => {
        renderTagFilter();

        const dropdown = getDropdown();
        fireEvent.change(dropdown, { target: { value: 'true' } });
        expect(mockUpdate).toHaveBeenCalledWith(
          expect.objectContaining({
            excludeUnselected: true,
          }),
        );
      });

      it('updates excludeUnselected to false', () => {
        const tagsWithExclude = createTagFilterData([], true);
        renderTagFilter({ tags: tagsWithExclude });

        const dropdown = getDropdown();
        fireEvent.change(dropdown, { target: { value: 'false' } });
        expect(mockUpdate).toHaveBeenCalledWith(
          expect.objectContaining({
            excludeUnselected: false,
          }),
        );
      });

      it('handles invalid selection gracefully', () => {
        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
        renderTagFilter();

        const dropdown = getDropdown();

        // HTML select elements filter out invalid values, so we get empty string
        // This tests the default case in the switch statement
        fireEvent.change(dropdown, { target: { value: '' } });

        expect(consoleSpy).toHaveBeenCalledWith('Invalid selection: ');
        expect(mockUpdate).toHaveBeenCalledWith(
          expect.objectContaining({
            excludeUnselected: false,
          }),
        );

        consoleSpy.mockRestore();
      });
    });
  });

  describe('with Select component integration', () => {
    it('accepts input typing', () => {
      renderTagFilter();

      typeInSearch('test');
      expect(getSelect()).toHaveValue('test');
    });

    it('handles clearing selections via keyboard', () => {
      const selectedTags = createTagFilterData([mockTagFilter]);
      renderTagFilter({ tags: selectedTags });

      fireEvent.keyDown(getSelect(), { key: 'Backspace', ctrlKey: true });
      expect(mockUpdate).toHaveBeenCalledWith(
        expect.objectContaining({
          tagFilter: Immutable.OrderedMap(),
        }),
      );
    });
  });

  describe('with selected tags displayed', () => {
    describe('when multiple tags selected', () => {
      it('displays selected tags', () => {
        const selectedTags = createTagFilterData([mockTagFilters[0], mockTagFilters[1]]);
        renderTagFilter({ tags: selectedTags });

        expect(screen.getByText(`#${mockTagFilters[0].get('tag').get('name')}`)).toBeInTheDocument();
        expect(screen.getByText(`#${mockTagFilters[1].get('tag').get('name')}`)).toBeInTheDocument();
      });
    });

    describe('when selected tag clicked', () => {
      it('toggles include state', () => {
        const selectedTags = createTagFilterData([mockTagFilter]);
        renderTagFilter({ tags: selectedTags });

        const tagElement = screen.getByText(`#${mockTagFilter.get('tag').get('name')}`);
        fireEvent.click(tagElement);
        expect(mockUpdate).toHaveBeenCalled();
      });

      it('blurs active element', () => {
        const selectedTags = createTagFilterData([mockTagFilter]);
        const mockBlur = setupMockBlur();

        renderTagFilter({ tags: selectedTags });

        const tagElement = screen.getByText(`#${mockTagFilter.get('tag').get('name')}`);
        fireEvent.click(tagElement);
        expect(mockBlur).toHaveBeenCalled();
      });

      it('applies strikethrough for excluded tags', () => {
        const excludedFilter = createTagFilter(mockTags[0], false);
        const selectedTags = createTagFilterData([excludedFilter]);

        renderTagFilter({ tags: selectedTags });

        const tagElement = screen.getByText(`#${excludedFilter.get('tag').get('name')}`);
        expect(tagElement).toHaveStyle('text-decoration-line: line-through');
      });
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders Remove button', () => {
      renderTagFilter({ displayType: FilterDisplay.REMOVEABLE });

      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders Clear button', () => {
      renderTagFilter({ displayType: FilterDisplay.PERMANENT });

      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    test('triggers onClear prop and clears tags', () => {
      renderTagFilter({ onClear: mockOnClear });

      fireEvent.click(screen.getByText('Clear'));
      expect(mockOnClear).toHaveBeenCalled();
      expect(mockUpdate).toHaveBeenCalledWith(new TagFilterData());
    });
  });

  describe('with option generation', () => {
    it('excludes already selected tags', () => {
      const selectedTags = createTagFilterData(mockTagFilters);
      renderTagFilter({ tags: selectedTags });

      fireEvent.focus(getSelect());
      fireEvent.keyDown(getSelect(), { key: 'ArrowDown' });

      // Selected tags should not appear in dropdown (options are filtered)
      // We can verify this by checking the component doesn't crash
      expect(getSelect()).toBeInTheDocument();
    });
  });

  describe('with API integration', () => {
    it('calls API during mount', async () => {
      renderTagFilter();

      await waitFor(() => {
        expect(TagsAPI.all).toHaveBeenCalledWith(DEFAULT_PROPS.cardPage.get('ownerUsername'));
      });
    });
  });
});
