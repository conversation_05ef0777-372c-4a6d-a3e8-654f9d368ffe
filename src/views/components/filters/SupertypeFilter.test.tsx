import { fireEvent, render, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { TextFormat } from '../../../helpers/fmt';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { SupertypeFilter } from '../../../models/filters/SupertypeFilter';
import { Supertype } from '../../../models/Supertype';
import { SupertypeFilterComponent } from './SupertypeFilter';

const mockUpdate = vi.fn();
const mockOnClear = vi.fn();

type SupertypeFilterProps = Partial<ComponentProps<typeof SupertypeFilterComponent>>;

const createSupertypeFilter = (supertype: Supertype, include = true) => {
  return new SupertypeFilter({ include, supertype });
};

const createSupertypesMap = (supertypes: { type: Supertype; include?: boolean }[] = []) => {
  return Immutable.OrderedMap<string, SupertypeFilter>(
    supertypes.map(({ type, include = true }) => [type, createSupertypeFilter(type, include)]),
  );
};

const DEFAULT_PROPS = {
  supertypes: Immutable.OrderedMap<string, SupertypeFilter>(),
  update: mockUpdate,
  displayType: FilterDisplay.REMOVEABLE,
  onClear: mockOnClear,
};

const renderSupertypeFilter = (props: SupertypeFilterProps = {}) => {
  return render(<SupertypeFilterComponent {...DEFAULT_PROPS} {...props} />);
};

const mockSupertypes = createSupertypesMap([
  { type: Supertype.BASIC, include: true },
  { type: Supertype.LEGENDARY, include: false },
]);

const capitalize = (str: string) => {
  return TextFormat.humanizeKey(str);
};

describe('SupertypeFilterComponent', () => {
  describe('when rendered', () => {
    describe('with FilterTitle', () => {
      it('renders title', () => {
        renderSupertypeFilter();
        expect(screen.getByText('Supertype')).toBeInTheDocument();
      });
    });

    it('shows placeholder when empty', () => {
      renderSupertypeFilter();
      expect(screen.getByText('Select...')).toBeInTheDocument();
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders a remove button', () => {
      renderSupertypeFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders a clear button', () => {
      renderSupertypeFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    it('onClear prop is triggered', () => {
      renderSupertypeFilter();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });

    it('clears all selections when clicked', () => {
      const supertypes = createSupertypesMap([{ type: Supertype.BASIC }]);
      renderSupertypeFilter({ supertypes });

      fireEvent.click(screen.getByText('Remove'));

      expect(mockUpdate).toHaveBeenCalledWith(Immutable.OrderedMap<string, SupertypeFilter>());
    });
  });

  describe('with react-select component', () => {
    it('displays selected items', () => {
      renderSupertypeFilter({ supertypes: mockSupertypes });
      expect(screen.getByText(capitalize(Supertype.BASIC))).toBeInTheDocument();
      expect(screen.getByText(capitalize(Supertype.LEGENDARY))).toBeInTheDocument();
    });

    it('shows excluded items with strikethrough', () => {
      renderSupertypeFilter({ supertypes: mockSupertypes });
      expect(screen.getByText(capitalize(Supertype.LEGENDARY))).toHaveStyle({ textDecorationLine: 'line-through' });
    });

    it('shows included items without strikethrough', () => {
      renderSupertypeFilter({ supertypes: mockSupertypes });
      expect(screen.getByText(capitalize(Supertype.BASIC))).toHaveStyle({ textDecorationLine: '' });
    });

    it('toggles include/exclude on click', () => {
      renderSupertypeFilter({ supertypes: mockSupertypes });

      fireEvent.click(screen.getByText(capitalize(Supertype.BASIC)));

      expect(mockUpdate).toHaveBeenCalled();
      // Get the first argument from the first call to mockUpdate (the updated Immutable.OrderedMap)
      const updateCall = mockUpdate.mock.calls[0][0];
      expect(updateCall.has(Supertype.BASIC)).toBe(true);
      const updatedBasic = updateCall.get(Supertype.BASIC);
      expect(updatedBasic.get('include')).toBe(false);
      expect(updatedBasic.get('supertype')).toBe(Supertype.BASIC);
    });

    it('adds selected option from dropdown', () => {
      renderSupertypeFilter();

      fireEvent.mouseDown(screen.getByRole('textbox'));

      expect(screen.getByText(capitalize(Supertype.BASIC))).toBeInTheDocument();

      fireEvent.click(screen.getByText(capitalize(Supertype.BASIC)));

      expect(mockUpdate).toHaveBeenCalled();
      // Get the first argument from the first call to mockUpdate (the updated Immutable.OrderedMap)
      const updateCall = mockUpdate.mock.calls[0][0];
      expect(updateCall.has(Supertype.BASIC)).toBe(true);
      const basicFilter = updateCall.get(Supertype.BASIC);
      expect(basicFilter.get('include')).toBe(true);
      expect(basicFilter.get('supertype')).toBe(Supertype.BASIC);
    });

    it('filters out selected options from dropdown menu', () => {
      const supertypes = createSupertypesMap([{ type: Supertype.BASIC }]);
      const { container } = renderSupertypeFilter({ supertypes });

      fireEvent.mouseDown(screen.getByRole('textbox'));

      expect(screen.getByText(capitalize(Supertype.LEGENDARY))).toBeInTheDocument();
      expect(screen.getByText(capitalize(Supertype.BASIC))).toBeInTheDocument();

      const dropdownMenu = container.querySelector('.react-select-type__menu');
      if (dropdownMenu) {
        expect(dropdownMenu).not.toHaveTextContent(capitalize(Supertype.BASIC));
      }
    });

    it('removes individual selections via X button', () => {
      const supertypes = createSupertypesMap([{ type: Supertype.BASIC }]);
      const { container } = renderSupertypeFilter({ supertypes });

      const removeButton = container.querySelector('.react-select-type__multi-value__remove');
      expect(removeButton).toBeInTheDocument();
      fireEvent.click(removeButton!);

      expect(mockUpdate).toHaveBeenCalledWith(Immutable.OrderedMap<string, SupertypeFilter>());
    });

    describe('with disabled prop', () => {
      it('disables select component', () => {
        renderSupertypeFilter({ disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();
      });
    });
  });
});
