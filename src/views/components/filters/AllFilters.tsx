import filter_list from '@iconify/icons-ic/baseline-filter-list';
import * as Immutable from 'immutable';
import * as React from 'react';
import * as FilterActions from '../../../actions/FilterActions';
import * as HasCardLists from '../../../containers/HasCardLists';
import * as HasCards from '../../../containers/HasCards';
import * as HasSubscription from '../../../containers/HasSubscription';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { FilterComponentType } from '../../../models/FilterComponent';
import { MTGFilter } from '../../../models/filters/mtg/MTGFilters';
import { FilterDialog } from './FilterDialog';
import { Button, ButtonClass } from './UpdateFiltersButton';

interface IProps {
  dispatcher: Dispatcher;
  mergedFilters: MTGFilter;
  filterOptions: MTGFilter;
  advancedFilterOptions: MTGFilter;
}

interface IState {
  allFiltersOpen: boolean;
}

export const AllFilters = HasSubscription.Attach<IProps>(
  HasCardLists.Attach<IProps & HasSubscription.IProps>(
    HasCards.Attach<IProps & HasCardLists.IProps & HasSubscription.IProps>(
      class extends React.Component<IProps & HasCards.IProps & HasCardLists.IProps & HasSubscription.IProps, IState> {
        /**
         * @override
         */
        constructor(props: IProps & HasCards.IProps & HasCardLists.IProps & HasSubscription.IProps) {
          super(props);
          this.state = { allFiltersOpen: false };
        }

        render() {
          let restrictedOptions = Immutable.List<FilterComponentType>();
          if (!this.props.subscription.isMerchant()) {
            restrictedOptions = restrictedOptions.push(FilterComponentType.CARD_LIST);
          }

          return (
            <>
              <FilterDialog
                title="Collection Filters"
                dispatcher={this.props.dispatcher}
                isOpen={this.state.allFiltersOpen}
                filters={this.props.mergedFilters}
                filterOverride={this.props.advancedFilterOptions}
                cardLists={this.props.cardLists}
                onUpdate={(filters: MTGFilter) =>
                  FilterActions.activateFilter(
                    this.props.cardPage,
                    filters,
                    this.props.advancedFilterOptions,
                    this.props.dispatcher,
                  )
                }
                onDismiss={this.onDismissAllFilters.bind(this)}
                disabledFilters={restrictedOptions}
              />
              <Button
                class={ButtonClass.GREY}
                icon={filter_list}
                title="Filters"
                onClick={this.onClickAllFilters.bind(this)}
              />
            </>
          );
        }

        private onClickAllFilters(evt: React.SyntheticEvent<HTMLElement>) {
          evt.currentTarget.blur();
          this.setState({ allFiltersOpen: true });
        }

        private onDismissAllFilters() {
          this.setState({ allFiltersOpen: false });
        }
      },
    ),
  ),
);
