import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { FilterState } from '../../../models/filters/FilterState';
import { ReservedFilter } from './ReservedFilter';

const mockChangeReservedStatus = vi.fn();
const mockOnClear = vi.fn();

const DEFAULT_PROPS: ComponentProps<typeof ReservedFilter> = {
  filterState: FilterState.INACTIVE,
  changeReservedStatus: mockChangeReservedStatus,
  displayType: FilterDisplay.REMOVEABLE,
  onClear: mockOnClear,
};

const renderReservedFilter = (props: Partial<ComponentProps<typeof ReservedFilter>> = {}) => {
  return render(<ReservedFilter {...DEFAULT_PROPS} {...props} />);
};

describe('ReservedFilter', () => {
  describe('when rendered', () => {
    describe('with FilterTitle', () => {
      it('renders title', () => {
        renderReservedFilter();
        expect(screen.getByText('Reserved', { selector: ':not(option)' })).toBeInTheDocument();
      });
    });
    it('renders select', () => {
      renderReservedFilter();
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    it('renders default select options', () => {
      renderReservedFilter();
      expect(screen.getByRole('option', { name: 'Reserved' })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: 'Not Reserved' })).toBeInTheDocument();
    });

    describe('when on mount', () => {
      test('onPropChange triggered', () => {
        renderReservedFilter({
          filterState: FilterState.INACTIVE,
          displayType: FilterDisplay.REMOVEABLE,
        });
        expect(mockChangeReservedStatus).toHaveBeenCalledWith(FilterState.ON);
      });
      test('onPropChange not triggered', () => {
        renderReservedFilter({
          filterState: FilterState.ON,
          displayType: FilterDisplay.PERMANENT,
        });
        expect(mockChangeReservedStatus).not.toHaveBeenCalled();
      });
    });
  });

  describe('when displayType prop', () => {
    describe('with PERMANENT', () => {
      it('renders No Filter option', () => {
        renderReservedFilter({ displayType: FilterDisplay.PERMANENT });
        expect(screen.getByRole('option', { name: 'No Filter' })).toBeInTheDocument();
      });
    });

    describe('with REMOVEABLE', () => {
      it('does not render No Filter option', () => {
        renderReservedFilter({ displayType: FilterDisplay.REMOVEABLE });
        expect(screen.queryByRole('option', { name: 'No Filter' })).not.toBeInTheDocument();
      });
    });
  });

  describe('when select onChange', () => {
    test('triggers changeReservedStatus prop', () => {
      renderReservedFilter();
      fireEvent.change(screen.getByRole('combobox'), { target: { value: FilterState.ON } });
      expect(mockChangeReservedStatus).toHaveBeenCalledWith(FilterState.ON);
    });
  });

  describe('with disabled prop', () => {
    it('should be disabled when true', () => {
      renderReservedFilter({ disabled: true });
      expect(screen.getByRole('combobox')).toBeDisabled();
    });

    it('should not be disabled when false', () => {
      renderReservedFilter({ disabled: false });
      expect(screen.getByRole('combobox')).not.toBeDisabled();
    });

    it('should not be disabled by default', () => {
      renderReservedFilter();
      expect(screen.getByRole('combobox')).not.toBeDisabled();
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders Remove button', () => {
      renderReservedFilter({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders Clear button', () => {
      renderReservedFilter({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    it('disabled ClearFilterButton', () => {
      // Testing click behavior rather than .toBeDisabled() because this component
      // doesn't use a <button> element, so we verify the disabled functionality
      // by ensuring the onClick handler is not triggered when disabled
      renderReservedFilter({ disabled: true });
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).not.toHaveBeenCalled();
    });

    test('onClear prop is triggered with displayType REMOVEABLE', () => {
      renderReservedFilter();
      fireEvent.click(screen.getByText('Remove'));
      expect(mockOnClear).toHaveBeenCalled();
      expect(mockChangeReservedStatus).toHaveBeenCalledWith(FilterState.INACTIVE);
    });

    test('onClear prop is triggered with displayType PERMANENT', () => {
      renderReservedFilter({ displayType: FilterDisplay.PERMANENT });
      fireEvent.click(screen.getByText('Clear'));
      expect(mockOnClear).toHaveBeenCalled();
      expect(mockChangeReservedStatus).toHaveBeenCalledWith(FilterState.INACTIVE);
    });
  });
});
