import * as Immutable from 'immutable';
import * as React from 'react';
import Select, { OptionTypeBase } from 'react-select';
import { EnumHelper } from '../../../helpers/enum';
import { TextFormat } from '../../../helpers/fmt';
import { FilterComponentType } from '../../../models/FilterComponent';
import { MTGFilter } from '../../../models/filters/mtg/MTGFilters';
import { SimpleNoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';
import { FilterTitle } from './FilterTitle';

interface IProps {
  localFilter: MTGFilter;
  onUpdate: (newFilter: MTGFilter) => void;
  addFilter: (type: FilterComponentType) => void;
  menuPortal?: HTMLDivElement;
  restrictedOptions?: Immutable.List<FilterComponentType>;
}

class FilterComponentItem implements OptionTypeBase {
  label: string;
  value?: FilterComponentType;
  constructor(selectValue?: FilterComponentType) {
    this['label'] = TextFormat.humanizeKey(selectValue || 'Select...'); // Hack in the placeholder
    this['value'] = selectValue;
  }

  formatOptionLabel = () => {
    return (
      <div className="text__suggestion">
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__value">{TextFormat.humanizeKey(this['label'])}</div>
        </div>
      </div>
    );
  };
}

// TODO: Refactor this component to use the FilterStore to manage what filters are currentlyactive.
export const AddFilterComponent = (props: IProps) => {
  const [selected, setSelected] = React.useState<FilterComponentItem>(new FilterComponentItem(undefined));

  return (
    <div className="filter-composable-container">
      <FilterTitle name="Add Filter" />
      <Select
        className={'react-select-thin'}
        classNamePrefix="react-select-thin"
        components={{
          NoOptionsMessage: SimpleNoOptionsMessage,
        }}
        formatOptionLabel={(filterItem: FilterComponentItem) => filterItem.formatOptionLabel()}
        styles={styleOverride}
        options={generateOptions()}
        isClearable={false}
        value={selected} // Forcing a value with undefined forces the value to reset after selection
        onChange={onChange}
        defaultOptions
        menuPosition="fixed"
        menuPortalTarget={props.menuPortal}
      />
    </div>
  );

  function generateOptions() {
    const options = EnumHelper.allCases(FilterComponentType)
      .toSet()
      .subtract(props.restrictedOptions || Immutable.List());
    const items = Immutable.OrderedSet<FilterComponentType>(options);
    return items
      .subtract(props.localFilter.get('filterOrder'))
      .map((type: FilterComponentType) => new FilterComponentItem(type))
      .toArray();
  }

  function onChange(selectValue: FilterComponentItem) {
    if (selectValue['value'] !== undefined) {
      props.addFilter(selectValue['value']);
      setSelected(new FilterComponentItem(undefined));
    }
  }
};
