import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { FilterComponentType } from '../../../models/FilterComponent';
import { CardSetFilter } from '../../../models/filters/CardSetFilter';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { PokeFilter } from '../../../models/filters/pokemon/PokeFilters';
import { PriceMinMaxFilters } from '../filters/PriceMinMaxFilters';
import { QueryFilter } from '../filters/QueryFilter';
import { StartsWithFilter } from '../filters/StartsWithFilter';
import { CardSetFilterSuggestion } from '../react-select/CardSetFilterSuggestion';
import PokeSetFilter from './PokeSetFilter';

interface IProps {
  dispatcher: Dispatcher;
  filter: PokeFilter;
  type: FilterComponentType;
  displayType: FilterDisplay;
  filterOverride?: PokeFilter;
  onUpdate: (newFilter: PokeFilter) => void;
  disabled?: boolean;
}

export const PokeFilterComponent = (props: IProps) => {
  switch (props.type) {
    case FilterComponentType.QUERY:
      return (
        <QueryFilter
          localQuery={props.filter.getQuery()}
          advancedQuery={props.filterOverride?.getQuery()}
          updateQuery={(query: string) => props.onUpdate(props.filter.setQuery(query))}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.PRICE:
      return (
        <PriceMinMaxFilters
          filter={props.filter.get('price')}
          processValue={(minmax: 'min' | 'max', value?: string) => {
            props.onUpdate(
              props.filter.set('price', props.filter.get('price').set(minmax, value === undefined ? '' : value)),
            );
          }}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.STARTS_WITH:
      return (
        <StartsWithFilter
          startsWith={props.filter.get('startsWith')}
          update={(startsWith: string) => {
            props.onUpdate(props.filter.set('startsWith', startsWith));
          }}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.SET_NAME_STARTS_WITH:
      return (
        <StartsWithFilter
          titleOverride="Set Name Starts With"
          startsWith={props.filter.get('setStartsWith')}
          update={(startsWith: string) => {
            props.onUpdate(props.filter.set('setStartsWith', startsWith));
          }}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.CARD_SET:
      const setsDisabled =
        props.disabled == true || (props.filterOverride && props.filterOverride.get('pokeSet').isEmpty()) || false;
      return (
        <PokeSetFilter
          disabled={setsDisabled}
          updateSetFilter={(cardSetSuggestions: CardSetFilterSuggestion[]) => {
            if (!setsDisabled) {
              const newFilter = CardSetFilter.suggestionsToFilter(cardSetSuggestions);
              props.onUpdate(props.filter.set('pokeSet', newFilter));
            }
          }}
          setFilters={props.filter.get('pokeSet')}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
        />
      );
    default:
      return null;
  }
};
