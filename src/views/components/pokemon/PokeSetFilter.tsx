import * as Immutable from 'immutable';
import moment from 'moment';
import React from 'react';
import Select, { components, InputActionMeta, OptionsType } from 'react-select';
import * as PokeSetAPI from '../../../api/pokemon/PokeSets';
import * as request from '../../../api/Requests';
import { CardSetFilter } from '../../../models/filters/CardSetFilter';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { Game } from '../../../models/Game';
import { PokeSet } from '../../../models/pokemon/PokeSet';
import { LoadingMessage, NoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';
import { ClearFilterButton } from '../filters/ClearFilterButton';
import { FilterTitle } from '../filters/FilterTitle';
import { CardSetFilterSuggestion } from '../react-select/CardSetFilterSuggestion';

interface IProps {
  setFilters: Immutable.OrderedMap<string, CardSetFilter>;
  updateSetFilter: (suggestions: CardSetFilterSuggestion[]) => void;
  onClear?: () => void;
  displayType: FilterDisplay;
  disabled?: boolean;
}

interface IState {
  query: string;
  forceMenuOpen: boolean;
  isLoading: boolean;
  options?: OptionsType<CardSetFilterSuggestion>;
}

export default class PokeSetFilter extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      query: '',
      isLoading: false,
      // Set to true when set is selected, otherwise menu automatically closes until next user input.
      forceMenuOpen: false,
    };
  }

  private lastQuery: request.Request;
  private lastQueryTime: Date = new Date();

  MultiValueCard = (props: any) => {
    const cardSetFilter = props.data.value as CardSetFilter;
    return (
      <components.MultiValue {...props}>
        <div
          style={{ textDecorationLine: cardSetFilter.get('include') ? '' : 'line-through', cursor: 'pointer' }}
          onClick={() => this.updatePokeSetStatus(cardSetFilter.get('cardSet'))}
        >
          {cardSetFilter.get('cardSet').name()}
        </div>
      </components.MultiValue>
    );
  };

  render() {
    const query = this.state.query;
    return (
      <>
        <FilterTitle name="Set">
          <ClearFilterButton
            label={this.props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
            disabled={this.props.disabled}
            onClick={() => {
              if (this.props.onClear !== undefined) {
                this.props.onClear();
              }
            }}
          />
        </FilterTitle>
        <div style={{ cursor: 'text', minWidth: '20rem' }}>
          <Select
            components={{
              MultiValue: this.MultiValueCard,
              NoOptionsMessage,
              LoadingMessage,
            }}
            formatOptionLabel={(option: CardSetFilterSuggestion) => option.formatOptionLabel(Game.POKEMON)}
            styles={styleOverride}
            className={'react-select-sets'}
            classNamePrefix="react-select-sets"
            placeholder="Search by set name..."
            onInputChange={this.onChangeQuery.bind(this)}
            onChange={(selectedOptions: CardSetFilterSuggestion[]) => this.onChange(selectedOptions)}
            onBlur={() => this.onBlurFocus.bind(this, query)}
            onFocus={() => this.onBlurFocus.bind(this, query)}
            inputValue={query}
            noOptionsMessage={this.noOptionsHandler}
            options={query.length > 0 ? this.state.options : []}
            value={this.processCardSets(this.props.setFilters)}
            isMulti={true}
            filterOption={() => true}
            menuIsOpen={this.state.forceMenuOpen ? true : undefined}
            isLoading={this.state.isLoading}
            isDisabled={this.props.disabled}
          />
        </div>
      </>
    );
  }

  private updatePokeSetStatus(cardSet: PokeSet) {
    document.activeElement && (document.activeElement as HTMLElement).blur();
    const target = this.props.setFilters.get(cardSet.get('name'));
    this.props.updateSetFilter(
      this.props.setFilters
        .set(cardSet.get('name'), target.set('include', !target.get('include')))
        .map((cardSet: PokeSet) => new CardSetFilterSuggestion(cardSet))
        .toArray(),
    );
  }

  private processCardSets(filterMap: Immutable.OrderedMap<string, CardSetFilter>): CardSetFilterSuggestion[] {
    return filterMap
      .valueSeq()
      .map((cardSetFilter: CardSetFilter) => new CardSetFilterSuggestion(cardSetFilter))
      .toArray();
  }

  private onChange(selectedOptions: CardSetFilterSuggestion[] | null) {
    if (selectedOptions === null) {
      this.props.updateSetFilter([]);
    } else {
      // Resetting options here prevents users accidentally adding multiple sets in quick succession.
      this.setState({
        options: [],
      });
      this.setState({
        query: '',
        // Ensures that loading screen is displayed until numbers are found, unless this.state.query.length < 3.
        forceMenuOpen: false,
        isLoading: false,
      });
      this.props.updateSetFilter(selectedOptions);
    }
  }

  private onChangeQuery(query: string, evt: InputActionMeta) {
    if (query.length < 1) {
      this.setState({
        query: query,
        forceMenuOpen: false,
        options: [],
        isLoading: false,
      });
      return;
    } else if (evt.action === 'input-blur' || evt.action === 'menu-close') {
      return;
    }

    // Partial state reset
    // Very important that this is not deleted, will cause unacceptable lag otherwise.
    this.setState({
      query: query,
      forceMenuOpen: false,
      options: [],
      isLoading: true,
    });

    this.lastQuery && this.lastQuery.abort();
    this.lastQueryTime = new Date();
    this.handleSetSearch(query, true);
  }

  private onBlurFocus(query: string, evt: React.SyntheticEvent<HTMLInputElement>) {
    this.setState({
      query: query,
      forceMenuOpen: false,
    });
  }

  private noOptionsHandler(wrapper: { inputValue: string }) {
    const query = wrapper.inputValue;
    if (query.length < 3) {
      return null;
    } else {
      return 'No Results';
    }
  }

  // TODO: Work out how to search with just 1 or 2 characters without destroying the backend with requests
  // so that our users can search through available sets using the set code.
  private async handleSetSearch(set: string, include: boolean) {
    // We only want to run a request every 150ms at most otherwise we spam the
    // network and everything slows down
    setTimeout(() => {
      const lastQueryTime = moment(this.lastQueryTime);
      if (moment(new Date()).diff(lastQueryTime, 'milliseconds', true) > 150) {
        const requestPromise = PokeSetAPI.search(set);

        this.lastQuery = requestPromise.request;
        requestPromise.promise
          .then((pokeSets) => {
            const suggestions = Immutable.List<CardSetFilterSuggestion>(
              pokeSets
                .filterNot((pokeSet: PokeSet) => this.props.setFilters.has(pokeSet.get('name')))
                .map(
                  (cardSet: PokeSet) =>
                    new CardSetFilterSuggestion(new CardSetFilter({ include: include, cardSet: cardSet })),
                ),
            ).toArray();
            this.setState({
              options: suggestions as OptionsType<CardSetFilterSuggestion>,
              isLoading: false,
            });
          })
          .catch(() => {
            this.setState({
              options: undefined,
              isLoading: false,
            });
          });
      }
    }, 200);
  }
}
