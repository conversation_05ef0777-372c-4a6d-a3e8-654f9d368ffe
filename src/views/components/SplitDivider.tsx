import * as React from 'react';

interface IProps {
  children: React.ReactNode;
  inDialog?: boolean;
}

export function SplitDivider(props: IProps): JSX.Element {
  const tabsClassNames = props.inDialog ? 'col-xs-4' : 'col-xs-6 col-sm-6 col-md-4 col-lg-3 col-xl-2';
  const dividerClassNames = props.inDialog ? 'col-xs-4' : 'col-xs-3 col-sm-3 col-md-4 col-lg-4 col-xl-5';
  return (
    <>
      <div className={dividerClassNames}>
        <div className="section-divider" style={{ marginTop: '1.5rem' }} />
      </div>
      <div className={tabsClassNames} style={{ padding: '0.1rem 0 0 0' }}>
        {props.children}
      </div>
      <div className={dividerClassNames}>
        <div className="section-divider" style={{ marginTop: '1.5rem' }} />
      </div>
    </>
  );
}
