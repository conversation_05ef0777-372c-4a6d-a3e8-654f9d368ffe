import * as React from 'react';
import * as MessageActions from '../../actions/MessageActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import { ICallback } from '../../models/Messages';

interface IProps {
  type: 'info' | 'success' | 'error';
  message: string;
  button: string | null;
  onClickButton: ICallback | null;
}

interface IState {}

export class Message extends React.Component<HasDispatcher.IProps & IProps, IState> {
  /**
   * @override
   */
  constructor(props: HasDispatcher.IProps & IProps) {
    super(props);
    this.state = {};
  }

  /**
   * @override
   */
  render() {
    if (!this.props.message || !this.props.message.length) {
      return null;
    }

    let typeClassName = '';
    switch (this.props.type) {
      case 'info':
        typeClassName = 'is-info';
        break;
      case 'success':
        typeClassName = 'is-success';
        break;
      case 'error':
        typeClassName = 'is-error';
        break;
    }

    return (
      <div className="message" onClick={this.onClickDismiss.bind(this)}>
        <div className={'message-content ' + typeClassName} onClick={this.onClickDismiss.bind(this)}>
          <span className="message-text">{this.props.message}</span>
          {this.props.button && this.props.onClickButton ? (
            <div className="message-button" onClick={this.onClickButton.bind(this)}>
              {this.props.button}
            </div>
          ) : null}
        </div>
      </div>
    );
  }

  private onClickDismiss(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    evt.stopPropagation();
    MessageActions.clear(this.props.dispatcher);
  }

  private onClickButton(evt: React.SyntheticEvent<HTMLButtonElement>) {
    evt.preventDefault();
    evt.stopPropagation();
    this.props.onClickButton && this.props.onClickButton(evt);
  }
}
