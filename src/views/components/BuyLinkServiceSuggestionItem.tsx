import * as React from 'react';
import { OptionTypeBase } from 'react-select';
import { CartService } from '../../lib/carts/CartService';

export class BuyLinkItem implements OptionTypeBase {
  label: CartService['type'];
  value: CartService;
  constructor(cartService: CartService) {
    this['label'] = cartService.type;
    this['value'] = cartService;
  }

  formatOptionLabel = () => {
    return (
      <div className="react-select__suggestion">
        <div className="react-select__suggestion__set">
          <img src={this['value'].iconURL} />
        </div>
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__value">{this['label']}</div>
        </div>
      </div>
    );
  };
}
