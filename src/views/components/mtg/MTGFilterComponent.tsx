import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { SelectValue } from '../../../helpers/select';
import { CardList } from '../../../models/CardList';
import { Condition } from '../../../models/Condition';
import { FilterComponentType } from '../../../models/FilterComponent';
import { CardSetFilter } from '../../../models/filters/CardSetFilter';
import { ColorFilter } from '../../../models/filters/ColorFilter';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { booleanToFilterState, FilterState, filterStateToAPI } from '../../../models/filters/FilterState';
import { MTGFilter } from '../../../models/filters/mtg/MTGFilters';
import { SetTypeFilter } from '../../../models/filters/SetTypeFilter';
import { SubtypeFilter } from '../../../models/filters/SubtypeFilter';
import { SupertypeFilter } from '../../../models/filters/SupertypeFilter';
import { TagFilterData } from '../../../models/filters/TagFilterData';
import { TypeFilters } from '../../../models/filters/TypeFilters';
import { Legality } from '../../../models/Legality';
import { Rarity } from '../../../models/Rarity';
import { CardListDropdown } from '../../cardbot/components/CardListDropdown';
import { ColorFilter as ColorFilterComponent } from '../filters/ColorFilter';
import { ConditionFilter } from '../filters/ConditionFilter';
import { FoilFilter } from '../filters/FoilFilter';
import { LegalityFilter } from '../filters/LegalityFilter';
import { PriceMinMaxFilters } from '../filters/PriceMinMaxFilters';
import { QueryFilter } from '../filters/QueryFilter';
import { RarityFilter } from '../filters/RarityFilter';
import { ReservedFilter } from '../filters/ReservedFilter';
import { SelectSetType } from '../filters/SelectSetType';
import SetFilter from '../filters/SetFilter';
import { StartsWithFilter } from '../filters/StartsWithFilter';
import { SubtypeFilterComponent } from '../filters/SubtypeFilter';
import { SupertypeFilterComponent } from '../filters/SupertypeFilter';
import { TagFilterComponent } from '../filters/TagFilter';
import { TypeFilter } from '../filters/TypeFilter';
import { CardSetFilterSuggestion } from '../react-select/CardSetFilterSuggestion';

interface IProps {
  dispatcher: Dispatcher;
  filter: MTGFilter;
  type: FilterComponentType;
  displayType: FilterDisplay;
  filterOverride?: MTGFilter;
  menuPortal?: HTMLDivElement;
  onUpdate: (newFilter: MTGFilter) => void;
  disabled?: boolean;
}

export const MTGFilterComponent = (props: IProps) => {
  switch (props.type) {
    case FilterComponentType.QUERY:
      return (
        <QueryFilter
          localQuery={props.filter.getQuery()}
          advancedQuery={props.filterOverride?.getQuery()}
          updateQuery={(query: string) => props.onUpdate(props.filter.setQuery(query))}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.COLOR:
      const advancedMana = props.filterOverride?.get('color');
      const colorDisabled = props.disabled == true || (advancedMana && advancedMana.active()) || false;
      return (
        <ColorFilterComponent
          colorFilters={props.filter.get('color')}
          onUpdateFilter={(color: ColorFilter) => {
            if (!colorDisabled) {
              props.onUpdate(props.filter.set('color', color));
            }
          }}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
          disabled={colorDisabled}
        />
      );
    case FilterComponentType.CARD_TYPES:
      return (
        <TypeFilter
          typeFilters={props.filter.get('cardTypes')}
          onUpdateFilter={(types: TypeFilters) => props.onUpdate(props.filter.set('cardTypes', types))}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.RARITIES:
      return (
        <RarityFilter
          rarityFilters={props.filter.get('rarities')}
          onUpdateFilter={(rarities: Immutable.OrderedSet<Rarity>) =>
            props.onUpdate(props.filter.set('rarities', rarities))
          }
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.TAGS:
      let tags: TagFilterData;
      let disabled = props.disabled == true;
      if (props.filterOverride && !props.filterOverride.get('tagFilter').get('tagFilter').isEmpty()) {
        tags = props.filterOverride.get('tagFilter');
        disabled ||= true;
      } else {
        tags = props.filter.get('tagFilter');
        disabled ||= false;
      }
      return (
        <TagFilterComponent
          dispatcher={props.dispatcher}
          tags={tags}
          update={(tags: TagFilterData) => props.onUpdate(props.filter.set('tagFilter', tags))}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={disabled}
          displayType={props.displayType}
          menuPortal={props.menuPortal}
        />
      );
    case FilterComponentType.CARD_SET:
      const setsDisabled =
        props.disabled == true || (props.filterOverride && !props.filterOverride.get('setFilter').isEmpty()) || false;
      return (
        <SetFilter
          disabled={setsDisabled}
          updateSetFilter={(cardSetSuggestions: CardSetFilterSuggestion[]) => {
            if (!setsDisabled) {
              props.onUpdate(props.filter.set('setFilter', CardSetFilter.suggestionsToFilter(cardSetSuggestions)));
            }
          }}
          cardSetFilters={props.filter.get('setFilter')}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
          menuPortal={props.menuPortal}
        />
      );
    case FilterComponentType.CARD_SET_TYPE:
      return (
        <SelectSetType
          setTypes={props.filter.get('setTypes')}
          update={(setTypeFilters: Immutable.OrderedMap<string, SetTypeFilter>) => {
            props.onUpdate(props.filter.set('setTypes', setTypeFilters));
          }}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
          disabled={props.disabled}
          menuPortal={props.menuPortal}
        />
      );
    case FilterComponentType.FOIL:
      const foilDisabled =
        props.disabled == true || (props.filterOverride && props.filterOverride.get('foil') !== undefined) || false;
      return (
        <FoilFilter
          include={props.filter.get('foil')}
          advancedFoil={props.filterOverride && props.filterOverride.get('foil')}
          updateFoil={(foil: boolean) => {
            if (foilDisabled) {
              return;
            }
            props.onUpdate(props.filter.set('foil', foil));
          }}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={foilDisabled}
        />
      );
    case FilterComponentType.PRICE:
      return (
        <PriceMinMaxFilters
          filter={props.filter.get('priceFilter')}
          processValue={(minmax: 'min' | 'max', value?: string) => {
            props.onUpdate(
              props.filter.set(
                'priceFilter',
                props.filter.get('priceFilter').set(minmax, value === undefined ? '' : value),
              ),
            );
          }}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.CARD_LIST:
      return (
        <CardListDropdown
          cardList={props.filter.get('cardList')}
          onSelectCardList={(cardList?: CardList) => {
            props.onUpdate(props.filter.set('cardList', cardList));
          }}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
          disabled={props.disabled}
          menuPortal={props.menuPortal}
        />
      );
    case FilterComponentType.CONDITION:
      return (
        <ConditionFilter
          onChange={(condition: SelectValue<Condition>) => {
            props.onUpdate(props.filter.set('condition', condition));
          }}
          className="icon-container--card-panel"
          currentCondition={props.filter.get('condition')}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.SUPERTYPE:
      return (
        <SupertypeFilterComponent
          supertypes={props.filter.get('supertypes')}
          update={(supertypes: Immutable.OrderedMap<string, SupertypeFilter>) => {
            props.onUpdate(props.filter.set('supertypes', supertypes));
          }}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
          menuPortal={props.menuPortal}
        />
      );
    case FilterComponentType.SUBTYPE:
      return (
        <SubtypeFilterComponent
          subtypes={props.filter.get('subtypes')}
          update={(subtypes: Immutable.OrderedMap<string, SubtypeFilter>) => {
            props.onUpdate(props.filter.set('subtypes', subtypes));
          }}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
          menuPortal={props.menuPortal}
        />
      );
    case FilterComponentType.LEGALITY:
      return (
        <LegalityFilter
          onChange={(legality: SelectValue<Legality>) => {
            props.onUpdate(props.filter.set('legality', legality));
          }}
          currentLegality={props.filter.get('legality')}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.RESERVED:
      return (
        <ReservedFilter
          filterState={booleanToFilterState(props.filter.get('isReserved'))}
          changeReservedStatus={(reservedStatus: FilterState) => {
            props.onUpdate(props.filter.set('isReserved', filterStateToAPI(reservedStatus)));
          }}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.STARTS_WITH:
      return (
        <StartsWithFilter
          startsWith={props.filter.get('startsWith')}
          update={(startsWith: string) => {
            props.onUpdate(props.filter.set('startsWith', startsWith));
          }}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.SET_NAME_STARTS_WITH:
      return (
        <StartsWithFilter
          titleOverride="Set Name Starts With"
          startsWith={props.filter.get('setStartsWith')}
          update={(startsWith: string) => {
            props.onUpdate(props.filter.set('setStartsWith', startsWith));
          }}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );
    default:
      return null;
  }
};
