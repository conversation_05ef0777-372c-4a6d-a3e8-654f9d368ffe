import * as React from 'react';
import { OptionTypeBase } from 'react-select';
import { AnyCard } from '../../../models/AnyCard';
import { Card } from '../../../models/Cards';
import { Game } from '../../../models/Game';
import { LorcanaCard } from '../../../models/lorcana/LorcanaCards';
import { PokeCard } from '../../../models/pokemon/PokeCards';
import { YugiCardSet } from '../../../models/yugioh/YugiCards';
import { LorcanaSetSymbol } from '../lorcana/LorcanaSetSymbol';
import { PokeSetSymbol } from '../pokemon/PokeSetSymbol';
import { SetSymbol } from '../SetSymbol';
import { YugiSetSymbol } from '../yugioh/YugiSetSymbol';

export class CardSuggestion implements OptionTypeBase {
  label: string;
  value: AnyCard;
  game: Game;
  identifier?: string;

  constructor(card: AnyCard, game: Game, identifier?: string) {
    this['label'] = card.displayLabel();
    this['value'] = card;
    this['game'] = game;
    this['identifier'] = identifier;
  }

  formatOptionLabel = () => {
    let setSymbol: JSX.Element;
    let cardName: string;
    let printingLabel: string;
    let ownedCount: number | undefined;

    switch (this.game as Game) {
      case Game.MTG:
        const mtgCard = this.value as Card;
        cardName = mtgCard.get('name');
        printingLabel = `${mtgCard.get('setName')} (${mtgCard.get('collectorNumber')})`;
        ownedCount = mtgCard.get('ownedCount') || 0;
        setSymbol = (
          <SetSymbol
            setName={mtgCard.get('setName')}
            setCode={mtgCard.get('setCode')}
            rarity={mtgCard.get('rarity')}
            hoverText={false}
          />
        );
        break;
      case Game.POKEMON:
        const pokeCard = this.value as PokeCard;
        cardName = pokeCard.get('name');
        printingLabel = `${pokeCard.get('setName')} (${pokeCard.get('collectorNumber')})`;
        ownedCount = 0;
        setSymbol = (
          <PokeSetSymbol
            setName={pokeCard.get('setName')}
            setUUID={pokeCard.get('setUUID')}
            rarity={pokeCard.get('rarity')}
            hoverText={false}
          />
        );
        break;
      case Game.YUGIOH:
        const yugiCardSet = this.value as YugiCardSet;
        cardName = yugiCardSet.get('card').get('name');
        const setName = yugiCardSet.get('set').get('name');
        printingLabel = `${setName} (${yugiCardSet.get('code')})`;
        ownedCount = 0;
        setSymbol = (
          <YugiSetSymbol
            setName={setName}
            setUUID={yugiCardSet.get('set').get('uuid')}
            rarity={yugiCardSet.get('rarity')}
            hoverText={false}
          />
        );
        break;
      case Game.LORCANA:
        const lorcanaCard = this.value as LorcanaCard;
        cardName = lorcanaCard.get('name');
        printingLabel = `${lorcanaCard.get('setName')} (${lorcanaCard.get('collectorNumber')})`;
        ownedCount = 0;
        setSymbol = (
          <LorcanaSetSymbol
            setName={lorcanaCard.get('setName')}
            setUUID={lorcanaCard.get('setUUID')}
            rarity={lorcanaCard.get('rarity')}
            hoverText={false}
          />
        );
        break;
    }

    return (
      <div className="react-select__suggestion--large">
        <div className="react-select__suggestion__set">{setSymbol}</div>
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__name">{cardName}</div>
          <div className="react-select__suggestion__label__set">{printingLabel}</div>
        </div>
        {(ownedCount || 0) > 0 && (
          <div className="react-select__suggestion__count">
            <div>{ownedCount}</div>
          </div>
        )}
      </div>
    );
  };
}
