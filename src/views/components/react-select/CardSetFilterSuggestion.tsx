import * as React from 'react';
import { OptionTypeBase } from 'react-select';
import { CardSet } from '../../../models/CardSets';
import { CardSetFilter } from '../../../models/filters/CardSetFilter';
import { Game } from '../../../models/Game';
import { LorcanaSet } from '../../../models/lorcana/LorcanaSet';
import { PokeSet } from '../../../models/pokemon/PokeSet';
import { YugiSet } from '../../../models/yugioh/YugiSet';
import { LorcanaSetSymbol } from '../lorcana/LorcanaSetSymbol';
import { PokeSetSymbol } from '../pokemon/PokeSetSymbol';
import { SetSymbol } from '../SetSymbol';
import { YugiSetSymbol } from '../yugioh/YugiSetSymbol';

export class CardSetFilterSuggestion implements OptionTypeBase {
  label: string;
  value: CardSetFilter;
  onFocus?: (uuid: string) => void;
  onBlur?: () => void;

  constructor(cardSetFilter: CardSetFilter) {
    this['label'] = `${cardSetFilter.get('include') ? 's: ' : '-s: '} 
        ${cardSetFilter.get('cardSet').name()}`;
    this['value'] = cardSetFilter;
  }

  formatOptionLabel = (game: Game) => {
    const cardSet = this.value.get('cardSet');

    let inner: JSX.Element | null = null;
    switch (game) {
      case Game.MTG:
        const mtgSet = cardSet as CardSet;
        inner = (
          <>
            <div className="react-select__suggestion__set">
              <SetSymbol setName={mtgSet.get('name')} setCode={mtgSet.get('setCode')} hoverText={false} />
            </div>
            <div className="react-select__suggestion__label">
              <div className="react-select__suggestion__label__name">{mtgSet.get('name')}</div>
              <div className="react-select__suggestion__label__set">{mtgSet.get('setCode')}</div>
            </div>
          </>
        );
        break;
      case Game.POKEMON:
        const pokeSet = cardSet as PokeSet;
        inner = (
          <>
            <div className="react-select__suggestion__set">
              <PokeSetSymbol setName={pokeSet.get('name')} setUUID={pokeSet.get('uuid')} hoverText={false} />
            </div>
            <div className="react-select__suggestion__label">
              <div className="react-select__suggestion__label__name">{pokeSet.get('name')}</div>
              <div className="react-select__suggestion__label__set">{pokeSet.get('setCode')}</div>
            </div>
          </>
        );
        break;
      case Game.YUGIOH:
        const yugiSet = cardSet as YugiSet;
        inner = (
          <>
            <div className="react-select__suggestion__set">
              <YugiSetSymbol setName={yugiSet.get('name')} setUUID={yugiSet.get('uuid')} hoverText={false} />
            </div>
            <div className="react-select__suggestion__label">
              <div className="react-select__suggestion__label__name">{yugiSet.get('name')}</div>
              <div className="react-select__suggestion__label__set">{yugiSet.get('setCode')}</div>
            </div>
          </>
        );
        break;
      case Game.LORCANA:
        const lorcanaSet = cardSet as LorcanaSet;
        inner = (
          <>
            <div className="react-select__suggestion__set">
              <LorcanaSetSymbol setName={lorcanaSet.get('name')} setUUID={lorcanaSet.get('uuid')} hoverText={false} />
            </div>
            <div className="react-select__suggestion__label">
              <div className="react-select__suggestion__label__name">{lorcanaSet.get('name')}</div>
              <div className="react-select__suggestion__label__set">{lorcanaSet.get('setCode')}</div>
            </div>
          </>
        );
        break;
      default:
        // Display error as react-select may call this function with 'undefined'
        inner = (
          <>
            <div className="react-select__suggestion__label">
              <div className="react-select__suggestion__label__name">Unknown Game</div>
              <div className="react-select__suggestion__label__set">(?)</div>
            </div>
          </>
        );
        break;
    }

    return <div className="react-select__suggestion">{inner}</div>;
  };
}
