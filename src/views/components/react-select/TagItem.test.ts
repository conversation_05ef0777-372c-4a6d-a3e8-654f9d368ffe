import { faker } from '@faker-js/faker';
import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { Tag } from '../../../models/Tags';
import { TagFilter } from '../../../models/filters/TagFilter';
import { TagItem } from './TagItem';

const createTagItem = (name: string) => {
  const tag = new Tag({ name });
  const tagFilter = new TagFilter({
    include: true,
    tag,
  });
  const tagItem = new TagItem(tagFilter);
  return {
    tagItem,
    tagFilter,
  };
};

describe('TagItem', () => {
  it('initialize', () => {
    const tagName = faker.word.sample();
    const { tagItem, tagFilter } = createTagItem(tagName);

    expect(tagItem.label).toBe(`#${tagName}`);
    expect(tagItem.value).toBe(tagFilter);
  });

  test('formatOptionLabel', () => {
    const tagName = faker.word.sample();
    const { tagItem } = createTagItem(tagName);
    const formattedOption = tagItem.formatOptionLabel();
    const { container } = render(formattedOption);

    expect(container.textContent).toBe(`#${tagName}`);
  });
});
