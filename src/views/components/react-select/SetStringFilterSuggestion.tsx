import * as React from 'react';
import { OptionTypeBase } from 'react-select';

export class SetStringFilterSuggestion implements OptionTypeBase {
  label: string;
  value: string;
  constructor(myString: string) {
    this['label'] = myString;
    this['value'] = myString;
  }

  formatOptionLabel = () => {
    return formatStringSuggestion(this['value']);
  };
}

export function formatStringSuggestion(myString: string) {
  return (
    <div className="set-string-filter__suggestion">
      <div className="react-select__suggestion__label" style={{ marginLeft: '2.5rem' }}>
        <div className="react-select__suggestion__label__name">{`Filter by '${myString}'`}</div>
      </div>
    </div>
  );
}
