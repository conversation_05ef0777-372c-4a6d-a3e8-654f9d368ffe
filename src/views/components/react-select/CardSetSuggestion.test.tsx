import { render } from '@testing-library/react';
import { describe } from 'vitest';
import { createCardSetSuggestion } from '../../../../tests/views/components/react-select/helpers';
import { CardSet } from '../../../models/CardSets';
import { Game } from '../../../models/Game';

describe('CardSetSuggestion', () => {
  it('should render formatOptionLabel for MTG set', () => {
    const { cardSetFilter, suggestion, cardSet } = createCardSetSuggestion(true, Game.MTG);
    const formattedOption = suggestion.formatOptionLabel(Game.MTG);

    const { container } = render(formattedOption);

    expect(container.textContent).toContain(cardSetFilter.get('cardSet').name());

    expect(container.textContent).toContain((cardSet as CardSet).get('setCode'));
  });
});
