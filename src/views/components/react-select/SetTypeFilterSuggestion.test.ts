import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { TextFormat } from '../../../helpers/fmt';
import { CardSetType } from '../../../models/CardSets';
import { SetTypeFilter } from '../../../models/filters/SetTypeFilter';
import { SetTypeFilterSuggestion } from './SetTypeFilterSuggestion';

const createSetTypeFilter = (setType: CardSetType) => {
  return new SetTypeFilter({
    setType,
  });
};

describe('SetTypeFilterSuggestion', () => {
  it('initialize', () => {
    const setType = CardSetType.DUEL_DECK;
    const setTypeFilter = createSetTypeFilter(setType);
    const suggestion = new SetTypeFilterSuggestion(setTypeFilter);

    expect(suggestion.label).toBe(TextFormat.humanizeKey(setType));
    expect(suggestion.value).toBe(setTypeFilter);
  });

  test('formatOptionLabel', () => {
    const setType = CardSetType.DUEL_DECK;
    const setTypeFilter = createSetTypeFilter(setType);
    const suggestion = new SetTypeFilterSuggestion(setTypeFilter);
    const formattedOption = suggestion.formatOptionLabel();
    const { container } = render(formattedOption);

    const expectedText = TextFormat.capitalizeWord(TextFormat.humanizeKey(setType));
    expect(container.textContent).toBe(expectedText);
  });
});
