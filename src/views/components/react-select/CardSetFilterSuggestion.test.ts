import { render } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createCardSetSuggestion } from '../../../../tests/views/components/react-select/helpers';
import { Game } from '../../../models/Game';

// Helper function to test the formatOptionLabel function inside react-select components
const testFormatOptionLabel = (game: Game) => {
  const { cardSetFilter, suggestion, cardSet } = createCardSetSuggestion(true, game);
  const formattedOption = suggestion.formatOptionLabel(game);

  // Render the JSX element
  const { container } = render(formattedOption);

  // Check label name
  expect(container.textContent).toContain(cardSetFilter.get('cardSet').name());

  // Check Code
  // type cast to any to avoid type error
  expect(container.textContent).toContain((cardSet.get as any)('setCode'));
};

describe('CardSetFilterSuggestion', () => {
  beforeEach(() => {
    // Clear mocks before each test
    vi.clearAllMocks();
  });

  describe('with CardSetFilter include flag', () => {
    it('initializes with CardSetFilter include=true', () => {
      const { cardSet, suggestion, cardSetFilter } = createCardSetSuggestion(true);

      expect(suggestion.label).toContain('s:');
      expect(suggestion.label).toContain(cardSet.name());
      expect(suggestion.value).toBe(cardSetFilter);
    });

    it('initializes with CardSetFilter include=false', () => {
      const { cardSet, suggestion, cardSetFilter } = createCardSetSuggestion(false);

      expect(suggestion.label).toContain('-s:');
      expect(suggestion.label).toContain(cardSet.name());
      expect(suggestion.value).toBe(cardSetFilter);
    });
  });

  describe('With SetSymbol(MTG)', () => {
    it('renders MTG format', () => {
      testFormatOptionLabel(Game.MTG);
    });
  });

  describe('With PokeSetSymbol', () => {
    it('renders Pokemon format', () => {
      testFormatOptionLabel(Game.POKEMON);
    });
  });

  describe('With YugiSetSymbol', () => {
    it('renders Yugioh format', () => {
      testFormatOptionLabel(Game.YUGIOH);
    });
  });

  describe('With LorcanaSetSymbol', () => {
    it('renders Lorcana format', () => {
      testFormatOptionLabel(Game.LORCANA);
    });
  });

  describe('With unknown game type', () => {
    it('renders unknown game format', () => {
      const { suggestion } = createCardSetSuggestion();

      // @ts-ignore - Testing invalid input
      const result = suggestion.formatOptionLabel(undefined);

      const { container } = render(result);

      expect(container.textContent).toContain('Unknown Game');
      expect(container.textContent).toContain('(?)');
    });
  });
});
