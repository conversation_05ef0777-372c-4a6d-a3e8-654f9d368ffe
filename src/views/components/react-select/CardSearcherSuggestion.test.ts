import { faker } from '@faker-js/faker';
import { describe, expect, it, vi } from 'vitest';
import { Game } from '../../../models/Game';
import { CardSearcherSuggestion, CardSearcherSuggestionType } from './CardSearcherSuggestion';

const cardName = faker.commerce.productName();
const mockSuggestion = {
  label: cardName,
  value: {
    id: faker.string.uuid(),
    name: cardName,
  },
  formatOptionLabel: vi.fn(),
};

describe('CardSearcherSuggestion', () => {
  describe('with card type', () => {
    it('creates suggestion with correct properties', () => {
      const suggestion = new CardSearcherSuggestion(mockSuggestion, CardSearcherSuggestionType.CARD);

      expect(suggestion.label).toBe(cardName);
      expect(suggestion.type).toBe(CardSearcherSuggestionType.CARD);
      expect(suggestion.suggestion).toBe(mockSuggestion);
    });
  });

  describe('with card-set type', () => {
    it('creates suggestion with correct properties', () => {
      const suggestion = new CardSearcherSuggestion(mockSuggestion, CardSearcherSuggestionType.CARD_SET);

      expect(suggestion.label).toBe(cardName);
      expect(suggestion.type).toBe(CardSearcherSuggestionType.CARD_SET);
      expect(suggestion.suggestion).toBe(mockSuggestion);
    });
  });

  describe('with option label', () => {
    it('delegates to underlying suggestion', () => {
      const suggestion = new CardSearcherSuggestion(mockSuggestion, CardSearcherSuggestionType.CARD);
      const game = Game.MTG;

      suggestion.formatOptionLabel(game);

      expect(mockSuggestion.formatOptionLabel).toHaveBeenCalledWith(game);
    });
  });
});
