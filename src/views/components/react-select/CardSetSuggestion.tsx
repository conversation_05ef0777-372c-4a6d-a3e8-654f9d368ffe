import * as React from 'react';
import { OptionTypeBase } from 'react-select';
import { CardSet } from '../../../models/CardSets';
import { SetSymbol } from '../SetSymbol';

// TODO: Duplicate of CardSetFilterSuggestion
export class CardSetSuggestion implements OptionTypeBase {
  label: string;
  value: CardSet;
  constructor(cardSet: CardSet) {
    this['label'] = `${cardSet.get('name')} (${cardSet.get('setCode')})`;
    this['value'] = cardSet;
  }

  formatOptionLabel = () => {
    return (
      <div className="react-select__suggestion">
        <div className="react-select__suggestion__set">
          <SetSymbol setName={this['value'].get('name')} setCode={this['value'].get('setCode')} hoverText={false} />
        </div>
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__name">{this['value'].get('name')}</div>
          <div className="react-select__suggestion__label__set">{this['value'].get('setCode')}</div>
        </div>
      </div>
    );
  };
}
