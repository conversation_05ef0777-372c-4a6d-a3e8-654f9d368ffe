import { faker } from '@faker-js/faker';
import { cleanup, render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { Card } from '../../../models/Cards';
import { Game } from '../../../models/Game';
import { LorcanaCard } from '../../../models/lorcana/LorcanaCards';
import { LorcanaRarity } from '../../../models/lorcana/LorcanaRarity';
import { PokeCard } from '../../../models/pokemon/PokeCards';
import { PokeRarity } from '../../../models/pokemon/PokeRarity';
import { Rarity } from '../../../models/Rarity';
import { YugiCard, YugiCardSet } from '../../../models/yugioh/YugiCards';
import { YugiRarity } from '../../../models/yugioh/YugiRarity';
import { YugiSet } from '../../../models/yugioh/YugiSet';
import { CardSuggestion } from './CardSuggestion';

interface MockMTGCardOptions {
  ownedCount?: number;
}

// Card test configurations for each game type
interface CardTestConfig {
  game: Game;
  getCardName: (card: any) => string;
  getSetName: (card: any) => string;
  getIdentifier: (card: any) => string;
}

// Card test configurations for each game type
const testConfigs: Record<Game, CardTestConfig> = {
  [Game.MTG]: {
    game: Game.MTG,
    getCardName: (card: Card) => card.get('name'),
    getSetName: (card: Card) => card.get('setName'),
    getIdentifier: (card: Card) => card.get('collectorNumber'),
  },
  [Game.POKEMON]: {
    game: Game.POKEMON,
    getCardName: (card: PokeCard) => card.get('name'),
    getSetName: (card: PokeCard) => card.get('setName'),
    getIdentifier: (card: PokeCard) => card.get('collectorNumber'),
  },
  [Game.YUGIOH]: {
    game: Game.YUGIOH,
    getCardName: (card: YugiCardSet) => card.get('card').get('name'),
    getSetName: (card: YugiCardSet) => card.get('set').get('name'),
    getIdentifier: (card: YugiCardSet) => card.get('code'),
  },
  [Game.LORCANA]: {
    game: Game.LORCANA,
    getCardName: (card: LorcanaCard) => card.get('name'),
    getSetName: (card: LorcanaCard) => card.get('setName'),
    getIdentifier: (card: LorcanaCard) => card.get('collectorNumber'),
  },
};

// Helper function to create a mock MTG card
const createMockMTGCard = (options: MockMTGCardOptions = {}) => {
  return new Card({
    name: faker.commerce.productName(),
    setName: faker.commerce.department(),
    setCode: faker.string.alphanumeric(3).toUpperCase(),
    collectorNumber: faker.string.numeric(3),
    rarity: Rarity.RARE,
    ownedCount: options.ownedCount ?? faker.number.int({ min: 0, max: 4 }),
  });
};

//Helper function to create a mock Pokemon card
const createMockPokeCard = () => {
  return new PokeCard({
    name: faker.commerce.productName(),
    setName: faker.commerce.department(),
    setUUID: faker.string.uuid(),
    collectorNumber: faker.string.numeric(3),
    rarity: PokeRarity.RARE,
  });
};

// Helper function to create a mock Yugi card set
// based on CardSuggestion, we need to use CardSet for yugi game
const createMockYugiCardSet = () => {
  const mockCard = new YugiCard({
    uuid: faker.string.uuid(),
    name: faker.commerce.productName(),
    level: faker.number.int({ min: 1, max: 12 }),
    attack: faker.number.int({ min: 0, max: 5000 }),
    defense: faker.number.int({ min: 0, max: 5000 }),
    type: 'Monster',
    description: faker.lorem.sentence(),
  });

  const mockSet = new YugiSet({
    uuid: faker.string.uuid(),
    name: faker.commerce.department(),
    releaseDate: new Date(),
    setCode: faker.string.alphanumeric(3).toUpperCase(),
  });

  return new YugiCardSet({
    uuid: faker.string.uuid(),
    card: mockCard,
    set: mockSet,
    rarity: YugiRarity.COMMON,
    code: faker.string.alphanumeric(8).toUpperCase(),
  });
};

// Helper function to create a mock Lorcana card
const createMockLorcanaCard = () => {
  return new LorcanaCard({
    name: faker.commerce.productName(),
    setName: faker.commerce.department(),
    setUUID: faker.string.uuid(),
    collectorNumber: faker.string.numeric(3),
    rarity: LorcanaRarity.COMMON,
  });
};

// Helper function to test basic card suggestion rendering
const testBasicCardRendering = (card: any, game: Game) => {
  const config = testConfigs[game];
  const suggestion = new CardSuggestion(card, game);
  const formattedOption = suggestion.formatOptionLabel();
  const { container } = render(formattedOption);

  // Test card information
  expect(container.textContent).toContain(config.getCardName(card));
  expect(container.textContent).toContain(config.getSetName(card));
  expect(container.textContent).toContain(config.getIdentifier(card));

  return { container, suggestion };
};

// Helper function to test owned count display
const testMTGOwnedCountDisplay = (ownedCount: number) => {
  const card = createMockMTGCard({ ownedCount });
  const suggestion = new CardSuggestion(card, Game.MTG);
  const formattedOption = suggestion.formatOptionLabel();
  const { container } = render(formattedOption);

  const countElement = container.querySelector('.react-select__suggestion__count');
  if (ownedCount > 0) {
    expect(countElement?.textContent).toContain(ownedCount.toString());
  } else {
    expect(countElement).toBeFalsy();
  }
};

// Helper function to test printing label format
const testPrintingLabel = (card: any, game: Game) => {
  const config = testConfigs[game];
  const suggestion = new CardSuggestion(card, game);
  const formattedOption = suggestion.formatOptionLabel();
  const { container } = render(formattedOption);

  const expectedLabel = `${config.getSetName(card)} (${config.getIdentifier(card)})`;
  const labelElement = container.querySelector('.react-select__suggestion__label__set');
  expect(labelElement?.textContent).toBe(expectedLabel);
};

describe('CardSuggestion', () => {
  describe('with MTG Game', () => {
    it('render card', () => {
      const card = createMockMTGCard({ ownedCount: 0 });
      testBasicCardRendering(card, Game.MTG);
    });

    it('render printing label', () => {
      const card = createMockMTGCard({ ownedCount: 0 });
      testPrintingLabel(card, Game.MTG);
    });

    // right now we only mtg game has owned count
    test('render owned count', () => {
      testMTGOwnedCountDisplay(0);
      cleanup();
      testMTGOwnedCountDisplay(3);
    });
  });

  describe('with Pokemon Game', () => {
    it('render card', () => {
      const card = createMockPokeCard();
      testBasicCardRendering(card, Game.POKEMON);
    });

    it('render printing label', () => {
      const card = createMockPokeCard();
      testPrintingLabel(card, Game.POKEMON);
    });
  });

  describe('with Yugioh Game', () => {
    it('render card', () => {
      const card = createMockYugiCardSet();
      testBasicCardRendering(card, Game.YUGIOH);
    });

    it('render printing label', () => {
      const card = createMockYugiCardSet();
      testPrintingLabel(card, Game.YUGIOH);
    });
  });

  describe('with Lorcana Game', () => {
    it('render card', () => {
      const card = createMockLorcanaCard();
      testBasicCardRendering(card, Game.LORCANA);
    });

    it('render printing label', () => {
      const card = createMockLorcanaCard();
      testPrintingLabel(card, Game.LORCANA);
    });
  });
});
