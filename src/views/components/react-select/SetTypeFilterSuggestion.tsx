import * as React from 'react';
import { OptionTypeBase } from 'react-select';
import { TextFormat } from '../../../helpers/fmt';
import { SetTypeFilter } from '../../../models/filters/SetTypeFilter';

export class SetTypeFilterSuggestion implements OptionTypeBase {
  label: string;
  value: SetTypeFilter;
  constructor(setTypeFilter: SetTypeFilter) {
    this['label'] = TextFormat.humanizeKey(setTypeFilter.get('setType'));
    this['value'] = setTypeFilter;
  }

  formatOptionLabel = () => {
    return <div className="react-select__suggestion">{TextFormat.capitalizeWord(this['label'])}</div>;
  };
}
