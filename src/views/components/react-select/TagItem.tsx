import * as React from 'react';
import { OptionTypeBase } from 'react-select';
import { TagFilter } from '../../../models/filters/TagFilter';

export class TagItem implements OptionTypeBase {
  label: string;
  value: TagFilter;
  constructor(filter: TagFilter) {
    this['label'] = `#${filter.get('tag').get('name')}`;
    this['value'] = filter;
  }

  formatOptionLabel = () => {
    return <div className="react-select__suggestion">{this['label']}</div>;
  };
}
