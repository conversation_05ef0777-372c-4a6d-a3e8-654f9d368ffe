import { OptionTypeBase } from 'react-select';
import { Game } from '../../../models/Game';

export enum CardSearcherSuggestionType {
  CARD = 'card',
  CARD_SET = 'card-set',
}

export class CardSearcherSuggestion implements OptionTypeBase {
  label: string;
  value: any;
  type: CardSearcherSuggestionType;
  suggestion: OptionTypeBase;

  constructor(suggestion: OptionTypeBase, type: CardSearcherSuggestionType) {
    this['label'] = suggestion['label'];
    this['value'] = suggestion['value'];
    this['suggestion'] = suggestion;
    this['type'] = type;
  }

  formatOptionLabel = (game: Game) => {
    return this.suggestion.formatOptionLabel(game);
  };
}
