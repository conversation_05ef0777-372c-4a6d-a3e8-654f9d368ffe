import { faker } from '@faker-js/faker';
import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeCardSet } from '../../../../tests/views/components/react-select/helpers';
import { CardSet } from '../../../models/CardSets';
import { Game } from '../../../models/Game';
import { CardSetSuggestion } from './CardSetSuggestion';
import { SetSearchSuggestion, SetSearchType } from './SetSearchSuggestion';
import { SetStringFilterSuggestion } from './SetStringFilterSuggestion';

// Helper function to create a mock CardSetSuggestion
const createMockCardSetSuggestion = () => {
  const cardSet = create(FakeCardSet, { game: Game.MTG }) as CardSet;
  return new CardSetSuggestion(cardSet);
};

// Helper function to create a mock SetStringFilterSuggestion
const createMockSetStringFilterSuggestion = () => {
  const value = faker.commerce.department();
  return new SetStringFilterSuggestion(value);
};

describe('SetSearchSuggestion', () => {
  describe('with CardSetSuggestion', () => {
    it('initialize correctly', () => {
      const cardSetSuggestion = createMockCardSetSuggestion();
      const setSearchSuggestion = new SetSearchSuggestion(cardSetSuggestion);

      expect(setSearchSuggestion.type).toBe(SetSearchType.CARD_SET);
      expect(setSearchSuggestion.label).toBe(cardSetSuggestion.label);
      expect(setSearchSuggestion.value).toBe(cardSetSuggestion.value);
      expect(setSearchSuggestion.suggestion).toBe(cardSetSuggestion);
    });
    it('delegate formatting', () => {
      const cardSetSuggestion = createMockCardSetSuggestion();
      const setSearchSuggestion = new SetSearchSuggestion(cardSetSuggestion);
      const formattedOption = setSearchSuggestion.formatOptionLabel();
      const { container } = render(formattedOption);

      const cardSet = cardSetSuggestion.value;
      expect(container.textContent).toContain(cardSet.get('name'));
      expect(container.textContent).toContain(cardSet.get('setCode'));
    });
  });
  describe('with SetStringFilterSuggestion', () => {
    it('initialize correctly', () => {
      const stringFilterSuggestion = createMockSetStringFilterSuggestion();
      const setSearchSuggestion = new SetSearchSuggestion(stringFilterSuggestion);

      expect(setSearchSuggestion.type).toBe(SetSearchType.STRING);
      expect(setSearchSuggestion.label).toBe(stringFilterSuggestion.label);
      expect(setSearchSuggestion.value).toBe(stringFilterSuggestion.value);
      expect(setSearchSuggestion.suggestion).toBe(stringFilterSuggestion);
    });
    it('delegate formatting', () => {
      const stringFilterSuggestion = createMockSetStringFilterSuggestion();
      const setSearchSuggestion = new SetSearchSuggestion(stringFilterSuggestion);
      const formattedOption = setSearchSuggestion.formatOptionLabel();
      const { container } = render(formattedOption);

      const expectedText = `Filter by '${stringFilterSuggestion.value}'`;
      expect(container.textContent).toBe(expectedText);
    });
  });
});
