import { faker } from '@faker-js/faker';
import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { SetStringFilterSuggestion } from './SetStringFilterSuggestion';

describe('SetStringFilterSuggestion', () => {
  it('initialize', () => {
    const testValue = faker.commerce.department();
    const suggestion = new SetStringFilterSuggestion(testValue);

    expect(suggestion.label).toBe(testValue);
    expect(suggestion.value).toBe(testValue);
  });

  test('formatOptionLabel', () => {
    const testValue = faker.commerce.department();
    const suggestion = new SetStringFilterSuggestion(testValue);
    const formattedOption = suggestion.formatOptionLabel();
    const { container } = render(formattedOption);

    expect(container.textContent).toBe(`Filter by '${testValue}'`);
  });
});
