import { OptionTypeBase } from 'react-select';
import { isString } from 'util';
import { CardSetSuggestion } from './CardSetSuggestion';
import { SetStringFilterSuggestion } from './SetStringFilterSuggestion';

export enum SetSearchType {
  CARD_SET = 'card-set',
  STRING = 'string',
}

/**
 * Used in cases where searching sets by both query and a specific ID is desired
 */
export class SetSearchSuggestion implements OptionTypeBase {
  label: string;
  value: CardSetSuggestion['value'] | SetStringFilterSuggestion['value'];
  suggestion: CardSetSuggestion | SetStringFilterSuggestion;
  type: SetSearchType;
  __isNew__?: boolean;
  constructor(suggestion: CardSetSuggestion | SetStringFilterSuggestion) {
    this['label'] = suggestion['label'];
    this['value'] = suggestion['value'];
    this['suggestion'] = suggestion;
    // (true for CardSuggestion, false for CardSetSuggestion)
    this['type'] = isString(this['value']) ? SetSearchType.STRING : SetSearchType.CARD_SET;
  }

  formatOptionLabel = () => {
    return this['suggestion'].formatOptionLabel();
  };
}
