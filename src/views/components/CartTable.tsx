import * as React from 'react';
import { CartTableGrouping } from '../../lib/carts/CartService';
import { CartLocation } from '../../models/CartButton';
import { CartData } from '../../models/CartCards';
import { CartRow } from './CartRow';
import { ColumnSize, TableConfiguration } from './Table';

interface IProps {
  cartData?: CartData;
  location: CartLocation;
  grouping: CartTableGrouping;
  isPublic: boolean;
  loggedIn: boolean;
  loading: boolean;
  onUpdateMap: (name: string, value: string) => void;
  onDeleteCell: (name: string) => void;
}

export const CartTable = (props: IProps) => {
  if (props.cartData === undefined || props.loading) {
    return (
      <div className="row justify-center">
        <div className="col-xs-12 flex vertical align-center">
          <span className="builder-placeholder">
            <div className="flex">
              <div className="spinner-xs"></div>
              <p style={{ marginLeft: '0.5rem' }}>Loading</p>
            </div>
          </span>
        </div>
      </div>
    );
  } else if (props.cartData && props.cartData.get('data').size < 1) {
    return (
      <div className="row justify-center">
        <div className="col-xs-12 flex vertical align-center">
          <span className="builder-placeholder">
            <p>Click Reset to restore removed cards back to the table.</p>
          </span>
        </div>
      </div>
    );
  } else {
    const groupedCardData = props.cartData.groupCartData(props.grouping);
    const additionalCells =
      Number(props.loggedIn) +
      Number(props.location === CartLocation.DECKVIEWER) +
      Number(props.grouping === CartTableGrouping.SET) +
      2 * Number(props.grouping === CartTableGrouping.PRINTING);

    const tableConfiguration = new TableConfiguration([
      TableConfiguration.repeat(1 + additionalCells, ColumnSize.MAX_CONTENT),
      ColumnSize.MINMAX_1FR,
      ColumnSize.MAX_CONTENT,
    ]);

    return (
      <div className={`base-table`} style={tableConfiguration.style()}>
        <div className="table__header collection-cell-justification cell-border-left">Quantity</div>
        {props.loggedIn ? (
          <div className="table__header collection-cell-justification cell-border-left">Owned</div>
        ) : null}
        {props.location === CartLocation.DECKVIEWER ? (
          <div className="table__header collection-cell-justification cell-border-left">In Deck</div>
        ) : null}
        {props.grouping !== CartTableGrouping.NAME ? (
          <div className="table__header collection-cell-justification cell-border-left">Set</div>
        ) : null}
        {props.grouping === CartTableGrouping.PRINTING ? (
          <div className="table__header collection-cell-justification cell-border-left">#</div>
        ) : null}
        <div className="table__header collection-cell-justification cell-border-left">Card Name</div>
        <div className="table__header collection-cell-justification cell-border-left"></div>
        {groupedCardData
          .keySeq()
          .toArray()
          .map((key: string, index: number) => {
            return (
              <React.Fragment key={`buycardcell-${index}`}>
                <CartRow
                  recordList={groupedCardData.get(key)}
                  location={props.location}
                  grouping={props.grouping}
                  isPublic={props.isPublic}
                  loggedIn={props.loggedIn}
                  dark={index % 2 === 0 ? true : false}
                  final={groupedCardData.size - 1 === index}
                  onUpdateMap={props.onUpdateMap.bind(this)}
                  onDeleteCell={props.onDeleteCell.bind(this)}
                />
              </React.Fragment>
            );
          })}
      </div>
    );
  }
};
