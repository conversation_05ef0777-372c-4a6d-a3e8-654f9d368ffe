import * as React from 'react';

interface IProps {
  title: string;
  onSubmitTitle: (title: string) => void;
  subtitle?: boolean;
  enabled?: boolean;
  emptyAllowed?: boolean;
  placeholder?: string;
  isLoading?: boolean;
  maxCharacter?: number;
}

interface IDisplayProps {
  text: string;
  placeholder?: string;
  onClick: (evt: React.SyntheticEvent<HTMLElement, Event>) => void;
}

// The props are a bit of a mess because they have to deal with two different edge cases.
// TODO: See if this can be simplified. Doesn't seem like a priority right now.
interface IState {
  editingTitle?: string;
  updatedTitle?: string;
  isEditingName: boolean;
}

export class EditableTitle extends React.Component<IProps, IState> {
  /**
   * @override
   */
  componentDidUpdate() {
    if (this.textInput.current && this.state.isEditingName) {
      this.textInput.current.focus();
    }
  }

  // There is a better way to handle focus but it requires React >16.3.
  // TODO: Replace when we update to a newer version of React.

  private readonly textInput: React.RefObject<HTMLInputElement>;
  constructor(props: IProps) {
    super(props);
    this.textInput = React.createRef<HTMLInputElement>();
    this.state = { isEditingName: false };
  }

  render() {
    if (this.props.isLoading !== undefined && this.props.isLoading) {
      return null;
    }
    const displayTitle = this.state.updatedTitle || this.props.title;
    const textClassName = this.props.subtitle ? 'sub-heading' : 'heading-lg';
    const enabled = this.props.enabled === undefined ? true : (this.props.enabled as boolean);
    const newTitle = this.state.editingTitle ? this.state.editingTitle : '';
    if (!enabled) {
      return (
        <>
          <h1 className={textClassName}>
            <this.DisplayText
              text={displayTitle}
              placeholder={this.props.placeholder}
              onClick={this.onClickTitle.bind(this)}
            />
          </h1>
        </>
      );
    }
    return (
      <>
        {this.state.isEditingName ? (
          <form onSubmit={this.onSubmitTitle.bind(this)}>
            <input
              maxLength={this.props.maxCharacter}
              ref={this.textInput}
              className={textClassName}
              value={this.state.editingTitle}
              onChange={this.onChangeTitle.bind(this)}
              onBlur={this.onBlurTitle.bind(this)}
            />
          </form>
        ) : (
          <div className={textClassName}>
            <this.DisplayText
              text={displayTitle}
              placeholder={this.props.placeholder}
              onClick={this.onClickTitle.bind(this)}
            />
          </div>
        )}
      </>
    );
  }

  private DisplayText(props: IDisplayProps) {
    const opacity = props.text === '' ? 0.75 : 1;
    return (
      <span style={{ opacity: opacity }} onClick={props.onClick}>
        {props.text || props.placeholder}
      </span>
    );
  }

  private onClickTitle(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({
      editingTitle: this.state.updatedTitle || this.props.title,
      isEditingName: true,
    });
  }

  private onSubmitTitle(evt: React.SyntheticEvent<HTMLFormElement>) {
    evt.preventDefault();
    const newTitle = this.state.editingTitle ? this.state.editingTitle : '';
    const characterValid = this.props.maxCharacter === undefined ? true : newTitle.length <= this.props.maxCharacter;
    if ((this.props.emptyAllowed || this.state.editingTitle) && characterValid) {
      this.props.onSubmitTitle(newTitle);
      this.setState({ updatedTitle: newTitle, editingTitle: undefined, isEditingName: false });
    }
  }

  private onChangeTitle(evt: React.SyntheticEvent<HTMLInputElement>) {
    evt.preventDefault();
    this.setState({ editingTitle: evt.currentTarget.value, isEditingName: true });
  }

  private onBlurTitle(evt: React.SyntheticEvent<HTMLInputElement>) {
    evt.preventDefault();
    this.setState({
      updatedTitle: this.state.updatedTitle || this.props.title,
      editingTitle: undefined,
      isEditingName: false,
    });
  }
}
