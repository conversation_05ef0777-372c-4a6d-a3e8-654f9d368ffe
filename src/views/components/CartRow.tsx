import clear from '@iconify/icons-ic/clear';
import * as Immutable from 'immutable';
import * as React from 'react';
import { CartTableGrouping } from '../../lib/carts/CartService';
import { CartLocation } from '../../models/CartButton';
import { CartCardRecord } from '../../models/CartCards';
import { CellBorders } from '../../models/CellBorder';
import * as Table from './Table';

interface IProps {
  recordList: Immutable.List<CartCardRecord>;
  location: CartLocation;
  grouping: CartTableGrouping;
  isPublic: boolean;
  loggedIn: boolean;
  dark: boolean;
  final: boolean;
  onUpdateMap: (name: string, value: string) => void;
  onDeleteCell: (jsonID: string) => void;
}

export const CartRow = (props: IProps) => {
  // cartCard is consistent over all instances of recordList.
  const reducedRecord = props.recordList.reduce((reduction: CartCardRecord, value: CartCardRecord) => {
    const recordQuantity = reduction.get('quantity');
    const valueQuantity = value.get('quantity');
    let newQuantity: number | undefined = undefined;
    if (recordQuantity !== undefined || valueQuantity !== undefined) {
      newQuantity =
        (recordQuantity === undefined ? 0 : recordQuantity) + (valueQuantity === undefined ? 0 : valueQuantity);
    }
    return reduction
      .set('ownedCount', reduction.get('ownedCount') + value.get('ownedCount'))
      .set('deckCount', reduction.get('deckCount') + value.get('deckCount'))
      .set('quantity', newQuantity);
  }, new CartCardRecord({ cartCard: props.recordList.first().get('cartCard') }));
  return (
    <>
      <Table.ParentCell dark={props.dark} cellBorders={CellBorders.default(props.final)}>
        <input
          className="buy-cards-input input is-number"
          value={reducedRecord.get('quantity') === undefined ? '' : reducedRecord.get('quantity')}
          type="number"
          onChange={(evt) => onUpdateMap.bind(this)(evt)}
        />
      </Table.ParentCell>
      {props.loggedIn ? (
        <Table.TextCell
          dark={props.dark}
          cellBorders={CellBorders.default(props.final)}
          text={reducedRecord.get('ownedCount').toString()}
        />
      ) : null}
      {props.location === CartLocation.DECKVIEWER ? (
        <Table.TextCell
          dark={props.dark}
          cellBorders={CellBorders.default(props.final)}
          text={reducedRecord.get('deckCount').toString()}
        />
      ) : null}
      {props.grouping !== CartTableGrouping.NAME ? (
        <Table.SetIconCell
          dark={props.dark}
          cellBorders={CellBorders.default(props.final)}
          setName={reducedRecord.get('cartCard').get('setName')}
          setCode={reducedRecord.get('cartCard').get('setCode')}
          hoverText={false}
        />
      ) : null}
      {props.grouping === CartTableGrouping.PRINTING ? (
        <Table.TextCell
          dark={props.dark}
          cellBorders={CellBorders.default(props.final)}
          text={reducedRecord.get('cartCard').get('collectorNumber')}
        />
      ) : null}
      <Table.TextCell
        dark={props.dark}
        cellBorders={CellBorders.default(props.final)}
        text={reducedRecord.get('cartCard').get('name')}
      />
      <Table.MaterialIcon
        dark={props.dark}
        cellBorders={CellBorders.rightCell(props.final)}
        materialIconText={clear}
        onClick={() => props.onDeleteCell(reducedRecord.get('cartCard').get('jsonID'))}
      />
    </>
  );

  function onUpdateMap(evt: React.SyntheticEvent<HTMLInputElement>) {
    props.onUpdateMap(reducedRecord.get('cartCard').get('jsonID'), evt.currentTarget.value);
  }
};
