import * as Immutable from 'immutable';
import * as React from 'react';
import * as HasCards from '../../containers/HasCards';
import * as HasDeck from '../../containers/HasDeck';
// Containers, Actions and Dispatcher
import * as HasMe from '../../containers/HasMe';
import * as HasUsers from '../../containers/HasUsers';
import Dispatcher from '../../dispatcher/Dispatcher';
import { CartLocation } from '../../models/CartButton';
import { CartData } from '../../models/CartCards';
// Models and Components
import { BuyLinkDialog } from './BuyLinkDialog';

interface IProps {
  dispatcher: Dispatcher;
  cartData: CartData;
  location: CartLocation;
  isMerchant: boolean;
  linkedCards?: Immutable.Map<string, number>;
}

interface IState {
  isOpen: boolean;
}

// Attachment is done here so that BuyLinkDialog.tsx can be a simplified functional component.
export const CartButton = HasMe.Attach<IProps>(
  HasUsers.Attach<IProps & HasMe.IProps>(
    HasDeck.Attach<IProps & HasMe.IProps & HasUsers.IProps>(
      HasCards.Attach<IProps & HasMe.IProps & HasUsers.IProps & HasDeck.IProps>(
        class extends React.Component<
          IProps & HasMe.IProps & HasUsers.IProps & HasDeck.IProps & HasCards.IProps,
          IState
        > {
          /**
           * @override
           */
          constructor(props: IProps & HasMe.IProps & HasUsers.IProps & HasDeck.IProps & HasCards.IProps) {
            super(props);
            this.state = {
              isOpen: false,
            };
          }

          render() {
            if (this.props.isMerchant) {
              return null;
            }
            const isPublic =
              this.props.location === CartLocation.COLLECTION
                ? this.props.publicCollection
                : this.props.currentDeckPublic;

            return (
              <>
                {this.state.isOpen && (
                  <BuyLinkDialog
                    dispatcher={this.props.dispatcher}
                    me={this.props.me}
                    isOpen={this.state.isOpen}
                    location={this.props.location}
                    cartData={this.props.cartData}
                    onDismiss={() => this.setState({ isOpen: false })}
                    linkedCards={this.props.linkedCards}
                    isPublic={isPublic}
                  />
                )}
                <button className="button-primary" onClick={() => this.setState({ isOpen: true })}>
                  {this.props.location === CartLocation.COLLECTION ? 'Purchase Cards' : 'Purchase Deck'}
                </button>
              </>
            );
          }
        },
      ),
    ),
  ),
);
