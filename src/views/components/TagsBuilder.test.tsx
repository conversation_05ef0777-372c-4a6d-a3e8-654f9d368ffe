import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { create, FakeUser } from '../../../tests/fake/Fake';
import { renderWithDispatcher } from '../../../tests/test-utils';
import * as Request from '../../api/Requests';
import { FreeLimits } from '../../models/Subscriptions';
import { Tag } from '../../models/Tags';
import { TagsBuilder } from './TagsBuilder';

vi.mock('../../api/Requests', () => ({
  get: vi.fn(),
}));

type TagsBuilderProps = React.ComponentProps<typeof TagsBuilder>;
const DEFAULT_PROPS: Omit<TagsBuilderProps, 'dispatcher'> = {
  me: create(FakeUser, {
    subscribed: true,
  }),
  tags: Immutable.OrderedSet([]),
  userTags: Immutable.OrderedSet([]),
  onAdd: vi.fn(),
  onRemove: vi.fn(),
  immutable: false,
  publicViewing: false,
  placeholder: '',
  isTaggingDeck: false,
};
function renderTagBuilder(props: Partial<TagsBuilderProps> = {}) {
  return renderWithDispatcher(TagsBuilder, { ...DEFAULT_PROPS, ...props });
}
describe('TagsBuilder', () => {
  describe('when mutable', () => {
    it('renders an input to add new tags', () => {
      const tags = Immutable.OrderedSet([new Tag({ name: 'test-tag' })]);
      renderTagBuilder({ tags: tags });
      expect(screen.getByText('#test-tag')).toBeInTheDocument();
      expect(screen.getByRole('textbox', { hidden: false })).toHaveAttribute('placeholder', '#add tag');
    });

    it('renders an existing tag', () => {
      const tags = [new Tag({ name: 'test-tag' })];
      const immutableTags = Immutable.OrderedSet(tags);
      renderTagBuilder({ tags: immutableTags });
      expect(screen.getByText(tags[0].get('name'), { exact: false })).toBeInTheDocument();
    });

    it('calls onRemove when remove button is clicked', () => {
      const tag = new Tag({ name: 'test-tag' });
      const tags = Immutable.OrderedSet([tag]);

      renderTagBuilder({ tags: tags });
      const closeButton = screen.getByRole('img', { hidden: true });
      fireEvent.click(closeButton);

      expect(DEFAULT_PROPS.onRemove).toHaveBeenCalledWith(tag);
    });
  });

  describe('when immutable', () => {
    test('new tag cannot be added', () => {
      renderTagBuilder({ immutable: true });

      expect(screen.queryByRole('textbox')).toBeNull();
    });

    test('existing tags cannot be removed', () => {
      const tags = Immutable.OrderedSet([new Tag({ name: 'test-tag' })]);
      renderTagBuilder({ tags: tags, immutable: true });
      expect(screen.getByText('#test-tag')).toBeInTheDocument();
      expect(screen.queryByRole('img', { hidden: true })).toBeNull();
    });

    it('renders a default placeholder', () => {
      renderTagBuilder({ immutable: true });
      expect(screen.getByText('Tags of selected cards will appear here.')).toBeInTheDocument();
    });

    it('renders a custom placeholder when provided', () => {
      const customPlaceholder = 'Custom placeholder text';
      renderTagBuilder({ placeholder: customPlaceholder, immutable: true });
      expect(screen.getByText(customPlaceholder)).toBeInTheDocument();
    });
  });

  describe('with InputAutoSize', () => {
    it('adds # prefix when typing without it', () => {
      renderTagBuilder();
      const input = screen.getByPlaceholderText('#add tag');

      fireEvent.change(input, { target: { value: 'test' } });
      expect(input).toHaveValue('#test');
    });

    it('prevents tags longer than 65 characters', () => {
      renderTagBuilder();
      const input = screen.getByPlaceholderText('#add tag');
      const longTag = '#' + 'a'.repeat(65);

      fireEvent.change(input, { target: { value: longTag } });
      expect(input).not.toHaveValue(longTag);
    });

    it('adds tag on enter key', () => {
      renderTagBuilder();
      const input = screen.getByPlaceholderText('#add tag');

      fireEvent.change(input, { target: { value: '#newtag' } });
      fireEvent.keyDown(input, { keyCode: 13 });

      expect(DEFAULT_PROPS.onAdd).toHaveBeenCalledWith(expect.any(Tag));
      expect(input).toHaveValue('');
    });

    it('prevents duplicate tags', () => {
      const existingTag = new Tag({ name: 'test-tag' });
      const tags = Immutable.OrderedSet([existingTag]);
      renderTagBuilder({ tags });
      const input = screen.getByPlaceholderText('#add tag');

      fireEvent.change(input, { target: { value: 'test-tag' } });
      fireEvent.keyDown(input, { keyCode: 13 });

      expect(DEFAULT_PROPS.onAdd).not.toHaveBeenCalled();
    });
  });

  describe('suggestions dropdown', () => {
    beforeEach(() => {
      vi.mocked(Request.get).mockReturnValue({
        end: () => Promise.resolve({ body: { tags: [{ name: 'suggestion' }] } }),
        abort: vi.fn(),
      } as any);
    });

    it('shows suggestions when typing', async () => {
      renderTagBuilder();
      const input = screen.getByPlaceholderText('#add tag');

      fireEvent.change(input, { target: { value: '#test' } });
      fireEvent.focus(input);

      const suggestion = await screen.findByText('#suggestion');
      expect(suggestion).toBeInTheDocument();
    });

    it('selects suggestion with arrow keys', async () => {
      renderTagBuilder();
      const input = screen.getByPlaceholderText('#add tag');

      fireEvent.change(input, { target: { value: '#test' } });
      fireEvent.focus(input);

      const suggestion = await screen.findByText('#suggestion');
      fireEvent.keyDown(input, { keyCode: 40 }); // Down arrow
      expect(suggestion).toHaveClass('is-selected');
    });
  });

  describe('user tags', () => {
    describe('when user is not subscribed', () => {
      describe('user has reached tag limit', () => {
        it('shows upgrade message', () => {
          const userTags = Immutable.OrderedSet(
            Array(FreeLimits.TAGS)
              .fill(null)
              .map((_, i) => new Tag({ name: `tag-${i}` })),
          );

          renderTagBuilder({ userTags, me: create(FakeUser, { subscribed: false }) });
          expect(screen.getByText(/You've reached the tag limit for Squire accounts/)).toBeInTheDocument();
          expect(screen.getByText('Upgrade')).toBeInTheDocument();
        });
      });
    });
  });
});
