import { Icon, IconifyIcon } from '@iconify/react';
import classNames from 'classnames';
import * as React from 'react';

interface IProps {
  icon: IconifyIcon;
  value: any;
  onChange: (evt: any) => void;
  children: JSX.Element;
  className: string;
  disabled?: boolean;
  error?: boolean;
}

export const IconSelect = (props: IProps) => {
  const disabled = props.disabled === undefined ? false : props.disabled;
  const selectClassName = classNames('select', {
    'select-error': props.error,
  });
  return (
    <div className={props.className}>
      <Icon
        height={'18px'}
        width={'18px'}
        icon={props.icon}
        color={props.error ? /*$dark-alert-color*/ '#c11f1f' : ''}
      />
      <select
        disabled={disabled}
        className={selectClassName}
        value={props.value}
        onChange={disabled ? undefined : (evt: any) => props.onChange(evt)}
        style={{ marginRight: '1rem' }}
      >
        {props.children}
      </select>
    </div>
  );
};
