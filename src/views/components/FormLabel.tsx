import * as React from 'react';

interface IProps {
  heading?: string;
  subheading?: string;
  addMargin?: boolean; // Used to configure no margin on checkbox form items
}

export class FormLabel extends React.Component<IProps> {
  /**
   * @override
   */
  render() {
    let bottomMargin = '0rem';
    if (this.props.addMargin !== false) {
      if (this.props.subheading) {
        if (this.props.heading) {
          // Displaying both heading and subheading looks best with a larger bottom margin
          bottomMargin = '1rem';
        } else {
          // Subheading by itself will typically be presented at the bottom of a form component
          bottomMargin = '0rem';
        }
      } else {
        // Regular margin
        bottomMargin = '0.5rem';
      }
    }

    // Subheading by itself will need some padding above it
    const topMargin = this.props.heading ? '0rem' : '0.5rem';
    return (
      <div className="form-label" style={{ marginBottom: bottomMargin, marginTop: topMargin }}>
        {this.props.heading ? <div className="form-label--heading">{this.props.heading}</div> : undefined}
        {this.props.subheading ? <div>{this.props.subheading}</div> : undefined}
      </div>
    );
  }
}
