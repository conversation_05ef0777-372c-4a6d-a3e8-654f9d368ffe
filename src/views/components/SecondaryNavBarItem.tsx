import classNames from 'classnames';
import * as React from 'react';
import history from '../../helpers/history';

interface IProps {
  disabled?: boolean;
  href?: string;
  selected?: boolean;
  secondaryNavButton?: boolean;
  onClick?: (evt: React.SyntheticEvent<HTMLElement>) => void;
}

interface IState {}

export class SecondaryNavBarItem extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  public render() {
    const className = classNames('secondary-nav-bar-item', {
      'is-disabled': this.props.disabled,
      'is-selected': this.props.selected,
      flex: this.props.secondaryNavButton,
    });
    const disabled = this.props.disabled === undefined ? false : this.props.disabled;
    return (
      <li className={className} onClick={disabled ? undefined : this.onClick.bind(this)}>
        {this.props.children}
      </li>
    );
  }

  private onClick(evt: React.SyntheticEvent<HTMLElement>) {
    if (this.props.onClick) {
      this.props.onClick(evt);
    } else {
      evt.preventDefault();
      this.props.href && history.push(this.props.href);
    }
  }
}

export default SecondaryNavBarItem;
