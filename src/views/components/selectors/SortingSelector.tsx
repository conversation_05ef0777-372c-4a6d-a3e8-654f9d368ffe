import sort from '@iconify/icons-ic/sort';
import * as React from 'react';
import { Grouping } from '../../../models/Grouping';
import { SortingGroupingHelper } from '../../../models/sorting/Helper';
import { Sorting, type SortingKey } from '../../../models/sorting/Sorting';
import { FormLabel } from '../FormLabel';
import { IconSelect } from '../IconSelect';

interface IProps {
  sorting: Sorting;
  grouping: Grouping;
  onChange: (sorting: Sorting) => void;
  disabled?: boolean;
}

export function SortingSelector(props: IProps) {
  return (
    <>
      <FormLabel heading="Sort" />
      <IconSelect
        className="icon-container--card-input"
        disabled={props.disabled === true}
        icon={sort}
        value={props.sorting}
        onChange={(evt) => {
          evt.preventDefault();
          props.onChange(evt.currentTarget.value as Sorting);
        }}
      >
        <>
          {Object.keys(Sorting).map((key) => {
            if (SortingGroupingHelper.validCombo(props.grouping, Sorting[key as SortingKey])) {
              return (
                <option key={key} value={Sorting[key as SortingKey]}>
                  {Sorting[key as SortingKey]}
                </option>
              );
            }

            return null;
          })}
        </>
      </IconSelect>
    </>
  );
}
