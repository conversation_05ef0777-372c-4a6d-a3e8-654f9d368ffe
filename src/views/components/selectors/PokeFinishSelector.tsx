import grade from '@iconify/icons-ic/grade';
import { Icon } from '@iconify/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { components, ControlProps, OptionTypeBase } from 'react-select';
import AsyncSelect from 'react-select/async';
import * as FinishActions from '../../../actions/FinishActions';
import * as HasDispatcher from '../../../containers/HasDispatcher';
import * as HasPokeFinishes from '../../../containers/HasPokeFinishes';
import { GroupValue } from '../../../models/CardInstances';
import { PokeFinish } from '../../../models/pokemon/PokeFinish';
import { LoadingIndicator, LoadingMessage, NoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';
import { FormLabel } from '../FormLabel';

interface IProps {
  finish?: GroupValue<PokeFinish>;
  onChange: (finish: PokeFinish) => void;
  disabled?: boolean;
  error?: boolean;
  hideTitle?: boolean;
}

interface IState {
  currentSelection?: GroupValue<PokeFinish>;
}

export class PokeFinishSuggestion implements OptionTypeBase {
  label: string;
  value: GroupValue<PokeFinish>;
  constructor(pokeFinish: GroupValue<PokeFinish>) {
    this['label'] = pokeFinish === 'Multiple' ? 'Multiple' : pokeFinish.get('name');
    this['value'] = pokeFinish;
  }

  formatOptionLabel = () => {
    return (
      <div className="text__suggestion">
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__value">{this['label']}</div>
        </div>
      </div>
    );
  };
}

const Control = ({ children, ...props }: ControlProps<PokeFinishSuggestion, false>) => {
  const { icon } = props.selectProps;

  return (
    <components.Control {...props}>
      <Icon height={'18px'} width={'18px'} icon={icon} />
      {children}
    </components.Control>
  );
};

export const PokeFinishSelector = HasPokeFinishes.Attach<IProps & HasDispatcher.IProps>(
  class extends React.Component<IProps & HasPokeFinishes.IProps & HasDispatcher.IProps, IState> {
    constructor(props: IProps & HasPokeFinishes.IProps & HasDispatcher.IProps) {
      super(props);
      this.state = {
        currentSelection: props.finish,
      };
    }

    componentDidMount(): void {
      FinishActions.loadPokeFinishes(this.props.dispatcher);
    }

    componentDidUpdate(
      prevProps: Readonly<IProps & HasPokeFinishes.IProps>,
      _prevState: Readonly<IState>,
      _snapshot?: any,
    ): void {
      if (this.props.finish != prevProps.finish) {
        this.setState({ currentSelection: this.props.finish });
      }
    }

    render() {
      const defaultFinish = this.props.pokeFinishes.sortBy((finish: PokeFinish) => finish.get('fallbackOrder')).first();

      let selection = undefined;

      if (this.state.currentSelection) {
        selection = new PokeFinishSuggestion(this.state.currentSelection);
      } else {
        selection = defaultFinish && new PokeFinishSuggestion(defaultFinish);
      }

      return (
        <>
          {!this.props.hideTitle && <FormLabel heading="Finish" />}
          <AsyncSelect
            defaultOptions={this.makeSuggestions(this.props.pokeFinishes)}
            icon={grade}
            components={{
              Control,
              NoOptionsMessage,
              LoadingMessage,
              LoadingIndicator,
            }}
            value={selection}
            className={'react-select-thin'}
            classNamePrefix="react-select-thin"
            formatOptionLabel={(option: PokeFinishSuggestion) => option.formatOptionLabel()}
            styles={styleOverride}
            placeholder={''}
            isDisabled={this.props.disabled}
            onChange={(option: PokeFinishSuggestion) => {
              const finish = option['value'] as PokeFinish;
              this.setState({ currentSelection: finish });
              this.props.onChange(finish);
            }}
            loadOptions={(query: string) => {
              return this.loadOptions(query).then((results: Immutable.OrderedSet<PokeFinish>) =>
                this.makeSuggestions(results),
              );
            }}
          />
        </>
      );
    }

    makeSuggestions(options: Immutable.OrderedSet<PokeFinish>) {
      return options
        .toList()
        .map((finish: PokeFinish) => new PokeFinishSuggestion(finish))
        .toJS();
    }

    loadOptions(query: string): Promise<Immutable.OrderedSet<PokeFinish>> {
      return new Promise((resolve, _reject) => {
        resolve(
          this.props.pokeFinishes
            .toList()
            .filter((finish: PokeFinish) => finish.get('name').toLowerCase().includes(query.toLowerCase()))
            .toOrderedSet(),
        );
      });
    }
  },
);
