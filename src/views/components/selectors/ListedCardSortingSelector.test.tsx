import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { ListedCardSorting, ListedCardSortingKey } from '../../../models/sorting/ListedCardSorting';
import { ListedCardSortingSelector } from './ListedCardSortingSelector';

type ListedCardSortingSelectorProps = React.ComponentProps<typeof ListedCardSortingSelector>;
const DEFAULT_PROPS: ListedCardSortingSelectorProps = {
  sorting: ListedCardSorting.NAME_ASC,
  onChange: vi.fn(),
  disabled: false,
};

function renderListedCardSortingSelector(props: Partial<ListedCardSortingSelectorProps> = {}) {
  return render(<ListedCardSortingSelector {...DEFAULT_PROPS} {...props} />);
}

describe('ListedCardSortingSelector', () => {
  describe('with FormLabel', () => {
    it('renders Form<PERSON>abel with correct heading', () => {
      renderListedCardSortingSelector();
      expect(screen.getByText('Sort', { selector: 'div.form-label--heading' })).toBeInTheDocument();
    });
  });

  describe('with IconSelect', () => {
    it('renders select and all sorting options', () => {
      renderListedCardSortingSelector();
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
      expect(select).toHaveValue(DEFAULT_PROPS.sorting);
      Object.keys(ListedCardSorting).forEach((key) => {
        expect(
          screen.getByRole('option', { name: ListedCardSorting[key as ListedCardSortingKey] }),
        ).toBeInTheDocument();
      });
    });

    test('trigger onChange prop', () => {
      renderListedCardSortingSelector();
      const select = screen.getByRole('combobox');
      fireEvent.change(select, { target: { value: ListedCardSorting.DATE_ADDED_DESC } });
      expect(DEFAULT_PROPS.onChange).toHaveBeenCalledWith(ListedCardSorting.DATE_ADDED_DESC);
    });

    it('disables the select when disabled prop is true', () => {
      renderListedCardSortingSelector({ disabled: true });
      const select = screen.getByRole('combobox');
      expect(select).toBeDisabled();
    });
  });
});
