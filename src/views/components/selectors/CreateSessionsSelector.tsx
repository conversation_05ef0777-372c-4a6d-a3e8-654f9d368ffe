import layers from '@iconify/icons-ic/baseline-layers';
import * as React from 'react';
import { CreateSessions, type CreateSessionsKey } from '../../../models/CardBot';
import { FormLabel } from '../FormLabel';
import { IconSelect } from '../IconSelect';

interface IProps {
  selection: CreateSessions;
  onChange: (selection: CreateSessions) => void;
  disabled?: boolean;
}

export function CreateSessionsSelector(props: IProps) {
  function displayName(selection: CreateSessions): string {
    switch (selection) {
      case CreateSessions.ALL:
        return 'All Cards';
      case CreateSessions.ACCEPTED:
        return 'Accepted Cards';
      case CreateSessions.REJECTED:
        return 'Rejected Cards';
      case CreateSessions.NONE:
        return 'None';
    }
  }

  return (
    <>
      <FormLabel heading="Create Sessions" />
      <IconSelect
        className={'icon-container--card-input'}
        disabled={props.disabled === true}
        icon={layers}
        value={props.selection}
        onChange={(evt) => {
          evt.preventDefault();
          props.onChange(evt.currentTarget.value as CreateSessions);
        }}
      >
        <>
          <option disabled>Create Sessions</option>
          {Object.keys(CreateSessions).map((key) => (
            <option key={key} value={CreateSessions[key as CreateSessionsKey]}>
              {displayName(CreateSessions[key as CreateSessionsKey])}
            </option>
          ))}
        </>
      </IconSelect>
      <FormLabel subheading="Select which cards should be staged" />
    </>
  );
}
