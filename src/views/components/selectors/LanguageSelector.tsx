import language_icon from '@iconify/icons-ic/baseline-language';
import * as Immutable from 'immutable';
import * as React from 'react';
import { GroupValue } from '../../../models/CardInstances';
import { Language, LanguageName } from '../../../models/Language';
import { FormLabel } from '../FormLabel';
import { IconSelect } from '../IconSelect';

interface IProps {
  language: GroupValue<Language>;
  options: Immutable.OrderedSet<Language>;
  onChange: (language: Language) => void;
  disabled?: boolean;
  error?: boolean;
  hideTitle?: boolean;
}

export function LanguageSelector(props: IProps) {
  return (
    <>
      {!props.hideTitle && <FormLabel heading="Language" />}
      <IconSelect
        className={'icon-container--card-input'}
        disabled={props.disabled === true}
        icon={language_icon}
        value={props.language}
        error={props.error}
        onChange={(evt) => {
          evt.preventDefault();
          props.onChange(evt.currentTarget.value as Language);
        }}
      >
        <>
          <option disabled>Language</option>
          {props.language === 'Multiple' ? (
            <option disabled value={'Multiple'}>
              Multiple
            </option>
          ) : null}
          {props.options.map((language: Language) => (
            <option key={language} value={language}>
              {LanguageName(language)}
            </option>
          ))}
        </>
      </IconSelect>
    </>
  );
}
