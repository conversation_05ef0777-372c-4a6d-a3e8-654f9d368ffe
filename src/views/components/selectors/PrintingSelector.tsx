import refresh from '@iconify/icons-ic/refresh';
import * as React from 'react';
import Select, { components, OptionsType } from 'react-select';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { AnyCard } from '../../../models/AnyCard';
import { Game } from '../../../models/Game';
import { PricingSource } from '../../../models/PricingSource';
import { AnyPrinting } from '../../../models/Printing';
import { LoadingMessage, NoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';
import { SecondaryButton } from '../buttons/SecondaryButton';
import { LorcanaCardSearcher } from '../cardSearcher/LorcanaCardSearcher';
import { MTGCardSearcher } from '../cardSearcher/MTGCardSearcher';
import { PokeCardSearcher } from '../cardSearcher/PokeCardSearcher';
import { YugiCardSearcher } from '../cardSearcher/YugiCardSearcher';
import { PrintingSuggestion } from '../PrintingSuggestionItem';

interface IProps {
  dispatcher: Dispatcher;
  pricingSource: PricingSource;
  currentPrinting: AnyPrinting;
  options: OptionsType<PrintingSuggestion>;
  game: Game;
  loadOptions: () => void;
  loading: boolean;
  onChange: (printing: AnyPrinting) => void;
  onChangeCard: (card: AnyCard) => void;
  disabled?: boolean;
  hideTitle?: boolean;
  changeCardDisabled?: boolean;
}

enum PrintingDisplayMode {
  PRINTING,
  CARD,
}

const SingleValue = (props: any) => <components.SingleValue {...props}>{props.data.label}</components.SingleValue>;

export function PrintingSelector(props: IProps) {
  const [mode, changeMode] = React.useState<PrintingDisplayMode>(PrintingDisplayMode.PRINTING);
  const [selection, changeSelection] = React.useState<AnyPrinting>(props.currentPrinting);

  React.useEffect(() => {
    changeSelection(props.currentPrinting);
  }, [props.currentPrinting]);

  let title = '';
  let action = '';
  let nextMode = PrintingDisplayMode.PRINTING;

  let searcher: JSX.Element | null = null;

  switch (mode) {
    case PrintingDisplayMode.PRINTING:
      title = 'Set';
      action = 'Change Card';
      nextMode = PrintingDisplayMode.CARD;
      break;
    case PrintingDisplayMode.CARD:
      title = 'Card';
      action = 'Change Printing';
      nextMode = PrintingDisplayMode.PRINTING;
      switch (props.game) {
        case Game.MTG:
          searcher = (
            <MTGCardSearcher
              dispatcher={props.dispatcher}
              chooseSuggestion={(card?: AnyCard) => card && props.onChangeCard(card)}
            />
          );
          break;
        case Game.POKEMON:
          searcher = (
            <PokeCardSearcher
              dispatcher={props.dispatcher}
              chooseSuggestion={(card?: AnyCard) => card && props.onChangeCard(card)}
            />
          );
          break;
        case Game.YUGIOH:
          searcher = (
            <YugiCardSearcher
              dispatcher={props.dispatcher}
              chooseSuggestion={(card?: AnyCard) => card && props.onChangeCard(card)}
            />
          );
          break;
        case Game.LORCANA:
          searcher = (
            <LorcanaCardSearcher
              dispatcher={props.dispatcher}
              chooseSuggestion={(card?: AnyCard) => card && props.onChangeCard(card)}
            />
          );
          break;
      }
      break;
  }

  return (
    <>
      <div className="flex">
        {/* TODO: Replace with FormLabel component */}
        {!props.hideTitle && <div className="card-panel-info-heading grow-xs-1">{title}</div>}
        {!props.changeCardDisabled && (
          <div style={{ justifyContent: 'end' }}>
            <SecondaryButton icon={refresh} text={action} onClick={() => changeMode(nextMode)} />
          </div>
        )}
      </div>
      {mode === PrintingDisplayMode.PRINTING && (
        <Select
          components={{
            SingleValue,
            NoOptionsMessage,
            LoadingMessage,
          }}
          formatOptionLabel={PrintingSuggestion.formatOptionLabel}
          styles={styleOverride}
          className={'react-select-printings'}
          classNamePrefix="react-select-printings"
          options={props.options}
          isLoading={props.loading}
          onChange={(suggestion: PrintingSuggestion) => {
            changeSelection(suggestion.value);
            props.onChange(suggestion.value);
          }}
          onFocus={props.loadOptions.bind(this)}
          value={new PrintingSuggestion(Game.MTG, selection)}
        />
      )}
      {mode === PrintingDisplayMode.CARD && searcher}
    </>
  );
}
