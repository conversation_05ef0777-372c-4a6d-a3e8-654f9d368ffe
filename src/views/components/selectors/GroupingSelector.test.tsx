import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { TextFormat } from '../../../helpers/fmt';
import { Grouping, GroupingKey } from '../../../models/Grouping';
import { GroupingSelector } from './GroupingSelector';

type GroupingSelectorProps = React.ComponentProps<typeof GroupingSelector>;
const DEFAULT_PROPS: GroupingSelectorProps = {
  grouping: Grouping.NONE,
  onChange: vi.fn(),
  disabled: false,
};

function renderGroupingSelector(props: Partial<GroupingSelectorProps> = {}) {
  return render(<GroupingSelector {...DEFAULT_PROPS} {...props} />);
}

describe('GroupingSelector', () => {
  describe('with FormLabel', () => {
    it('renders heading', () => {
      renderGroupingSelector();
      expect(screen.getByText('Group', { selector: 'div.form-label--heading' })).toBeInTheDocument();
    });
  });

  describe('with IconSelect', () => {
    it('renders select and all grouping options with capitalized text', () => {
      renderGroupingSelector();
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
      expect(select).toHaveValue(DEFAULT_PROPS.grouping);
      Object.keys(Grouping).forEach((groupKey) => {
        const label = TextFormat.capitalize(Grouping[groupKey as GroupingKey]);
        expect(screen.getByRole('option', { name: label })).toBeInTheDocument();
      });
    });

    test('trigger onChange prop', () => {
      renderGroupingSelector();
      const select = screen.getByRole('combobox');
      fireEvent.change(select, { target: { value: Grouping.PRINTING } });
      expect(DEFAULT_PROPS.onChange).toHaveBeenCalledWith(Grouping.PRINTING);
    });

    it('disables select when disabled prop is true', () => {
      renderGroupingSelector({ disabled: true });
      expect(screen.getByRole('combobox')).toBeDisabled();
    });
  });
});
