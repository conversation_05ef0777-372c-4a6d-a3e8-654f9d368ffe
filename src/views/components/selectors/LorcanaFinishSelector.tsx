import grade from '@iconify/icons-ic/grade';
import { Icon } from '@iconify/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { components, ControlProps, OptionTypeBase } from 'react-select';
import AsyncSelect from 'react-select/async';
import * as FinishActions from '../../../actions/FinishActions';
import * as HasDispatcher from '../../../containers/HasDispatcher';
import * as <PERSON><PERSON>or<PERSON>aFinishes from '../../../containers/HasLorcanaFinishes';
import { GroupValue } from '../../../models/CardInstances';
import { LorcanaFinish } from '../../../models/lorcana/LorcanaFinish';
import { LoadingIndicator, LoadingMessage, NoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';
import { FormLabel } from '../FormLabel';

interface IProps {
  finish?: GroupValue<LorcanaFinish>;
  onChange: (finish: LorcanaFinish) => void;
  disabled?: boolean;
  error?: boolean;
  hideTitle?: boolean;
}

interface IState {
  currentSelection?: GroupValue<LorcanaFinish>;
}

export class LorcanaFinishSuggestion implements OptionTypeBase {
  label: string;
  value: GroupValue<LorcanaFinish>;
  constructor(lorcanaFinish: GroupValue<LorcanaFinish>) {
    this['label'] = lorcanaFinish === 'Multiple' ? 'Multiple' : lorcanaFinish.get('name');
    this['value'] = lorcanaFinish;
  }

  formatOptionLabel = () => {
    return (
      <div className="text__suggestion">
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__value">{this['label']}</div>
        </div>
      </div>
    );
  };
}

const Control = ({ children, ...props }: ControlProps<LorcanaFinishSuggestion, false>) => {
  const { icon } = props.selectProps;

  return (
    <components.Control {...props}>
      <Icon height={'18px'} width={'18px'} icon={icon} />
      {children}
    </components.Control>
  );
};

export const LorcanaFinishSelector = HasLorcanaFinishes.Attach<IProps & HasDispatcher.IProps>(
  class extends React.Component<IProps & HasLorcanaFinishes.IProps & HasDispatcher.IProps, IState> {
    constructor(props: IProps & HasLorcanaFinishes.IProps & HasDispatcher.IProps) {
      super(props);
      this.state = {
        currentSelection: props.finish,
      };
    }

    componentDidMount(): void {
      FinishActions.loadLorcanaFinishes(this.props.dispatcher);
    }

    componentDidUpdate(
      prevProps: Readonly<IProps & HasLorcanaFinishes.IProps>,
      _prevState: Readonly<IState>,
      _snapshot?: any,
    ): void {
      if (this.props.finish != prevProps.finish) {
        this.setState({ currentSelection: this.props.finish });
      }
    }

    render() {
      const defaultFinish = this.props.lorcanaFinishes
        .sortBy((finish: LorcanaFinish) => finish.get('fallbackOrder'))
        .first();

      let selection = undefined;

      if (this.state.currentSelection) {
        selection = new LorcanaFinishSuggestion(this.state.currentSelection);
      } else {
        selection = defaultFinish && new LorcanaFinishSuggestion(defaultFinish);
      }

      return (
        <>
          {!this.props.hideTitle && <FormLabel heading="Finish" />}
          <AsyncSelect
            defaultOptions={this.makeSuggestions(this.props.lorcanaFinishes)}
            icon={grade}
            components={{
              Control,
              NoOptionsMessage,
              LoadingMessage,
              LoadingIndicator,
            }}
            value={selection}
            className={'react-select-thin'}
            classNamePrefix="react-select-thin"
            formatOptionLabel={(option: LorcanaFinishSuggestion) => option.formatOptionLabel()}
            styles={styleOverride}
            placeholder={''}
            isDisabled={this.props.disabled}
            onChange={(option: LorcanaFinishSuggestion) => {
              const finish = option['value'] as LorcanaFinish;
              this.setState({ currentSelection: finish });
              this.props.onChange(finish);
            }}
            loadOptions={(query: string) => {
              return this.loadOptions(query).then((results: Immutable.OrderedSet<LorcanaFinish>) =>
                this.makeSuggestions(results),
              );
            }}
          />
        </>
      );
    }

    makeSuggestions(options: Immutable.OrderedSet<LorcanaFinish>) {
      return options
        .toList()
        .map((finish: LorcanaFinish) => new LorcanaFinishSuggestion(finish))
        .toJS();
    }

    loadOptions(query: string): Promise<Immutable.OrderedSet<LorcanaFinish>> {
      return new Promise((resolve, _reject) => {
        resolve(
          this.props.lorcanaFinishes
            .toList()
            .filter((finish: LorcanaFinish) => finish.get('name').toLowerCase().includes(query.toLowerCase()))
            .toOrderedSet(),
        );
      });
    }
  },
);
