import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createFakePokeFinishes } from '../../../../tests/fake/FakeFinishData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as FinishActions from '../../../actions/FinishActions';
import * as PokeFinishAPI from '../../../api/pokemon/PokeFinish';
import { PokeFinishSelector } from './PokeFinishSelector';

const mockFinishes = createFakePokeFinishes(10);

type PokeFinishSelectorProps = React.ComponentProps<typeof PokeFinishSelector>;
const DEFAULT_PROPS: Omit<PokeFinishSelectorProps, 'dispatcher'> = {
  finish: mockFinishes[0],
  onChange: vi.fn(),
  disabled: false,
  hideTitle: false,
  // This prop is defined in the component interface but not currently used in the component.
  // If it's implemented in the future, this test should be updated to properly test its functionality.
  error: false,
};
const renderPokeFinishSelector = (props: Partial<PokeFinishSelectorProps> = {}) => {
  return renderWithDispatcher(PokeFinishSelector, { ...DEFAULT_PROPS, ...props });
};

describe('PokeFinishSelector', () => {
  beforeEach(() => {
    vi.spyOn(PokeFinishAPI, 'load').mockResolvedValue(mockFinishes);
    vi.spyOn(FinishActions, 'loadPokeFinishes');
  });

  describe('with FormLabel', () => {
    it('renders FormLabel with correct heading', () => {
      renderPokeFinishSelector();
      expect(screen.getByText('Finish', { selector: 'div.form-label--heading' })).toBeInTheDocument();
    });

    it('hides FormLabel when hideTitle is true', () => {
      renderPokeFinishSelector({ hideTitle: true });
      expect(screen.queryByText('Finish', { selector: 'div.form-label--heading' })).not.toBeInTheDocument();
    });
  });

  describe('with AsyncSelect', () => {
    it('renders with first pokeFinishes when no finish is provided', async () => {
      const { dispatcher } = renderPokeFinishSelector({
        finish: undefined,
      });
      await waitFor(() => {
        expect(FinishActions.loadPokeFinishes).toHaveBeenCalledWith(dispatcher);
      });
      expect(screen.getByText(mockFinishes[0].get('name'))).toBeInTheDocument();
    });
    it('updates selected finish when finish prop changes', () => {
      const { rerenderWithDispatcher } = renderPokeFinishSelector();
      expect(screen.getByText(mockFinishes[0].get('name'))).toBeInTheDocument();

      rerenderWithDispatcher({
        finish: mockFinishes[1],
      });
      expect(screen.getByText(mockFinishes[1].get('name'))).toBeInTheDocument();
    });

    it('renders with "Multiple" when finish prop is "Multiple"', () => {
      renderPokeFinishSelector({ finish: 'Multiple' });
      expect(screen.getByText('Multiple')).toBeInTheDocument();
    });
    it('disables the AsyncSelect when disabled prop is true', () => {
      renderPokeFinishSelector({ disabled: true });
      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });
    it('calls onChange when select change', async () => {
      const { container, dispatcher } = renderPokeFinishSelector();
      await waitFor(() => {
        expect(FinishActions.loadPokeFinishes).toHaveBeenCalledWith(dispatcher);
      });
      const input = screen.getByRole('textbox');
      const targetSelectedOption = mockFinishes[3];
      const query = targetSelectedOption.get('name').slice(0, 10);
      fireEvent.change(input, { target: { value: query } });
      await waitFor(() => {
        const suggestion = container.querySelector('.react-select-thin__option') as HTMLElement;
        expect(suggestion).toBeInTheDocument();
        fireEvent.click(suggestion);
        expect(DEFAULT_PROPS.onChange).toHaveBeenCalledExactlyOnceWith(targetSelectedOption);
      });
    });
  });
});
