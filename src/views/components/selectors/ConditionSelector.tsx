import image_search from '@iconify/icons-ic/baseline-image-search';
import * as React from 'react';
import { GroupValue } from '../../../models/CardInstances';
import { Condition, type ConditionKey } from '../../../models/Condition';
import { FormLabel } from '../FormLabel';
import { IconSelect } from '../IconSelect';

interface IProps {
  condition: GroupValue<Condition>;
  onChange: (condition: Condition) => void;
  disabled?: boolean;
  hideTitle?: boolean;
}

export function ConditionSelector(props: IProps) {
  return (
    <>
      {!props.hideTitle && <FormLabel heading="Condition" />}
      <IconSelect
        className={'icon-container--card-input'}
        disabled={props.disabled === true}
        icon={image_search}
        value={props.condition}
        onChange={(evt) => {
          evt.preventDefault();
          props.onChange(evt.currentTarget.value as Condition);
        }}
      >
        <>
          <option disabled>Condition</option>
          {props.condition === 'Multiple' ? (
            <option disabled value={'Multiple'}>
              Multiple
            </option>
          ) : null}
          {Object.keys(Condition).map((key) => (
            <option key={key} value={Condition[key as ConditionKey]}>
              {Condition[key as ConditionKey]}
            </option>
          ))}
        </>
      </IconSelect>
    </>
  );
}
