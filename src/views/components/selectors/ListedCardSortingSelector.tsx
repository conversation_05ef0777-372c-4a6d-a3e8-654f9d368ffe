import sort from '@iconify/icons-ic/sort';
import * as React from 'react';
import { ListedCardSorting, type ListedCardSortingKey } from '../../../models/sorting/ListedCardSorting';
import { FormLabel } from '../FormLabel';
import { IconSelect } from '../IconSelect';

interface IProps {
  sorting: ListedCardSorting;
  onChange: (sorting: ListedCardSorting) => void;
  disabled?: boolean;
}

export function ListedCardSortingSelector(props: IProps) {
  return (
    <>
      <FormLabel heading="Sort" />
      <IconSelect
        className="icon-container--card-input"
        disabled={props.disabled === true}
        icon={sort}
        value={props.sorting}
        onChange={(evt) => {
          evt.preventDefault();
          props.onChange(evt.currentTarget.value as ListedCardSorting);
        }}
      >
        <>
          {Object.keys(ListedCardSorting).map((key) => {
            return (
              <option key={key} value={ListedCardSorting[key as ListedCardSortingKey]}>
                {ListedCardSorting[key as ListedCardSortingKey]}
              </option>
            );
          })}
        </>
      </IconSelect>
    </>
  );
}
