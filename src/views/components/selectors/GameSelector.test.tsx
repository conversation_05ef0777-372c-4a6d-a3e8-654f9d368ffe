import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { Game, GameKey, gameName, inBeta } from '../../../models/Game';
import { GameSelector } from './GameSelector';

type GameSelectorProps = React.ComponentProps<typeof GameSelector>;
const DEFAULT_PROPS: GameSelectorProps = {
  game: Game.MTG,
  onChange: vi.fn(),
  disabled: false,
};

function renderGameSelector(props: Partial<GameSelectorProps> = {}) {
  return render(<GameSelector {...DEFAULT_PROPS} {...props} />);
}

describe('GameSelector', () => {
  describe('with FormLabel', () => {
    it('renders heading', () => {
      renderGameSelector();
      expect(screen.getByText('Game', { selector: 'div.form-label--heading' })).toBeInTheDocument();
    });
  });

  describe('with IconSelect', () => {
    it('renders select and all game options with correct label', () => {
      renderGameSelector();
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
      expect(select).toHaveValue(DEFAULT_PROPS.game);
      expect(screen.getByRole('option', { name: 'Game' })).toBeDisabled();
      Object.keys(Game).forEach((gameKey) => {
        const game = Game[gameKey as GameKey];
        let label = gameName(game);
        if (inBeta(game)) label += ' [BETA]';
        expect(screen.getByRole('option', { name: label })).toBeInTheDocument();
      });
    });

    test('trigger onChange prop', () => {
      renderGameSelector();
      const select = screen.getByRole('combobox');
      fireEvent.change(select, { target: { value: Game.POKEMON } });
      expect(DEFAULT_PROPS.onChange).toHaveBeenCalledWith(Game.POKEMON);
    });

    it('disables select when disabled prop is true', () => {
      renderGameSelector({ disabled: true });
      expect(screen.getByRole('combobox')).toBeDisabled();
    });
  });
});
