import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { Grouping } from '../../../models/Grouping';
import { SortingGroupingHelper } from '../../../models/sorting/Helper';
import { Sorting, SortingKey } from '../../../models/sorting/Sorting';
import { SortingSelector } from './SortingSelector';

type SortingSelectorProps = React.ComponentProps<typeof SortingSelector>;
const DEFAULT_PROPS: SortingSelectorProps = {
  sorting: Sorting.NAME_ASC,
  grouping: Grouping.NONE,
  onChange: vi.fn(),
  disabled: false,
};

function renderSortingSelector(props: Partial<SortingSelectorProps> = {}) {
  return render(<SortingSelector {...DEFAULT_PROPS} {...props} />);
}

describe('SortingSelector', () => {
  describe('with FormLabel', () => {
    it('renders FormLabel with correct heading', () => {
      renderSortingSelector();
      expect(screen.getByText('Sort', { selector: 'div.form-label--heading' })).toBeInTheDocument();
    });
  });

  describe('with IconSelect', () => {
    it('renders select and all valid sorting options', () => {
      renderSortingSelector();
      Object.keys(Sorting).forEach((key) => {
        const sortingValue = Sorting[key as SortingKey];
        if (SortingGroupingHelper.validCombo(DEFAULT_PROPS.grouping, sortingValue)) {
          expect(screen.getByRole('option', { name: sortingValue })).toBeInTheDocument();
        } else {
          expect(screen.queryByRole('option', { name: sortingValue })).not.toBeInTheDocument();
        }
      });
    });

    test('trigger onChange prop', () => {
      renderSortingSelector();
      const select = screen.getByRole('combobox');
      fireEvent.change(select, { target: { value: Sorting.PRICE_DESC } });
      expect(DEFAULT_PROPS.onChange).toHaveBeenCalledWith(Sorting.PRICE_DESC);
    });

    it('disables the select when disabled prop is true', () => {
      renderSortingSelector({ disabled: true });
      const select = screen.getByRole('combobox');
      expect(select).toBeDisabled();
    });
  });
});
