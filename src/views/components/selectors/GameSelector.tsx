import category from '@iconify/icons-ic/baseline-category';
import * as React from 'react';
import { Game, gameName, inBeta, isDisabled, type GameKey } from '../../../models/Game';
import { FormLabel } from '../FormLabel';
import { IconSelect } from '../IconSelect';

interface IProps {
  game: Game;
  onChange: (game: Game) => void;
  disabled?: boolean;
}

export function GameSelector(props: IProps) {
  function displayName(game: Game): string {
    let name = gameName(game);
    if (inBeta(game)) name += ' [BETA]';
    return name;
  }
  return (
    <>
      <FormLabel heading="Game" />
      <IconSelect
        className={'icon-container--card-input'}
        disabled={props.disabled === true}
        icon={category}
        value={props.game}
        onChange={(evt) => {
          evt.preventDefault();
          props.onChange(evt.currentTarget.value as Game);
        }}
      >
        <>
          <option disabled>Game</option>
          {Object.keys(Game)
            .filter((key) => !isDisabled(Game[key as <PERSON>Key]))
            .map((key) => (
              <option key={key} value={Game[key as GameKey]}>
                {displayName(Game[key as GameKey])}
              </option>
            ))}
        </>
      </IconSelect>
    </>
  );
}
