import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import {
  createFakeLorcanaSuggestedCard,
  createFakePokeSuggestedCard,
  createFakeSuggestedCard,
  createFakeYugiSuggestedCardSet,
  FakePrinting,
} from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as CardsAPI from '../../../api/Cards';
import * as LorcanaCardsAPI from '../../../api/lorcana/LorcanaCards';
import * as PokeCardsAPI from '../../../api/pokemon/PokeCards';
import * as YugiCardsAPI from '../../../api/yugioh/YugiCards';
import { AnyCard } from '../../../models/AnyCard';
import { Card } from '../../../models/Cards';
import { Game } from '../../../models/Game';
import { PricingSource } from '../../../models/PricingSource';
import { YugiCardSet } from '../../../models/yugioh/YugiCards';
import { PrintingSuggestion } from '../PrintingSuggestionItem';
import { PrintingSelector } from './PrintingSelector';

const createFakePrintingSuggestion = (count: number) => {
  return Array.from({ length: count }, () => {
    return new PrintingSuggestion(Game.MTG, create(FakePrinting));
  });
};

const mockPrintingSuggestion = createFakePrintingSuggestion(10);

type PrintingSelectorProps = React.ComponentProps<typeof PrintingSelector>;

const DEFAULT_PROPS: Omit<PrintingSelectorProps, 'dispatcher'> = {
  pricingSource: PricingSource.CARD_MARKET,
  currentPrinting: mockPrintingSuggestion[0].value,
  options: mockPrintingSuggestion,
  game: Game.MTG,
  loadOptions: vi.fn(),
  loading: false,
  onChange: vi.fn(),
  onChangeCard: vi.fn(),
  hideTitle: false,
  changeCardDisabled: false,
  // This prop is defined in the component interface but not currently used in the component.
  // If it's implemented in the future, this test should be updated to properly test its functionality.
  disabled: false,
};

export const searchOnCardSearchSuggestion = (targetSelectedOption: AnyCard) => {
  const changeButton = screen.getByText('Change Card');
  fireEvent.click(changeButton);
  const input = screen.getByRole('textbox');
  const searchQuery = (
    (targetSelectedOption as YugiCardSet)?.get('card')?.get('name') || (targetSelectedOption as Card).get('name')
  ).slice(0, 5);
  fireEvent.change(input, { target: { value: searchQuery } });

  return searchQuery;
};

export const selectCardSuggestion = async (container: HTMLElement) => {
  await waitFor(() => {
    const suggestion = container.querySelector('.react-select-cards__option') as HTMLElement;
    expect(suggestion).toBeInTheDocument();
    fireEvent.click(suggestion);
  });
};
const mockApiSearch = (apiModule: any, mockData: AnyCard[]) => {
  const thenMock = vi.fn().mockImplementation((onFulfilled) => {
    onFulfilled(Immutable.List(mockData));
    return Promise.resolve();
  });

  vi.spyOn(apiModule, 'search').mockReturnValue({
    request: vi.fn().mockReturnThis(),
    promise: { then: thenMock },
  } as any);

  return thenMock;
};
function renderPrintingSelector(props: Partial<PrintingSelectorProps> = {}) {
  return renderWithDispatcher(PrintingSelector, { ...DEFAULT_PROPS, ...props });
}

describe('PrintingSelector', () => {
  describe('component render', () => {
    it('renders title when hideTitle is false', () => {
      renderPrintingSelector();
      expect(screen.getByText('Set')).toBeInTheDocument();
    });

    it('hides title when hideTitle is true', () => {
      renderPrintingSelector({ hideTitle: true });
      expect(screen.queryByText('Set')).not.toBeInTheDocument();
    });

    it('does not render Change Card button when changeCardDisabled is true', () => {
      renderPrintingSelector({ changeCardDisabled: true });
      expect(screen.queryByText('Change Card')).not.toBeInTheDocument();
    });
  });

  describe('with Select component in PRINTING mode', () => {
    it('calls loadOptions when Select is focused', () => {
      renderPrintingSelector();
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      expect(DEFAULT_PROPS.loadOptions).toHaveBeenCalled();
    });

    it('calls onChange when a printing is selected', () => {
      const { container } = renderPrintingSelector();
      const targetSelectedOption = mockPrintingSuggestion[3];
      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: targetSelectedOption.label.slice(0, 10) } });
      const suggestion = container.querySelector('.react-select__suggestion') as HTMLElement;
      fireEvent.click(suggestion);
      expect(DEFAULT_PROPS.onChange).toHaveBeenCalledExactlyOnceWith(targetSelectedOption.value);
    });
  });

  describe('mode switching', () => {
    it('switches to CARD mode when Change Card button is clicked', () => {
      renderPrintingSelector();
      const changeButton = screen.getByText('Change Card');
      fireEvent.click(changeButton);
      expect(screen.getByText('Card')).toBeInTheDocument();
      expect(screen.getByText('Change Printing')).toBeInTheDocument();
    });

    test('calls onChangeCard with MTG game', async () => {
      const mockSearchSuggestMTGCards = createFakeSuggestedCard(10);
      const targetSelectedOption = mockSearchSuggestMTGCards[0];
      const { container } = renderPrintingSelector();
      const thenSearchMTGCardMock = mockApiSearch(CardsAPI, mockSearchSuggestMTGCards);
      const searchQuery = searchOnCardSearchSuggestion(targetSelectedOption);
      await waitFor(() => {
        expect(CardsAPI.search).toHaveBeenCalledWith(searchQuery, Immutable.Set());
        expect(thenSearchMTGCardMock).toHaveBeenCalled();
      });
      await selectCardSuggestion(container);
      expect(DEFAULT_PROPS.onChangeCard).toHaveBeenCalledExactlyOnceWith(targetSelectedOption);
    });

    test('calls onChangeCard with POKEMON game', async () => {
      const mockSearchSuggestPokeCards = createFakePokeSuggestedCard(10);
      const targetSelectedOption = mockSearchSuggestPokeCards[0];

      const { container } = renderPrintingSelector({ game: Game.POKEMON });
      const thenSearchPokeCardMock = mockApiSearch(PokeCardsAPI, mockSearchSuggestPokeCards);
      const searchQuery = searchOnCardSearchSuggestion(targetSelectedOption);
      await waitFor(() => {
        expect(PokeCardsAPI.search).toHaveBeenCalledWith(searchQuery, Immutable.Set());
        expect(thenSearchPokeCardMock).toHaveBeenCalled();
      });
      await selectCardSuggestion(container);
      expect(DEFAULT_PROPS.onChangeCard).toHaveBeenCalledExactlyOnceWith(targetSelectedOption);
    });

    test('calls onChangeCard with YUGIOH game', async () => {
      const mockSearchSuggestYugiCards = createFakeYugiSuggestedCardSet(10);
      const targetSelectedOption = mockSearchSuggestYugiCards[0];
      const { container } = renderPrintingSelector({ game: Game.YUGIOH });
      const thenSearchYugiCardMock = mockApiSearch(YugiCardsAPI, mockSearchSuggestYugiCards);

      const searchQuery = searchOnCardSearchSuggestion(targetSelectedOption);
      await waitFor(() => {
        expect(YugiCardsAPI.search).toHaveBeenCalledWith(searchQuery, Immutable.Set());
        expect(thenSearchYugiCardMock).toHaveBeenCalled();
      });
      await selectCardSuggestion(container);
      expect(DEFAULT_PROPS.onChangeCard).toHaveBeenCalledExactlyOnceWith(targetSelectedOption);
    });

    test('calls onChangeCard with LORCANA game', async () => {
      const mockSearchSuggestLorcanaCards = createFakeLorcanaSuggestedCard(10);
      const targetSelectedOption = mockSearchSuggestLorcanaCards[0];
      const { container } = renderPrintingSelector({ game: Game.LORCANA });
      const thenSearchLorcanaCardMock = mockApiSearch(LorcanaCardsAPI, mockSearchSuggestLorcanaCards);

      const searchQuery = searchOnCardSearchSuggestion(targetSelectedOption);
      await waitFor(() => {
        expect(LorcanaCardsAPI.search).toHaveBeenCalledWith(searchQuery, Immutable.Set());
        expect(thenSearchLorcanaCardMock).toHaveBeenCalled();
      });
      await selectCardSuggestion(container);
      expect(DEFAULT_PROPS.onChangeCard).toHaveBeenCalledExactlyOnceWith(targetSelectedOption);
    });

    it('switches back to PRINTING mode when Change Printing button is clicked', () => {
      const { container } = renderPrintingSelector();
      const changeCardButton = screen.getByText('Change Card');
      fireEvent.click(changeCardButton);
      const changePrintingButton = screen.getByText('Change Printing');
      fireEvent.click(changePrintingButton);
      expect(screen.getByText('Set')).toBeInTheDocument();
      expect(screen.getByText('Change Card')).toBeInTheDocument();
      expect(container.querySelector('.react-select-printings')).toBeInTheDocument();
    });
  });
});
