import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createFakeLorcanaFinishes } from '../../../../tests/fake/FakeFinishData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as FinishActions from '../../../actions/FinishActions';
import * as LorcanaFinishAPI from '../../../api/lorcana/LorcanaFinish';
import * as HasLorcanaFinishes from '../../../containers/HasLorcanaFinishes';
import { LorcanaFinishSelector } from './LorcanaFinishSelector';

const mockFinishes = createFakeLorcanaFinishes(10);

type LorcanaFinishSelectorProps = React.ComponentProps<typeof LorcanaFinishSelector> & HasLorcanaFinishes.IProps;

const DEFAULT_PROPS: Omit<LorcanaFinishSelectorProps, 'dispatcher'> = {
  finish: mockFinishes[0],
  onChange: vi.fn(),
  disabled: false,
  hideTitle: false,
  lorcanaFinishes: Immutable.OrderedSet(mockFinishes),
  // This prop is defined in the component interface but not currently used in the component.
  // If it's implemented in the future, this test should be updated to properly test its functionality.
  error: false,
};
const renderLorcanaFinishSelector = (props: Partial<LorcanaFinishSelectorProps> = {}) => {
  return renderWithDispatcher(LorcanaFinishSelector, { ...DEFAULT_PROPS, ...props });
};

describe('LorcanaFinishSelector', () => {
  beforeEach(() => {
    vi.spyOn(LorcanaFinishAPI, 'load').mockResolvedValue(mockFinishes);
    vi.spyOn(FinishActions, 'loadLorcanaFinishes');
  });

  describe('with FormLabel', () => {
    it('renders FormLabel with correct heading', () => {
      renderLorcanaFinishSelector();
      expect(screen.getByText('Finish', { selector: 'div.form-label--heading' })).toBeInTheDocument();
    });

    it('hides FormLabel when hideTitle is true', () => {
      renderLorcanaFinishSelector({ hideTitle: true });
      expect(screen.queryByText('Finish', { selector: 'div.form-label--heading' })).not.toBeInTheDocument();
    });
  });

  describe('with AsyncSelect', () => {
    it('renders with first lorcanaFinishes when no finish is provided', async () => {
      const { dispatcher } = renderLorcanaFinishSelector({ finish: undefined });
      await waitFor(() => {
        expect(FinishActions.loadLorcanaFinishes).toHaveBeenCalledWith(dispatcher);
      });
      expect(screen.getByText(mockFinishes[0].get('name'))).toBeInTheDocument();
    });
    it('updates selected finish when finish prop changes', () => {
      const { rerenderWithDispatcher } = renderLorcanaFinishSelector();
      expect(screen.getByText(mockFinishes[0].get('name'))).toBeInTheDocument();

      rerenderWithDispatcher({
        finish: mockFinishes[1],
      });
      expect(screen.getByText(mockFinishes[1].get('name'))).toBeInTheDocument();
    });
    it('renders with "Multiple" when finish prop is "Multiple"', () => {
      renderLorcanaFinishSelector({ finish: 'Multiple' });
      expect(screen.getByText('Multiple')).toBeInTheDocument();
    });
    it('disables the AsyncSelect when disabled prop is true', () => {
      renderLorcanaFinishSelector({ disabled: true });
      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });
    it('calls onChange when select change', async () => {
      const { container, dispatcher } = renderLorcanaFinishSelector();
      await waitFor(() => {
        expect(FinishActions.loadLorcanaFinishes).toHaveBeenCalledWith(dispatcher);
      });
      const input = screen.getByRole('textbox');
      const targetSelectedOption = mockFinishes[3];
      const query = targetSelectedOption.get('name').slice(0, 10);
      fireEvent.change(input, { target: { value: query } });
      await waitFor(() => {
        const suggestion = container.querySelector('.react-select-thin__option') as HTMLElement;
        expect(suggestion).toBeInTheDocument();
        fireEvent.click(suggestion);
        expect(DEFAULT_PROPS.onChange).toHaveBeenCalledExactlyOnceWith(targetSelectedOption);
      });
    });
  });
});
