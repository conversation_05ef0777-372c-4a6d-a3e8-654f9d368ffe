import { fireEvent, render, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { Language, LanguageName } from '../../../models/Language';
import { LanguageSelector } from './LanguageSelector';

type LanguageSelectorProps = React.ComponentProps<typeof LanguageSelector>;
const languageOptions = [Language.ENGLISH, Language.JAPANESE, Language.GERMAN];
const DEFAULT_PROPS: LanguageSelectorProps = {
  language: Language.ENGLISH,
  options: Immutable.OrderedSet(languageOptions),
  onChange: vi.fn(),
  disabled: false,
  error: false,
  hideTitle: false,
};

function renderLanguageSelector(props: Partial<LanguageSelectorProps> = {}) {
  return render(<LanguageSelector {...DEFAULT_PROPS} {...props} />);
}

describe('LanguageSelector', () => {
  describe('with FormLabel', () => {
    it('renders FormLabel', () => {
      renderLanguageSelector();
      expect(screen.getByText('Language', { selector: 'div.form-label--heading' })).toBeInTheDocument();
    });

    it('hides FormLabel', () => {
      renderLanguageSelector({ hideTitle: true });
      expect(screen.queryByText('Language', { selector: 'div.form-label--heading' })).not.toBeInTheDocument();
    });
  });

  describe('with IconSelect', () => {
    it('renders select and all language options', () => {
      renderLanguageSelector();
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
      expect(select).toHaveValue(DEFAULT_PROPS.language);
      expect(screen.getByRole('option', { name: 'Language' })).toBeDisabled();
      languageOptions.forEach((language) => {
        expect(screen.getByRole('option', { name: LanguageName(language) })).toBeInTheDocument();
      });
    });

    it("renders 'Multiple' option when language is 'Multiple'", () => {
      renderLanguageSelector({ language: 'Multiple' });
      const multipleOption = screen.getByRole('option', { name: 'Multiple' });
      expect(multipleOption).toBeInTheDocument();
      expect(multipleOption).toBeDisabled();
    });

    test('trigger onChange prop', () => {
      renderLanguageSelector();
      const select = screen.getByRole('combobox');
      fireEvent.change(select, { target: { value: Language.JAPANESE } });
      expect(DEFAULT_PROPS.onChange).toHaveBeenCalledWith(Language.JAPANESE);
    });

    it('disables the select when disabled prop is true', () => {
      renderLanguageSelector({ disabled: true });
      expect(screen.getByRole('combobox')).toBeDisabled();
    });
  });
});
