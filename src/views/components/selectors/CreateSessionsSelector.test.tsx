import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CreateSessions, CreateSessionsKey } from '../../../models/CardBot';
import { CreateSessionsSelector } from './CreateSessionsSelector';

type CreateSessionsSelectorProps = React.ComponentProps<typeof CreateSessionsSelector>;
const DEFAULT_PROPS: CreateSessionsSelectorProps = {
  selection: CreateSessions.ALL,
  onChange: vi.fn(),
  disabled: false,
};

function renderCreateSessionsSelector(props: Partial<CreateSessionsSelectorProps> = {}) {
  return render(<CreateSessionsSelector {...DEFAULT_PROPS} {...props} />);
}

describe('CreateSessionsSelector', () => {
  describe('with FormLabel', () => {
    it('renders heading and subheading', () => {
      renderCreateSessionsSelector();
      expect(screen.getByText('Create Sessions', { selector: 'div.form-label--heading' })).toBeInTheDocument();
      expect(screen.getByText('Select which cards should be staged')).toBeInTheDocument();
    });
  });

  describe('with IconSelect', () => {
    it('renders select and all session options', () => {
      renderCreateSessionsSelector();
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
      expect(select).toHaveValue(DEFAULT_PROPS.selection);
      expect(screen.getByRole('option', { name: 'Create Sessions' })).toBeDisabled();
      Object.keys(CreateSessions).forEach((sessionKey) => {
        const optionByValue = select.querySelector(
          `option[value="${CreateSessions[sessionKey as CreateSessionsKey]}"]`,
        );
        expect(optionByValue).toBeInTheDocument();
      });
    });

    test('trigger onChange prop', () => {
      renderCreateSessionsSelector();
      const select = screen.getByRole('combobox');
      fireEvent.change(select, { target: { value: CreateSessions.ACCEPTED } });
      expect(DEFAULT_PROPS.onChange).toHaveBeenCalledWith(CreateSessions.ACCEPTED);
    });

    it('disables select when disabled prop is true', () => {
      renderCreateSessionsSelector({ disabled: true });
      expect(screen.getByRole('combobox')).toBeDisabled();
    });
  });
});
