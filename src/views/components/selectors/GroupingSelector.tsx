import style from '@iconify/icons-ic/style';
import * as React from 'react';
import { TextFormat } from '../../../helpers/fmt';
import { Grouping, type GroupingKey } from '../../../models/Grouping';
import { FormLabel } from '../FormLabel';
import { IconSelect } from '../IconSelect';

interface IProps {
  grouping: Grouping;
  onChange: (grouping: Grouping) => void;
  disabled?: boolean;
}

export function GroupingSelector(props: IProps) {
  return (
    <>
      <FormLabel heading="Group" />
      <IconSelect
        className="icon-container--card-input"
        disabled={props.disabled === true}
        icon={style}
        value={props.grouping}
        onChange={(evt) => {
          evt.preventDefault();
          props.onChange(evt.currentTarget.value as Grouping);
        }}
      >
        <>
          {Object.keys(Grouping).map((key) => (
            <option key={key} value={Grouping[key as GroupingKey]}>
              {TextFormat.capitalize(Grouping[key as Grouping<PERSON>ey])}
            </option>
          ))}
        </>
      </IconSelect>
    </>
  );
}
