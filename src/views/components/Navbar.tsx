import build from '@iconify/icons-ic/build';
import device_hub from '@iconify/icons-ic/device-hub';
import dns from '@iconify/icons-ic/dns';
import pie_chart from '@iconify/icons-ic/pie-chart';
import settings from '@iconify/icons-ic/settings';
import widgets from '@iconify/icons-ic/widgets';
import { Icon } from '@iconify/react';
import * as React from 'react';
import * as BannerActions from '../../actions/BannerActions';
import * as HasBanner from '../../containers/HasBanner';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMessage from '../../containers/HasMessage';
import { environment } from '../../helpers/environment';
import history from '../../helpers/history';
import isServerside from '../../helpers/serverside';
import { User } from '../../models/Users';
import { Message } from './Message';

interface IProps {
  me: User;
  isRibbon?: boolean;
  isLogin?: boolean;
  selection?: string;
}

interface IState {
  isBanner?: boolean;
}

export const Navbar = HasMessage.Attach<IProps & HasDispatcher.IProps>(
  HasBanner.Attach<IProps & HasDispatcher.IProps & HasMessage.IProps>(
    class extends React.Component<IProps & HasDispatcher.IProps & HasMessage.IProps & HasBanner.IProps, IState> {
      /**
       * @override
       */
      constructor(props: IProps & HasDispatcher.IProps & HasMessage.IProps & HasBanner.IProps) {
        super(props);
        this.state = {
          isBanner:
            !isServerside() && props.banner && props.banner.get('token')
              ? localStorage.getItem(props.banner.get('token')!) !== 'true'
              : false,
        };
      }

      /**
       * @override
       */
      componentDidMount() {
        BannerActions.refresh(this.props.dispatcher).catch((err) => {
          err && console.error(err);
        });
      }

      /**
       * @override
       */
      componentWillReceiveProps(props: IProps & HasDispatcher.IProps & HasMessage.IProps & HasBanner.IProps) {
        this.setState({
          isBanner:
            !isServerside() && props.banner && props.banner.get('token')
              ? localStorage.getItem(props.banner.get('token')!) !== 'true'
              : false,
        });
      }

      /**
       * @override
       */
      render() {
        return (
          <header className="navbar">
            {this.props.banner.get('id') && this.state.isBanner ? (
              <div className="info-container horizontal align-center">
                {this.props.banner.get('message')}
                <button className="button-white" style={{ marginLeft: '20px' }} onClick={this.dismissBanner.bind(this)}>
                  {this.props.banner.get('buttonMessage')}
                </button>
              </div>
            ) : null}

            <div className="navbar-container">
              <div className="navbar-logo" onClick={this.goHome.bind(this)}>
                <img className="visible-md" src="/images/logo-white.png" />
                <img className="hidden-md" src="/images/logo-white-small.png" />
              </div>
              {this.props.me && this.props.me.get('id') && this.props.isRibbon ? (
                <div className="navbar-items">
                  <div
                    className={'navbar-item' + (this.props.selection === 'build' ? ' is-selected' : '')}
                    onClick={this.link.bind(this, 'builder')}
                  >
                    Build
                  </div>
                  <div
                    className={'navbar-item-xs' + (this.props.selection === 'build' ? ' is-selected' : '')}
                    onClick={this.link.bind(this, 'builder')}
                  >
                    <Icon height={'24px'} width={'24px'} icon={build} />
                  </div>
                  <div
                    className={'navbar-item' + (this.props.selection === 'collection' ? ' is-selected' : '')}
                    onClick={this.link.bind(this, 'collection')}
                  >
                    Collection
                  </div>
                  <div
                    className={'navbar-item-xs' + (this.props.selection === 'collection' ? ' is-selected' : '')}
                    onClick={this.link.bind(this, 'collection')}
                  >
                    <Icon height={'24px'} width={'24px'} icon={widgets} />
                  </div>
                  <div
                    className={'navbar-item' + (this.props.selection === 'decks' ? ' is-selected' : '')}
                    onClick={this.link.bind(this, 'decks')}
                  >
                    Decks
                  </div>
                  <div
                    className={'navbar-item-xs' + (this.props.selection === 'decks' ? ' is-selected' : '')}
                    onClick={this.link.bind(this, 'decks')}
                  >
                    <Icon height={'24px'} width={'24px'} icon={dns} />
                  </div>
                  <div
                    className={
                      'navbar-item' +
                      (this.props.selection === 'cardbot-connect' || this.props.selection === 'cardbot-control'
                        ? ' is-selected'
                        : '')
                    }
                    onClick={this.link.bind(this, 'cardbot-connect')}
                  >
                    CardBot
                  </div>
                  <div
                    className={
                      'navbar-item-xs' +
                      (this.props.selection === 'cardbot-connect' || this.props.selection === 'cardbot-control'
                        ? ' is-selected'
                        : '')
                    }
                    onClick={this.link.bind(this, 'cardbot-connect')}
                  >
                    <Icon height={'24px'} width={'24px'} icon={device_hub} />
                  </div>
                  <div
                    className={'navbar-item' + (this.props.selection === 'statistics' ? ' is-selected' : '')}
                    onClick={this.link.bind(this, 'statistics')}
                  >
                    Statistics
                  </div>
                  <div
                    className={'navbar-item-xs' + (this.props.selection === 'statistics' ? ' is-selected' : '')}
                    onClick={this.link.bind(this, 'statistics')}
                  >
                    <Icon height={'24px'} width={'24px'} icon={pie_chart} />
                  </div>
                  <div
                    className={'navbar-item' + (this.props.selection === 'settings' ? ' is-selected' : '')}
                    onClick={this.link.bind(this, 'settings')}
                  >
                    Settings
                  </div>
                  <div
                    className={'navbar-item-xs' + (this.props.selection === 'settings' ? ' is-selected' : '')}
                    onClick={this.link.bind(this, 'settings')}
                  >
                    <Icon height={'24px'} width={'24px'} icon={settings} />
                  </div>
                </div>
              ) : (
                <div className="navbar-items">
                  {this.props.isLogin ? (
                    this.props.me && this.props.me.get('id') ? (
                      <a className="button-white" href="/login">
                        My Collection
                      </a>
                    ) : (
                      <a className="button-white" href="/signup">
                        Sign Up
                      </a>
                    )
                  ) : this.props.me && this.props.me.get('id') ? (
                    <a className="button-white" href="/login">
                      My Collection
                    </a>
                  ) : (
                    <a className="button-white" href="/login">
                      Login
                    </a>
                  )}
                </div>
              )}
            </div>

            {this.props.infoMessage ? (
              <Message
                dispatcher={this.props.dispatcher}
                type="info"
                message={this.props.infoMessage.get('message')}
                button={this.props.infoMessage.get('buttonMessage')}
                onClickButton={this.props.infoMessage.get('buttonCallback')}
              />
            ) : null}
            {this.props.successMessage ? (
              <Message
                dispatcher={this.props.dispatcher}
                type="success"
                message={this.props.successMessage.get('message')}
                button={this.props.successMessage.get('buttonMessage')}
                onClickButton={this.props.successMessage.get('buttonCallback')}
              />
            ) : null}
            {this.props.errorMessage ? (
              <Message
                dispatcher={this.props.dispatcher}
                type="error"
                message={this.props.errorMessage.get('message')}
                button={this.props.errorMessage.get('buttonMessage')}
                onClickButton={this.props.errorMessage.get('buttonCallback')}
              />
            ) : null}
          </header>
        );
      }

      private link(page: string, evt: React.SyntheticEvent<HTMLElement>) {
        history.push('/' + page);
      }

      private goHome(evt: React.SyntheticEvent<HTMLElement>) {
        location.assign(environment().homeURL());
      }

      private dismissBanner(evt: React.SyntheticEvent<HTMLElement>) {
        evt.preventDefault();
        this.setState({ isBanner: false });
        if (this.props.banner.get('token')) {
          localStorage.setItem(this.props.banner.get('token')!, 'true');
        }
        if (this.props.banner.get('buttonUrl')) {
          window.open(this.props.banner.get('buttonUrl')!, '_blank');
        }
      }
    },
  ),
);
