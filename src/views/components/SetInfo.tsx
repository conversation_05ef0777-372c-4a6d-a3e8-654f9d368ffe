import * as React from 'react';
import { Game } from '../../models/Game';
import { LorcanaRarity } from '../../models/lorcana/LorcanaRarity';
import { PokeRarity } from '../../models/pokemon/PokeRarity';
import { Rarity } from '../../models/Rarity';
import { SetIconInfo } from '../../models/SetIconInfo';
import { YugiRarity } from '../../models/yugioh/YugiRarity';
import { LorcanaSetSymbol } from './lorcana/LorcanaSetSymbol';
import { PokeSetSymbol } from './pokemon/PokeSetSymbol';
import { SetSymbol } from './SetSymbol';
import { YugiSetSymbol } from './yugioh/YugiSetSymbol';

interface IProps {
  game: Game;
  info: SetIconInfo;
}

export const SetInfo = (props: IProps) => {
  const rarities = props.info.get('rarities');

  let setSymbols: JSX.Element;
  switch (props.game) {
    case Game.MTG:
      setSymbols = (
        <>
          {rarities.map((rarity: Rarity) => {
            return (
              <SetSymbol
                key={props.info.get('setCode') + rarity.toString()} // Reduces Typescript's grumpiness.
                setName={props.info.get('setName')}
                setCode={props.info.get('setCode')}
                rarity={rarity}
                hoverText={false}
              />
            );
          })}
        </>
      );
      break;
    case Game.POKEMON:
      setSymbols = (
        <>
          {rarities.map((rarity: PokeRarity) => {
            return (
              <PokeSetSymbol
                key={props.info.get('setCode') + rarity.toString()} // Reduces Typescript's grumpiness.
                setName={props.info.get('setName')}
                setUUID={props.info.get('setCode')}
                rarity={rarity}
                hoverText={false}
              />
            );
          })}
        </>
      );
      break;
    case Game.YUGIOH:
      setSymbols = (
        <>
          {rarities.map((rarity: YugiRarity) => {
            return (
              <YugiSetSymbol
                key={props.info.get('setCode') + rarity.toString()} // Reduces Typescript's grumpiness.
                setName={props.info.get('setName')}
                setUUID={props.info.get('setCode')}
                rarity={rarity}
                hoverText={false}
              />
            );
          })}
        </>
      );
      break;
    case Game.LORCANA:
      setSymbols = (
        <>
          {rarities.map((rarity: LorcanaRarity) => {
            return (
              <LorcanaSetSymbol
                key={props.info.get('setCode') + rarity.toString()} // Reduces Typescript's grumpiness.
                setName={props.info.get('setName')}
                setUUID={props.info.get('setCode')}
                rarity={rarity}
                hoverText={false}
              />
            );
          })}
        </>
      );
      break;
  }

  return (
    <div className="card-panel-set-info-container">
      {setSymbols}
      <span className="flex" style={{ verticalAlign: 'middle' }}>
        {props.info.get('setName')}
      </span>
    </div>
  );
};
