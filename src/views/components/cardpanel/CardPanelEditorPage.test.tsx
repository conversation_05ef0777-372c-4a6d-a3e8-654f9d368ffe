import { screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import {
  FakeCardPanelWithOptions,
  FakeLorcanaCardGroupWithOptions,
  FakePokeCardGroupWithOptions,
  FakeYugiCardGroupWithOptions,
} from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { CardPanel, CardPanelType } from '../../../models/CardPanels';
import { Game } from '../../../models/Game';
import { MTGCardGroup } from '../../../models/mtg/MTGCardPage';
import { CardPanelEditorContent } from './CardPanelEditorPage';
const DEFAULT_PROPS = {
  cardPanel: create(FakeCardPanelWithOptions),
  cardNames: Immutable.Set<string>(['Card 1']),
  showForeignName: false,
  priceLabel: '$10.00',
  cardPanelType: CardPanelType.COLLECTION_EDIT,
  loadOptions: vi.fn(),
  onChangeAttributes: vi.fn(),
  onChangeCard: vi.fn(),
  timeAdded: '2024-01-01',
};
type CardPanelEditorContentProps = React.ComponentProps<typeof CardPanelEditorContent>;

function renderCardPanelEditorContent(props: Partial<CardPanelEditorContentProps> = {}) {
  return renderWithDispatcher(CardPanelEditorContent, { ...DEFAULT_PROPS, ...props });
}
function getForeignName(cardPanel: CardPanel) {
  return (cardPanel.get('cardGroup') as MTGCardGroup).getInstances().first().get('foreignName');
}

describe('CardPanelEditorContent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  describe('when cardGroup empty', () => {
    it('renders nothing', () => {
      const { container } = renderCardPanelEditorContent({
        cardPanel: new CardPanel({
          game: Game.MTG,
          cardGroup: new MTGCardGroup(),
          cardPanelType: CardPanelType.COLLECTION_VIEW,
        }),
      });
      expect(container).toBeEmptyDOMElement();
    });
  });

  describe('when card group not empty', () => {
    it('renders card name when card name size more than 1', () => {
      const cardNames = ['Card 1', 'Card 2'];
      renderCardPanelEditorContent({ cardNames: Immutable.Set<string>(cardNames) });
      expect(screen.getByText('Card Names')).toBeInTheDocument();
      expect(screen.queryByText(`${cardNames[0]}, and ${cardNames.length - 1} more`)).toBeInTheDocument();
    });
    it('renders foreign name when showForeignName is true', () => {
      renderCardPanelEditorContent({ showForeignName: true });
      const foreignName = getForeignName(DEFAULT_PROPS.cardPanel);
      expect(screen.getByText(foreignName)).toBeInTheDocument();
    });
    it('renders MTG attributes', () => {
      renderCardPanelEditorContent();
      expect(screen.getByText('Foil')).toBeInTheDocument();
      const conditions = screen.getAllByText('Condition');
      expect(conditions.length).toBeGreaterThan(0);
      expect(screen.getByText('Language')).toBeInTheDocument();
    });

    it('renders POKEMON attributes', () => {
      const cardGroup = create(FakePokeCardGroupWithOptions);
      const cardPanel = create(FakeCardPanelWithOptions, {
        game: Game.POKEMON,
        cardGroup,
      });
      renderCardPanelEditorContent({
        cardPanel,
      });
      const conditions = screen.getAllByText('Condition');
      expect(conditions.length).toBeGreaterThan(0);
      expect(screen.getByText('Finish')).toBeInTheDocument();
    });
    it('renders YUGIOH attributes', () => {
      const cardGroup = create(FakeYugiCardGroupWithOptions);
      const cardPanel = create(FakeCardPanelWithOptions, {
        game: Game.YUGIOH,
        cardGroup,
      });
      renderCardPanelEditorContent({
        cardPanel,
      });
      const conditions = screen.getAllByText('Condition');
      expect(conditions.length).toBeGreaterThan(0);
    });
    it('renders LORCANA attributes', () => {
      const cardGroup = create(FakeLorcanaCardGroupWithOptions);
      const cardPanel = create(FakeCardPanelWithOptions, {
        game: Game.LORCANA,
        cardGroup,
      });
      renderCardPanelEditorContent({
        cardPanel,
      });
      const conditions = screen.getAllByText('Condition');
      expect(conditions.length).toBeGreaterThan(0);
      expect(screen.getByText('Finish')).toBeInTheDocument();
    });
  });
});
