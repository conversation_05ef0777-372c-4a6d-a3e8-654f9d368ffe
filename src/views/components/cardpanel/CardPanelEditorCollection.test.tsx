import * as Immutable from 'immutable';
import * as React from 'react';
import { beforeEach, describe, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeUserWithOptions } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as CardPanelActions from '../../../actions/CardPanelActions';
import * as MessageActions from '../../../actions/MessageActions';
import { SupportedCurrencyCodes } from '../../../helpers/currency_helper';
import { CardAttributes } from '../../../models/CardAttributes';
import { CardPanel, CardPanelType } from '../../../models/CardPanels';
import { Condition } from '../../../models/Condition';
import { Game } from '../../../models/Game';
import { MTGCardGroup } from '../../../models/mtg/MTGCardPage';
import { FreeLimits } from '../../../models/Subscriptions';
import { Tag } from '../../../models/Tags';
import { CardPanelEditorCollection } from './CardPanelEditorCollection';

vi.mock('../../../actions/CardPanelActions');
vi.mock('../../../actions/MessageActions');

const mockNewTag = new Tag({ name: 'new-tag' });
const mockRemovetag = new Tag({ name: 'test-tag' });
vi.mock('../TagsBuilder', () => ({
  TagsBuilder: ({ onRemove, onAdd }: any) => {
    onAdd(mockNewTag);
    onRemove(mockRemovetag);
    return <div>Mock</div>;
  },
}));

const mockAttributes = new CardAttributes({ condition: Condition.NEAR_MINT });

vi.mock('./CardPanelEditorPage', () => ({
  CardPanelEditorContent: ({ onChangeAttributes, loadOptions }: any) => {
    onChangeAttributes(mockAttributes);
    loadOptions(true);
    return <div>Mock</div>;
  },
}));

const DEFAULT_PROPS = {
  me: create(FakeUserWithOptions, { currency: SupportedCurrencyCodes.USD, subscribed: false }),
  cardPanel: new CardPanel({
    game: Game.MTG,
    cardGroup: new MTGCardGroup(),
    cardPanelType: CardPanelType.COLLECTION_VIEW,
  }),
  showForeignName: false,
  cardNames: Immutable.Set<string>(['Test Card']),
  priceLabel: '$10.00',
  timeAdded: '2024-01-01',
  userTags: Immutable.Set<Tag>(),
};

type CardPanelEditorCollectionProps = React.ComponentProps<typeof CardPanelEditorCollection>;

function renderCardPanelEditor(props: Partial<CardPanelEditorCollectionProps> = {}) {
  return renderWithDispatcher(CardPanelEditorCollection, { ...DEFAULT_PROPS, ...props });
}

describe('CardPanelEditorCollection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('with CardPanelEditorContent', () => {
    it('call updateUnsavedChanges with correct attributes', () => {
      const { dispatcher } = renderCardPanelEditor();
      expect(CardPanelActions.updateUnsavedChanges).toHaveBeenCalledWith(dispatcher, mockAttributes);
    });
    it('call populatePrintingDropDown when options are not ready', () => {
      const { dispatcher } = renderCardPanelEditor();
      expect(CardPanelActions.populatePrintingDropDown).toHaveBeenCalledWith(
        DEFAULT_PROPS.cardPanel.get('game'),
        DEFAULT_PROPS.cardPanel.get('cardGroup'),
        dispatcher,
      );
    });

    it('call populatePrintingDropDown when forced', () => {
      const cardPanel = DEFAULT_PROPS.cardPanel.set('printingOptionsReady', true);
      renderCardPanelEditor({ cardPanel });
      expect(CardPanelActions.populatePrintingDropDown).toHaveBeenCalled();
    });
  });

  describe('with TagBuilder', () => {
    it('show error when adding tag at limit for non-subscribed user', () => {
      const userTags = Array(FreeLimits.TAGS)
        .fill(null)
        .map((_, i) => new Tag({ name: `tag-${i}` }));
      const { dispatcher } = renderCardPanelEditor({ userTags: Immutable.Set(userTags) });

      expect(MessageActions.error).toHaveBeenCalledWith("You've reached the tag limit for Squire accounts", dispatcher);
    });

    it('remove tag from tagsToRemove when re-adding', () => {
      const tagsToRemove = [mockRemovetag.get('name'), mockRemovetag];
      const cardPanel = DEFAULT_PROPS.cardPanel.setIn(['tagsToRemove'], Immutable.Map([tagsToRemove]));

      const { dispatcher } = renderCardPanelEditor({ cardPanel });

      expect(CardPanelActions.removeTags).toHaveBeenCalledWith(
        cardPanel.get('tagsToRemove').remove(mockNewTag.get('name')),
        dispatcher,
      );
    });

    it('remove tag from tagsToAdd when removing unsaved tag', () => {
      const tagsToRemove = [mockRemovetag.get('name'), mockRemovetag];
      const cardPanel = DEFAULT_PROPS.cardPanel.setIn(['tagsToAdd'], Immutable.Map([tagsToRemove]));

      const { dispatcher } = renderCardPanelEditor({ cardPanel });

      expect(CardPanelActions.addTags).toHaveBeenCalledWith(
        cardPanel.get('tagsToAdd').remove(mockRemovetag.get('name')),
        dispatcher,
      );
    });
  });
});
