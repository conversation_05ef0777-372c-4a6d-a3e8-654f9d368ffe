import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardPanelActions from '../../../actions/CardPanelActions';
import * as MessageActions from '../../../actions/MessageActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { CardAttributes } from '../../../models/CardAttributes';
import { CardPanel as CardPanelModel, CardPanelType } from '../../../models/CardPanels';
import { Card } from '../../../models/Cards';
import { FreeLimits } from '../../../models/Subscriptions';
import { Tag } from '../../../models/Tags';
import { User } from '../../../models/Users';
import { TagsBuilder } from '../TagsBuilder';
import { CardPanelEditorContent } from './CardPanelEditorPage';

interface IState {}

interface CPECollectionProps {
  dispatcher: Dispatcher;
  me: User;
  cardPanel: CardPanelModel;
  showForeignName: boolean;
  cardNames: Immutable.Set<string>;
  timeAdded?: string;
  priceLabel: string;
  userTags: Immutable.Set<Tag>;
}

export class CardPanelEditorCollection extends React.Component<CPECollectionProps, IState> {
  /**
   * @override
   */
  render() {
    return (
      <>
        <CardPanelEditorContent
          dispatcher={this.props.dispatcher}
          cardPanel={this.props.cardPanel}
          showForeignName={this.props.showForeignName}
          cardNames={this.props.cardNames}
          timeAdded={this.props.timeAdded}
          cardPanelType={CardPanelType.COLLECTION_EDIT}
          onChangeAttributes={this.updateAttributes.bind(this)}
          onChangeCard={this.changeCard.bind(this)}
          loadOptions={this.loadOptions.bind(this)}
          priceLabel={this.props.priceLabel}
        />
        <div className="card-panel-info-heading" style={{ marginBottom: '0.5rem' }}>
          Tags
        </div>
        <TagsBuilder
          dispatcher={this.props.dispatcher}
          me={this.props.me}
          userTags={this.props.userTags}
          tags={this.props.cardPanel
            .get('tags')
            .union(this.props.cardPanel.get('tagsToAdd').toOrderedSet())
            .subtract(this.props.cardPanel.get('tagsToRemove').toOrderedSet())}
          onAdd={this.onAddTag.bind(this)}
          onRemove={this.onRemoveTag.bind(this)}
          publicViewing={false}
        />
      </>
    );
  }

  private updateAttributes(attributes: CardAttributes) {
    CardPanelActions.updateUnsavedChanges(this.props.dispatcher, attributes);
  }

  private changeCard(card: Card) {
    CardPanelActions.changeCard(this.props.dispatcher, card);
  }

  loadOptions = (force: boolean) => {
    // TODO: Move printing options loading to selector state - requires ensuring options are cached correctly
    if (!this.props.cardPanel.get('printingOptionsReady') || force) {
      CardPanelActions.populatePrintingDropDown(
        this.props.cardPanel.get('game'),
        this.props.cardPanel.get('cardGroup'),
        this.props.dispatcher,
      );
    }
  };

  private onAddTag(tag: Tag) {
    const limited = this.props.userTags.size >= FreeLimits.TAGS && !this.props.me.get('subscribed');
    const addingNewTag =
      this.props.userTags.filter((existingTag: Tag) => existingTag.get('name') === tag.get('name')).count() === 0;

    if (limited && addingNewTag) {
      MessageActions.error("You've reached the tag limit for Squire accounts", this.props.dispatcher);
    } else if (this.props.cardPanel.get('tagsToRemove').has(tag.get('name'))) {
      CardPanelActions.removeTags(
        this.props.cardPanel.get('tagsToRemove').remove(tag.get('name')),
        this.props.dispatcher,
      );
    } else {
      CardPanelActions.addTags(this.props.cardPanel.get('tagsToAdd').set(tag.get('name'), tag), this.props.dispatcher);
    }
  }

  private onRemoveTag(tag: Tag) {
    // Removes tag from state if not saved to backend yet.
    if (this.props.cardPanel.get('tagsToAdd').has(tag.get('name'))) {
      CardPanelActions.addTags(this.props.cardPanel.get('tagsToAdd').remove(tag.get('name')), this.props.dispatcher);
    } else {
      // Else adds tag to tags that need to be removed from backend on save.
      CardPanelActions.removeTags(
        this.props.cardPanel.get('tagsToRemove').set(tag.get('name'), tag),
        this.props.dispatcher,
      );
    }
  }
}
