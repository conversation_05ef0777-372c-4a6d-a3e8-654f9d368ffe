import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it } from 'vitest';
import 'vitest-dom/extend-expect';
import { create } from '../../../../tests/fake/Fake';
import {
  FakeCardPanelWithOptions,
  FakeLorcanaCardGroupWithOptions,
  FakeMTGCardGroupWithOptions,
  FakePokeCardGroupWithOptions,
  FakeYugiCardGroupWithOptions,
} from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { CardAttributes } from '../../../models/CardAttributes';
import { CardPanel, CardPanelType } from '../../../models/CardPanels';
import { Foil } from '../../../models/Foil';
import { Game } from '../../../models/Game';
import { MTGCardGroup } from '../../../models/mtg/MTGCardPage';
import { PokeCardGroup } from '../../../models/pokemon/PokeCardPage';
import { CardPanelStack } from './CardPanelStack';
import { DisplayMode } from './DisplayImageSwitch';

import * as CardPanelActions from '../../../actions/CardPanelActions';
import * as InputSessionActions from '../../../actions/InputSessionActions';

vi.mock('../../../actions/CardPanelActions');
vi.mock('../../../actions/InputSessionActions');
const DEFAULT_PROPS = {
  cardPanel: create(FakeCardPanelWithOptions),
  cardPanelRow: 0,
  attributesOverride: new CardAttributes({ foil: Foil.On }),
};
type CardPanelStackProps = React.ComponentProps<typeof CardPanelStack>;

function getCardName(cardPanel: CardPanel) {
  if (cardPanel.get('game') == Game.POKEMON) {
    return (cardPanel.get('cardGroup') as PokeCardGroup).getInstances().first().get('card').get('name');
  }
  return (cardPanel.get('cardGroup') as MTGCardGroup).getInstances().first().get('cardName');
}
function renderCardPanelStack(props: Partial<CardPanelStackProps> = {}) {
  return renderWithDispatcher(CardPanelStack, { ...DEFAULT_PROPS, ...props });
}

describe('CardPanelStack', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  describe('card stack image', () => {
    it('renders nothing when card group not provided', () => {
      const { container } = renderCardPanelStack({
        cardPanel: new CardPanel({
          game: Game.MTG,
          cardGroup: new MTGCardGroup(),
          cardPanelType: CardPanelType.COLLECTION_VIEW,
        }),
      });
      expect(container).toBeEmptyDOMElement();
    });
    it('render single card stack image', () => {
      const { container } = renderCardPanelStack();
      expect(container.querySelector('.card-panel-stack-image-offset')).toBeInTheDocument();
      const cardName = getCardName(DEFAULT_PROPS.cardPanel);
      const image = screen.getByAltText(cardName);
      expect(image).toBeInTheDocument();
    });
    it('render multiple card stack image', () => {
      const cardPanel = create(FakeCardPanelWithOptions, {
        cardGroup: create(FakeMTGCardGroupWithOptions, { cardInstancesLength: 2 }),
      });
      const { container } = renderCardPanelStack({ cardPanel });
      expect(container.querySelectorAll('.card-panel-stack-image-offset')).toHaveLength(2);
    });
    it('render max 4 card stack image', () => {
      const cardPanel = create(FakeCardPanelWithOptions, {
        cardGroup: create(FakeMTGCardGroupWithOptions, { cardInstancesLength: 10 }),
      });
      const { container } = renderCardPanelStack({ cardPanel });
      expect(container.querySelectorAll('.card-panel-stack-image-offset')).toHaveLength(4);
    });
    it('applies standard stack class for MTG cards', () => {
      const { container } = renderCardPanelStack();
      expect(container.querySelector('.card-panel-stack--standard')).toBeInTheDocument();
    });
    it('applies standard stack class for Pokemon cards', () => {
      const cardPanel = create(FakeCardPanelWithOptions, {
        game: Game.POKEMON,
        cardGroup: create(FakePokeCardGroupWithOptions),
      });
      const { container } = renderCardPanelStack({ cardPanel });
      expect(container.querySelector('.card-panel-stack--standard')).toBeInTheDocument();
    });

    it('applies japanese stack class for Yugioh cards', () => {
      const cardPanel = create(FakeCardPanelWithOptions, {
        game: Game.YUGIOH,
        cardGroup: create(FakeYugiCardGroupWithOptions),
      });
      const { container } = renderCardPanelStack({ cardPanel });
      expect(container.querySelector('.card-panel-stack--japanese')).toBeInTheDocument();
    });
    it('applies japanese stack class for LorcanaCara cards', () => {
      const cardPanel = create(FakeCardPanelWithOptions, {
        game: Game.LORCANA,
        cardGroup: create(FakeLorcanaCardGroupWithOptions),
      });
      const { container } = renderCardPanelStack({ cardPanel });
      expect(container.querySelector('.card-panel-stack--japanese')).toBeInTheDocument();
    });
  });

  describe('imageAttributes', () => {
    describe('when mode source', () => {
      it('applies no rotation', () => {
        renderCardPanelStack();
        const cardName = getCardName(DEFAULT_PROPS.cardPanel);
        const imageContainer = screen.getByAltText(cardName).closest('.card-panel-stack-image-offset');
        expect(imageContainer).toHaveStyle({ transform: 'none' });
      });
      it('hide foil element when foil off', () => {
        renderCardPanelStack({ attributesOverride: new CardAttributes({ foil: Foil.Off }) });
        const cardName = getCardName(DEFAULT_PROPS.cardPanel);
        const image = screen.getByAltText(cardName);
        expect(image.parentElement?.querySelector('.card-foil-overlay')).toBeNull();
        expect(image.parentElement?.querySelector('.card-foil-overlay--half')).toBeNull();
      });
      it('applies correct foil className when foil on', () => {
        renderCardPanelStack();
        const cardName = getCardName(DEFAULT_PROPS.cardPanel);
        const image = screen.getByAltText(cardName);
        expect(image.parentElement?.querySelector('.card-foil-overlay')).toBeInTheDocument();
      });
      it('applies correct foil className when foil partial', () => {
        renderCardPanelStack({ attributesOverride: new CardAttributes({ foil: Foil.Partial }) });
        const cardName = getCardName(DEFAULT_PROPS.cardPanel);
        const image = screen.getByAltText(cardName);
        expect(image.parentElement?.querySelector('.card-foil-overlay--half')).toBeInTheDocument();
      });
      it('always disables foil when game not MTG', () => {
        const cardPanel = create(FakeCardPanelWithOptions, {
          game: Game.POKEMON,
          cardGroup: create(FakePokeCardGroupWithOptions),
        });
        renderCardPanelStack({ cardPanel });
        const cardName = getCardName(cardPanel);
        const image = screen.getByAltText(cardName);
        expect(image.parentElement?.querySelector('.card-foil-overlay')).toBeNull();
        expect(image.parentElement?.querySelector('.card-foil-overlay--half')).toBeNull();
      });
    });
    describe('when mode scan', () => {
      it('applies no rotation when scannedImageURL not provided', () => {
        const cardPanel = create(FakeCardPanelWithOptions, {
          displayMode: DisplayMode.SCAN,
        });
        renderCardPanelStack({ cardPanel });
        const cardName = getCardName(cardPanel);
        const imageContainer = screen.getByAltText(cardName).closest('.card-panel-stack-image-offset');
        expect(imageContainer).toHaveStyle({ transform: 'none' });
      });
      it('applies rotation when scannedImageURL provided', () => {
        const cardPanel = create(FakeCardPanelWithOptions, {
          displayMode: DisplayMode.SCAN,
          scannedImageURL: 'test-url',
        });
        renderCardPanelStack({ cardPanel });
        const cardName = getCardName(cardPanel);

        const imageContainer = screen.getByAltText(cardName).closest('.card-panel-stack-image-offset');
        expect(imageContainer).toHaveStyle({ transform: 'rotate(90deg) translate(0, -100%)' });
      });
      it('applies flip rotation when flipped true', () => {
        const cardPanel = create(FakeCardPanelWithOptions, {
          displayMode: DisplayMode.SCAN,
          scannedImageURL: 'test-url',
          scanMetadata: { flipped: true },
        });
        renderCardPanelStack({ cardPanel });
        const cardName = getCardName(cardPanel);

        const imageContainer = screen.getByAltText(cardName).closest('.card-panel-stack-image-offset');
        expect(imageContainer).toHaveStyle({ transform: 'rotate(-90deg) translate(-100%, 0)' });
      });
      it('enables zoom', () => {
        const cardPanel = create(FakeCardPanelWithOptions, {
          displayMode: DisplayMode.SCAN,
          scannedImageURL: 'test-url',
        });
        renderCardPanelStack({ cardPanel });
        const cardName = getCardName(cardPanel);
        const image = screen.getByAltText(cardName);
        expect(image).toHaveStyle({ cursor: 'zoom-in' });
      });
    });
  });

  describe('with DisplayImageSwitch', () => {
    it('shows when scan is available', () => {
      const cardPanel = create(FakeCardPanelWithOptions, { scannedImageURL: 'test-url' });
      renderCardPanelStack({ cardPanel });
      const sourceTab = screen.getByText('Source');
      const scanTab = screen.getByText('Scan');
      expect(sourceTab).toBeInTheDocument();
      expect(scanTab).toBeInTheDocument();
    });
    it('hide when scan is unavailable', () => {
      renderCardPanelStack();
      const sourceTab = screen.queryByText('Source');
      const scanTab = screen.queryByText('Scan');
      expect(sourceTab).not.toBeInTheDocument();
      expect(scanTab).not.toBeInTheDocument();
    });
    it('calls InputSessionActions.switchDisplayMode when in staging mode', () => {
      const cardPanel = create(FakeCardPanelWithOptions, {
        cardPanelType: CardPanelType.STAGING,
        scannedImageURL: 'test-url',
      });
      const { dispatcher } = renderCardPanelStack({ cardPanel });

      const scanTab = screen.getByText('Scan');
      fireEvent.click(scanTab);
      expect(InputSessionActions.switchDisplayMode).toHaveBeenCalledWith(DisplayMode.SCAN, dispatcher);
    });

    it('calls CardPanelActions.switchDisplayMode when not in staging mode', () => {
      const cardPanel = create(FakeCardPanelWithOptions, {
        cardPanelType: CardPanelType.COLLECTION_VIEW,
        scannedImageURL: 'test-url',
      });
      const { dispatcher } = renderCardPanelStack({ cardPanel });

      const scanTab = screen.getByText('Scan');
      fireEvent.click(scanTab);

      expect(CardPanelActions.switchDisplayMode).toHaveBeenCalledWith(DisplayMode.SCAN, dispatcher);
    });
  });
});
