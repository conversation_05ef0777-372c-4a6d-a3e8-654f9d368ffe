import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardPanelActions from '../../../actions/CardPanelActions';
import * as InputSessionActions from '../../../actions/InputSessionActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { ImageQuality } from '../../../helpers/core_assets';
import { CardAttributes } from '../../../models/CardAttributes';
import { CardInstance } from '../../../models/CardInstances';
import { CardPanel, CardPanelType } from '../../../models/CardPanels';
import { Foil } from '../../../models/Foil';
import { Game } from '../../../models/Game';
import { Language } from '../../../models/Language';
import { LorcanaCardInstance } from '../../../models/lorcana/LorcanaCardInstances';
import { LorcanaCardGroup } from '../../../models/lorcana/LorcanaCardPage';
import { MTGCardGroup } from '../../../models/mtg/MTGCardPage';
import { PokeCardInstance } from '../../../models/pokemon/PokeCardInstances';
import { PokeCardGroup } from '../../../models/pokemon/PokeCardPage';
import { ScanMetadata } from '../../../models/ScanMetadata';
import { YugiCardInstance } from '../../../models/yugioh/YugiCardInstances';
import { CardImage } from '../CardImage';
import { ScannedImage } from '../ScannedImage';
import { DisplayImageSwitch, DisplayMode } from './DisplayImageSwitch';

interface IProps {
  dispatcher: Dispatcher;
  cardPanel: CardPanel;
  cardPanelRow: number;
  attributesOverride?: CardAttributes;
}

export enum ImageRotation {
  NONE,
  ROTATED,
  ROTATED_FLIPPED,
}

const MAX_CARDS = 4;

export class ImageAttributes {
  source: string;
  alt: string;
  foil: Foil;
  zoomEnabled: boolean;
  rotation: ImageRotation;

  constructor(source: string, alt: string, foil = Foil.Off, zoomEnabled = false, rotation = ImageRotation.NONE) {
    this.source = source;
    this.alt = alt;
    this.foil = foil;
    this.zoomEnabled = zoomEnabled;
    this.rotation = rotation;
  }
}

export const CardPanelStack = (props: IProps) => {
  const cardGroup = props.cardPanel.get('cardGroup');
  let imageAttributes: Immutable.List<ImageAttributes>;
  let scanAvailable: boolean;
  let stackClassName: string;

  function rotation(displayMode: DisplayMode, scannedImageURL: string, metadata: ScanMetadata): ImageRotation {
    switch (displayMode) {
      case DisplayMode.SOURCE:
        return ImageRotation.NONE;
      case DisplayMode.SCAN:
        if (scannedImageURL) {
          const flipped = (metadata && metadata.get('flipped')) || false;
          if (flipped) {
            return ImageRotation.ROTATED_FLIPPED;
          } else {
            return ImageRotation.ROTATED;
          }
        } else {
          return ImageRotation.NONE;
        }
    }
  }

  switch (props.cardPanel.get('game')) {
    case Game.MTG:
      const displayInstances = (cardGroup as MTGCardGroup).getInstances().take(MAX_CARDS);
      scanAvailable = displayInstances.first()?.get('scannedImageURL') !== undefined;
      const zoomEnabled = scanAvailable && props.cardPanel.get('displayMode') != DisplayMode.SOURCE;

      imageAttributes = displayInstances
        .map((cardInstance: CardInstance) => {
          let language: Language;
          let foil: Foil;
          let jsonID: string;
          if (props.attributesOverride) {
            language = props.attributesOverride.get('language') || cardInstance.get('language');
            foil =
              props.attributesOverride.get('foil') === undefined
                ? cardInstance.get('foil')
                : props.attributesOverride.get('foil');
            jsonID =
              !props.attributesOverride.get('printing') || props.attributesOverride.get('printing') === 'Multiple'
                ? cardInstance.get('cardJsonID')
                : props.attributesOverride.get('printing');
          } else {
            language = cardInstance.get('language');
            foil = cardInstance.get('foil');
            jsonID = cardInstance.get('cardJsonID');
          }

          const sourceImage = cardInstance.imageURL(ImageQuality.HQ, language, jsonID);

          let imageURL = sourceImage;
          if (props.cardPanel.get('displayMode') == DisplayMode.SCAN && cardInstance.get('scannedImageURL')) {
            imageURL = cardInstance.get('scannedImageURL');
            foil = Foil.Off; // Disable foil overlay for scanned images
          }

          const imageRotation = rotation(
            props.cardPanel.get('displayMode'),
            cardInstance.get('scannedImageURL'),
            cardInstance.get('scanMetadata'),
          );

          return new ImageAttributes(imageURL, cardInstance.get('cardName'), foil, zoomEnabled, imageRotation);
        })
        .toList();

      stackClassName = 'card-panel-stack--standard';
      break;
    case Game.POKEMON:
      const displayPokeInstances = (cardGroup as PokeCardGroup).getInstances().take(MAX_CARDS);
      scanAvailable = displayPokeInstances.first()?.get('scannedImageURL') !== undefined;
      const pokeZoomEnabled = scanAvailable && props.cardPanel.get('displayMode') != DisplayMode.SOURCE;
      imageAttributes = displayPokeInstances
        .map((cardInstance: PokeCardInstance) => {
          const sourceImage = cardInstance.imageURL(ImageQuality.HQ);

          let imageURL: string;
          switch (props.cardPanel.get('displayMode')) {
            case DisplayMode.SOURCE:
              imageURL = sourceImage;
              break;
            case DisplayMode.SCAN:
              imageURL = cardInstance.get('scannedImageURL') || sourceImage;
              break;
          }

          const imageRotation = rotation(
            props.cardPanel.get('displayMode'),
            cardInstance.get('scannedImageURL'),
            cardInstance.get('scanMetadata'),
          );

          return new ImageAttributes(
            imageURL,
            cardInstance.get('card').get('name'),
            Foil.Off,
            pokeZoomEnabled,
            imageRotation,
          );
        })
        .toList();
      stackClassName = 'card-panel-stack--standard';
      break;
    case Game.YUGIOH:
      const displayYugiInstances = (cardGroup as PokeCardGroup).getInstances().take(MAX_CARDS);
      scanAvailable = displayYugiInstances.first()?.get('scannedImageURL') !== undefined;
      const yugiZoomEnabled = scanAvailable && props.cardPanel.get('displayMode') != DisplayMode.SOURCE;
      imageAttributes = displayYugiInstances
        .map((cardInstance: YugiCardInstance) => {
          const sourceImage = cardInstance.imageURL(ImageQuality.HQ);

          let imageURL: string;
          switch (props.cardPanel.get('displayMode')) {
            case DisplayMode.SOURCE:
              imageURL = sourceImage;
              break;
            case DisplayMode.SCAN:
              imageURL = cardInstance.get('scannedImageURL') || sourceImage;
              break;
          }

          const imageRotation = rotation(
            props.cardPanel.get('displayMode'),
            cardInstance.get('scannedImageURL'),
            cardInstance.get('scanMetadata'),
          );

          return new ImageAttributes(
            imageURL,
            cardInstance.get('card').get('name'),
            Foil.Off,
            yugiZoomEnabled,
            imageRotation,
          );
        })
        .toList();
      stackClassName = 'card-panel-stack--japanese';
      break;
    case Game.LORCANA:
      const displayLorcanaInstances = (cardGroup as LorcanaCardGroup).getInstances().take(MAX_CARDS);
      scanAvailable = displayLorcanaInstances.first()?.get('scannedImageURL') !== undefined;
      const lorcanaZoomEnabled = scanAvailable && props.cardPanel.get('displayMode') != DisplayMode.SOURCE;
      imageAttributes = displayLorcanaInstances
        .map((cardInstance: LorcanaCardInstance) => {
          const sourceImage = cardInstance.imageURL(ImageQuality.HQ);

          let imageURL: string;
          switch (props.cardPanel.get('displayMode')) {
            case DisplayMode.SOURCE:
              imageURL = sourceImage;
              break;
            case DisplayMode.SCAN:
              imageURL = cardInstance.get('scannedImageURL') || sourceImage;
              break;
          }

          const imageRotation = rotation(
            props.cardPanel.get('displayMode'),
            cardInstance.get('scannedImageURL'),
            cardInstance.get('scanMetadata'),
          );

          return new ImageAttributes(
            imageURL,
            cardInstance.get('card').get('name'),
            Foil.Off,
            lorcanaZoomEnabled,
            imageRotation,
          );
        })
        .toList();
      stackClassName = 'card-panel-stack--japanese';
      break;
  }
  const ref = React.useRef<HTMLDivElement>(null);

  const [width, setWidth] = React.useState(0);
  const [height, setHeight] = React.useState(0);

  React.useLayoutEffect(() => {
    const newWidth = (ref.current && ref.current.offsetWidth) || 0;
    const newHeight = (ref.current && ref.current.offsetHeight) || 0;
    setWidth(newWidth);
    setHeight(newHeight);
  }, [props]);

  if (!cardGroup.count()) {
    return null;
  }

  return (
    <div style={{ marginBottom: '1rem' }}>
      <div ref={ref} className={stackClassName} style={{ marginBottom: '1rem' }}>
        {imageAttributes.map((imageAttr: ImageAttributes, index: number) => {
          // Scale down the size of each image to fit multiple - reduce logarithmically less as card count increases
          const countMultiplier = imageAttributes.count() - 1;
          let shrinkSize = 1 - (countMultiplier * 0.15) / imageAttributes.count();
          // Round to 2 dp
          shrinkSize = Math.round(shrinkSize * 100) / 100;

          const imageWidth = `${width * shrinkSize}px`;
          const imageHeight = `${height * shrinkSize}px`;

          let transform: string;
          let size: React.CSSProperties;
          let displayStyle: React.CSSProperties;
          switch (imageAttr.rotation) {
            case ImageRotation.ROTATED:
              transform = 'rotate(90deg) translate(0, -100%)';
              size = { width: imageHeight, height: imageWidth };
              displayStyle = {};
              break;
            case ImageRotation.ROTATED_FLIPPED:
              transform = 'rotate(-90deg) translate(-100%, 0)';
              size = { width: imageHeight, height: imageWidth };
              displayStyle = { display: 'flex', justifyContent: 'end' };
              break;
            default:
              transform = 'none';
              size = { width: imageWidth, height: imageHeight };
              displayStyle = {};
          }

          // Offset each image top and left by the amount we shrink by
          let offset = (1 - shrinkSize) / Math.max(1, countMultiplier);
          offset = Math.round(offset * 100);

          let imageElement: JSX.Element;

          switch (props.cardPanel.get('displayMode')) {
            case DisplayMode.SOURCE:
              imageElement = (
                <CardImage
                  src={imageAttr.source}
                  alt={imageAttr.alt}
                  foil={imageAttr.foil}
                  highlightOnHover={false}
                  selected={false}
                  game={props.cardPanel.get('game')}
                />
              );
              break;
            case DisplayMode.SCAN:
              imageElement = <ScannedImage src={imageAttr.source} alt={imageAttr.alt} rotation={imageAttr.rotation} />;
              break;
          }

          return (
            <div
              key={index}
              className="card-panel-stack-image-offset"
              style={{
                ...size,
                ...displayStyle,
                top: `${offset * index}%`,
                left: `${offset * index}%`,
                transformOrigin: 'left top',
                zIndex: MAX_CARDS - index,
                transform: transform,
              }}
            >
              {imageElement}
            </div>
          );
        })}
      </div>
      {scanAvailable && (
        <DisplayImageSwitch
          mode={props.cardPanel.get('displayMode')}
          onSwitchMode={(mode: DisplayMode) => {
            switch (props.cardPanel.get('cardPanelType')) {
              case CardPanelType.STAGING:
                InputSessionActions.switchDisplayMode(mode, props.dispatcher);
              case CardPanelType.COLLECTION_VIEW:
              case CardPanelType.COLLECTION_EDIT:
                CardPanelActions.switchDisplayMode(mode, props.dispatcher);
                break;
            }
          }}
        />
      )}
    </div>
  );
};
