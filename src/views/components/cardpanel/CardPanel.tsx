import classNames from 'classnames';
import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardPanelActions from '../../../actions/CardPanelActions';
import * as InputSessionActions from '../../../actions/InputSessionActions';
import * as MessageActions from '../../../actions/MessageActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { NumberFormat } from '../../../helpers/fmt';
import { CardAttributes } from '../../../models/CardAttributes';
import { CardPanel as CardPanelModel, CardPanelType } from '../../../models/CardPanels';
import { Game } from '../../../models/Game';
import { Language, SortLanguages } from '../../../models/Language';
import { LorcanaCardInstance } from '../../../models/lorcana/LorcanaCardInstances';
import { LorcanaCardGroup } from '../../../models/lorcana/LorcanaCardPage';
import { MTGCardGroup, MTGCardPage } from '../../../models/mtg/MTGCardPage';
import { PokeCardInstance } from '../../../models/pokemon/PokeCardInstances';
import { PokeCardGroup } from '../../../models/pokemon/PokeCardPage';
import { AnyPrinting } from '../../../models/Printing';
import { Tag } from '../../../models/Tags';
import { User } from '../../../models/Users';
import { YugiCardInstance } from '../../../models/yugioh/YugiCardInstances';
import { YugiCardGroup } from '../../../models/yugioh/YugiCardPage';
import { Dialog } from '../Dialog';
import { CardPanelPage } from './CardPanelPage';
import { CardPanelStack } from './CardPanelStack';

export enum CSSGridContainer {
  BUILDER = 'builder',
  COLLECTION = 'collection',
}

export interface CardPanelProps {
  dispatcher: Dispatcher;
  me: User;
  gridContainer?: CSSGridContainer;
  cardPage: MTGCardPage;
  cardPanel: CardPanelModel;
  cardPanelRow: number;
  userTags: Immutable.OrderedSet<Tag>;
  isGrid: boolean;
  usernamePublic: boolean;
  isMultiple?: boolean;
}

interface IState {
  confirmRemove: boolean;
}

export class CardPanel extends React.Component<CardPanelProps, IState> {
  constructor(props: CardPanelProps) {
    super(props);
    this.state = {
      confirmRemove: false,
    };
  }
  /**
   * @override
   */
  render() {
    const isOpen = this.props.cardPanelRow === this.props.cardPanel.get('y') && this.props.cardPanel.get('y') !== null;
    const isNull = this.props.cardPanelRow !== this.props.cardPanel.get('y');
    const title = this.generateTitle();
    const cardGroup = this.props.cardPanel.get('cardGroup');

    const className = classNames('card-panel', {
      'is-collapsed': !isOpen,
      'is-grid': this.props.isGrid,
      'is-multiple': this.props.isMultiple,
    });

    const cardPanelSpacingClassName = classNames({
      'card-panel-spacing__builder': this.props.gridContainer === CSSGridContainer.BUILDER,
      'card-panel-spacing__collection': this.props.gridContainer === CSSGridContainer.COLLECTION,
    });

    return (
      <>
        <Dialog
          isOpen={this.state.confirmRemove}
          onDismiss={() => {
            this.setState({ confirmRemove: false });
          }}
        >
          <h3 className="query-dialog-heading">Are you sure?</h3>
          <p className="query-dialog-subheading" style={{ whiteSpace: 'normal' }}>
            {(cardGroup.count() > 1 ? 'These cards' : 'This card') +
              ' will be removed from your collection. This decision cannot be reversed!'}
          </p>
          <div className="flex justify-center">
            <input
              type="submit"
              className="button-alert"
              value="Remove"
              autoComplete="off"
              onClick={this.onClickConfirmRemove.bind(this)}
            />
          </div>
        </Dialog>
        <div
          className={cardPanelSpacingClassName}
          id={'panel-' + this.props.cardPanelRow}
          style={{ position: 'relative' }}
        >
          <div
            className={className + (this.props.cardPanel.get('x') ? ' is-column-' + this.props.cardPanel.get('x') : '')}
          >
            <CardPanelStack
              dispatcher={this.props.dispatcher}
              cardPanel={this.props.cardPanel}
              cardPanelRow={this.props.cardPanelRow}
              attributesOverride={this.attributesOverride()}
            />
            <div className="grow-xs-1" style={{ marginLeft: '2rem', maxWidth: '100%' }}>
              <div className="card-panel-heading-container">
                <div className="card-panel-content-heading" data-testid="cardpanel-title">
                  {title}
                </div>
                {this.cardPanelActions()}
              </div>
              {isNull ? null : (
                <CardPanelPage
                  dispatcher={this.props.dispatcher}
                  me={this.props.me}
                  cardPanel={this.props.cardPanel}
                  userTags={this.props.userTags}
                  usernamePublic={this.props.usernamePublic}
                />
              )}
            </div>
          </div>
        </div>
      </>
    );
  }

  private cardPanelActions() {
    const actions: JSX.Element[] = [];

    if (this.props.cardPanel.get('cardGroup').count() > 0) {
      switch (this.props.cardPanel.get('cardPanelType')) {
        case CardPanelType.STAGING:
          actions.push(
            <div key={'close'} className="card-panel-finish-btn--active" onClick={this.onClickClose.bind(this)}>
              Close
            </div>,
          );
          break;
        case CardPanelType.COLLECTION_VIEW:
          if (!this.props.usernamePublic) {
            actions.push(
              <div key={'remove'} className="card-panel-finish-btn--active" onClick={this.onClickRemove.bind(this)}>
                {this.props.cardPanel.get('cardGroup').count() > 1 ? 'Remove All' : 'Remove'}
              </div>,
              <div key={'edit'} className="card-panel-finish-btn--active" onClick={this.onClickEdit.bind(this)}>
                Edit
              </div>,
            );
          }
          actions.push(
            <div key={'close'} className="card-panel-finish-btn--active" onClick={this.onClickClose.bind(this)}>
              Close
            </div>,
          );
          break;
        case CardPanelType.COLLECTION_EDIT:
          const unsavedCardAttributes = this.props.cardPanel.get('unsavedCardAttributes');

          let cardGroup = this.props.cardPanel.get('cardGroup');

          let currentLanguage: Language | string;
          let currentPrinting: AnyPrinting;

          switch (this.props.cardPanel.get('game')) {
            case Game.MTG:
              cardGroup = cardGroup as MTGCardGroup;
              currentLanguage =
                unsavedCardAttributes.get('language') || cardGroup.groupValue('language', 'Multiple', Language.ENGLISH);
              const jsonID =
                unsavedCardAttributes.get('printing') || cardGroup.groupValue('cardJsonID', 'Multiple', '');
              currentPrinting = cardGroup.selectedPrinting(
                jsonID,
                SortLanguages(Immutable.Set(this.props.cardPanel.get('languageOptions'))),
              );
              break;
            case Game.POKEMON:
              cardGroup = cardGroup as PokeCardGroup;
              currentLanguage = Language.ENGLISH;
              const pokeUUID = unsavedCardAttributes.get('printing') || cardGroup.cardValues('uuid', 'Multiple', '');
              currentPrinting = cardGroup.selectedPrinting(
                pokeUUID,
                SortLanguages(Immutable.Set(this.props.cardPanel.get('languageOptions'))),
              );
              break;
            case Game.YUGIOH:
              cardGroup = cardGroup as YugiCardGroup;
              currentLanguage = Language.ENGLISH;
              const yugiUUID = unsavedCardAttributes.get('printing') || cardGroup.cardValues('uuid', 'Multiple', '');
              currentPrinting = cardGroup.selectedPrinting(
                yugiUUID,
                SortLanguages(Immutable.Set(this.props.cardPanel.get('languageOptions'))),
              );
              break;
            case Game.LORCANA:
              cardGroup = cardGroup as LorcanaCardGroup;
              currentLanguage = Language.ENGLISH;
              const lorcanaUUID = unsavedCardAttributes.get('printing') || cardGroup.cardValues('uuid', 'Multiple', '');
              currentPrinting = cardGroup.selectedPrinting(
                lorcanaUUID,
                SortLanguages(Immutable.Set(this.props.cardPanel.get('languageOptions'))),
              );
              break;
          }

          const languageTuple = currentPrinting.validateLanguage(currentLanguage);

          actions.push(
            <div key={'close'} className="card-panel-finish-btn--active" onClick={this.onClickCancel.bind(this)}>
              Cancel
            </div>,
          );
          if (languageTuple.get('languageValid')) {
            actions.push(
              <div key={'save'} className="card-panel-finish-btn--active" onClick={this.onClickSave.bind(this)}>
                Save
              </div>,
            );
          } else {
            actions.push(
              <div key={'save'} className="card-panel-finish-btn--inactive">
                Save
              </div>,
            );
          }
          break;
      }
    } else {
      actions.push(
        <div className="card-panel-finish-btn--active" onClick={this.onClickClose.bind(this)}>
          Close
        </div>,
      );
    }

    return <div className="card-panel-actions justify-end">{actions}</div>;
  }

  private onClickSave(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    if (this.props.cardPanel.get('cardPanelType') == CardPanelType.STAGING) return;

    try {
      CardPanelActions.save(this.props.dispatcher, this.props.cardPanel.get('unsavedCardAttributes'), false);
    } catch (err) {
      MessageActions.error('An error has occurred', this.props.dispatcher);
    }
  }

  private onClickConfirmRemove(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({
      confirmRemove: false,
    });
    CardPanelActions.removeCards(this.props.cardPage, this.props.dispatcher);
  }

  private onClickRemove(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({
      confirmRemove: true,
    });
  }

  private onClickEdit(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    if (this.props.cardPanel.get('cardPanelType') == CardPanelType.STAGING) return;

    CardPanelActions.editCardPanel(this.props.dispatcher);
  }

  private onClickClose(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    switch (this.props.cardPanel.get('cardPanelType')) {
      case CardPanelType.STAGING:
        InputSessionActions.closeCardPanel(this.props.dispatcher);
        break;
      default:
        CardPanelActions.closeCardPanel(this.props.dispatcher);
        break;
    }
  }

  private onClickCancel(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    if (this.props.cardPanel.get('cardPanelType') == CardPanelType.STAGING) return;

    CardPanelActions.cancelCardPanel(this.props.dispatcher);
  }

  private generateTitle(): string {
    const cardGroup = this.props.cardPanel.get('cardGroup');
    if (cardGroup.count() < 1) return '';

    let cardNames: Immutable.Set<string>;
    switch (this.props.cardPanel.get('game')) {
      case Game.MTG:
        cardNames = (cardGroup as MTGCardGroup).collect('cardName');
        break;
      case Game.POKEMON:
        cardNames = Immutable.Set(
          (cardGroup as PokeCardGroup)
            .getInstances()
            .map((instance: PokeCardInstance) => instance.get('card').get('name')),
        );
        break;
      case Game.YUGIOH:
        cardNames = Immutable.Set(
          (cardGroup as YugiCardGroup)
            .getInstances()
            .map((instance: YugiCardInstance) => instance.get('card').get('name')),
        );
        break;
      case Game.LORCANA:
        cardNames = Immutable.Set(
          (cardGroup as LorcanaCardGroup)
            .getInstances()
            .map((instance: LorcanaCardInstance) => instance.get('card').get('name')),
        );
        break;
    }
    if (cardNames.size > 1) {
      return 'Viewing ' + NumberFormat.commaSeparated(cardGroup.count()) + ' Cards';
    } else if (cardGroup.count() > 1) {
      return NumberFormat.commaSeparated(cardGroup.count()) + 'x ' + cardNames.first();
    } else {
      return cardNames.first();
    }
  }

  private attributesOverride(): CardAttributes | undefined {
    switch (this.props.cardPanel.get('cardPanelType')) {
      case CardPanelType.COLLECTION_EDIT:
        return this.props.cardPanel.get('unsavedCardAttributes');
      case CardPanelType.COLLECTION_VIEW:
      case CardPanelType.STAGING:
        return undefined;
    }
  }
}
