import * as React from 'react';

interface IProps {
  title: string;
  info: string;
}

export default class extends React.Component<IProps> {
  constructor(props: IProps) {
    super(props);
  }

  public render() {
    return (
      <div className="card-panel-content-info-container--small">
        <div className="card-panel-info-heading">{this.props.title}</div>
        <div className="card-panel-content-info">{this.props.info}</div>
      </div>
    );
  }
}
