import * as Immutable from 'immutable';
import * as React from 'react';
import { EnumHelper } from '../../../helpers/enum';
import { TabItem } from '../../../models/TabItem';
import { TabBar } from '../TabBar';

export enum DisplayMode {
  SOURCE = 'Source',
  SCAN = 'Scan',
}

interface IProps {
  mode: DisplayMode;
  onSwitchMode: (mode: DisplayMode) => void;
}

export const DisplayImageSwitch = (props: IProps) => {
  const [displayMode, setDisplayMode] = React.useState<DisplayMode>(props.mode);

  function switchMode(mode: DisplayMode) {
    setDisplayMode(mode);
    props.onSwitchMode(mode);
  }

  // Reset the internal state to the props when it changes
  React.useEffect(() => {
    setDisplayMode(props.mode);
  }, [props.mode]);

  const pages = EnumHelper.allCases(DisplayMode).map((title: string) => new TabItem({ title: title, disabled: false }));

  return (
    <>
      <TabBar
        pages={Immutable.List(pages)}
        selectedTab={displayMode}
        onChangeTab={(selectedPage: string) => switchMode(EnumHelper.match(DisplayMode, selectedPage) as DisplayMode)}
      />
    </>
  );
};
