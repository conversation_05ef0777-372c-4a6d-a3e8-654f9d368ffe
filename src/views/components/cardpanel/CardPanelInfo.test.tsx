import { faker } from '@faker-js/faker';
import { render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it } from 'vitest';
import CardPanelInfo from './CardPanelInfo';

const DEFAULT_PROPS = {
  title: faker.lorem.words(faker.number.int({ min: 1, max: 25 })),
  info: faker.lorem.words(faker.number.int({ min: 30, max: 100 })),
};
type CardPanelInfoProps = React.ComponentProps<typeof CardPanelInfo>;

function renderCardPanelInfo(rawProps: Partial<CardPanelInfoProps> = {}) {
  const props = { ...DEFAULT_PROPS, ...rawProps };
  return render(<CardPanelInfo {...props} />);
}
describe('CardPanelInfo', () => {
  describe('component render', () => {
    it('renders title and info correctly', () => {
      renderCardPanelInfo();
      expect(screen.getByText(DEFAULT_PROPS.title)).toBeInTheDocument();
      expect(screen.getByText(DEFAULT_PROPS.info)).toBeInTheDocument();
    });
    it('applies correct CSS classes', () => {
      const { container } = renderCardPanelInfo();
      expect(container.querySelector('.card-panel-content-info-container--small')).toBeTruthy();
      expect(container.querySelector('.card-panel-info-heading')).toBeTruthy();
      expect(container.querySelector('.card-panel-content-info')).toBeTruthy();
    });
  });
});
