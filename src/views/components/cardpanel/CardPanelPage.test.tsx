import { screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it } from 'vitest';
import { CardPanelType } from '../../../../src/models/CardPanels';
import { MTGCardGroup } from '../../../../src/models/mtg/MTGCardPage';
import { CardPanelPage } from '../../../../src/views/components/cardpanel/CardPanelPage';
import { create, FakeTag } from '../../../../tests/fake/Fake';
import { FakeCardPanelWithOptions, FakePokeCardGroupWithOptions } from '../../../../tests/fake/FakeCardData';
import { FakeUser } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { SupportedCurrencyCodes } from '../../../helpers/currency_helper';
import { Game } from '../../../models/Game';

const DEFAULT_PROPS = {
  me: create(FakeUser, { currency: SupportedCurrencyCodes.USD }),
  userTags: Immutable.OrderedSet([create(FakeTag)]),
  usernamePublic: false,
  cardPanel: create(FakeCardPanelWithOptions, { cardPanelType: CardPanelType.STAGING }),
};
type CardPanelPageProps = React.ComponentProps<typeof CardPanelPage>;

function renderCardPanelPage(props: Partial<CardPanelPageProps> = {}) {
  return renderWithDispatcher(CardPanelPage, { ...DEFAULT_PROPS, ...props });
}
describe('CardPanelPage', () => {
  describe('component render', () => {
    it('shows the foreign name correctly when game is MTG', () => {
      renderCardPanelPage();
      const foreignName = (DEFAULT_PROPS.cardPanel.get('cardGroup') as MTGCardGroup)
        .getInstances()
        .first()
        .get('foreignName');
      const headingForeginName = screen.getByText('Printed Name', { selector: '.card-panel-info-heading' });
      expect(headingForeginName).toBeInTheDocument();
      expect(
        headingForeginName.parentElement?.querySelector('.card-panel-content-info') as HTMLElement,
      ).toHaveTextContent(foreignName);
    });
    it('hides the foreign name when the game is not MTG', () => {
      renderCardPanelPage({
        cardPanel: create(FakeCardPanelWithOptions, {
          game: Game.POKEMON,
          cardGroup: create(FakePokeCardGroupWithOptions),
          cardPanelType: CardPanelType.STAGING,
        }),
      });
      expect(screen.queryByText('Printed Name', { selector: '.card-panel-info-heading' })).not.toBeInTheDocument();
    });
  });
  describe('cardPanelType', () => {
    describe('when STAGING', () => {
      it('renders CardPanelEditorBuilder', () => {
        renderCardPanelPage();
        expect(screen.queryByText('Tags')).not.toBeInTheDocument();
      });
    });

    describe('when COLLECTION_EDIT', () => {
      it('renders CardPanelEditorCollection', () => {
        const cardPanel = create(FakeCardPanelWithOptions, { cardPanelType: CardPanelType.COLLECTION_EDIT });
        renderCardPanelPage({ cardPanel });
        expect(screen.queryByText('Tags')?.closest('div.card-panel-info-heading')).toBeInTheDocument();
      });
    });
    describe('when COLLECTION_VIEW', () => {
      it('renders CardPanelContent', () => {
        const cardPanel = create(FakeCardPanelWithOptions, { cardPanelType: CardPanelType.COLLECTION_VIEW });
        renderCardPanelPage({ cardPanel });
        expect(screen.queryByText('Tags')?.closest('div.card-panel-info-heading')).toBeInTheDocument();
      });
    });
  });
});
