import * as Immutable from 'immutable';
import * as React from 'react';
import * as InputSessionActions from '../../../actions/InputSessionActions';
import * as MessageActions from '../../../actions/MessageActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { AnyCard } from '../../../models/AnyCard';
import { CardAttributes } from '../../../models/CardAttributes';
import { CardPanel as CardPanelModel, CardPanelType } from '../../../models/CardPanels';
import { User } from '../../../models/Users';
import { CardPanelEditorContent } from './CardPanelEditorPage';

interface IState {}

interface CPEBuilderProps {
  dispatcher: Dispatcher;
  me: User;
  cardPanel: CardPanelModel;
  showForeignName: boolean;
  cardNames: Immutable.Set<string>;
  priceLabel: string;
  timeAdded?: string;
}

export class CardPanelEditorBuilder extends React.Component<CPEBuilderProps, IState> {
  /**
   * @override
   */
  constructor(props: CPEBuilderProps) {
    super(props);
  }

  /**
   * @override
   */
  render() {
    return (
      <CardPanelEditorContent
        dispatcher={this.props.dispatcher}
        cardPanel={this.props.cardPanel}
        showForeignName={this.props.showForeignName}
        cardNames={this.props.cardNames}
        timeAdded={this.props.timeAdded}
        cardPanelType={CardPanelType.STAGING}
        onChangeAttributes={this.update.bind(this)}
        onChangeCard={this.changeCard.bind(this)}
        loadOptions={this.loadOptions.bind(this)}
        priceLabel={this.props.priceLabel}
      />
    );
  }

  private async update(attributes: CardAttributes) {
    const annotateScan = this.props.cardPanel.get('scanViewed');
    InputSessionActions.updateCards(
      this.props.cardPanel.get('game'),
      this.props.cardPanel.get('cardGroup'),
      attributes,
      annotateScan,
      this.props.dispatcher,
    ).catch((err) => {
      if (err && err.res && err.res.text && (err.res.text as string).includes('with language')) {
        MessageActions.error('Invalid language selection for card printing', this.props.dispatcher);
      } else {
        MessageActions.error('An error has occurred', this.props.dispatcher, err);
      }
    });
  }

  private async changeCard(card: AnyCard) {
    const annotateScan = this.props.cardPanel.get('scanViewed');
    InputSessionActions.changeCards(
      this.props.cardPanel.get('game'),
      this.props.cardPanel.get('cardGroup'),
      card,
      annotateScan,
      this.props.dispatcher,
    )
      .then(() => this.loadOptions(true))
      .catch((err) => {
        if (err && err.res && err.res.text && (err.res.text as string).includes('with language')) {
          MessageActions.error('Invalid language selection for card printing', this.props.dispatcher);
        } else {
          MessageActions.error('An error has occurred', this.props.dispatcher, err);
        }
      });
  }

  loadOptions = (force: boolean) => {
    // TODO: Move printing options loading to selector state - requires ensuring options are cached correctly
    if (!this.props.cardPanel.get('printingOptionsReady') || force) {
      InputSessionActions.populatePrintingDropDown(
        this.props.cardPanel.get('game'),
        this.props.cardPanel.get('cardGroup'),
        this.props.dispatcher,
      );
    }
  };
}
