import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import displayCurrency from '../../../helpers/currency_helper';
import { CardInstance } from '../../../models/CardInstances';
import { CardPanel as CardPanelModel, CardPanelType } from '../../../models/CardPanels';
import { Game } from '../../../models/Game';
import { LorcanaCardInstance } from '../../../models/lorcana/LorcanaCardInstances';
import { LorcanaCardGroup } from '../../../models/lorcana/LorcanaCardPage';
import { MTGCardGroup } from '../../../models/mtg/MTGCardPage';
import { PokeCardInstance } from '../../../models/pokemon/PokeCardInstances';
import { PokeCardGroup } from '../../../models/pokemon/PokeCardPage';
import { Tag } from '../../../models/Tags';
import { User } from '../../../models/Users';
import { YugiCardGroup } from '../../../models/yugioh/YugiCardPage';
import { CardPanelContent } from './CardPanelContent';
import { CardPanelEditorBuilder } from './CardPanelEditorBuilder';
import { CardPanelEditorCollection } from './CardPanelEditorCollection';

export interface CardPanelPageProps {
  dispatcher: Dispatcher;
  me: User;
  cardPanel: CardPanelModel;
  userTags: Immutable.Set<Tag>;
  usernamePublic: boolean;
}

export const CardPanelPage = (props: CardPanelPageProps) => {
  let cardGroup = props.cardPanel.get('cardGroup');
  let showForeignName: boolean;
  let cardNames: Immutable.Set<string>;

  switch (props.cardPanel.get('game')) {
    case Game.MTG:
      cardGroup = cardGroup as MTGCardGroup;
      const foreignNames = Immutable.Set<string>(
        cardGroup.getInstances().map((cardInstance: CardInstance) => cardInstance.get('foreignName')),
      );
      showForeignName =
        foreignNames.size == 1 && foreignNames.first() != cardGroup.getInstances().first().get('cardName');
      cardNames = Immutable.Set<string>(
        cardGroup.getInstances().map((cardInstance: CardInstance) => cardInstance.get('cardName')),
      );
      break;
    case Game.POKEMON:
      cardGroup = cardGroup as PokeCardGroup;
      showForeignName = false;
      cardNames = Immutable.Set<string>(
        cardGroup.getInstances().map((instance: PokeCardInstance) => instance.get('card').get('name')),
      );
      break;
    case Game.YUGIOH:
      cardGroup = cardGroup as YugiCardGroup;
      showForeignName = false;
      cardNames = Immutable.Set<string>(
        cardGroup.getInstances().map((instance: PokeCardInstance) => instance.get('card').get('name')),
      );
      break;
    case Game.LORCANA:
      cardGroup = cardGroup as LorcanaCardGroup;
      showForeignName = false;
      cardNames = Immutable.Set<string>(
        cardGroup.getInstances().map((instance: LorcanaCardInstance) => instance.get('card').get('name')),
      );
      break;
  }
  const price = cardGroup.priceSummary();
  const priceLabel = price
    ? displayCurrency(price, true, props.me.get('preferences').get('localization').get('currency'))
    : '-';

  const timeAdded = cardGroup.timeSummary(props.me.get('preferences').get('localization').get('timezone'));

  let pageContent: JSX.Element;
  switch (props.cardPanel.get('cardPanelType')) {
    case CardPanelType.STAGING:
      pageContent = (
        <CardPanelEditorBuilder
          dispatcher={props.dispatcher}
          me={props.me}
          showForeignName={showForeignName}
          cardNames={cardNames}
          timeAdded={timeAdded}
          cardPanel={props.cardPanel}
          priceLabel={priceLabel}
        />
      );
      break;
    case CardPanelType.COLLECTION_EDIT:
      pageContent = (
        <CardPanelEditorCollection
          dispatcher={props.dispatcher}
          me={props.me}
          showForeignName={showForeignName}
          cardNames={cardNames}
          timeAdded={timeAdded}
          cardPanel={props.cardPanel}
          userTags={props.userTags}
          priceLabel={priceLabel}
        />
      );
      break;
    case CardPanelType.COLLECTION_VIEW:
      pageContent = (
        <CardPanelContent
          dispatcher={props.dispatcher}
          me={props.me}
          showForeignName={showForeignName}
          cardNames={cardNames}
          timeAdded={timeAdded ? timeAdded : '-'}
          cardPanel={props.cardPanel}
          userTags={props.userTags}
          usernamePublic={props.usernamePublic}
          priceLabel={priceLabel}
        />
      );
      break;
  }

  return <div className="card-panel-content-body">{pageContent}</div>;
};
