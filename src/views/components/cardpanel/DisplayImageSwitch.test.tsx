import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect } from 'vitest';
import { DisplayImageSwitch, DisplayMode } from './DisplayImageSwitch';
const DEFAULT_PROPS = {
  mode: DisplayMode.SOURCE,
  onSwitchMode: vi.fn(),
};
type DisplayImageSwitchProps = React.ComponentProps<typeof DisplayImageSwitch>;
function renderDisplayImageSwitch(rawProps: Partial<DisplayImageSwitchProps> = {}) {
  const props = { ...DEFAULT_PROPS, ...rawProps };
  return render(<DisplayImageSwitch {...props} />);
}
describe('DisplayImageSwitch', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('TabBar', () => {
    it('renders both Source and Scan tab', () => {
      renderDisplayImageSwitch();
      expect(screen.getByText(DisplayMode.SOURCE)).toBeInTheDocument();
      expect(screen.getByText(DisplayMode.SCAN)).toBeInTheDocument();
    });

    it('calls onSwitchMode when tab is clicked', () => {
      renderDisplayImageSwitch();
      fireEvent.click(screen.getByText(DisplayMode.SOURCE));
      expect(DEFAULT_PROPS.onSwitchMode).toHaveBeenCalledWith(DisplayMode.SOURCE);
      fireEvent.click(screen.getByText(DisplayMode.SCAN));
      expect(DEFAULT_PROPS.onSwitchMode).toHaveBeenCalledWith(DisplayMode.SCAN);
    });
  });
  describe('when mode Source', () => {
    it('renders Source tab with class selected', () => {
      renderDisplayImageSwitch();
      const divElementForTabSource = screen.getByText(DisplayMode.SOURCE).parentElement as HTMLElement;
      expect(divElementForTabSource).toHaveClass('selected');
    });
    test('click Scan', () => {
      renderDisplayImageSwitch();
      fireEvent.click(screen.getByText(DisplayMode.SCAN));
      expect(DEFAULT_PROPS.onSwitchMode).toHaveBeenCalledWith(DisplayMode.SCAN);

      const divElementForTabSource = screen.getByText(DisplayMode.SOURCE).parentElement as HTMLElement;
      expect(divElementForTabSource).not.toHaveClass('selected');

      const divElementForTabScan = screen.getByText(DisplayMode.SCAN).parentElement as HTMLElement;
      expect(divElementForTabScan).toHaveClass('selected');
    });
  });
  describe('when mode Scan', () => {
    it('renders Scan tab with class selected', () => {
      renderDisplayImageSwitch({ mode: DisplayMode.SCAN });
      const divElementForTabScan = screen.getByText(DisplayMode.SCAN).parentElement as HTMLElement;
      expect(divElementForTabScan).toHaveClass('selected');
    });
    test('click Source tab', () => {
      renderDisplayImageSwitch({ mode: DisplayMode.SCAN });
      fireEvent.click(screen.getByText(DisplayMode.SOURCE));
      expect(DEFAULT_PROPS.onSwitchMode).toHaveBeenCalledWith(DisplayMode.SOURCE);

      const divElementForTabScan = screen.getByText(DisplayMode.SCAN).parentElement as HTMLElement;
      expect(divElementForTabScan).not.toHaveClass('selected');

      const divElementForTabSource = screen.getByText(DisplayMode.SOURCE).parentElement as HTMLElement;
      expect(divElementForTabSource).toHaveClass('selected');
    });
  });
});
