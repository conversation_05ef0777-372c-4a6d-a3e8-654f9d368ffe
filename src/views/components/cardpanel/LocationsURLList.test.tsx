import { faker } from '@faker-js/faker';
import { screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import 'vitest-dom/extend-expect';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as StackLocationAction from '../../../actions/StackLocationActions';
import { LinkedResources } from '../../../models/LinkedResources';
import { StackedCard } from '../../../models/StackedCard';
import { StackLocation } from '../../../models/StackLocation';
import { LocationURLList } from './LocationsURLList';

const testPath = 'Test/Path';
const mockGetAllInPathStackLocationAction = Immutable.List(
  testPath.split('/').map(
    (v) =>
      new StackLocation({
        name: v,
      }),
  ),
);

type LocationURLListProps = React.ComponentProps<typeof LocationURLList>;

function createResource(lengthResource: number): Record<string, LinkedResources> {
  return lengthResource
    ? Array.from({ length: lengthResource }).reduce<Record<string, LinkedResources>>(
        (acc, _, index) => ({
          ...acc,
          [`resource-${index + 1}`]: new LinkedResources({
            stackedCard: new StackedCard({
              locationUUID: faker.string.uuid(),
              position: index + 1,
            }),
          }),
        }),
        {},
      )
    : {};
}
function renderLocationURLList(props: Partial<LocationURLListProps> = {}) {
  return renderWithDispatcher(LocationURLList, { ...props });
}

describe('LocationURLList', () => {
  beforeEach(() => {
    vi.spyOn(StackLocationAction, 'getAllInPath').mockResolvedValue(mockGetAllInPathStackLocationAction);
    vi.clearAllMocks();
  });

  describe('when no resources provided', () => {
    it('renders nothing', () => {
      const { container } = renderLocationURLList();
      expect(container).toBeEmptyDOMElement();
    });
  });
  describe('when resources provided', () => {
    test('add single resource', async () => {
      // Note: Component needs to rerender to trigger componentDidUpdate
      // because state.locations is updated in componentDidUpdate callback
      const { rerenderWithDispatcher } = renderLocationURLList();
      const resources = createResource(1);
      const testUUID = resources['resource-1']?.get('stackedCard')?.get('locationUUID');
      rerenderWithDispatcher({
        resources: Immutable.Map<string, LinkedResources>(resources),
      });
      const link = await screen.findByRole('link');
      expect(screen.getByText('Location')).toBeInTheDocument();
      expect(link).toHaveAttribute('href', `/collection?locationUUID=${testUUID}`);
      expect(link).toHaveAttribute('target', '_blank');
      expect(link).toHaveClass('location-url');
      expect(screen.getByText(`${testPath}/1`)).toBeInTheDocument();
    });
    test('add multiple resource', async () => {
      // Note: Component needs to rerender to trigger componentDidUpdate
      // because state.locations is updated in componentDidUpdate callback
      const { rerenderWithDispatcher } = renderLocationURLList();
      const resources = createResource(3);
      rerenderWithDispatcher({
        resources: Immutable.Map<string, LinkedResources>(resources),
      });

      const link = await screen.findAllByRole('link');
      expect(screen.getByText('Locations')).toBeInTheDocument();
      expect(link).toHaveLength(1);
      expect(screen.getByText(`${testPath}/1`)).toBeInTheDocument();
      expect(screen.getByText(`and ${Object.keys(resources).length - 1} more`)).toBeInTheDocument();
    });
  });
});
