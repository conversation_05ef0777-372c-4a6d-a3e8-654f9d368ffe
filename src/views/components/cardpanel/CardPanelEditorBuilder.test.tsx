import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import { beforeEach, describe, expect, it, MockInstance, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { createFakeSuggestedCard, FakeCardPanelWithOptions } from '../../../../tests/fake/FakeCardData';
import { FakeUserWithOptions } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { typeInCardSearcher } from '../../../../tests/views/components/cardSearcher/helpers';
import * as InputSessionActions from '../../../actions/InputSessionActions';
import * as MessageActions from '../../../actions/MessageActions';
import * as CardsAPI from '../../../api/Cards';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { CardAttributes } from '../../../models/CardAttributes';
import { CardPanelType } from '../../../models/CardPanels';
import { Foil } from '../../../models/Foil';
import { CardPanelEditorBuilder } from './CardPanelEditorBuilder';

type Props = React.ComponentProps<typeof CardPanelEditorBuilder>;
const DEFAULT_PROPS: Omit<Props, 'dispatcher'> = {
  me: create(FakeUserWithOptions, { subscribed: true }),
  cardPanel: create(FakeCardPanelWithOptions, {
    // Set default foil to On so we can test the checkbox
    unsavedCardAttributes: new CardAttributes({
      foil: Foil.On,
    }),
    cardPanelType: CardPanelType.STAGING,
  }),
  cardNames: Immutable.Set([]),
  showForeignName: false,
  priceLabel: 'Custom Price Label',
  timeAdded: '2024-01-01',
};

const mockUpdateCards = vi.spyOn(InputSessionActions, 'updateCards');
const mockChangeCards = vi.spyOn(InputSessionActions, 'changeCards');
const mockpPopulatePrintingDropDown = vi.spyOn(InputSessionActions, 'populatePrintingDropDown');
const mockMessageError = vi.spyOn(MessageActions, 'error');
const mockCards = createFakeSuggestedCard(10);
const mockCardsSearch = vi.spyOn(CardsAPI, 'search').mockReturnValue({
  request: { abort: vi.fn() } as any,
  promise: Promise.resolve(Immutable.List(mockCards)),
});

function callChangeAttribute(spy: MockInstance, dispatcher: Dispatcher) {
  expect(spy).toHaveBeenCalledWith(
    DEFAULT_PROPS.cardPanel.get('game'),
    DEFAULT_PROPS.cardPanel.get('cardGroup'),
    new CardAttributes({ foil: Foil.Off }),
    DEFAULT_PROPS.cardPanel.get('scanViewed'),
    dispatcher,
  );
}
async function callChangeCard(spy: MockInstance, container: HTMLElement, dispatcher: Dispatcher) {
  fireEvent.click(screen.getByText('Change Card'));
  const searchTerm = mockCards[0].get('name').slice(0, 3);
  typeInCardSearcher(searchTerm);

  await waitFor(() => {
    expect(mockCardsSearch).toHaveBeenCalledWith(searchTerm, Immutable.Set());
  });

  const suggestion = container.querySelector('.react-select-cards__option') as HTMLElement;
  expect(suggestion).not.toBeNull();
  fireEvent.click(suggestion);
  await waitFor(() => {
    expect(spy).toHaveBeenCalledWith(
      DEFAULT_PROPS.cardPanel.get('game'),
      DEFAULT_PROPS.cardPanel.get('cardGroup'),
      mockCards[0],
      DEFAULT_PROPS.cardPanel.get('scanViewed'),
      dispatcher,
    );
  });
}
function renderCardPanelEditorBuilder(props: Partial<Props> = {}) {
  return renderWithDispatcher(CardPanelEditorBuilder, { ...DEFAULT_PROPS, ...props });
}
describe('CardPanelEditorBuilder', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('method update', () => {
    it('calls InputSessionActions.updateCards when attributes change', () => {
      const spy = mockUpdateCards.mockResolvedValue(undefined);
      const { dispatcher } = renderCardPanelEditorBuilder();
      const checkbox = screen.getByRole('checkbox');
      expect(checkbox).toBeInTheDocument();
      fireEvent.click(checkbox);
      callChangeAttribute(spy, dispatcher);
    });

    it('shows specific message', async () => {
      const err = { res: { text: 'something went wrong with language' } };
      const spy = mockUpdateCards.mockRejectedValue(err);
      const { dispatcher } = renderCardPanelEditorBuilder();
      const checkbox = screen.getByRole('checkbox');
      fireEvent.click(checkbox);
      await waitFor(() => {
        callChangeAttribute(spy, dispatcher);
      });
      expect(mockMessageError).toHaveBeenCalledExactlyOnceWith(
        'Invalid language selection for card printing',
        dispatcher,
      );
    });

    it('shows generic message', async () => {
      const err = { res: { text: 'something else' } };
      const spy = mockUpdateCards.mockRejectedValue(err);
      const { dispatcher } = renderCardPanelEditorBuilder();
      const checkbox = screen.getByRole('checkbox');
      fireEvent.click(checkbox);
      await waitFor(() => {
        callChangeAttribute(spy, dispatcher);
      });
      expect(mockMessageError).toHaveBeenCalledExactlyOnceWith('An error has occurred', dispatcher, err);
    });
  });

  describe('method changeCard', () => {
    it('calls InputSessionActions.changeCards then loadOptions on success', async () => {
      const changeSpy = mockChangeCards.mockResolvedValue(undefined);
      const { dispatcher, container } = renderCardPanelEditorBuilder();
      await callChangeCard(changeSpy, container, dispatcher);
      expect(mockpPopulatePrintingDropDown).toHaveBeenCalledWith(
        DEFAULT_PROPS.cardPanel.get('game'),
        DEFAULT_PROPS.cardPanel.get('cardGroup'),
        dispatcher,
      );
    });

    it('shows specific message', async () => {
      const err = { res: { text: '…with language…' } };
      const changeSpy = mockChangeCards.mockRejectedValue(err);
      const { dispatcher, container } = renderCardPanelEditorBuilder();
      await callChangeCard(changeSpy, container, dispatcher);

      expect(mockMessageError).toHaveBeenCalledExactlyOnceWith(
        'Invalid language selection for card printing',
        dispatcher,
      );
    });
    it('shows generic message', async () => {
      const err = { res: { text: 'something else' } };
      const changeSpy = mockChangeCards.mockRejectedValue(err);
      const { dispatcher, container } = renderCardPanelEditorBuilder();
      await callChangeCard(changeSpy, container, dispatcher);
      expect(mockMessageError).toHaveBeenCalledExactlyOnceWith('An error has occurred', dispatcher, err);
    });
  });
});
