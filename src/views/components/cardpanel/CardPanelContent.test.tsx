import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import {
  FakeCardPanelWithOptions,
  FakeMTGCardGroupWithOptions,
  FakePokeCardGroupWithOptions,
} from '../../../../tests/fake/FakeCardData';
import { FakeUserWithOptions } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as CardsAPI from '../../../api/Cards';
import * as DecksAPI from '../../../api/Decks';
import { CardPanel } from '../../../models/CardPanels';
import { Deck } from '../../../models/Decks';
import { Game } from '../../../models/Game';
import { LinkedResources } from '../../../models/LinkedResources';
import { MTGCardGroup } from '../../../models/mtg/MTGCardPage';
import { Tag } from '../../../models/Tags';
import { CardPanelContent } from './CardPanelContent';

type CardPanelContentProps = React.ComponentProps<typeof CardPanelContent>;

const resultLinkedResource = {
  deck: new LinkedResources({ deck: new Deck({ name: 'Existing Deck' }) }),
};
const mockGetlinkResource = vi
  .spyOn(CardsAPI, 'getLinkedResources')
  .mockResolvedValue(Immutable.Map(resultLinkedResource));

const newDeckFromSelection = vi
  .spyOn(DecksAPI, 'newDeckFromSelection')
  .mockResolvedValue(new Deck({ name: 'New Deck' }));

const DEFAULT_PROPS: Omit<CardPanelContentProps, 'dispatcher'> = {
  me: create(FakeUserWithOptions, {
    subscribed: true,
  }),
  cardPanel: create(FakeCardPanelWithOptions),
  usernamePublic: false,
  cardNames: Immutable.Set(['Card 1']),
  showForeignName: false,
  priceLabel: 'Custom Price Label',
  timeAdded: '2024-01-01',
  userTags: Immutable.OrderedSet([new Tag({ name: 'user-tag' })]),
};

function renderCardPanelContent(props: Partial<CardPanelContentProps> = {}) {
  return renderWithDispatcher(CardPanelContent, { ...DEFAULT_PROPS, ...props });
}

function getForeignName(cardPanel: CardPanel) {
  return (cardPanel.get('cardGroup') as MTGCardGroup).getInstances().first().get('foreignName');
}

describe('CardPanelContent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  describe('component render', () => {
    it('shows empty message if no cards are present', () => {
      renderCardPanelContent({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardGroup: new MTGCardGroup(),
        }),
      });
      expect(screen.getByText('There are no cards in your selection.')).toBeInTheDocument();
    });
    it('shows multiple card names', () => {
      const cardNames = ['Card 1', 'Card 2', 'Card 3'];
      renderCardPanelContent({
        cardNames: Immutable.Set(cardNames),
      });
      expect(screen.getByText('Card Names')).toBeInTheDocument();
      expect(screen.getByText(`${cardNames[0]}, and ${cardNames.length - 1} more`)).toBeInTheDocument();
    });

    it('shows single setName', () => {
      const cardGroup = create(FakeMTGCardGroupWithOptions);
      renderCardPanelContent({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardGroup: cardGroup,
        }),
      });
      expect(screen.getByText('Set')).toBeInTheDocument();
      expect(screen.getByText(`${cardGroup.getInstances().first().get('cardSetName')}`)).toBeInTheDocument();
    });
    it('shows multiple setName', () => {
      const cardInstancesLength = 5;
      const cardGroup = create(FakeMTGCardGroupWithOptions, {
        cardInstancesLength: cardInstancesLength,
      });
      renderCardPanelContent({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardGroup: cardGroup,
        }),
      });
      expect(screen.getByText('Sets')).toBeInTheDocument();
      expect(screen.getByText(`${cardGroup.getInstances().first().get('cardSetName')}`)).toBeInTheDocument();
      expect(screen.getByText(`and ${cardInstancesLength - 1} more`)).toBeInTheDocument();
    });

    it('shows price label', () => {
      renderCardPanelContent();
      expect(screen.getByText(DEFAULT_PROPS.priceLabel)).toBeInTheDocument();
    });
    it('shows price timeAdded', async () => {
      renderCardPanelContent();
      expect(screen.getByText(DEFAULT_PROPS.timeAdded)).toBeInTheDocument();
    });

    it('disables tag management when usernamePublic is true', () => {
      renderCardPanelContent({
        usernamePublic: true,
      });
      expect(screen.queryByRole('textbox')).not.toBeInTheDocument();
    });
    it('shows single collector number', () => {
      const cardGroup = create(FakeMTGCardGroupWithOptions);
      renderCardPanelContent({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardGroup: cardGroup,
        }),
      });
      expect(screen.getByText('Collector Number')).toBeInTheDocument();
      expect(screen.getByText(`${cardGroup.getInstances().first().get('cardCollectorNumber')}`)).toBeInTheDocument();
    });
    it('shows multiple collector numbers', () => {
      const cardInstancesLength = 5;
      const cardGroup = create(FakeMTGCardGroupWithOptions, {
        cardInstancesLength: cardInstancesLength,
      });
      renderCardPanelContent({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardGroup: cardGroup,
        }),
      });
      expect(screen.getByText('Collector Numbers')).toBeInTheDocument();
      expect(
        screen.getByText(
          `${cardGroup.getInstances().first().get('cardCollectorNumber')}, and ${cardInstancesLength - 1} more`,
        ),
      ).toBeInTheDocument();
    });
  });
  describe('when game is MTG', () => {
    it('shows foreign name when showForeignName', () => {
      const foreignName = getForeignName(DEFAULT_PROPS.cardPanel);
      renderCardPanelContent({
        showForeignName: true,
      });
      expect(screen.getByText(foreignName)).toBeInTheDocument();
    });
    it('call CardsAPI.getLinkedResources when componentDidMount', async () => {
      renderCardPanelContent();
      await waitFor(() => {
        expect(mockGetlinkResource).toHaveBeenCalledExactlyOnceWith(
          DEFAULT_PROPS.cardPanel.get('cardGroup').getInstances(),
        );
      });
    });

    it('shows dialog when button Create Deck is clicked', async () => {
      renderCardPanelContent();

      await waitFor(() => {
        expect(mockGetlinkResource).toHaveBeenCalledExactlyOnceWith(
          DEFAULT_PROPS.cardPanel.get('cardGroup').getInstances(),
        );
      });

      fireEvent.click(screen.getByText('Create Deck'));
      expect(screen.getByText('Are you sure?')).toBeInTheDocument();
      expect(screen.getByText('This card will be unlinked from its current deck.')).toBeInTheDocument();
    });
    it('hides dialog when dismiss button is clicked', async () => {
      const { container } = renderCardPanelContent();
      await waitFor(() => {
        expect(mockGetlinkResource).toHaveBeenCalledExactlyOnceWith(
          DEFAULT_PROPS.cardPanel.get('cardGroup').getInstances(),
        );
      });
      fireEvent.click(screen.getByText('Create Deck'));
      expect(screen.queryByText('Are you sure?')).toBeInTheDocument();
      fireEvent.click(container.querySelector('.dialog-dismiss') as HTMLElement);
      expect(screen.queryByText('Are you sure?')).not.toBeInTheDocument();
    });

    it('unlinks deck when confirming dialog', async () => {
      renderCardPanelContent();
      await waitFor(() => {
        expect(mockGetlinkResource).toHaveBeenCalledExactlyOnceWith(
          DEFAULT_PROPS.cardPanel.get('cardGroup').getInstances(),
        );
      });
      fireEvent.click(screen.getByText('Create Deck'));
      fireEvent.click(screen.getByText('Unlink'));
      expect(newDeckFromSelection).toHaveBeenCalledExactlyOnceWith(
        DEFAULT_PROPS.cardPanel.get('cardGroup').getInstances(),
      );
    });
  });
  describe('when game is not MTG', () => {
    it('shows first cardInstance name when showForeignName', () => {
      const cardGroup = create(FakePokeCardGroupWithOptions);
      const cardPanel = create(FakeCardPanelWithOptions, {
        game: Game.POKEMON,
        cardGroup,
      });
      renderCardPanelContent({
        showForeignName: true,
        cardPanel,
      });
      expect(screen.getByText(cardGroup.get('cardInstances').first().get('card').get('name'))).toBeInTheDocument();
    });
    it('does not shows dialog when button Create Deck is clicked', async () => {
      const cardGroup = create(FakePokeCardGroupWithOptions);
      const cardPanel = create(FakeCardPanelWithOptions, {
        game: Game.POKEMON,
        cardGroup,
      });
      renderCardPanelContent({
        cardPanel,
      });
      fireEvent.click(screen.getByText('Create Deck'));
      expect(screen.queryByText('Are you sure?')).not.toBeInTheDocument();
    });
  });
});
