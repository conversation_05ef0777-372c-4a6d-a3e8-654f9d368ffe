import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardsAPI from '../../../api/Cards';
import * as DecksAPI from '../../../api/Decks';
import * as HasMe from '../../../containers/HasMe';
import * as HasSubscription from '../../../containers/HasSubscription';
import Dispatcher from '../../../dispatcher/Dispatcher';
import history from '../../../helpers/history';
import { CardFormAttributes } from '../../../models/CardAttributes';
import { CardInstance } from '../../../models/CardInstances';
import { CardPanel as CardPanelModel } from '../../../models/CardPanels';
import { CartLocation } from '../../../models/CartButton';
import { CartCardRecord } from '../../../models/CartCards';
import { Condition } from '../../../models/Condition';
import { Deck } from '../../../models/Decks';
import { Foil } from '../../../models/Foil';
import { Game } from '../../../models/Game';
import { Language, LanguageName } from '../../../models/Language';
import { LinkedResources } from '../../../models/LinkedResources';
import { LorcanaCardInstance } from '../../../models/lorcana/LorcanaCardInstances';
import { LorcanaCardGroup } from '../../../models/lorcana/LorcanaCardPage';
import { MTGCardGroup } from '../../../models/mtg/MTGCardPage';
import { PokeCardInstance } from '../../../models/pokemon/PokeCardInstances';
import { PokeCardGroup } from '../../../models/pokemon/PokeCardPage';
import { SetIconInfo, setIconsFromCardGroup } from '../../../models/SetIconInfo';
import { Tag } from '../../../models/Tags';
import { User } from '../../../models/Users';
import { YugiCardGroup } from '../../../models/yugioh/YugiCardPage';
import { CartButton } from '../CartButton';
import { Checkbox } from '../Checkbox';
import { Dialog } from '../Dialog';
import { SetList } from '../SetList';
import { TagsBuilder } from '../TagsBuilder';
import { TruncatedText } from '../TruncatedText';
import CardPanelInfo from './CardPanelInfo';

interface IState {
  showDeckChangeDialog?: boolean;
  attributes: CardFormAttributes;
  resources?: Immutable.Map<string, LinkedResources>;
}

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  cardPanel: CardPanelModel;
  usernamePublic: boolean;
  cardNames: Immutable.Set<string>;
  showForeignName: boolean;
  priceLabel: string;
  timeAdded: string;
  userTags: Immutable.OrderedSet<Tag>;
}

//
export const CardPanelContent = HasMe.Attach<IProps>(
  HasSubscription.Attach<IProps & HasMe.IProps>(
    class extends React.Component<IProps & HasSubscription.IProps & HasMe.IProps, IState> {
      /**
       * @override
       */
      constructor(props: IProps & HasSubscription.IProps & HasMe.IProps) {
        super(props);
        this.state = { attributes: this.resolveState(props) };
      }

      /**
       * @override
       */
      componentDidMount() {
        if (!this.props.usernamePublic) {
          this.resolveResources(this.props);
        }
      }

      /**
       *
       */
      componentDidUpdate(prevProps: IProps) {
        if (
          !prevProps.cardPanel.get('cardGroup').equals(this.props.cardPanel.get('cardGroup')) &&
          !this.props.usernamePublic
        ) {
          this.setState({ attributes: this.resolveState(this.props) }, () => {
            this.resolveResources(this.props);
          });
        }
      }

      /**
       * @override
       */
      render() {
        let cardGroup = this.props.cardPanel.get('cardGroup');
        let foreignName: string | undefined;
        let collectorNumbers: Immutable.Set<string>;
        let setNames: Immutable.Set<string>;
        let setIcons: Immutable.Set<SetIconInfo>;

        if (!cardGroup.count()) {
          return (
            <div className="card-panel-content" style={{ width: '100%', height: '100%', margin: '0rem' }}>
              <div className="flex align-center justify-center" style={{ width: '100%', height: '100%' }}>
                <span className="lato-N4">There are no cards in your selection.</span>
              </div>
            </div>
          );
        }

        switch (this.props.cardPanel.get('game')) {
          case Game.MTG:
            cardGroup = cardGroup as MTGCardGroup;
            foreignName = cardGroup.getInstances().first().get('foreignName');
            collectorNumbers = Immutable.Set<string>(
              cardGroup
                .getInstances()
                .map((ci: CardInstance) => ci.get('cardCollectorNumber'))
                .filter((cn: string) => cn !== null),
            );
            setNames = Immutable.Set<string>(
              cardGroup
                .getInstances()
                .map((ci: CardInstance) => ci.get('cardSetName'))
                .filter((cn: string) => cn !== null),
            );
            setIcons = setIconsFromCardGroup(this.props.cardPanel.get('game'), cardGroup);
            break;
          case Game.POKEMON:
            cardGroup = cardGroup as PokeCardGroup;
            foreignName = cardGroup.getInstances().first().get('card').get('name');
            collectorNumbers = Immutable.Set<string>(
              cardGroup
                .getInstances()
                .map((ci: PokeCardInstance) => ci.get('card').get('collectorNumber'))
                .filter((cn: string) => cn !== null),
            );
            setNames = Immutable.Set<string>(
              cardGroup
                .getInstances()
                .map((ci: PokeCardInstance) => ci.get('card').get('setName'))
                .filter((cn: string) => cn !== null),
            );
            setIcons = setIconsFromCardGroup(this.props.cardPanel.get('game'), cardGroup);
            break;
          case Game.YUGIOH:
            cardGroup = cardGroup as YugiCardGroup;
            foreignName = cardGroup.getInstances().first().get('card').get('name');
            collectorNumbers = Immutable.Set<string>(
              cardGroup
                .getInstances()
                .map((ci: PokeCardInstance) => ci.get('card').get('collectorNumber'))
                .filter((cn: string) => cn !== null),
            );
            setNames = Immutable.Set<string>(
              cardGroup
                .getInstances()
                .map((ci: PokeCardInstance) => ci.get('card').get('setName'))
                .filter((cn: string) => cn !== null),
            );
            setIcons = setIconsFromCardGroup(this.props.cardPanel.get('game'), cardGroup);
            break;
          case Game.LORCANA:
            cardGroup = cardGroup as LorcanaCardGroup;
            foreignName = cardGroup.getInstances().first().get('card').get('name');
            collectorNumbers = Immutable.Set<string>(
              cardGroup
                .getInstances()
                .map((ci: LorcanaCardInstance) => ci.get('card').get('collectorNumber'))
                .filter((cn: string) => cn !== null),
            );
            setNames = Immutable.Set<string>(
              cardGroup
                .getInstances()
                .map((ci: LorcanaCardInstance) => ci.get('card').get('setName'))
                .filter((cn: string) => cn !== null),
            );
            setIcons = setIconsFromCardGroup(this.props.cardPanel.get('game'), cardGroup);
            break;
        }

        const isMerchant = this.props.subscription.isMerchant();
        let deckNames = Immutable.List<string>();
        if (this.state.resources !== undefined) {
          deckNames = this.state.resources
            .filterNot((resources: LinkedResources) => {
              return resources.get('deck') === undefined;
            })
            .map((resources: LinkedResources) => {
              return (resources.get('deck') as Deck).get('name');
            })
            .toSet()
            .toList();
        }
        return (
          <>
            <Dialog
              isOpen={this.state.showDeckChangeDialog || false}
              onDismiss={() => {
                this.setState({ showDeckChangeDialog: false });
              }}
            >
              <h3 className="query-dialog-heading">Are you sure?</h3>
              <p className="query-dialog-subheading" style={{ whiteSpace: 'normal' }}>
                {(cardGroup.count() > 1
                  ? 'These cards will be unlinked from their'
                  : 'This card will be unlinked from its') + ' current deck.'}
              </p>
              <div className="flex justify-center">
                <input
                  type="submit"
                  className="button-alert"
                  value="Unlink"
                  autoComplete="off"
                  onClick={this.onClickConfirmDeckChange.bind(this)}
                />
              </div>
            </Dialog>
            {this.props.cardNames.size > 1 ? (
              <div className="card-panel-content-info-container--small">
                <div className="card-panel-info-heading" data-testid="cpc-card-names">
                  Card Names
                </div>
                <TruncatedText listOfStrings={this.props.cardNames.toList()}></TruncatedText>
              </div>
            ) : null}
            {this.props.showForeignName ? <CardPanelInfo title="Printed Name" info={foreignName} /> : null}
            <div className="card-panel-content-info-container--small">
              <div className="card-panel-info-heading" data-testid="cpc-set-sets">
                {setNames.size > 1 ? 'Sets' : 'Set'}
              </div>
              <SetList game={this.props.cardPanel.get('game')} setIcons={setIcons}></SetList>
            </div>
            {/*collectorNumbers.size > 0 checks for cases when MTGJSON has not provided a set number for the card(s)*/}
            {this.props.cardNames.size === 1 && collectorNumbers.size > 0 ? (
              <div className="card-panel-content-info-container--small">
                <div className="card-panel-info-heading" data-testid="cpc-collector">
                  {collectorNumbers.size > 1 ? 'Collector Numbers' : 'Collector Number'}
                </div>
                <TruncatedText listOfStrings={collectorNumbers.toList()}></TruncatedText>
              </div>
            ) : null}
            <div className="container">
              <div className="row">
                <div className="col-md-4" style={{ flex: 'auto', padding: '0' }} data-testid="cpc-price-parent">
                  <CardPanelInfo title={cardGroup.count() > 1 ? 'Total Price' : 'Price'} info={this.props.priceLabel} />
                </div>
                {this.props.timeAdded ? (
                  <div className="col-md-4" style={{ flex: 'auto' }} data-testid="cpc-time-parent">
                    <CardPanelInfo title={'Added'} info={this.props.timeAdded} />
                  </div>
                ) : null}
              </div>
              <div>
                <div className="row">
                  <div className="col-md-4" style={{ flex: 'auto', padding: '0' }}>
                    <div className="card-panel-info-heading">Foil</div>
                    <Checkbox
                      disabled
                      checked={this.state.attributes.get('foil') === Foil.On}
                      multiple={this.state.attributes.get('foil') === Foil.Partial}
                    />
                  </div>
                  <div className="col-md-4" style={{ flex: 'auto' }}>
                    <CardPanelInfo title="Condition" info={this.state.attributes.get('condition')} />
                  </div>
                  <div className="col-md-4" style={{ flex: 'auto' }}>
                    <CardPanelInfo
                      title={'Language'}
                      info={
                        this.state.attributes.get('language') === 'Multiple'
                          ? 'Multiple'
                          : LanguageName(this.state.attributes.get('language'))
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="card-panel-content-info-container--large">
              <div className="card-panel-info-heading" style={{ marginBottom: '0.5rem' }}>
                Tags
              </div>
              <TagsBuilder
                immutable
                dispatcher={this.props.dispatcher}
                me={this.props.me}
                userTags={this.props.userTags}
                tags={this.props.cardPanel.get('tags')}
                onAdd={() => {}}
                onRemove={() => {}}
                publicViewing={this.props.usernamePublic}
                placeholder={
                  this.props.usernamePublic
                    ? 'No tags'
                    : "Click 'Edit' to add tags to " + (cardGroup.count() === 1 ? 'this card' : 'these cards')
                }
              />
            </div>
            {this.props.usernamePublic ? null : (
              <>
                {deckNames.size > 0 ? (
                  <div className="card-panel-content-info-container--small">
                    <div className="card-panel-info-heading">{deckNames.size === 1 ? 'Deck ' : 'Decks'}</div>
                    <TruncatedText listOfStrings={deckNames}></TruncatedText>
                  </div>
                ) : null}
                {/* TODO: Re-enable locations display -> <LocationURLList dispatcher={this.props.dispatcher} resources={this.state.resources} /> */}
              </>
            )}
            {(cardGroup.count() < 121 && !this.props.usernamePublic) || !isMerchant ? (
              <div className="card-panel-content-info-container--small">
                <div className="card-panel-info-heading">Actions</div>
                <div className="flex" style={{ marginTop: '1rem' }}>
                  {cardGroup.count() < 121 && !this.props.usernamePublic ? (
                    <button
                      className="button-primary create-deck-selection"
                      onClick={this.onClickCreateDeck}
                      style={{ marginRight: '0.5rem' }}
                    >
                      Create Deck
                    </button>
                  ) : null}
                  <CartButton
                    cartData={CartCardRecord.mapFromCardInstances(this.props.cardPanel.get('game'), cardGroup)}
                    location={CartLocation.COLLECTION}
                    isMerchant={isMerchant}
                    dispatcher={this.props.dispatcher}
                  />
                </div>
              </div>
            ) : null}
          </>
        );
      }

      private onClickCreateDeck = (evt: React.SyntheticEvent<HTMLElement>) => {
        evt.preventDefault();

        const alreadyLinkedToDeck =
          this.state.resources &&
          this.state.resources.some((resources: LinkedResources) => resources.get('deck') !== undefined);

        if (alreadyLinkedToDeck) {
          this.setState({
            showDeckChangeDialog: true,
          });
        } else {
          this.createDeck();
        }
      };

      private onClickConfirmDeckChange(evt: React.SyntheticEvent<HTMLElement>) {
        evt.preventDefault();
        this.createDeck();
      }

      private createDeck() {
        switch (this.props.cardPanel.get('game')) {
          case Game.MTG:
            const cardGroup = this.props.cardPanel.get('cardGroup') as MTGCardGroup;
            DecksAPI.newDeckFromSelection(cardGroup.getInstances()).then((deck) => {
              history.push(`/decks/${deck.get('uuid')}`);
            });
            break;
          case Game.POKEMON:
          case Game.YUGIOH:
          case Game.LORCANA:
            break;
        }
      }

      private resolveState(props: IProps): CardFormAttributes {
        let cardGroup = props.cardPanel.get('cardGroup');

        switch (this.props.cardPanel.get('game')) {
          case Game.MTG:
            cardGroup = cardGroup as MTGCardGroup;
            return new CardFormAttributes({
              foil: cardGroup.groupValue('foil', Foil.Partial, Foil.Off),
              condition: cardGroup.groupValue('condition', 'Multiple', Condition.NEAR_MINT),
              language: cardGroup.groupValue('language', 'Multiple', Language.ENGLISH),
            });
          case Game.POKEMON:
            cardGroup = cardGroup as PokeCardGroup;
            return new CardFormAttributes({
              foil: Foil.Off,
              condition: cardGroup.groupValue('condition', 'Multiple', Condition.NEAR_MINT),
              language: Language.ENGLISH,
            });
          case Game.YUGIOH:
            cardGroup = cardGroup as YugiCardGroup;
            return new CardFormAttributes({
              foil: Foil.Off,
              condition: cardGroup.groupValue('condition', 'Multiple', Condition.NEAR_MINT),
              language: Language.ENGLISH,
            });
          case Game.LORCANA:
            cardGroup = cardGroup as LorcanaCardGroup;
            return new CardFormAttributes({
              foil: Foil.Off,
              condition: cardGroup.groupValue('condition', 'Multiple', Condition.NEAR_MINT),
              language: Language.ENGLISH,
            });
        }
      }

      private async resolveResources(props: IProps) {
        let cardGroup = props.cardPanel.get('cardGroup');
        if (cardGroup.count() > 0) {
          switch (this.props.cardPanel.get('game')) {
            case Game.MTG:
              cardGroup = cardGroup as MTGCardGroup;
              const resources = await CardsAPI.getLinkedResources(cardGroup.getInstances());
              this.setState({
                resources: resources,
              });
              break;
            case Game.POKEMON:
            case Game.YUGIOH:
            case Game.LORCANA:
              break;
          }
        }
      }
    },
  ),
);
