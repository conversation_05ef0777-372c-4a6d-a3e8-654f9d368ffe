import * as Immutable from 'immutable';
import * as React from 'react';
import { OptionsType } from 'react-select';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { AnyCard } from '../../../models/AnyCard';
import { CardAttributes, CardFormAttributes } from '../../../models/CardAttributes';
import { CardPanel as CardPanelModel, CardPanelType } from '../../../models/CardPanels';
import { Condition } from '../../../models/Condition';
import { Foil } from '../../../models/Foil';
import { Game } from '../../../models/Game';
import { Language, SortLanguages } from '../../../models/Language';
import { LorcanaCardGroup } from '../../../models/lorcana/LorcanaCardPage';
import { LorcanaPrinting } from '../../../models/lorcana/LorcanaPrinting';
import { MTGCardGroup } from '../../../models/mtg/MTGCardPage';
import { MTGPrinting } from '../../../models/mtg/MTGPrinting';
import { PokeCardGroup } from '../../../models/pokemon/PokeCardPage';
import { PokeFinish } from '../../../models/pokemon/PokeFinish';
import { PokePrinting } from '../../../models/pokemon/PokePrinting';
import { PricingSource } from '../../../models/PricingSource';
import { AnyPrinting } from '../../../models/Printing';
import { YugiCardGroup } from '../../../models/yugioh/YugiCardPage';
import { YugiPrinting } from '../../../models/yugioh/YugiPrinting';
import { Checkbox } from '../Checkbox';
import { PrintingSuggestion } from '../PrintingSuggestionItem';
import { ConditionSelector } from '../selectors/ConditionSelector';
import { LanguageSelector } from '../selectors/LanguageSelector';
import { LorcanaFinishSelector } from '../selectors/LorcanaFinishSelector';
import { PokeFinishSelector } from '../selectors/PokeFinishSelector';
import { PrintingSelector } from '../selectors/PrintingSelector';
import { TruncatedText } from '../TruncatedText';
import CardPanelInfo from './CardPanelInfo';

interface CPEContentProps {
  dispatcher: Dispatcher;
  cardPanel: CardPanelModel;
  cardNames: Immutable.Set<string>;
  showForeignName: boolean;
  priceLabel: string;
  cardPanelType: CardPanelType;
  loadOptions: () => void;
  onChangeAttributes: (attributes: CardAttributes) => void;
  onChangeCard: (card: AnyCard) => void;
  timeAdded?: string;
}

interface IState {
  attributes: CardFormAttributes;
  isChangingCard: boolean;
}

export class CardPanelEditorContent extends React.Component<CPEContentProps, IState> {
  constructor(props: CPEContentProps) {
    super(props);
    this.state = {
      isChangingCard: false,
      attributes: this.groupAttributes(),
    };
  }

  groupAttributes(props: CPEContentProps = this.props): CardFormAttributes {
    let cardGroup = props.cardPanel.get('cardGroup');

    // Will not be set when on the staging page as edits are applied live
    const unsaved = props.cardPanel.get('unsavedCardAttributes');

    switch (props.cardPanel.get('game')) {
      case Game.MTG:
        cardGroup = cardGroup as MTGCardGroup;
        return new CardFormAttributes({
          foil:
            unsaved.get('foil') === undefined
              ? cardGroup.groupValue('foil', Foil.Partial, Foil.Off)
              : unsaved.get('foil'),
          condition: unsaved.get('condition') || cardGroup.groupValue('condition', 'Multiple', Condition.NEAR_MINT),
          language: unsaved.get('language') || cardGroup.groupValue('language', 'Multiple', Language.ENGLISH),
        });
      case Game.POKEMON:
        cardGroup = cardGroup as PokeCardGroup;
        return new CardFormAttributes({
          foil: unsaved.get('foil') === undefined ? Foil.Off : unsaved.get('foil'),
          condition: unsaved.get('condition') || cardGroup.groupValue('condition', 'Multiple', Condition.NEAR_MINT),
          language: unsaved.get('language') || Language.ENGLISH,
          pokeFinish: unsaved.get('pokeFinish') || cardGroup.groupValue('finish', 'Multiple', new PokeFinish()),
        });
      case Game.YUGIOH:
        cardGroup = cardGroup as YugiCardGroup;
        return new CardFormAttributes({
          foil: unsaved.get('foil') === undefined ? Foil.Off : unsaved.get('foil'),
          condition: unsaved.get('condition') || cardGroup.groupValue('condition', 'Multiple', Condition.NEAR_MINT),
          language: unsaved.get('language') || Language.ENGLISH,
        });
      case Game.LORCANA:
        cardGroup = cardGroup as LorcanaCardGroup;
        return new CardFormAttributes({
          foil: unsaved.get('foil') === undefined ? Foil.Off : unsaved.get('foil'),
          condition: unsaved.get('condition') || cardGroup.groupValue('condition', 'Multiple', Condition.NEAR_MINT),
          language: unsaved.get('language') || Language.ENGLISH,
          lorcanaFinish: unsaved.get('pokeFinish') || cardGroup.groupValue('finish', 'Multiple', new PokeFinish()),
        });
    }
  }

  // Attributes can be changed from CardPanel - override state
  componentDidUpdate(prevProps: Readonly<CPEContentProps>, prevState: Readonly<IState>, _snapshot?: any): void {
    if (!prevState.attributes.equals(this.groupAttributes())) {
      this.setState({
        attributes: this.groupAttributes(),
      });
    }
  }

  onUpdateFoil(value: boolean) {
    const newFoil = value ? Foil.On : Foil.Off;
    this.setState({ attributes: this.state.attributes.set('foil', newFoil) });
    this.props.onChangeAttributes(new CardAttributes({ foil: newFoil }));
  }

  onUpdateCondition(condition: Condition) {
    this.setState({ attributes: this.state.attributes.set('condition', condition) });
    this.props.onChangeAttributes(new CardAttributes({ condition: condition }));
  }

  onUpdateLanguage(language: Language) {
    this.setState({ attributes: this.state.attributes.set('language', language) });
    this.props.onChangeAttributes(new CardAttributes({ language: language }));
  }

  onUpdatePokeFinish(finish: PokeFinish) {
    this.setState({ attributes: this.state.attributes.set('pokeFinish', finish) });
    this.props.onChangeAttributes(new CardAttributes({ pokeFinish: finish }));
  }

  onChangeCard(isChangingCard: boolean) {
    this.setState({ isChangingCard: isChangingCard });
  }

  render() {
    let cardGroup = this.props.cardPanel.get('cardGroup');

    if (cardGroup.count() < 1) {
      console.error('internal error: a CardPanelEditor should never have zero card instances');
      return null;
    }

    let foreignName: string | undefined;
    let options: OptionsType<PrintingSuggestion> = [];
    let currentPrinting: AnyPrinting;
    let currentLanguage: Language | 'Multiple';

    const game = this.props.cardPanel.get('game');

    // Will not be set when on the staging page as edits are applied live
    const unsaved = this.props.cardPanel.get('unsavedCardAttributes');

    switch (game) {
      case Game.MTG:
        cardGroup = cardGroup as MTGCardGroup;
        foreignName = cardGroup.getInstances().first().get('foreignName');
        currentLanguage = unsaved.get('language') || cardGroup.groupValue('language', 'Multiple', Language.ENGLISH);
        const jsonID = unsaved.get('printing') || cardGroup.groupValue('cardJsonID', 'Multiple', '');
        currentPrinting = cardGroup.selectedPrinting(
          jsonID,
          SortLanguages(Immutable.Set(this.props.cardPanel.get('languageOptions'))),
        );
        options = cardGroup
          .get('printingOptions')
          .map((printing: MTGPrinting) => new PrintingSuggestion(game, printing))
          .toJS();
        break;
      case Game.POKEMON:
        cardGroup = cardGroup as PokeCardGroup;
        foreignName = cardGroup.getInstances().first().get('card').get('name');
        currentLanguage = Language.ENGLISH;
        const pokeUUID = unsaved.get('printing') || cardGroup.cardValues('uuid', 'Multiple', '');
        currentPrinting = cardGroup.selectedPrinting(
          pokeUUID,
          SortLanguages(Immutable.Set(this.props.cardPanel.get('languageOptions'))),
        );

        options = cardGroup
          .get('printingOptions')
          .map((printing: PokePrinting) => new PrintingSuggestion(game, printing))
          .toJS();
        break;
      case Game.YUGIOH:
        cardGroup = cardGroup as YugiCardGroup;
        foreignName = cardGroup.getInstances().first().get('card').get('name');
        currentLanguage = Language.ENGLISH;
        const yugiUUID = unsaved.get('printing') || cardGroup.cardValues('uuid', 'Multiple', '');
        currentPrinting = cardGroup.selectedPrinting(
          yugiUUID,
          SortLanguages(Immutable.Set(this.props.cardPanel.get('languageOptions'))),
        );
        options = cardGroup
          .get('printingOptions')
          .map((printing: YugiPrinting) => new PrintingSuggestion(game, printing))
          .toJS();
        break;
      case Game.LORCANA:
        cardGroup = cardGroup as LorcanaCardGroup;
        foreignName = cardGroup.getInstances().first().get('card').get('name');
        currentLanguage = Language.ENGLISH;
        const lorcanaUUID = unsaved.get('printing') || cardGroup.cardValues('uuid', 'Multiple', '');
        currentPrinting = cardGroup.selectedPrinting(
          lorcanaUUID,
          SortLanguages(Immutable.Set(this.props.cardPanel.get('languageOptions'))),
        );

        options = cardGroup
          .get('printingOptions')
          .map((printing: LorcanaPrinting) => new PrintingSuggestion(game, printing))
          .toJS();
        break;
    }

    const languageTuple = currentPrinting.validateLanguage(currentLanguage);

    const testPrefix = this.props.cardPanelType === CardPanelType.COLLECTION_EDIT ? 'cpec' : 'cpeb';
    return (
      <>
        <div className="card-panel-content-info-container--small" style={{ whiteSpace: 'normal' }}>
          {this.props.cardNames.size > 1 ? (
            <div className="card-panel-content-info-container--small">
              <div className="card-panel-info-heading" data-testid={`${testPrefix}-card-names`}>
                Card Names
              </div>
              <TruncatedText listOfStrings={this.props.cardNames.toList()}></TruncatedText>
            </div>
          ) : null}
        </div>
        {this.props.showForeignName ? <CardPanelInfo title="Printed Name" info={foreignName} /> : null}

        <div className="container">
          <div className="row">
            <div className="card-panel-content-info-container--large" style={{ flex: 'auto', paddingLeft: '0' }}>
              <PrintingSelector
                dispatcher={this.props.dispatcher}
                game={game}
                pricingSource={PricingSource.TCG_PLAYER}
                currentPrinting={currentPrinting}
                options={options}
                loadOptions={this.props.loadOptions.bind(this)}
                loading={!this.props.cardPanel.get('printingOptionsReady')}
                onChange={(selection: AnyPrinting) => {
                  this.props.onChangeAttributes(new CardAttributes({ printing: selection.identifier() }));
                }}
                onChangeCard={(card: AnyCard) => this.props.onChangeCard(card)}
                changeCardDisabled={this.props.cardPanel.get('cardPanelType') !== CardPanelType.STAGING}
              />
            </div>
          </div>
        </div>

        <div className="container">
          <div className="row">
            <div className="col-md-4" style={{ flex: 'auto', padding: '0' }} data-testid={`${testPrefix}-price-parent`}>
              <CardPanelInfo title={cardGroup.count() > 1 ? 'Total Price' : 'Price'} info={this.props.priceLabel} />
            </div>
            {this.props.timeAdded ? (
              <div className="col-md-4" style={{ flex: 'auto' }} data-testid={`${testPrefix}-time-parent`}>
                <CardPanelInfo
                  title={this.props.cardPanelType === CardPanelType.STAGING ? 'Staged' : 'Added'}
                  info={this.props.timeAdded}
                />
              </div>
            ) : null}
          </div>
        </div>
        <div className="container">
          <div className="row" style={{ gap: '0 2rem' }}>
            {this.attributeElements(languageTuple.get('languageValid'), languageTuple.get('setOfLanguages'))}
          </div>
        </div>
      </>
    );
  }

  attributeElements(languageValid: boolean, languageOptions: Immutable.Set<Language>): JSX.Element {
    const unicodeWarningSign = String.fromCharCode(9888);

    switch (this.props.cardPanel.get('game')) {
      case Game.MTG:
        return (
          <>
            <div className={'card-panel-content-info-container--large'} style={{ flex: 'auto', maxWidth: '6rem' }}>
              <div className="card-panel-info-heading">Foil</div>
              <Checkbox
                checked={this.state.attributes.get('foil') === Foil.On}
                multiple={this.state.attributes.get('foil') === Foil.Partial}
                onChange={this.onUpdateFoil.bind(this)}
              />
            </div>
            <div
              className={'card-panel-content-info-container--large'}
              style={{ flex: 'auto', maxWidth: 'fit-content' }}
            >
              <div className="card-panel-info-heading with-select">Condition</div>
              <ConditionSelector
                condition={this.state.attributes.get('condition')}
                onChange={this.onUpdateCondition.bind(this)}
                hideTitle={true} // TODO: Use consistent titling
              />
            </div>
            <div
              className={'card-panel-content-info-container--large'}
              style={{ flex: 'auto', maxWidth: 'fit-content' }}
            >
              {languageValid ? (
                <div className="card-panel-info-heading with-select">Language</div>
              ) : (
                <div className="card-panel-info-heading with-select is-error">{'Language ' + unicodeWarningSign}</div>
              )}
              <LanguageSelector
                language={this.state.attributes.get('language')}
                options={languageOptions}
                onChange={this.onUpdateLanguage.bind(this)}
                hideTitle={true} // TODO: Use consistent titling
              />
            </div>
          </>
        );
      case Game.POKEMON:
        return (
          <>
            <div
              className={'card-panel-content-info-container--large'}
              style={{ flex: 'auto', maxWidth: 'fit-content' }}
            >
              <div className="card-panel-info-heading with-select">Condition</div>
              <ConditionSelector
                condition={this.state.attributes.get('condition')}
                onChange={this.onUpdateCondition.bind(this)}
                hideTitle={true} // TODO: Use consistent titling
              />
            </div>
            <div
              className={'card-panel-content-info-container--large'}
              style={{ flex: 'auto', maxWidth: 'fit-content' }}
            >
              <div className="card-panel-info-heading with-select">Finish</div>
              <PokeFinishSelector
                dispatcher={this.props.dispatcher}
                finish={this.state.attributes.get('pokeFinish')}
                onChange={this.onUpdatePokeFinish.bind(this)}
                hideTitle={true} // TODO: Use consistent titling
              />
            </div>
          </>
        );
      case Game.YUGIOH:
        return (
          <>
            <div
              className={'card-panel-content-info-container--large'}
              style={{ flex: 'auto', maxWidth: 'fit-content' }}
            >
              <div className="card-panel-info-heading with-select">Condition</div>
              <ConditionSelector
                condition={this.state.attributes.get('condition')}
                onChange={this.onUpdateCondition.bind(this)}
                hideTitle={true} // TODO: Use consistent titling
              />
            </div>
          </>
        );
      case Game.LORCANA:
        return (
          <>
            <div
              className={'card-panel-content-info-container--large'}
              style={{ flex: 'auto', maxWidth: 'fit-content' }}
            >
              <div className="card-panel-info-heading with-select">Condition</div>
              <ConditionSelector
                condition={this.state.attributes.get('condition')}
                onChange={this.onUpdateCondition.bind(this)}
                hideTitle={true} // TODO: Use consistent titling
              />
            </div>
            <div
              className={'card-panel-content-info-container--large'}
              style={{ flex: 'auto', maxWidth: 'fit-content' }}
            >
              <div className="card-panel-info-heading with-select">Finish</div>
              <LorcanaFinishSelector
                dispatcher={this.props.dispatcher}
                finish={this.state.attributes.get('lorcanaFinish')}
                onChange={this.onUpdatePokeFinish.bind(this)}
                hideTitle={true} // TODO: Use consistent titling
              />
            </div>
          </>
        );
    }
  }
}
