import open_in_new from '@iconify/icons-ic/outline-open-in-new';
import { Icon } from '@iconify/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import TruncateMarkup from 'react-truncate-markup';
import * as StackLocationAction from '../../../actions/StackLocationActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { LinkedResources } from '../../../models/LinkedResources';
import { StackedCard } from '../../../models/StackedCard';
import { StackLocation } from '../../../models/StackLocation';

interface IProps {
  dispatcher: Dispatcher;
  resources?: Immutable.Map<string, LinkedResources>;
}

interface IState {
  locations: string[][] /* uuid, path */; // WARNING: Must be Array type, otherwise React will crash!
}

export class LocationURLList extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      locations: [],
    };
  }

  async componentDidUpdate(prevProps: IProps) {
    if (this.props.resources !== prevProps.resources) {
      this.setState({ locations: await this.resourcesToStringArray.bind(this)() });
    }
  }

  private async resourcesToStringArray(): Promise<string[][]> {
    if (this.props.resources === undefined || this.props.resources.size === 0) {
      return [];
    }

    const promiseArray = this.props.resources
      .map((resources: LinkedResources) => resources.get('stackedCard'))
      .filterNot((stackedCard: StackedCard | undefined) => stackedCard === undefined)
      .map((value: StackedCard) => {
        return this.resolveStackedCardEntry(value);
      })
      .toArray();
    return await Promise.all(promiseArray);
  }

  private async resolveStackedCardEntry(value: StackedCard): Promise<string[]> {
    const locationUUID = value.get('locationUUID');
    const path = StackLocation.pathToString(
      await StackLocationAction.getAllInPath(locationUUID, this.props.dispatcher),
    );
    return [locationUUID, `${path}/${value.get('position')}`];
  }

  private ellipsis = (node: React.ReactNode) => {
    if (node) {
      const element = node as React.ReactElement;
      if (element.props) {
        const visibleElements: number = element.props.children.length || 0;
        return <div className="truncated--sets">{` and ${this.state.locations.length - visibleElements} more`}</div>;
      }
    }
    return '';
  };

  render() {
    if (this.state.locations.length === 0) {
      return null;
    }

    return (
      <>
        <div className="card-panel-content-info-container--small">
          <div className="card-panel-info-heading">{this.state.locations.length === 1 ? 'Location ' : 'Locations'}</div>
        </div>

        <TruncateMarkup lines={1} ellipsis={this.ellipsis}>
          <div
            className="truncated-text"
            style={
              /* In-line styling apppears to be required here, we think it may be an issue with TruncateMarkup. */
              { display: 'flex', verticalAlign: 'middle' }
            }
          >
            {this.state.locations.map((entry: string[] /* uuid, path */, index: number) => {
              return (
                <TruncateMarkup.Atom key={index}>
                  <>
                    <a className="location-url" target="_blank" href={`/collection?locationUUID=${entry[0]}`}>
                      <span>{entry[1]}</span> <Icon height={'15px'} width={'15px'} icon={open_in_new} />
                    </a>
                    {index === this.state.locations.length - 1 ? null : <span style={{ marginRight: '1rem' }}>,</span>}
                  </>
                </TruncateMarkup.Atom>
              );
            })}
          </div>
        </TruncateMarkup>
      </>
    );
  }
}
