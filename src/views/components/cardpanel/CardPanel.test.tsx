import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import { beforeEach, describe, vi } from 'vitest';
import { create, FakeUser } from '../../../../tests/fake/Fake';
import {
  FakeCardPanelWithOptions,
  FakeMTGCardGroupWithOptions,
  FakeMTGCardPage,
} from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as CardPanelActions from '../../../actions/CardPanelActions';
import * as InputSessionActions from '../../../actions/InputSessionActions';
import { CardAttributes } from '../../../models/CardAttributes';
import { CardPanel as CardPanelModel, CardPanelType } from '../../../models/CardPanels';
import { Game } from '../../../models/Game';
import { Language } from '../../../models/Language';
import { MTGCardGroup } from '../../../models/mtg/MTGCardPage';
import { PokeCardGroup } from '../../../models/pokemon/PokeCardPage';
import { CardPanel, CSSGridContainer } from './CardPanel';

vi.mock('../../../actions/CardPanelActions');
vi.mock('../../../actions/InputSessionActions.ts');

type Props = React.ComponentProps<typeof CardPanel>;

const DEFAULT_PROPS: Omit<Props, 'dispatcher'> = {
  me: create(FakeUser, { subscribed: true }),
  gridContainer: CSSGridContainer.COLLECTION,
  cardPage: create(FakeMTGCardPage),
  cardPanel: create(FakeCardPanelWithOptions),
  cardPanelRow: 0,
  userTags: Immutable.OrderedSet([]),
  isGrid: false,
  usernamePublic: false,
  isMultiple: false,
};

function renderCardPanel(props: Partial<Props> = {}) {
  return renderWithDispatcher(CardPanel, { ...DEFAULT_PROPS, ...props });
}

function getCardName(cardPanel: CardPanelModel) {
  if (cardPanel.get('game') !== Game.MTG) {
    return (cardPanel.get('cardGroup') as PokeCardGroup).getInstances().first().get('card').get('name');
  }
  return (cardPanel.get('cardGroup') as MTGCardGroup).getInstances().first().get('cardName');
}

describe('CardPanel', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('component render', () => {
    it('renders an empty heading when card data is missing', () => {
      const { container } = renderCardPanel({
        cardPanel: create(FakeCardPanelWithOptions, { cardGroup: new MTGCardGroup() }),
      });
      const cardTitle = container.querySelector('.card-panel-content-heading');
      expect(cardTitle).toBeEmptyDOMElement();
    });

    it('renders the card name when card data is available', () => {
      const { container } = renderCardPanel();
      const cardName = getCardName(DEFAULT_PROPS.cardPanel);
      const cardTitle = container.querySelector('.card-panel-content-heading');
      expect(cardTitle).toHaveTextContent(cardName);
    });

    it('renders a multi-card title for multiple card instances', () => {
      const cardGroup = create(FakeMTGCardGroupWithOptions, { cardInstancesLength: 2 });
      const { container } = renderCardPanel({
        cardPanel: create(FakeCardPanelWithOptions, { cardGroup }),
      });
      const cardTitle = container.querySelector('.card-panel-content-heading');
      expect(cardTitle).toHaveTextContent('Viewing 2 Cards');
    });
  });

  describe('CardPanelType: STAGING', () => {
    it('calls closeCardPanel when the Close button is clicked', () => {
      const { dispatcher } = renderCardPanel({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardPanelType: CardPanelType.STAGING,
        }),
      });
      fireEvent.click(screen.getByText('Close'));
      expect(InputSessionActions.closeCardPanel).toHaveBeenCalledExactlyOnceWith(dispatcher);
    });
  });

  describe('CardPanelType: COLLECTION_VIEW', () => {
    it('calls closeCardPanel when the Close button is clicked', () => {
      const { dispatcher } = renderCardPanel({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardPanelType: CardPanelType.COLLECTION_VIEW,
        }),
      });
      fireEvent.click(screen.getByText('Close'));
      expect(CardPanelActions.closeCardPanel).toHaveBeenCalledExactlyOnceWith(dispatcher);
    });

    it('shows the confirmation dialog when the Remove button is clicked', () => {
      const cardPanel = create(FakeCardPanelWithOptions, {
        cardPanelType: CardPanelType.COLLECTION_VIEW,
      });
      renderCardPanel({ cardPanel });
      fireEvent.click(screen.getByText('Remove'));
      expect(screen.getByText('Are you sure?')).toBeInTheDocument();
      expect(
        screen.getByText('This card will be removed from your collection. This decision cannot be reversed!'),
      ).toBeInTheDocument();
    });

    it('shows the confirmation dialog when the Remove All button is clicked', () => {
      const cardPanel = create(FakeCardPanelWithOptions, {
        cardPanelType: CardPanelType.COLLECTION_VIEW,
        cardGroup: create(FakeMTGCardGroupWithOptions, { cardInstancesLength: 2 }),
      });
      renderCardPanel({ cardPanel });
      fireEvent.click(screen.getByText('Remove All'));
      expect(screen.getByText('Are you sure?')).toBeInTheDocument();
      expect(
        screen.getByText('These cards will be removed from your collection. This decision cannot be reversed!'),
      ).toBeInTheDocument();
    });

    it('hides the confirmation dialog when the dismiss button is clicked', () => {
      const { container } = renderCardPanel({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardPanelType: CardPanelType.COLLECTION_VIEW,
        }),
      });
      fireEvent.click(screen.getByText('Remove'));
      expect(screen.queryByText('Are you sure?')).toBeInTheDocument();

      const dismissButton = container.querySelector('.dialog-dismiss') as HTMLElement;
      fireEvent.click(dismissButton);
      expect(screen.queryByText('Are you sure?')).not.toBeInTheDocument();
    });

    it('calls removeCards when the confirmation Remove button is clicked', () => {
      const { dispatcher } = renderCardPanel({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardPanelType: CardPanelType.COLLECTION_VIEW,
        }),
      });
      fireEvent.click(screen.getByText('Remove'));
      fireEvent.click(screen.getByRole('button', { name: 'Remove' }));
      expect(CardPanelActions.removeCards).toHaveBeenCalledExactlyOnceWith(DEFAULT_PROPS.cardPage, dispatcher);
    });

    it('calls editCardPanel when the Edit button is clicked', () => {
      const { dispatcher } = renderCardPanel({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardPanelType: CardPanelType.COLLECTION_VIEW,
        }),
      });
      fireEvent.click(screen.getByText('Edit'));
      expect(CardPanelActions.editCardPanel).toHaveBeenCalledExactlyOnceWith(dispatcher);
    });
  });

  describe('CardPanelType: COLLECTION_EDIT', () => {
    it('calls cancelCardPanel when the Cancel button is clicked', () => {
      const { dispatcher } = renderCardPanel({
        cardPanel: create(FakeCardPanelWithOptions, {
          cardPanelType: CardPanelType.COLLECTION_EDIT,
        }),
      });
      fireEvent.click(screen.getByText('Cancel'));
      expect(CardPanelActions.cancelCardPanel).toHaveBeenCalledExactlyOnceWith(dispatcher);
    });

    it('calls save when the Save button is clicked and the language is valid', () => {
      const languageOptions = [Language.ENGLISH, Language.SPANISH];
      const cardPanel = create(FakeCardPanelWithOptions, {
        cardPanelType: CardPanelType.COLLECTION_EDIT,
        unsavedCardAttributes: new CardAttributes({
          printing: 'test-printing',
          language: languageOptions[0],
        }),
        languageOptions: Immutable.List(languageOptions),
      });
      const { dispatcher } = renderCardPanel({ cardPanel });
      fireEvent.click(screen.getByText('Save'));
      expect(CardPanelActions.save).toHaveBeenCalledExactlyOnceWith(
        dispatcher,
        cardPanel.get('unsavedCardAttributes'),
        false,
      );
    });

    it('does not call save when the Save button is clicked and the language is invalid', () => {
      const languageOptions = [Language.SPANISH];
      const cardPanel = create(FakeCardPanelWithOptions, {
        cardPanelType: CardPanelType.COLLECTION_EDIT,
        unsavedCardAttributes: new CardAttributes({
          printing: 'test-printing',
          language: Language.ENGLISH,
        }),
        languageOptions: Immutable.List(languageOptions),
      });
      renderCardPanel({ cardPanel });
      fireEvent.click(screen.getByText('Save'));
      expect(CardPanelActions.save).not.toHaveBeenCalled();
    });
  });

  describe('CardPanel visibility based on row', () => {
    it('shows content when cardPanelRow matches cardPanel.y', () => {
      const { container } = renderCardPanel({
        cardPanelRow: 1,
        cardPanel: create(FakeCardPanelWithOptions, { y: 1 }),
      });
      expect(container.querySelector('.card-panel-content-body')).toBeInTheDocument();
    });

    it('hides content when cardPanelRow does not match cardPanel.y', () => {
      const { container } = renderCardPanel({
        cardPanelRow: 2,
        cardPanel: create(FakeCardPanelWithOptions, { y: 1 }),
      });
      expect(container.querySelector('.card-panel-content-body')).toBeNull();
    });
  });
});
