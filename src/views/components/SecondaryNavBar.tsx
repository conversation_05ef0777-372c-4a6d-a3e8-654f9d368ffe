import * as Immutable from 'immutable';
import * as React from 'react';
import { TabItem } from '../../models/TabItem';
import SecondaryNavBarItem from './SecondaryNavBarItem';
import { SecondaryNavBarData, SecondaryNavButton } from './SecondaryNavButton';

interface IProps {
  pages: Immutable.List<TabItem>;
  selectedPage: string;
  openPage: (page: string) => void;
  displayName: (page: string) => string;
  leftButton?: SecondaryNavBarData;
  rightButton?: SecondaryNavBarData;
}

export class SecondaryNavBar extends React.Component<IProps> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  private generatedNavBar(): JSX.Element {
    return (
      <>
        {this.props.pages.map((page: TabItem) => (
          <SecondaryNavBarItem
            key={page.get('title')}
            selected={!page.get('disabled') && page.get('title') === this.props.selectedPage}
            onClick={() => this.props.openPage(page.get('title'))}
            disabled={page.get('disabled') ? page.get('disabled') : false}
          >
            {this.props.displayName(page.get('title'))}
          </SecondaryNavBarItem>
        ))}
      </>
    );
  }

  public render() {
    return (
      <>
        <div className="visible-md flex">
          <div style={{ marginRight: '1rem' }}>
            {this.props.leftButton !== undefined ? (
              <SecondaryNavButton
                href={this.props.leftButton.get('href')}
                onClick={this.props.leftButton.get('onClick')}
                iconType={this.props.leftButton.get('iconType')}
              />
            ) : null}
          </div>
          <ul className="secondary-nav-bar-items nav-bar__horizontal">{this.generatedNavBar()}</ul>
          <div style={{ marginLeft: '1rem' }}>
            {this.props.rightButton !== undefined ? (
              <SecondaryNavButton
                href={this.props.rightButton.get('href')}
                onClick={this.props.rightButton.get('onClick')}
                iconType={this.props.rightButton.get('iconType')}
              />
            ) : null}
          </div>
        </div>
        <div className="hidden-md">
          <div style={{ marginBottom: '1rem' }}>
            {this.props.leftButton !== undefined ? (
              <SecondaryNavButton
                href={this.props.leftButton.get('href')}
                onClick={this.props.leftButton.get('onClick')}
                iconType={this.props.leftButton.get('iconType')}
              />
            ) : null}
          </div>
          <ul className="secondary-nav-bar-items">{this.generatedNavBar()}</ul>
          <div style={{ marginTop: '1rem' }}>
            {this.props.rightButton !== undefined ? (
              <SecondaryNavButton
                href={this.props.rightButton.get('href')}
                onClick={this.props.rightButton.get('onClick')}
                iconType={this.props.rightButton.get('iconType')}
              />
            ) : null}
          </div>
        </div>
      </>
    );
  }
}

export default SecondaryNavBar;
