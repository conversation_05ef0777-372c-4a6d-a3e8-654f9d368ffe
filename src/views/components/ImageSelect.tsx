import image from '@iconify/icons-ic/image';
import { Icon } from '@iconify/react';
import * as React from 'react';
import Dropzone from 'react-dropzone';
import ReactCrop, { Crop, PercentCrop } from 'react-image-crop';
import { Dialog } from './Dialog';

interface IProps {
  src: string;
  onChangeImage: (file?: File, crop?: ImageCrop) => void;
  className?: string;
}

interface IState {
  imageFile?: File;
  imageData?: string;
  imageCrop?: ImageCrop;
  isCropping?: boolean;
}

class ImageCrop implements Crop {
  x: number;
  y: number;
  width?: number;
  height?: number;
  aspect?: number;

  constructor(x?: number, y?: number, width?: number, height?: number, aspect?: number) {
    this.x = x || 0;
    this.y = y || 0;
    this.width = width;
    this.height = height;
    this.aspect = aspect;
  }

  static fromCrop(crop: Crop): ImageCrop {
    return new ImageCrop(crop.x, crop.y, crop.width, crop.height, crop.aspect);
  }

  static default(): ImageCrop {
    return new ImageCrop(5, 5, 90, 90, 1);
  }
}

export const ImageSelect = class extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      imageFile: undefined,
      imageData: undefined,
      imageCrop: ImageCrop.default(),
      isCropping: false,
    };
  }

  public render(): JSX.Element {
    return (
      <div
        className={
          'image-select' + (this.props.className && this.props.className.length ? ' ' + this.props.className : '')
        }
      >
        <Dropzone onDrop={this.onDropImage.bind(this)}>
          {({ getRootProps, getInputProps }) => (
            <div className="image-dropzone" {...getRootProps()}>
              <div
                className={'image-dropzone-content' + (!this.props.src || !this.props.src.length ? ' is-empty' : '')}
              >
                <div className="image-dropzone-image" style={{ backgroundImage: 'url(' + this.props.src + ')' }} />
                <div
                  className={
                    'image-dropzone-placeholder' + (!this.props.src || !this.props.src.length ? ' is-empty' : '')
                  }
                >
                  <Icon height={'24px'} width={'24px'} icon={image} />
                  <span>
                    <strong>Click</strong> or <strong>Drag & Drop</strong>
                    <br />
                    to add a profile picture
                  </span>
                </div>
                <input style={{ display: 'none' }} {...getInputProps()} />
              </div>
            </div>
          )}
        </Dropzone>
        {this.renderCrop()}
      </div>
    );
  }

  private renderCrop(): JSX.Element | null {
    if (!this.state.isCropping || !this.state.imageData) {
      return null;
    }
    return (
      <Dialog className="image-select-dialog" isOpen={true} onClickBackground={ImageSelect.onClick.bind(this)}>
        <ReactCrop
          src={this.state.imageData}
          crop={this.state.imageCrop}
          onChange={(crop: Crop, pixelCrop: PercentCrop) => this.setState({ imageCrop: ImageCrop.fromCrop(crop) })}
          onComplete={(crop: Crop, pixelCrop: PercentCrop) => this.setState({ imageCrop: ImageCrop.fromCrop(crop) })}
        />
        <div className="row" style={{ marginTop: '1rem' }}>
          <div className="col-xs-6 flex">
            <button className="button-primary" onClick={this.onClickOkCrop.bind(this)}>
              Update
            </button>
          </div>
          <div className="col-xs-6 flex justify-end">
            <button className="button-alert" onClick={this.onClickCancelCrop.bind(this)}>
              Cancel
            </button>
          </div>
        </div>
      </Dialog>
    );
  }

  private static onClick(evt: React.SyntheticEvent<HTMLElement>) {
    evt.stopPropagation();
  }

  private onDropImage(files: Array<File>) {
    const reader = new FileReader();
    reader.onload = (evt: Event) => {
      const result = (evt.target as FileReader).result as string;
      this.setState({
        imageFile: files[0],
        imageData: result,
        isCropping: true,
      });
    };
    reader.readAsDataURL(files[0]);
  }

  private onClickCancelCrop(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({
      imageFile: undefined,
      imageData: undefined,
      imageCrop: ImageCrop.default(),
      isCropping: false,
    });
  }

  private onClickOkCrop(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    if (this.state.imageCrop !== undefined) {
      this.props.onChangeImage(this.state.imageFile, this.state.imageCrop);
    }
    this.setState({
      imageFile: undefined,
      imageData: undefined,
      imageCrop: ImageCrop.default(),
      isCropping: false,
    });
  }
};
