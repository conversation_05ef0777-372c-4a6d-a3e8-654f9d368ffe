import chevron_left from '@iconify/icons-ic/chevron-left';
import chevron_right from '@iconify/icons-ic/chevron-right';
import first_page from '@iconify/icons-ic/first-page';
import last_page from '@iconify/icons-ic/last-page';
import { Icon } from '@iconify/react';
import classNames from 'classnames';
import * as React from 'react';
import { NumberFormat } from '../../helpers/fmt';

interface IProps {
  readonly page: number;
  readonly totalItems: number;
  readonly onClickPage: (_: number) => void;
}

export function Paginator(props: IProps) {
  const pages = [];
  const pageCount = Math.ceil(props.totalItems / 100);
  if (pageCount <= 5) {
    for (let i = 1; i <= pageCount; i++) {
      pages.push(i);
    }
  } else {
    let pageBoundLower = props.page - 2;
    let pageBoundUpper = props.page + 2;

    if (pageBoundLower < 1) {
      pageBoundUpper += 1 - pageBoundLower;
      pageBoundLower = 1;
    }
    if (pageBoundUpper > pageCount) {
      pageBoundLower -= pageBoundUpper - pageCount;
      pageBoundUpper = pageCount;
    }

    for (let i = pageBoundLower; i <= pageBoundUpper; i++) {
      pages.push(i);
    }
  }

  if (pages.length <= 1) {
    return null;
  }

  return (
    <div className="paginator">
      <Arrow
        type={ArrowType.START}
        page={props.page}
        totalPages={pageCount}
        onClick={(page) => props.onClickPage(page)}
      />
      <Arrow
        type={ArrowType.PREV}
        page={props.page}
        totalPages={pageCount}
        onClick={(page) => props.onClickPage(page)}
      />
      {pages.map((page: number) => (
        <div
          key={`page-${page}`}
          className={classNames({
            paginator__page: true,
            'is-selected': page === props.page,
          })}
          onClick={() => props.onClickPage(page)}
        >
          <span style={{ margin: page >= 100 ? '0 0.5rem' : '0' }}>{NumberFormat.commaSeparated(page)}</span>
        </div>
      ))}
      <Arrow
        type={ArrowType.NEXT}
        page={props.page}
        totalPages={pageCount}
        onClick={(page) => props.onClickPage(page)}
      />
      <Arrow
        type={ArrowType.END}
        page={props.page}
        totalPages={pageCount}
        onClick={(page) => props.onClickPage(page)}
      />
    </div>
  );
}

enum ArrowType {
  START = 'first_page',
  PREV = 'chevron_left',
  NEXT = 'chevron_right',
  END = 'last_page',
}

interface ArrowProps {
  readonly type: ArrowType;
  readonly page: number;
  readonly totalPages: number;
  readonly onClick: (_: number) => void;
}

function arrowTypeToIconify(type: ArrowType) {
  switch (type) {
    case ArrowType.START:
      return first_page;
    case ArrowType.PREV:
      return chevron_left;
    case ArrowType.NEXT:
      return chevron_right;
    case ArrowType.END:
      return last_page;
  }
}

function Arrow(props: ArrowProps) {
  const back = props.type == ArrowType.START || props.type == ArrowType.PREV;

  const className = classNames('paginator__arrow', {
    'is-disabled': (back && props.page <= 1) || (!back && props.page >= props.totalPages),
  });

  let targetPage: number;

  switch (props.type) {
    case ArrowType.START:
      targetPage = 1;
      break;
    case ArrowType.PREV:
      targetPage = Math.max(1, props.page - 1);
      break;
    case ArrowType.NEXT:
      targetPage = Math.min(props.totalPages, props.page + 1);
      break;
    case ArrowType.END:
      targetPage = props.totalPages;
      break;
  }

  return (
    <div className={className} onClick={() => props.onClick(targetPage)}>
      <Icon width={'35px'} height={'35px'} icon={arrowTypeToIconify(props.type)} />
    </div>
  );
}
