import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { FilterComponentType } from '../../../models/FilterComponent';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { LorcanaFilter } from '../../../models/filters/lorcana/LorcanaFilters';
import { PriceMinMaxFilters } from '../filters/PriceMinMaxFilters';
import { QueryFilter } from '../filters/QueryFilter';
import { StartsWithFilter } from '../filters/StartsWithFilter';

interface IProps {
  dispatcher: Dispatcher;
  filter: LorcanaFilter;
  type: FilterComponentType;
  displayType: FilterDisplay;
  filterOverride?: LorcanaFilter;
  onUpdate: (newFilter: LorcanaFilter) => void;
  disabled?: boolean;
}

export const LorcanaFilterComponent = (props: IProps) => {
  switch (props.type) {
    case FilterComponentType.QUERY:
      return (
        <QueryFilter
          localQuery={props.filter.getQuery()}
          advancedQuery={props.filterOverride?.getQuery()}
          updateQuery={(query: string) => props.onUpdate(props.filter.setQuery(query))}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.PRICE:
      return (
        <PriceMinMaxFilters
          filter={props.filter.get('price')}
          processValue={(minmax: 'min' | 'max', value?: string) => {
            props.onUpdate(
              props.filter.set('price', props.filter.get('price').set(minmax, value === undefined ? '' : value)),
            );
          }}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          displayType={props.displayType}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.STARTS_WITH:
      return (
        <StartsWithFilter
          startsWith={props.filter.get('startsWith')}
          update={(startsWith: string) => {
            props.onUpdate(props.filter.set('startsWith', startsWith));
          }}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );
    case FilterComponentType.SET_NAME_STARTS_WITH:
      return (
        <StartsWithFilter
          titleOverride="Set Name Starts With"
          startsWith={props.filter.get('setStartsWith')}
          update={(startsWith: string) => {
            props.onUpdate(props.filter.set('setStartsWith', startsWith));
          }}
          displayType={props.displayType}
          onClear={() => props.onUpdate(props.filter.clearFilter(props.type, props.displayType))}
          disabled={props.disabled}
        />
      );

    default:
      return null;
  }
};
