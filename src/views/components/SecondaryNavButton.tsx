import exit_to_app from '@iconify/icons-ic/baseline-exit-to-app';
import chevron_left from '@iconify/icons-ic/chevron-left';
import { Icon } from '@iconify/react';
import classNames from 'classnames';
import * as React from 'react';
import { Record } from '../../models/Records';
import SecondaryNavBarItem from './SecondaryNavBarItem';

interface IProps {
  href: string;
  onClick: (evt: React.SyntheticEvent<HTMLElement>) => void;
  iconType?: SecondaryIconType;
  detached?: boolean;
}

export class SecondaryNavBarData extends Record<IProps>({
  href: '',
  onClick: (evt: React.SyntheticEvent<HTMLElement>) => {
    return;
  },
  iconType: undefined,
}) {}

export enum SecondaryIconType {
  CHEVRON_LEFT = 'chevron_left',
  EXIT_TO_APP = 'exit_to_app',
}

function iconTypeToIconify(type?: SecondaryIconType) {
  switch (type) {
    case SecondaryIconType.CHEVRON_LEFT:
      return chevron_left;
    case SecondaryIconType.EXIT_TO_APP:
      return exit_to_app;
    default:
      return undefined;
  }
}

export class SecondaryNavButton extends React.Component<IProps> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  public render() {
    const icon = iconTypeToIconify(this.props.iconType);
    const className = classNames('secondary-nav-bar-items', { 'is-detached': this.props.detached === true });
    return (
      <div>
        <ul className={className}>
          <SecondaryNavBarItem onClick={this.props.onClick} secondaryNavButton={true}>
            {icon === undefined ? null : (
              <Icon height={'15px'} width={'15px'} icon={icon} style={{ margin: '0.5rem 0.5rem 0 0' }} />
            )}
            {this.props.href}
          </SecondaryNavBarItem>
        </ul>
      </div>
    );
  }
}
