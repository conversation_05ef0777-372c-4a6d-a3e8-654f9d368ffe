import * as React from 'react';
import * as HasFocusedSearchCard from '../../containers/HasFocusedSearchCard';
import Dispatcher from '../../dispatcher/Dispatcher';
import { ImageQuality } from '../../helpers/core_assets';
import { Card } from '../../models/Cards';
import { Foil } from '../../models/Foil';
import { Game } from '../../models/Game';
import { Language } from '../../models/Language';
import { LorcanaCard } from '../../models/lorcana/LorcanaCards';
import { PokeCard } from '../../models/pokemon/PokeCards';
import { YugiCardSet } from '../../models/yugioh/YugiCards';
import { CardImage } from './CardImage';
import { ImageAttributes } from './cardpanel/CardPanelStack';

interface IProps {
  dispatcher: Dispatcher;
  game: Game;
  language?: Language;
  foil?: Foil;
}

interface IState {}

export const CardSearcherImagePreview = HasFocusedSearchCard.Attach<IProps>(
  class extends React.Component<IProps & HasFocusedSearchCard.IProps, IState> {
    /**
     * @override
     */
    constructor(props: IProps & HasFocusedSearchCard.IProps) {
      super(props);
      this.state = {
        isOpen: false,
      };
    }

    render() {
      if (this.props.card === undefined) {
        return null;
      }

      let imageAttributes: ImageAttributes;

      switch (this.props.game) {
        case Game.MTG:
          const mtgCard = this.props.card as Card;
          imageAttributes = new ImageAttributes(
            mtgCard.imageURL(ImageQuality.HQ, this.props.language || Language.ENGLISH),
            mtgCard.get('name'),
            this.props.foil,
          );
          break;
        case Game.POKEMON:
          const pokeCard = this.props.card as PokeCard;
          imageAttributes = new ImageAttributes(
            pokeCard.imageURL(ImageQuality.HQ, this.props.language || Language.ENGLISH),
            pokeCard.get('name'),
            this.props.foil,
          );
          break;
        case Game.YUGIOH:
          const yugiCardSet = this.props.card as YugiCardSet;
          imageAttributes = new ImageAttributes(
            yugiCardSet.imageURL(ImageQuality.HQ, this.props.language || Language.ENGLISH),
            yugiCardSet.get('card').get('name'),
            this.props.foil,
          );
          break;
        case Game.LORCANA:
          const lorcanaCard = this.props.card as LorcanaCard;
          imageAttributes = new ImageAttributes(
            lorcanaCard.imageURL(ImageQuality.HQ, this.props.language || Language.ENGLISH),
            lorcanaCard.get('name'),
            this.props.foil,
          );
          break;
      }

      return (
        <div className="card-suggestion-image-container">
          <div className="card-suggestion-image">
            <CardImage
              src={imageAttributes.source}
              game={this.props.game}
              alt={imageAttributes.alt}
              foil={imageAttributes.foil}
              highlightOnHover={false}
              selected={false}
            />
          </div>
        </div>
      );
    }
  },
);
