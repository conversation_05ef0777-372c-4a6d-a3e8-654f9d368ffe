import * as Immutable from 'immutable';
import * as React from 'react';
import TruncateMarkup from 'react-truncate-markup';
import { Game } from '../../models/Game';
import { SetIconInfo } from '../../models/SetIconInfo';
import { SetInfo } from './SetInfo';

export interface SetListProps {
  game: Game;
  setIcons: Immutable.Set<SetIconInfo>;
}

export const SetList = (props: SetListProps) => {
  // Converts to set, removing duplicates. Then converts to array for the for loop.
  const infoArray = props.setIcons.toArray(); // WARNING: Must be Array type, otherwise React will crash!
  const uniqueSets = props.setIcons
    .map((icon: SetIconInfo) => icon.get('setName'))
    .toSet()
    .count();

  const remainingCards = (node: React.ReactNode) => {
    if (node) {
      const element = node as React.ReactElement;
      if (element.props) {
        const visibleElements: number = element.props.children.length || 0;
        return <div className="truncated--sets">{` and ${uniqueSets - visibleElements} more`}</div>;
      }
    }
    return '';
  };

  return (
    <TruncateMarkup lines={1} ellipsis={remainingCards}>
      <div
        className="truncated-text"
        style={
          /* In-line styling apppears to be required here, we think it may be an issue wiht TruncateMarkup. */
          { display: 'flex', verticalAlign: 'middle' }
        }
      >
        {infoArray.map((info: SetIconInfo, index, arr) => {
          const last = index >= arr.length - 1 || arr.length === 0;
          return (
            <TruncateMarkup.Atom key={index}>
              <SetInfo game={props.game} info={info} />{' '}
              <span style={{ verticalAlign: 'middle', paddingRight: '5px' }}>{!last ? ',' : ''}</span>
            </TruncateMarkup.Atom>
          );
        })}
      </div>
    </TruncateMarkup>
  );
};
