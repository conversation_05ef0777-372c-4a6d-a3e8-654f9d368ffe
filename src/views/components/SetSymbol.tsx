import * as React from 'react';
import { CoreAssets } from '../../helpers/core_assets';
import { TextFormat } from '../../helpers/fmt';
import { Rarity } from '../../models/Rarity';

export enum SetSymbolSize {
  XS = 'xs',
  SM = 'sm',
  MD = 'md',
  XL = 'xl',
}

export interface ISetSymbolProps {
  setName: string;
  setCode: string;
  hoverText: boolean;
  collectorNumber?: string;
  rarity?: Rarity;
  size?: SetSymbolSize;
}

interface IState {
  hover: boolean;
}

export function rarityToChar(rarity?: Rarity) {
  switch (rarity) {
    case undefined:
      return undefined;
    case Rarity.BASIC_LAND:
    case Rarity.COMMON:
      return 'C';
    case Rarity.UNCOMMON:
      return 'U';
    case Rarity.RARE:
      return 'R';
    case Rarity.MYTHIC_RARE:
      return 'M';
    case Rarity.SPECIAL:
      return 'S';
    default:
      // TODO: Add Bugsnag notifier when we work out how to get that working without causing Jest to lose it.
      console.error(`Invalid rarity ${rarity} converted to COMMON`);
      return undefined;
  }
}

export class SetSymbol extends React.Component<ISetSymbolProps, IState> {
  constructor(props: ISetSymbolProps) {
    super(props);
    this.state = { hover: false };
  }

  public render() {
    const size = this.props.size === undefined ? SetSymbolSize.XS : this.props.size;
    const char = rarityToChar(this.props.rarity);
    const rarityHoverMessage = this.props.rarity ? TextFormat.capitalize(this.props.rarity) : '';
    const collectorNumberText = this.props.collectorNumber ? ' - ' + this.props.collectorNumber : '';
    const imgSrc =
      char === undefined
        ? `${CoreAssets.iconHost()}/set_symbols/${this.props.setCode}_large.png`
        : `${CoreAssets.iconHost()}/set_symbols/${this.props.setCode}_${char}_large.png`;
    return (
      <div
        className={`flex set-symbol__${size}`}
        onMouseEnter={this.onMouseEnter.bind(this)}
        onMouseLeave={this.onMouseLeave.bind(this)}
      >
        <img src={imgSrc} />
        {this.props.hoverText && this.state.hover ? (
          <div className="set-symbol-tooltip-container">
            <div className="set-symbol-tooltip">
              <div>{this.props.setName}</div>
              <div>{rarityHoverMessage + collectorNumberText}</div>
            </div>
          </div>
        ) : null}
      </div>
    );
  }

  private onMouseEnter(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({ hover: true });
  }

  private onMouseLeave(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({ hover: false });
  }
}
