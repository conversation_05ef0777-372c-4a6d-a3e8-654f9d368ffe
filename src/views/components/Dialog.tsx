import close from '@iconify/icons-ic/close';
import { Icon } from '@iconify/react';
import classNames from 'classnames';
import * as React from 'react';

export interface IDialogProps {
  isOpen: boolean;
  overflowRef?: React.Ref<HTMLDivElement>;
  isClosable?: boolean;
  isDismissible?: boolean;
  onDismiss?: (evt: React.SyntheticEvent<HTMLElement>) => void;
  onClickBackground?: (evt: React.SyntheticEvent<HTMLElement>) => void;
  className?: string;
  heading?: string;
  size?: DialogSize;
}

export enum DialogSize {
  SMALL = 'sm',
  MEDIUM = 'md',
  LARGE = 'lg',
  X_LARGE = 'xl',
  LARGE_DYNAMIC = 'lg-dynamic', // TODO: Deprecate non-dynamic sizes
}

interface IState {}

export class Dialog extends React.Component<IDialogProps, IState> {
  constructor(props: IDialogProps) {
    super(props);
    this.state = {};
  }

  public render() {
    if (!this.props.isOpen) {
      return null;
    }

    // TODO: Deprecate micro-managed bootstrap columns - default to `dialog-width`
    const className = `${classNames('dialog', {
      'col-xs-8 col-sm-6 col-md-4': this.props.size === DialogSize.SMALL || this.props.size === undefined,
      'col-xs-10 col-sm-8 col-md-6': this.props.size === DialogSize.MEDIUM,
      'col-xs-10 col-sm-8 col-md-7 col-lg-7 col-xl-7': this.props.size === DialogSize.LARGE,
      'col-xs-11 col-sm-11 col-md-11 col-lg-11 col-xl-11': this.props.size === DialogSize.X_LARGE,
      'dialog-width': this.props.size === DialogSize.LARGE_DYNAMIC,
    })}${this.props.className ? ' ' + this.props.className : ''}`;

    return (
      <div ref={this.props.overflowRef} className="dialog-background" onClick={this.onClickBackground.bind(this)}>
        <div className={className} onClick={Dialog.onClick.bind(this)}>
          {(this.props.isDismissible === null || this.props.isDismissible === undefined || this.props.isDismissible) &&
          (this.props.isClosable === null || this.props.isClosable === undefined || this.props.isClosable) ? (
            <div className="dialog-dismiss" onClick={this.onDismiss.bind(this)}>
              <Icon height={'24px'} width={'24px'} icon={close} />
            </div>
          ) : null}
          {this.props.heading === undefined ? null : <div className="dialog-heading">{this.props.heading}</div>}
          {this.props.children}
        </div>
      </div>
    );
  }

  private static onClick(evt: React.SyntheticEvent<HTMLElement>) {
    evt.stopPropagation();
  }

  private onClickBackground(evt: React.SyntheticEvent<HTMLElement>) {
    if (this.props.onClickBackground) {
      this.props.onClickBackground(evt);
    } else if (
      this.props.isDismissible === null ||
      this.props.isDismissible === undefined ||
      this.props.isDismissible
    ) {
      this.props.onDismiss && this.props.onDismiss(evt);
    }
  }

  private onDismiss(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    evt.stopPropagation();
    if (this.props.isDismissible === null || this.props.isDismissible === undefined || this.props.isDismissible) {
      this.props.onDismiss && this.props.onDismiss(evt);
    }
  }
}
