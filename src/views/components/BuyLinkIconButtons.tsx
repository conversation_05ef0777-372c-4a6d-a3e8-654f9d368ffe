import link from '@iconify/icons-ic/baseline-link';
import library_add from '@iconify/icons-ic/library-add';
import refresh from '@iconify/icons-ic/refresh';
import { Icon } from '@iconify/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { CartLocation } from '../../models/CartButton';

interface IProps {
  location: CartLocation;
  isPublic: boolean;
  loggedIn: boolean;
  disabled: boolean;
  onReset: () => void;
  onCompletePlayset: () => void;
  onPurchaseAllCards: () => void;
  onAddLinkedCards: () => void;
  linkedCards?: Immutable.Map<string, number>;
}

export const BuyLinkIconButtons = (props: IProps) => {
  return (
    <>
      <div className="buy-link-row">
        <button className="dialog-text-button" disabled={props.disabled} onClick={props.onReset.bind(this)}>
          <Icon height={'21px'} width={'21px'} icon={refresh} style={{ marginRight: '0.5rem' }} />
          <span className="buy-link-text"> Reset</span>
        </button>
        {renderOptionalButtons()}
      </div>
    </>
  );

  function renderOptionalButtons() {
    switch (props.location) {
      case CartLocation.COLLECTION:
        return (
          <button className="dialog-text-button" disabled={props.disabled} onClick={props.onCompletePlayset}>
            <Icon height={'21px'} width={'21px'} icon={library_add} style={{ marginRight: '0.5rem' }} />
            <span className="buy-link-text">Complete Playset</span>
          </button>
        );
      case CartLocation.DECKVIEWER:
        if (!props.loggedIn) {
          return null;
        }
        return (
          <>
            <button className="dialog-text-button" disabled={props.disabled} onClick={props.onPurchaseAllCards}>
              <Icon height={'21px'} width={'21px'} icon={library_add} style={{ marginRight: '0.5rem' }} />
              <span className="buy-link-text">Purchase all cards</span>
            </button>
            {!props.isPublic && props.linkedCards !== undefined ? (
              <button
                className="dialog-text-button"
                disabled={props.disabled}
                onClick={props.onAddLinkedCards.bind(this)}
              >
                <Icon height={'21px'} width={'21px'} icon={link} style={{ marginRight: '0.5rem' }} />
                <span className="buy-link-text">Purchase unlinked cards</span>
              </button>
            ) : null}
          </>
        );
    }
  }
};
