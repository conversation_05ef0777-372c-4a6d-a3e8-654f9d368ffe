import { fireEvent, render, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TabItem } from '../../models/TabItem';
import { TabBar } from './TabBar';

type TabBarProps = React.ComponentProps<typeof TabBar>;

const defaultTabItem = [
  new TabItem({ title: 'Tab 1' }),
  new TabItem({ title: 'Tab 2' }),
  new TabItem({ title: 'Tab 3' }),
];
const DEFAULT_PROPS: TabBarProps = {
  pages: Immutable.List(defaultTabItem),
  selectedTab: 'Tab 1',
  onChangeTab: vi.fn(),
};

function renderTabBar(props: Partial<TabBarProps> = {}) {
  return render(<TabBar {...DEFAULT_PROPS} {...props} />);
}

describe('TabBar', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  describe('when pages is empty', () => {
    it('renders nothing', () => {
      const { container } = renderTabBar({ pages: Immutable.List() });
      expect(container.firstChild).toBeEmptyDOMElement();
    });
  });
  describe('when pages is not empty', () => {
    it('applies correct className based on selected tab', () => {
      renderTabBar();
      defaultTabItem.forEach((tab) => {
        const activeTab = screen.getByText(tab.get('title'));
        expect(activeTab).toBeInTheDocument();
        if (tab.get('title') === DEFAULT_PROPS.selectedTab) {
          expect(activeTab.parentElement).toHaveClass('selected');
        } else {
          expect(activeTab.parentElement).not.toHaveClass('selected');
        }
      });
    });
    it('applies correct className based on tab position', () => {
      renderTabBar();
      defaultTabItem.forEach((tab, index) => {
        const activeTab = screen.getByText(tab.get('title'));
        expect(activeTab).toBeInTheDocument();
        if (index === 0) {
          expect(activeTab.parentElement).toHaveClass('tab-bar__left');
        } else if (index === defaultTabItem.length - 1) {
          expect(activeTab.parentElement).toHaveClass('tab-bar__right');
        } else {
          expect(activeTab.parentElement).toHaveClass('tab-bar');
        }
      });
    });
    it('calls onChangeTab when tab is clicked', () => {
      renderTabBar();
      const secondTab = defaultTabItem[1];
      fireEvent.click(screen.getByText(secondTab.get('title')));
      expect(DEFAULT_PROPS.onChangeTab).toHaveBeenCalledWith(secondTab.get('title'));
    });
  });
  describe('when a tab is disabled', () => {
    it('does not call onChangeTab when a disabled tab is clicked', () => {
      const pages = defaultTabItem.map((tab, idx) =>
        idx == 1 ? new TabItem({ title: tab.get('title'), disabled: true }) : tab,
      );
      renderTabBar({ pages: Immutable.List(pages) });
      const disabledTab = screen.getByText(pages[1].get('title'));
      expect(disabledTab.parentElement).toHaveClass('disabled');
      fireEvent.click(disabledTab);
      expect(DEFAULT_PROPS.onChangeTab).not.toHaveBeenCalled();
    });
  });
  describe('fallback tab selection', () => {
    it('selects the first tab when the selected tab is disabled', () => {
      const pages = defaultTabItem.map((tab, idx) =>
        idx == 1 ? new TabItem({ title: tab.get('title'), disabled: true }) : tab,
      );
      renderTabBar({ pages: Immutable.List(pages), selectedTab: pages[1].get('title') });
      const firstTab = screen.getByText(pages[0].get('title'));
      expect(firstTab.parentElement).toHaveClass('selected');
    });
  });
});
