import classNames from 'classnames';
import React, { useRef, useState } from 'react';
import { CoreAssets, ImageQuality } from '../../helpers/core_assets';
import { ImageHelper } from '../../helpers/img';
import isServerside from '../../helpers/serverside';
import { Foil } from '../../models/Foil';
import { Game } from '../../models/Game';

interface IProps {
  src: string;
  alt: string;
  foil: Foil;
  highlightOnHover: boolean;
  selected: boolean | 'multi';
  game?: Game;
  quality?: ImageQuality;
}

export const CardImage = (props: IProps) => {
  const [imageBorderRadius, setImageBorderRadius] = useState<string>('0px');
  const [hoverEnabled, setHoverEnabled] = useState<boolean>(false);

  const img = ImageHelper.preload(props.src);
  if (img) {
    img.onload = () => {
      setImageLoaded(true);
    };
  }

  const [imageLoaded, setImageLoaded] = useState<boolean>(img?.complete || false);

  const imgRef = useRef<HTMLImageElement>(null);

  function updateBorderRadius() {
    if (imgRef.current) {
      let borderSize = 0.05;
      switch (props.game) {
        case Game.YUGIOH:
          borderSize = 0.03;
          break;
        default:
          break;
      }
      setImageBorderRadius(`${imgRef.current.offsetWidth * borderSize}px`);
    }
  }
  // Adjusts border radius to be proportional to actual image width
  if (!isServerside()) {
    React.useLayoutEffect(() => {
      window.addEventListener('resize', updateBorderRadius);
      updateBorderRadius();

      return () => window.removeEventListener('resize', updateBorderRadius);
    }, [props]);
  } else {
    updateBorderRadius();
  }

  const foilClass = classNames({
    'card-foil-overlay': props.foil == Foil.On,
    'card-foil-overlay--half': props.foil == Foil.Partial,
  });

  const placeholder = CoreAssets.cardBack(props.game || Game.MTG, props.quality || ImageQuality.HQ);

  const imgElement = <img ref={imgRef} alt={props.alt} src={imageLoaded ? props.src : placeholder} />;

  const selectOverlayClass = classNames({
    'card-selection-overlay--hover': props.highlightOnHover && hoverEnabled,
    'card-selection-overlay--selected': props.selected === true,
    'card-selection-overlay--multi-selected': props.selected === 'multi',
  });

  return (
    <div
      className={'card-image-container'}
      style={{ width: '100%', height: 'fit-content', borderRadius: imageBorderRadius }}
      onPointerOver={() => setHoverEnabled(true)}
      onPointerOut={() => setHoverEnabled(false)}
    >
      {imgElement}
      {props.foil !== Foil.Off ? <div className={foilClass} /> : null}
      <div className={selectOverlayClass} />
    </div>
  );
};
