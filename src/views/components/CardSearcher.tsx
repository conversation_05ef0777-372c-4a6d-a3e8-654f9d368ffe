import * as Immutable from 'immutable';
import moment from 'moment';
import React from 'react';
import Select, { components, InputActionMeta, OptionsType } from 'react-select';
import * as FocusedCardActions from '../../actions/FocusedCardActions';
import * as request from '../../api/Requests';
import Dispatcher from '../../dispatcher/Dispatcher';
import * as FilterRegex from '../../helpers/filter_regex';
import { AnyCard } from '../../models/AnyCard';
import { AnySet } from '../../models/AnyCardSet';
import { Card } from '../../models/Cards';
import { CardSetFilter } from '../../models/filters/CardSetFilter';
import { Foil } from '../../models/Foil';
import { Game } from '../../models/Game';
import { Language } from '../../models/Language';
import { LoadingMessage, NoOptionsMessage, styleOverride } from '../shared/ReactSelectHelper';
import { CardSearcherImagePreview } from './CardSearcherImagePreview';
import { CardSearcherSuggestion, CardSearcherSuggestionType } from './react-select/CardSearcherSuggestion';
import { CardSetFilterSuggestion } from './react-select/CardSetFilterSuggestion';
import { CardSuggestion } from './react-select/CardSuggestion';

interface IProps {
  dispatcher: Dispatcher;
  chooseSuggestion: (suggestion?: AnyCard) => void;
  searchCards: (query: string) => request.RequestWithPromise<Immutable.List<AnyCard>>;
  searchSets: (query: string) => request.RequestWithPromise<Immutable.List<AnySet>>;
  updateSetFilters: (setFilters: Immutable.Set<CardSetFilter>) => void;
  game: Game;
  language?: Language;
  foil?: Foil;
}

interface IState {
  query: string;
  reactSelectValue: CardSearcherSuggestion[];
  forceMenuOpen: boolean;
  isLoading: boolean;
  options?: OptionsType<CardSearcherSuggestion>;
  chosenCard?: AnyCard;
}

const SingleValueCard = (props: any) => <components.SingleValue {...props}>{props.data.label}</components.SingleValue>;
const MultiValueCard = (props: any) => <components.MultiValue {...props}>{props.data.label}</components.MultiValue>;

const OptionComponent = (props: any) => {
  const newProps = props;
  newProps.innerProps.onFocus = props.data.suggestion['onFocusProp'];
  newProps.innerProps.onBlur = props.data.suggestion['onBlurProp'];
  return <components.Option {...newProps} />;
};

export default class CardSearcher extends React.Component<IProps, IState> {
  private readonly searchBox: React.RefObject<Select<CardSearcherSuggestion>>;

  constructor(props: IProps) {
    super(props);
    this.searchBox = React.createRef<Select<CardSearcherSuggestion>>();
    this.state = {
      query: '',
      reactSelectValue: [],
      isLoading: false,
      // Set to true when set is selected, otherwise menu automatically closes until next user input.
      forceMenuOpen: false,
    };
  }

  private lastQuery: request.Request;
  private lastQueryTime: Date = new Date();

  render() {
    const query = this.state.query;
    return (
      <div className="card-searcher-container">
        <Select
          ref={this.searchBox}
          components={{
            SingleValue: SingleValueCard,
            MultiValue: MultiValueCard,
            NoOptionsMessage,
            LoadingMessage,
            Option: OptionComponent,
            MenuList: (props: any) => {
              if (this.onChangeFocus !== undefined) {
                if (props.focusedOption && props.focusedOption['type'] === CardSearcherSuggestionType.CARD) {
                  this.onChangeFocus(props.focusedOption['value']);
                } else {
                  this.onChangeFocus(undefined);
                }
              }
              return <components.MenuList {...props} />;
            },
          }}
          formatOptionLabel={(option: CardSearcherSuggestion) => option.formatOptionLabel(this.props.game)}
          styles={styleOverride}
          className={'react-select-cards'}
          classNamePrefix="react-select-cards"
          placeholder='Search cards by name. Try "s:<set-name>" to refine your results.'
          onInputChange={this.onChangeQuery.bind(this)}
          onChange={(selectedOptions: CardSearcherSuggestion[]) => this.onChangeSuggestion(selectedOptions)}
          // This is just bind the this context. The methods are never called.
          /* v8 ignore start */
          onBlur={() => this.onBlur.bind(this, query)}
          onFocus={() => this.onFocus.bind(this, query)}
          /* v8 ignore stop */
          onMenuClose={this.onMenuClose.bind(this)}
          inputValue={query}
          noOptionsMessage={this.noOptionsHandler}
          options={this.setSearchActive(query) || query.length > 2 ? this.state.options : []}
          value={this.state.reactSelectValue}
          isMulti={this.state.chosenCard ? false : true}
          filterOption={() => true}
          menuIsOpen={this.state.forceMenuOpen ? true : undefined}
          isLoading={this.state.isLoading}
        />
        <CardSearcherImagePreview
          dispatcher={this.props.dispatcher}
          game={this.props.game}
          foil={this.props.foil}
          language={this.props.language}
        />
      </div>
    );
  }

  private onChangeSuggestion(selectedOptions: CardSearcherSuggestion[]) {
    // Resetting options here prevents users accidentally adding multiple sets in quick succession.
    this.setState({ options: [] });

    if (!selectedOptions) {
      /**
       * This if block is never reached.
       * The react select onChange props fires with a selected option or null when cleared. However, this component is never cleared using the clear button.
       * The option is cleared via the `onChangeQuery` method when the user types in the search box.
       */
      /* v8 ignore start */
      // Nothing selected - reset state
      this.setState({
        query: this.state.chosenCard ? '' : this.state.query,
        reactSelectValue: [],
        chosenCard: undefined,
        forceMenuOpen: false,
      });
      this.props.chooseSuggestion();
      /* v8 ignore start */
    } else {
      // Find any set-based suggestions
      const optionsSet = Immutable.Set<CardSearcherSuggestion>(selectedOptions);
      const cardSetFilters = Immutable.Set<CardSetFilter>(
        Immutable.Set<CardSetFilterSuggestion>(
          optionsSet.filter(
            (suggestion: CardSearcherSuggestion) => suggestion.type === CardSearcherSuggestionType.CARD_SET,
          ),
        ).map((suggestion: CardSetFilterSuggestion) => suggestion.value),
      );

      this.props.updateSetFilters(cardSetFilters);

      // Find the card suggestion
      const foundCardSuggestion = optionsSet.find(
        (suggestion: CardSearcherSuggestion) => suggestion['type'] === CardSearcherSuggestionType.CARD,
      );

      if (foundCardSuggestion) {
        // Save the card
        this.setState({
          query: '',
          chosenCard: foundCardSuggestion['value'] as AnyCard,
          reactSelectValue: [foundCardSuggestion],
          forceMenuOpen: false,
        });
        this.props.chooseSuggestion(foundCardSuggestion['value'] as AnyCard);
      } else {
        // Assume still searching
        const newQuery = this.state.query.replace(FilterRegex.matchSetsReactSelect, '');
        this.setState({
          query: newQuery,
          reactSelectValue: selectedOptions,
          // Ensures that loading screen is displayed until numbers are found, unless newQuery.length < 3.
          forceMenuOpen: newQuery.length > 2,
          isLoading: newQuery.length > 2,
        });
        this.handleCardSearch(newQuery);
      }
    }
  }

  focus() {
    if (this.searchBox.current) {
      this.searchBox.current.focus();
    }
  }

  private onChangeQuery(query: string, evt: InputActionMeta) {
    if (!this.setSearchActive(query) && query.length < 3 && !this.state.chosenCard) {
      this.setState({
        query: query,
        forceMenuOpen: false,
        options: [],
        isLoading: false,
      });
      return;
    } else if (evt.action === 'input-blur' || evt.action === 'menu-close') {
      return;
    } else if (this.state.chosenCard) {
      this.setState({
        query: query,
        reactSelectValue: [],
        chosenCard: undefined,
        options: undefined,
        forceMenuOpen: false,
        isLoading: false,
      });
      this.props.chooseSuggestion();
      return;
    }

    // Partial state reset
    // Very important that this is not deleted, will cause unacceptable lag otherwise.
    this.setState({
      query: query,
      forceMenuOpen: false,
      options: [],
    });

    this.lastQuery && this.lastQuery.abort();
    this.lastQueryTime = new Date();
    const setFilters = FilterRegex.extractFilters(query, FilterRegex.matchSetsReactSelect, true);
    const setNotFilters = FilterRegex.extractFilters(query, FilterRegex.matchSetsReactSelect, false);
    if (query.indexOf('s:') === 0 || setFilters.length > 0) {
      this.setState({
        query: query,
        options: undefined,
        forceMenuOpen: true,
        isLoading: true,
      });
      this.handleSetSearch(setFilters[0], true);
    } else if (query.indexOf('-s:') === 0 || setNotFilters.length > 0) {
      this.setState({
        query: query,
        options: undefined,
        forceMenuOpen: true,
        isLoading: true,
      });
      this.handleSetSearch(setNotFilters[0], false);
    } else if (query.length < 3) {
      /** Unreachable code... */
      /* v8 ignore start */
      this.setState({
        query: query,
        options: undefined,
        forceMenuOpen: false,
        isLoading: false,
      });
      return;
      /* v8 ignore stop */
    } else {
      this.setState({
        isLoading: true,
      });
      this.handleCardSearch(query);
    }
  }

  /**
   * Ignoring following chunk of code because it never gets called...
   *
   * Line 100 and 101 are just binding the context to these methods and never actually calling them.
   */
  /* v8 ignore start */
  private onFocus(query: string, evt: React.SyntheticEvent<HTMLInputElement>) {
    this.setState({
      query: query,
      forceMenuOpen: false,
    });
  }

  private onBlur(query: string, evt: React.SyntheticEvent<HTMLInputElement>) {
    FocusedCardActions.updateFocusedCard(this.props.dispatcher, undefined);
    this.setState({
      query: query,
      forceMenuOpen: false,
    });
  }
  /* v8 ignore stop */

  private onMenuClose() {
    FocusedCardActions.updateFocusedCard(this.props.dispatcher, undefined);
  }

  // TODO: Revisit the set/setNot logic here and in loadOptions when we get "search by set code" implemented on the backend.
  // Maybe it would be possible to search when provided with two characters, but restrict to codes, without too much slowdown?
  private noOptionsHandler(wrapper: { inputValue: string }) {
    const query = wrapper.inputValue;
    const setFilters = FilterRegex.extractFilters(query, FilterRegex.matchSetsReactSelect, true);
    const setNotFilters = FilterRegex.extractFilters(query, FilterRegex.matchSetsReactSelect, false);
    if (setFilters.length > 0 && setFilters[0].length < 1) {
      return null;
    } else if (setNotFilters.length > 0 && setNotFilters[0].length < 1) {
      return null;
    } else if (query.length < 3) {
      return null;
    } else {
      return 'No Results';
    }
  }

  private async handleSetSearch(query: string, include: boolean) {
    // We only want to run a request every 150ms at most otherwise we spam the
    // network and everything slows down
    setTimeout(() => {
      const lastQueryTime = moment(this.lastQueryTime);
      if (moment(new Date()).diff(lastQueryTime, 'milliseconds', true) > 150) {
        const requestPromise = this.props.searchSets(query);
        this.lastQuery = requestPromise.request;
        requestPromise.promise
          .then((cardSets) => {
            this.setState({
              options: this.cardSetsToOptions(cardSets, include),
              isLoading: false,
            });
          })
          .catch((err) => {
            console.error(err);
            this.setState({
              options: undefined,
              isLoading: false,
            });
          });
      }
    }, 200);
  }

  private cardSetsToOptions(sets: Immutable.List<AnySet>, include: boolean): CardSearcherSuggestion[] {
    return sets
      .map((cardSet: AnySet) => new CardSetFilterSuggestion(new CardSetFilter({ include: include, cardSet: cardSet })))
      .map(
        (suggestion: CardSetFilterSuggestion) =>
          new CardSearcherSuggestion(suggestion, CardSearcherSuggestionType.CARD_SET),
      )
      .toArray();
  }

  private async handleCardSearch(query: string) {
    // We only want to run a request every 150ms at most otherwise we spam the
    // network and everything slows down
    setTimeout(() => {
      const lastQueryTime = moment(this.lastQueryTime);
      if (moment(new Date()).diff(lastQueryTime, 'milliseconds', true) > 150) {
        const requestPromise = this.props.searchCards(query);
        this.lastQuery = requestPromise.request;
        requestPromise.promise
          .then((cards: Immutable.List<AnyCard>) => {
            const newOptions = cards
              .map(
                (card: Card) =>
                  new CardSearcherSuggestion(
                    new CardSuggestion(card, this.props.game),
                    CardSearcherSuggestionType.CARD,
                  ),
              )
              .toArray();

            this.setState({
              options: newOptions,
              isLoading: newOptions.length > 0,
            });
          })
          .catch((err) => {
            console.error(err);
            this.setState({
              options: undefined,
              isLoading: false,
            });
          });
      }
    }, 200);
  }

  private setSearchActive(query: string): boolean {
    return query.indexOf('s:') > -1 || query.indexOf('-s:') > -1;
  }

  private onChangeFocus(card?: Card) {
    FocusedCardActions.updateFocusedCard(this.props.dispatcher, card);
  }
}
