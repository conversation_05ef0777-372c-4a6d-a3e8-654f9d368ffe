// Node Modules
import open_in_new from '@iconify/icons-ic/open-in-new';
import style from '@iconify/icons-ic/style';
// Iconify
import { Icon } from '@iconify/react';
import clamp from 'clamp';
import * as Immutable from 'immutable';
import * as React from 'react';
// Actions and Dispatcher
import * as CartActions from '../../actions/CartActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import { TextFormat } from '../../helpers/fmt';
import { CardKingdomService } from '../../lib/carts/CardKingdom';
import { CartService, CartTableGrouping } from '../../lib/carts/CartService';
import { CartLocation } from '../../models/CartButton';
// Models and Classes
import { CartCardRecord, CartData } from '../../models/CartCards';
import { User } from '../../models/Users';
import { BuyLinkIconButtons } from './BuyLinkIconButtons';
import { BuyLinkServiceDropdown } from './BuyLinkServiceDropdown';
// Components
import { CartTable } from './CartTable';
import { Dialog, DialogSize } from './Dialog';
import { IconSelect } from './IconSelect';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  isPublic: boolean;
  isOpen: boolean;
  location: CartLocation;
  cartData: CartData;
  onDismiss?: (evt: React.SyntheticEvent<HTMLElement>) => void;
  linkedCards?: Immutable.Map<string, number>;
}

export const BuyLinkDialog = (props: IProps) => {
  const [cartData, setCartData] = React.useState<CartData | undefined>(undefined);
  const [service, setService] = React.useState<CartService>(new CardKingdomService());
  const [grouping, setGrouping] = React.useState<CartTableGrouping>(CartTableGrouping.NAME);
  const [loadingFlag, setLoadingFlag] = React.useState<boolean>(false);

  async function updateCartData() {
    setLoadingFlag(true);
    if (!User.loggedIn(props.me)) {
      setCartData(props.cartData);
      setLoadingFlag(false);
      return;
    }

    // Program continues if user logged in.
    const cardQuantityData = await CartActions.getCardQuantities(props.cartData, props.me.get('username'));

    let newCartData = CartCardRecord.extendMapFromQuantity(props.cartData, cardQuantityData);

    if (props.location === CartLocation.DECKVIEWER) {
      newCartData = CartCardRecord.initializeDeckTable(props.cartData, newCartData);
    }

    setCartData(newCartData);
    setLoadingFlag(false);
  }

  React.useEffect(() => {
    updateCartData();
  }, [props.cartData.get('data').size > 0]);

  if (!props.isOpen) {
    return null;
  }
  // If totalCards is zero either the user has set every card quantity to zero or has cleared the list of cards.
  let totalCards: number | undefined = 0;
  if (cartData !== undefined) {
    totalCards = cartData.get('data').reduce((reduction: number, value: CartCardRecord) => {
      const desired = value.get('quantity');
      if (desired) {
        return reduction + desired;
      } else {
        return reduction;
      }
    }, 0);
  }

  // submitDisabled if loading or the user has set every card quantity to zero or has cleared the list of cards.
  const submitDisabled = totalCards === 0 || loadingFlag;
  const buttonClassName = submitDisabled ? 'button-disabled' : 'button-primary';
  const cardsText =
    props.cartData === undefined || loadingFlag ? 'Loading...' : TextFormat.pluralizedCardsCapitalized(totalCards);
  return (
    <Dialog isOpen={props.isOpen} onDismiss={props.onDismiss} heading="Buy Cards" size={DialogSize.LARGE_DYNAMIC}>
      <div className="heading-divider-wrapper" />
      <div className="row cart-button-text-container">
        <div className="col-xs-4" style={{ margin: '1rem 0' }}>
          <div className="info-heading">Purchasing</div>
          <div className="info-block">{cardsText}</div>
        </div>
        <div className="col-xs-4" style={{ margin: '0.6rem 0' }}>
          <div className="info-heading">Service</div>
          <BuyLinkServiceDropdown
            onSelect={(service: CartService) => {
              updateService(service);
            }}
          />
        </div>
        <div className="col-xs-4" style={{ margin: '0.6rem 0' }}>
          <div className="info-heading">Group By</div>
          <IconSelect
            className="icon-container--card-input"
            icon={style}
            onChange={(evt: any) => {
              updateGrouping(evt.currentTarget.value as CartTableGrouping);
            }}
            value={grouping}
          >
            <>
              <option disabled>Group By</option>
              {service.validGroupings.map((grouping: CartTableGrouping) => {
                return (
                  <option key={grouping} value={grouping}>
                    {grouping}
                  </option>
                );
              })}
            </>
          </IconSelect>
        </div>
      </div>
      <BuyLinkIconButtons
        location={props.location}
        isPublic={props.isPublic}
        loggedIn={User.loggedIn(props.me)}
        disabled={cartData === undefined && loadingFlag}
        linkedCards={props.linkedCards}
        onReset={updateCartData}
        onCompletePlayset={onCompletePlayset}
        onPurchaseAllCards={onPurchaseAllCards}
        onAddLinkedCards={onAddLinkedCards}
      />
      <CartTable
        location={props.location}
        grouping={grouping}
        cartData={cartData}
        isPublic={props.isPublic}
        loading={loadingFlag}
        loggedIn={User.loggedIn(props.me)}
        onUpdateMap={onUpdateMap}
        onDeleteCell={onDeleteCell}
      />
      <div className="buy-link-row">
        <form id="ID" target="_blank" action={service.partnerURL} method="post">
          <input type="hidden" id="c" name="c" value={service.generatePayload(grouping, cartData)} />
          <input type="hidden" id="partner" name="partner" value={service.partnerCode} />
          <button type="submit" disabled={submitDisabled} className={buttonClassName}>
            <span>Send to Cart</span>
            <Icon height={'15px'} width={'15px'} icon={open_in_new} />
          </button>
        </form>
        <button className="button-alert" onClick={onDismiss}>
          Cancel
        </button>
      </div>
    </Dialog>
  );

  function onDismiss(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    evt.stopPropagation();
    props.onDismiss && props.onDismiss(evt);
  }

  function onUpdateMap(jsonID: string, value: string) {
    if (cartData === undefined) {
      return;
    }
    const currentRecord = cartData.get('data').get(jsonID);
    if (value === '') {
      const oldData = cartData.get('data');
      const newData = oldData.set(jsonID, oldData.get(jsonID).set('quantity', undefined));
      setCartData(cartData.set('data', newData));
      return;
    }
    const newQuantity = Number.parseInt(value);
    if (newQuantity < 0 || newQuantity > 100) {
      return;
    }
    const oldData = cartData.get('data');
    const newData = oldData.set(jsonID, oldData.get(jsonID).set('quantity', newQuantity));
    setCartData(cartData.set('data', newData));
  }

  function onDeleteCell(jsonID: string) {
    if (cartData === undefined) {
      return;
    }
    const newData = cartData.get('data').delete(jsonID);
    setCartData(cartData.set('data', newData));
  }

  function onCompletePlayset() {
    if (cartData === undefined) {
      return;
    }
    setLoadingFlag(true);
    const groupedCardData = cartData.groupCartData(grouping);
    const completedCardData = completePlayset(groupedCardData);
    const newCartData = ungroupCartData(completedCardData);
    setCartData(newCartData);
    setLoadingFlag(false);
  }

  function onPurchaseAllCards() {
    if (cartData === undefined) {
      return;
    }
    setLoadingFlag(true);
    setCartData(CartCardRecord.purchaseAllCards(cartData));
    setLoadingFlag(false);
  }

  function completePlayset(groupedData: Immutable.Map<string, Immutable.List<CartCardRecord>>) {
    return groupedData
      .map((list: Immutable.List<CartCardRecord>) => {
        const ownedCount = list.reduce((reduction: number, record: CartCardRecord) => {
          const current = Number.isNaN(record.get('ownedCount')) ? 0 : record.get('ownedCount');
          return reduction + current;
        }, 0);
        const newValue = 4 - ownedCount;
        return list
          .first()
          .set('quantity', newValue < 0 ? 0 : newValue)
          .set('ownedCount', ownedCount);
      })
      .toMap();
  }

  function ungroupCartData(completedCardData: Immutable.Map<string, CartCardRecord>) {
    return new CartData({
      data: completedCardData.mapEntries((entry: (string | CartCardRecord)[]) => {
        const record = entry[1] as CartCardRecord;
        return [record.get('cartCard').get('jsonID'), record];
      }) as Immutable.Map<string, CartCardRecord>,
    });
  }

  async function onAddLinkedCards() {
    if (props.linkedCards === undefined || cartData === undefined) {
      return;
    }
    setLoadingFlag(true);
    // Stops TS complaining about props.linkedCards potentially being undefined.
    const linkedCards = props.linkedCards;
    const cardQuantityData = await CartActions.getCardQuantities(props.cartData, props.me.get('username'));
    const newCartData = new CartData({
      data: Immutable.Map<string, CartCardRecord>(
        CartCardRecord.extendMapFromQuantity(props.cartData, cardQuantityData)
          .get('data')
          .map((record: CartCardRecord) => {
            const cardName = record.get('cartCard').get('name');
            if (!linkedCards.has(cardName)) {
              return record.set('quantity', record.get('deckCount'));
            }
            // Stops TS complaining about record.get('quantity') being undefined.
            const newValue: number = record.get('deckCount') === undefined ? 0 : record.get('deckCount');
            return record.set('quantity', clamp(newValue - linkedCards.get(cardName), 0, 100));
          }),
      ),
    });
    setCartData(newCartData);
    setLoadingFlag(false);
  }

  function updateService(newService: CartService) {
    if (!newService.validGroupings.contains(grouping)) {
      setGrouping(CartTableGrouping.NAME);
    }
    setService(newService);
    updateCartData();
  }

  function updateGrouping(newGrouping: CartTableGrouping) {
    if (service.validGroupings.contains(newGrouping)) {
      setGrouping(newGrouping);
      updateCartData();
    }
  }
};
