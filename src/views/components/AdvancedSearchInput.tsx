import * as React from 'react';
import * as ReactDOM from 'react-dom';
import * as FilterActions from '../../actions/FilterActions';
import * as CardSetsApi from '../../api/CardSets';
import * as request from '../../api/Requests';
import Dispatcher from '../../dispatcher/Dispatcher';
import * as FilterRegex from '../../helpers/filter_regex';
import { CardSet } from '../../models/CardSets';
import { MTGFilter } from '../../models/filters/mtg/MTGFilters';
import { MTGCardPage } from '../../models/mtg/MTGCardPage';
import { ClearFilterButton } from './filters/ClearFilterButton';
import { FormLabel } from './FormLabel';

interface IProps {
  dispatcher: Dispatcher;
  cardPage: MTGCardPage;
  rawQuery?: string;
  filterOptions: MTGFilter;
}

interface IState {
  cursor?: number;
  isFocus?: boolean;

  suggestions?: Array<string>;
  suggestionsIndex?: number;
}

export class AdvancedSearchInput extends React.Component<IProps, IState> {
  public refs: {
    [key: string]: Element;
    advancedSearchInput: HTMLInputElement;
  };

  constructor(props: IProps) {
    super(props);
    this.state = {
      cursor: 0,
      isFocus: false,
      suggestions: [],
      suggestionsIndex: -1,
    };
  }

  public render() {
    let htmlStr = this.props.rawQuery || '';
    this.matches().forEach((match) => {
      htmlStr = htmlStr.replace(match, '<span class="advanced-search-input__tag">$1</span>');
    });
    return (
      <>
        <div className="flex col-xs-12 filter-no-padding">
          <FormLabel heading="Search" />
          <div className="col-xs-6 filter-no-padding flex justify-end">
            <ClearFilterButton onClick={this.onClear.bind(this)} label="Clear" />
          </div>
        </div>
        <form
          className={'advanced-search-input' + (this.state.isFocus ? ' is-focus' : '')}
          onClick={this.onClick.bind(this)}
          onSubmit={this.onSubmit.bind(this)}
        >
          <div className="formatted-input">
            {!this.props.rawQuery || !this.props.rawQuery.length ? (
              'Search your collection'
            ) : (
              <span
                style={{ whiteSpace: 'pre-wrap' }}
                dangerouslySetInnerHTML={{
                  __html: htmlStr,
                }}
              />
            )}
          </div>
          <input
            ref="advancedSearchInput"
            type="text"
            value={this.props.rawQuery || ''}
            onClick={this.onClick.bind(this)}
            onChange={this.onChange.bind(this)}
            onKeyDown={this.onKeyDown.bind(this)}
            onFocus={this.onFocus.bind(this)}
          />

          {this.state.isFocus && this.state.suggestions && this.state.suggestions.length ? (
            <div className="advanced-search-input__suggestions">
              {(this.state.suggestions || []).map((suggestion, index) => (
                <div
                  key={'suggestion-' + suggestion + '-' + index}
                  className={
                    'advanced-search-input__suggestion' + (index === this.state.suggestionsIndex ? ' is-selected' : '')
                  }
                  onClick={this.onClickSuggestion.bind(this, suggestion)}
                >
                  {suggestion}
                </div>
              ))}
            </div>
          ) : null}
        </form>
      </>
    );
  }

  private onClick(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.ignoreDocumentClick = true;
  }

  private onClear() {
    FilterActions.updateQuery('', this.props.dispatcher);
    this.setState({
      suggestions: [],
      suggestionsIndex: -1,
    });
  }

  private onChange(evt: React.SyntheticEvent<HTMLInputElement>) {
    evt.preventDefault();

    // autocomplete for tags
    const query = evt.currentTarget.value;
    let matched = false;
    query.replace(FilterRegex.matchTags, (m, p1, p2, p3, offset) => {
      if (this.validateSelectionStart(m, offset)) {
        request
          .get('/api/v3/tags?query=' + m.split(':')[1])
          .end()
          .then((res) => {
            this.setState({ suggestions: (res.body.tags || []).map((tag: any) => tag.name) });
          })
          .catch((err) => {
            console.error(err);
            this.setState({ suggestions: [] });
          });
        matched = true;
      }
      return m;
    });
    // autocomplete for sets
    query.replace(FilterRegex.matchSets, (m, p1, p2, p3, offset) => {
      if (this.validateSelectionStart(m, offset)) {
        CardSetsApi.search(m.split(':')[1])
          .then((sets) => {
            this.setState({
              suggestions: (sets.toArray() || []).map((set: CardSet) => set.get('name')),
            });
          })
          .catch((err) => {
            console.error(err);
            this.setState({ suggestions: [] });
          });

        matched = true;
      }
      return m;
    });
    // autocomplete for colors
    query.replace(FilterRegex.matchColors, (m, p1, p2, p3, offset) => {
      if (this.validateSelectionStart(m, offset)) {
        this.setState({
          suggestions: ['red', 'green', 'blue', 'white', 'black', 'colorless'].filter(
            (suggestion) => suggestion.indexOf(m.split(':')[1]) >= 0,
          ),
        });
        matched = true;
      }
      return m;
    });
    // autocomplete for foils
    query.replace(FilterRegex.matchFoils, (m, p1, p2, p3, offset) => {
      if (this.validateSelectionStart(m, offset)) {
        this.setState({
          suggestions: ['true', 'false'].filter((suggestion) => suggestion.indexOf(m.split(':')[1]) >= 0),
        });
        matched = true;
      }
      return m;
    });

    FilterActions.updateQuery(query, this.props.dispatcher);
    if (!matched) {
      this.setState({
        suggestions: [],
        suggestionsIndex: -1,
      });
    } else {
      this.setState({
        suggestionsIndex: -1,
      });
    }
  }

  private validateSelectionStart(replacement: string, offset: number) {
    const input = ReactDOM.findDOMNode(this.refs.advancedSearchInput) as HTMLInputElement;
    return (
      input != null &&
      input.selectionStart != null &&
      input.selectionStart >= offset &&
      input.selectionStart <= offset + replacement.length
    );
  }

  private onSubmit(evt: React.SyntheticEvent<HTMLFormElement>) {
    evt.preventDefault();
    FilterActions.activateFilter(
      this.props.cardPage,
      this.props.filterOptions,
      MTGFilter.fromString(this.props.rawQuery),
      this.props.dispatcher,
    );
  }

  private onKeyDown(evt: React.KeyboardEvent<HTMLInputElement>) {
    switch (evt.keyCode) {
      case 13: {
        // ENTER
        // replace query with suggestion
        let replaced = false;
        let query = this.props.rawQuery || '';
        this.matches().forEach((match) => {
          query = query.replace(match, (m, p1, p2, p3, offset) => {
            if (this.validateSelectionStart(m, offset)) {
              if (this.state.suggestions && this.state.suggestions.length) {
                replaced = true;
                const suggestion = this.state.suggestions[Math.max(this.state.suggestionsIndex!, 0)];
                return (
                  m.split(':')[0] + ':' + (suggestion.indexOf(' ') >= 0 ? '"' + suggestion + '"' : suggestion) + ' '
                );
              }
              return m;
            }
            return m;
          });
        });
        // if an item was replaced we should cancel the event
        if (replaced) {
          FilterActions.updateQuery(query, this.props.dispatcher);
          this.setState({
            suggestions: [],
            suggestionsIndex: -1,
          });
          evt.preventDefault();
          evt.stopPropagation();
        }
        break;
      }

      case 38: // UP
        evt.preventDefault();
        evt.stopPropagation();

        if (this.state.suggestions && this.state.suggestions.length) {
          this.setState({
            suggestionsIndex: Math.max(this.state.suggestionsIndex! - 1, -1),
          });
        }
        break;

      case 40: // DOWN
        evt.preventDefault();
        evt.stopPropagation();

        if (this.state.suggestions && this.state.suggestions.length) {
          this.setState({
            suggestionsIndex: Math.min(this.state.suggestionsIndex! + 1, this.state.suggestions.length - 1),
          });
        }
        break;
    }
  }

  private onClickSuggestion(suggestion: string, evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    evt.stopPropagation();

    // replace query with suggestion
    let query = this.props.rawQuery || '';
    this.matches().forEach((match) => {
      query = query.replace(match, (m, p1, p2, p3, offset) => {
        if (this.validateSelectionStart(m, offset)) {
          return m.split(':')[0] + ':' + (suggestion.indexOf(' ') >= 0 ? '"' + suggestion + '"' : suggestion);
        }
        return m;
      });
    });

    FilterActions.updateQuery(query, this.props.dispatcher);
    this.setState({
      suggestions: [],
      suggestionsIndex: -1,
    });
    (ReactDOM.findDOMNode(this.refs.advancedSearchInput) as HTMLInputElement).focus();

    this.ignoreDocumentClick = true;
  }

  private onFocus(evt: React.SyntheticEvent<HTMLInputElement>) {
    this.setState({
      isFocus: true,
    });
    document.addEventListener('click', this.handleDocumentClick);
  }

  private matches() {
    return [FilterRegex.matchTags, FilterRegex.matchSets, FilterRegex.matchFoils, FilterRegex.matchColors];
  }

  private ignoreDocumentClick = false;
  private handleDocumentClick = (evt: Event) => {
    if (this.ignoreDocumentClick) {
      this.ignoreDocumentClick = false;
      return;
    }
    this.setState({
      isFocus: false,
    });
    document.removeEventListener('click', this.handleDocumentClick);
  };
}
