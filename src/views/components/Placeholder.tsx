import * as React from 'react';
import history from '../../helpers/history';

interface IProps {
  message: string;
  button: string | null;
  href?: string;
  onClick?: (evt: React.SyntheticEvent<HTMLElement>) => void;
}

interface IState {}

export class Placeholder extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  public render() {
    return (
      <div className="placeholder">
        <p className="placeholder-message">{this.props.message}</p>
        {this.props.button ? (
          <div className="placeholder-button" onClick={this.onClickButton.bind(this)}>
            {this.props.button}
          </div>
        ) : null}
      </div>
    );
  }

  private onClickButton(evt: React.SyntheticEvent<HTMLElement>) {
    if (this.props.onClick) {
      return this.props.onClick(evt);
    }
    evt.preventDefault();
    if (this.props.href) {
      history.push(this.props.href);
    }
  }
}
