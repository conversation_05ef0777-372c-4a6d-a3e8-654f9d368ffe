import { render } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it } from 'vitest';
import { TruncatedText } from './TruncatedText';

type TruncatedTextProps = React.ComponentProps<typeof TruncatedText>;

const defaultListString = ['First String'];
const DEFAULT_PROPS = {
  listOfStrings: Immutable.List(defaultListString),
};
function renderTruncateText(props: Partial<TruncatedTextProps> = {}) {
  return render(<TruncatedText {...{ ...DEFAULT_PROPS, ...props }} />);
}
describe('TruncatedText', () => {
  describe('when listOfStrings is single string', () => {
    it('renders a single string without truncation', () => {
      const { container } = renderTruncateText();
      expect(container.textContent).toBe(defaultListString[0]);
    });
  });
  describe('when listOfStrings is multiple strings', () => {
    it('renders a first string and truncates the rest', () => {
      const longListString = ['First String', 'Second String', 'Third String'];
      const { container } = renderTruncateText({
        listOfStrings: Immutable.List(longListString),
      });
      const countTruncate = longListString.length - 1;
      expect(container.textContent).toBe(`${longListString[0]}, and ${countTruncate} more`);
    });
  });
});
