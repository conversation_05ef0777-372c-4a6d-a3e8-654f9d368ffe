import clamp from 'clamp';
import React, { useRef, useState } from 'react';
import { ImageHelper } from '../../helpers/img';
import { ImageRotation } from './cardpanel/CardPanelStack';

interface IProps {
  src: string;
  alt: string;
  rotation?: ImageRotation;
}

enum Dimension {
  WIDTH,
  HEIGHT,
}

class ZoomTransform {
  readonly scale: number;
  readonly x: number;
  readonly y: number;

  constructor(scale = 1.0, x = 0, y = 0) {
    this.scale = scale;
    this.x = x;
    this.y = y;
  }

  style(): string {
    return `scale(${this.scale}) translate(${this.x}%, ${this.y}%)`;
  }
}

export const ScannedImage = (props: IProps) => {
  const [imageBorderRadius, setImageBorderRadius] = useState<string>('0px');

  const imgRef = useRef<HTMLImageElement>(null);
  const keyDimension = props.rotation != ImageRotation.NONE ? Dimension.HEIGHT : Dimension.WIDTH;

  // Adjusts border radius to be proportional to actual image width
  React.useLayoutEffect(() => {
    const updateBorderRadius = () => {
      if (imgRef.current) {
        let size: number;
        if (keyDimension == Dimension.WIDTH) {
          size = imgRef.current.offsetWidth;
        } else {
          size = imgRef.current.offsetHeight;
        }

        setImageBorderRadius(`${size * 0.05}px`);
      }
    };

    window.addEventListener('resize', updateBorderRadius);
    updateBorderRadius();

    return () => window.removeEventListener('resize', updateBorderRadius);
  }, [props]);

  let containerSize: React.CSSProperties;
  let imgSize: React.CSSProperties;

  if (keyDimension == Dimension.WIDTH) {
    containerSize = { width: '100%', height: 'fit-content' };
    imgSize = { width: '100%' };
  } else {
    containerSize = { height: '100%', width: 'fit-content' };
    imgSize = { height: '100%' };
  }

  const [zoomEnabled, setZoomEnabled] = useState<boolean>(false);
  const [zoomPosition, setZoomPosition] = useState<ZoomTransform>(new ZoomTransform());
  let zoomStyle: React.CSSProperties = {};
  if (zoomEnabled) {
    zoomStyle = { cursor: 'zoom-out', transform: zoomPosition.style() };
  } else {
    zoomStyle = { cursor: 'zoom-in' };
  }

  const imgElement = <img ref={imgRef} alt={props.alt} src={props.src} style={{ ...zoomStyle, ...imgSize }} />;

  ImageHelper.preload(props.src);

  return (
    <div
      className={'card-image-container'}
      style={{ ...containerSize, borderRadius: imageBorderRadius }}
      onPointerDown={() => setZoomEnabled(!zoomEnabled)}
      onPointerMove={(evt) => {
        const rect = evt.currentTarget.getBoundingClientRect();
        const relativeX = evt.clientX - rect.left;
        const relativeY = evt.clientY - rect.top;
        const width = rect.right - rect.left;
        const height = rect.bottom - rect.top;
        const centerX = width / 2;
        const centerY = height / 2;
        const absoluteX = relativeX - centerX;
        const absoluteY = relativeY - centerY;
        const percentCenterX = absoluteX / centerX;
        const percentCenterY = absoluteY / centerY;

        const zoomScale = 2.0;
        // Prevents the position translation from pushing the image off the bounding rectangle
        const maxDeviation = (1 - zoomScale) / 2 / zoomScale;

        const clampedX = clamp(percentCenterX, -maxDeviation, maxDeviation);
        const clampedY = clamp(percentCenterY, -maxDeviation, maxDeviation);

        let zoomTransform: ZoomTransform;
        switch (props.rotation) {
          case undefined:
          case ImageRotation.NONE:
            zoomTransform = new ZoomTransform(zoomScale, -clampedX * 100, -clampedY * 100);
            break;
          case ImageRotation.ROTATED:
            zoomTransform = new ZoomTransform(zoomScale, -clampedY * 100, clampedX * 100);
            break;
          case ImageRotation.ROTATED_FLIPPED:
            zoomTransform = new ZoomTransform(zoomScale, clampedY * 100, -clampedX * 100);
            break;
        }

        setZoomPosition(zoomTransform);
      }}
    >
      {imgElement}
    </div>
  );
};
