import * as React from 'react';

interface IProps {}

interface IState {
  isScrollingUp: boolean;
}

export class ScrollToTop extends React.Component<IProps, IState> {
  private previousTop: number;
  private onScroll = (evt: Event) => {
    // this magic number is the scroll height at which the select/tags component starts to stick
    // it enables us to detect that the user is scrolling down but to only update the state after
    // the select/tag component has changed state. if we don't do this, things jitter.
    // 256 = 228 (height of filtering section) + 28 (height of section padding)
    if ($(document).scrollTop() > 256) {
      // scrolling up
      if ($(document).scrollTop() < this.previousTop) {
        if (!this.state.isScrollingUp) {
          // embed the within this check to prevent always changing state
          if ($(document).scrollTop() > $(window).height() * 4) {
            // only activate after a certain amount of scrolling
            this.setState({ isScrollingUp: true });
          }
        }
      } else {
        if (this.state.isScrollingUp) {
          // embed the within this check to prevent always changing state
          this.setState({ isScrollingUp: false });
        }
      }
    } else {
      if (this.state.isScrollingUp) {
        // embed the within this check to prevent always changing state
        this.setState({ isScrollingUp: false });
      }
    }
    this.previousTop = $(document).scrollTop();
  };

  constructor(props: IProps) {
    super(props);
    this.state = {
      isScrollingUp: false,
    };
  }

  public componentDidMount() {
    this.previousTop = $(document).scrollTop();
    $(window).scroll(this.onScroll);
  }

  public componentWillUnmount() {
    $(window).off('scroll', this.onScroll);
  }

  public render() {
    return (
      <div className={'scroll-to-top' + (!this.state.isScrollingUp ? ' is-hidden' : '')}>
        <div className="scroll-to-top-button" onClick={this.onClick.bind(this)}>
          Scroll to top
        </div>
      </div>
    );
  }

  private onClick(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    $('html, body').animate({ scrollTop: '0px' }, $(document).scrollTop() / 10);
  }
}
