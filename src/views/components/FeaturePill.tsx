import * as React from 'react';

export enum PillColor {
  RED = 'red',
  BLUE = 'blue',
  GREEN = 'green',
}

export enum PillSize {
  EXTRA_SMALL = 'xs',
  SMALL = 'sm',
}

interface IProps {
  readonly text: string;
  readonly color: PillColor;
  readonly size?: PillSize;
}

export const FeaturePill = (props: IProps) => {
  const size = props.size || PillSize.SMALL;
  let className = `feature-pill--${props.color}`;
  if (size == PillSize.EXTRA_SMALL) {
    className += ` feature-pill-size--xs`;
  }

  return <div className={className}>{props.text}</div>;
};
