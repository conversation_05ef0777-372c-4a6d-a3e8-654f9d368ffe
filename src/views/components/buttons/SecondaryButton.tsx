import { Icon, IconifyIcon } from '@iconify/react';
import * as React from 'react';

interface IProps {
  icon: IconifyIcon; // Expecting an icon class from @iconify
  text: string;
  onClick: () => void;
  disabled?: boolean;
}

export const SecondaryButton = (props: IProps) => {
  return (
    <div
      className="flex secondary-button"
      style={{ cursor: props.disabled ? 'default' : 'pointer' }}
      onClick={(evt: any) => {
        evt.preventDefault();
        if (!props.disabled) {
          props.onClick();
        }
      }}
    >
      <Icon height={'16px'} width={'16px'} icon={props.icon} style={{ alignSelf: 'center' }} />
      <span style={{ marginLeft: '0.25rem' }}>{props.text}</span>
    </div>
  );
};
