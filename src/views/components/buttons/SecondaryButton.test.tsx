import { faker } from '@faker-js/faker';
import plusIcon from '@iconify/icons-ic/baseline-plus';
import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { SecondaryButton } from './SecondaryButton';

const DEFAULT_PROPS = {
  icon: plusIcon,
  text: '',
  onClick: vi.fn(),
};
type SecondaryButtonProps = React.ComponentProps<typeof SecondaryButton>;
function renderSecondaryButton(rawProps: Partial<SecondaryButtonProps> = {}) {
  const props = { ...DEFAULT_PROPS, ...rawProps };
  return render(<SecondaryButton {...props} />);
}

describe('SecondaryButton', () => {
  beforeEach(() => {
    DEFAULT_PROPS.text = faker.string.sample();
    DEFAULT_PROPS.onClick = vi.fn();
  });
  describe('render the component SecondaryButton', () => {
    it('Renders text and icon', () => {
      renderSecondaryButton();
      expect(screen.getByText(DEFAULT_PROPS.text)).toBeInTheDocument();
      const button = screen.getByText(DEFAULT_PROPS.text).parentElement as HTMLElement;
      expect(button.querySelector('svg')).toBeInTheDocument();
    });
  });

  describe('When component SecondaryButton not disabled', () => {
    it('Applies pointer cursor', () => {
      renderSecondaryButton();
      const button = screen.getByText(DEFAULT_PROPS.text).parentElement as HTMLElement;
      expect(button).toHaveStyle({ cursor: 'pointer' });
    });
    it('Call onClick', () => {
      renderSecondaryButton();
      fireEvent.click(screen.getByText(DEFAULT_PROPS.text));
      expect(DEFAULT_PROPS.onClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('When component SecondaryButton disabled', () => {
    it('Applies correct cursor style', () => {
      renderSecondaryButton({ disabled: true });
      const button = screen.getByText(DEFAULT_PROPS.text).parentElement as HTMLElement;
      expect(button).toHaveStyle({ cursor: 'default' });
    });
    it('Does not call onClick', () => {
      renderSecondaryButton({ disabled: true });
      fireEvent.click(screen.getByText(DEFAULT_PROPS.text));
      expect(DEFAULT_PROPS.onClick).not.toHaveBeenCalled();
    });
  });
});
