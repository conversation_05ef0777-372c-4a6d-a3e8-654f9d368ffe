import classNames from 'classnames';
import * as Immutable from 'immutable';
import * as React from 'react';
import { TabItem } from '../../models/TabItem';

interface IProps {
  pages: Immutable.List<TabItem>;
  selectedTab: string;
  onChangeTab: (selectedPage: string) => void;
}

export function TabBar(props: IProps): JSX.Element {
  const selectedItem = props.pages.find((tabItem: TabItem) => tabItem.get('title') === props.selectedTab);
  const selectionUnavailable = selectedItem && selectedItem.get('disabled');

  return (
    <div className="flex">
      {props.pages.map((tab: TabItem, index: number) => {
        // Select the first item if the selection is unavailable
        const fallbackSelection = index === 0 && selectionUnavailable === true;

        const className = classNames({
          'tab-bar__left': index === 0,
          'tab-bar__right': index === props.pages.size - 1,
          'tab-bar': index !== 0 && index !== props.pages.size - 1,
          disabled: tab.get('disabled'),
          selected: fallbackSelection || (!tab.get('disabled') && tab.get('title') === props.selectedTab),
        });

        return (
          <div
            key={index}
            className={className}
            onClick={(evt) => {
              evt.preventDefault();
              if (!tab.get('disabled')) {
                props.onChangeTab(tab.get('title'));
              }
            }}
          >
            <div className="tab-bar-heading">{tab.get('title')}</div>
          </div>
        );
      })}
    </div>
  );
}
