import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it } from 'vitest';
import { YugiRarity } from '../../../models/yugioh/YugiRarity';
import { SetSymbolSize } from '../SetSymbol';
import { YugiSetSymbol } from './YugiSetSymbol';

type YugiSetSymbolProps = React.ComponentProps<typeof YugiSetSymbol>;

const DEFAULT_PROPS: YugiSetSymbolProps = {
  setName: 'Test SetName',
  setUUID: 'test-uuid',
  hoverText: true,
  collectorNumber: '123546',
  rarity: YugiRarity.COMMON,
  size: SetSymbolSize.XS,
};

const renderYugiSetSymbol = (props: Partial<YugiSetSymbolProps> = {}) => {
  return render(<YugiSetSymbol {...DEFAULT_PROPS} {...props} />);
};

const showTooltip = (container: HTMLElement) => {
  const symbolContainer = container.querySelector(`.flex.set-symbol__${DEFAULT_PROPS.size}`) as HTMLElement;
  fireEvent.mouseEnter(symbolContainer);
  return symbolContainer;
};
describe('YugiSetSymbol', () => {
  describe('component render', () => {
    it('renders image based on setUUID', () => {
      renderYugiSetSymbol();
      const img = screen.getByRole<HTMLImageElement>('img');
      expect(img.src).toContain(DEFAULT_PROPS.setUUID?.[0]);
      expect(img.src).toContain(DEFAULT_PROPS.setUUID?.[1]);
      expect(img.src).toContain(`${DEFAULT_PROPS.setUUID}.png`);
    });
    it('renders correct class based on size', () => {
      const { container } = renderYugiSetSymbol({ size: SetSymbolSize.XL });
      expect(container.querySelector(`.flex.set-symbol__${SetSymbolSize.XL}`)).toBeInTheDocument();
    });
    test('defaults to XS size when size prop is undefined', () => {
      const { container } = renderYugiSetSymbol({ size: undefined });
      expect(container.querySelector('.flex.set-symbol__xs')).toBeInTheDocument();
    });
  });

  describe('hover text', () => {
    it('does not show when hoverText is false', () => {
      const { container } = renderYugiSetSymbol({ hoverText: false });
      showTooltip(container);
      const tooltip = container.querySelector('.set-symbol-tooltip-container');
      expect(tooltip).not.toBeInTheDocument();
    });
    it('shows on mouse enter', () => {
      const { container } = renderYugiSetSymbol();
      showTooltip(container);
      const tooltipContent = container.querySelector('.set-symbol-tooltip');
      expect(tooltipContent).toHaveTextContent(DEFAULT_PROPS.setName);
      expect(tooltipContent).toHaveTextContent(`${DEFAULT_PROPS.rarity} - ${DEFAULT_PROPS.collectorNumber}`);
    });
    it('hides on mouse leave', () => {
      const { container } = renderYugiSetSymbol();
      const symbolContainer = showTooltip(container);
      fireEvent.mouseLeave(symbolContainer);
      const tooltip = container.querySelector('.set-symbol-tooltip-container');
      expect(tooltip).not.toBeInTheDocument();
    });
  });
});
