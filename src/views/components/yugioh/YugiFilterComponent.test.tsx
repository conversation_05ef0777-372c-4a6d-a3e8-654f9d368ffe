import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { FilterComponentType } from '../../../models/FilterComponent';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { YugiFilter } from '../../../models/filters/yugioh/YugiFilters';
import { YugiFilterComponent } from './YugiFilterComponent';
type YugiFilterComponentProps = React.ComponentProps<typeof YugiFilterComponent>;

const DEFAULT_PROPS: Omit<YugiFilterComponentProps, 'dispatcher'> = {
  filter: new YugiFilter(),
  type: FilterComponentType.QUERY,
  displayType: FilterDisplay.PERMANENT,
  onUpdate: vi.fn(),
  disabled: false,
};

const renderYugiFilterComponent = (props: Partial<YugiFilterComponentProps> = {}) => {
  return renderWithDispatcher(YugiFilterComponent, { ...DEFAULT_PROPS, ...props });
};

describe('YugiFilterComponent', () => {
  describe('FilterComponentType', () => {
    describe('when type prop is QUERY', () => {
      it('renders QueryFilter ', () => {
        renderYugiFilterComponent();
        expect(screen.getByText('Card Name')).toBeInTheDocument();
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
      test('triggers onUpdate when the input value changes', () => {
        renderYugiFilterComponent();
        fireEvent.change(screen.getByRole('textbox'), { target: { value: 'test query' } });
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      test('triggers onUpdate when clear button clicked', () => {
        renderYugiFilterComponent();
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      it('uses disabled prop', () => {
        renderYugiFilterComponent({ disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();
      });
    });

    describe('when type prop is PRICE', () => {
      it('renders PriceMinMaxFilters ', () => {
        renderYugiFilterComponent({
          type: FilterComponentType.PRICE,
        });
        expect(screen.getByText('Price Range')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Min Price')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Max Price')).toBeInTheDocument();
      });
      test('triggers onUpdate when the input value changes', () => {
        renderYugiFilterComponent({
          type: FilterComponentType.PRICE,
        });
        fireEvent.change(screen.getByPlaceholderText('Min Price'), { target: { value: '10000' } });
        fireEvent.change(screen.getByPlaceholderText('Max Price'), { target: { value: '12000' } });
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalledTimes(2);
      });
      test('triggers onUpdate when clear button clicked', () => {
        renderYugiFilterComponent({
          type: FilterComponentType.PRICE,
        });
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      it('uses disabled prop', () => {
        renderYugiFilterComponent({ type: FilterComponentType.PRICE, disabled: true });
        expect(screen.getByPlaceholderText('Min Price')).toBeDisabled();
        expect(screen.getByPlaceholderText('Max Price')).toBeDisabled();

        // Testing click behavior rather than .toBeDisabled() because this component
        // doesn't use a <button> element, so we verify the disabled functionality
        // by ensuring the onClick handler is not triggered when disabled
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).not.toHaveBeenCalled();
      });
    });

    describe('when type prop is STARTS_WITH', () => {
      it('renders StartsWithFilter with title prop is "Starts With"', () => {
        renderYugiFilterComponent({
          type: FilterComponentType.STARTS_WITH,
        });
        expect(screen.getByText('Starts With')).toBeInTheDocument();
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
      test('triggers onUpdate when the input value changes', () => {
        renderYugiFilterComponent({
          type: FilterComponentType.STARTS_WITH,
        });
        fireEvent.change(screen.getByRole('textbox'), { target: { value: 's:' } });
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      test('triggers onUpdate when clear button clicked', () => {
        renderYugiFilterComponent({
          type: FilterComponentType.STARTS_WITH,
        });
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      it('uses disabled prop', () => {
        renderYugiFilterComponent({ type: FilterComponentType.STARTS_WITH, disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();

        // Testing click behavior rather than .toBeDisabled() because this component
        // doesn't use a <button> element, so we verify the disabled functionality
        // by ensuring the onClick handler is not triggered when disabled
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).not.toHaveBeenCalled();
      });
    });

    describe('when type prop is SET_NAME_STARTS_WITH', () => {
      it('renders StartsWithFilter with title prop is "Set Name Starts With"', () => {
        renderYugiFilterComponent({
          type: FilterComponentType.SET_NAME_STARTS_WITH,
        });
        expect(screen.getByText('Set Name Starts With')).toBeInTheDocument();
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
      test('triggers onUpdate when the input value changes', () => {
        renderYugiFilterComponent({
          type: FilterComponentType.SET_NAME_STARTS_WITH,
        });
        fireEvent.change(screen.getByRole('textbox'), { target: { value: 's:' } });
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      test('triggers s onUpdate when clear button clicked', () => {
        renderYugiFilterComponent({
          type: FilterComponentType.SET_NAME_STARTS_WITH,
        });
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      it('uses disabled prop', () => {
        renderYugiFilterComponent({ type: FilterComponentType.SET_NAME_STARTS_WITH, disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();

        // Testing click behavior rather than .toBeDisabled() because this component
        // doesn't use a <button> element, so we verify the disabled functionality
        // by ensuring the onClick handler is not triggered when disabled
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).not.toHaveBeenCalled();
      });
    });
  });
});
