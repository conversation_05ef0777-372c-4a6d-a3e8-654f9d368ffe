import questionMark from '@iconify/icons-ic/baseline-question-mark';
import { Icon } from '@iconify/react';
import * as React from 'react';
import { CoreAssets } from '../../../helpers/core_assets';
import { TextFormat } from '../../../helpers/fmt';
import { YugiRarity } from '../../../models/yugioh/YugiRarity';
import { SetSymbolSize } from '../SetSymbol';

interface IProps {
  setName: string;
  setUUID?: string;
  hoverText: boolean;
  collectorNumber?: string;
  rarity?: YugiRarity;
  size?: SetSymbolSize;
}

export const YugiSetSymbol = (props: IProps) => {
  const [hover, setHover] = React.useState<boolean>(false);

  const size = props.size === undefined ? SetSymbolSize.XS : props.size;
  const rarityHoverMessage = props.rarity ? TextFormat.capitalize(props.rarity) : '';
  const collectorNumberText = props.collectorNumber ? ' - ' + props.collectorNumber : '';

  function onMouseEnter(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    setHover(true);
  }

  function onMouseLeave(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    setHover(false);
  }

  let imgElement = <Icon height={'18px'} width={'18px'} icon={questionMark} />;
  if (props.setUUID) {
    const imgSrc = `${CoreAssets.iconHost()}/set_symbols/yugioh/large/${props.setUUID[0]}/${props.setUUID[1]}/${
      props.setUUID
    }.png`;
    imgElement = <img src={imgSrc} />;
  }
  return (
    <div
      className={`flex set-symbol__${size}`}
      onMouseEnter={onMouseEnter.bind(this)}
      onMouseLeave={onMouseLeave.bind(this)}
    >
      {imgElement}
      {props.hoverText && hover ? (
        <div className="set-symbol-tooltip-container">
          <div className="set-symbol-tooltip">
            <div>{props.setName}</div>
            <div>{rarityHoverMessage + collectorNumberText}</div>
          </div>
        </div>
      ) : null}
    </div>
  );
};
