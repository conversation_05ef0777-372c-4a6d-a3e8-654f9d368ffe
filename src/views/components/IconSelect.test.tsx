import plusIcon from '@iconify/icons-ic/baseline-plus';
import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { IconSelect } from './IconSelect';

type IconSelectProps = Omit<React.ComponentProps<typeof IconSelect>, 'children'>;
const DEFAULT_PROPS: IconSelectProps = {
  icon: plusIcon,
  value: 'default',
  onChange: vi.fn(),
  className: '',
  disabled: false,
  error: false,
};
function renderIconSelect(props: Partial<IconSelectProps> = {}) {
  return render(
    <IconSelect {...DEFAULT_PROPS} {...props}>
      <option value="default">Default Option</option>
    </IconSelect>,
  );
}
describe('IconSelect', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  describe('component render', () => {
    it('renders input select', () => {
      renderIconSelect();
      const combobox = screen.getByRole('combobox');
      expect(combobox).toBeInTheDocument();
      expect(combobox).toHaveValue(DEFAULT_PROPS.value);
      expect(screen.getByText('Default Option')).toBeInTheDocument();
    });
    it('renders icon', () => {
      const { container } = renderIconSelect();
      const icon = container.querySelector('svg');
      expect(icon).toBeInTheDocument();
      expect(icon?.getAttribute('width')).toBe('18px');
      expect(icon?.getAttribute('height')).toBe('18px');
    });
    it('applies custom className', () => {
      const customClassName = 'custom-class';
      renderIconSelect({ className: customClassName });
      const combobox = screen.getByRole('combobox');
      expect(combobox).toBeInTheDocument();
      const divContainerEl = combobox.parentElement as HTMLElement;
      expect(divContainerEl).toHaveClass(customClassName);
    });
    it('calls onChange when selection changes', () => {
      renderIconSelect();
      const select = screen.getByRole('combobox');
      fireEvent.change(select, { target: { value: 'new-value' } });
      expect(DEFAULT_PROPS.onChange).toHaveBeenCalledOnce();
    });
    it('does not call onChange when selection changes and component is disabled', () => {
      renderIconSelect({ disabled: true });
      const select = screen.getByRole('combobox');
      expect(select).toBeDisabled();
    });
    it('applies error styles', () => {
      const { container } = renderIconSelect({ error: true });
      const icon = container.querySelector('svg');
      const combobox = screen.getByRole('combobox');
      expect(combobox).toHaveClass('select-error');
      expect(icon).toHaveStyle({ color: '#c11f1f' });
    });
  });
});
