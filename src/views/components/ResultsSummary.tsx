import * as React from 'react';

interface IProps {
  content: ResultsContent;
}

export class ResultsContent {
  title: string;
  subtitle?: string;

  constructor(title: string, subtitle?: string) {
    this.title = title;
    this.subtitle = subtitle;
  }
}

export function ResultsSummary(props: IProps) {
  return (
    <div className="col-sm-12 col-md-4 flex align-center" style={{ marginBottom: '1rem' }}>
      <div className="flex vertical">
        <span className="builder-count">{props.content.title}</span>
        <span className="builder-value">{props.content.subtitle || <>&nbsp;</>}</span>
      </div>
    </div>
  );
}
