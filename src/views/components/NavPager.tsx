import chevron_left from '@iconify/icons-ic/chevron-left';
import chevron_right from '@iconify/icons-ic/chevron-right';
import first_page from '@iconify/icons-ic/first-page';
import last_page from '@iconify/icons-ic/last-page';
import { Icon } from '@iconify/react';
import classNames from 'classnames';
import * as React from 'react';

export enum PageDirection {
  FIRST = 'first',
  BACKWARDS = 'backwards',
  FORWARDS = 'forwards',
  LAST = 'last',
}

interface IProps {
  loading: boolean;
  previousDisabled: boolean;
  nextDisabled: boolean;
  onChangePage: (direction: PageDirection) => void;
}

export function NavPager(props: IProps): JSX.Element {
  const previousClassNames = classNames('secondary-nav-bar-item flex', {
    'is-disabled': props.previousDisabled,
    'is-loading': props.loading,
  });
  const nextClassNames = classNames('secondary-nav-bar-item flex', {
    'is-disabled': props.nextDisabled,
    'is-loading': props.loading,
  });

  function onClickButton(evt: React.SyntheticEvent<HTMLElement>, direction: PageDirection, disabled: boolean) {
    evt.preventDefault();

    if (!disabled) {
      props.onChangePage(direction);
    }
  }

  return (
    <>
      <ul className="secondary-nav-bar-items nav-bar__horizontal is-input-session">
        <li
          className={previousClassNames}
          onClick={(evt: any) => onClickButton(evt, PageDirection.FIRST, props.previousDisabled || props.loading)}
        >
          <Icon height={'15px'} width={'15px'} icon={first_page} style={{ margin: '0.5rem 0.5rem 0 0' }} />
        </li>
        <li
          className={previousClassNames}
          onClick={(evt: any) => onClickButton(evt, PageDirection.BACKWARDS, props.previousDisabled || props.loading)}
        >
          <Icon height={'15px'} width={'15px'} icon={chevron_left} style={{ margin: '0.5rem 0.5rem 0 0' }} />
          <span>Previous</span>
        </li>
        <li
          className={nextClassNames}
          onClick={(evt: any) => onClickButton(evt, PageDirection.FORWARDS, props.nextDisabled || props.loading)}
        >
          <Icon height={'15px'} width={'15px'} icon={chevron_right} style={{ margin: '0.5rem 0.5rem 0 0' }} />
          <span>Next</span>
        </li>
        <li
          className={nextClassNames}
          onClick={(evt: any) => onClickButton(evt, PageDirection.LAST, props.nextDisabled || props.loading)}
        >
          <Icon height={'15px'} width={'15px'} icon={last_page} style={{ margin: '0.5rem 0.5rem 0 0' }} />
        </li>
      </ul>
    </>
  );
}
