import * as React from 'react';
import { TextFormat } from '../../../helpers/fmt';
import { Validator } from '../../../lib/deckbuilder/validators/DeckValidator';
import { Deck } from '../../../models/Decks';
import { Legality, type LegalityKey } from '../../../models/Legality';

interface IProps {
  deck: Deck;
  isBuilding: boolean;
}

interface IState {}

export class DeckLegalities extends React.Component<IProps, IState> {
  /**
   * @override
   */
  render() {
    const legalityErrors: { [_: string]: Array<string> } = {};
    Object.keys(Legality).forEach((key) => {
      legalityErrors[Legality[key as LegalityKey]] = Validator(Legality[key as LegalityKey]).check(
        this.props.deck,
        this.props.isBuilding,
      );
    });

    return (
      <div className="deck-legalities">
        {Object.keys(Legality).map((key, position) => {
          const legality = Legality[key as LegalityKey];
          const errors = legalityErrors[legality];
          return (
            <div className={'deck-legality' + (errors.length ? ' is-error' : '')} key={key + '-' + position}>
              {(errors.length ? 'Not legal in ' : 'Legal in ') + TextFormat.capitalize(legality)}
              {errors.length ? (
                <div className="deck-legality__errors">
                  {errors.map((error, position) => (
                    <div className="deck-legality__error" key={legality + '-' + position}>
                      {error}
                    </div>
                  ))}
                </div>
              ) : null}
            </div>
          );
        })}
      </div>
    );
  }
}
