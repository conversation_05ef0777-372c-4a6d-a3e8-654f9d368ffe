// Iconify
import style from '@iconify/icons-ic/style';
import * as React from 'react';
// Containers, actions, and dispatcher
import * as DeckActions from '../../../actions/DeckActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { TextFormat } from '../../../helpers/fmt';
// Models
import { Grouping, type GroupingKey } from '../../../models/Grouping';
import { IconSelect } from '../../components/IconSelect';

interface IProps {
  dispatcher: Dispatcher;
  deckGrouping: Grouping;
}

//export class DeckBuilderGrouping extends React.Component<IProps, IState> {
export const DeckBuilderGrouping = (props: IProps) => {
  return (
    <div className="deckbuilder-info-container">
      <div className="deckbuilder-info-heading">Group By</div>
      <IconSelect className="icon-container--deck" icon={style} value={props.deckGrouping} onChange={onChangeGrouping}>
        <>
          <option disabled>Group by</option>
          {Object.keys(Grouping).map((key) => (
            <option key={key} value={Grouping[key as GroupingKey]}>
              {TextFormat.capitalize(Grouping[key as GroupingKey])}
            </option>
          ))}
        </>
      </IconSelect>
    </div>
  );

  function onChangeGrouping(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    DeckActions.grouping(evt.currentTarget.value as any, props.dispatcher);
  }
};
