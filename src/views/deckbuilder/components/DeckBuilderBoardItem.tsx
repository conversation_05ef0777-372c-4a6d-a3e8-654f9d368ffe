import add from '@iconify/icons-ic/add';
import icon_delete from '@iconify/icons-ic/delete';
import link from '@iconify/icons-ic/link';
import remove from '@iconify/icons-ic/remove';
import wallpaper from '@iconify/icons-ic/wallpaper';
import zoom_in from '@iconify/icons-ic/zoom-in';
// Iconify
import { Icon } from '@iconify/react';
import * as Immutable from 'immutable';
import * as React from 'react';
// Containers, actions, and dispatcher
import * as DeckActions from '../../../actions/DeckActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { CoreAssets } from '../../../helpers/core_assets';
import track from '../../../helpers/ga_helper';
// Models
import { Card } from '../../../models/Cards';
import { Deck, DeckBoard, DeckCard } from '../../../models/Decks';
import { Game } from '../../../models/Game';
import { Dialog } from '../../components/Dialog';

interface IProps {
  dispatcher: Dispatcher;
  deck: Deck;
  deckBoard?: DeckBoard;
  deckCards: Immutable.List<DeckCard>;
  card: Card;
  cardCount?: number;
  showCount?: boolean;
  mutable?: boolean;
  onClick?: (evt: React.SyntheticEvent<HTMLElement>) => void;

  playtest?: boolean;
  onGotoTop?: (evt: React.SyntheticEvent<HTMLElement>) => void;
  onGotoBottom?: (evt: React.SyntheticEvent<HTMLElement>) => void;
  onGotoGraveyard?: (evt: React.SyntheticEvent<HTMLElement>) => void;

  linkable?: boolean;
  onClickLink?: (evt: React.SyntheticEvent<HTMLElement>) => void;

  expandable?: boolean;
}

interface IState {
  cardCount?: number;
  isOpen: boolean;
  isDeletingAll: boolean;
}

export class DeckBuilderBoardItem extends React.Component<IProps, IState> {
  private readonly imageRef: React.RefObject<HTMLImageElement>;
  private readonly imagePlaceholderRef: React.RefObject<HTMLImageElement>;

  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.imageRef = React.createRef<HTMLImageElement>();
    this.imagePlaceholderRef = React.createRef<HTMLImageElement>();
    this.state = {
      cardCount: props.cardCount,
      isOpen: false,
      isDeletingAll: false,
    };
  }

  /**
   * @override
   */
  componentDidMount() {
    if (this.imageRef.current) {
      this.imageRef.current.addEventListener('load', this.imageLoaded);
    }
  }

  /**
   * @override
   */
  componentWillReceiveProps(props: IProps) {
    this.setState({
      cardCount: props.cardCount,
    });
  }

  /**
   * @override
   */
  componentWillUnmount() {
    if (this.imageRef.current) {
      this.imageRef.current.removeEventListener('load', this.imageLoaded);
    }
  }

  /**
   * @override
   */
  render() {
    const numLinks = this.props.deckCards.filter((deckCard: DeckCard) => !!deckCard.get('cardInstanceId')).size;

    return (
      <div className="deckbuilder-board-item" onClick={this.onClick}>
        <Dialog
          isOpen={this.state.isDeletingAll || false}
          onDismiss={() => {
            this.setState({ isDeletingAll: false });
          }}
        >
          <h3 className="query-dialog-heading">Are you sure?</h3>
          <p className="query-dialog-subheading" style={{ whiteSpace: 'normal' }}>
            {'This card will be removed from your deck. This decision cannot be reversed!'}
          </p>
          <div className="flex justify-center">
            <input
              type="submit"
              className="button-alert"
              value="Remove"
              autoComplete="off"
              onClick={this.onConfirmDeleteAll.bind(this)}
            />
          </div>
        </Dialog>

        <div className="deckbuilder-board-item__image-wrapper">
          <img
            ref={this.imageRef}
            className="deckbuilder-board-item__image-wrapper__image"
            src={`${CoreAssets.imageHost()}/card_images/${this.props.card.get('jsonID')}.jpg`}
            alt={this.props.card.get('name')}
          />
          <img
            ref={this.imagePlaceholderRef}
            className="deckbuilder-board-item__image-wrapper__image"
            src={CoreAssets.cardBack(Game.MTG)}
            alt={this.props.card.get('name')}
            style={{ display: 'none' }}
          />
          {this.props.mutable ? (
            /* for mutating the deck cards */
            <div key={'keep'} className="deckbuilder-board-item__image-wrapper__hover">
              <div className="deckbuilder-board-item__image-wrapper__hover__button" onClick={this.onClickIncrement}>
                <Icon height={'24px'} width={'24px'} icon={add} />
                <div className="deckbuilder-board-item__image-wrapper__hover__button__label">
                  <span>Add one</span>
                </div>
              </div>
              <div className="deckbuilder-board-item__image-wrapper__hover__button" onClick={this.onClickDecrement}>
                <Icon height={'24px'} width={'24px'} icon={remove} />
                <div className="deckbuilder-board-item__image-wrapper__hover__button__label">
                  <span>Remove one</span>
                </div>
              </div>
              <div className="deckbuilder-board-item__image-wrapper__hover__button" onClick={this.onClickDeleteAll}>
                <Icon height={'24px'} width={'24px'} icon={icon_delete} />
                <div className="deckbuilder-board-item__image-wrapper__hover__button__label">
                  <span>Remove all</span>
                </div>
              </div>
            </div>
          ) : null}
          {this.props.playtest ? (
            /* for playing the deck cards */
            <div key={'keep'} className="deckbuilder-board-item__image-wrapper__hover">
              <div className="deckbuilder-board-item__image-wrapper__hover__button" onClick={this.onClickFullscreen}>
                <Icon height={'24px'} width={'24px'} icon={zoom_in} />
                <div className="deckbuilder-board-item__image-wrapper__hover__button__label">
                  <span>View</span>
                </div>
              </div>
              <div
                className="deckbuilder-board-item__image-wrapper__hover__button"
                style={{ padding: '0.5rem', boxSizing: 'border-box' }}
                onClick={this.props.onGotoTop}
              >
                <img width="15" height="15" src="/images/top.png" />
                <div className="deckbuilder-board-item__image-wrapper__hover__button__label">
                  <span>Return to Top</span>
                </div>
              </div>
              <div
                className="deckbuilder-board-item__image-wrapper__hover__button"
                style={{ padding: '0.5rem', boxSizing: 'border-box' }}
                onClick={this.props.onGotoBottom}
              >
                <img width="15" height="15" src="/images/bottom.png" />
                <div className="deckbuilder-board-item__image-wrapper__hover__button__label">
                  <span>Return to Bottom</span>
                </div>
              </div>
              <div
                className="deckbuilder-board-item__image-wrapper__hover__button"
                style={{ padding: '0.5rem', boxSizing: 'border-box' }}
                onClick={this.props.onGotoGraveyard}
              >
                <img width="15" height="15" src="/images/graveyard.png" />
                <div className="deckbuilder-board-item__image-wrapper__hover__button__label">
                  <span>Graveyard</span>
                </div>
              </div>
            </div>
          ) : null}

          {this.props.linkable ? (
            /* for linking the deck cards */
            <div key={'keep'} className="deckbuilder-board-item__image-wrapper__hover">
              <div className="deckbuilder-board-item__image-wrapper__hover__button" onClick={this.props.onClickLink}>
                <Icon height={'24px'} width={'24px'} icon={link} />
                <div className="deckbuilder-board-item__image-wrapper__hover__button__label">
                  <span style={{ textAlign: 'center' }}>
                    {`${numLinks}/${this.props.cardCount || 1}`}
                    <br />
                    Links
                  </span>
                </div>
              </div>
              <div
                className="deckbuilder-board-item__image-wrapper__hover__button"
                onClick={(evt: React.SyntheticEvent<HTMLElement>) => {
                  evt.preventDefault();
                  evt.stopPropagation();
                  DeckActions.updateDeck(
                    this.props.deck.set(
                      'image',
                      this.props.deck.get('cards').get(this.props.deckCards.get(0).get('cardId')).get('jsonID'),
                    ),
                    this.props.dispatcher,
                  );
                }}
              >
                <Icon height={'24px'} width={'24px'} icon={wallpaper} />
                <div
                  className="deckbuilder-board-item__image-wrapper__hover__button__label"
                  style={{ padding: '0 0.5rem', boxSizing: 'border-box' }}
                >
                  <span>Set as Cover Art</span>
                </div>
              </div>
            </div>
          ) : null}

          {this.props.expandable ? (
            /* for deck cards with only expanding */
            <div key={'keep'} className="deckbuilder-board-item__image-wrapper__hover">
              <div className="deckbuilder-board-item__image-wrapper__hover__button" onClick={this.onClickFullscreen}>
                <Icon height={'24px'} width={'24px'} icon={zoom_in} />
                <div className="deckbuilder-board-item__image-wrapper__hover__button__label">
                  <span>View</span>
                </div>
              </div>
            </div>
          ) : null}
        </div>
        {this.props.showCount && this.state.cardCount !== undefined && this.state.cardCount !== null ? (
          <div className="deckbuilder-board-item__label-wrapper">
            <span className="deckbuilder-board-item__label-wrapper__card-count">{this.state.cardCount}</span>
          </div>
        ) : null}

        {this.renderOpen()}
      </div>
    );
  }

  private renderOpen = () => {
    if (!this.state.isOpen) {
      return null;
    }
    return (
      <div className="deckbuilder-board-item__overlay" onClick={this.onClick}>
        <div
          className="deckbuilder-board-item__image-wrapper"
          style={{
            width: '100%',
            height: 'auto !important',
            minHeight: 'initial !important',
            maxHeight: 'auto !important',
          }}
        >
          <img
            ref="imageHQ"
            className="deckbuilder-board-item__image-wrapper__image"
            src={`${CoreAssets.imageHost()}/card_images_hq/${this.props.card.get('jsonID')}.jpg`}
            alt={this.props.card.get('name')}
            style={{
              width: '100%',
              height: 'auto !important',
              minHeight: 'initial !important',
              maxHeight: 'auto !important',
            }}
          />
          <img
            ref="imagePlaceholderHQ"
            className="deckbuilder-board-item__image-wrapper__image"
            src={CoreAssets.cardBack(Game.MTG)}
            alt={this.props.card.get('name')}
            style={{
              display: 'none',
              width: '100%',
              height: 'auto !important',
              minHeight: 'initial !important',
              maxHeight: 'auto !important',
            }}
          />
        </div>
      </div>
    );
  };

  private onClick = (evt: React.SyntheticEvent<HTMLElement>) => {
    // if the parent wants to handle the click and we don't have better ideas
    if (this.props.onClick && !this.state.isOpen) {
      // then delegate handling the click to the parent
      return this.props.onClick(evt);
    }
    // otherwise we handle the click ourselves
    this.onClickFullscreen(evt);
  };

  private onClickFullscreen = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    evt.stopPropagation();
    track('decks', 'card', 'view');
    this.setState({
      isOpen: !this.state.isOpen,
    });
  };

  private onClickIncrement = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    evt.stopPropagation();
    track('decks', 'build', 'increment');
    if (this.props.deckBoard) {
      DeckActions.addDeckCard(this.props.deck, this.props.deckBoard, this.props.card, 1, this.props.dispatcher);
    }
  };

  private onClickDecrement = (evt: React.SyntheticEvent<HTMLElement>) => {
    if (this.props.cardCount === undefined || this.props.cardCount === null) {
      return;
    }
    if (this.props.cardCount === 1) {
      return this.onClickDeleteAll(evt);
    }
    evt.preventDefault();
    evt.stopPropagation();
    track('decks', 'build', 'decrement');
    this.setState(
      {
        cardCount: this.state.cardCount! - 1,
      },
      () => DeckActions.removeDeckCard(this.props.deck, this.props.deckCards.last(), this.props.dispatcher),
    );
  };

  private onClickDeleteAll = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    evt.stopPropagation();
    track('decks', 'build', 'remove_all');
    this.setState({
      isDeletingAll: true,
    });
  };

  private onConfirmDeleteAll = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    evt.stopPropagation();
    this.setState({
      isDeletingAll: false,
    });
    DeckActions.removeDeckCards(this.props.deck, this.props.deckCards, this.props.dispatcher);
  };

  private imageLoaded = () => {
    if (this.imageRef.current && this.imagePlaceholderRef.current) {
      this.imageRef.current.style.display = 'initial';
      this.imagePlaceholderRef.current.style.display = 'none';
    }
  };
}
