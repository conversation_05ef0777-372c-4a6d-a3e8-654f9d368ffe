import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { DeckStatisticsRatios } from './DeckStatisticsRatios';

// Mock Plotly
const mockNewPlot = vi.hoisted(() => vi.fn());
const mockPurge = vi.hoisted(() => vi.fn());

vi.mock('../../../helpers/plotly', () => ({
  default: () => ({
    newPlot: mockNewPlot,
    purge: mockPurge,
  }),
}));

// Mock serverside helper
vi.mock('../../../helpers/serverside', () => ({
  default: vi.fn(() => false),
}));

type DeckStatisticsRatiosProps = React.ComponentProps<typeof DeckStatisticsRatios>;

const defaultProps: Omit<DeckStatisticsRatiosProps, 'dispatcher'> = {
  title: 'Card Types',
  ratios: Immutable.Map({
    creature: 0.4,
    instant: 0.3,
    sorcery: 0.2,
    land: 0.1,
  }),
  mini: false,
};

const renderDeckStatisticsRatios = (props: Partial<DeckStatisticsRatiosProps> = {}) => {
  return renderWithDispatcher(DeckStatisticsRatios, { ...defaultProps, ...props });
};

describe('DeckStatisticsRatios', () => {
  describe('when component renders', () => {
    it('applies correct styling for normal size', () => {
      const { container } = renderDeckStatisticsRatios({ mini: false });
      const chartContainer = container.firstChild as HTMLElement;

      expect(chartContainer).toHaveStyle({
        position: 'relative',
        width: '100%',
        minWidth: '0',
        maxWidth: '100%',
        height: '400px',
        maxHeight: '400px',
      });
    });

    it('applies correct styling for mini size', () => {
      const { container } = renderDeckStatisticsRatios({ mini: true });
      const chartContainer = container.firstChild as HTMLElement;

      expect(chartContainer).toHaveStyle({
        position: 'relative',
        width: '100%',
        minWidth: '300px',
        maxWidth: '300px',
        height: '300px',
        maxHeight: '300px',
      });
    });

    it('creates chart element with correct id', () => {
      const { container } = renderDeckStatisticsRatios({ title: 'Custom Title' });
      expect(container.querySelector('div[id="Custom Title"]')).toBeInTheDocument();
    });
  });

  describe('chart rendering', () => {
    it('calls Plotly.newPlot on mount', () => {
      renderDeckStatisticsRatios();

      expect(mockNewPlot).toHaveBeenCalledWith(
        expect.any(HTMLElement),
        expect.any(Array),
        expect.any(Object),
        expect.any(Object),
      );
    });

    it('calls Plotly.newPlot on update', () => {
      const { rerenderWithDispatcher } = renderDeckStatisticsRatios();

      mockNewPlot.mockClear();

      rerenderWithDispatcher({
        ratios: Immutable.Map({
          artifact: 0.5,
          enchantment: 0.5,
        }),
      });

      expect(mockNewPlot).toHaveBeenCalled();
    });

    it('passes correct data structure to Plotly', () => {
      renderDeckStatisticsRatios();

      const [, data] = mockNewPlot.mock.calls[0];
      expect(data).toHaveLength(1);
      expect(data[0]).toMatchObject({
        type: 'pie',
        name: 'Card Types',
        hoverinfo: 'label+percent',
        textinfo: 'none',
        hole: 0.6,
      });
    });

    it('capitalizes labels correctly', () => {
      renderDeckStatisticsRatios({
        ratios: Immutable.Map({
          creature: 0.4,
          instant: 0.3,
          'multi word': 0.3,
        }),
      });

      const [, data] = mockNewPlot.mock.calls[0];
      expect(data[0].labels).toContain('Creature');
      expect(data[0].labels).toContain('Instant');
      expect(data[0].labels).toContain('Multi Word');
    });

    it('passes correct values to Plotly', () => {
      renderDeckStatisticsRatios();

      const [, data] = mockNewPlot.mock.calls[0];
      expect(data[0].values).toEqual([0.4, 0.3, 0.2, 0.1]);
    });

    it('applies correct layout configuration', () => {
      renderDeckStatisticsRatios({ title: 'Test Chart' });

      const [, , layout] = mockNewPlot.mock.calls[0];
      expect(layout).toMatchObject({
        title: 'Test Chart',
        showlegend: false,
        font: {
          family: 'lato-bold',
          size: 18,
          color: '#3e3e3e',
        },
      });
    });
  });

  describe('color mapping', () => {
    it('applies correct colors for card types', () => {
      renderDeckStatisticsRatios({
        ratios: Immutable.Map({
          land: 0.2,
          instant: 0.2,
          artifact: 0.2,
          enchantment: 0.2,
          creature: 0.2,
          planeswalker: 0.2,
        }),
      });

      const [, data] = mockNewPlot.mock.calls[0];
      const colors = data[0].marker.colors;

      expect(colors).toContain('#614A3A'); // Land
      expect(colors).toContain('#D61616'); // Instant
      expect(colors).toContain('#686868'); // Artifact
      expect(colors).toContain('#E8E8E8'); // Enchantment
      expect(colors).toContain('#1AC655'); // Creature
    });

    it('applies correct colors for mana colors', () => {
      renderDeckStatisticsRatios({
        ratios: Immutable.Map({
          blue: 0.2,
          red: 0.2,
          green: 0.2,
          white: 0.2,
          black: 0.2,
        }),
      });

      const [, data] = mockNewPlot.mock.calls[0];
      const colors = data[0].marker.colors;

      expect(colors).toContain('#45C3FF'); // Blue
      expect(colors).toContain('#FF5C35'); // Red
      expect(colors).toContain('#1AC655'); // Green
      expect(colors).toContain('#FFE48B'); // White
      expect(colors).toContain('#676767'); // Black
    });

    it('handles colorless and disabled colors', () => {
      renderDeckStatisticsRatios({
        ratios: Immutable.Map({
          colorless: 0.5,
          disabled: 0.5,
        }),
      });

      const [, data] = mockNewPlot.mock.calls[0];
      const colors = data[0].marker.colors;

      expect(colors).toContain('#C5C5C5'); // Colorless/Disabled
    });
  });

  describe('window resize handling', () => {
    it('adds resize event listener on mount', () => {
      const addEventListenerSpy = vi.spyOn(window, 'addEventListener');

      renderDeckStatisticsRatios();

      expect(addEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function));
    });

    it('removes resize event listener on unmount', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');

      const { unmount } = renderDeckStatisticsRatios();
      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function));
    });

    it('purges and re-renders chart on resize', () => {
      renderDeckStatisticsRatios({ title: 'Resize Test' });

      // Simulate window resize
      const resizeEvent = new Event('resize');
      window.dispatchEvent(resizeEvent);

      expect(mockPurge).toHaveBeenCalledWith('Resize Test');
      expect(mockNewPlot).toHaveBeenCalledTimes(2); // Once on mount, once on resize
    });
  });

  describe('component lifecycle', () => {
    it('cleans up properly on unmount', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');

      const { unmount } = renderDeckStatisticsRatios();
      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalled();
    });
  });

  describe('serverside rendering', () => {
    it('skips chart rendering on server side', async () => {
      // Mock serverside to return true
      const serversideMock = await import('../../../helpers/serverside');
      vi.mocked(serversideMock.default).mockReturnValue(true);

      renderDeckStatisticsRatios();

      expect(mockNewPlot).not.toHaveBeenCalled();

      // Reset mock
      vi.mocked(serversideMock.default).mockReturnValue(false);
    });
  });

  describe('ref handling', () => {
    it('creates ref for title element', () => {
      const { container } = renderDeckStatisticsRatios();
      const titleElement = container.querySelector('div[id="Card Types"]');

      expect(titleElement).toBeInTheDocument();
    });

    it('only renders chart when ref is available', () => {
      // This is tested indirectly by ensuring newPlot is called with a valid element
      renderDeckStatisticsRatios();

      const [element] = mockNewPlot.mock.calls[0];
      expect(element).toBeInstanceOf(HTMLElement);
    });
  });
});
