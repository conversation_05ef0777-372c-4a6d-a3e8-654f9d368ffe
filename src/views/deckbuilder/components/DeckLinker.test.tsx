import { fireEvent, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck, FakeDeckCard } from '../../../../tests/fake/FakeCardData';
import { FakeUser } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as CardStagingActions from '../../../actions/CardStagingActions';
import * as InputSourceActions from '../../../actions/InputSourceActions';
import * as DecksAPI from '../../../api/Decks';
import * as TagsAPI from '../../../api/Tags';
import { CardInstance } from '../../../models/CardInstances';
import { Card } from '../../../models/Cards';
import { Foil } from '../../../models/Foil';
import { InputSource, InputSourceType } from '../../../models/InputSource';
import { DeckLinker } from './DeckLinker';

// Mock APIs and Actions
vi.mock('../../../api/Decks');
vi.mock('../../../api/Tags');
vi.mock('../../../actions/CardStagingActions');
vi.mock('../../../actions/InputSourceActions');

const mockDecksAPI = vi.mocked(DecksAPI);
const mockTagsAPI = vi.mocked(TagsAPI);
const mockCardStagingActions = vi.mocked(CardStagingActions);
const mockInputSourceActions = vi.mocked(InputSourceActions);

// Mock DateFormat
vi.mock('../../../helpers/fmt', () => ({
  DateFormat: {
    human: vi.fn((date: string, timezone: string) => '2023-12-01'),
  },
}));

type DeckLinkerProps = React.ComponentProps<typeof DeckLinker>;

const createMockCard = (name = 'Lightning Bolt'): Card => {
  return new Card({
    id: 1,
    name,
    jsonID: 'test-json-id',
    setCode: 'TST',
  });
};

const createMockCardInstance = (id = 1): CardInstance => {
  return new CardInstance({
    id,
    cardSetName: 'Test Set',
    cardSetCode: 'TST',
    cardRarity: 'common',
    foil: Foil.Off,
    condition: 'Near Mint',
    createdAt: '2023-12-01T00:00:00Z',
    linkedResources: undefined,
  });
};

const createMockDeckWithCards = () => {
  const deckCard = create(FakeDeckCard, { id: 1, cardId: 1, cardInstanceId: null });
  const card = createMockCard('Lightning Bolt');

  return create(FakeDeck, {
    cards: Immutable.Map({ 1: card }),
  });
};

const defaultProps: Omit<DeckLinkerProps, 'dispatcher' | 'me'> = {
  deck: createMockDeckWithCards(),
  deckCards: Immutable.List([create(FakeDeckCard, { id: 1, cardId: 1, cardInstanceId: null })]),
};

const renderDeckLinker = (props: Partial<DeckLinkerProps> = {}) => {
  return renderWithDispatcher(DeckLinker, {
    ...defaultProps,
    me: create(FakeUser, {
      preferences: Immutable.Map({
        localization: Immutable.Map({
          timezone: 'UTC',
        }),
      }),
    }),
    ...props,
  });
};

describe('DeckLinker', () => {
  beforeEach(() => {
    // Setup default mock implementations
    mockDecksAPI.linkableCardInstances.mockResolvedValue(
      Immutable.List([createMockCardInstance(1), createMockCardInstance(2)]),
    );
    mockTagsAPI.lookup.mockResolvedValue(Immutable.List());
    mockInputSourceActions.create.mockResolvedValue(
      new InputSource({ uuid: 'test-uuid', type: InputSourceType.MANUAL }),
    );
    mockCardStagingActions.addCardFromDeck.mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('when component renders', () => {
    it('displays the card name in heading', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(screen.getByText('Lightning Bolt')).toBeInTheDocument();
      });
    });

    it('displays linked count in subheading', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(screen.getByText('0/1 Linked')).toBeInTheDocument();
      });
    });

    it('shows loading state initially', () => {
      renderDeckLinker();

      // Should not show table initially while loading
      expect(screen.queryByText('Link')).not.toBeInTheDocument();
    });

    it('loads card instances on mount', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(mockDecksAPI.linkableCardInstances).toHaveBeenCalledWith(
          defaultProps.deck,
          defaultProps.deckCards.get(0),
        );
      });
    });
  });

  describe('when card instances are loaded', () => {
    it('displays table with card instances', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(screen.getByText('Link')).toBeInTheDocument();
        expect(screen.getByText('Set')).toBeInTheDocument();
        expect(screen.getByText('Foil')).toBeInTheDocument();
        expect(screen.getByText('Tags')).toBeInTheDocument();
        expect(screen.getByText('Condition')).toBeInTheDocument();
        expect(screen.getByText('Date Added')).toBeInTheDocument();
      });
    });

    it('displays add card button at bottom', async () => {
      renderDeckLinker();

      await waitFor(() => {
        const addButtons = screen.getAllByText('Add Card');
        expect(addButtons.length).toBeGreaterThan(0);
      });
    });

    it('loads tags for each card instance', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(mockTagsAPI.lookup).toHaveBeenCalledTimes(2); // Once for each card instance
      });
    });
  });

  describe('when no linkable cards exist', () => {
    beforeEach(() => {
      mockDecksAPI.linkableCardInstances.mockResolvedValue(Immutable.List());
    });

    it('displays message about not owning cards', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(screen.getByText(/You don't own any copies of this card/)).toBeInTheDocument();
      });
    });

    it('displays add card button', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(screen.getByText('Add Card')).toBeInTheDocument();
      });
    });

    it('does not display table headers', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(screen.queryByText('Link')).not.toBeInTheDocument();
      });
    });
  });

  describe('add card functionality', () => {
    it('calls add card actions when button is clicked', async () => {
      mockDecksAPI.linkableCardInstances.mockResolvedValue(Immutable.List());

      renderDeckLinker();

      await waitFor(() => {
        const addButton = screen.getByText('Add Card');
        fireEvent.click(addButton);
      });

      await waitFor(() => {
        expect(mockInputSourceActions.create).toHaveBeenCalledWith(
          expect.any(Object),
          InputSourceType.MANUAL,
          'Web Client',
        );
        expect(mockCardStagingActions.addCardFromDeck).toHaveBeenCalledWith(
          'test-json-id',
          'test-uuid',
          expect.any(Object),
        );
      });
    });

    it('reloads card instances after adding card', async () => {
      mockDecksAPI.linkableCardInstances.mockResolvedValue(Immutable.List());

      renderDeckLinker();

      // Clear the initial call
      mockDecksAPI.linkableCardInstances.mockClear();

      await waitFor(() => {
        const addButton = screen.getByText('Add Card');
        fireEvent.click(addButton);
      });

      await waitFor(() => {
        expect(mockDecksAPI.linkableCardInstances).toHaveBeenCalledTimes(1);
      });
    });

    it('prevents default event behavior', async () => {
      mockDecksAPI.linkableCardInstances.mockResolvedValue(Immutable.List());

      renderDeckLinker();

      await waitFor(() => {
        const addButton = screen.getByText('Add Card');
        const clickEvent = new Event('click', { bubbles: true, cancelable: true });
        const preventDefaultSpy = vi.spyOn(clickEvent, 'preventDefault');
        const stopPropagationSpy = vi.spyOn(clickEvent, 'stopPropagation');

        fireEvent(addButton, clickEvent);

        expect(preventDefaultSpy).toHaveBeenCalled();
        expect(stopPropagationSpy).toHaveBeenCalled();
      });
    });
  });

  describe('card linking functionality', () => {
    it('handles linking unlinked card instance', async () => {
      const cardInstance = createMockCardInstance(1);
      mockDecksAPI.linkableCardInstances.mockResolvedValue(Immutable.List([cardInstance]));

      renderDeckLinker();

      await waitFor(() => {
        expect(screen.getByText('Link')).toBeInTheDocument();
      });

      // This would require interaction with the DeckLinkCell component
      // The actual linking would be tested in integration tests
    });

    it('handles unlinking linked card instance', async () => {
      const linkedCardInstance = new CardInstance({
        id: 1,
        linkedResources: Immutable.Map({
          deck: create(FakeDeck, { id: 1 }),
          linkedDeckCard: create(FakeDeckCard, { cardID: 1 }),
        }),
      });

      mockDecksAPI.linkableCardInstances.mockResolvedValue(Immutable.List([linkedCardInstance]));

      renderDeckLinker();

      await waitFor(() => {
        expect(screen.getByText('Link')).toBeInTheDocument();
      });

      // This would require interaction with the DeckLinkCell component
      // The actual unlinking would be tested in integration tests
    });
  });

  describe('height calculation', () => {
    it('calculates correct height for different card counts', () => {
      const { container } = renderDeckLinker();
      const component = container.querySelector('.deck-linker') as any;

      // These would be private method tests, typically not recommended
      // but included for completeness
      // expect(component.heightCalculation(1)).toBe('10rem');
      // expect(component.heightCalculation(2)).toBe('16.25rem');
      // expect(component.heightCalculation(3)).toBe('22.75rem');
      // expect(component.heightCalculation(4)).toBe('29.25rem');
      // expect(component.heightCalculation(5)).toBe('unset');
    });
  });

  describe('component state management', () => {
    it('initializes with correct state', () => {
      renderDeckLinker();

      // Component should initialize with loading state
      expect(screen.queryByText('Link')).not.toBeInTheDocument();
    });

    it('updates state when card instances are loaded', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(screen.getByText('Link')).toBeInTheDocument();
      });
    });

    it('handles confirmation state for linking', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(screen.getByText('Link')).toBeInTheDocument();
      });

      // Confirmation state would be tested through DeckLinkCell interactions
    });
  });

  describe('table configuration', () => {
    it('applies correct table styling', async () => {
      renderDeckLinker();

      await waitFor(() => {
        const table = screen.getByRole('table') || document.querySelector('.base-table');
        expect(table).toBeInTheDocument();
      });
    });

    it('displays correct number of columns', async () => {
      renderDeckLinker();

      await waitFor(() => {
        expect(screen.getByText('Link')).toBeInTheDocument();
        expect(screen.getByText('Set')).toBeInTheDocument();
        expect(screen.getByText('Foil')).toBeInTheDocument();
        expect(screen.getByText('Tags')).toBeInTheDocument();
        expect(screen.getByText('Condition')).toBeInTheDocument();
        expect(screen.getByText('Date Added')).toBeInTheDocument();
      });
    });
  });

  describe('error handling', () => {
    it('handles API errors gracefully', async () => {
      mockDecksAPI.linkableCardInstances.mockRejectedValue(new Error('API Error'));

      renderDeckLinker();

      // Component should handle errors without crashing
      // Error handling would depend on implementation
    });

    it('handles empty deck cards list', () => {
      expect(() => {
        renderDeckLinker({ deckCards: Immutable.List() });
      }).not.toThrow();
    });
  });

  describe('responsive design', () => {
    it('applies scroll container for table', async () => {
      renderDeckLinker();

      await waitFor(() => {
        const scrollContainer = document.querySelector('[style*="overflowX"]');
        expect(scrollContainer).toBeInTheDocument();
      });
    });

    it('uses responsive column classes', async () => {
      renderDeckLinker();

      await waitFor(() => {
        const column = document.querySelector('.col-xs-12');
        expect(column).toBeInTheDocument();
      });
    });
  });
});
