import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import importPlotly from '../../../helpers/plotly';
import isServerside from '../../../helpers/serverside';

// Plotly is imported like this because the TypeScript bindings for Plotly are terrible.
const Plotly = importPlotly();

interface IProps {
  dispatcher: Dispatcher;
  title: string;
  ratios: Immutable.Map<string, number>;
  mini?: boolean;
}

interface IState {}

export class DeckStatisticsRatios extends React.Component<IProps, IState> {
  private handleWindowResize: EventListener | null;
  private readonly titleRef: React.RefObject<HTMLDivElement>;

  constructor(props: IProps) {
    super(props);
    this.titleRef = React.createRef<HTMLDivElement>();
    this.state = {};
  }

  public componentDidMount() {
    this.renderChart();
    this.handleWindowResize = () => {
      if (this.titleRef.current && this.titleRef.current.id !== '') {
        Plotly.purge(this.titleRef.current.id);
      }
      this.renderChart();
    };
    window.addEventListener('resize', this.handleWindowResize);
  }

  public componentWillUnmount() {
    if (this.handleWindowResize != null) window.removeEventListener('resize', this.handleWindowResize);
    this.handleWindowResize = null;
  }

  public componentDidUpdate() {
    this.renderChart();
  }

  public render() {
    return (
      <div
        style={{
          position: 'relative',
          width: '100%',
          minWidth: this.props.mini ? '300px' : '0',
          maxWidth: this.props.mini ? '300px' : '100%',
          height: this.props.mini ? '300px' : '400px',
          maxHeight: this.props.mini ? '300px' : '400px',
        }}
      >
        <div
          style={{ width: '100%', maxWidth: '100%', height: '100%', maxHeight: '100%' }}
          id={this.props.title}
          ref={this.titleRef}
        />
      </div>
    );
  }

  private renderChart() {
    if (isServerside()) {
      return;
    }

    const keys = this.props.ratios
      .keySeq()
      .toJS()
      .map((key: string) => key.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()));
    const values = this.props.ratios.valueSeq().toJS();

    const data = [
      {
        labels: keys,
        values: values,
        type: 'pie',
        name: this.props.title,
        marker: {
          colors: keys.map((key: string) => {
            switch (key) {
              case 'Land':
                return '#614A3A';
              case 'Instant':
                return '#D61616';
              case 'Artifact':
                return '#686868';
              case 'Enchantment':
                return '#E8E8E8';
              case 'Planeswalker':
                return '#000000';
              case 'Creature':
                return '#1AC655';
              case 'Sorcery':
                return '#0A73EE';

              case 'Blue':
                return '#45C3FF';
              case 'Red':
                return '#FF5C35';
              case 'Green':
                return '#1AC655';
              case 'White':
                return '#FFE48B';
              case 'Black':
                return '#676767';
              case 'Colorless':
                return '#C5C5C5';
              case 'Disabled':
                return '#C5C5C5';
            }
          }),
        },
        domain: {
          x: [0, 1],
          y: [0, 1],
        },
        hoverinfo: 'label+percent',
        textinfo: 'none',
        hole: 0.6,
      },
    ];

    const layout = {
      title: this.props.title,
      font: {
        family: 'lato-bold',
        src: {
          url: '/fonts/lato-bold-webfont.svg#latobold',
          format: 'svg',
        },
        size: 18,
        color: '#3e3e3e',
      },
      showlegend: false,
    };

    const config: any = {
      staticPlot: false,
      editable: false,
      scrollZoom: false,
      showTips: false,
      showLink: false,
      sendData: false,
      showSources: false,
      displayModeBar: false,
      modeBarButtons: false,
      logging: false,
    };

    if (this.titleRef.current) {
      Plotly.newPlot(this.titleRef.current, data, layout, config);
    }
  }
}
