import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { ColorFilter, ColorOption } from '../../../models/filters/ColorFilter';
import { FilterState } from '../../../models/filters/FilterState';
import { DeckColorFiltering } from './DeckColorFiltering';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockColor = vi.mocked(DeckActions.color);

// Mock TextFormat
vi.mock('../../../helpers/fmt', () => ({
  TextFormat: {
    capitalizeWord: vi.fn((str: string) => str.charAt(0).toUpperCase() + str.slice(1)),
  },
}));

// Mock nextFilterState
vi.mock('../../../models/filters/FilterState', async () => {
  const actual = await vi.importActual('../../../models/filters/FilterState');
  return {
    ...actual,
    nextFilterState: vi.fn((state: FilterState) => {
      switch (state) {
        case FilterState.INACTIVE:
          return FilterState.INCLUDE;
        case FilterState.INCLUDE:
          return FilterState.EXCLUDE;
        case FilterState.EXCLUDE:
          return FilterState.INACTIVE;
        default:
          return FilterState.INACTIVE;
      }
    }),
  };
});

import { nextFilterState } from '../../../models/filters/FilterState';
const mockNextFilterState = vi.mocked(nextFilterState);

type DeckColorFilteringProps = React.ComponentProps<typeof DeckColorFiltering>;

const createMockColorFilter = (overrides: Partial<Record<ColorOption, FilterState>> = {}) => {
  return new ColorFilter({
    white: FilterState.INACTIVE,
    blue: FilterState.INACTIVE,
    black: FilterState.INACTIVE,
    red: FilterState.INACTIVE,
    green: FilterState.INACTIVE,
    colorless: FilterState.INACTIVE,
    ...overrides,
  });
};

const defaultProps: Omit<DeckColorFilteringProps, 'dispatcher'> = {
  color: createMockColorFilter(),
};

const renderDeckColorFiltering = (props: Partial<DeckColorFilteringProps> = {}) => {
  return renderWithDispatcher(DeckColorFiltering, { ...defaultProps, ...props });
};

describe('DeckColorFiltering', () => {
  beforeEach(() => {
    mockColor.mockClear();
    mockNextFilterState.mockClear();
  });

  describe('when component renders', () => {
    it('displays the color filtering container', () => {
      const { container } = renderDeckColorFiltering();
      expect(container.querySelector('.deck-color-filtering')).toBeInTheDocument();
    });

    it('displays color symbol container', () => {
      const { container } = renderDeckColorFiltering();
      expect(container.querySelector('.color-symbol-container')).toBeInTheDocument();
    });

    it('renders color filter icons for all colors', () => {
      const { container } = renderDeckColorFiltering();
      
      // Should render icons for white, blue, black, red, green (not colorless in onlyColors)
      const colorIcons = container.querySelectorAll('.gate-filter-icon');
      expect(colorIcons.length).toBe(5); // white, blue, black, red, green
    });

    it('displays correct mana symbol paths', () => {
      const { container } = renderDeckColorFiltering();
      
      expect(container.querySelector('img[src="/svg/mana-white.svg"]')).toBeInTheDocument();
      expect(container.querySelector('img[src="/svg/mana-blue.svg"]')).toBeInTheDocument();
      expect(container.querySelector('img[src="/svg/mana-black.svg"]')).toBeInTheDocument();
      expect(container.querySelector('img[src="/svg/mana-red.svg"]')).toBeInTheDocument();
      expect(container.querySelector('img[src="/svg/mana-green.svg"]')).toBeInTheDocument();
    });
  });

  describe('color filter states', () => {
    it('displays inactive state correctly', () => {
      const colorFilter = createMockColorFilter({
        white: FilterState.INACTIVE,
      });
      
      renderDeckColorFiltering({ color: colorFilter });
      
      // The GateFilterIcon should receive the correct filter state
      const { container } = renderDeckColorFiltering({ color: colorFilter });
      expect(container.querySelector('.deck-color-filtering')).toBeInTheDocument();
    });

    it('displays include state correctly', () => {
      const colorFilter = createMockColorFilter({
        blue: FilterState.INCLUDE,
      });
      
      renderDeckColorFiltering({ color: colorFilter });
      
      // The GateFilterIcon should receive the correct filter state
      const { container } = renderDeckColorFiltering({ color: colorFilter });
      expect(container.querySelector('.deck-color-filtering')).toBeInTheDocument();
    });

    it('displays exclude state correctly', () => {
      const colorFilter = createMockColorFilter({
        red: FilterState.EXCLUDE,
      });
      
      renderDeckColorFiltering({ color: colorFilter });
      
      // The GateFilterIcon should receive the correct filter state
      const { container } = renderDeckColorFiltering({ color: colorFilter });
      expect(container.querySelector('.deck-color-filtering')).toBeInTheDocument();
    });
  });

  describe('color filter interactions', () => {
    it('calls color action when color is clicked', () => {
      const { dispatcher } = renderDeckColorFiltering();
      const { container } = renderDeckColorFiltering();
      
      const whiteIcon = container.querySelector('img[src="/svg/mana-white.svg"]');
      if (whiteIcon) {
        fireEvent.click(whiteIcon);
        
        expect(mockNextFilterState).toHaveBeenCalledWith(FilterState.INACTIVE);
        expect(mockColor).toHaveBeenCalledWith(
          expect.any(ColorFilter),
          dispatcher
        );
      }
    });

    it('cycles through filter states when clicked multiple times', () => {
      const { dispatcher } = renderDeckColorFiltering();
      const { container } = renderDeckColorFiltering();
      
      const blueIcon = container.querySelector('img[src="/svg/mana-blue.svg"]');
      if (blueIcon) {
        // First click: INACTIVE -> INCLUDE
        fireEvent.click(blueIcon);
        expect(mockNextFilterState).toHaveBeenCalledWith(FilterState.INACTIVE);
        
        // Second click: INCLUDE -> EXCLUDE
        mockNextFilterState.mockClear();
        fireEvent.click(blueIcon);
        expect(mockNextFilterState).toHaveBeenCalledWith(FilterState.INACTIVE);
      }
    });

    it('updates color filter with new state', () => {
      const { dispatcher } = renderDeckColorFiltering();
      const { container } = renderDeckColorFiltering();
      
      const redIcon = container.querySelector('img[src="/svg/mana-red.svg"]');
      if (redIcon) {
        fireEvent.click(redIcon);
        
        expect(mockColor).toHaveBeenCalledWith(
          expect.objectContaining({
            red: FilterState.INCLUDE, // nextFilterState should return INCLUDE for INACTIVE
          }),
          dispatcher
        );
      }
    });
  });

  describe('query color restrictions', () => {
    it('does not change color when query color is not inactive', () => {
      // Mock the dispatcher to return a query color that's not inactive
      const mockDispatcher = {
        DeckStore: vi.fn(() => ({
          getState: vi.fn(() => 
            Immutable.Map({
              queryColor: new ColorFilter({
                white: FilterState.INCLUDE, // This should prevent changes
                blue: FilterState.INACTIVE,
                black: FilterState.INACTIVE,
                red: FilterState.INACTIVE,
                green: FilterState.INACTIVE,
                colorless: FilterState.INACTIVE,
              }),
            })
          ),
        })),
      };
      
      const { container } = renderDeckColorFiltering();
      
      const whiteIcon = container.querySelector('img[src="/svg/mana-white.svg"]');
      if (whiteIcon) {
        fireEvent.click(whiteIcon);
        
        // Should not call color action because white is specified in query
        expect(mockColor).not.toHaveBeenCalled();
      }
    });

    it('allows changes when query color is inactive', () => {
      // Mock the dispatcher to return a query color that's inactive
      const mockDispatcher = {
        DeckStore: vi.fn(() => ({
          getState: vi.fn(() => 
            Immutable.Map({
              queryColor: new ColorFilter({
                white: FilterState.INACTIVE,
                blue: FilterState.INACTIVE,
                black: FilterState.INACTIVE,
                red: FilterState.INACTIVE,
                green: FilterState.INACTIVE,
                colorless: FilterState.INACTIVE,
              }),
            })
          ),
        })),
      };
      
      const { dispatcher } = renderDeckColorFiltering();
      const { container } = renderDeckColorFiltering();
      
      const greenIcon = container.querySelector('img[src="/svg/mana-green.svg"]');
      if (greenIcon) {
        fireEvent.click(greenIcon);
        
        // Should call color action because green is not specified in query
        expect(mockColor).toHaveBeenCalled();
      }
    });
  });

  describe('component lifecycle', () => {
    it('initializes with empty state', () => {
      renderDeckColorFiltering();
      
      // Component should initialize without errors
      const { container } = renderDeckColorFiltering();
      expect(container.querySelector('.deck-color-filtering')).toBeInTheDocument();
    });

    it('has empty componentDidMount implementation', () => {
      // This test ensures the component mounts without errors
      const { container } = renderDeckColorFiltering();
      expect(container.querySelector('.deck-color-filtering')).toBeInTheDocument();
    });
  });

  describe('accessibility and styling', () => {
    it('applies correct CSS classes', () => {
      const { container } = renderDeckColorFiltering();
      
      expect(container.querySelector('.deck-color-filtering')).toBeInTheDocument();
      expect(container.querySelector('.color-container')).toBeInTheDocument();
      expect(container.querySelector('.color-symbol-container')).toBeInTheDocument();
    });

    it('applies correct margin styling to color symbol container', () => {
      const { container } = renderDeckColorFiltering();
      const colorSymbolContainer = container.querySelector('.color-symbol-container');
      
      expect(colorSymbolContainer).toHaveStyle({ margin: '0' });
    });

    it('provides hover text for each color', () => {
      renderDeckColorFiltering();
      
      // The TextFormat.capitalizeWord should be called for each color
      // This is tested indirectly by ensuring the component renders without errors
      const { container } = renderDeckColorFiltering();
      expect(container.querySelector('.deck-color-filtering')).toBeInTheDocument();
    });
  });

  describe('color filter integration', () => {
    it('works with different color filter configurations', () => {
      const mixedColorFilter = createMockColorFilter({
        white: FilterState.INCLUDE,
        blue: FilterState.EXCLUDE,
        black: FilterState.INACTIVE,
        red: FilterState.INCLUDE,
        green: FilterState.EXCLUDE,
      });
      
      renderDeckColorFiltering({ color: mixedColorFilter });
      
      // Should render without errors with mixed filter states
      const { container } = renderDeckColorFiltering({ color: mixedColorFilter });
      expect(container.querySelector('.deck-color-filtering')).toBeInTheDocument();
    });
  });
});
