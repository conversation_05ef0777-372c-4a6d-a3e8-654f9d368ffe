import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { Tag } from '../../../models/Tags';
import { DeckTags } from './DeckTags';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockSelectTag = vi.mocked(DeckActions.selectTag);
const mockUnselectTag = vi.mocked(DeckActions.unselectTag);
const mockQueryByTags = vi.mocked(DeckActions.queryByTags);

// Mock moment
vi.mock('moment', () => ({
  default: vi.fn((_date) => ({
    isBefore: vi.fn(() => false),
  })),
}));

type DeckTagsProps = React.ComponentProps<typeof DeckTags>;

const createMockTag = (name: string, id = 1): Tag => {
  return new Tag({
    id,
    name,
    createdAt: new Date(),
  });
};

const defaultProps: Omit<DeckTagsProps, 'dispatcher'> = {
  deckTags: Immutable.Map({
    'red-deck': createMockTag('red-deck', 1),
    aggro: createMockTag('aggro', 2),
    budget: createMockTag('budget', 3),
  }),
  deckTagsSelected: Immutable.Map(),
  deckTagsSelectedNot: Immutable.Map(),
};

const renderDeckTags = (props: Partial<DeckTagsProps> = {}) => {
  return renderWithDispatcher(DeckTags, { ...defaultProps, ...props });
};

describe('DeckTags', () => {
  describe('when component renders', () => {
    it('displays tag collection when tags exist', () => {
      const { container } = renderDeckTags();
      expect(container.querySelector('.tag-filter-collection')).toBeInTheDocument();
    });

    it('displays all tags with hash prefix', () => {
      renderDeckTags();
      expect(screen.getByText('#red-deck')).toBeInTheDocument();
      expect(screen.getByText('#aggro')).toBeInTheDocument();
      expect(screen.getByText('#budget')).toBeInTheDocument();
    });

    it('displays see more/less toggle', () => {
      renderDeckTags();
      expect(screen.getByText('See more')).toBeInTheDocument();
    });
  });

  describe('when no tags exist', () => {
    it('displays placeholder message', () => {
      renderDeckTags({ deckTags: Immutable.Map() });

      expect(screen.getByText(/You haven't added any tags yet!/)).toBeInTheDocument();
    });
  });

  describe('tag selection', () => {
    it('displays selected tags with selected styling', () => {
      const selectedTags = Immutable.Map({
        'red-deck': createMockTag('red-deck', 1),
      });

      const { container } = renderDeckTags({ deckTagsSelected: selectedTags });

      const redDeckTag = container.querySelector('.tag.is-selected');
      expect(redDeckTag).toBeInTheDocument();
      expect(redDeckTag).toHaveTextContent('#red-deck');
    });

    it('displays unselected tags without selected styling', () => {
      const { container } = renderDeckTags();

      const unselectedTags = container.querySelectorAll('.tag:not(.is-selected)');
      expect(unselectedTags.length).toBe(3);
    });

    it('calls selectTag when unselected tag is clicked', () => {
      const { dispatcher } = renderDeckTags();

      const redDeckTag = screen.getByText('#red-deck');
      fireEvent.click(redDeckTag);

      expect(mockSelectTag).toHaveBeenCalledWith(expect.objectContaining({ name: 'red-deck' }), dispatcher);
    });

    it('calls unselectTag when selected tag is clicked', () => {
      const selectedTags = Immutable.Map({
        'red-deck': createMockTag('red-deck', 1),
      });
      const { dispatcher } = renderDeckTags({ deckTagsSelected: selectedTags });

      const redDeckTag = screen.getByText('#red-deck');
      fireEvent.click(redDeckTag);

      expect(mockUnselectTag).toHaveBeenCalledWith(expect.objectContaining({ name: 'red-deck' }), dispatcher);
    });

    it('calls queryByTags when tag selection changes', () => {
      const { dispatcher } = renderDeckTags();

      const redDeckTag = screen.getByText('#red-deck');
      fireEvent.click(redDeckTag);

      expect(mockQueryByTags).toHaveBeenCalledWith(expect.any(Immutable.Map), dispatcher);
    });
  });

  describe('see more/less functionality', () => {
    it('changes text to "See less" when see more is clicked', () => {
      renderDeckTags();

      const seeMoreButton = screen.getByText('See more');
      fireEvent.click(seeMoreButton);

      expect(screen.getByText('See less')).toBeInTheDocument();
    });

    it('changes text back to "See more" when see less is clicked', () => {
      renderDeckTags();

      const seeMoreButton = screen.getByText('See more');
      fireEvent.click(seeMoreButton);

      const seeLessButton = screen.getByText('See less');
      fireEvent.click(seeLessButton);

      expect(screen.getByText('See more')).toBeInTheDocument();
    });
  });

  describe('tag sorting', () => {
    it('sorts tags by creation date', () => {
      const tags = Immutable.Map({
        newest: new Tag({
          id: 1,
          name: 'newest',
          createdAt: new Date('2023-12-01T00:00:00Z'),
        }),
        oldest: new Tag({
          id: 2,
          name: 'oldest',
          createdAt: new Date('2023-01-01T00:00:00Z'),
        }),
        middle: new Tag({
          id: 3,
          name: 'middle',
          createdAt: new Date('2023-06-01T00:00:00Z'),
        }),
      });

      renderDeckTags({ deckTags: tags });

      // Tags should be rendered (sorting is tested indirectly)
      expect(screen.getByText('#newest')).toBeInTheDocument();
      expect(screen.getByText('#oldest')).toBeInTheDocument();
      expect(screen.getByText('#middle')).toBeInTheDocument();
    });
  });
});
