import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { DeckShareLink } from './DeckShareLink';

// Mock navigator.clipboard
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn(),
  },
});

const mockWriteText = vi.mocked(navigator.clipboard.writeText);

type DeckShareLinkProps = React.ComponentProps<typeof DeckShareLink>;

const DEFAULT_PROPS: DeckShareLinkProps = {
  href: 'https://example.com/deck/123',
  urlText: 'Deck 123',
};

const renderDeckShareLink = (props: Partial<DeckShareLinkProps> = {}) => {
  return render(<DeckShareLink {...DEFAULT_PROPS} {...props} />);
};

describe('DeckShareLink', () => {
  beforeEach(() => {
    mockWriteText.mockClear();
    mockWriteText.mockResolvedValue(undefined);
  });

  describe('when component renders', () => {
    it('displays the share link', () => {
      renderDeckShareLink();
      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', 'https://example.com/deck/123');
      expect(link).toHaveAttribute('target', '_blank');
    });

    it('displays the URL text in the link', () => {
      renderDeckShareLink();
      expect(screen.getByText(DEFAULT_PROPS.urlText)).toBeInTheDocument();
    });

    it('displays the copy button with initial text', () => {
      renderDeckShareLink();
      expect(screen.getByRole('button', { name: 'Copy URL' })).toBeInTheDocument();
    });

    it('displays the external link icon', () => {
      renderDeckShareLink();
      expect(screen.getByRole('img', { hidden: true })).toBeInstanceOf(SVGElement);
    });

    it('uses correct CSS classes for layout', () => {
      const { container } = renderDeckShareLink();

      expect(container.querySelector('.col-xs-12.col-md-9')).toBeInTheDocument();
      expect(container.querySelector('.col-xs-12.col-md-3')).toBeInTheDocument();
      expect(container.querySelector('.deckbuilder-link-container')).toBeInTheDocument();
    });
  });

  describe('when copy button is clicked', () => {
    it('copies URL text to clipboard', async () => {
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });

      fireEvent.click(copyButton);

      expect(mockWriteText).toHaveBeenCalledWith(DEFAULT_PROPS.urlText);
    });

    it('changes button text to "Copied"', () => {
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });

      fireEvent.click(copyButton);

      expect(screen.getByRole('button', { name: 'Copied' })).toBeInTheDocument();
    });

    it('blurs the button after clicking', () => {
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });
      const blurSpy = vi.spyOn(copyButton, 'blur');

      fireEvent.click(copyButton);

      expect(blurSpy).toHaveBeenCalled();
    });
  });
});
