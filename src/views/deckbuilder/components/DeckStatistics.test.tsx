import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck } from '../../../../tests/fake/FakeCardData';
import { FakeUser } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DecksModule from '../../../models/Decks';
import { DeckCardGrouping } from '../../../models/Decks';
import { DeckStatistics } from './DeckStatistics';

// Mock the deck calculation functions
vi.mock('../../../models/Decks', async () => {
  const actual = await vi.importActual('../../../models/Decks');
  return {
    ...actual,
    calculateCardTypeRatios: vi.fn(),
    calculateManaSymbolRatios: vi.fn(),
    calculateManaSymbolStack: vi.fn(),
    calculateTurnProbabilities: vi.fn(),
  };
});

const mockCalculateCardTypeRatios = vi.mocked(DecksModule.calculateCardTypeRatios);
const mockCalculateManaSymbolRatios = vi.mocked(DecksModule.calculateManaSymbolRatios);
const mockCalculateManaSymbolStack = vi.mocked(DecksModule.calculateManaSymbolStack);
const mockCalculateTurnProbabilities = vi.mocked(DecksModule.calculateTurnProbabilities);

type DeckStatisticsProps = React.ComponentProps<typeof DeckStatistics>;

const defaultProps: Omit<DeckStatisticsProps, 'dispatcher' | 'me'> = {
  deck: create(FakeDeck),
};

const renderDeckStatistics = (props: Partial<DeckStatisticsProps> = {}) => {
  return renderWithDispatcher(DeckStatistics, { 
    ...defaultProps, 
    me: create(FakeUser),
    ...props 
  });
};

describe('DeckStatistics', () => {
  beforeEach(() => {
    // Setup default mock return values
    mockCalculateCardTypeRatios.mockReturnValue(
      Immutable.Map({
        Creature: 0.4,
        Instant: 0.3,
        Sorcery: 0.2,
        Land: 0.1,
      }),
    );

    mockCalculateManaSymbolRatios.mockReturnValue(
      Immutable.Map({
        totalCount: 60,
        red: 0.3,
        blue: 0.2,
        white: 0.2,
        black: 0.2,
        green: 0.1,
        disabled: 0,
      }),
    );

    mockCalculateManaSymbolStack.mockReturnValue(
      Immutable.Map({
        1: Immutable.Map({ white: 5, blue: 3 }),
        2: Immutable.Map({ red: 8, green: 2 }),
        3: Immutable.Map({ black: 6 }),
      }),
    );

    mockCalculateTurnProbabilities.mockReturnValue(
      Immutable.Map({
        'Lightning Bolt': 0.4,
        'Mountain': 0.3,
        'Shock': 0.3,
      }),
    );
  });

  describe('when component renders', () => {
    it('displays the deck statistics container', () => {
      const { container } = renderDeckStatistics();
      expect(container.querySelector('.deck-statistics')).toBeInTheDocument();
    });

    it('calls calculation functions with the provided deck', () => {
      const deck = create(FakeDeck);
      renderDeckStatistics({ deck });

      expect(mockCalculateCardTypeRatios).toHaveBeenCalledWith(deck);
      expect(mockCalculateManaSymbolRatios).toHaveBeenCalledWith(deck);
      expect(mockCalculateManaSymbolStack).toHaveBeenCalledWith(deck);
      expect(mockCalculateTurnProbabilities).toHaveBeenCalledWith(deck, DeckCardGrouping.NAME, true);
    });

    it('renders card types statistics with correct title', () => {
      renderDeckStatistics();
      expect(screen.getByText('Card Types')).toBeInTheDocument();
    });

    it('renders mana symbols statistics with correct title', () => {
      renderDeckStatistics();
      expect(screen.getByText('Mana Symbols')).toBeInTheDocument();
    });

    it('renders mana curve statistics with correct title', () => {
      renderDeckStatistics();
      expect(screen.getByText('Mana Curve')).toBeInTheDocument();
    });

    it('renders turn probabilities section', () => {
      renderDeckStatistics();
      expect(screen.getByText('Turn Probabilities')).toBeInTheDocument();
    });
  });

  describe('turn probabilities section', () => {
    it('displays grouping selector', () => {
      renderDeckStatistics();
      expect(screen.getByText('Group by')).toBeInTheDocument();
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    it('displays grouping options excluding NONE', () => {
      renderDeckStatistics();
      const select = screen.getByRole('combobox');
      
      // Check that grouping options are present (excluding NONE)
      expect(screen.getByRole('option', { name: DeckCardGrouping.NAME })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: DeckCardGrouping.CARD_TYPE })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: DeckCardGrouping.MANA_COST })).toBeInTheDocument();
    });

    it('displays turn columns for turns 1, 2, and 3', () => {
      renderDeckStatistics();
      expect(screen.getByText('Turn 1')).toBeInTheDocument();
      expect(screen.getByText('Turn 2')).toBeInTheDocument();
      expect(screen.getByText('Turn 3')).toBeInTheDocument();
    });

    it('changes grouping when selection changes', () => {
      renderDeckStatistics();
      const select = screen.getByRole('combobox');
      
      fireEvent.change(select, { target: { value: DeckCardGrouping.CARD_TYPE } });
      
      expect(select).toHaveValue(DeckCardGrouping.CARD_TYPE);
      expect(mockCalculateTurnProbabilities).toHaveBeenCalledWith(
        expect.any(Object),
        DeckCardGrouping.CARD_TYPE,
        true
      );
    });
  });

  describe('chart pills rendering', () => {
    it('renders card type chart pills with percentages', () => {
      renderDeckStatistics();
      
      // Should display percentages for card types
      expect(screen.getByText('40%')).toBeInTheDocument(); // Creature
      expect(screen.getByText('30%')).toBeInTheDocument(); // Instant
      expect(screen.getByText('20%')).toBeInTheDocument(); // Sorcery
      expect(screen.getByText('10%')).toBeInTheDocument(); // Land
    });

    it('renders mana symbol chart pills with percentages', () => {
      renderDeckStatistics();
      
      // Should display percentages for mana symbols
      // The exact percentages depend on the mock data
      const chartPills = document.querySelectorAll('.chart-pill');
      expect(chartPills.length).toBeGreaterThan(0);
    });

    it('applies correct colors to chart pills', () => {
      const { container } = renderDeckStatistics();
      
      // Should apply colors based on keyToColor method
      const chartPills = container.querySelectorAll('.chart-pill');
      expect(chartPills.length).toBeGreaterThan(0);
    });
  });

  describe('probability calculations', () => {
    it('calculates turn probabilities correctly', () => {
      renderDeckStatistics();
      
      // The component should display calculated probabilities
      // This is tested indirectly by ensuring the component renders without errors
      expect(screen.getByText('Turn Probabilities')).toBeInTheDocument();
    });

    it('handles mana cost grouping with "Drops" suffix', () => {
      renderDeckStatistics();
      const select = screen.getByRole('combobox');
      
      fireEvent.change(select, { target: { value: DeckCardGrouping.MANA_COST } });
      
      // When grouping by mana cost, should add "Drops" suffix
      expect(mockCalculateTurnProbabilities).toHaveBeenCalledWith(
        expect.any(Object),
        DeckCardGrouping.MANA_COST,
        true
      );
    });

    it('handles land grouping specially', () => {
      renderDeckStatistics();
      
      // Lands should be displayed as "Lands" in the table
      // This is tested indirectly by ensuring the component renders without errors
      expect(screen.getByText('Turn Probabilities')).toBeInTheDocument();
    });
  });

  describe('when deck data changes', () => {
    it('recalculates statistics when deck prop changes', () => {
      const initialDeck = create(FakeDeck, { name: 'Initial Deck' });
      const { rerenderWithDispatcher } = renderDeckStatistics({ deck: initialDeck });

      expect(mockCalculateCardTypeRatios).toHaveBeenCalledWith(initialDeck);

      const newDeck = create(FakeDeck, { name: 'New Deck' });
      rerenderWithDispatcher({ deck: newDeck, me: create(FakeUser) });

      expect(mockCalculateCardTypeRatios).toHaveBeenCalledWith(newDeck);
    });
  });

  describe('component lifecycle', () => {
    it('initializes with NAME grouping', () => {
      renderDeckStatistics();
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(DeckCardGrouping.NAME);
    });

    it('has empty componentDidMount implementation', () => {
      // This test ensures the component mounts without errors
      const { container } = renderDeckStatistics();
      expect(container.querySelector('.deck-statistics')).toBeInTheDocument();
    });
  });

  describe('color mapping', () => {
    it('maps card types to correct colors', () => {
      const { container } = renderDeckStatistics();
      
      // The keyToColor method should map different keys to appropriate colors
      // This is tested indirectly by ensuring chart pills render with styles
      const chartPills = container.querySelectorAll('.chart-pill');
      expect(chartPills.length).toBeGreaterThan(0);
    });

    it('handles special text color for white and enchantment', () => {
      const { container } = renderDeckStatistics();
      
      // White and Enchantment should have special text colors
      // This is tested indirectly by ensuring the component renders without errors
      expect(container.querySelector('.deck-statistics')).toBeInTheDocument();
    });
  });
});
