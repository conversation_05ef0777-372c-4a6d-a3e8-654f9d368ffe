import * as Immutable from 'immutable';
import moment from 'moment';
import * as React from 'react';
import * as DeckActions from '../../../actions/DeckActions';
import * as HasDispatcher from '../../../containers/HasDispatcher';
import { Tag } from '../../../models/Tags';

interface IProps {
  deckTags: Immutable.Map<string, Tag>;
  deckTagsSelected: Immutable.Map<string, Tag>;
  deckTagsSelectedNot: Immutable.Map<string, Tag>;
}

interface IState {
  seeMore: boolean;
}

export const DeckTags = class extends React.Component<IProps & HasDispatcher.IProps, IState> {
  /**
   * @override
   */
  constructor(props: IProps & HasDispatcher.IProps) {
    super(props);
    this.state = {
      seeMore: false,
    };
  }

  /**
   * @override
   */
  componentDidMount() {}

  /**
   * @override
   */
  render() {
    const tags = this.props.deckTags
      .toList()
      .sort((a, b) => (moment(a.get('createdAt')).isBefore(b.get('createdAt')) ? 1 : -1));

    return (
      <div className="row">
        <div className="tag-filter-container col-xs-12" style={{ paddingLeft: 0, paddingRight: 0, marginTop: '1rem' }}>
          {tags.size > 0 ? (
            <div className={'tag-filter-collection' + (this.state.seeMore ? ' is-see-more' : '')}>
              {tags.map((tag: Tag, i) => {
                const selected = this.props.deckTagsSelected.has(tag.get('name'));
                const onClickTag = (evt: React.SyntheticEvent<HTMLElement>) => {
                  evt.preventDefault();
                  if (selected) {
                    this.onSubmitSearch(this.props.deckTagsSelected.remove(tag.get('name')));
                    DeckActions.unselectTag(tag, this.props.dispatcher);
                  } else {
                    this.onSubmitSearch(this.props.deckTagsSelected.set(tag.get('name'), tag));
                    DeckActions.selectTag(tag, this.props.dispatcher);
                  }
                };
                return (
                  <div
                    className={'tag' + (selected ? ' is-selected' : '')}
                    key={tag.get('id') + '-' + i}
                    onClick={onClickTag}
                  >
                    #{tag.get('name')}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="tag-filter-collection--placeholder">
              You haven't added any tags yet! Select a deck and tag it with custom info like #deck, #borrowed or
              #tradebinder1
            </div>
          )}
        </div>
        <div className="filter-action-container col-xs-12">
          <div className="filter-action" onClick={this.onClickSeeMore.bind(this)}>
            {this.state.seeMore ? 'See less' : 'See more'}
          </div>
        </div>
      </div>
    );
  }

  private onClickSeeMore(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({
      seeMore: !this.state.seeMore,
    });
  }

  private onSubmitSearch(tags: Immutable.Map<string, Tag>) {
    DeckActions.queryByTags(tags, this.props.dispatcher);
  }
};
