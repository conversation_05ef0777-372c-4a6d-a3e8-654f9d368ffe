// Iconify
import style from '@iconify/icons-ic/style';
import * as React from 'react';
// Containers, actions, and dispatcher
import * as HasDispatcher from '../../../containers/HasDispatcher';
import * as HasMe from '../../../containers/HasMe';
// Components
// Models
import {
  calculateCardTypeRatios,
  calculateManaSymbolRatios,
  calculateManaSymbolStack,
  calculateTurnProbabilities,
  Deck,
  DeckCardGrouping,
  type DeckCardGroupingKey,
} from '../../../models/Decks';
import { IconSelect } from '../../components/IconSelect';
import { DeckStatisticsColorStack } from './DeckStatisticsColorStack';
import { DeckStatisticsRatios } from './DeckStatisticsRatios';

interface IProps {
  deck: Deck;
}

interface IState {
  grouping: DeckCardGrouping;
}

export const DeckStatistics = HasMe.Attach<HasDispatcher.IProps & IProps>(
  class extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    /**
     * @override
     * @constructor
     */
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = {
        grouping: DeckCardGrouping.NAME,
      };
    }

    /**
     * @override
     */
    componentDidMount() {}

    /**
     * @override
     */
    render() {
      const cardTypeRatios = calculateCardTypeRatios(this.props.deck);
      const manaSymbolStack = calculateManaSymbolStack(this.props.deck);
      const turnProbabilities = calculateTurnProbabilities(this.props.deck, this.state.grouping, true)
        .sortBy(
          (_, key) => key,
          (a, b) => {
            if (this.state.grouping === DeckCardGrouping.MANA_COST) {
              if (a === 'Land') {
                return -1; // Put lands first
              } else {
                return parseInt(a!) === parseInt(b!) ? 0 : parseInt(a!) < parseInt(b!) ? -1 : 1;
              }
            } else {
              return a!.localeCompare(b!);
            }
          },
        )
        .toMap();

      const manaStatistics = calculateManaSymbolRatios(this.props.deck);
      const totalManaSymbols = manaStatistics.get('totalCount');
      const manaSymbolRatios = manaStatistics.remove('totalCount');

      return (
        <div className="container-fluid">
          <div className="section deck-statistics">
            {/* graphs */}
            <div className="row">
              <div className="col-xs-12 col-md-6 flex vertical align-center">
                <DeckStatisticsRatios dispatcher={this.props.dispatcher} ratios={cardTypeRatios} title={'Card Types'} />
                <div className="row justify-center" style={{ width: '100%' }}>
                  {cardTypeRatios
                    .remove('disabled')
                    .keySeq()
                    .map((key: string) => (
                      <div key={key} className="col-xs-12 col-md-6 col-lg-4" style={{ padding: '0 0.5rem' }}>
                        <div
                          className="chart-pill"
                          style={{
                            backgroundColor: this.keyToColor(key),
                            color: key === 'Enchantment' ? '#3e3e3e' : '#ffffff',
                          }}
                        >
                          <div className="chart-pill__ratio">{(cardTypeRatios.get(key) * 100).toFixed() + '%'}</div>
                          <div className="chart-pill__label">{key}</div>
                          <div
                            className="chart-pill__hover"
                            style={{
                              backgroundColor: this.keyToColor(key),
                              color: key === 'Enchantment' ? '#3e3e3e' : '#ffffff',
                            }}
                          >
                            <div className="chart-pill__ratio">
                              {(cardTypeRatios.get(key) * this.props.deck.get('deckCards').size).toFixed(2)}
                            </div>
                            <div className="chart-pill__label">{key}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
              <div className="col-xs-12 col-md-6 flex vertical align-center">
                <DeckStatisticsRatios
                  dispatcher={this.props.dispatcher}
                  ratios={manaSymbolRatios}
                  title={'Mana Symbols'}
                />
                <div className="row justify-center" style={{ width: '100%' }}>
                  {manaSymbolRatios
                    .remove('disabled')
                    .keySeq()
                    .map((key: string) => (
                      <div key={key} className="col-xs-12 col-md-6 col-lg-4" style={{ padding: '0 0.5rem' }}>
                        <div
                          className="chart-pill"
                          style={{
                            backgroundColor: this.keyToColor(key),
                            color: key === 'white' ? '#3e3e3e' : '#ffffff',
                          }}
                        >
                          <div className="chart-pill__ratio">{(manaSymbolRatios.get(key) * 100).toFixed() + '%'}</div>
                          <div className="chart-pill__label">
                            {key.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase())}
                          </div>
                          <div
                            className="chart-pill__hover"
                            style={{
                              backgroundColor: this.keyToColor(key),
                              color: key === 'white' ? '#3e3e3e' : '#ffffff',
                            }}
                          >
                            <div className="chart-pill__ratio">{manaSymbolRatios.get(key) * totalManaSymbols}</div>
                            <div className="chart-pill__label">
                              {key.replace(
                                /\w\S*/g,
                                (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase(),
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
              <div className="col-xs-12 flex vertical align-center" style={{ marginTop: '2rem' }}>
                <DeckStatisticsColorStack
                  dispatcher={this.props.dispatcher}
                  statistics={manaSymbolStack}
                  title={'Mana Curve'}
                />
              </div>

              {/* Turn probabilities table */}
              <div className="col-xs-12 flex vertical align-center">
                <h2
                  className="lato-N6"
                  style={{
                    color: '#3e3e3e',
                    fontSize: 25,
                  }}
                >
                  Turn Probabilities
                </h2>

                {/* grouping options */}
                <div className="deckbuilder-grouping__select-wrapper justify-center">
                  <span className="deckbuilder-grouping__select-wrapper__label">Group by</span>
                  <IconSelect
                    className="icon-container--deck"
                    icon={style}
                    value={this.state.grouping}
                    onChange={this.onChangeGrouping}
                  >
                    <>
                      <option className="deckbuilder-grouping__select-wrapper__select__option" disabled>
                        Grouping
                      </option>
                      {Object.keys(DeckCardGrouping)
                        .map((key: string) => DeckCardGrouping[key as DeckCardGroupingKey])
                        .filter((grouping: DeckCardGrouping) => grouping !== DeckCardGrouping.NONE)
                        .map((key) => (
                          <option
                            key={key}
                            className="deckbuilder-grouping__select-wrapper__select__option"
                            value={key}
                          >
                            {key}
                          </option>
                        ))}
                    </>
                  </IconSelect>
                </div>
                <div className="deck-table" style={{ marginTop: '2rem' }}>
                  {/* grouping */}
                  <div className="deck-table__col grow-xs-1 shrink-xs-1">
                    <div className="deck-table__header cell-border-left">{this.state.grouping}</div>
                    {turnProbabilities.keySeq().map((key: string) => (
                      <div key={key} className="deck-table__row">
                        {key === 'Land'
                          ? 'Lands'
                          : key + (this.state.grouping === DeckCardGrouping.MANA_COST ? ' Drops' : '')}
                      </div>
                    ))}
                  </div>

                  {[1, 2, 3].map((turn: number) => {
                    return (
                      <div key={turn} className="deck-table__col grow-xs-1 shrink-xs-1">
                        <div className="deck-table__header cell-border-left">{'Turn ' + turn}</div>
                        {turnProbabilities.keySeq().map((key: string) => (
                          <div key={key} className="deck-table__row">
                            {this.calculateProbability(turnProbabilities.get(key), turn)}
                          </div>
                        ))}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    private onChangeGrouping = (evt: React.SyntheticEvent<HTMLSelectElement>) => {
      evt.preventDefault();
      this.setState({
        grouping: evt.currentTarget.value as DeckCardGrouping,
      });
    };

    private calculateProbability(value: number, turn: number): string {
      return ((1 - Math.pow(1 - value, turn)) * 100).toFixed(2) + '%';
    }

    private keyToColor = (key: string) => {
      switch (key) {
        case 'Land':
          return '#614A3A';
        case 'Instant':
          return '#D61616';
        case 'Artifact':
          return '#686868';
        case 'Enchantment':
          return '#E8E8E8';
        case 'Planeswalker':
          return '#000000';
        case 'Creature':
          return '#1AC655';
        case 'Sorcery':
          return '#0A73EE';

        case 'blue':
          return '#45C3FF';
        case 'red':
          return '#FF5C35';
        case 'green':
          return '#1AC655';
        case 'white':
          return '#FFE48B';
        case 'black':
          return '#676767';
        case 'colorless':
          return '#C5C5C5';
        case 'disabled':
          return '#C5C5C5';
      }
      return '#C5C5C5';
    };
  },
);
