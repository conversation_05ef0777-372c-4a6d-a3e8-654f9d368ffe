import info_outline from '@iconify/icons-ic/outline-info';
import { Icon } from '@iconify/react';
import * as React from 'react';
// Containers, actions, and dispatcher
import * as DeckActions from '../../../actions/DeckActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
// Helpers
import track from '../../../helpers/ga_helper';
import { CardLayout } from '../../../models/CardLayout';
import { Card } from '../../../models/Cards';
// Models
import { Deck, DeckBoard } from '../../../models/Decks';
import { ColorFilter, ColorOption } from '../../../models/filters/ColorFilter';
import { PricingSource } from '../../../models/PricingSource';
import CardSearcher from '../../components/CardSearcher';
import { MTGCardSearcher } from '../../components/cardSearcher/MTGCardSearcher';

interface IProps {
  dispatcher: Dispatcher;
  pricingSource: PricingSource;
  deck: Deck;
}

interface IState {
  query: string;
  chosenCard?: Card;
  currentBoard?: DeckBoard;
  fourCardsActive: boolean;
  cardCanBeAdded: boolean;
}

export class DeckBuilderAdd extends React.Component<IProps, IState> {
  private readonly cardSearcher: React.RefObject<CardSearcher>;

  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.cardSearcher = React.createRef<CardSearcher>();
    this.state = {
      query: '',
      chosenCard: undefined,
      currentBoard:
        props.deck.get('boards') && props.deck.get('boards').size ? props.deck.get('boards').get(0) : undefined,
      fourCardsActive: false,
      cardCanBeAdded: true,
    };
  }

  /**
   * @override
   */
  componentWillReceiveProps(props: IProps) {
    if (!this.state.currentBoard) {
      this.setState({
        currentBoard:
          props.deck.get('boards') && props.deck.get('boards').size ? props.deck.get('boards').get(0) : undefined,
      });
    }
  }

  /**
   * @override
   */
  componentDidMount() {
    document.addEventListener('keydown', this.handleKeyChange.bind(this));
    document.addEventListener('keyup', this.handleKeyChange.bind(this));
    if (this.cardSearcher.current) {
      this.cardSearcher.current.focus();
    }
  }

  /**
   * @override
   */
  componentWillUnmount() {
    document.removeEventListener('keydown', this.handleKeyChange.bind(this));
    document.removeEventListener('keyup', this.handleKeyChange.bind(this));
  }

  /**
   * @override
   */
  render() {
    return (
      <form className="deckbuilder-add__form" onSubmit={this.onSubmitInput}>
        <div className="deckbuilder-add">
          <div className="row">
            {/* search cards */}
            <div className="col-xs-12 col-xl-6" style={{ marginBottom: '1rem' }}>
              <div className="deckbuilder-info-container">
                <div className="deckbuilder-info-heading">Search Cards</div>
                <div
                  onFocus={this.updateCardCanBeAdded.bind(this, true)}
                  onBlur={this.updateCardCanBeAdded.bind(this, false)}
                >
                  <MTGCardSearcher
                    searcherRef={this.cardSearcher}
                    dispatcher={this.props.dispatcher}
                    chooseSuggestion={this.onChooseSuggestion.bind(this)}
                  />
                </div>
              </div>
            </div>

            {/* select board */}
            <div className="col-xs-12 col-md-6 col-xl-3" style={{ marginBottom: '1rem' }}>
              <div className="deckbuilder-info-container">
                <div className="deckbuilder-info-heading">Board</div>
                <select
                  ref="board-selector"
                  className="deckbuilder-add__select"
                  value={this.state.currentBoard ? this.state.currentBoard.get('id') : undefined}
                  onChange={this.onChangeCurrentBoard}
                >
                  <option className="deckbuilder-add__select__option" disabled>
                    Boards
                  </option>
                  {this.props.deck.get('boards').map((board: DeckBoard) => (
                    <option key={board.get('name')} className="deckbuilder-add__select__option" value={board.get('id')}>
                      {board.get('name')}
                    </option>
                  ))}
                  <option className="deckbuilder-add__select__option" value={'Add Board...'}>
                    Add Board...
                  </option>
                </select>
              </div>
            </div>

            {/* add card(s) */}
            <div className="col-xs-12 col-md-6 col-xl-3 flex" style={{ margin: '2rem 0 1rem 0' }}>
              <button
                className="button-primary"
                style={{ height: 'fit-content' }}
                onClick={this.onClickAdd}
                onFocus={this.updateCardCanBeAdded.bind(this, true)}
                onBlur={this.updateCardCanBeAdded.bind(this, false)}
              >
                {`Add ${this.state.fourCardsActive ? 'Four' : 'One'}`}
              </button>
              <div className="hover-hint-container" style={{ margin: '0.5rem 0 0 1rem' }}>
                <div className="hover-hint">
                  <Icon className="deckbuilder-hint" height={'24px'} width={'24px'} icon={info_outline} />
                  <div className="hover-hint-tooltip-container" style={{ left: '-15.0rem' }}>
                    <div className="hover-hint-tooltip">Hold Ctrl/Cmd to Add Four</div>
                  </div>
                </div>
              </div>
            </div>

            {/* add lands */}
            <div className="col-xs-12 flex align-center">
              <div className="deckbuilder-info-container">
                <div className="deckbuilder-info-heading">Add Lands</div>
                <div className="color-container" style={{ marginTop: '0.5rem' }}>
                  <div className="color-symbol-container" style={{ margin: '0' }}>
                    {ColorFilter.onlyColors().map((color: ColorOption, index: number) => (
                      <div key={color} className={'color-symbol'}>
                        <button className="card-icon-button" onClick={this.onClickMana.bind(this, color)}>
                          <img src={`/svg/mana-${color}.svg`} width="32px" height="32px" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    );
  }

  private onChangeCurrentBoard = (evt: React.SyntheticEvent<HTMLSelectElement>) => {
    evt.preventDefault();
    if (evt.currentTarget.value === 'Add Board...') {
      this.addDeckBoard();
    } else {
      this.setState({
        currentBoard: this.props.deck.boardById(
          parseInt(evt.currentTarget.value),
        )! /* only boards in the deck can be selected here so the id will match */,
      });
    }
  };

  private onSubmitInput = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    evt.stopPropagation();
    this.submitCards();

    if (this.cardSearcher.current) {
      this.cardSearcher.current.focus();
    }
  };

  private onClickAdd = (evt: React.MouseEvent<HTMLElement>) => {
    evt.preventDefault();
    this.submitCards();
  };

  private onClickMana = (color: string, evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    track('decks', 'build', 'basic_land');
    DeckActions.addLandCard(this.props.deck, color as any, this.props.dispatcher);
  };

  private onChooseSuggestion = (suggestion?: Card) => {
    this.setState({
      query: suggestion ? this.suggestionDisplayName(suggestion) + ' - ' + this.suggestionLabel(suggestion) : '',
      chosenCard: suggestion,
    });
    if (this.cardSearcher.current) {
      this.cardSearcher.current.focus();
    }
  };

  private submitCards = () => {
    if (this.state.fourCardsActive) {
      this.addDeckCard(4);
    } else {
      this.addDeckCard(1);
    }
  };

  private addDeckCard = (count: number) => {
    if (this.state.currentBoard && this.state.chosenCard) {
      track('decks', 'build', 'add', count);
      DeckActions.addDeckCard(
        this.props.deck,
        this.state.currentBoard,
        this.state.chosenCard,
        count,
        this.props.dispatcher,
      );
    }
  };

  private addDeckBoard = () => {
    track('decks', 'board', 'add');
    DeckActions.addDeckBoard(this.props.deck, this.props.dispatcher);
    window.scrollTo(0, document.body.scrollHeight);
  };

  // Chrome doesn't register button or form input when the user presses control+enter,
  // even if they focus on it. To circumvent this, this.state.cardCanBeAdded is set to true
  // whenever the Add One/Four button or CardSearcher is focused on.
  private handleKeyChange(evt: KeyboardEvent) {
    if (evt.ctrlKey || evt.metaKey) {
      if (evt.key === 'Enter' && this.state.cardCanBeAdded) {
        this.addDeckCard(4);
      }
      this.setState({
        fourCardsActive: true,
      });
    } else {
      this.setState({
        fourCardsActive: false,
      });
    }
  }

  private updateCardCanBeAdded(focus: boolean) {
    this.setState({
      cardCanBeAdded: focus,
    });
  }

  private suggestionDisplayName(card: Card): string {
    return card.get('layout') == CardLayout.TOKEN ? `${card.get('name')} (Token)` : card.get('name');
  }

  private suggestionLabel(card: Card): string {
    const num = card.get('collectorNumber');
    const setName = card.get('setName');
    let label: string;
    if (num) {
      label = `${setName} (${num})`;
    } else {
      label = setName;
    }

    return label.substring(0, 50);
  }
}
