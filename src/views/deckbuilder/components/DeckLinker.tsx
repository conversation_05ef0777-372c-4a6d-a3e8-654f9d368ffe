// npm
import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardStagingActions from '../../../actions/CardStagingActions';
import * as InputSourceActions from '../../../actions/InputSourceActions';
// APIs and Actions
import * as DecksAPI from '../../../api/Decks';
import * as TagsAPI from '../../../api/Tags';
// Dispatcher and Helpers
import Dispatcher from '../../../dispatcher/Dispatcher';
import { DateFormat } from '../../../helpers/fmt';
import { CardInstance } from '../../../models/CardInstances';
import { CellBorders } from '../../../models/CellBorder';
// Models
import { Deck, DeckCard } from '../../../models/Decks';
import { Foil } from '../../../models/Foil';
import { InputSourceType } from '../../../models/InputSource';
import { Tag } from '../../../models/Tags';
import { User } from '../../../models/Users';
// Components
import * as Table from '../../components/Table';
import { ColumnSize, TableConfiguration } from '../../components/Table';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  deck: Deck;
  deckCards: Immutable.List<DeckCard>;
}

interface IState {
  deckCards: Immutable.List<DeckCard>;
  cardInstances: Immutable.List<CardInstance>;
  cardInstanceTags: Immutable.Map<number, Immutable.List<Tag>>;
  isLoading: boolean;
  confirmIndex?: number;
}

export class DeckLinker extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {
      isLoading: true,
      deckCards: props.deckCards,
      cardInstances: Immutable.List<CardInstance>(),
      cardInstanceTags: Immutable.Map<number, Immutable.List<Tag>>(),
      confirmIndex: undefined,
    };
  }
  /**
   * @override
   */
  componentDidMount() {
    this.loadCardInstances();
  }

  /**
   * @override
   */
  render() {
    const numCardsUnlinked = this.state.deckCards.filter((deckCard: DeckCard) => !!deckCard.get('cardInstanceId')).size;

    return (
      <>
        <div>
          <div className="dialog-heading" style={{ marginBottom: 0 }}>
            {`${this.props.deck.get('cards').get(this.state.deckCards.get(0).get('cardId')).get('name')}`}
          </div>
          <div className="dialog-subheading">{`${numCardsUnlinked}/${this.state.deckCards.size} Linked`}</div>
        </div>
        {this.state.isLoading ? null : this.renderTable()}
      </>
    );
  }

  private renderTable = () => {
    const linkableCards = this.state.cardInstances.filter(
      (card: CardInstance) => card.get('linkedResources') !== undefined,
    );

    if (!linkableCards.size) {
      return (
        <div>
          <div className="lato-N4 flex align-center justify-center" style={{ margin: '1rem 0', textAlign: 'center' }}>
            You don't own any copies of this card, would you like to add one to your collection?
          </div>
          <div className="flex justify-center">
            <button className="button-primary" onClick={this.onClickAddCard}>
              Add Card
            </button>
          </div>
        </div>
      );
    }

    const tableConfiguration = new TableConfiguration([
      TableConfiguration.repeat(3, ColumnSize.MAX_CONTENT),
      ColumnSize.MINMAX_1FR,
      TableConfiguration.repeat(2, ColumnSize.MAX_CONTENT),
    ]);

    return (
      <>
        <div style={{ overflowX: 'scroll' }}>
          <div className="col-xs-12" style={{ margin: '0 auto' }}>
            <div className="base-table" style={{ ...tableConfiguration.style(), margin: '1rem auto' }}>
              <div className="table__header cell-border-left">Link</div>
              <div className="table__header cell-border-left">Set</div>
              <div className="table__header cell-border-left">Foil</div>
              <div className="table__header cell-border-left">Tags</div>
              <div className="table__header cell-border-left">Condition</div>
              <div className="table__header cell-border-left">Date Added</div>
              {linkableCards.map((cardInstance: CardInstance, index: number) => {
                const dark = index % 2 === 0 ? true : false;
                const final = linkableCards.size - 1 === index;
                return (
                  <React.Fragment key={index}>
                    <Table.DeckLinkCell
                      cellBorders={CellBorders.default(final)}
                      dark={dark}
                      deck={this.props.deck}
                      cardInstance={cardInstance}
                      isConfirming={index === this.state.confirmIndex}
                      onRequestConfirmation={() => this.onRequestConfirmation(index)}
                      onLink={() => this.linkCardInstance(cardInstance)}
                    />
                    <Table.SetIconCell
                      cellBorders={CellBorders.default(final)}
                      dark={dark}
                      setName={cardInstance.get('cardSetName')}
                      setCode={cardInstance.get('cardSetCode')}
                      rarity={cardInstance.get('cardRarity')}
                      hoverText={true}
                    />
                    <Table.CheckCell
                      cellBorders={CellBorders.default(final)}
                      dark={dark}
                      checked={cardInstance.get('foil') === Foil.On}
                    />
                    <Table.TagsCell
                      cellBorders={CellBorders.default(final)}
                      dark={dark}
                      cardInstanceTags={this.state.cardInstanceTags}
                      cardInstance={cardInstance}
                    />
                    <Table.TextCell
                      cellBorders={CellBorders.default(final)}
                      dark={dark}
                      text={cardInstance.get('condition').toString()}
                    />
                    <Table.TextCell
                      cellBorders={CellBorders.rightCell(final)}
                      dark={dark}
                      text={DateFormat.human(
                        cardInstance.get('createdAt'),
                        this.props.me.get('preferences').get('localization').get('timezone'),
                      )}
                    />
                  </React.Fragment>
                );
              })}
            </div>
          </div>
        </div>
        <div>
          <div className="lato-N4 flex align-center justify-center" style={{ margin: '1rem 0', textAlign: 'center' }}>
            Would you like to add one of these cards to your collection?
          </div>
          <div className="flex justify-center">
            <button className="button-primary" onClick={this.onClickAddCard}>
              Add Card
            </button>
          </div>
        </div>
      </>
    );
  };

  private heightCalculation(cardCount: number): string {
    switch (cardCount) {
      case 1:
        return '10rem';
      case 2:
        return '16.25rem';
      case 3:
        return '22.75rem';
      case 4:
        return '29.25rem';
      default:
        return 'unset';
    }
  }

  private onClickAddCard = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    evt.stopPropagation();
    (async () => {
      const inputSource = await InputSourceActions.create(this.props.dispatcher, InputSourceType.MANUAL, 'Web Client');
      const card = this.props.deck.get('cards').get(this.state.deckCards.get(0).get('cardId'));
      await CardStagingActions.addCardFromDeck(card.get('jsonID'), inputSource.get('uuid'), this.props.dispatcher);
      this.loadCardInstances();
    })();
  };

  private linkCardInstance = async (cardInstance: CardInstance) => {
    const resources = cardInstance.get('linkedResources');
    const deck = resources === undefined ? undefined : resources.get('deck');
    const linkedDeckCard = resources === undefined ? undefined : resources.get('linkedDeckCard');
    if (deck !== undefined && linkedDeckCard !== undefined) {
      if (deck.get('id')) {
        await DecksAPI.unlinkCardInstance(deck.get('uuid'), linkedDeckCard.get('cardID'), cardInstance);
        await this.loadCardInstances(true);
        this.setState({
          deckCards: this.state.deckCards
            .map((candidate: DeckCard) =>
              candidate.get('id') === linkedDeckCard.get('cardID') ? candidate.set('cardInstanceId', 0) : candidate,
            )
            .toList(),
          confirmIndex: undefined,
        });
      } else {
        const deckCards = this.state.deckCards.filter((deckCard: DeckCard) => !deckCard.get('cardInstanceId')).toList();
        if (!deckCards.size) {
          return;
        }
        let deckCard = deckCards.first();
        deckCards.forEach((candidate: DeckCard) => {
          if (
            this.props.deck.get('cards').get(candidate.get('cardId')).get('setCode') === cardInstance.get('cardSetCode')
          ) {
            deckCard = candidate;
          }
        });
        await DecksAPI.linkCardInstance(this.props.deck, deckCard, cardInstance);
        await this.loadCardInstances(true);
        this.setState({
          deckCards: this.state.deckCards
            .map((candidate: DeckCard) =>
              candidate.get('id') === deckCard.get('id')
                ? candidate.set('cardInstanceId', cardInstance.get('id'))
                : candidate,
            )
            .toList(),
          confirmIndex: undefined,
        });
      }
    }
  };

  private loadCardInstances = async (skipTags?: boolean) => {
    const cardInstances = (await DecksAPI.linkableCardInstances(this.props.deck, this.state.deckCards.get(0)))
      .sort((a, b) => (a.get('id') < b.get('id') ? 1 : -1))
      .toList();
    this.setState({
      isLoading: false,
      cardInstances: cardInstances,
    });
    if (!skipTags) {
      cardInstances.forEach(async (cardInstance: CardInstance) => {
        const tags = await TagsAPI.lookup(this.props.me.get('username'), Immutable.List([cardInstance]));
        this.setState({
          cardInstanceTags: this.state.cardInstanceTags.set(cardInstance.get('id'), tags.toList()),
        });
      });
    }
  };

  private onRequestConfirmation(index: number) {
    this.setState({
      confirmIndex: index,
    });
  }
}
