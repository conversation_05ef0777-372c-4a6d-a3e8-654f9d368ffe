import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck } from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { DeckTile } from './DeckTile';

// Mock history helper
vi.mock('../../../helpers/history', () => ({
  default: {
    push: vi.fn(),
  },
}));

// Mock CoreAssets helper
vi.mock('../../../helpers/core_assets', () => ({
  CoreAssets: {
    imageHost: vi.fn().mockReturnValue('https://example.com'),
  },
}));

import { CoreAssets } from '../../../helpers/core_assets';
import history from '../../../helpers/history';

const mockHistoryPush = vi.mocked(history.push);
const mockImageHost = vi.mocked(CoreAssets.imageHost);

type DeckTileProps = React.ComponentProps<typeof DeckTile>;

const defaultProps: Omit<DeckTileProps, 'dispatcher'> = {
  deck: create(FakeDeck, {
    name: 'Test Deck',
    uuid: 'test-deck-uuid',
    image: 'test-image',
    colorWhite: true,
    colorBlue: false,
    colorBlack: true,
    colorRed: false,
    colorGreen: true,
  }),
};

const renderDeckTile = (props: Partial<DeckTileProps> = {}) => {
  return renderWithDispatcher(DeckTile, { ...defaultProps, ...props });
};

describe('DeckTile', () => {
  beforeEach(() => {
    mockHistoryPush.mockClear();
    mockImageHost.mockReturnValue('https://example.com');
  });

  describe('when component renders', () => {
    it('displays the deck tile container', () => {
      const { container } = renderDeckTile();
      expect(container.querySelector('.deck-tile')).toBeInTheDocument();
    });

    it('displays the deck name', () => {
      renderDeckTile();
      expect(screen.getByText('Test Deck')).toBeInTheDocument();
    });

    it('displays the deck image wrapper', () => {
      const { container } = renderDeckTile();
      expect(container.querySelector('.deck-tile__image-wrapper')).toBeInTheDocument();
    });

    it('displays the color indicators', () => {
      const { container } = renderDeckTile();
      expect(container.querySelector('.deck-tile__colors')).toBeInTheDocument();
    });

    it('shows correct color indicators based on deck colors', () => {
      const { container } = renderDeckTile();

      // White should be on
      expect(container.querySelector('.deck-tile__colors__color.is-white.on')).toBeInTheDocument();

      // Blue should be off (element exists but doesn't have 'on' class)
      const blueElement = container.querySelector('.deck-tile__colors__color.is-blue');
      expect(blueElement).toBeInTheDocument();
      expect(blueElement).not.toHaveClass('on');

      // Black should be on
      expect(container.querySelector('.deck-tile__colors__color.is-black.on')).toBeInTheDocument();

      // Red should be off (element exists but doesn't have 'on' class)
      const redElement = container.querySelector('.deck-tile__colors__color.is-red');
      expect(redElement).toBeInTheDocument();
      expect(redElement).not.toHaveClass('on');

      // Green should be on
      expect(container.querySelector('.deck-tile__colors__color.is-green.on')).toBeInTheDocument();
    });
  });

  describe('deck image handling', () => {
    it('uses deck image when available', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, { image: 'custom-image' }),
      });

      const imageElement = container.querySelector('.deck-tile__image-wrapper__image');
      expect(imageElement).toHaveStyle({
        backgroundImage: 'url(https://example.com/card_art_hq/custom-image.jpg)',
      });
    });

    it('uses default image when deck image is null', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, { image: null }),
      });

      const imageElement = container.querySelector('.deck-tile__image-wrapper__image');
      expect(imageElement).toHaveStyle({
        backgroundImage: 'url(/images/deck-avatar-default.jpg)',
      });
    });

    /** But why... */
    it('uses default image when deck image is "null" string', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, { image: 'null' }),
      });

      const imageElement = container.querySelector('.deck-tile__image-wrapper__image');
      expect(imageElement).toHaveStyle({
        backgroundImage: 'url(/images/deck-avatar-default.jpg)',
      });
    });

    it('uses default image when deck image is undefined', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, { image: undefined }),
      });

      const imageElement = container.querySelector('.deck-tile__image-wrapper__image');
      expect(imageElement).toHaveStyle({
        backgroundImage: 'url(/images/deck-avatar-default.jpg)',
      });
    });
  });

  describe('when user clicks on deck tile', () => {
    it('navigates to deck page', () => {
      renderDeckTile();
      const deckTile = screen.getByText('Test Deck').closest('.deck-tile');

      if (deckTile) {
        fireEvent.click(deckTile);
        expect(mockHistoryPush).toHaveBeenCalledWith('/decks/test-deck-uuid');
      }
    });
  });

  describe('with different deck configurations', () => {
    it('renders deck with all colors enabled', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, {
          colorWhite: true,
          colorBlue: true,
          colorBlack: true,
          colorRed: true,
          colorGreen: true,
        }),
      });

      expect(container.querySelector('.deck-tile__colors__color.is-white.on')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-blue.on')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-black.on')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-red.on')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-green.on')).toBeInTheDocument();
    });

    it('renders deck with no colors enabled', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, {
          colorWhite: false,
          colorBlue: false,
          colorBlack: false,
          colorRed: false,
          colorGreen: false,
        }),
      });

      // All color elements should exist but none should have the 'on' class
      const whiteElement = container.querySelector('.deck-tile__colors__color.is-white');
      expect(whiteElement).toBeInTheDocument();
      expect(whiteElement).not.toHaveClass('on');

      const blueElement = container.querySelector('.deck-tile__colors__color.is-blue');
      expect(blueElement).toBeInTheDocument();
      expect(blueElement).not.toHaveClass('on');

      const blackElement = container.querySelector('.deck-tile__colors__color.is-black');
      expect(blackElement).toBeInTheDocument();
      expect(blackElement).not.toHaveClass('on');

      const redElement = container.querySelector('.deck-tile__colors__color.is-red');
      expect(redElement).toBeInTheDocument();
      expect(redElement).not.toHaveClass('on');

      const greenElement = container.querySelector('.deck-tile__colors__color.is-green');
      expect(greenElement).toBeInTheDocument();
      expect(greenElement).not.toHaveClass('on');
    });

    it('renders deck with special characters in name', () => {
      const specialName = 'Deck with "Special" & <Characters>';
      renderDeckTile({
        deck: create(FakeDeck, { name: specialName }),
      });

      expect(screen.getByText(specialName)).toBeInTheDocument();
    });
  });
});
