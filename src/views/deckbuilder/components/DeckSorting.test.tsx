import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { DeckSorting as Sorting } from '../../../models/sorting/DeckSorting';
import { DeckSorting } from './DeckSorting';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockSorting = vi.mocked(DeckActions.sorting);

type DeckSortingProps = React.ComponentProps<typeof DeckSorting>;

const defaultProps: Omit<DeckSortingProps, 'dispatcher'> = {
  sorting: Sorting.NAME_ASC,
};

const renderDeckSorting = (props: Partial<DeckSortingProps> = {}) => {
  return renderWithDispatcher(DeckSorting, { ...defaultProps, ...props });
};

describe('DeckSorting', () => {
  describe('when component renders', () => {
    it('displays the select dropdown with current sorting value', () => {
      renderDeckSorting({ sorting: Sorting.DATE_ADDED_DESC });
      const select = screen.getByDisplayValue(Sorting.DATE_ADDED_DESC);
      expect(select).toBeInTheDocument();
    });

    it('displays disabled "Sort By" option', () => {
      renderDeckSorting();
      const disabledOption = screen.getByRole('option', { name: 'Sort By' });
      expect(disabledOption).toBeDisabled();
    });

    it('displays all sorting options', () => {
      renderDeckSorting();

      // Check that all sorting options are present
      Object.keys(Sorting).forEach((key) => {
        const sortingValue = Sorting[key as keyof typeof Sorting];
        const option = screen.getByRole('option', { name: sortingValue });
        expect(option).toBeInTheDocument();
      });
    });

    it('applies correct CSS classes', () => {
      const { container } = renderDeckSorting();

      const sortingOptions = container.querySelectorAll('.deck-sorting__select__option');
      expect(sortingOptions.length).toBeGreaterThan(DeckSorting.length);
    });
  });

  describe('when user interacts with sorting selector', () => {
    it('calls DeckActions.sorting when selection changes', () => {
      const { dispatcher } = renderDeckSorting();
      const select = screen.getByRole('combobox');

      fireEvent.change(select, { target: { value: Sorting.DATE_ADDED_DESC } });

      expect(mockSorting).toHaveBeenCalledWith(Sorting.DATE_ADDED_DESC, dispatcher);
    });

    it('handles different sorting values correctly', () => {
      const { dispatcher } = renderDeckSorting();
      const select = screen.getByRole('combobox');

      // Test changing to different sorting values
      fireEvent.change(select, { target: { value: Sorting.NAME_DESC } });
      expect(mockSorting).toHaveBeenCalledWith(Sorting.NAME_DESC, dispatcher);

      fireEvent.change(select, { target: { value: Sorting.PRICE_ASC } });
      expect(mockSorting).toHaveBeenCalledWith(Sorting.PRICE_ASC, dispatcher);

      fireEvent.change(select, { target: { value: Sorting.DATE_EDITED_DESC } });
      expect(mockSorting).toHaveBeenCalledWith(Sorting.DATE_EDITED_DESC, dispatcher);
    });
  });

  describe('when component has different initial sorting values', () => {
    it('renders with NAME_ASC sorting selected', () => {
      renderDeckSorting({ sorting: Sorting.NAME_ASC });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Sorting.NAME_ASC);
    });
  });
});
