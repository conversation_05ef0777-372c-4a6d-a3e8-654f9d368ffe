import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { Arranging } from '../../../models/Arranging';
import { DeckBuilderArrangement } from './DeckBuilderArrangement';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockArranging = vi.mocked(DeckActions.arranging);

type DeckBuilderArrangementProps = React.ComponentProps<typeof DeckBuilderArrangement>;

const defaultProps: Omit<DeckBuilderArrangementProps, 'dispatcher'> = {
  deckArranging: Arranging.NONE,
};

const renderDeckBuilderArrangement = (props: Partial<DeckBuilderArrangementProps> = {}) => {
  return renderWithDispatcher(DeckBuilderArrangement, { ...defaultProps, ...props });
};

describe('DeckBuilderArrangement', () => {
  describe('when component renders', () => {
    it('displays the arrange by heading', () => {
      renderDeckBuilderArrangement();
      expect(screen.getByText('Arrange By')).toBeInTheDocument();
    });

    it('displays the select dropdown with current arranging value', () => {
      renderDeckBuilderArrangement({ deckArranging: Arranging.ALPHABETICAL });
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
      expect(select).toHaveValue(Arranging.ALPHABETICAL);
    });

    it('displays all arranging options', () => {
      renderDeckBuilderArrangement();
      const select = screen.getByRole('combobox');
      
      // Check that all arranging options are present
      Object.values(Arranging).forEach((arranging) => {
        const option = screen.getByRole('option', { name: arranging });
        expect(option).toBeInTheDocument();
      });
    });

    it('displays disabled "Arrange by" option', () => {
      renderDeckBuilderArrangement();
      const disabledOption = screen.getByRole('option', { name: 'Arrange by' });
      expect(disabledOption).toBeInTheDocument();
      expect(disabledOption).toBeDisabled();
    });
  });

  describe('when user interacts with arranging selector', () => {
    it('calls DeckActions.arranging when selection changes', () => {
      const { dispatcher } = renderDeckBuilderArrangement();
      const select = screen.getByRole('combobox');
      
      fireEvent.change(select, { target: { value: Arranging.ALPHABETICAL } });
      
      expect(mockArranging).toHaveBeenCalledWith(Arranging.ALPHABETICAL, dispatcher);
    });

    it('prevents default event behavior when selection changes', () => {
      renderDeckBuilderArrangement();
      const select = screen.getByRole('combobox');
      
      const changeEvent = new Event('change', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(changeEvent, 'preventDefault');
      
      fireEvent(select, changeEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('handles different arranging values correctly', () => {
      const { dispatcher } = renderDeckBuilderArrangement();
      const select = screen.getByRole('combobox');
      
      // Test changing to different arranging values
      fireEvent.change(select, { target: { value: Arranging.MANA_COST } });
      expect(mockArranging).toHaveBeenCalledWith(Arranging.MANA_COST, dispatcher);
      
      fireEvent.change(select, { target: { value: Arranging.CARD_TYPE } });
      expect(mockArranging).toHaveBeenCalledWith(Arranging.CARD_TYPE, dispatcher);
    });
  });

  describe('when component has different initial arranging values', () => {
    it('renders with NONE arranging selected', () => {
      renderDeckBuilderArrangement({ deckArranging: Arranging.NONE });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Arranging.NONE);
    });

    it('renders with ALPHABETICAL arranging selected', () => {
      renderDeckBuilderArrangement({ deckArranging: Arranging.ALPHABETICAL });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Arranging.ALPHABETICAL);
    });

    it('renders with MANA_COST arranging selected', () => {
      renderDeckBuilderArrangement({ deckArranging: Arranging.MANA_COST });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Arranging.MANA_COST);
    });

    it('renders with CARD_TYPE arranging selected', () => {
      renderDeckBuilderArrangement({ deckArranging: Arranging.CARD_TYPE });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Arranging.CARD_TYPE);
    });
  });
});
