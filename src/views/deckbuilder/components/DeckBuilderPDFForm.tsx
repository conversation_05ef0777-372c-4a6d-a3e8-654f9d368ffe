import * as React from 'react';
import { TextFormat } from '../../../helpers/fmt';
import track from '../../../helpers/ga_helper';
import isServerside from '../../../helpers/serverside';
import { Validator } from '../../../lib/deckbuilder/validators/DeckValidator';
import { Deck, DeckBoard, DeckCard } from '../../../models/Decks';
import { Legality, type LegalityKey } from '../../../models/Legality';

const Deck2PDF = !isServerside() ? require('./DeckToPDF') : undefined;

interface IProps {
  deck: Deck;
}

interface IState {
  firstName: string;
  lastName: string;
  dci: string;
  designer: string;
  event: string;
  location: string;
  date: string;
  legality: Legality;
}

export class DeckBuilderPDFForm extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      firstName: '',
      lastName: '',
      designer: '',
      dci: '',
      event: '',
      location: '',
      date: '',
      legality: this.props.deck.get('legality'),
    };
  }

  render() {
    const validLegalities = Object.keys(Legality)
      .filter((key) => Validator(Legality[key as LegalityKey]).check(this.props.deck, false).length === 0)
      .map((key) => Legality[key as LegalityKey]);

    return (
      <div style={{ maxWidth: '50rem', padding: '0 2rem', boxSizing: 'border-box' }}>
        <form onSubmit={this.onClickCreatePDF}>
          <div className="container">
            <div className="row">
              <h2
                className="offset-xs-3 col-xs-9 lato-N6"
                style={{ textAlign: 'left', marginTop: '3.5rem', paddingLeft: '0.1rem' }}
              >
                Please enter the following personal details
              </h2>

              <label
                className="col-xs-3 lato-N6 flex align-center justify-end"
                htmlFor="firstname"
                style={{ textAlign: 'right', textTransform: 'uppercase', color: '#696969', marginTop: '1rem' }}
              >
                First name
              </label>
              <input
                name="firstname"
                className="col-xs-9 input"
                onChange={this.onChangeFirstName}
                value={this.state.firstName}
                style={{ marginTop: '1rem' }}
              />

              <label
                className="col-xs-3 lato-N6 flex align-center justify-end"
                htmlFor="lastname"
                style={{ textAlign: 'right', textTransform: 'uppercase', color: '#696969', marginTop: '1rem' }}
              >
                Last name
              </label>
              <input
                name="lastname"
                className="col-xs-9 input"
                onChange={this.onChangeLastName}
                value={this.state.lastName}
                style={{ marginTop: '1rem' }}
              />

              <label
                className="col-xs-3 lato-N6 flex align-center justify-end"
                htmlFor="dci"
                style={{ textAlign: 'right', textTransform: 'uppercase', color: '#696969', marginTop: '1rem' }}
              >
                DCI
              </label>
              <input
                name="dci"
                className="col-xs-9 input"
                onChange={this.onChangeDCI}
                value={this.state.dci}
                style={{ marginTop: '1rem' }}
              />

              <label
                className="col-xs-3 lato-N6 flex align-center justify-end"
                htmlFor="dci"
                style={{ textAlign: 'right', textTransform: 'uppercase', color: '#696969', marginTop: '1rem' }}
              >
                Designer
              </label>
              <input
                name="dci"
                className="col-xs-9 input"
                onChange={this.onChangeDesigner}
                value={this.state.designer}
                style={{ marginTop: '1rem' }}
              />

              <h2
                className="offset-xs-3 col-xs-9 lato-N6"
                style={{ textAlign: 'left', marginTop: '3.5rem', paddingLeft: '0.1rem' }}
              >
                Please enter the event details
              </h2>

              <label
                className="col-xs-3 lato-N6 flex align-center justify-end"
                htmlFor="name"
                style={{ textAlign: 'right', textTransform: 'uppercase', color: '#696969', marginTop: '1rem' }}
              >
                Name
              </label>
              <input
                name="name"
                className="col-xs-9 input"
                onChange={this.onChangeEvent}
                value={this.state.event}
                style={{ marginTop: '1rem' }}
              />

              <label
                className="col-xs-3 lato-N6 flex align-center justify-end"
                htmlFor="location"
                style={{ textAlign: 'right', textTransform: 'uppercase', color: '#696969', marginTop: '1rem' }}
              >
                Location
              </label>
              <input
                name="location"
                className="col-xs-9 input"
                onChange={this.onChangeLocation}
                value={this.state.location}
                style={{ marginTop: '1rem' }}
              />

              <label
                className="col-xs-3 lato-N6 flex align-center justify-end"
                htmlFor="date"
                style={{ textAlign: 'right', textTransform: 'uppercase', color: '#696969', marginTop: '1rem' }}
              >
                Date
              </label>
              <input
                name="date"
                type="date"
                className="col-xs-9 input"
                onChange={this.onChangeDate}
                value={this.state.date}
                style={{ marginTop: '1rem' }}
              />

              <div className="flex justify-end" style={{ width: '100%', marginTop: '3rem' }}>
                <select
                  className="deck-select"
                  onChange={this.onChangeLegality}
                  value={this.state.legality}
                  style={{ width: 'auto' }}
                >
                  <option className="deck-select__option" disabled>
                    Format
                  </option>
                  {validLegalities.map((legality) => (
                    <option key={legality} className="deck-select__option" value={legality}>
                      {TextFormat.capitalize(legality)}
                    </option>
                  ))}
                </select>
                <input
                  type="submit"
                  className="button-primary"
                  onClick={this.onClickCreatePDF}
                  value={'Create PDF'}
                  style={{ marginLeft: '1rem' }}
                />
              </div>
            </div>
          </div>
        </form>
      </div>
    );
  }

  private onChangeFirstName = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    evt.preventDefault();
    this.setState({
      firstName: evt.currentTarget.value,
    });
  };

  private onChangeLastName = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    evt.preventDefault();
    this.setState({
      lastName: evt.currentTarget.value,
    });
  };

  private onChangeDCI = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    evt.preventDefault();
    this.setState({
      dci: evt.currentTarget.value,
    });
  };

  private onChangeDesigner = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    evt.preventDefault();
    this.setState({
      designer: evt.currentTarget.value,
    });
  };

  private onChangeEvent = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    evt.preventDefault();
    this.setState({
      event: evt.currentTarget.value,
    });
  };

  private onChangeLocation = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    evt.preventDefault();
    this.setState({
      location: evt.currentTarget.value,
    });
  };

  private onChangeDate = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    evt.preventDefault();
    this.setState({
      date: evt.currentTarget.value,
    });
  };

  private onChangeLegality = (evt: React.SyntheticEvent<HTMLSelectElement>) => {
    evt.preventDefault();
    evt.stopPropagation();
    this.setState({
      legality: evt.currentTarget.value as Legality,
    });
  };

  private onClickCreatePDF = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    evt.stopPropagation();
    track('decks', 'export', 'pdf');
    this.generatePDF();
  };

  private generatePDF = (mainList?: any[], sideList?: any[], pageNo?: number) => {
    const deckbuilder: any = new (Deck2PDF as any)();

    deckbuilder.addEventDetails(
      this.state.date.length > parseString(this.state.date)
        ? this.state.date.slice(0, parseString(this.state.date)) + '...'
        : this.state.date,
      this.state.location.length > parseString(this.state.location)
        ? this.state.location.slice(0, parseString(this.state.location)) + '...'
        : this.state.location,
      this.state.event.length > parseString(this.state.event)
        ? this.state.event.slice(0, parseString(this.state.event)) + '...'
        : this.state.event,
    );

    deckbuilder.addDeckDetails(
      this.props.deck.get('name').length > parseString(this.props.deck.get('name'))
        ? this.props.deck.get('name').slice(0, parseString(this.props.deck.get('name'))) + '...'
        : this.props.deck.get('name'),
      this.state.designer.length > parseString(this.state.designer)
        ? this.state.designer.slice(0, parseString(this.state.designer)) + '...'
        : this.state.designer,
    );

    deckbuilder.addPlayerDetails(
      this.state.firstName.length > parseString(this.state.firstName)
        ? this.state.firstName.slice(0, parseString(this.state.firstName)) + '...'
        : this.state.firstName,
      this.state.lastName.length > parseString(this.state.lastName)
        ? this.state.lastName.slice(0, parseString(this.state.lastName)) + '...'
        : this.state.lastName,
      this.state.dci.length > 10 ? this.state.dci.slice(0, 10) : this.state.dci,
    );

    let cards: any[] = [];
    let excessMain: any[] | undefined = undefined;
    let excessSide: any[] | undefined = undefined;

    const mains = this.props.deck.get('boards').filter((board: DeckBoard) => board.get('name') === 'Main');
    if (mains.size) {
      const main = mains.first();

      if (mainList) {
        // handling leftovers from previous call.
        cards = mainList;
      } else {
        // handle main board
        const cardData: any = {};
        this.props.deck
          .get('deckCards')
          .filter((deckCard: DeckCard) => deckCard.get('boardId') === main.get('id'))
          .forEach((deckCard: DeckCard) => {
            const card = this.props.deck.get('cards').get(deckCard.get('cardId'));
            cardData[card.get('name')] = (cardData[card.get('name')] || 0) + 1;
          });
        for (const name in cardData) {
          cards.push({ count: String(cardData[name]), name: name });
        }
      }

      // split the list over multiple pages if necessary.
      if (cards.length <= 44) {
        deckbuilder.addMainBoard(cards, main.count());
      } else {
        excessMain = cards.slice(44);
        cards = cards.slice(0, 44);
        deckbuilder.addMainBoard(cards, main.count()); // need to check if this should reflect cards in the entire mainboard, or just on this page...
      }
    }

    const sides = this.props.deck.get('boards').filter((board: DeckBoard) => board.get('name') === 'Side');
    if (sides.size) {
      const side = sides.first();

      if (sideList) {
        cards = sideList;
      } else {
        cards = [];
        // handle side board
        const cardData: any = {};
        this.props.deck
          .get('deckCards')
          .filter((deckCard: DeckCard) => deckCard.get('boardId') === side.get('id'))
          .forEach((deckCard: DeckCard) => {
            const card = this.props.deck.get('cards').get(deckCard.get('cardId'));
            cardData[card.get('name')] = (cardData[card.get('name')] || 0) + 1;
          });
        for (const name in cardData) {
          cards.push({ count: String(cardData[name]), name: name });
        }
      }

      if (cards.length <= 15) {
        deckbuilder.addSideBoard(cards, side.count());
      } else {
        excessSide = cards.slice(15);
        cards = cards.slice(0, 15);
        deckbuilder.addSideBoard(cards, side.count()); // need to check, same as above.
      }
    }

    deckbuilder.addTotalCounts();
    try {
      if (excessSide || excessMain) {
        // if we have leftovers, make a new sheet.
        if (pageNo) {
          // not first page
          deckbuilder.downloadPDF(this.props.deck.get('name'), pageNo);
          this.generatePDF(excessMain, excessSide, pageNo + 1);
        } else {
          // first page, add numbering
          deckbuilder.downloadPDF(this.props.deck.get('name'), 1);
          this.generatePDF(excessMain, excessSide, 2);
        }
      } else {
        // no excess, either at end of file list, or first file and less than excess cards
        deckbuilder.downloadPDF(this.props.deck.get('name'), pageNo);
      }
    } catch (e) {
      alert('Could not create pdf, Reason: ' + e);
    }
  };
}

function parseString(data: string, length?: number) {
  // Adjust the maximum length of a string to be put into the pdf sections.
  if (!length) length = 24; // rough estimate.
  for (const c of data) {
    if (c === 'W' || c === 'M' || c === 'w' || c === 'm') {
      // large letters take up more space and so we reduce the maximum length.
      length -= 0.25;
    } else if (c === 'I' || c === 'l' || c === '1') {
      // smaller characters take less space so we can increase the number of characters.
      length += 0.35;
    } else if (c === 'i') {
      length += 0.5; // the character 'i' is particularly small so we can increase the string length significantly.
    }
  }
  // return a whole number value as we cannot have partial values in the textbox.
  return Math.floor(length);
}
