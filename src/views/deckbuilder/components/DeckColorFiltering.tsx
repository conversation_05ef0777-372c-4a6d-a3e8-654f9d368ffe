import * as React from 'react';
import * as DeckActions from '../../../actions/DeckActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { TextFormat } from '../../../helpers/fmt';
import { ColorFilter, ColorOption } from '../../../models/filters/ColorFilter';
import { FilterState, nextFilterState } from '../../../models/filters/FilterState';
import { GateFilterIcon } from '../../components/filters/GateFilterIcon';

interface IProps {
  dispatcher: Dispatcher;
  color: ColorFilter;
}

interface IState {}

export class DeckColorFiltering extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  /**
   * @override
   */
  componentDidMount() {}

  /**
   * @override
   */
  render() {
    return (
      <div className="deck-color-filtering">
        <div className="color-container">
          <div className="color-symbol-container" style={{ margin: '0' }}>
            {ColorFilter.onlyColors().map((color) => (
              <GateFilterIcon
                key={color}
                iconPath={`/svg/mana-${color}.svg`}
                hoverText={TextFormat.capitalizeWord(color as string)}
                filterState={this.props.color.get(color)}
                onClick={() => this.onClickColor(color)}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  private onClickColor = (color: ColorOption) => {
    // If the color is specified in the query, it cannot be changed in the UI
    if (this.props.dispatcher.DeckStore().getState().get('queryColor').get(color) !== FilterState.INACTIVE) {
      return;
    }

    const nextState = nextFilterState(this.props.color.get(color));
    DeckActions.color(this.props.color.set(color, nextState), this.props.dispatcher);
  };
}
