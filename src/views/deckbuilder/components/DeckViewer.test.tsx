import { fireEvent, screen, within } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck, FakeDeckBoard, FakeTag } from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { Arranging } from '../../../models/Arranging';
import { Grouping } from '../../../models/Grouping';
import { OwnershipStatus } from '../Deck';
import { DeckViewer } from './DeckViewer';

// Mock SubscriptionActions
vi.mock('../../../actions/SubscriptionActions', () => ({
  subscription: vi.fn(),
}));

// Mock DeckActions
vi.mock('../../../actions/DeckActions', () => ({
  updateDeck: vi.fn(),
  addTag: vi.fn(),
  removeTag: vi.fn(),
  removeDeck: vi.fn(),
}));

// Mock MessageActions
vi.mock('../../../actions/MessageActions', () => ({
  error: vi.fn(),
}));

// Mock helpers
vi.mock('../../../helpers/ga_helper', () => ({
  default: vi.fn(),
}));

vi.mock('../../../helpers/history', () => ({
  default: {
    push: vi.fn(),
  },
}));

vi.mock('../../../helpers/core_assets', () => ({
  CoreAssets: {
    imageHost: vi.fn(() => 'https://example.com'),
  },
}));

vi.mock('../../../helpers/currency_helper', async (importOriginal) => {
  const actual = (await importOriginal()) as any;
  return {
    ...actual,
    default: vi.fn((amount: number) => `$${amount}`),
  };
});

vi.mock('../../../helpers/serverside', () => ({
  default: vi.fn(() => true), // Return true to avoid requiring DeckToPDF
}));

// Mock DeckToPDF
vi.mock('./DeckToPDF', () => ({
  default: vi.fn(() => ({
    addEventDetails: vi.fn(),
    addDeckDetails: vi.fn(),
    addPlayerDetails: vi.fn(),
    addMainBoard: vi.fn(),
    addSideBoard: vi.fn(),
    addTotalCounts: vi.fn(),
    downloadPDF: vi.fn(),
  })),
}));

type DeckViewerProps = React.ComponentProps<typeof DeckViewer>;

const createMockDeckWithBoards = () => {
  const mainBoard = create(FakeDeckBoard, { id: 1, name: 'Main' });
  const sideBoard = create(FakeDeckBoard, { id: 2, name: 'Sideboard' });

  return create(FakeDeck, {
    name: 'Test Deck',
    uuid: 'test-deck-uuid',
    boards: Immutable.List([mainBoard, sideBoard]),
  });
};

const defaultProps: Omit<DeckViewerProps, 'dispatcher' | 'me'> = {
  accessState: OwnershipStatus.OWNED,
  deck: createMockDeckWithBoards(),
  deckGrouping: Grouping.NONE,
  deckArranging: Arranging.CARD_TYPE,
  onDuplicate: vi.fn(),
};

const renderDeckViewer = (props: Partial<DeckViewerProps> = {}) => {
  return renderWithDispatcher(DeckViewer, {
    ...defaultProps,
    ...props,
  });
};

describe('DeckViewer', () => {
  it('renders the component', () => {
    renderDeckViewer();
    // The component should render without errors
    expect(screen.getByText('Test Deck')).toBeInTheDocument();
  });

  it('renders a default deck image', () => {
    const { container } = renderDeckViewer();
    const deckImageEl = container.querySelector('.deck-tile__image-wrapper__image');
    expect(deckImageEl).toBeInTheDocument();
    expect(deckImageEl).toHaveStyle('background-image: url(/images/deck-avatar-default.jpg)');
  });

  it('renders a custom deck image', () => {
    const { container } = renderDeckViewer({
      deck: create(FakeDeck, { image: 'custom-image' }),
    });
    const deckImageEl = container.querySelector('.deck-tile__image-wrapper__image');
    expect(deckImageEl).toBeInTheDocument();
    expect(deckImageEl).toHaveStyle('background-image: url(https://example.com/card_art_hq/custom-image.jpg)');
  });

  it('displays the statistics section', () => {
    const { container } = renderDeckViewer();
    expect(container.querySelector('.deck-statistics')).toBeInTheDocument();
  });

  it('renders deck legalities component', () => {
    renderDeckViewer();
    expect(screen.getByText('Legality')).toBeInTheDocument();
  });

  it('renders DeckBuilderGrouping component', () => {
    renderDeckViewer();
    // The grouping section should contain the "Group by" option
    expect(screen.getByRole('option', { name: 'Group by' })).toBeInTheDocument();
  });

  it('renders DeckBuilderArrangement', () => {
    renderDeckViewer();
    // The DeckBuilderArrangement component should be rendered
    expect(screen.getByRole('option', { name: 'Arrange by' })).toBeInTheDocument();
  });

  it('renders deck with multiple boards', () => {
    const board1 = create(FakeDeckBoard, { id: 1, name: 'Main' });
    const board2 = create(FakeDeckBoard, { id: 2, name: 'Sideboard' });
    const board3 = create(FakeDeckBoard, { id: 3, name: 'Maybeboard' });

    const deck = create(FakeDeck, {
      boards: Immutable.List([board1, board2, board3]),
    });

    renderDeckViewer({ deck });

    // Should render without errors
    expect(screen.getByText(board1.get('name'), { exact: false })).toBeInTheDocument();
    expect(screen.getByText(board2.get('name'), { exact: false })).toBeInTheDocument();
    expect(screen.getByText(board3.get('name'), { exact: false })).toBeInTheDocument();
  });

  describe('as a deck owner', () => {
    test('I can edit the deck name', async () => {
      renderDeckViewer();
      expect(screen.getByText(defaultProps.deck.get('name'))).toBeInTheDocument();
      fireEvent.click(screen.getByText(defaultProps.deck.get('name')));
      // On clicking the span with the deck name an input should appear
      expect(screen.getByDisplayValue(defaultProps.deck.get('name'))).toBeInTheDocument();

      fireEvent.change(screen.getByDisplayValue(defaultProps.deck.get('name')), {
        target: { value: 'New Name' },
      });

      fireEvent.submit(screen.getByDisplayValue('New Name'));
      expect(vi.mocked(DeckActions.updateDeck)).toHaveBeenCalled();
    });

    test('I can make the deck public', () => {
      renderDeckViewer();
      expect(screen.getByRole('checkbox')).toBeInTheDocument();
      fireEvent.click(screen.getByRole('checkbox'));
      expect(vi.mocked(DeckActions.updateDeck)).toHaveBeenCalledOnce();
    });

    test('I can change the deck legality', () => {
      renderDeckViewer();
      expect(screen.getByRole('option', { name: 'Format' })).toBeInTheDocument();
      fireEvent.change(screen.getByRole('option', { name: 'Format' }).parentElement!, {
        target: { value: 'standard' },
      });
      expect(vi.mocked(DeckActions.updateDeck)).toHaveBeenCalledOnce();
    });

    test('I can delete the deck', async () => {
      renderDeckViewer();
      expect(screen.getByText('Delete')).toBeInTheDocument();
      fireEvent.click(screen.getByText('Delete'));
      expect(screen.getByText('Remove')).toBeInTheDocument();
      fireEvent.click(screen.getByText('Remove'));
      expect(vi.mocked(DeckActions.removeDeck)).toHaveBeenCalledOnce();
    });

    test('I can add new tags', () => {
      renderDeckViewer();
      expect(screen.getByPlaceholderText('#add tag')).toBeInTheDocument();
      fireEvent.change(screen.getByPlaceholderText('#add tag'), {
        target: { value: '#newtag' },
      });
      fireEvent.keyDown(screen.getByPlaceholderText('#add tag'), { keyCode: 13 });
      expect(vi.mocked(DeckActions.addTag)).toHaveBeenCalledOnce();
    });

    test('I can delete tags', () => {
      const mockTag = create(FakeTag);
      const mockDeckWithTag = create(FakeDeck, {
        tags: Immutable.Map({
          'red-deck': mockTag,
        }),
      });
      renderDeckViewer({
        deck: mockDeckWithTag,
      });
      expect(screen.getByText(`#${mockTag.get('name')}`)).toBeInTheDocument();
      const tagEl = screen.getByText(`#${mockTag.get('name')}`);
      fireEvent.click(within(tagEl).getByRole('img', { hidden: true }).parentElement!);
      expect(vi.mocked(DeckActions.removeTag)).toHaveBeenCalledOnce();
    });

    test('I can share my public deck', () => {
      const mockPublicDeck = create(FakeDeck, { isPublic: true });
      renderDeckViewer({
        deck: mockPublicDeck,
      });
      // The DeckShareLink component should be rendered in the main area
      expect(screen.getByRole('link')).toBeInTheDocument();
      expect(screen.getByRole('link')).toHaveTextContent(mockPublicDeck.get('uuid'));
    });
  });

  describe('as a non-deck owner', () => {
    test('I cannot edit the deck name', () => {
      renderDeckViewer({
        accessState: OwnershipStatus.NOT_OWNED,
      });
      expect(screen.queryByText(defaultProps.deck.get('name'))).toBeInTheDocument();
      fireEvent.click(screen.getByText(defaultProps.deck.get('name')));
      // On clicking the span with the deck name an input should not appear
      expect(screen.queryByDisplayValue(defaultProps.deck.get('name'))).not.toBeInTheDocument();
    });

    test('I cannot delete the deck', () => {
      renderDeckViewer({
        accessState: OwnershipStatus.NOT_OWNED,
      });
      expect(screen.queryByText('Delete')).not.toBeInTheDocument();
    });

    test('I can duplicate the deck', () => {
      renderDeckViewer({
        accessState: OwnershipStatus.NOT_OWNED,
      });
      expect(screen.getByRole('button', { name: 'Duplicate' })).toBeInTheDocument();
    });

    test('I can purchase the deck', () => {
      renderDeckViewer({
        accessState: OwnershipStatus.NOT_OWNED,
      });
      expect(screen.getByRole('button', { name: 'Purchase Deck' })).toBeInTheDocument();
    });

    test('I cannot edit tags', () => {
      renderDeckViewer({
        accessState: OwnershipStatus.NOT_OWNED,
      });
      expect(screen.queryByPlaceholderText('#add tag')).not.toBeInTheDocument();
    });
  });

  test('deck group by can be preselected', () => {
    renderDeckViewer({ deckGrouping: Grouping.NAME });
    expect(screen.getByRole('option', { name: 'Name', selected: true })).toBeInTheDocument();
  });

  test('deck arrange by can be preselected', () => {
    renderDeckViewer({ deckArranging: Arranging.MANA_COST });
    expect(screen.getByRole('option', { name: 'Mana Cost', selected: true })).toBeInTheDocument();
  });

  it('calls onDuplicate when duplicate action is triggered', () => {
    const onDuplicate = vi.fn();
    renderDeckViewer({ onDuplicate });
    const duplicateBtn = screen.getByRole('button', { name: 'Duplicate' });
    expect(duplicateBtn).toBeInTheDocument();
    fireEvent.click(duplicateBtn);
    expect(onDuplicate).toHaveBeenCalled();
  });
});
