import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { CoreAssets } from '../../../helpers/core_assets';
import history from '../../../helpers/history';
import { Deck } from '../../../models/Decks';

interface IProps {
  dispatcher: Dispatcher;
  deck: Deck;
}

interface IState {}

export const DeckTile = class extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  /**
   * @override
   */
  componentDidMount() {}

  /**
   * @override
   */
  render() {
    return (
      <div className="deck-tile" onClick={this.onClick}>
        <div className="deck-tile__image-wrapper">
          <div
            className="deck-tile__image-wrapper__image"
            style={{
              backgroundImage:
                this.props.deck.get('image') && this.props.deck.get('image') !== 'null'
                  ? `url(${CoreAssets.imageHost()}/card_art_hq/${this.props.deck.get('image')}.jpg)`
                  : 'url(/images/deck-avatar-default.jpg)',
            }}
          />
        </div>
        <div className="deck-tile__colors">
          <div className={`deck-tile__colors__color is-white ${this.props.deck.get('colorWhite') ? 'on' : ''}`} />
          <div className={`deck-tile__colors__color is-blue ${this.props.deck.get('colorBlue') ? 'on' : ''}`} />
          <div className={`deck-tile__colors__color is-black ${this.props.deck.get('colorBlack') ? 'on' : ''}`} />
          <div className={`deck-tile__colors__color is-red ${this.props.deck.get('colorRed') ? 'on' : ''}`} />
          <div className={`deck-tile__colors__color is-green ${this.props.deck.get('colorGreen') ? 'on' : ''}`} />
        </div>
        <span className="deck-tile__name">{this.props.deck.get('name')}</span>
      </div>
    );
  }

  private onClick = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    history.push(`/decks/${this.props.deck.get('uuid')}`);
  };
};
