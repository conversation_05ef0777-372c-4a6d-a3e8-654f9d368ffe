import { fireEvent, screen } from '@testing-library/react';
import { render } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck, FakeDeckBoard, FakeDeckCard } from '../../../../tests/fake/FakeCardData';
import { Card } from '../../../models/Cards';
import { DeckBoard, DeckCard } from '../../../models/Decks';
import { Legality } from '../../../models/Legality';
import { DeckBuilderPDFForm } from './DeckBuilderPDFForm';

// Mock dependencies
vi.mock('../../../helpers/ga_helper', () => ({
  default: vi.fn(),
}));

vi.mock('../../../helpers/serverside', () => ({
  default: vi.fn(() => false),
}));

vi.mock('../../../helpers/fmt', () => ({
  TextFormat: {
    capitalize: vi.fn((str: string) => str.charAt(0).toUpperCase() + str.slice(1)),
  },
}));

vi.mock('../../../lib/deckbuilder/validators/DeckValidator', () => ({
  Validator: vi.fn(() => ({
    check: vi.fn(() => []), // Return no errors by default
  })),
}));

// Mock DeckToPDF
const mockDeckToPDF = {
  addEventDetails: vi.fn(),
  addDeckDetails: vi.fn(),
  addPlayerDetails: vi.fn(),
  addMainBoard: vi.fn(),
  addSideBoard: vi.fn(),
  addTotalCounts: vi.fn(),
  downloadPDF: vi.fn(),
};

vi.mock('./DeckToPDF', () => vi.fn(() => mockDeckToPDF));

type DeckBuilderPDFFormProps = React.ComponentProps<typeof DeckBuilderPDFForm>;

const createMockCard = (name: string = 'Lightning Bolt'): Card => {
  return new Card({
    id: 1,
    name,
  });
};

const createMockDeckWithCards = () => {
  const mainBoard = create(FakeDeckBoard, { id: 1, name: 'Main' });
  const sideBoard = create(FakeDeckBoard, { id: 2, name: 'Side' });
  const deckCard1 = create(FakeDeckCard, { id: 1, cardId: 1, boardId: 1 });
  const deckCard2 = create(FakeDeckCard, { id: 2, cardId: 2, boardId: 2 });
  const card1 = createMockCard('Lightning Bolt');
  const card2 = createMockCard('Counterspell');

  return create(FakeDeck, {
    name: 'Test Deck',
    legality: Legality.STANDARD,
    boards: Immutable.List([mainBoard, sideBoard]),
    deckCards: Immutable.Map({ 1: deckCard1, 2: deckCard2 }),
    cards: Immutable.Map({ 1: card1, 2: card2 }),
  });
};

const defaultProps: DeckBuilderPDFFormProps = {
  deck: createMockDeckWithCards(),
};

const renderDeckBuilderPDFForm = (props: Partial<DeckBuilderPDFFormProps> = {}) => {
  return render(<DeckBuilderPDFForm {...defaultProps} {...props} />);
};

describe('DeckBuilderPDFForm', () => {
  beforeEach(() => {
    Object.values(mockDeckToPDF).forEach(mock => mock.mockClear());
  });

  describe('when component renders', () => {
    it('displays the form container', () => {
      const { container } = renderDeckBuilderPDFForm();
      expect(container.querySelector('form')).toBeInTheDocument();
    });

    it('displays personal details section', () => {
      renderDeckBuilderPDFForm();
      expect(screen.getByText('Please enter the following personal details')).toBeInTheDocument();
    });

    it('displays event details section', () => {
      renderDeckBuilderPDFForm();
      expect(screen.getByText('Please enter the event details')).toBeInTheDocument();
    });

    it('displays all input fields', () => {
      renderDeckBuilderPDFForm();
      
      expect(screen.getByLabelText('First name')).toBeInTheDocument();
      expect(screen.getByLabelText('Last name')).toBeInTheDocument();
      expect(screen.getByLabelText('DCI')).toBeInTheDocument();
      expect(screen.getByLabelText('Designer')).toBeInTheDocument();
      expect(screen.getByLabelText('Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Location')).toBeInTheDocument();
      expect(screen.getByLabelText('Date')).toBeInTheDocument();
    });

    it('displays format selector', () => {
      renderDeckBuilderPDFForm();
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    it('displays create PDF button', () => {
      renderDeckBuilderPDFForm();
      expect(screen.getByRole('button', { name: 'Create PDF' })).toBeInTheDocument();
    });
  });

  describe('form inputs', () => {
    it('updates first name when input changes', () => {
      renderDeckBuilderPDFForm();
      const input = screen.getByLabelText('First name');
      
      fireEvent.change(input, { target: { value: 'John' } });
      
      expect(input).toHaveValue('John');
    });

    it('updates last name when input changes', () => {
      renderDeckBuilderPDFForm();
      const input = screen.getByLabelText('Last name');
      
      fireEvent.change(input, { target: { value: 'Doe' } });
      
      expect(input).toHaveValue('Doe');
    });

    it('updates DCI when input changes', () => {
      renderDeckBuilderPDFForm();
      const input = screen.getByLabelText('DCI');
      
      fireEvent.change(input, { target: { value: '1234567890' } });
      
      expect(input).toHaveValue('1234567890');
    });

    it('updates designer when input changes', () => {
      renderDeckBuilderPDFForm();
      const input = screen.getByLabelText('Designer');
      
      fireEvent.change(input, { target: { value: 'Jane Smith' } });
      
      expect(input).toHaveValue('Jane Smith');
    });

    it('updates event name when input changes', () => {
      renderDeckBuilderPDFForm();
      const input = screen.getByLabelText('Name');
      
      fireEvent.change(input, { target: { value: 'Friday Night Magic' } });
      
      expect(input).toHaveValue('Friday Night Magic');
    });

    it('updates location when input changes', () => {
      renderDeckBuilderPDFForm();
      const input = screen.getByLabelText('Location');
      
      fireEvent.change(input, { target: { value: 'Local Game Store' } });
      
      expect(input).toHaveValue('Local Game Store');
    });

    it('updates date when input changes', () => {
      renderDeckBuilderPDFForm();
      const input = screen.getByLabelText('Date');
      
      fireEvent.change(input, { target: { value: '2023-12-01' } });
      
      expect(input).toHaveValue('2023-12-01');
    });

    it('updates legality when selection changes', () => {
      renderDeckBuilderPDFForm();
      const select = screen.getByRole('combobox');
      
      fireEvent.change(select, { target: { value: Legality.MODERN } });
      
      expect(select).toHaveValue(Legality.MODERN);
    });
  });

  describe('PDF generation', () => {
    it('generates PDF when form is submitted', () => {
      renderDeckBuilderPDFForm();
      const form = screen.getByRole('form') || document.querySelector('form');
      
      if (form) {
        fireEvent.submit(form);
        
        expect(mockDeckToPDF.addEventDetails).toHaveBeenCalled();
        expect(mockDeckToPDF.addDeckDetails).toHaveBeenCalled();
        expect(mockDeckToPDF.addPlayerDetails).toHaveBeenCalled();
        expect(mockDeckToPDF.addTotalCounts).toHaveBeenCalled();
        expect(mockDeckToPDF.downloadPDF).toHaveBeenCalled();
      }
    });

    it('generates PDF when button is clicked', () => {
      renderDeckBuilderPDFForm();
      const button = screen.getByRole('button', { name: 'Create PDF' });
      
      fireEvent.click(button);
      
      expect(mockDeckToPDF.addEventDetails).toHaveBeenCalled();
      expect(mockDeckToPDF.addDeckDetails).toHaveBeenCalled();
      expect(mockDeckToPDF.addPlayerDetails).toHaveBeenCalled();
      expect(mockDeckToPDF.addTotalCounts).toHaveBeenCalled();
      expect(mockDeckToPDF.downloadPDF).toHaveBeenCalled();
    });

    it('passes correct event details to PDF generator', () => {
      renderDeckBuilderPDFForm();
      
      // Fill in form data
      fireEvent.change(screen.getByLabelText('Date'), { target: { value: '2023-12-01' } });
      fireEvent.change(screen.getByLabelText('Location'), { target: { value: 'Test Location' } });
      fireEvent.change(screen.getByLabelText('Name'), { target: { value: 'Test Event' } });
      
      const button = screen.getByRole('button', { name: 'Create PDF' });
      fireEvent.click(button);
      
      expect(mockDeckToPDF.addEventDetails).toHaveBeenCalledWith(
        '2023-12-01',
        'Test Location',
        'Test Event'
      );
    });

    it('passes correct deck details to PDF generator', () => {
      renderDeckBuilderPDFForm();
      
      fireEvent.change(screen.getByLabelText('Designer'), { target: { value: 'Test Designer' } });
      
      const button = screen.getByRole('button', { name: 'Create PDF' });
      fireEvent.click(button);
      
      expect(mockDeckToPDF.addDeckDetails).toHaveBeenCalledWith(
        'Test Deck',
        'Test Designer'
      );
    });

    it('passes correct player details to PDF generator', () => {
      renderDeckBuilderPDFForm();
      
      fireEvent.change(screen.getByLabelText('First name'), { target: { value: 'John' } });
      fireEvent.change(screen.getByLabelText('Last name'), { target: { value: 'Doe' } });
      fireEvent.change(screen.getByLabelText('DCI'), { target: { value: '1234567890' } });
      
      const button = screen.getByRole('button', { name: 'Create PDF' });
      fireEvent.click(button);
      
      expect(mockDeckToPDF.addPlayerDetails).toHaveBeenCalledWith(
        'John',
        'Doe',
        '1234567890'
      );
    });
  });

  describe('component state', () => {
    it('initializes with deck legality', () => {
      const deck = create(FakeDeck, { legality: Legality.MODERN });
      renderDeckBuilderPDFForm({ deck });
      
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Legality.MODERN);
    });

    it('initializes with empty form fields', () => {
      renderDeckBuilderPDFForm();
      
      expect(screen.getByLabelText('First name')).toHaveValue('');
      expect(screen.getByLabelText('Last name')).toHaveValue('');
      expect(screen.getByLabelText('DCI')).toHaveValue('');
      expect(screen.getByLabelText('Designer')).toHaveValue('');
      expect(screen.getByLabelText('Name')).toHaveValue('');
      expect(screen.getByLabelText('Location')).toHaveValue('');
      expect(screen.getByLabelText('Date')).toHaveValue('');
    });
  });

  describe('event handling', () => {
    it('prevents default on form submission', () => {
      renderDeckBuilderPDFForm();
      const form = screen.getByRole('form') || document.querySelector('form');
      
      if (form) {
        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
        const preventDefaultSpy = vi.spyOn(submitEvent, 'preventDefault');
        
        fireEvent(form, submitEvent);
        
        expect(preventDefaultSpy).toHaveBeenCalled();
      }
    });

    it('prevents default on all input changes', () => {
      renderDeckBuilderPDFForm();
      const input = screen.getByLabelText('First name');
      
      const changeEvent = new Event('change', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(changeEvent, 'preventDefault');
      
      fireEvent(input, changeEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });
});
