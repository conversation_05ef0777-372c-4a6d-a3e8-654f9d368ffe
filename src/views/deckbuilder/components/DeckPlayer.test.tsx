import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck, FakeDeckBoard, FakeDeckCard } from '../../../../tests/fake/FakeCardData';
import { FakeUser } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { Card } from '../../../models/Cards';
import { DeckBoard, DeckCard } from '../../../models/Decks';
import { DeckPlayer } from './DeckPlayer';

// Mock track helper
vi.mock('../../../helpers/ga_helper', () => ({
  default: vi.fn(),
}));

// Mock deck calculation functions
vi.mock('../../../models/Decks', async () => {
  const actual = await vi.importActual('../../../models/Decks');
  return {
    ...actual,
    calculateTurnProbabilities: vi.fn().mockReturnValue(
      Immutable.Map({
        'Lightning Bolt': 0.4,
        'Mountain': 0.3,
        'Shock': 0.3,
      }),
    ),
    findDeckBoardByName: vi.fn().mockReturnValue(new DeckBoard({ id: 1, name: 'Main' })),
  };
});

type DeckPlayerProps = React.ComponentProps<typeof DeckPlayer>;

const createMockCard = (name: string = 'Lightning Bolt', types: string[] = ['Instant']): Card => {
  return new Card({
    id: 1,
    name,
    types: Immutable.List(types),
  });
};

const createMockDeckWithCards = () => {
  const mainBoard = create(FakeDeckBoard, { id: 1, name: 'Main' });
  const deckCard1 = create(FakeDeckCard, { id: 1, cardId: 1, boardId: 1 });
  const deckCard2 = create(FakeDeckCard, { id: 2, cardId: 2, boardId: 1 });
  const card1 = createMockCard('Lightning Bolt', ['Instant']);
  const card2 = createMockCard('Mountain', ['Land']);

  return create(FakeDeck, {
    boards: Immutable.List([mainBoard]),
    deckCards: Immutable.Map({ 1: deckCard1, 2: deckCard2 }),
    cards: Immutable.Map({ 1: card1, 2: card2 }),
  });
};

const defaultProps: Omit<DeckPlayerProps, 'dispatcher' | 'me'> = {
  deck: createMockDeckWithCards(),
};

const renderDeckPlayer = (props: Partial<DeckPlayerProps> = {}) => {
  return renderWithDispatcher(DeckPlayer, { 
    ...defaultProps, 
    me: create(FakeUser),
    ...props 
  });
};

describe('DeckPlayer', () => {
  describe('when component renders', () => {
    it('displays the playtest container', () => {
      const { container } = renderDeckPlayer();
      expect(container.querySelector('.playtest')).toBeInTheDocument();
    });

    it('displays battlefield section', () => {
      const { container } = renderDeckPlayer();
      expect(container.querySelector('.playtest__battlefield')).toBeInTheDocument();
    });

    it('displays graveyard section', () => {
      const { container } = renderDeckPlayer();
      expect(container.querySelector('.playtest__graveyard')).toBeInTheDocument();
    });

    it('displays hand section', () => {
      const { container } = renderDeckPlayer();
      expect(container.querySelector('.playtest__hand')).toBeInTheDocument();
    });

    it('shows placeholder when battlefield is empty', () => {
      renderDeckPlayer();
      expect(screen.getByText('No cards on Battlefield')).toBeInTheDocument();
    });

    it('shows placeholder when graveyard is empty', () => {
      renderDeckPlayer();
      expect(screen.getByText('No cards in Graveyard')).toBeInTheDocument();
    });

    it('shows placeholder when hand is empty', () => {
      renderDeckPlayer();
      expect(screen.getByText('No cards in Hand')).toBeInTheDocument();
    });
  });

  describe('game controls', () => {
    it('displays New Game button', () => {
      renderDeckPlayer();
      expect(screen.getByRole('button', { name: 'New Game' })).toBeInTheDocument();
    });

    it('displays Next Turn button', () => {
      renderDeckPlayer();
      expect(screen.getByRole('button', { name: 'Next Turn' })).toBeInTheDocument();
    });

    it('displays Draw Card button', () => {
      renderDeckPlayer();
      expect(screen.getByRole('button', { name: 'Draw Card' })).toBeInTheDocument();
    });

    it('displays Shuffle button', () => {
      renderDeckPlayer();
      expect(screen.getByRole('button', { name: 'Shuffle' })).toBeInTheDocument();
    });

    it('displays Mulligan button on turn 1', () => {
      renderDeckPlayer();
      expect(screen.getByRole('button', { name: 'Mulligan' })).toBeInTheDocument();
    });

    it('displays turn number', () => {
      renderDeckPlayer();
      expect(screen.getByText('Turn number: 1')).toBeInTheDocument();
    });

    it('displays cards in hand count', () => {
      renderDeckPlayer();
      expect(screen.getByText(/Cards in hand: \d+/)).toBeInTheDocument();
    });
  });

  describe('tutor functionality', () => {
    it('displays tutor input field', () => {
      renderDeckPlayer();
      expect(screen.getByPlaceholderText('Tutor a card from your deck')).toBeInTheDocument();
    });

    it('displays Tutor button', () => {
      renderDeckPlayer();
      expect(screen.getByRole('button', { name: 'Tutor' })).toBeInTheDocument();
    });

    it('handles tutor input changes', () => {
      renderDeckPlayer();
      const tutorInput = screen.getByPlaceholderText('Tutor a card from your deck');
      
      fireEvent.change(tutorInput, { target: { value: 'Lightning' } });
      
      expect(tutorInput).toHaveValue('Lightning');
    });

    it('submits tutor form when button is clicked', () => {
      renderDeckPlayer();
      const tutorButton = screen.getByRole('button', { name: 'Tutor' });
      
      fireEvent.click(tutorButton);
      
      // Should not throw an error
      expect(tutorButton).toBeInTheDocument();
    });
  });

  describe('user interactions', () => {
    it('handles New Game button click', () => {
      renderDeckPlayer();
      const newGameButton = screen.getByRole('button', { name: 'New Game' });
      
      fireEvent.click(newGameButton);
      
      // Should reset the game state
      expect(screen.getByText('Turn number: 1')).toBeInTheDocument();
    });

    it('handles Next Turn button click', () => {
      renderDeckPlayer();
      const nextTurnButton = screen.getByRole('button', { name: 'Next Turn' });
      
      fireEvent.click(nextTurnButton);
      
      // Turn number should increment
      expect(screen.getByText('Turn number: 2')).toBeInTheDocument();
    });

    it('handles Draw Card button click', () => {
      renderDeckPlayer();
      const drawCardButton = screen.getByRole('button', { name: 'Draw Card' });
      
      fireEvent.click(drawCardButton);
      
      // Should not throw an error
      expect(drawCardButton).toBeInTheDocument();
    });

    it('handles Shuffle button click', () => {
      renderDeckPlayer();
      const shuffleButton = screen.getByRole('button', { name: 'Shuffle' });
      
      fireEvent.click(shuffleButton);
      
      // Should not throw an error
      expect(shuffleButton).toBeInTheDocument();
    });

    it('handles Mulligan button click', () => {
      renderDeckPlayer();
      const mulliganButton = screen.getByRole('button', { name: 'Mulligan' });
      
      fireEvent.click(mulliganButton);
      
      // Should not throw an error
      expect(mulliganButton).toBeInTheDocument();
    });
  });

  describe('when deck changes', () => {
    it('updates state when deck prop changes', () => {
      const initialDeck = createMockDeckWithCards();
      const { rerenderWithDispatcher } = renderDeckPlayer({ deck: initialDeck });
      
      const newDeck = createMockDeckWithCards();
      rerenderWithDispatcher({ deck: newDeck, me: create(FakeUser) });
      
      // Component should re-render without errors
      expect(screen.getByText('Turn number: 1')).toBeInTheDocument();
    });
  });

  describe('statistics table', () => {
    it('displays statistics table when draw pile has cards', () => {
      renderDeckPlayer();
      
      // The statistics table should be present if there are cards in the draw pile
      // This is tested indirectly by checking the component renders without errors
      expect(screen.getByRole('button', { name: 'New Game' })).toBeInTheDocument();
    });
  });

  describe('component lifecycle', () => {
    it('initializes state from props on construction', () => {
      renderDeckPlayer();
      
      // Component should initialize properly
      expect(screen.getByText('Turn number: 1')).toBeInTheDocument();
    });

    it('has empty componentDidMount implementation', () => {
      // This test ensures the component mounts without errors
      const { container } = renderDeckPlayer();
      expect(container.querySelector('.playtest')).toBeInTheDocument();
    });
  });
});
