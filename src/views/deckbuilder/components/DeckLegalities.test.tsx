import { render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck } from '../../../../tests/fake/FakeCardData';
import { BaseValidator } from '../../../lib/deckbuilder/validators/BaseValidator';
import { Legality, LegalityKey } from '../../../models/Legality';
import { DeckLegalities } from './DeckLegalities';

// Mock the Validator
vi.mock('../../../lib/deckbuilder/validators/DeckValidator', () => ({
  Validator: vi.fn(),
}));

// Mock TextFormat
vi.mock('../../../helpers/fmt', () => ({
  TextFormat: {
    capitalize: vi.fn((str: string) => str.charAt(0).toUpperCase() + str.slice(1)),
  },
}));

import { TextFormat } from '../../../helpers/fmt';
import { Validator } from '../../../lib/deckbuilder/validators/DeckValidator';
const mockValidator = vi.mocked(Validator);

type DeckLegalitiesProps = React.ComponentProps<typeof DeckLegalities>;

const defaultProps: DeckLegalitiesProps = {
  deck: create(FakeDeck),
  isBuilding: false,
};

const renderDeckLegalities = (props: Partial<DeckLegalitiesProps> = {}) => {
  return render(<DeckLegalities {...defaultProps} {...props} />);
};

describe('DeckLegalities', () => {
  beforeEach(() => {
    // Setup default mock validator that returns no errors
    mockValidator.mockReturnValue({
      check: vi.fn().mockReturnValue([]),
      checkMainBoardHasAtLeast60Cards: vi.fn().mockReturnValue([]),
      checkSideBoardHasAtMost15Cards: vi.fn().mockReturnValue([]),
      checkCopyLimit: vi.fn().mockReturnValue([]),
      checkAllLegal: vi.fn().mockReturnValue([]),
    } as BaseValidator);
  });

  describe('when deck is legal in all formats', () => {
    it('renders the deck legalities', () => {
      renderDeckLegalities();
      Object.keys(Legality).forEach((legality) => {
        expect(
          screen.getByText(`Legal in ${TextFormat.capitalize(Legality[legality as LegalityKey])}`),
        ).toBeInTheDocument();
      });
    });

    it('calls validator for each legality format', () => {
      const deck = create(FakeDeck);
      renderDeckLegalities({ deck, isBuilding: true });

      // Test specific legalities instead of using Object.values
      const legalities = Object.keys(Legality).map((legality) => Legality[legality as LegalityKey]);
      legalities.forEach((legality: Legality) => {
        expect(mockValidator).toHaveBeenCalledWith(legality);
      });
    });
  });

  describe('when deck has legality errors', () => {
    beforeEach(() => {
      // Setup mock validator to return errors for some formats
      mockValidator.mockImplementation(
        (legality: Legality) =>
          ({
            check: vi.fn().mockReturnValue(['foo', 'bar']),
            checkMainBoardHasAtLeast60Cards: vi.fn().mockReturnValue([]),
            checkSideBoardHasAtMost15Cards: vi.fn().mockReturnValue([]),
            checkCopyLimit: vi.fn().mockReturnValue([]),
            checkAllLegal: vi.fn().mockReturnValue([]),
          } as BaseValidator),
      );
    });

    it('displays "Not legal" for formats with errors', () => {
      renderDeckLegalities();

      Object.keys(Legality).forEach((legality) => {
        console.log(`Not Legal in ${TextFormat.capitalize(Legality[legality as LegalityKey])}`);
        expect(
          screen.getByText(`Not Legal in ${TextFormat.capitalize(Legality[legality as LegalityKey])}`, {
            exact: false,
          }),
        ).toBeInTheDocument();
      });
    });
  });
});
