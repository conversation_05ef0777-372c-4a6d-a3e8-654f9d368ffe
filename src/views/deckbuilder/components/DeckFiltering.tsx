import fact_check from '@iconify/icons-ic/baseline-fact-check';
import * as React from 'react';
import * as DeckActions from '../../../actions/DeckActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { TextFormat } from '../../../helpers/fmt';
import { Legality, type LegalityKey } from '../../../models/Legality';
import { IconSelect } from '../../components/IconSelect';

interface IProps {
  dispatcher: Dispatcher;
  legality: Legality;
}

interface IState {}

export class DeckFiltering extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  /**
   * @override
   */
  componentDidMount() {}

  /**
   * @override
   */
  render() {
    return (
      <IconSelect
        className="icon-container--deck"
        icon={fact_check}
        value={this.props.legality}
        onChange={this.onChange}
      >
        <>
          <option className="deck-select__option" disabled>
            Format
          </option>
          <option className="deck-select__option">All</option>
          {Object.keys(Legality).map((key) => (
            <option key={key} className="deck-select__option" value={Legality[key as LegalityKey]}>
              {TextFormat.capitalize(Legality[key as LegalityKey])}
            </option>
          ))}
        </>
      </IconSelect>
    );
  }

  private onChange = (evt: React.SyntheticEvent<HTMLSelectElement>) => {
    evt.preventDefault();
    const value = evt.currentTarget.value === 'All' ? undefined : (evt.currentTarget.value as Legality);
    DeckActions.legality(this.props.dispatcher, value);
  };
}
