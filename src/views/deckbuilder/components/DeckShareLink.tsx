import open_in_new from '@iconify/icons-ic/open-in-new';
import { Icon } from '@iconify/react';
import * as React from 'react';

interface IProps {
  href: string;
  urlText: string;
}

interface IState {
  copyText: string;
}

// DeckShareLink doesn't function when running on localhost.
export class DeckShareLink extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);

    this.state = {
      copyText: 'Copy URL',
    };
  }

  render() {
    return (
      <>
        <div className="col-xs-12 col-md-9">
          <div className="deckbuilder-link-container">
            <a href={this.props.href} target="_blank">
              {this.props.urlText}
              <Icon height={'15px'} width={'15px'} icon={open_in_new} />
            </a>
          </div>
        </div>
        <div className="col-xs-12 col-md-3">
          <button id="share-button" className="button-primary" onClick={this.onClickCopy.bind(this)}>
            {this.state.copyText}
          </button>
        </div>
      </>
    );
  }

  private onClickCopy = (evt: React.SyntheticEvent<HTMLElement>) => {
    navigator.clipboard.writeText(this.props.urlText);
    evt.currentTarget.blur();
    this.setState({ copyText: 'Copied' });
  };
}
