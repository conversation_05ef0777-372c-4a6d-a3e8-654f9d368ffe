import classNames from 'classnames';
import { Map } from 'immutable';
import * as React from 'react';
import { CellBorders } from '../../../models/CellBorder';
import { Deck } from '../../../models/Decks';

interface IDeckColorProps {
  deck: Deck;
  dark: boolean;
  onClick: (deck: Deck) => void;
  cellBorders: CellBorders;
}

export function DeckColorCell(props: IDeckColorProps): JSX.Element {
  const colors = Map<string, boolean>({
    'is-white': props.deck.get('colorWhite'),
    'is-blue': props.deck.get('colorBlue'),
    'is-black': props.deck.get('colorBlack'),
    'is-red': props.deck.get('colorRed'),
    'is-green': props.deck.get('colorGreen'),
  });
  const className = classNames({
    'flex justify-center table__cell': true,
    'cell-border-top': props.cellBorders.get('top'),
    'cell-border-left': props.cellBorders.get('left'),
    'cell-border-right': props.cellBorders.get('right'),
    'cell-border-bottom': props.cellBorders.get('bottom'),
    cell__dark: props.dark,
  });

  if (colors.every((color: boolean) => !color)) {
    return (
      <div className={className} onClick={() => props.onClick(props.deck)} style={{ cursor: 'pointer' }}>
        <p>-</p>
      </div>
    );
  } else {
    return (
      <div className={className} onClick={() => props.onClick(props.deck)} style={{ cursor: 'pointer' }}>
        {colors
          .map((color: boolean, key: string) => (
            <div
              key={key}
              className={classNames('deck-tile__colors__color', key, {
                on: color,
              })}
            />
          ))
          .valueSeq()}
      </div>
    );
  }
}
