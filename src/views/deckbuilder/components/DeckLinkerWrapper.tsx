import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { Deck, DeckCard } from '../../../models/Decks';
import { User } from '../../../models/Users';
import { Dialog, DialogSize } from '../../components/Dialog';
import { DeckLinker } from './DeckLinker';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  deck: Deck;
  deckCards: Immutable.List<DeckCard>;
  isOpen: boolean;
  onDismiss?: (evt: React.SyntheticEvent<HTMLElement>) => void;
}

interface IState {}

export class DeckLinkerWrapper extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  public render() {
    if (!this.props.isOpen) {
      return null;
    }
    return (
      <Dialog
        className="dialog-left-align"
        isOpen={this.props.isOpen}
        onDismiss={this.onDismiss.bind(this)}
        size={DialogSize.LARGE_DYNAMIC}
      >
        <DeckLinker
          dispatcher={this.props.dispatcher}
          me={this.props.me}
          deck={this.props.deck}
          deckCards={this.props.deckCards}
        />
      </Dialog>
    );
  }

  private onDismiss(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    evt.stopPropagation();
    this.props.onDismiss && this.props.onDismiss(evt);
  }
}
