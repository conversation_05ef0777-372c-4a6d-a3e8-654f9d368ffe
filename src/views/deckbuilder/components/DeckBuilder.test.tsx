import { screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck, FakeDeckBoard } from '../../../../tests/fake/FakeCardData';
import { FakeUser } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { Arranging } from '../../../models/Arranging';
import { Grouping } from '../../../models/Grouping';
import { PricingSource } from '../../../models/PricingSource';
import { DeckBuilder } from './DeckBuilder';

type DeckBuilderProps = React.ComponentProps<typeof DeckBuilder>;

const createMockDeckWithBoards = () => {
  const mainBoard = create(FakeDeckBoard, { id: 1, name: 'Main' });
  const sideBoard = create(FakeDeckBoard, { id: 2, name: 'Sideboard' });

  return create(FakeDeck, {
    name: 'Test Deck',
    boards: Immutable.List([mainBoard, sideBoard]),
  });
};

const defaultProps: Omit<DeckBuilderProps, 'dispatcher' | 'me'> = {
  deck: createMockDeckWithBoards(),
  deckGrouping: Grouping.NONE,
  deckArranging: Arranging.NONE,
};

const renderDeckBuilder = (props: Partial<DeckBuilderProps> = {}) => {
  return renderWithDispatcher(DeckBuilder, {
    ...defaultProps,
    me: create(FakeUser, {
      preferences: Immutable.Map({
        pricing: Immutable.Map({
          source: PricingSource.CARD_KINGDOM,
        }),
      }),
    }),
    ...props,
  });
};

describe('DeckBuilder', () => {
  describe('when component renders', () => {
    it('displays the main section container', () => {
      const { container } = renderDeckBuilder();
      expect(container.querySelector('.section')).toBeInTheDocument();
    });

    it('renders the navbar component', () => {
      renderDeckBuilder();
      // The Navbar component should be rendered
      // We can't easily test its content without mocking it, but we can ensure it doesn't crash
      expect(screen.getByRole('main') || document.body).toBeInTheDocument();
    });

    it('displays the statistics section', () => {
      renderDeckBuilder();
      expect(screen.getByText('Legality')).toBeInTheDocument();
    });

    it('renders deck builder statistics component', () => {
      const { container } = renderDeckBuilder();
      // The DeckBuilderStatistics component should be rendered
      expect(container.querySelector('.col-xs-3')).toBeInTheDocument();
    });

    it('renders deck legalities component', () => {
      renderDeckBuilder();
      expect(screen.getByText('Legality')).toBeInTheDocument();
    });
  });

  describe('deck builder sections', () => {
    it('renders the deck builder add component', () => {
      const { container } = renderDeckBuilder();
      // The DeckBuilderAdd component should be rendered in the main area
      expect(container.querySelector('.col-xs-9')).toBeInTheDocument();
    });

    it('renders the grouping section', () => {
      const { container } = renderDeckBuilder();
      expect(container.querySelector('.deckbuilder-grouping')).toBeInTheDocument();
    });

    it('renders deck builder grouping component', () => {
      const { container } = renderDeckBuilder();
      // The DeckBuilderGrouping component should be rendered
      expect(container.querySelector('.col-md-4')).toBeInTheDocument();
    });

    it('renders deck builder arrangement component', () => {
      const { container } = renderDeckBuilder();
      // The DeckBuilderArrangement component should be rendered
      const arrangementColumns = container.querySelectorAll('.col-md-4');
      expect(arrangementColumns.length).toBeGreaterThanOrEqual(2);
    });
  });

  describe('deck boards rendering', () => {
    it('renders deck boards from the deck', () => {
      const deck = createMockDeckWithBoards();
      renderDeckBuilder({ deck });

      // The component should render DeckBuilderBoard components for each board
      // This is tested indirectly by ensuring the component renders without errors
      const { container } = renderDeckBuilder({ deck });
      expect(container.querySelector('.col-xs-12')).toBeInTheDocument();
    });

    it('handles deck with multiple boards', () => {
      const board1 = create(FakeDeckBoard, { id: 1, name: 'Main' });
      const board2 = create(FakeDeckBoard, { id: 2, name: 'Sideboard' });
      const board3 = create(FakeDeckBoard, { id: 3, name: 'Maybeboard' });

      const deck = create(FakeDeck, {
        boards: Immutable.List([board1, board2, board3]),
      });

      renderDeckBuilder({ deck });

      // Should render without errors
      expect(screen.getByText('Legality')).toBeInTheDocument();
    });

    it('handles deck with no boards', () => {
      const deck = create(FakeDeck, {
        boards: Immutable.List([]),
      });

      renderDeckBuilder({ deck });

      // Should render without errors
      expect(screen.getByText('Legality')).toBeInTheDocument();
    });
  });

  describe('props handling', () => {
    it('passes deck prop to child components', () => {
      const deck = createMockDeckWithBoards();
      renderDeckBuilder({ deck });

      // The deck should be passed to child components
      // This is tested indirectly by ensuring the component renders without errors
      expect(screen.getByText('Legality')).toBeInTheDocument();
    });

    it('passes deckGrouping prop to grouping component', () => {
      renderDeckBuilder({ deckGrouping: Grouping.CARD_TYPE });

      // The grouping should be passed to the DeckBuilderGrouping component
      // This is tested indirectly by ensuring the component renders without errors
      expect(screen.getByText('Legality')).toBeInTheDocument();
    });

    it('passes deckArranging prop to arrangement component', () => {
      renderDeckBuilder({ deckArranging: Arranging.ALPHABETICAL });

      // The arranging should be passed to the DeckBuilderArrangement component
      // This is tested indirectly by ensuring the component renders without errors
      expect(screen.getByText('Legality')).toBeInTheDocument();
    });

    it('passes pricing source from user preferences', () => {
      const me = create(FakeUser, {
        preferences: Immutable.Map({
          pricing: Immutable.Map({
            source: PricingSource.TCG_PLAYER,
          }),
        }),
      });

      renderDeckBuilder({ me });

      // The pricing source should be passed to the DeckBuilderAdd component
      // This is tested indirectly by ensuring the component renders without errors
      expect(screen.getByText('Legality')).toBeInTheDocument();
    });
  });

  describe('component layout', () => {
    it('uses correct Bootstrap grid classes', () => {
      const { container } = renderDeckBuilder();

      // Statistics column
      expect(container.querySelector('.col-xs-3')).toBeInTheDocument();

      // Main builder column
      expect(container.querySelector('.col-xs-9')).toBeInTheDocument();

      // Full width sections
      expect(container.querySelector('.col-xs-12')).toBeInTheDocument();
    });

    it('applies correct styling to legality heading', () => {
      renderDeckBuilder();
      const legalityHeading = screen.getByText('Legality');

      expect(legalityHeading).toHaveClass('lato-N6');
      expect(legalityHeading).toHaveStyle({
        fontSize: '1.9rem',
        color: '#3e3e3e',
        textAlign: 'center',
      });
    });
  });

  describe('component lifecycle', () => {
    it('initializes without errors', () => {
      const { container } = renderDeckBuilder();
      expect(container.querySelector('.section')).toBeInTheDocument();
    });

    it('cleans up deck store token on unmount', () => {
      const { unmount } = renderDeckBuilder();

      // Should unmount without errors
      expect(() => unmount()).not.toThrow();
    });
  });

  describe('responsive design', () => {
    it('uses responsive grid classes for grouping section', () => {
      const { container } = renderDeckBuilder();

      // Grouping and arrangement should use responsive classes
      const responsiveColumns = container.querySelectorAll('.col-xs-12.col-md-4');
      expect(responsiveColumns.length).toBeGreaterThanOrEqual(2);
    });
  });
});
