import * as Immutable from 'immutable';
import * as React from 'react';
import Dispatcher from '../../../dispatcher/Dispatcher';
import importPlotly from '../../../helpers/plotly';
import isServerside from '../../../helpers/serverside';

const Plotly = importPlotly();

interface IProps {
  dispatcher: Dispatcher;
  title: string;
  statistics: Immutable.Map<string, Immutable.Map<number, number>>;
  mini?: boolean;
}

interface IState {}

export class DeckStatisticsColorStack extends React.Component<IProps, IState> {
  private handleWindowResize: EventListener | null;
  private readonly titleRef: React.RefObject<HTMLDivElement>;

  constructor(props: IProps) {
    super(props);
    this.titleRef = React.createRef<HTMLDivElement>();
    this.state = {};
  }

  public componentDidMount() {
    this.renderChart();
    this.handleWindowResize = () => {
      if (this.titleRef.current && this.titleRef.current.id !== '') {
        Plotly.purge(this.titleRef.current.id);
      }
      this.renderChart();
    };
    window.addEventListener('resize', this.handleWindowResize);
  }

  public componentWillUnmount() {
    if (this.handleWindowResize != null) window.removeEventListener('resize', this.handleWindowResize);
    this.handleWindowResize = null;
  }

  public componentDidUpdate() {
    this.renderChart();
  }

  public render() {
    return (
      <div
        style={{
          position: 'relative',
          width: '100%',
          minWidth: this.props.mini ? '350px' : '0',
          maxWidth: this.props.mini ? '350px' : '100%',
          height: '30rem',
          maxHeight: this.props.mini ? '300px' : '30rem',
        }}
      >
        <div
          style={{ width: '100%', maxWidth: '100%', height: '100%', maxHeight: '100%' }}
          id={this.props.title}
          ref={this.titleRef}
        />
      </div>
    );
  }

  private renderChart() {
    if (isServerside()) {
      return;
    }

    const data = [
      this.stackData('white'),
      this.stackData('blue'),
      this.stackData('black'),
      this.stackData('red'),
      this.stackData('green'),
      this.stackData('multi'),
      this.stackData('colorless'),
    ];
    const maxCount = this.props.statistics.keySeq().reduce((max: number, key: string) => {
      return Math.max(max, ...this.props.statistics.get(key).keySeq().toJS());
    }, 7);

    const layout = {
      title: this.props.title,
      font: {
        family: 'lato-bold',
        src: {
          url: '/fonts/lato-bold-webfont.svg#latobold',
          format: 'svg',
        },
        size: 18,
        color: '#3e3e3e',
      },
      showlegend: false,
      barmode: 'stack',
      xaxis: {
        fixedrange: true,
        range: [-0.5, maxCount + 0.5],
        tick0: 0,
        dtick: this.props.mini ? 5 : 1,
      },
      yaxis: {
        fixedrange: true,
        tickmode: 'auto',
        tick0: 0,
        dtick: this.props.mini ? 4 : 2,
        nticks: this.props.mini ? 8 : 16,
        rangemode: 'tozero',
        autorange: true,
      },
    };

    const config: any = {
      staticPlot: false,
      editable: false,
      scrollZoom: false,
      showTips: false,
      showLink: false,
      sendData: false,
      showSources: false,
      displayModeBar: false,
      modeBarButtons: false,
      logging: false,
    };

    if (this.titleRef.current) {
      Plotly.newPlot(this.titleRef.current, data, layout, config);
    }
  }

  private stackData = (color: string) => {
    const keys = this.props.statistics.get(color).keySeq().toJS();
    const values = this.props.statistics.get(color).valueSeq().toJS();
    return {
      x: keys,
      y: values,
      name: color.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()),
      type: 'bar',
      marker: {
        color: (() => {
          switch (color) {
            case 'blue':
              return '#45C3FF';
            case 'red':
              return '#FF5C35';
            case 'green':
              return '#1AC655';
            case 'white':
              return '#FFE48B';
            case 'black':
              return '#676767';
            case 'multi':
              return '#EDAE49';
            case 'colorless':
            case 'disabled':
              return '#C5C5C5';
          }
        })(),
      },
    };
  };
}
