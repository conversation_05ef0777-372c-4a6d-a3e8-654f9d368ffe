import { describe, expect, it, vi } from 'vitest';

// Mock jsPDF
const mockJsPDF = {
  addImage: vi.fn(),
  setLineWidth: vi.fn(),
  rect: vi.fn(),
  setFontSize: vi.fn(),
  setFontStyle: vi.fn(),
  setFont: vi.fn(),
  text: vi.fn(),
  line: vi.fn(),
  save: vi.fn(),
};

vi.mock('jspdf', () => ({
  default: vi.fn(() => mockJsPDF),
}));

// Import after mocking
const Deck2PDF = require('./DeckToPDF');

describe('DeckToPDF', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    Object.values(mockJsPDF).forEach(mock => mock.mockClear());
  });

  describe('constructor', () => {
    it('initializes PDF with correct settings', () => {
      const deck2pdf = new Deck2PDF();
      
      expect(deck2pdf.pdf).toBeDefined();
      expect(deck2pdf.mainBoardCount).toBe(0);
      expect(deck2pdf.sideBoardCount).toBe(0);
    });

    it('adds logo image', () => {
      new Deck2PDF();
      
      expect(mockJsPDF.addImage).toHaveBeenCalledWith(
        expect.stringContaining('data:image/png;base64'),
        'PNG',
        20,
        5,
        100,
        100
      );
    });

    it('sets up form rectangles', () => {
      new Deck2PDF();
      
      // Should call rect multiple times to create form structure
      expect(mockJsPDF.rect).toHaveBeenCalledTimes(expect.any(Number));
      expect(mockJsPDF.rect).toHaveBeenCalledWith(135, 54, 441, 24); // date + event
      expect(mockJsPDF.rect).toHaveBeenCalledWith(135, 78, 441, 24); // location + deck name
    });

    it('adds form text labels', () => {
      new Deck2PDF();
      
      expect(mockJsPDF.text).toHaveBeenCalledWith('DECK REGISTRATION SHEET', 135, 45);
      expect(mockJsPDF.text).toHaveBeenCalledWith('Main Deck:', 62, 149);
      expect(mockJsPDF.text).toHaveBeenCalledWith('Sideboard:', 336, 404);
    });

    it('creates card entry lines', () => {
      new Deck2PDF();
      
      // Should call line multiple times to create entry lines
      expect(mockJsPDF.line).toHaveBeenCalledTimes(expect.any(Number));
    });
  });

  describe('downloadPDF', () => {
    it('saves PDF with default name when no parameters provided', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.downloadPDF();
      
      expect(mockJsPDF.save).toHaveBeenCalledWith('decklist.pdf');
    });

    it('saves PDF with custom name', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.downloadPDF('My Deck');
      
      expect(mockJsPDF.save).toHaveBeenCalledWith('My Deck decklist.pdf');
    });

    it('saves PDF with name and page number', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.downloadPDF('My Deck', 2);
      
      expect(mockJsPDF.save).toHaveBeenCalledWith('My Deck decklist page 2.pdf');
    });
  });

  describe('addEventDetails', () => {
    it('adds event information to PDF', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.addEventDetails('2023-12-01', 'Local Game Store', 'Friday Night Magic');
      
      expect(mockJsPDF.text).toHaveBeenCalledWith('2023-12-01', 192, 69.5);
      expect(mockJsPDF.text).toHaveBeenCalledWith('Local Game Store', 192, 93.5);
      expect(mockJsPDF.text).toHaveBeenCalledWith('Friday Night Magic', 412, 69.5);
    });
  });

  describe('addDeckDetails', () => {
    it('adds deck information to PDF', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.addDeckDetails('Red Deck Wins', 'John Doe');
      
      expect(mockJsPDF.text).toHaveBeenCalledWith('Red Deck Wins', 412, 93.5);
      expect(mockJsPDF.text).toHaveBeenCalledWith('John Doe', 412, 117.5);
    });
  });

  describe('addPlayerDetails', () => {
    it('adds player information to PDF', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.addPlayerDetails('John', 'Doe', '1234567890');
      
      expect(mockJsPDF.text).toHaveBeenCalledWith('John', 43, 544, 90);
      expect(mockJsPDF.text).toHaveBeenCalledWith('Doe', 43, 724, 90);
      expect(mockJsPDF.text).toHaveBeenCalledWith('D', expect.any(Number), 49);
    });

    it('handles special character positioning for first letter', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.addPlayerDetails('John', 'Miller', '1234567890');
      
      // 'M' should have offset of -1
      expect(mockJsPDF.text).toHaveBeenCalledWith('M', 556, 49);
    });

    it('adds DCI number digits', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.addPlayerDetails('John', 'Doe', '123');
      
      expect(mockJsPDF.text).toHaveBeenCalledWith('1', 43, 372, 90);
      expect(mockJsPDF.text).toHaveBeenCalledWith('2', 43, 348, 90);
      expect(mockJsPDF.text).toHaveBeenCalledWith('3', 43, 324, 90);
    });
  });

  describe('addMainBoard', () => {
    it('adds main deck cards to PDF', () => {
      const deck2pdf = new Deck2PDF();
      const cards = [
        { count: 4, name: 'Lightning Bolt' },
        { count: 2, name: 'Shock' },
        { count: 0, name: 'Empty Card' }, // Should be ignored
      ];
      
      deck2pdf.addMainBoard(cards, 6);
      
      expect(deck2pdf.mainBoardCount).toBe(6);
      expect(mockJsPDF.text).toHaveBeenCalledWith(4, 82, 182);
      expect(mockJsPDF.text).toHaveBeenCalledWith('Lightning Bolt', 120, 182);
      expect(mockJsPDF.text).toHaveBeenCalledWith(2, 82, 200);
      expect(mockJsPDF.text).toHaveBeenCalledWith('Shock', 120, 200);
      
      // Should not add the card with count 0
      expect(mockJsPDF.text).not.toHaveBeenCalledWith(0, expect.any(Number), expect.any(Number));
    });

    it('handles large deck lists by moving to second column', () => {
      const deck2pdf = new Deck2PDF();
      const cards = Array.from({ length: 35 }, (_, i) => ({
        count: 1,
        name: `Card ${i + 1}`,
      }));
      
      deck2pdf.addMainBoard(cards, 35);
      
      // Cards after index 32 should be in the second column (x = 356)
      expect(mockJsPDF.text).toHaveBeenCalledWith(1, 356, 182);
      expect(mockJsPDF.text).toHaveBeenCalledWith('Card 33', 394, 182);
    });
  });

  describe('addSideBoard', () => {
    it('adds sideboard cards to PDF', () => {
      const deck2pdf = new Deck2PDF();
      const cards = [
        { count: 3, name: 'Counterspell' },
        { count: 2, name: 'Negate' },
      ];
      
      deck2pdf.addSideBoard(cards, 5);
      
      expect(deck2pdf.sideBoardCount).toBe(5);
      expect(mockJsPDF.text).toHaveBeenCalledWith(3, 356, 434);
      expect(mockJsPDF.text).toHaveBeenCalledWith('Counterspell', 394, 434);
      expect(mockJsPDF.text).toHaveBeenCalledWith(2, 356, 452);
      expect(mockJsPDF.text).toHaveBeenCalledWith('Negate', 394, 452);
    });

    it('handles empty sideboard', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.addSideBoard([], 0);
      
      expect(deck2pdf.sideBoardCount).toBe(0);
    });
  });

  describe('addTotalCounts', () => {
    it('adds main deck count when non-zero', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.mainBoardCount = 60;
      deck2pdf.addTotalCounts();
      
      expect(mockJsPDF.text).toHaveBeenCalledWith('60', 268, 766);
    });

    it('adds sideboard count with correct positioning', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.sideBoardCount = 5;
      deck2pdf.addTotalCounts();
      
      // Single digit should use position 547
      expect(mockJsPDF.text).toHaveBeenCalledWith('5', 547, 712);
    });

    it('positions double-digit sideboard count correctly', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.sideBoardCount = 15;
      deck2pdf.addTotalCounts();
      
      // Double digit should use position 541
      expect(mockJsPDF.text).toHaveBeenCalledWith('15', 541, 712);
    });

    it('does not add counts when they are zero', () => {
      const deck2pdf = new Deck2PDF();
      deck2pdf.mainBoardCount = 0;
      deck2pdf.sideBoardCount = 0;
      deck2pdf.addTotalCounts();
      
      // Should not add any count text
      expect(mockJsPDF.text).not.toHaveBeenCalledWith('0', expect.any(Number), expect.any(Number));
    });
  });

  describe('getImageData', () => {
    it('returns base64 image data', () => {
      const deck2pdf = new Deck2PDF();
      const imageData = deck2pdf.getImageData();
      
      expect(imageData).toMatch(/^data:image\/png;base64,/);
      expect(typeof imageData).toBe('string');
      expect(imageData.length).toBeGreaterThan(100);
    });
  });
});
