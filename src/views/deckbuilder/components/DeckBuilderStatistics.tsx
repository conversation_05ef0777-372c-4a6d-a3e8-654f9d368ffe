import * as React from 'react';
// Containers, actions, and dispatcher
import Dispatcher from '../../../dispatcher/Dispatcher';
// Models
import {
  calculateCardTypeRatios,
  calculateManaSymbolRatios,
  calculateManaSymbolStack,
  Deck,
} from '../../../models/Decks';
import { DeckStatisticsColorStack } from './DeckStatisticsColorStack';
// Components
import { DeckStatisticsRatios } from './DeckStatisticsRatios';

interface IProps {
  dispatcher: Dispatcher;
  deck: Deck;
}

interface IState {}

export class DeckBuilderStatistics extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  /**
   * @override
   */
  componentDidMount() {}

  /**
   * @override
   */
  render() {
    const cardTypeRatios = calculateCardTypeRatios(this.props.deck);
    const manaSymbolRatios = calculateManaSymbolRatios(this.props.deck).remove('totalCount').remove('disabled');
    const manaSymbolStack = calculateManaSymbolStack(this.props.deck);

    return (
      <div className="deck-statistics" style={{ zIndex: -1 }}>
        {/* graphs */}
        <div className="row">
          <div className="col-xs-12 flex vertical align-center" style={{ overflow: 'hidden' }}>
            <DeckStatisticsRatios
              dispatcher={this.props.dispatcher}
              ratios={cardTypeRatios}
              title={'Card Types'}
              mini
            />
          </div>
          <div className="col-xs-12 flex vertical align-center" style={{ overflow: 'hidden' }}>
            <DeckStatisticsRatios
              dispatcher={this.props.dispatcher}
              ratios={manaSymbolRatios}
              title={'Mana Symbols'}
              mini
            />
          </div>
          <div className="col-xs-12 flex vertical align-center" style={{ overflow: 'hidden' }}>
            <DeckStatisticsColorStack
              dispatcher={this.props.dispatcher}
              statistics={manaSymbolStack}
              title={'Mana Curve'}
              mini
            />
          </div>
        </div>
      </div>
    );
  }
}
