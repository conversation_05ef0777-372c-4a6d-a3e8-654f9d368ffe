import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck } from '../../../../tests/fake/FakeCardData';
import { FakeUser } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { DeckExport } from './DeckExport';

// Mock dependencies
vi.mock('../../../helpers/ga_helper', () => ({
  default: vi.fn(),
}));

vi.mock('../../../helpers/serverside', () => ({
  default: vi.fn(() => false),
}));

// Mock Clipboard
const mockClipboard = {
  on: vi.fn(),
  destroy: vi.fn(),
};

vi.mock('clipboard', () => vi.fn(() => mockClipboard));

// Mock window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: vi.fn(),
});

import track from '../../../helpers/ga_helper';
const mockTrack = vi.mocked(track);
const mockWindowOpen = vi.mocked(window.open);

type DeckExportProps = React.ComponentProps<typeof DeckExport>;

const defaultProps: Omit<DeckExportProps, 'dispatcher' | 'me'> = {
  deck: create(FakeDeck, {
    uuid: 'test-deck-uuid',
    name: 'Test Deck',
  }),
};

const renderDeckExport = (props: Partial<DeckExportProps> = {}) => {
  return renderWithDispatcher(DeckExport, { 
    ...defaultProps, 
    me: create(FakeUser),
    ...props 
  });
};

describe('DeckExport', () => {
  beforeEach(() => {
    mockTrack.mockClear();
    mockWindowOpen.mockClear();
    mockClipboard.on.mockClear();
  });

  describe('when component renders', () => {
    it('displays the main container', () => {
      const { container } = renderDeckExport();
      expect(container.querySelector('.container-fluid')).toBeInTheDocument();
    });

    it('displays the section container', () => {
      const { container } = renderDeckExport();
      expect(container.querySelector('.section')).toBeInTheDocument();
    });

    it('displays PDF export section', () => {
      renderDeckExport();
      expect(screen.getByText('Decklist PDF')).toBeInTheDocument();
      expect(screen.getByText(/Generate a decklist PDF to register in competitions/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Create PDF' })).toBeInTheDocument();
    });

    it('displays CSV export section', () => {
      renderDeckExport();
      expect(screen.getByText('CSV')).toBeInTheDocument();
      expect(screen.getByText('Export your deck as a CSV file')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Download CSV' })).toBeInTheDocument();
    });

    it('displays link to decklist.org', () => {
      renderDeckExport();
      const link = screen.getByRole('link', { name: 'decklist.org' });
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', 'https://decklist.org');
    });

    it('uses correct grid layout', () => {
      const { container } = renderDeckExport();
      
      const columns = container.querySelectorAll('.col-xs-12.col-md-6');
      expect(columns).toHaveLength(2);
    });
  });

  describe('PDF creation', () => {
    it('opens PDF dialog when Create PDF button is clicked', () => {
      renderDeckExport();
      const createPDFButton = screen.getByRole('button', { name: 'Create PDF' });
      
      fireEvent.click(createPDFButton);
      
      // Dialog should be open and contain the PDF form
      expect(screen.getByText('Please enter the following personal details')).toBeInTheDocument();
    });

    it('prevents default event behavior when Create PDF is clicked', () => {
      renderDeckExport();
      const createPDFButton = screen.getByRole('button', { name: 'Create PDF' });
      
      const clickEvent = new Event('click', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(clickEvent, 'preventDefault');
      const stopPropagationSpy = vi.spyOn(clickEvent, 'stopPropagation');
      
      fireEvent(createPDFButton, clickEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
      expect(stopPropagationSpy).toHaveBeenCalled();
    });

    it('closes PDF dialog when dismissed', () => {
      renderDeckExport();
      const createPDFButton = screen.getByRole('button', { name: 'Create PDF' });
      
      fireEvent.click(createPDFButton);
      
      // Dialog should be open
      expect(screen.getByText('Please enter the following personal details')).toBeInTheDocument();
      
      // The dialog should be closable through the onDismiss callback
      // This would be tested in integration tests with the actual Dialog component
    });

    it('renders PDF form with correct deck prop', () => {
      const deck = create(FakeDeck, { name: 'Custom Deck' });
      renderDeckExport({ deck });
      
      const createPDFButton = screen.getByRole('button', { name: 'Create PDF' });
      fireEvent.click(createPDFButton);
      
      // The DeckBuilderPDFForm should receive the deck prop
      // This is tested indirectly by ensuring the form renders
      expect(screen.getByText('Please enter the following personal details')).toBeInTheDocument();
    });
  });

  describe('CSV download', () => {
    it('opens CSV download URL when Download CSV button is clicked', () => {
      renderDeckExport();
      const downloadCSVButton = screen.getByRole('button', { name: 'Download CSV' });
      
      fireEvent.click(downloadCSVButton);
      
      expect(mockWindowOpen).toHaveBeenCalledWith(
        '/api/v3/decks/test-deck-uuid/export',
        '_blank'
      );
    });

    it('tracks CSV export event', () => {
      renderDeckExport();
      const downloadCSVButton = screen.getByRole('button', { name: 'Download CSV' });
      
      fireEvent.click(downloadCSVButton);
      
      expect(mockTrack).toHaveBeenCalledWith('decks', 'export', 'csv');
    });

    it('prevents default event behavior when Download CSV is clicked', () => {
      renderDeckExport();
      const downloadCSVButton = screen.getByRole('button', { name: 'Download CSV' });
      
      const clickEvent = new Event('click', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(clickEvent, 'preventDefault');
      const stopPropagationSpy = vi.spyOn(clickEvent, 'stopPropagation');
      
      fireEvent(downloadCSVButton, clickEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
      expect(stopPropagationSpy).toHaveBeenCalled();
    });

    it('uses correct deck UUID in export URL', () => {
      const deck = create(FakeDeck, { uuid: 'custom-uuid-123' });
      renderDeckExport({ deck });
      
      const downloadCSVButton = screen.getByRole('button', { name: 'Download CSV' });
      fireEvent.click(downloadCSVButton);
      
      expect(mockWindowOpen).toHaveBeenCalledWith(
        '/api/v3/decks/custom-uuid-123/export',
        '_blank'
      );
    });
  });

  describe('clipboard functionality', () => {
    it('initializes clipboard on mount', () => {
      renderDeckExport();
      
      // Clipboard should be initialized with the share button
      expect(mockClipboard.on).toHaveBeenCalledWith('success', expect.any(Function));
      expect(mockClipboard.on).toHaveBeenCalledWith('error', expect.any(Function));
    });

    it('handles clipboard success event', () => {
      renderDeckExport();
      
      // Get the success callback
      const successCallback = mockClipboard.on.mock.calls.find(
        call => call[0] === 'success'
      )?.[1];
      
      if (successCallback) {
        const mockEvent = {
          clearSelection: vi.fn(),
        };
        
        successCallback(mockEvent);
        
        expect(mockEvent.clearSelection).toHaveBeenCalled();
        // The component state should be updated to show 'Copied'
        // This would be visible if there was a share button in the component
      }
    });

    it('handles clipboard error event', () => {
      renderDeckExport();
      
      // Get the error callback
      const errorCallback = mockClipboard.on.mock.calls.find(
        call => call[0] === 'error'
      )?.[1];
      
      if (errorCallback) {
        const mockEvent = {};
        
        errorCallback(mockEvent);
        
        // The component state should be updated to show 'CMD+C to copy'
        // This would be visible if there was a share button in the component
      }
    });
  });

  describe('component state', () => {
    it('initializes with correct default state', () => {
      renderDeckExport();
      
      // Component should initialize with creatingPDF: false
      // PDF dialog should not be visible initially
      expect(screen.queryByText('Please enter the following personal details')).not.toBeInTheDocument();
    });

    it('updates state when PDF creation is triggered', () => {
      renderDeckExport();
      const createPDFButton = screen.getByRole('button', { name: 'Create PDF' });
      
      fireEvent.click(createPDFButton);
      
      // State should be updated to show PDF dialog
      expect(screen.getByText('Please enter the following personal details')).toBeInTheDocument();
    });
  });

  describe('responsive design', () => {
    it('applies correct responsive classes', () => {
      const { container } = renderDeckExport();
      
      // Both sections should use responsive grid classes
      const responsiveColumns = container.querySelectorAll('.col-xs-12.col-md-6');
      expect(responsiveColumns).toHaveLength(2);
    });

    it('applies correct margin styling', () => {
      const { container } = renderDeckExport();
      
      const columns = container.querySelectorAll('.col-xs-12.col-md-6');
      columns.forEach(column => {
        expect(column).toHaveStyle({ marginBottom: '2rem' });
      });
    });
  });

  describe('accessibility', () => {
    it('provides proper button labels', () => {
      renderDeckExport();
      
      expect(screen.getByRole('button', { name: 'Create PDF' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Download CSV' })).toBeInTheDocument();
    });

    it('provides descriptive text for each export option', () => {
      renderDeckExport();
      
      expect(screen.getByText(/Generate a decklist PDF to register in competitions/)).toBeInTheDocument();
      expect(screen.getByText('Export your deck as a CSV file')).toBeInTheDocument();
    });

    it('provides external link to decklist.org', () => {
      renderDeckExport();
      
      const link = screen.getByRole('link', { name: 'decklist.org' });
      expect(link).toHaveAttribute('href', 'https://decklist.org');
    });
  });

  describe('dialog configuration', () => {
    it('configures PDF dialog correctly', () => {
      renderDeckExport();
      const createPDFButton = screen.getByRole('button', { name: 'Create PDF' });
      
      fireEvent.click(createPDFButton);
      
      // Dialog should be open and not closable (based on the component code)
      // The exact dialog configuration would be tested in integration tests
      expect(screen.getByText('Please enter the following personal details')).toBeInTheDocument();
    });
  });
});
