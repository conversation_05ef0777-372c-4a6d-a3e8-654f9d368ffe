import * as Immutable from 'immutable';
import * as React from 'react';
import * as ReactDOM from 'react-dom';
// Containers, actions, and dispatcher
import * as DeckActions from '../../../actions/DeckActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import track from '../../../helpers/ga_helper';
import { Arranging } from '../../../models/Arranging';
// Models
import { Deck, DeckBoard, DeckCard } from '../../../models/Decks';
import { Grouping } from '../../../models/Grouping';
import { User } from '../../../models/Users';
import { Dialog } from '../../components/Dialog';
// Components
import { DeckBuilderBoardItem } from './DeckBuilderBoardItem';
import { DeckLinkerWrapper } from './DeckLinkerWrapper';

interface IProps {
  dispatcher: Dispatcher;
  me: User;
  deck: Deck;
  deckBoard: DeckBoard;
  deckArranging: Arranging;
  deckGrouping: Grouping;
  mutable?: boolean;
  linkable?: boolean;
}

interface IState {
  deckBoard: DeckBoard;
  isHiding: boolean;
  isRemoving: boolean;
  isEditingName: boolean;
  isLinkingCards?: Immutable.List<DeckCard>;
}

export class DeckBuilderBoard extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {
      deckBoard: props.deckBoard,
      isHiding: false,
      isRemoving: false,
      isEditingName: false,
    };
  }

  /**
   * @override
   */
  componentWillReceiveProps(props: IProps) {
    this.setState({
      deckBoard: props.deckBoard,
    });
  }

  /**
   * @override
   */
  componentDidUpdate() {
    if (this.isEditingNeedsFocus) {
      this.isEditingNeedsFocus = false;
      if (this.refs['nameInput']) {
        (ReactDOM.findDOMNode(this.refs['nameInput']) as HTMLElement).focus();
      }
    }
  }

  /**
   * @override
   */
  render() {
    const cards = this.props.deck.get('cards');
    let deckCardStacks = Immutable.Map<string, Immutable.List<DeckCard>>();

    this.props.deck
      .get('deckCards')
      .filter((deckCard: DeckCard) => deckCard.get('boardId') === this.props.deckBoard.get('id'))
      .forEach((deckCard: DeckCard) => {
        switch (this.props.deckGrouping) {
          case Grouping.NONE:
            deckCardStacks = deckCardStacks.set('' + deckCard.get('id'), Immutable.List<DeckCard>([deckCard]));
            break;
          case Grouping.NAME:
            const cardName = cards.get(deckCard.get('cardId')).get('name');
            if (deckCardStacks.has(cardName)) {
              deckCardStacks = deckCardStacks.set(cardName, deckCardStacks.get(cardName).push(deckCard));
            } else {
              deckCardStacks = deckCardStacks.set(cardName, Immutable.List<DeckCard>([deckCard]));
            }
            break;
          case Grouping.PRINTING:
            const jsonID = cards.get(deckCard.get('cardId')).get('jsonID');
            if (deckCardStacks.has(jsonID)) {
              deckCardStacks = deckCardStacks.set(jsonID, deckCardStacks.get(jsonID).push(deckCard));
            } else {
              deckCardStacks = deckCardStacks.set(jsonID, Immutable.List<DeckCard>([deckCard]));
            }
            break;
        }
      });

    let arrangements = Immutable.Map<string, Immutable.List<Immutable.List<DeckCard>>>();
    deckCardStacks
      .filter((deckCardStack: Immutable.List<DeckCard>) => deckCardStack.size > 0)
      .forEach((deckCardStack: Immutable.List<DeckCard>, _: string) => {
        const card = cards.get(deckCardStack.get(0).get('cardId'));
        switch (this.props.deckArranging) {
          case Arranging.CARD_TYPE:
            const types = card.get('types');
            const fullType = types.join(' ');
            if (types.indexOf('Land') >= 0) {
              if (arrangements.has('Land')) {
                arrangements = arrangements.set('Land', arrangements.get('Land').push(deckCardStack));
              } else {
                arrangements = arrangements.set('Land', Immutable.List([deckCardStack]));
              }
            } else if (arrangements.has(fullType)) {
              arrangements = arrangements.set(fullType, arrangements.get(fullType).push(deckCardStack));
            } else {
              arrangements = arrangements.set(fullType, Immutable.List([deckCardStack]));
            }
            break;

          case Arranging.MANA_COST:
            const manaCost = card.get('manaValue');
            if (arrangements.has('' + manaCost)) {
              arrangements = arrangements.set('' + manaCost, arrangements.get('' + manaCost).push(deckCardStack));
            } else {
              arrangements = arrangements.set('' + manaCost, Immutable.List([deckCardStack]));
            }
            break;
        }
      });

    const numDeckCards = this.props.deck
      .get('deckCards')
      .filter((deckCard: DeckCard) => deckCard.get('boardId') === this.props.deckBoard.get('id')).size;
    const arrangementSorter = (deckCardStackA: Immutable.List<DeckCard>, deckCardStackB: Immutable.List<DeckCard>) => {
      if (this.props.deckArranging === Arranging.CARD_TYPE) {
        const manaValueA = cards.get(deckCardStackA!.get(0).get('cardId')).get('manaValue');
        const manaValueB = cards.get(deckCardStackB!.get(0).get('cardId')).get('manaValue');
        if (manaValueA === manaValueB) {
          const a = cards.get(deckCardStackA!.get(0).get('cardId')).get('name');
          const b = cards.get(deckCardStackB!.get(0).get('cardId')).get('name');
          return a.localeCompare(b);
        }
        return manaValueA > manaValueB ? -1 : 1;
      } else {
        const a = cards.get(deckCardStackA!.get(0).get('cardId')).get('name');
        const b = cards.get(deckCardStackB!.get(0).get('cardId')).get('name');
        return a.localeCompare(b);
      }
    };

    return (
      <div className="deckbuilder-board">
        <Dialog
          isOpen={this.state.isRemoving || false}
          onDismiss={() => {
            this.setState({ isRemoving: false });
          }}
        >
          <h3 className="query-dialog-heading">Are you sure?</h3>
          <p className="query-dialog-subheading" style={{ whiteSpace: 'normal' }}>
            {'This board will be removed from your deck. This decision cannot be reversed!'}
          </p>
          <div className="flex justify-center">
            <input
              type="submit"
              className="button-alert"
              value="Remove"
              autoComplete="off"
              onClick={this.onConfirmRemove.bind(this)}
            />
          </div>
        </Dialog>

        <DeckLinkerWrapper
          dispatcher={this.props.dispatcher}
          me={this.props.me}
          deck={this.props.deck}
          deckCards={this.state.isLinkingCards!}
          isOpen={this.state.isLinkingCards !== undefined || false}
          onDismiss={() => {
            DeckActions.deck(this.props.deck.get('uuid'), this.props.dispatcher);
            this.setState({ isLinkingCards: undefined });
          }}
        />

        <div className="row">
          {/* heading */}
          {/* TODO: Replace editable board names with an extension of the EditableTitle element. */}
          <div className="col-xs-12">
            <div className="deck-board-title">
              <div className="deck-board-title__left-wrapper" />
              {this.state.isEditingName ? (
                <form onSubmit={this.onSubmitName}>
                  <input
                    ref="nameInput"
                    className="heading-md"
                    value={this.state.deckBoard.get('name') || ''}
                    onChange={this.onChangeName}
                    onBlur={this.onBlurName}
                    style={{ margin: '0.67em 0' }}
                  />
                </form>
              ) : (
                <h1 className="heading-md">{`${this.state.deckBoard.get('name')} Board (${numDeckCards})`}</h1>
              )}
              <div className="deck-board-title__right-wrapper">
                {!this.isSpecialBoard() && this.props.mutable ? (
                  <div className="deck-board-title__right-wrapper__button" onClick={this.onClickRemove}>
                    Remove
                  </div>
                ) : null}
                {!this.isSpecialBoard() && this.props.mutable ? (
                  <div className="deck-board-title__right-wrapper__button" onClick={this.onClickRename}>
                    Rename
                  </div>
                ) : null}
                <div className="deck-board-title__right-wrapper__button" onClick={this.onClickToggleHide}>
                  {this.state.isHiding ? 'View' : 'Hide'}
                </div>
              </div>
            </div>
          </div>
          {/* divider */}
          <div
            className="col-xs-12"
            style={{
              content: '""',
              height: '1px',
              backgroundColor: '#CDCDCD',
              margin: `1rem 0 ${this.state.isHiding ? `0` : `4rem`} 0`,
            }}
          />
        </div>

        {/* body */}
        {this.state.isHiding
          ? null
          : arrangements
              // sort by keys using the value mapper `(_, key) => key` and then if we are in a color arrangement we
              // need to convert strings to ints and do an integer comparison, otherwise we need to do an alphabetical
              // comparison. the exception to this is "Land" cards which must always be last
              .sortBy(
                (_, key) => key,
                (a, b) =>
                  this.props.deckArranging === Arranging.MANA_COST
                    ? parseInt(a!) === parseInt(b!)
                      ? 0
                      : parseInt(a!) < parseInt(b!)
                      ? -1
                      : 1
                    : a === 'Land'
                    ? 1
                    : b === 'Land'
                    ? -1
                    : a!.localeCompare(b!),
              )
              .filter((arrangement: Immutable.List<Immutable.List<DeckCard>>) => arrangement.size > 0)
              // filter away arrangements that do not have at least one stack of cards for this board
              .filter(
                (arrangement: Immutable.List<Immutable.List<DeckCard>>) =>
                  arrangement.reduce(
                    (arrangement, deckCardStack) =>
                      deckCardStack!.size > 0 &&
                      deckCardStack!.get(0).get('boardId') === this.props.deckBoard.get('id') &&
                      (this.props.deckArranging !== Arranging.MANA_COST ||
                        cards.get(deckCardStack!.get(0).get('cardId')).get('types').indexOf('Land') < 0)
                        ? arrangement!.concat(deckCardStack!).toList()
                        : arrangement!,
                    Immutable.List<DeckCard>(),
                  ).size > 0,
              )
              // map all arrangements into rows of stacks
              .map((arrangement: Immutable.List<Immutable.List<DeckCard>>, key: string) => {
                arrangement = arrangement.reduce(
                  (arrangement, deckCardStack) =>
                    this.props.deckArranging !== Arranging.MANA_COST ||
                    cards.get(deckCardStack!.get(0).get('cardId')).get('types').indexOf('Land') < 0
                      ? arrangement!.push(deckCardStack!)
                      : arrangement!,
                  Immutable.List<Immutable.List<DeckCard>>(),
                );
                if (!arrangement.size) {
                  return null;
                }
                return (
                  <div key={key} className="row">
                    <h2 className="col-xs-12 lato-N6">
                      {(this.props.deckArranging === Arranging.MANA_COST ? key + ' Drops' : key) +
                        ' (' +
                        arrangement.reduce(
                          (count: number, deckCardStack: Immutable.List<DeckCard>) =>
                            deckCardStack.size > 0 &&
                            deckCardStack.get(0).get('boardId') === this.props.deckBoard.get('id')
                              ? count + deckCardStack.size
                              : count,
                          0,
                        ) +
                        ')'}
                    </h2>
                    {arrangement
                      // filter away stacks that have no cards or aren't on this board
                      .filter(
                        (deckCardStack: Immutable.List<DeckCard>) =>
                          deckCardStack.size > 0 &&
                          deckCardStack.get(0).get('boardId') === this.props.deckBoard.get('id'),
                      )
                      .sort(arrangementSorter)
                      // render the stack
                      .map((deckCardStack: Immutable.List<DeckCard>) => (
                        <div
                          key={`deck-card-${deckCardStack.get(0).get('id')}`}
                          className="col-xs-6 col-md-4 col-lg-3 col-xl-2"
                          style={{ marginBottom: '1rem' }}
                        >
                          <DeckBuilderBoardItem
                            dispatcher={this.props.dispatcher}
                            deck={this.props.deck}
                            deckBoard={this.props.deckBoard}
                            deckCards={deckCardStack}
                            card={this.props.deck.get('cards').get(deckCardStack.get(0).get('cardId'))}
                            cardCount={deckCardStack.size}
                            showCount={this.props.deckGrouping !== Grouping.NONE}
                            mutable={this.props.mutable}
                            linkable={this.props.linkable}
                            onClickLink={(evt: React.SyntheticEvent<HTMLElement>) => {
                              evt.preventDefault();
                              evt.stopPropagation();
                              this.setState({
                                isLinkingCards: deckCardStack,
                              });
                            }}
                          />
                        </div>
                      ))}
                  </div>
                );
              })
              .toArray()}

        {/* lands */}
        {this.props.deckArranging !== Arranging.MANA_COST || this.state.isHiding
          ? null
          : arrangements
              // sort by keys using the value mapper `(_, key) => key` and then if we are in a color arrangement we
              // need to convert strings to ints and do an integer comparison, otherwise we need to do an alphabetical
              // comparison. the exception to this is "Land" cards which must always be last
              .sortBy(
                (_, key) => key,
                (a, b) =>
                  this.props.deckArranging === Arranging.MANA_COST
                    ? parseInt(a!) === parseInt(b!)
                      ? 0
                      : parseInt(a!) < parseInt(b!)
                      ? -1
                      : 1
                    : a === 'Land'
                    ? 1
                    : b === 'Land'
                    ? -1
                    : a!.localeCompare(b!),
              )
              .filter((arrangement: Immutable.List<Immutable.List<DeckCard>>) => arrangement.size > 0)
              // filter away arrangements that do not have at least one stack of cards for this board
              .filter(
                (arrangement: Immutable.List<Immutable.List<DeckCard>>) =>
                  arrangement.reduce(
                    (arrangement, deckCardStack) =>
                      deckCardStack!.size > 0 &&
                      deckCardStack!.get(0).get('boardId') === this.props.deckBoard.get('id') &&
                      cards.get(deckCardStack!.get(0).get('cardId')).get('types').indexOf('Land') >= 0
                        ? arrangement!.concat(deckCardStack!).toList()
                        : arrangement!,
                    Immutable.List<DeckCard>(),
                  ).size > 0,
              )
              // map all arrangements into rows of stacks
              .map((arrangement: Immutable.List<Immutable.List<DeckCard>>, key: string) => {
                arrangement = arrangement.reduce(
                  (arrangement, deckCardStack) =>
                    cards.get(deckCardStack!.get(0).get('cardId')).get('types').indexOf('Land') >= 0
                      ? arrangement!.push(deckCardStack!)
                      : arrangement!,
                  Immutable.List<Immutable.List<DeckCard>>(),
                );
                if (!arrangement.size) {
                  return null;
                }
                return (
                  <div key={key} className="row">
                    <h2 className="col-xs-12 lato-N6">
                      {'Lands' +
                        ' (' +
                        arrangement.reduce(
                          (count: number, deckCardStack: Immutable.List<DeckCard>) =>
                            deckCardStack.size > 0 &&
                            deckCardStack.get(0).get('boardId') === this.props.deckBoard.get('id')
                              ? count + deckCardStack.size
                              : count,
                          0,
                        ) +
                        ')'}
                    </h2>
                    {arrangement
                      // filter away stacks that have no cards or aren't on this board
                      .filter(
                        (deckCardStack: Immutable.List<DeckCard>) =>
                          deckCardStack.size > 0 &&
                          deckCardStack.get(0).get('boardId') === this.props.deckBoard.get('id'),
                      )
                      .sort(arrangementSorter)
                      // render the stack
                      .map((deckCardStack: Immutable.List<DeckCard>) => (
                        <div
                          key={`deck-card-${deckCardStack.get(0).get('id')}`}
                          className="col-xs-6 col-md-4 col-lg-3 col-xl-2"
                          style={{ marginBottom: '1rem' }}
                        >
                          <DeckBuilderBoardItem
                            dispatcher={this.props.dispatcher}
                            deck={this.props.deck}
                            deckBoard={this.props.deckBoard}
                            deckCards={deckCardStack}
                            card={this.props.deck.get('cards').get(deckCardStack.get(0).get('cardId'))}
                            cardCount={deckCardStack.size}
                            showCount={this.props.deckGrouping !== Grouping.NONE}
                            mutable={this.props.mutable}
                            linkable={this.props.linkable}
                            onClickLink={(evt: React.SyntheticEvent<HTMLElement>) => {
                              evt.preventDefault();
                              evt.stopPropagation();
                              this.setState({
                                isLinkingCards: deckCardStack,
                              });
                            }}
                          />
                        </div>
                      ))}
                  </div>
                );
              })
              .toArray()}
      </div>
    );
  }

  private onClickToggleHide = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    track('decks', 'board', 'change_visibility');
    this.setState({
      isHiding: !this.state.isHiding,
    });
  };

  private onClickRemove = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    this.setState({ isRemoving: true });
  };

  private onConfirmRemove = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    evt.stopPropagation();
    track('decks', 'board', 'remove');
    this.setState({ isRemoving: false });
    DeckActions.removeDeckBoard(this.props.deck, this.props.deckBoard, this.props.dispatcher);
  };

  private onClickRename = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    this.isEditingNeedsFocus = true;
    this.setState({
      isEditingName: true,
    });
  };

  private onChangeName = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    evt.preventDefault();
    this.setState({
      deckBoard: this.state.deckBoard.set('name', evt.currentTarget.value),
    });
  };

  private onBlurName = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    evt.preventDefault();
    this.setState({
      deckBoard: this.state.deckBoard.set('name', this.props.deckBoard.get('name')),
      isEditingName: false,
    });
  };

  private onSubmitName = (evt: React.SyntheticEvent<HTMLFormElement>) => {
    evt.preventDefault();
    evt.stopPropagation();

    track('decks', 'board', 'name');

    this.setState({
      isEditingName: false,
    });
    DeckActions.updateDeckBoard(this.props.deck, this.state.deckBoard, this.props.dispatcher);
  };

  private isSpecialBoard = () => {
    return (
      this.props.deckBoard.get('name').toLowerCase() === 'main' ||
      this.props.deckBoard.get('name').toLowerCase() === 'side'
    );
  };

  private isEditingNeedsFocus = false;
}
