// Iconify
import round_table_rows from '@iconify/icons-ic/round-table-rows';
import * as React from 'react';
// Containers, actions, and dispatcher
import * as DeckActions from '../../../actions/DeckActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
// Models
import { Arranging, type ArrangingKey } from '../../../models/Arranging';
import { IconSelect } from '../../components/IconSelect';

interface IProps {
  dispatcher: Dispatcher;
  deckArranging: Arranging;
}

export const DeckBuilderArrangement = (props: IProps) => {
  return (
    <div className="deckbuilder-info-container">
      <div className="deckbuilder-info-heading">Arrange By</div>
      <IconSelect
        className="icon-container--deck"
        icon={round_table_rows}
        value={props.deckArranging}
        onChange={onChangeArranging}
      >
        <>
          <option disabled>Arrange by</option>
          {Object.keys(Arranging).map((key) => (
            <option key={key} value={Arranging[key as ArrangingKey]}>
              {Arranging[key as Arranging<PERSON>ey]}
            </option>
          ))}
        </>
      </IconSelect>
    </div>
  );

  function onChangeArranging(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    DeckActions.arranging(evt.currentTarget.value as any, props.dispatcher);
  }
};
