import * as React from 'react';
// Containers, actions, and dispatcher
import * as HasDispatcher from '../../../containers/HasDispatcher';
import * as HasMe from '../../../containers/HasMe';
import { Arranging } from '../../../models/Arranging';
// Models
import { Deck, DeckBoard } from '../../../models/Decks';
import { Grouping } from '../../../models/Grouping';
// Components
import { Navbar } from '../../components/Navbar';
import { DeckBuilderAdd } from './DeckBuilderAdd';
import { DeckBuilderArrangement } from './DeckBuilderArrangement';
import { DeckBuilderBoard } from './DeckBuilderBoard';
import { DeckBuilderGrouping } from './DeckBuilderGrouping';
import { DeckBuilderStatistics } from './DeckBuilderStatistics';
import { DeckLegalities } from './DeckLegalities';

interface IProps {
  deck: Deck;
  deckGrouping: Grouping;
  deckArranging: Arranging;
}

interface IState {}

export const DeckBuilder = HasMe.Attach<HasDispatcher.IProps & IProps>(
  class extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    private deckStoreToken?: { remove: () => void };

    /**
     * @override
     * @constructor
     */
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
    }

    /**
     * @override
     */
    componentWillUnmount() {
      this.deckStoreToken && this.deckStoreToken.remove();
    }

    /**
     * @override
     */
    render() {
      return (
        <div className="section">
          <Navbar dispatcher={this.props.dispatcher} me={this.props.me} selection="decks" isRibbon={true} />
          <div>
            <div className="container-fluid">
              <div className="row">
                {/* Statistics */}
                <div className="col-xs-3">
                  <DeckBuilderStatistics dispatcher={this.props.dispatcher} deck={this.props.deck} />
                  <h2 className="lato-N6" style={{ fontSize: '1.9rem', color: '#3e3e3e', textAlign: 'center' }}>
                    Legality
                  </h2>
                  <DeckLegalities deck={this.props.deck} isBuilding={true} />
                </div>

                {/* builder */}
                <div className="col-xs-9">
                  {/* body */}
                  <div className="row">
                    <div className="col-xs-12">
                      <DeckBuilderAdd
                        key="deckbuilder-add"
                        dispatcher={this.props.dispatcher}
                        pricingSource={this.props.me.get('preferences').get('pricing').get('source')}
                        deck={this.props.deck}
                      />
                      <div className="deckbuilder-grouping">
                        <div className="row">
                          <div className="col-xs-12 col-md-4">
                            <DeckBuilderGrouping
                              dispatcher={this.props.dispatcher}
                              deckGrouping={this.props.deckGrouping}
                            />
                          </div>
                          <div className="col-xs-12 col-md-4">
                            <DeckBuilderArrangement
                              dispatcher={this.props.dispatcher}
                              deckArranging={this.props.deckArranging}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-xs-12">
                      {this.props.deck.get('boards').map((board: DeckBoard) => (
                        <DeckBuilderBoard
                          key={board.get('id')}
                          me={this.props.me}
                          dispatcher={this.props.dispatcher}
                          deck={this.props.deck || new Deck()}
                          deckBoard={board}
                          deckGrouping={this.props.deckGrouping}
                          deckArranging={this.props.deckArranging}
                          mutable
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
  },
);
