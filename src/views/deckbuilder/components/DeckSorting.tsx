import sort from '@iconify/icons-ic/sort';
import * as React from 'react';
import * as DeckActions from '../../../actions/DeckActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { DeckSorting as Sorting, type DeckSortingKey } from '../../../models/sorting/DeckSorting';
import { IconSelect } from '../../components/IconSelect';

interface IProps {
  dispatcher: Dispatcher;
  sorting: Sorting;
}

interface IState {}

export class DeckSorting extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  /**
   * @override
   */
  componentDidMount() {}

  /**
   * @override
   */
  render() {
    return (
      <IconSelect className="icon-container--deck" icon={sort} value={this.props.sorting} onChange={this.onChange}>
        <>
          <option className="deck-select__option" disabled>
            Sort By
          </option>
          {Object.keys(Sorting).map((key) => (
            <option key={key} className="deck-sorting__select__option" value={Sorting[key as DeckSortingKey]}>
              {Sorting[key as DeckSortingKey]}
            </option>
          ))}
        </>
      </IconSelect>
    );
  }

  private onChange = (evt: React.SyntheticEvent<HTMLSelectElement>) => {
    evt.preventDefault();
    DeckActions.sorting(evt.currentTarget.value as Sorting, this.props.dispatcher);
  };
}
