import jsPDF from 'jspdf';

const Deck2PDF = function () {
  // Create a new pdf
  this.pdf = new jsPDF('portrait', 'pt', 'letter');
  this.mainBoardCount = 0;
  this.sideBoardCount = 0;

  // Add the logo
  this.pdf.addImage(this.getImageData(), 'PNG', 20, 5, 100, 100);

  // Create all the rectangles

  // Start with the top box, for deck designer, name, etc.
  this.pdf.setLineWidth(1);
  this.pdf.rect(135, 54, 441, 24); // date + event
  this.pdf.rect(135, 78, 441, 24); // location + deck name
  this.pdf.rect(355, 54, 221, 72); // event + deck name + deck designer
  this.pdf.rect(552, 30, 24, 24); // first letter
  this.pdf.rect(445, 30, 55, 24); // table number

  this.pdf.rect(27, 140, 24, 628); // last name + first name + dci
  this.pdf.rect(27, 140, 24, 270); // dci
  this.pdf.rect(27, 140, 24, 449); // first name + dci

  this.pdf.rect(250, 748, 56, 22); // total number main deck
  this.pdf.rect(524, 694, 56, 22); // total number side deck
  this.pdf.rect(320, 722, 260, 48); // judge box

  this.pdf.setLineWidth(0.5);
  this.pdf.rect(135, 54, 54, 48); // date + location
  this.pdf.rect(355, 54, 54, 72); // event + deck name + deck designer
  this.pdf.rect(320, 722, 130, 48); // official use + dc round + status + judge
  this.pdf.rect(320, 722, 260, 12); // official use + main/sb
  this.pdf.rect(320, 734, 260, 12); // dc round + dc round
  this.pdf.rect(320, 746, 260, 12); // status + status

  let y = 140;
  while (y < 380) {
    this.pdf.rect(27, y, 24, 24); // dci digits
    y = y + 24;
  }

  // Get all the various notes down on the page
  // There are a ton of them, so this will be exciting
  this.pdf.setFontSize(15);
  this.pdf.setFontStyle('bold');
  this.pdf.setFont('times'); // it's no Helvetica, that's for sure
  this.pdf.text('DECK REGISTRATION SHEET', 135, 45);

  this.pdf.setFontSize(13);
  this.pdf.text('PRINT CLEARLY USING ENGLISH CARD NAMES', 36, 121);

  this.pdf.setFontSize(13);
  this.pdf.text('Main Deck:', 62, 149);
  this.pdf.text('Main Deck Continued:', 336, 149);
  this.pdf.text('Sideboard:', 336, 404);

  this.pdf.setFontSize(11);
  this.pdf.text('# in deck:', 62, 166); // first row, main deck
  this.pdf.text('Card Name:', 122, 166);
  this.pdf.text('# in deck:', 336, 166); // second row, main deck
  this.pdf.text('Card Name:', 396, 166);
  this.pdf.text('# in deck:', 336, 420); // second row, sideboard
  this.pdf.text('Card Name:', 396, 420);
  this.pdf.text('Total Number of Cards in Main Deck:', 62, 768);
  this.pdf.text('Total Number of Cards in Sideboard:', 336, 714);

  this.pdf.setFontSize(7);
  this.pdf.setFontStyle('normal');
  this.pdf.text('Table', 421, 40);
  this.pdf.text('Number', 417, 48);
  this.pdf.text('First Letter of', 508, 40);
  this.pdf.text('Last Name', 516, 48);
  this.pdf.text('Date:', 169, 68);
  this.pdf.text('Event:', 387, 68);
  this.pdf.text('Location:', 158, 92);
  this.pdf.text('Deck Name:', 370, 92);
  this.pdf.text('Deck Designer:', 362, 116);
  this.pdf.text('First Name:', 41, 581, 90); // rotate
  this.pdf.text('Last Name:', 41, 760, 90);

  this.pdf.setFontStyle('italic');
  this.pdf.text('DCI #:', 41, 404, 90); // dci # is rotated and italic

  this.pdf.setFontSize(6);
  this.pdf.setFontStyle('normal');
  this.pdf.text('Deck Check Rd #:', 324, 742); // first row
  this.pdf.text('Status:', 324, 754);
  this.pdf.text('Judge:', 324, 766);

  this.pdf.text('Main/SB:', 454, 730); // second row
  this.pdf.text('/', 520, 730);
  this.pdf.text('Deck Check Rd #:', 454, 742);
  this.pdf.text('Status:', 454, 754);
  this.pdf.text('Judge:', 454, 766);

  this.pdf.setFontSize(5);
  this.pdf.setFontStyle('bold');
  this.pdf.text('FOR OFFICAL USE ONLY', 324, 730);

  // Now let's create a bunch of lines for putting cards on
  y = 186;
  while (y < 750) {
    // first column of lines
    this.pdf.line(62, y, 106, y);
    this.pdf.line(116, y, 306, y);
    y = y + 18;
  }

  y = 186;
  while (y < 386) {
    // second column of lines (main deck)
    this.pdf.line(336, y, 380, y);
    this.pdf.line(390, y, 580, y);
    y = y + 18;
  }

  y = 438;
  while (y < 696) {
    // second column of lines (main deck)
    this.pdf.line(336, y, 380, y);
    this.pdf.line(390, y, 580, y);
    y = y + 18;
  }

  this.pdf.setFont('helvetica');
  this.pdf.setFontSize(11);
  this.pdf.setFontStyle('normal');
};

Deck2PDF.prototype.downloadPDF = function (name?: string, page_number?: number) {
  let file_name = 'decklist';
  if (name) {
    if (page_number) {
      file_name = name + ' decklist page ' + page_number;
    } else {
      file_name = name + ' decklist';
    }
  }
  this.pdf.save(file_name + '.pdf');
};

Deck2PDF.prototype.addEventDetails = function (eventDate: any, eventLocation: any, eventName: any) {
  this.pdf.text(eventDate, 192, 69.5);
  this.pdf.text(eventLocation, 192, 93.5);
  this.pdf.text(eventName, 412, 69.5);
  this;
};

Deck2PDF.prototype.addDeckDetails = function (deckName: any, deckDesigner: any) {
  this.pdf.text(deckName, 412, 93.5);
  this.pdf.text(deckDesigner, 412, 117.5);
  this;
};

Deck2PDF.prototype.addPlayerDetails = function (playerFirstName: any, playerLastName: any, playerDci: any) {
  // put the first name into the PDF
  this.pdf.setFontStyle('bold');
  this.pdf.text(playerFirstName, 43, 544, 90);

  // put the last name into the PDF
  if (playerLastName.length > 0) {
    this.pdf.text(playerLastName, 43, 724, 90);

    this.pdf.setFontSize(20);

    // Getting the character perfectly aligned in the center of the box is super tricky, since it's hard to
    // get a glyph width.  So we manually fix some
    const lnfl = playerLastName.charAt(0);
    let offset = 0;

    switch (lnfl) {
      case 'I':
        offset = 4;
        break;
      case 'J':
        offset = 1;
        break;
      case 'M':
        offset = -1;
        break;
      case 'Q':
        offset = -1;
        break;
      case 'X':
        offset = 1;
        break;
      case 'Y':
        offset = 0.5;
        break;
      case 'W':
        offset = -2;
        break;
      case 'Z':
        offset = 1;
        break;
    }

    this.pdf.text(lnfl, 557 + offset, 49);
    this.pdf.setFontSize(12);
  }
  // put the DCI number into the PDF
  let y = 372;
  if (playerDci.length > 0) {
    for (let i = 0; i < playerDci.length; i++) {
      this.pdf.text(playerDci.charAt(i), 43, y, 90);
      y = y - 24;
    }
  }
};

Deck2PDF.prototype.addMainBoard = function (cards: any, total: any) {
  // Add the deck to the decklist
  let x = 82;
  let y = 182;
  this.pdf.setFontStyle('normal');

  if (Array.isArray(cards) && cards.length > 0) {
    this.mainBoardCount = total;
    for (let i = 0; i < cards.length; i++) {
      if (i === 32) {
        x = 356;
        y = 182;
      } // jump to the next row

      // Ignore zero quantity entries (blank)
      if (cards[i].count !== 0) {
        this.pdf.text(cards[i].count, x, y);
        this.pdf.text(cards[i].name, x + 38, y);
      }

      y = y + 18; // move to the next row
    }
  }
};

Deck2PDF.prototype.addSideBoard = function (cards: any, total: any) {
  // Add the sideboard to the decklist
  const x = 356;
  let y = 434;
  if (cards.length > 0) {
    this.sideBoardCount = total;
    for (let i = 0; i < cards.length; i++) {
      this.pdf.text(cards[i].count, x, y);
      this.pdf.text(cards[i].name, x + 38, y);
      y = y + 18; // move to the next row
    }
  }
};

Deck2PDF.prototype.addTotalCounts = function () {
  this.pdf.setFontSize(20);

  if (this.mainBoardCount !== 0) {
    this.pdf.text(String(this.mainBoardCount), 268, 766);
  }

  if (this.sideBoardCount !== 0) {
    if (this.sideBoardCount < 10) {
      this.pdf.text(String(this.sideBoardCount), 547, 712);
    } else {
      this.pdf.text(String(this.sideBoardCount), 541, 712);
    }
  }
};

Deck2PDF.prototype.getImageData = function () {
  return 'data:image/png;base64,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';
};

module.exports = Deck2PDF;
