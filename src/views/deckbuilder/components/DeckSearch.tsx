import * as Immutable from 'immutable';
import * as React from 'react';
import * as ReactDOM from 'react-dom';
// Actions and dispatcher
import * as DeckActions from '../../../actions/DeckActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
// Helpers
import * as FilterRegex from '../../../helpers/filter_regex';
import { ColorFilter } from '../../../models/filters/ColorFilter';
import { reduceToFilterState } from '../../../models/filters/FilterState';
// Models
import { Tag } from '../../../models/Tags';

interface IProps {
  dispatcher: Dispatcher;
  query: string;
}

interface IState {
  query: string;
  isFocus: boolean;
  suggestions: Array<string>;
  suggestionsIndex: number;
}

export class DeckSearch extends React.Component<IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: IProps) {
    super(props);
    this.state = {
      query: props.query,
      isFocus: false,
      suggestions: [],
      suggestionsIndex: -1,
    };
  }

  /**
   * @override
   */
  componentWillReceiveProps(props: IProps) {
    this.setState({
      query: props.query,
    });
  }

  /**
   * @override
   */
  render() {
    const query = this.state.query || '';
    const queryHtml = this.matches().reduce(
      (queryHtml, match) => queryHtml.replace(match, '<span class="deck-search__form__formatted-tag">$1</span>'),
      query,
    );

    return (
      <form
        className={`deck-search__form${this.state.isFocus ? ' is-focus' : ''}`}
        onClick={this.onClickForm}
        onSubmit={this.onSubmitQuery}
      >
        <div className={`deck-search__form__formatted-input`}>
          {!query || !query.length ? (
            <span>Search your decks</span>
          ) : (
            <span style={{ whiteSpace: 'pre-wrap' }} dangerouslySetInnerHTML={{ __html: queryHtml }} />
          )}
        </div>
        <input
          ref="input"
          className={`deck-search__form__input`}
          value={query}
          onChange={this.onChangeQuery}
          onFocus={this.onFocus}
          onKeyDown={this.onKeyDown}
        />

        {/* steal suggestion styles from the advanced search input */}
        {this.state.isFocus && this.state.suggestions && this.state.suggestions.length ? (
          <div className="advanced-search-input__suggestions">
            {(this.state.suggestions || []).map((suggestion, index) => (
              <div
                key={'suggestion-' + suggestion + '-' + index}
                className={`advanced-search-input__suggestion${
                  index === this.state.suggestionsIndex ? ' is-selected' : ''
                }`}
                onClick={this.onClickSuggestion.bind(this, suggestion)}
              >
                {suggestion}
              </div>
            ))}
          </div>
        ) : null}
      </form>
    );
  }

  private onClickForm = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    this.ignoreDocumentClick = true;
  };

  private onChangeQuery = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    evt.preventDefault();

    const query = evt.currentTarget.value || '';
    let matched = false;

    // TODO: autocomplete for tags, UI filters must handle active query filters
    // query.replace(this.matchTags, (m, p1, p2, p3, offset) => {
    //   if (this.validateSelectionStart(m, offset)) {
    //     request.get("/api/v3/deck_tags?query=" + m.split(":")[1]).end().then((res) => {
    //       this.setState({ suggestions: (res.body.deck_tags || []).map((tag: any) => tag.name) });
    //     }).catch((err) => {
    //       console.error(err);
    //       this.setState({ suggestions: [] });
    //     });
    //     matched = true;
    //   }
    //   return m;
    // });

    // autocomplete for colors
    query.replace(FilterRegex.matchColors, (m, p1, p2, p3, offset) => {
      if (this.validateSelectionStart(m, offset)) {
        this.setState({
          suggestions: ['white', 'blue', 'black', 'red', 'green', 'colorless'].filter(
            (suggestion) => suggestion.indexOf(m.split(':')[1]) >= 0,
          ),
        });
        matched = true;
      }
      return m;
    });

    // update state
    if (!matched) {
      this.setState({
        query: query,
        suggestions: [],
        suggestionsIndex: -1,
      });
    } else {
      this.setState({
        query: query,
        suggestionsIndex: -1,
      });
    }
  };

  private onSubmitQuery = (evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    evt.stopPropagation();

    const tags = Array<string>(); //this.extractFilters(this.state.query, this.matchTags, true);
    const notTags = Array<string>(); //this.extractFilters(this.state.query, this.matchTags, false);
    const colors = FilterRegex.extractFilters(this.state.query, FilterRegex.matchColors, true);
    const notColors = FilterRegex.extractFilters(this.state.query, FilterRegex.matchColors, false);

    let query = (this.state.query || '').trim();
    this.matches().forEach((match) => {
      {
        query = query.replace(match, '').trim();
      }
    });

    DeckActions.query(
      this.state.query,
      query,
      new ColorFilter({
        white: reduceToFilterState('white', colors, notColors),
        blue: reduceToFilterState('blue', colors, notColors),
        black: reduceToFilterState('black', colors, notColors),
        green: reduceToFilterState('green', colors, notColors),
        red: reduceToFilterState('red', colors, notColors),
      }),
      tags.reduce((accum, tag) => accum.set(tag, new Tag({ name: tag })), Immutable.Map<string, Tag>()),
      notTags.reduce((accum, tag) => accum.set(tag, new Tag({ name: tag })), Immutable.Map<string, Tag>()),
      this.props.dispatcher,
    );
  };

  private onKeyDown = (evt: React.KeyboardEvent<HTMLInputElement>) => {
    switch (evt.keyCode) {
      case 13: {
        // ENTER
        // replace query with suggestion
        let replaced = false;
        let query = this.state.query || '';
        this.matches().forEach((match) => {
          query = query.replace(match, (m, p1, p2, p3, offset) => {
            if (this.validateSelectionStart(m, offset)) {
              if (this.state.suggestions && this.state.suggestions.length) {
                replaced = true;
                const suggestion = this.state.suggestions[Math.max(this.state.suggestionsIndex!, 0)];
                return (
                  m.split(':')[0] + ':' + (suggestion.indexOf(' ') >= 0 ? '"' + suggestion + '"' : suggestion) + ' '
                );
              }
              return m;
            }
            return m;
          });
        });
        // if an item was replaced we should cancel the event
        if (replaced) {
          this.setState({
            query: query,
            suggestions: [],
            suggestionsIndex: -1,
          });
          evt.preventDefault();
          evt.stopPropagation();
        }
        break;
      }

      case 38: // UP
        evt.preventDefault();
        evt.stopPropagation();

        if (this.state.suggestions && this.state.suggestions.length) {
          this.setState({
            suggestionsIndex: Math.max(this.state.suggestionsIndex - 1, -1),
          });
        }
        break;

      case 40: // DOWN
        evt.preventDefault();
        evt.stopPropagation();

        if (this.state.suggestions && this.state.suggestions.length) {
          this.setState({
            suggestionsIndex: Math.min(this.state.suggestionsIndex + 1, this.state.suggestions.length - 1),
          });
        }
        break;
    }
  };

  private onClickSuggestion = (suggestion: string, evt: React.SyntheticEvent<HTMLElement>) => {
    evt.preventDefault();
    evt.stopPropagation();

    // replace query with suggestion
    let query = this.state.query || '';
    this.matches().forEach((match) => {
      query = query.replace(match, (m, p1, p2, p3, offset) => {
        if (this.validateSelectionStart(m, offset)) {
          return m.split(':')[0] + ':' + (suggestion.indexOf(' ') >= 0 ? '"' + suggestion + '"' : suggestion);
        }
        return m;
      });
    });

    this.setState({
      query: query,
      suggestions: [],
      suggestionsIndex: -1,
    });
    (ReactDOM.findDOMNode(this.refs.input) as HTMLInputElement).focus();

    this.ignoreDocumentClick = true;
  };

  private onFocus = (evt: React.SyntheticEvent<HTMLInputElement>) => {
    this.setState({
      isFocus: true,
    });
    document.addEventListener('click', this.handleDocumentClick);
  };

  private validateSelectionStart(replacement: string, offset: number) {
    const input = ReactDOM.findDOMNode(this.refs.input) as HTMLInputElement;
    return (
      input != null &&
      input.selectionStart != null &&
      input.selectionStart >= offset &&
      input.selectionStart <= offset + replacement.length
    );
  }

  private matches() {
    return [
      // this.matchTags,
      FilterRegex.matchColors,
    ];
  }

  private ignoreDocumentClick = false;
  private handleDocumentClick = () => {
    if (this.ignoreDocumentClick) {
      this.ignoreDocumentClick = false;
      return;
    }
    this.setState({
      isFocus: false,
    });
    document.removeEventListener('click', this.handleDocumentClick);
  };

  refs: {
    [key: string]: Element;
    input: HTMLInputElement;
  };
}
