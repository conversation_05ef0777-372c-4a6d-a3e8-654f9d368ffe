import fact_check from '@iconify/icons-ic/baseline-fact-check';
// Iconify
import { Icon } from '@iconify/react';
import * as Immutable from 'immutable';
import * as React from 'react';
// Actions
import * as DeckActions from '../../../actions/DeckActions';
import * as MessageActions from '../../../actions/MessageActions';
import * as SubscriptionActions from '../../../actions/SubscriptionActions';
// Containers
import * as HasDispatcher from '../../../containers/HasDispatcher';
import * as HasMe from '../../../containers/HasMe';
import * as HasSubscription from '../../../containers/HasSubscription';
import { CoreAssets } from '../../../helpers/core_assets';
// Helpers
import displayCurrency from '../../../helpers/currency_helper';
import { TextFormat } from '../../../helpers/fmt';
import track from '../../../helpers/ga_helper';
import history from '../../../helpers/history';
import { Arranging } from '../../../models/Arranging';
import { CartLocation } from '../../../models/CartButton';
// Models
import { CartCardRecord } from '../../../models/CartCards';
import { Deck, DeckBoard, IDeck } from '../../../models/Decks';
import { Grouping } from '../../../models/Grouping';
import { Legality, type LegalityKey } from '../../../models/Legality';
import { Tag } from '../../../models/Tags';
import { CartButton } from '../../components/CartButton';
import { Checkbox } from '../../components/Checkbox';
import { Dialog } from '../../components/Dialog';
import { EditableTitle } from '../../components/EditableTitle';
import { IconSelect } from '../../components/IconSelect';
import { TagsBuilder } from '../../components/TagsBuilder';
import { OwnershipStatus } from '../Deck';
import { DeckBuilderArrangement } from './DeckBuilderArrangement';
// Components
import { DeckBuilderBoard } from './DeckBuilderBoard';
import { DeckBuilderGrouping } from './DeckBuilderGrouping';
import { DeckBuilderStatistics } from './DeckBuilderStatistics';
import { DeckLegalities } from './DeckLegalities';
import { DeckShareLink } from './DeckShareLink';

interface IProps {
  accessState: OwnershipStatus;
  deck: Deck;
  deckGrouping: Grouping;
  deckArranging: Arranging;
  onDuplicate: (uuid: string) => void;
}

interface IState {
  deck: Deck;
  isRemoving: boolean;
  isShowingLegal: boolean;
}

export const DeckViewer = HasMe.Attach<IProps & HasDispatcher.IProps>(
  HasSubscription.Attach<IProps & HasMe.IProps & HasDispatcher.IProps>(
    class extends React.Component<IProps & HasSubscription.IProps & HasMe.IProps & HasDispatcher.IProps, IState> {
      /**
       * @override
       * @constructor
       */
      constructor(props: IProps & HasSubscription.IProps & HasMe.IProps & HasDispatcher.IProps) {
        super(props);
        this.state = {
          deck: props.deck,
          isRemoving: false,
          isShowingLegal: false,
        };
      }
      /**
       * @override
       */
      async componentDidMount() {
        if (this.props.accessState !== OwnershipStatus.NO_USER) {
          await SubscriptionActions.subscription(this.props.dispatcher);
        }
      }

      /**
       * @override
       */
      componentWillReceiveProps(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
        this.setState({
          deck: props.deck,
        });
      }

      /**
       * @override
       */
      render() {
        return (
          <div className="container-fluid">
            <div className="deck-viewer">
              <Dialog
                isOpen={this.state.isRemoving || false}
                onDismiss={() => {
                  this.setState({ isRemoving: false });
                }}
              >
                <h3 className="query-dialog-heading">Are you sure?</h3>
                <p className="query-dialog-subheading">
                  This deck will be deleted but your collection will remain unaffected. This decision cannot be
                  reversed!
                </p>
                <div className="flex justify-center">
                  <input
                    type="submit"
                    className="button-alert"
                    value="Remove"
                    autoComplete="off"
                    onClick={this.onConfirmRemoveDeck}
                  />
                </div>
              </Dialog>

              {/* heading */}
              <div className="row">
                <div className="col-xs-12">
                  <EditableTitle
                    title={this.state.deck.get('name')}
                    enabled={this.props.accessState === OwnershipStatus.OWNED}
                    onSubmitTitle={this.onSubmitName}
                  />
                </div>
                {/* divider */}
                <div
                  className="col-xs-12"
                  style={{ content: '""', height: '1px', backgroundColor: '#CDCDCD', margin: '1rem 0 3rem 0' }}
                />
              </div>

              {/* deck summary */}
              <div className="row">
                <div className="col-xs-12">
                  <div className="row">
                    {/* image */}
                    <div className="col-xs-12 col-xs-3 col-sm-3 col-md-3 col-lg-3">
                      <div className="deck-tile__image-wrapper">
                        <div
                          className="deck-tile__image-wrapper__image"
                          style={{
                            backgroundImage: this.props.deck.get('image')
                              ? `url(${CoreAssets.imageHost()}/card_art_hq/${this.props.deck.get('image')}.jpg)`
                              : 'url(/images/deck-avatar-default.jpg)',
                          }}
                        />
                      </div>
                      <div className="deck-tile__colors">
                        {['white', 'blue', 'black', 'red', 'green'].map((color: string) => {
                          <div
                            className={`deck-tile__colors__color is-${color} ${
                              this.props.deck.get(`color${TextFormat.capitalizeWord(color)}` as keyof IDeck) ? 'on' : ''
                            }`}
                          />;
                        })}

                        <div
                          className={`deck-tile__colors__color is-white ${
                            this.props.deck.get('colorWhite') ? 'on' : ''
                          }`}
                        />
                        <div
                          className={`deck-tile__colors__color is-blue ${this.props.deck.get('colorBlue') ? 'on' : ''}`}
                        />
                        <div
                          className={`deck-tile__colors__color is-black ${
                            this.props.deck.get('colorBlack') ? 'on' : ''
                          }`}
                        />
                        <div
                          className={`deck-tile__colors__color is-red ${this.props.deck.get('colorRed') ? 'on' : ''}`}
                        />
                        <div
                          className={`deck-tile__colors__color is-green ${
                            this.props.deck.get('colorGreen') ? 'on' : ''
                          }`}
                        />
                      </div>
                      <div className="center-row" style={{ marginTop: '1rem' }}>
                        <button
                          className="button-primary"
                          onClick={this.onClickDuplicateDeck}
                          style={{ marginBottom: '1rem' }}
                        >
                          Duplicate
                        </button>
                        {this.props.accessState === OwnershipStatus.OWNED ? (
                          <button
                            className="button-alert"
                            onClick={this.onClickRemoveDeck}
                            style={{ margin: '0 0 1rem 1rem' }}
                          >
                            Delete
                          </button>
                        ) : null}
                      </div>
                      <div className="center-row">
                        <CartButton
                          cartData={CartCardRecord.mapFromDeck(this.state.deck)}
                          location={CartLocation.DECKVIEWER}
                          isMerchant={this.props.subscription.isMerchant()}
                          dispatcher={this.props.dispatcher}
                          linkedCards={this.props.deck.getLinkedCardsForCardKingdom()}
                        />
                      </div>
                      <DeckBuilderStatistics dispatcher={this.props.dispatcher} deck={this.state.deck} />
                      <h2 className="lato-N6" style={{ fontSize: '1.9rem', color: '#3e3e3e', textAlign: 'center' }}>
                        Legality
                      </h2>
                      <DeckLegalities deck={this.state.deck} isBuilding={true} />
                    </div>
                    <div className="col-xs-9">
                      <div className="col-xs-12">
                        <div className="deckbuilder-row">
                          <div className="col-xs-12 col-md-3">
                            <div className="deckbuilder-info-container">
                              <div className="deckbuilder-info-heading">Price</div>
                              <div className="deckbuilder-info">
                                {this.props.deck.get('price')
                                  ? displayCurrency(
                                      this.props.deck.get('price'),
                                      true,
                                      this.props.me.get('preferences').get('localization').get('currency'),
                                    )
                                  : '-'}
                              </div>
                            </div>
                          </div>
                          <div className="col-xs-12 col-md-3">
                            <div className="deckbuilder-info-container">
                              <div className="deckbuilder-info-heading">Total Cards</div>
                              <div className="deckbuilder-info">{this.props.deck.get('numCards').toString()}</div>
                            </div>
                          </div>
                          <div className="col-xs-12 col-md-3">
                            <div className="deckbuilder-info-container">
                              <div className="deckbuilder-info-heading">Format</div>
                              {this.props.accessState === OwnershipStatus.OWNED ? (
                                <IconSelect
                                  className="icon-container--deck"
                                  icon={fact_check}
                                  value={this.props.deck.get('legality')}
                                  onChange={this.onChangeLegality}
                                >
                                  <>
                                    <option className="deck-select__option" disabled>
                                      Format
                                    </option>
                                    {Object.keys(Legality).map((key) => (
                                      <option
                                        key={key}
                                        className="deck-select__option"
                                        value={Legality[key as LegalityKey]}
                                      >
                                        {TextFormat.capitalize(Legality[key as LegalityKey])}
                                      </option>
                                    ))}
                                  </>
                                </IconSelect>
                              ) : (
                                <>
                                  <Icon width={'18px'} height={'18px'} icon={fact_check} />
                                  <div className="deckbuilder-info">
                                    {TextFormat.capitalize(this.props.deck.get('legality'))}
                                  </div>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        {this.props.accessState === OwnershipStatus.OWNED ? (
                          <div className="deckbuilder-row">
                            <div className="col-xs-12 col-md-1">
                              <div className="deckbuilder-info-container">
                                <div className="deckbuilder-info-heading">Public</div>
                                <Checkbox checked={this.props.deck.get('isPublic')} onChange={this.onChangePublicity} />
                              </div>
                            </div>
                            {this.props.deck.get('isPublic') ? (
                              <>
                                <div className="flex col-xs-12 col-md-11" style={{ marginTop: '1.5rem' }}>
                                  <DeckShareLink
                                    href={`/decks/${this.props.deck.get('uuid')}`}
                                    urlText={`${window.location.hostname}/decks/${this.props.deck.get('uuid')}`}
                                  />
                                </div>
                              </>
                            ) : null}
                          </div>
                        ) : null}
                        <div className="deckbuilder-row">
                          <div className="col-xs-12 col-md-8">
                            <div className="deckbuilder-info-container">
                              <div className="deckbuilder-info-heading">Tags</div>
                              <TagsBuilder
                                dispatcher={this.props.dispatcher}
                                me={this.props.me}
                                tags={this.props.deck.get('tags').toOrderedSet()}
                                userTags={Immutable.OrderedSet<any>()}
                                onAdd={this.onAddTag}
                                onRemove={this.onRemoveTag}
                                publicViewing={false}
                                isTaggingDeck
                                placeholder="No tags"
                                immutable={this.props.accessState !== OwnershipStatus.OWNED}
                                background={true}
                              />
                            </div>
                          </div>
                        </div>
                        <div className="deckbuilder-row">
                          <div className="col-xs-12 col-md-4">
                            <DeckBuilderGrouping
                              dispatcher={this.props.dispatcher}
                              deckGrouping={this.props.deckGrouping}
                            />
                          </div>
                          <div className="col-xs-12 col-md-4">
                            <DeckBuilderArrangement
                              dispatcher={this.props.dispatcher}
                              deckArranging={this.props.deckArranging}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="col-xs-12">
                        {this.state.deck.get('boards').map((board: DeckBoard) => (
                          <DeckBuilderBoard
                            key={board.get('id')}
                            me={this.props.me}
                            dispatcher={this.props.dispatcher}
                            deck={this.state.deck}
                            deckBoard={board}
                            deckArranging={this.props.deckArranging}
                            deckGrouping={this.props.deckGrouping}
                            linkable={this.props.accessState === OwnershipStatus.OWNED}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      }

      private onChangePublicity = (checked: boolean) => {
        if (this.props.accessState === OwnershipStatus.OWNED) {
          track('decks', 'edit', 'publicity');
          DeckActions.updateDeck(this.props.deck.set('isPublic', checked), this.props.dispatcher);
        }
      };

      private onChangeLegality = (evt: React.SyntheticEvent<HTMLSelectElement>) => {
        evt.preventDefault();
        if (this.props.accessState === OwnershipStatus.OWNED) {
          track('decks', 'edit', 'legality');
          DeckActions.updateDeck(
            this.props.deck.set('legality', evt.currentTarget.value as Legality),
            this.props.dispatcher,
          );
        }
      };

      private onSubmitName = (newName: string) => {
        DeckActions.updateDeck(this.state.deck.set('name', newName), this.props.dispatcher);
      };

      private onAddTag = (tag: Tag) => {
        track('decks', 'edit', 'tag');
        DeckActions.addTag(this.props.deck, tag, this.props.dispatcher);
      };

      private onRemoveTag = (tag: Tag) => {
        track('decks', 'edit', 'tag');
        DeckActions.removeTag(this.props.deck, tag, this.props.dispatcher);
      };

      private onClickRemoveDeck = (evt: React.SyntheticEvent<HTMLElement>) => {
        evt.preventDefault();
        this.setState({
          isRemoving: true,
        });
      };

      private onClickDuplicateDeck = (evt: React.SyntheticEvent<HTMLElement>) => {
        evt.preventDefault();
        this.props.onDuplicate(this.props.deck.get('uuid'));
      };

      private onConfirmRemoveDeck = async (evt: React.SyntheticEvent<HTMLElement>) => {
        evt.preventDefault();
        track('decks', 'remove');
        this.setState({
          isRemoving: false,
        });

        try {
          await DeckActions.removeDeck(this.props.deck, this.props.dispatcher);
          history.push('/decks');
        } catch (err) {
          if (err.status === 404) {
            return MessageActions.error("The deck you're removing could not be found", this.props.dispatcher);
          }

          MessageActions.error('Something went wrong, please try again later', this.props.dispatcher);
        }
      };
    },
  ),
);
