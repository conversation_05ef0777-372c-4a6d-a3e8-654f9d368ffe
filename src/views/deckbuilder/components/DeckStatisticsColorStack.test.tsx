import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { TextFormat } from '../../../helpers/fmt';
import { DeckStatisticsColorStack } from './DeckStatisticsColorStack';

const COLOR_MAP = {
  white: '#FFE48B',
  blue: '#45C3FF',
  black: '#676767',
  red: '#FF5C35',
  green: '#1AC655',
  multi: '#EDAE49',
  colorless: '#C5C5C5',
} as const;

// Mock Plotly
const mockNewPlot = vi.hoisted(() => vi.fn());
const mockPurge = vi.hoisted(() => vi.fn());

vi.mock('../../../helpers/plotly', () => ({
  default: () => ({
    newPlot: mockNewPlot,
    purge: mockPurge,
  }),
}));

// Mock serverside helper
vi.mock('../../../helpers/serverside', () => ({
  default: vi.fn(() => false),
}));

type DeckStatisticsColorStackProps = React.ComponentProps<typeof DeckStatisticsColorStack>;

const createMockStatistics = (): Immutable.Map<string, Immutable.Map<number, number>> => {
  return Immutable.Map({
    white: Immutable.Map<number, number>([
      [1, 5],
      [2, 3],
      [3, 2],
    ]),
    blue: Immutable.Map<number, number>([
      [1, 4],
      [2, 6],
      [3, 1],
    ]),
    black: Immutable.Map<number, number>([
      [1, 2],
      [2, 4],
      [3, 3],
    ]),
    red: Immutable.Map<number, number>([
      [1, 6],
      [2, 2],
      [3, 1],
    ]),
    green: Immutable.Map<number, number>([
      [1, 3],
      [2, 5],
      [3, 4],
    ]),
    multi: Immutable.Map<number, number>([
      [1, 1],
      [2, 2],
      [3, 1],
    ]),
    colorless: Immutable.Map<number, number>([
      [1, 2],
      [2, 1],
      [3, 0],
    ]),
  });
};

const defaultProps: Omit<DeckStatisticsColorStackProps, 'dispatcher'> = {
  title: 'Mana Curve',
  statistics: createMockStatistics(),
  mini: false,
};

const renderDeckStatisticsColorStack = (props: Partial<DeckStatisticsColorStackProps> = {}) => {
  return renderWithDispatcher(DeckStatisticsColorStack, { ...defaultProps, ...props });
};

describe('DeckStatisticsColorStack', () => {
  describe('when component renders', () => {
    it('applies correct styling for normal size', () => {
      const { container } = renderDeckStatisticsColorStack({ mini: false });
      const chartContainer = container.firstChild as HTMLElement;

      expect(chartContainer).toHaveStyle({
        position: 'relative',
        width: '100%',
        minWidth: '0',
        maxWidth: '100%',
        height: '30rem',
        maxHeight: '30rem',
      });
    });

    it('applies correct styling for mini size', () => {
      const { container } = renderDeckStatisticsColorStack({ mini: true });
      const chartContainer = container.firstChild as HTMLElement;

      expect(chartContainer).toHaveStyle({
        position: 'relative',
        width: '100%',
        minWidth: '350px',
        maxWidth: '350px',
        height: '30rem',
        maxHeight: '300px',
      });
    });
  });

  describe('chart rendering', () => {
    it('calls Plotly.newPlot on mount', () => {
      renderDeckStatisticsColorStack();

      expect(mockNewPlot).toHaveBeenCalledWith(
        expect.any(HTMLElement),
        expect.any(Array),
        expect.any(Object),
        expect.any(Object),
      );
    });

    it('calls Plotly.newPlot on update', () => {
      const { rerenderWithDispatcher } = renderDeckStatisticsColorStack();

      rerenderWithDispatcher({
        statistics: Immutable.Map({
          white: Immutable.Map<number, number>([[1, 10]]),
          blue: Immutable.Map<number, number>([[1, 5]]),
          black: Immutable.Map<number, number>([[1, 3]]),
          red: Immutable.Map<number, number>([[1, 2]]),
          green: Immutable.Map<number, number>([[1, 1]]),
          multi: Immutable.Map<number, number>([[1, 0]]),
          colorless: Immutable.Map<number, number>([[1, 0]]),
        }),
      });

      expect(mockNewPlot).toHaveBeenCalled();
    });

    it('creates data for all color categories', () => {
      renderDeckStatisticsColorStack();

      const [, data] = mockNewPlot.mock.calls[0];
      expect(data).toHaveLength(7); // white, blue, black, red, green, multi, colorless
    });

    it('applies correct bar chart configuration', () => {
      renderDeckStatisticsColorStack();

      const [, data] = mockNewPlot.mock.calls[0];
      data.forEach((series: any) => {
        expect(series.type).toBe('bar');
        expect(series).toHaveProperty('x');
        expect(series).toHaveProperty('y');
        expect(series).toHaveProperty('name');
        expect(series).toHaveProperty('marker');
      });
    });

    it('capitalizes color names correctly', () => {
      renderDeckStatisticsColorStack();

      const [, data] = mockNewPlot.mock.calls[0];
      const names = data.map((series: any) => series.name);

      Object.keys(COLOR_MAP).forEach((key) => {
        expect(names).toContain(TextFormat.capitalize(key));
      });
    });

    it('applies correct layout configuration', () => {
      renderDeckStatisticsColorStack({ title: 'Test Chart' });

      const [, , layout] = mockNewPlot.mock.calls[0];
      expect(layout).toMatchObject({
        title: 'Test Chart',
        showlegend: false,
        barmode: 'stack',
        font: {
          family: 'lato-bold',
          size: 18,
          color: '#3e3e3e',
        },
      });
    });

    it('configures x-axis correctly for normal size', () => {
      renderDeckStatisticsColorStack({ mini: false });

      const [, , layout] = mockNewPlot.mock.calls[0];
      expect(layout.xaxis).toMatchObject({
        fixedrange: true,
        tick0: 0,
        dtick: 1,
      });
    });

    it('configures x-axis correctly for mini size', () => {
      renderDeckStatisticsColorStack({ mini: true });

      const [, , layout] = mockNewPlot.mock.calls[0];
      expect(layout.xaxis).toMatchObject({
        fixedrange: true,
        tick0: 0,
        dtick: 5,
      });
    });

    it('configures y-axis correctly for normal size', () => {
      renderDeckStatisticsColorStack({ mini: false });

      const [, , layout] = mockNewPlot.mock.calls[0];
      expect(layout.yaxis).toMatchObject({
        fixedrange: true,
        tickmode: 'auto',
        tick0: 0,
        dtick: 2,
        nticks: 16,
        rangemode: 'tozero',
        autorange: true,
      });
    });

    it('configures y-axis correctly for mini size', () => {
      renderDeckStatisticsColorStack({ mini: true });

      const [, , layout] = mockNewPlot.mock.calls[0];
      expect(layout.yaxis).toMatchObject({
        fixedrange: true,
        tickmode: 'auto',
        tick0: 0,
        dtick: 4,
        nticks: 8,
        rangemode: 'tozero',
        autorange: true,
      });
    });

    it('applies correct config options', () => {
      renderDeckStatisticsColorStack();

      const [, , , config] = mockNewPlot.mock.calls[0];
      expect(config).toMatchObject({
        staticPlot: false,
        editable: false,
        scrollZoom: false,
        showTips: false,
        showLink: false,
        sendData: false,
        showSources: false,
        displayModeBar: false,
        modeBarButtons: false,
        logging: false,
      });
    });
  });

  describe('color mapping', () => {
    it('applies correct colors for each mana color', () => {
      renderDeckStatisticsColorStack();

      const [, data] = mockNewPlot.mock.calls[0];
      const colorMap = data.reduce((map: any, series: any) => {
        map[series.name.toLowerCase()] = series.marker.color;
        return map;
      }, {});

      Object.keys(COLOR_MAP).forEach((key: keyof typeof COLOR_MAP) => {
        expect(colorMap[key]).toBe(COLOR_MAP[key]);
      });
    });
  });

  describe('data processing', () => {
    it('extracts correct x and y values from statistics', () => {
      const statistics = Immutable.Map({
        white: Immutable.Map<number, number>([
          [1, 5],
          [2, 3],
          [3, 2],
        ]),
        blue: Immutable.Map<number, number>([
          [1, 4],
          [2, 6],
          [3, 1],
        ]),
        black: Immutable.Map<number, number>([
          [1, 2],
          [2, 4],
          [3, 3],
        ]),
        red: Immutable.Map<number, number>([
          [1, 6],
          [2, 2],
          [3, 1],
        ]),
        green: Immutable.Map<number, number>([
          [1, 3],
          [2, 5],
          [3, 4],
        ]),
        multi: Immutable.Map<number, number>([
          [1, 1],
          [2, 2],
          [3, 1],
        ]),
        colorless: Immutable.Map<number, number>([
          [1, 2],
          [2, 1],
          [3, 0],
        ]),
      });

      renderDeckStatisticsColorStack({ statistics });

      const [, data] = mockNewPlot.mock.calls[0];
      const whiteSeries = data.find((series: any) => series.name === 'White');

      expect(whiteSeries.x).toEqual([1, 2, 3]);
      expect(whiteSeries.y).toEqual([5, 3, 2]);
    });

    it('calculates correct x-axis range based on max count', () => {
      const statistics = Immutable.Map({
        white: Immutable.Map<number, number>([
          [1, 5],
          [2, 3],
          [10, 2],
        ]), // max key is 10
        blue: Immutable.Map<number, number>([
          [1, 4],
          [2, 6],
          [3, 1],
        ]),
        black: Immutable.Map<number, number>([
          [1, 2],
          [2, 4],
          [3, 3],
        ]),
        red: Immutable.Map<number, number>([
          [1, 6],
          [2, 2],
          [3, 1],
        ]),
        green: Immutable.Map<number, number>([
          [1, 3],
          [2, 5],
          [3, 4],
        ]),
        multi: Immutable.Map<number, number>([
          [1, 1],
          [2, 2],
          [3, 1],
        ]),
        colorless: Immutable.Map<number, number>([
          [1, 2],
          [2, 1],
          [3, 0],
        ]),
      });

      renderDeckStatisticsColorStack({ statistics });

      const [, , layout] = mockNewPlot.mock.calls[0];
      expect(layout.xaxis.range).toEqual([-0.5, 10.5]);
    });
  });

  describe('window resize handling', () => {
    it('removes resize event listener on unmount', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');

      const { unmount } = renderDeckStatisticsColorStack();
      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function));
    });

    it('purges and re-renders chart on resize', () => {
      renderDeckStatisticsColorStack({ title: 'Resize Test' });

      // Simulate window resize
      const resizeEvent = new Event('resize');
      window.dispatchEvent(resizeEvent);

      expect(mockPurge).toHaveBeenCalledWith('Resize Test');
      expect(mockNewPlot).toHaveBeenCalledTimes(2); // Once on mount, once on resize
    });
  });

  describe('serverside rendering', () => {
    it('skips chart rendering on server side', async () => {
      // Mock serverside to return true
      const serversideMock = await import('../../../helpers/serverside');
      vi.mocked(serversideMock.default).mockReturnValue(true);

      renderDeckStatisticsColorStack();

      expect(mockNewPlot).not.toHaveBeenCalled();

      // Reset mock
      vi.mocked(serversideMock.default).mockReturnValue(false);
    });
  });
});
