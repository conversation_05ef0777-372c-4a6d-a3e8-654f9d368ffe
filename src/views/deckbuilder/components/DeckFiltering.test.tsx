import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { Legality } from '../../../models/Legality';
import { DeckFiltering } from './DeckFiltering';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockLegality = vi.mocked(DeckActions.legality);

// Mock TextFormat
vi.mock('../../../helpers/fmt', () => ({
  TextFormat: {
    capitalize: vi.fn((str: string) => str.charAt(0).toUpperCase() + str.slice(1)),
  },
}));

type DeckFilteringProps = React.ComponentProps<typeof DeckFiltering>;

const defaultProps: Omit<DeckFilteringProps, 'dispatcher'> = {
  legality: Legality.STANDARD,
};

const renderDeckFiltering = (props: Partial<DeckFilteringProps> = {}) => {
  return renderWithDispatcher(DeckFiltering, { ...defaultProps, ...props });
};

describe('DeckFiltering', () => {
  beforeEach(() => {
    mockLegality.mockClear();
  });

  describe('when component renders', () => {
    it('displays the icon select container', () => {
      const { container } = renderDeckFiltering();
      expect(container.querySelector('.icon-container--deck')).toBeInTheDocument();
    });

    it('displays the select dropdown with current legality value', () => {
      renderDeckFiltering({ legality: Legality.MODERN });
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
      expect(select).toHaveValue(Legality.MODERN);
    });

    it('displays disabled "Format" option', () => {
      renderDeckFiltering();
      const disabledOption = screen.getByRole('option', { name: 'Format' });
      expect(disabledOption).toBeInTheDocument();
      expect(disabledOption).toBeDisabled();
    });

    it('displays "All" option', () => {
      renderDeckFiltering();
      const allOption = screen.getByRole('option', { name: 'All' });
      expect(allOption).toBeInTheDocument();
      expect(allOption).not.toBeDisabled();
    });

    it('displays all legality options', () => {
      renderDeckFiltering();
      
      // Check that all legality options are present
      Object.values(Legality).forEach((legalityValue) => {
        const capitalizedLegality = legalityValue.charAt(0).toUpperCase() + legalityValue.slice(1);
        const option = screen.getByRole('option', { name: capitalizedLegality });
        expect(option).toBeInTheDocument();
      });
    });

    it('applies correct CSS classes', () => {
      const { container } = renderDeckFiltering();
      
      expect(container.querySelector('.icon-container--deck')).toBeInTheDocument();
      
      const selectOptions = container.querySelectorAll('.deck-select__option');
      expect(selectOptions.length).toBeGreaterThan(0);
    });
  });

  describe('legality options', () => {
    it('includes standard option', () => {
      renderDeckFiltering();
      expect(screen.getByRole('option', { name: 'Standard' })).toBeInTheDocument();
    });

    it('includes modern option', () => {
      renderDeckFiltering();
      expect(screen.getByRole('option', { name: 'Modern' })).toBeInTheDocument();
    });

    it('includes legacy option', () => {
      renderDeckFiltering();
      expect(screen.getByRole('option', { name: 'Legacy' })).toBeInTheDocument();
    });

    it('includes vintage option', () => {
      renderDeckFiltering();
      expect(screen.getByRole('option', { name: 'Vintage' })).toBeInTheDocument();
    });

    it('includes commander option', () => {
      renderDeckFiltering();
      expect(screen.getByRole('option', { name: 'Commander' })).toBeInTheDocument();
    });

    it('includes freeform option', () => {
      renderDeckFiltering();
      expect(screen.getByRole('option', { name: 'Freeform' })).toBeInTheDocument();
    });

    it('capitalizes legality names correctly', () => {
      renderDeckFiltering();
      
      // TextFormat.capitalize should be called for each legality
      Object.values(Legality).forEach((legalityValue) => {
        const capitalizedLegality = legalityValue.charAt(0).toUpperCase() + legalityValue.slice(1);
        expect(screen.getByRole('option', { name: capitalizedLegality })).toBeInTheDocument();
      });
    });
  });

  describe('when user interacts with legality selector', () => {
    it('calls DeckActions.legality when selection changes', () => {
      const { dispatcher } = renderDeckFiltering();
      const select = screen.getByRole('combobox');
      
      fireEvent.change(select, { target: { value: Legality.MODERN } });
      
      expect(mockLegality).toHaveBeenCalledWith(dispatcher, Legality.MODERN);
    });

    it('calls DeckActions.legality with undefined when "All" is selected', () => {
      const { dispatcher } = renderDeckFiltering();
      const select = screen.getByRole('combobox');
      
      fireEvent.change(select, { target: { value: 'All' } });
      
      expect(mockLegality).toHaveBeenCalledWith(dispatcher, undefined);
    });

    it('prevents default event behavior when selection changes', () => {
      renderDeckFiltering();
      const select = screen.getByRole('combobox');
      
      const changeEvent = new Event('change', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(changeEvent, 'preventDefault');
      
      fireEvent(select, changeEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('handles different legality values correctly', () => {
      const { dispatcher } = renderDeckFiltering();
      const select = screen.getByRole('combobox');
      
      // Test changing to different legality values
      fireEvent.change(select, { target: { value: Legality.LEGACY } });
      expect(mockLegality).toHaveBeenCalledWith(dispatcher, Legality.LEGACY);
      
      fireEvent.change(select, { target: { value: Legality.VINTAGE } });
      expect(mockLegality).toHaveBeenCalledWith(dispatcher, Legality.VINTAGE);
      
      fireEvent.change(select, { target: { value: Legality.COMMANDER } });
      expect(mockLegality).toHaveBeenCalledWith(dispatcher, Legality.COMMANDER);
    });
  });

  describe('when component has different initial legality values', () => {
    it('renders with STANDARD legality selected', () => {
      renderDeckFiltering({ legality: Legality.STANDARD });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Legality.STANDARD);
    });

    it('renders with MODERN legality selected', () => {
      renderDeckFiltering({ legality: Legality.MODERN });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Legality.MODERN);
    });

    it('renders with LEGACY legality selected', () => {
      renderDeckFiltering({ legality: Legality.LEGACY });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Legality.LEGACY);
    });

    it('renders with VINTAGE legality selected', () => {
      renderDeckFiltering({ legality: Legality.VINTAGE });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Legality.VINTAGE);
    });

    it('renders with COMMANDER legality selected', () => {
      renderDeckFiltering({ legality: Legality.COMMANDER });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Legality.COMMANDER);
    });

    it('renders with FREEFORM legality selected', () => {
      renderDeckFiltering({ legality: Legality.FREEFORM });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Legality.FREEFORM);
    });
  });

  describe('component lifecycle', () => {
    it('initializes with empty state', () => {
      renderDeckFiltering();
      
      // Component should initialize without errors
      const { container } = renderDeckFiltering();
      expect(container.querySelector('.icon-container--deck')).toBeInTheDocument();
    });

    it('has empty componentDidMount implementation', () => {
      // This test ensures the component mounts without errors
      const { container } = renderDeckFiltering();
      expect(container.querySelector('.icon-container--deck')).toBeInTheDocument();
    });
  });

  describe('IconSelect integration', () => {
    it('passes correct props to IconSelect', () => {
      const { container } = renderDeckFiltering({ legality: Legality.MODERN });
      
      // IconSelect should receive the correct className, icon, value, and onChange
      const iconSelect = container.querySelector('.icon-container--deck');
      expect(iconSelect).toBeInTheDocument();
      
      // The select should have the correct value
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Legality.MODERN);
    });

    it('renders fact check icon', () => {
      const { container } = renderDeckFiltering();
      
      // Should render the fact check icon (this would be tested more thoroughly in IconSelect tests)
      const iconContainer = container.querySelector('.icon-container--deck');
      expect(iconContainer).toBeInTheDocument();
    });
  });

  describe('option rendering', () => {
    it('generates unique keys for options', () => {
      renderDeckFiltering();
      
      // Each option should have a unique key (tested indirectly by ensuring no React warnings)
      const options = screen.getAllByRole('option');
      expect(options.length).toBeGreaterThan(1); // At least the disabled option plus legality options
    });

    it('uses correct option values', () => {
      renderDeckFiltering();
      
      Object.values(Legality).forEach((legalityValue) => {
        const capitalizedLegality = legalityValue.charAt(0).toUpperCase() + legalityValue.slice(1);
        const option = screen.getByRole('option', { name: capitalizedLegality });
        expect(option).toHaveValue(legalityValue);
      });
    });

    it('applies correct CSS classes to options', () => {
      const { container } = renderDeckFiltering();
      
      const selectOptions = container.querySelectorAll('.deck-select__option');
      expect(selectOptions.length).toBe(Object.keys(Legality).length + 2); // +2 for disabled "Format" and "All" options
    });
  });

  describe('accessibility', () => {
    it('provides proper select element', () => {
      renderDeckFiltering();
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
    });

    it('provides proper option elements', () => {
      renderDeckFiltering();
      const options = screen.getAllByRole('option');
      expect(options.length).toBe(Object.keys(Legality).length + 2); // +2 for disabled "Format" and "All" options
    });

    it('marks disabled option correctly', () => {
      renderDeckFiltering();
      const disabledOption = screen.getByRole('option', { name: 'Format' });
      expect(disabledOption).toBeDisabled();
    });

    it('does not mark "All" option as disabled', () => {
      renderDeckFiltering();
      const allOption = screen.getByRole('option', { name: 'All' });
      expect(allOption).not.toBeDisabled();
    });
  });

  describe('edge cases', () => {
    it('handles undefined legality value', () => {
      // This would represent the "All" state
      const { container } = renderDeckFiltering();
      
      // Component should render without errors
      expect(container.querySelector('.icon-container--deck')).toBeInTheDocument();
    });

    it('handles selection of "All" option correctly', () => {
      const { dispatcher } = renderDeckFiltering();
      const select = screen.getByRole('combobox');
      
      fireEvent.change(select, { target: { value: 'All' } });
      
      // Should call legality action with undefined
      expect(mockLegality).toHaveBeenCalledWith(dispatcher, undefined);
    });
  });
});
