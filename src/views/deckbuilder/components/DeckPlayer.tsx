import * as Immutable from 'immutable';
import * as React from 'react';
// Containers, actions, and dispatcher
import * as HasDispatcher from '../../../containers/HasDispatcher';
import * as HasMe from '../../../containers/HasMe';
import track from '../../../helpers/ga_helper';
// Models
import {
  calculateTurnProbabilities,
  Deck,
  DeckCard,
  DeckCardGrouping,
  findDeckBoardByName,
} from '../../../models/Decks';
// Components
import { DeckBuilderBoardItem } from './DeckBuilderBoardItem';

interface IProps {
  deck: Deck;
}

interface IState {
  battlefieldTop: Immutable.OrderedMap<number, DeckCard>;
  battlefieldBottom: Immutable.OrderedMap<number, DeckCard>;
  graveyard: Immutable.OrderedMap<number, DeckCard>;
  hand: Immutable.List<DeckCard>;
  drawPile: Immutable.List<DeckCard>;
  turnNumber: number;
  drawCardFocus: boolean;
  drawCardQuery: string;
  drawCardSuggestions: Immutable.List<string>;
  drawCardSuggestionIndex: number;
}

enum BattleFieldLocation {
  TOP,
  BOTTOM,
  GRAVEYARD,
}

export const DeckPlayer = HasMe.Attach<HasDispatcher.IProps & IProps>(
  class extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    /**
     * @override
     * @constructor
     */
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = this.newStateFromProps(props);
    }

    /**
     * @override
     */
    componentWillReceiveProps(props: IProps) {
      this.setState(this.newStateFromProps(props));
    }

    /**
     * @override
     */
    componentDidMount() {}

    /**
     * @override
     */
    render() {
      const turnProbabilities = calculateTurnProbabilities(
        this.props.deck.set(
          'deckCards',
          this.state.drawPile.reduce(
            (map: Immutable.Map<number, DeckCard>, deckCard: DeckCard) => map.set(deckCard.get('id'), deckCard),
            Immutable.Map<number, DeckCard>(),
          ),
        ),
        DeckCardGrouping.NAME,
      );

      return (
        <div className="container-fluid">
          <div className="section playtest">
            <div className="row">
              {/* content */}
              <div className="col-xs-12">
                <div className="row">
                  {/* battlefield */}
                  <div className="col-xs-8">
                    <div className="playtest__battlefield">
                      {this.state.battlefieldTop.size === 0 && this.state.battlefieldBottom.size === 0 ? (
                        <span className="playtest__battlefield__placeholder">No cards on Battlefield</span>
                      ) : null}

                      {this.state.battlefieldTop.size || this.state.battlefieldBottom.size ? (
                        <div className="playtest__battlefield__items">
                          {this.mapBattlefieldItems(BattleFieldLocation.TOP)}
                        </div>
                      ) : null}

                      {this.state.battlefieldTop.size || this.state.battlefieldBottom.size ? (
                        <div className="playtest__battlefield__items">
                          {this.mapBattlefieldItems(BattleFieldLocation.BOTTOM)}
                        </div>
                      ) : null}
                    </div>
                  </div>

                  {/* graveyard */}
                  <div className="col-xs-4">
                    <div className="playtest__graveyard">
                      {this.state.graveyard.size === 0 ? (
                        <span className="playtest__battlefield__placeholder">No cards in Graveyard</span>
                      ) : (
                        <div className="playtest__battlefield__items">
                          {this.mapBattlefieldItems(BattleFieldLocation.GRAVEYARD)}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* actions */}
                  <div className="col-xs-12 flex justify-between" style={{ marginTop: '2rem' }}>
                    <div className="flex">
                      <button className="button-grey" onClick={this.onClickNewGame}>
                        New Game
                      </button>
                      <div className="flex vertical align-start justify-center" style={{ marginLeft: '1rem' }}>
                        <span className="playtest__turn-number__label">{`Turn number: ${this.state.turnNumber}`}</span>
                        <span className="playtest__cards-in-hand__label">{`Cards in hand: ${this.state.hand.size}`}</span>
                      </div>
                    </div>
                    <div className="flex align-center justify-end">
                      {this.state.turnNumber === 1 ? (
                        <button
                          className="button-primary"
                          style={{ marginRight: '0.5rem ' }}
                          onClick={this.onClickMulligan}
                        >
                          Mulligan
                        </button>
                      ) : null}
                      <button
                        className="button-primary"
                        style={{ marginRight: '0.5rem ' }}
                        onClick={this.onClickNextTurn}
                      >
                        Next Turn
                      </button>
                      <button
                        className="button-primary"
                        style={{ marginRight: '0.5rem ' }}
                        onClick={this.onClickDrawRandomCard}
                      >
                        Draw Card
                      </button>
                    </div>
                  </div>

                  {/* hand */}
                  <div className="col-xs-12">
                    <div className="playtest__hand">
                      {this.state.hand.size ? (
                        this.state.hand.reverse().map((deckCard: DeckCard, i: number) => (
                          <div
                            style={{
                              padding: i === this.state.hand.size - 1 ? '0 1rem' : '0 0 0 1rem',
                              boxSizing: 'border-box',
                            }}
                            key={`hand-${deckCard.get('id')}`}
                          >
                            <DeckBuilderBoardItem
                              playtest
                              dispatcher={this.props.dispatcher}
                              deck={this.props.deck}
                              deckCards={Immutable.List([deckCard])}
                              card={this.props.deck.get('cards').get(deckCard.get('cardId'))}
                              onClick={(evt: React.SyntheticEvent<HTMLElement>) => {
                                evt.preventDefault();
                                evt.stopPropagation();
                                track('decks', 'playtest', 'play');
                                const card = this.props.deck.get('cards').get(deckCard.get('cardId'));
                                this.setState({
                                  battlefieldTop:
                                    card.get('types').join(' ') === 'Land'
                                      ? this.state.battlefieldTop
                                      : this.state.battlefieldTop.set(deckCard.get('id'), deckCard),
                                  battlefieldBottom:
                                    card.get('types').join(' ') === 'Land'
                                      ? this.state.battlefieldBottom.set(deckCard.get('id'), deckCard)
                                      : this.state.battlefieldBottom,
                                  hand: this.state.hand
                                    .filter((candidate: DeckCard) => deckCard.get('id') !== candidate.get('id'))
                                    .toList(),
                                });
                              }}
                              onGotoTop={(evt: React.SyntheticEvent<HTMLElement>) => {
                                evt.preventDefault();
                                evt.stopPropagation();
                                track('decks', 'playtest', 'top');
                                this.setState({
                                  drawPile: this.state.drawPile.insert(0, deckCard),
                                  hand: this.state.hand
                                    .filter((candidate: DeckCard) => deckCard.get('id') !== candidate.get('id'))
                                    .toList(),
                                });
                              }}
                              onGotoBottom={(evt: React.SyntheticEvent<HTMLElement>) => {
                                evt.preventDefault();
                                evt.stopPropagation();
                                track('decks', 'playtest', 'bottom');
                                this.setState({
                                  drawPile: this.state.drawPile.push(deckCard),
                                  hand: this.state.hand
                                    .filter((candidate: DeckCard) => deckCard.get('id') !== candidate.get('id'))
                                    .toList(),
                                });
                              }}
                              onGotoGraveyard={(evt: React.SyntheticEvent<HTMLElement>) => {
                                evt.preventDefault();
                                evt.stopPropagation();
                                track('decks', 'playtest', 'graveyard');
                                this.setState({
                                  graveyard: this.state.graveyard.set(deckCard.get('id'), deckCard),
                                  hand: this.state.hand
                                    .filter((candidate: DeckCard) => deckCard.get('id') !== candidate.get('id'))
                                    .toList(),
                                });
                              }}
                            />
                          </div>
                        ))
                      ) : (
                        <div className="flex align-center justify-center" style={{ width: '100%' }}>
                          <span className="playtest__battlefield__placeholder">No cards in Hand</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* actions */}
                  <div className="col-xs-12 flex justify-between" style={{ marginTop: '2rem' }}>
                    <div className="flex vertical align-start justify-center">
                      <form
                        className="flex align-center justify-start"
                        style={{ position: 'relative' }}
                        onSubmit={this.onSubmitDrawCardQuery}
                      >
                        <input
                          ref="tutorInput"
                          className="input"
                          type="text"
                          placeholder="Tutor a card from your deck"
                          onChange={this.onChangeDrawCardQuery}
                          value={this.state.drawCardQuery}
                          onClick={this.onClickInput}
                          onKeyDown={this.onKeyDownInput}
                          onFocus={this.onFocusInput}
                          style={{
                            minWidth: '15rem',
                          }}
                        />
                        <input
                          className="button-primary"
                          type="submit"
                          value="Tutor"
                          style={{ marginLeft: '0.5rem' }}
                          onClick={this.onSubmitDrawCardQuery}
                        />
                        {this.state.drawCardFocus &&
                        this.state.drawCardSuggestions &&
                        this.state.drawCardSuggestions.size ? (
                          <div
                            ref="suggestions"
                            className="deck-player__suggestions"
                            style={{ top: '100%', left: '0%' }}
                          >
                            {this.state.drawCardSuggestions.map((suggestion: string, index) => (
                              <div
                                ref={'suggestion-' + index}
                                key={'suggestion-' + suggestion + '-' + index}
                                className={
                                  'deck-player__suggestion' +
                                  (index === this.state.drawCardSuggestionIndex ? ' is-selected' : '')
                                }
                                onClick={(evt: React.SyntheticEvent<HTMLElement>) => {
                                  evt.preventDefault();
                                  this.setState({
                                    drawCardQuery: suggestion,
                                    drawCardSuggestions: Immutable.List<string>(),
                                    drawCardSuggestionIndex: -1,
                                  });
                                }}
                              >
                                <div className="deck-player__suggestion__label">
                                  <div className="react-select__suggestion__label__name">{suggestion}</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : null}
                      </form>
                    </div>

                    <div className="flex align-center justify-end">
                      <button className="button-primary" onClick={this.onClickShuffle}>
                        Shuffle
                      </button>
                    </div>
                  </div>

                  {/* Statistics table */}
                  {this.state.drawPile.size ? (
                    <div className="col-xs-12">
                      <div className="row justify-center">
                        <div className="col-xs-12 col-lg-10">
                          <div className="deck-table" style={{ marginTop: '2rem' }}>
                            {/* card name */}
                            <div className="deck-table__col grow-xs-1 shrink-xs-1">
                              <div className="deck-table__header cell-border-left">{'Name'}</div>
                              {this.state.drawPile.map((deckCard: DeckCard, position: number) => (
                                <div key={position} className="deck-table__row">
                                  {this.props.deck.get('cards').get(deckCard.get('cardId')).get('name')}
                                </div>
                              ))}
                            </div>

                            {/* position */}
                            <div className="deck-table__col grow-xs-1 shrink-xs-1">
                              <div className="deck-table__header cell-border-left">{'Position'}</div>
                              {this.state.drawPile.map((deckCard: DeckCard, position: number) => (
                                <div key={position} className="deck-table__row">
                                  {position + 1}
                                </div>
                              ))}
                            </div>

                            {/* probability */}
                            <div className="deck-table__col grow-xs-1 shrink-xs-1">
                              <div className="deck-table__header cell-border-left">{'Probability'}</div>
                              {this.state.drawPile.map((deckCard: DeckCard, position: number) => (
                                <div key={position} className="deck-table__row">
                                  {(
                                    turnProbabilities.get(
                                      this.props.deck.get('cards').get(deckCard.get('cardId')).get('name'),
                                    ) * 100
                                  ).toFixed(2) + '%'}
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    private mapBattlefieldItems(location: BattleFieldLocation) {
      let battlefield = Immutable.OrderedMap<number, DeckCard>();
      let setState: (deckCard: DeckCard) => void;

      switch (location) {
        case BattleFieldLocation.TOP:
          battlefield = this.state.battlefieldTop;
          setState = (deckCard: DeckCard) => {
            this.setState({
              battlefieldTop: battlefield.remove(deckCard.get('id')),
              hand: this.state.hand.push(deckCard),
            });
          };
          break;
        case BattleFieldLocation.BOTTOM:
          battlefield = this.state.battlefieldBottom;
          setState = (deckCard: DeckCard) => {
            this.setState({
              battlefieldBottom: battlefield.remove(deckCard.get('id')),
              hand: this.state.hand.push(deckCard),
            });
          };
          break;
        case BattleFieldLocation.GRAVEYARD:
          battlefield = this.state.graveyard;
          setState = (deckCard: DeckCard) => {
            this.setState({
              graveyard: battlefield.remove(deckCard.get('id')),
              hand: this.state.hand.push(deckCard),
            });
          };
          break;
      }

      return battlefield
        .reverse()
        .keySeq()
        .map((key: number) => {
          const deckCard = battlefield.get(key);
          return (
            <div className="playtest__battlefield__item" key={`battlefield-${key}`}>
              <DeckBuilderBoardItem
                dispatcher={this.props.dispatcher}
                deck={this.props.deck}
                deckCards={Immutable.List([deckCard])}
                card={this.props.deck.get('cards').get(deckCard.get('cardId'))}
                mutable={false}
                expandable={true}
                onClick={(evt) => {
                  evt.preventDefault();
                  evt.stopPropagation();
                  setState(deckCard);
                }}
              />
            </div>
          );
        });
    }

    private onClickMulligan = (evt: React.SyntheticEvent<HTMLElement>) => {
      evt.preventDefault();
      track('decks', 'playtest', 'mulligan');
      this.mulligan();
    };

    private onClickNextTurn = (evt: React.SyntheticEvent<HTMLElement>) => {
      evt.preventDefault();
      this.nextTurn();
      this.drawRandomCards(1);
    };

    private onClickDrawRandomCard = (evt: React.SyntheticEvent<HTMLElement>) => {
      evt.preventDefault();
      track('decks', 'playtest', 'draw');
      this.drawRandomCards(1);
    };

    private onClickShuffle = (evt: React.SyntheticEvent<HTMLElement>) => {
      evt.preventDefault();
      track('decks', 'playtest', 'shuffle');
      this.setState({ drawPile: shuffleList(this.state.drawPile) });
    };

    private onClickNewGame = (evt: React.SyntheticEvent<HTMLElement>) => {
      evt.preventDefault();
      track('decks', 'playtest', 'new_game');
      this.setState(this.newStateFromProps(this.props, true));
    };

    private onChangeDrawCardQuery = (evt: React.SyntheticEvent<HTMLInputElement>) => {
      evt.preventDefault();
      this.setState({
        drawCardQuery: evt.currentTarget.value,
        drawCardSuggestions: this.state.drawPile
          .filter(
            (deckCard: DeckCard) =>
              this.props.deck
                .get('cards')
                .get(deckCard.get('cardId'))
                .get('name')
                .toLowerCase()
                .indexOf(evt.currentTarget.value.toLowerCase()) > -1,
          )
          .map((deckCard: DeckCard) => this.props.deck.get('cards').get(deckCard.get('cardId')).get('name'))
          .toSet()
          .toList(),
      });
      this.onFocusInput();
    };

    private onSubmitDrawCardQuery = (evt: React.SyntheticEvent<HTMLElement>) => {
      evt.preventDefault();
      track('decks', 'playtest', 'tutor');
      this.drawQueryCard();
    };

    private mulligan = () => {
      const num = this.state.hand.size - 1;
      const drawPile = shuffleList(this.state.drawPile.concat(this.state.hand.toList()).toList());
      this.setState({
        hand: drawPile.take(Math.min(drawPile.size, num)).toList(),
        drawPile: drawPile.takeLast(Math.max(drawPile.size - num, 0)).toList(),
      });
    };

    private nextTurn = () => {
      const turnNumber = this.state.turnNumber + 1;

      track('decks', 'playtest', 'next_turn', turnNumber);

      this.setState({
        turnNumber: turnNumber,
      });
    };

    private drawQueryCard = () => {
      let filteredCard: DeckCard | null = null;
      const drawPile = this.state.drawPile.filter((deckCard: DeckCard) => {
        if (filteredCard) return true;
        if (this.props.deck.get('cards').get(deckCard.get('cardId')).get('name') !== this.state.drawCardQuery)
          return true;
        filteredCard = deckCard;
        return false;
      });
      this.setState({
        hand: this.state.hand.concat(Immutable.List(filteredCard ? [filteredCard] : [])).toList(),
        drawPile: drawPile.toList(),
        drawCardQuery: '',
        drawCardSuggestions: Immutable.List<string>(),
        drawCardSuggestionIndex: -1,
      });
    };

    private drawRandomCards = (num: number) => {
      this.setState({
        hand: this.state.hand.concat(this.state.drawPile.take(Math.min(this.state.drawPile.size, num))).toList(),
        drawPile: this.state.drawPile.takeLast(Math.max(this.state.drawPile.size - num, 0)).toList(),
      });
    };

    private newStateFromProps = (props: IProps, draw?: boolean) => {
      const mainBoard = findDeckBoardByName(props.deck, 'Main')!;
      const drawPile = shuffleList(
        props.deck
          .get('deckCards')
          .filter((deckCard: DeckCard) => deckCard.get('boardId') === mainBoard.get('id'))
          .toList(),
      );
      return {
        battlefieldTop: Immutable.OrderedMap<number, DeckCard>(),
        battlefieldBottom: Immutable.OrderedMap<number, DeckCard>(),
        graveyard: Immutable.OrderedMap<number, DeckCard>(),
        hand: draw ? drawPile.take(Math.min(drawPile.size, 7)).toList() : Immutable.List<DeckCard>(),
        drawPile: draw ? drawPile.takeLast(Math.max(drawPile.size - 7, 0)).toList() : drawPile,
        turnNumber: 1,
        drawCardFocus: false,
        drawCardQuery: '',
        drawCardSuggestions: Immutable.List<string>(),
        drawCardSuggestionIndex: -1,
      };
    };

    // For handling card querying
    private onClickInput = (evt: React.SyntheticEvent<HTMLElement>) => {
      evt.preventDefault();
      this.ignoreDocumentClick = true;
    };

    // For handling card querying
    private onKeyDownInput = (evt: React.KeyboardEvent<HTMLInputElement>) => {
      switch (evt.keyCode) {
        case 13: // ENTER
          if (this.state.drawCardSuggestionIndex >= 0) {
            evt.preventDefault();
            evt.stopPropagation();
            this.setState({
              drawCardQuery: this.state.drawCardSuggestions.get(Math.max(this.state.drawCardSuggestionIndex!, 0)),
              drawCardSuggestions: Immutable.List<string>(),
              drawCardSuggestionIndex: -1,
            });
          } else if (this.state.drawCardSuggestions && this.state.drawCardSuggestions.size) {
            evt.preventDefault();
            evt.stopPropagation();
            this.setState({
              drawCardQuery: this.state.drawCardSuggestions.get(0),
              drawCardSuggestions: Immutable.List<string>(),
              drawCardSuggestionIndex: -1,
            });
          } else {
            // Handled by form submission
          }
          break;

        case 38: // UP
          evt.preventDefault();
          evt.stopPropagation();

          if (this.state.drawCardSuggestions && this.state.drawCardSuggestions.size) {
            // This is disabled because it causes a scroll jump which is a problem because the suggestions usually appear slightly off screen
            // if (this.refs["suggestion-" + Math.max(this.state.drawCardSuggestionIndex - 1, -1)]) {
            //   ReactDOM.findDOMNode(this.refs["suggestion-" + Math.max(this.state.drawCardSuggestionIndex - 1, -1)]).scrollIntoView(false);
            // }
            this.setState({
              drawCardSuggestionIndex: Math.max(this.state.drawCardSuggestionIndex - 1, -1),
            });
          }
          break;

        case 40: // DOWN
          evt.preventDefault();
          evt.stopPropagation();

          if (this.state.drawCardSuggestions && this.state.drawCardSuggestions.size) {
            // This is disabled because it causes a scroll jump which is a problem because the suggestions usually appear slightly off screen
            // if (this.refs["suggestion-" + Math.min(this.state.drawCardSuggestionIndex + 1, this.state.drawCardSuggestions.size - 1)]) {
            //   ReactDOM.findDOMNode(this.refs["suggestion-" + Math.min(this.state.drawCardSuggestionIndex + 1, this.state.drawCardSuggestions.size - 1)]).scrollIntoView(false);
            // }
            this.setState({
              drawCardSuggestionIndex: Math.min(
                this.state.drawCardSuggestionIndex + 1,
                this.state.drawCardSuggestions.size - 1,
              ),
            });
          }
          break;
      }
    };

    // For handling card querying
    private onFocusInput = () => {
      if (!this.handleDocumentClick) {
        this.setState({
          drawCardFocus: true,
        });
        this.handleDocumentClick = () => {
          if (this.ignoreDocumentClick) {
            this.ignoreDocumentClick = false;
            return;
          }
          this.setState({
            drawCardFocus: false,
          });
          if (this.handleDocumentClick != undefined) {
            document.removeEventListener('click', this.handleDocumentClick);
            this.handleDocumentClick = undefined;
          }
        };
        document.addEventListener('click', this.handleDocumentClick);
      }
    };

    // For handling card querying
    private ignoreDocumentClick = false;
    private handleDocumentClick: EventListenerOrEventListenerObject | undefined = undefined;
  },
);

function shuffleList(list: Immutable.List<any>) {
  const set = list;
  const size = set.size;
  let shuffled = Immutable.List(Array(size));
  for (let index = 0, rand; index < size; index++) {
    rand = Math.floor(Math.random() * (index + 1));
    if (rand !== index) shuffled = shuffled.set(index, shuffled.get(rand));
    shuffled = shuffled.set(rand, set.get(index));
  }
  return shuffled;
}
