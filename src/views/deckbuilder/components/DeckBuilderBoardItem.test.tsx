import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck, FakeDeckBoard, FakeDeckCard } from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { Card } from '../../../models/Cards';
import { DeckCard } from '../../../models/Decks';
import { Game } from '../../../models/Game';
import { DeckBuilderBoardItem } from './DeckBuilderBoardItem';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockAddDeckCard = vi.mocked(DeckActions.addDeckCard);
const mockRemoveDeckCard = vi.mocked(DeckActions.removeDeckCard);
const mockRemoveDeckCards = vi.mocked(DeckActions.removeDeckCards);
const mockUpdateDeck = vi.mocked(DeckActions.updateDeck);

// Mock track helper
vi.mock('../../../helpers/ga_helper', () => ({
  default: vi.fn(),
}));

// Mock CoreAssets
vi.mock('../../../helpers/core_assets', () => ({
  CoreAssets: {
    imageHost: vi.fn(() => 'https://example.com'),
    cardBack: vi.fn(() => '/images/card-back.jpg'),
  },
}));

type DeckBuilderBoardItemProps = React.ComponentProps<typeof DeckBuilderBoardItem>;

const createMockCard = (name: string = 'Lightning Bolt'): Card => {
  return new Card({
    id: 1,
    name,
    jsonID: 'test-json-id',
  });
};

const createMockDeckCard = (): DeckCard => {
  return create(FakeDeckCard, {
    id: 1,
    cardId: 1,
    boardId: 1,
    cardInstanceId: null,
  });
};

const defaultProps: Omit<DeckBuilderBoardItemProps, 'dispatcher'> = {
  deck: create(FakeDeck),
  deckBoard: create(FakeDeckBoard, { id: 1, name: 'Main' }),
  deckCards: Immutable.List([createMockDeckCard()]),
  card: createMockCard(),
  cardCount: 1,
  showCount: true,
  mutable: true,
};

const renderDeckBuilderBoardItem = (props: Partial<DeckBuilderBoardItemProps> = {}) => {
  return renderWithDispatcher(DeckBuilderBoardItem, { ...defaultProps, ...props });
};

describe('DeckBuilderBoardItem', () => {
  describe('when component renders', () => {
    it('displays the board item container', () => {
      const { container } = renderDeckBuilderBoardItem();
      expect(container.querySelector('.deckbuilder-board-item')).toBeInTheDocument();
    });

    it('displays the card image', () => {
      renderDeckBuilderBoardItem();
      const image = screen.getByAltText('Lightning Bolt');
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('src', 'https://example.com/card_images/test-json-id.jpg');
    });

    it('displays card count when showCount is true', () => {
      renderDeckBuilderBoardItem({ showCount: true, cardCount: 3 });
      expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('does not display card count when showCount is false', () => {
      renderDeckBuilderBoardItem({ showCount: false, cardCount: 3 });
      expect(screen.queryByText('3')).not.toBeInTheDocument();
    });

    it('displays placeholder image initially', () => {
      const { container } = renderDeckBuilderBoardItem();
      const placeholderImage = container.querySelector('img[src="/images/card-back.jpg"]');
      expect(placeholderImage).toBeInTheDocument();
    });
  });

  describe('mutable mode', () => {
    it('displays hover buttons when mutable is true', () => {
      const { container } = renderDeckBuilderBoardItem({ mutable: true });
      const hoverContainer = container.querySelector('.deckbuilder-board-item__image-wrapper__hover');
      expect(hoverContainer).toBeInTheDocument();
    });

    it('does not display hover buttons when mutable is false', () => {
      const { container } = renderDeckBuilderBoardItem({ mutable: false });
      const hoverContainer = container.querySelector('.deckbuilder-board-item__image-wrapper__hover');
      expect(hoverContainer).not.toBeInTheDocument();
    });

    it('calls addDeckCard when add button is clicked', () => {
      const { dispatcher } = renderDeckBuilderBoardItem({ mutable: true });
      const { container } = renderDeckBuilderBoardItem({ mutable: true });
      
      const addButton = container.querySelector('[data-icon="ic:add"]')?.closest('div');
      if (addButton) {
        fireEvent.click(addButton);
        
        expect(mockAddDeckCard).toHaveBeenCalledWith(
          defaultProps.deck,
          defaultProps.deckBoard,
          defaultProps.card,
          1,
          dispatcher
        );
      }
    });

    it('calls removeDeckCard when remove button is clicked', () => {
      const { dispatcher } = renderDeckBuilderBoardItem({ mutable: true, cardCount: 2 });
      const { container } = renderDeckBuilderBoardItem({ mutable: true, cardCount: 2 });
      
      const removeButton = container.querySelector('[data-icon="ic:remove"]')?.closest('div');
      if (removeButton) {
        fireEvent.click(removeButton);
        
        expect(mockRemoveDeckCard).toHaveBeenCalledWith(
          defaultProps.deck,
          expect.any(Object),
          dispatcher
        );
      }
    });

    it('shows confirmation dialog when delete all button is clicked', () => {
      const { container } = renderDeckBuilderBoardItem({ mutable: true });
      
      const deleteButton = container.querySelector('[data-icon="ic:delete"]')?.closest('div');
      if (deleteButton) {
        fireEvent.click(deleteButton);
        
        expect(screen.getByText('Are you sure?')).toBeInTheDocument();
        expect(screen.getByText('This card will be removed from your deck. This decision cannot be reversed!')).toBeInTheDocument();
      }
    });

    it('calls removeDeckCards when delete all is confirmed', () => {
      const { dispatcher } = renderDeckBuilderBoardItem({ mutable: true });
      const { container } = renderDeckBuilderBoardItem({ mutable: true });
      
      const deleteButton = container.querySelector('[data-icon="ic:delete"]')?.closest('div');
      if (deleteButton) {
        fireEvent.click(deleteButton);
        
        const confirmButton = screen.getByRole('button', { name: 'Remove' });
        fireEvent.click(confirmButton);
        
        expect(mockRemoveDeckCards).toHaveBeenCalledWith(
          defaultProps.deck,
          defaultProps.deckCards,
          dispatcher
        );
      }
    });
  });

  describe('playtest mode', () => {
    it('displays playtest buttons when playtest is true', () => {
      const { container } = renderDeckBuilderBoardItem({ playtest: true });
      const hoverContainer = container.querySelector('.deckbuilder-board-item__image-wrapper__hover');
      expect(hoverContainer).toBeInTheDocument();
    });

    it('calls onGotoTop when top button is clicked', () => {
      const onGotoTop = vi.fn();
      const { container } = renderDeckBuilderBoardItem({ playtest: true, onGotoTop });
      
      const topButton = container.querySelector('img[src="/images/top.png"]')?.closest('div');
      if (topButton) {
        fireEvent.click(topButton);
        expect(onGotoTop).toHaveBeenCalled();
      }
    });

    it('calls onGotoBottom when bottom button is clicked', () => {
      const onGotoBottom = vi.fn();
      const { container } = renderDeckBuilderBoardItem({ playtest: true, onGotoBottom });
      
      const bottomButton = container.querySelector('img[src="/images/bottom.png"]')?.closest('div');
      if (bottomButton) {
        fireEvent.click(bottomButton);
        expect(onGotoBottom).toHaveBeenCalled();
      }
    });

    it('calls onGotoGraveyard when graveyard button is clicked', () => {
      const onGotoGraveyard = vi.fn();
      const { container } = renderDeckBuilderBoardItem({ playtest: true, onGotoGraveyard });
      
      const graveyardButton = container.querySelector('img[src="/images/graveyard.png"]')?.closest('div');
      if (graveyardButton) {
        fireEvent.click(graveyardButton);
        expect(onGotoGraveyard).toHaveBeenCalled();
      }
    });
  });

  describe('linkable mode', () => {
    it('displays link buttons when linkable is true', () => {
      const { container } = renderDeckBuilderBoardItem({ linkable: true });
      const hoverContainer = container.querySelector('.deckbuilder-board-item__image-wrapper__hover');
      expect(hoverContainer).toBeInTheDocument();
    });

    it('displays link count', () => {
      renderDeckBuilderBoardItem({ linkable: true, cardCount: 4 });
      expect(screen.getByText('0/4')).toBeInTheDocument();
      expect(screen.getByText('Links')).toBeInTheDocument();
    });

    it('calls onClickLink when link button is clicked', () => {
      const onClickLink = vi.fn();
      const { container } = renderDeckBuilderBoardItem({ linkable: true, onClickLink });
      
      const linkButton = container.querySelector('[data-icon="ic:link"]')?.closest('div');
      if (linkButton) {
        fireEvent.click(linkButton);
        expect(onClickLink).toHaveBeenCalled();
      }
    });

    it('calls updateDeck when set cover art button is clicked', () => {
      const { dispatcher } = renderDeckBuilderBoardItem({ linkable: true });
      const { container } = renderDeckBuilderBoardItem({ linkable: true });
      
      const coverArtButton = container.querySelector('[data-icon="ic:wallpaper"]')?.closest('div');
      if (coverArtButton) {
        fireEvent.click(coverArtButton);
        
        expect(mockUpdateDeck).toHaveBeenCalledWith(
          expect.objectContaining({
            image: 'test-json-id',
          }),
          dispatcher
        );
      }
    });
  });

  describe('expandable mode', () => {
    it('displays view button when expandable is true', () => {
      const { container } = renderDeckBuilderBoardItem({ expandable: true });
      const hoverContainer = container.querySelector('.deckbuilder-board-item__image-wrapper__hover');
      expect(hoverContainer).toBeInTheDocument();
    });
  });

  describe('fullscreen functionality', () => {
    it('opens fullscreen view when card is clicked', () => {
      const { container } = renderDeckBuilderBoardItem();
      const cardItem = container.querySelector('.deckbuilder-board-item');
      
      if (cardItem) {
        fireEvent.click(cardItem);
        
        const overlay = container.querySelector('.deckbuilder-board-item__overlay');
        expect(overlay).toBeInTheDocument();
      }
    });

    it('closes fullscreen view when overlay is clicked', () => {
      const { container } = renderDeckBuilderBoardItem();
      const cardItem = container.querySelector('.deckbuilder-board-item');
      
      if (cardItem) {
        fireEvent.click(cardItem);
        
        const overlay = container.querySelector('.deckbuilder-board-item__overlay');
        if (overlay) {
          fireEvent.click(overlay);
          
          expect(container.querySelector('.deckbuilder-board-item__overlay')).not.toBeInTheDocument();
        }
      }
    });

    it('displays high quality image in fullscreen', () => {
      const { container } = renderDeckBuilderBoardItem();
      const cardItem = container.querySelector('.deckbuilder-board-item');
      
      if (cardItem) {
        fireEvent.click(cardItem);
        
        const hqImage = container.querySelector('img[src="https://example.com/card_images_hq/test-json-id.jpg"]');
        expect(hqImage).toBeInTheDocument();
      }
    });
  });

  describe('custom onClick handler', () => {
    it('calls custom onClick when provided and not in fullscreen', () => {
      const onClick = vi.fn();
      const { container } = renderDeckBuilderBoardItem({ onClick });
      
      const cardItem = container.querySelector('.deckbuilder-board-item');
      if (cardItem) {
        fireEvent.click(cardItem);
        expect(onClick).toHaveBeenCalled();
      }
    });
  });

  describe('component lifecycle', () => {
    it('updates card count when props change', () => {
      const { rerenderWithDispatcher } = renderDeckBuilderBoardItem({ cardCount: 2 });
      
      rerenderWithDispatcher({ cardCount: 5 });
      
      expect(screen.getByText('5')).toBeInTheDocument();
    });

    it('sets up image load event listener on mount', () => {
      const addEventListenerSpy = vi.spyOn(HTMLImageElement.prototype, 'addEventListener');
      
      renderDeckBuilderBoardItem();
      
      expect(addEventListenerSpy).toHaveBeenCalledWith('load', expect.any(Function));
    });

    it('removes image load event listener on unmount', () => {
      const removeEventListenerSpy = vi.spyOn(HTMLImageElement.prototype, 'removeEventListener');
      
      const { unmount } = renderDeckBuilderBoardItem();
      unmount();
      
      expect(removeEventListenerSpy).toHaveBeenCalledWith('load', expect.any(Function));
    });
  });

  describe('edge cases', () => {
    it('handles decrement when card count is 1 by showing delete confirmation', () => {
      const { container } = renderDeckBuilderBoardItem({ mutable: true, cardCount: 1 });
      
      const removeButton = container.querySelector('[data-icon="ic:remove"]')?.closest('div');
      if (removeButton) {
        fireEvent.click(removeButton);
        
        expect(screen.getByText('Are you sure?')).toBeInTheDocument();
      }
    });

    it('handles undefined card count gracefully', () => {
      renderDeckBuilderBoardItem({ cardCount: undefined, showCount: true });
      
      // Should not display any count
      expect(screen.queryByText(/\d+/)).not.toBeInTheDocument();
    });
  });
});
