import * as React from 'react';
// Containers, actions, and dispatcher
import * as HasDispatcher from '../../../containers/HasDispatcher';
import * as HasMe from '../../../containers/HasMe';
import track from '../../../helpers/ga_helper';
import isServerside from '../../../helpers/serverside';
// Models
import { Deck } from '../../../models/Decks';
// Components
import { Dialog, DialogSize } from '../../components/Dialog';
import { DeckBuilderPDFForm } from './DeckBuilderPDFForm';

// Clipboard
const Clipboard: any = !isServerside() ? require('clipboard') : null;

interface IProps {
  deck: Deck;
}

interface IState {
  copyText: string;
  creatingPDF: boolean;
}

export const DeckExport = HasMe.Attach<HasDispatcher.IProps & IProps>(
  class extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    private clipper: any;

    /**
     * @override
     * @constructor
     */
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = {
        copyText: 'Copy Sharelink',
        creatingPDF: false,
      };
    }

    /**
     * @override
     */
    componentDidMount() {
      this.clipper = new Clipboard('#share-button');
      this.clipper.on('success', (evt: any) => {
        evt.clearSelection();
        this.setState({ copyText: 'Copied' });
      });
      this.clipper.on('error', (evt: any) => {
        this.setState({ copyText: 'CMD+C to copy' });
      });
    }

    /**
     * @override
     */
    render() {
      return (
        <div className="container-fluid">
          <div className="section">
            {this.state.creatingPDF ? (
              <Dialog
                isOpen={this.state.creatingPDF || false}
                onDismiss={() => {
                  this.setState({ creatingPDF: false });
                }}
                isClosable={false}
                size={DialogSize.LARGE_DYNAMIC}
              >
                <DeckBuilderPDFForm deck={this.props.deck} />
              </Dialog>
            ) : null}

            <div className="row">
              {/* desklist registration */}
              <div className="col-xs-12 col-md-6" style={{ marginBottom: '2rem' }}>
                <div className="flex vertical align-center">
                  <div className="heading-divider-wrapper">
                    <div className="heading-md">Decklist PDF</div>
                  </div>
                  <div className="paragraph-sm is-centered deck-export-paragraph">
                    Generate a decklist PDF to register in competitions and tournaments. Based on{' '}
                    <a href="https://decklist.org">decklist.org</a>.
                  </div>
                  <button className="button-primary" onClick={this.onClickCreatePDF} style={{ marginTop: '1rem' }}>
                    Create PDF
                  </button>
                </div>
              </div>
              {/* export */}
              <div className="col-xs-12 col-md-6" style={{ marginBottom: '2rem' }}>
                <div className="flex vertical align-center">
                  <div className="heading-divider-wrapper">
                    <div className="heading-md">CSV</div>
                  </div>
                  <div className="paragraph-sm is-centered deck-export-paragraph">Export your deck as a CSV file</div>
                  <button className="button-primary" onClick={this.onClickDownloadCSV} style={{ marginTop: '1rem' }}>
                    Download CSV
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    private onClickCreatePDF = (evt: React.SyntheticEvent<HTMLElement>) => {
      evt.preventDefault();
      evt.stopPropagation();
      this.setState({
        creatingPDF: true,
      });
    };

    private onClickDownloadCSV = (evt: React.SyntheticEvent<HTMLElement>) => {
      evt.preventDefault();
      evt.stopPropagation();
      track('decks', 'export', 'csv');
      window.open(`/api/v3/decks/${this.props.deck.get('uuid')}/export`, '_blank');
    };
  },
);
