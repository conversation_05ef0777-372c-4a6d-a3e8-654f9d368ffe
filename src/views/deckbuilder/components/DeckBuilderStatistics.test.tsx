import { screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck } from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DecksModule from '../../../models/Decks';
import { DeckBuilderStatistics } from './DeckBuilderStatistics';

// Mock the deck calculation functions
vi.mock('../../../models/Decks', async () => {
  const actual = await vi.importActual('../../../models/Decks');
  return {
    ...actual,
    calculateCardTypeRatios: vi.fn(),
    calculateManaSymbolRatios: vi.fn(),
    calculateManaSymbolStack: vi.fn(),
  };
});

const mockCalculateCardTypeRatios = vi.mocked(DecksModule.calculateCardTypeRatios);
const mockCalculateManaSymbolRatios = vi.mocked(DecksModule.calculateManaSymbolRatios);
const mockCalculateManaSymbolStack = vi.mocked(DecksModule.calculateManaSymbolStack);

type DeckBuilderStatisticsProps = React.ComponentProps<typeof DeckBuilderStatistics>;

const defaultProps: Omit<DeckBuilderStatisticsProps, 'dispatcher'> = {
  deck: create(FakeDeck),
};

const renderDeckBuilderStatistics = (props: Partial<DeckBuilderStatisticsProps> = {}) => {
  return renderWithDispatcher(DeckBuilderStatistics, { ...defaultProps, ...props });
};

describe('DeckBuilderStatistics', () => {
  beforeEach(() => {
    // Setup default mock return values
    mockCalculateCardTypeRatios.mockReturnValue(
      Immutable.Map({
        Creature: 0.4,
        Instant: 0.3,
        Sorcery: 0.2,
        Land: 0.1,
      }),
    );

    mockCalculateManaSymbolRatios.mockReturnValue(
      Immutable.Map({
        totalCount: 60,
        red: 0.3,
        blue: 0.2,
        white: 0.2,
        black: 0.2,
        green: 0.1,
        disabled: 0,
      }),
    );

    mockCalculateManaSymbolStack.mockReturnValue(
      Immutable.Map({
        white: Immutable.Map<number, number>([
          [1, 5],
          [2, 0],
          [3, 0],
        ]),
        blue: Immutable.Map<number, number>([
          [1, 3],
          [2, 0],
          [3, 0],
        ]),
        black: Immutable.Map<number, number>([
          [1, 0],
          [2, 0],
          [3, 6],
        ]),
        red: Immutable.Map<number, number>([
          [1, 0],
          [2, 8],
          [3, 0],
        ]),
        green: Immutable.Map<number, number>([
          [1, 0],
          [2, 2],
          [3, 0],
        ]),
        multi: Immutable.Map<number, number>([
          [1, 0],
          [2, 0],
          [3, 0],
        ]),
        colorless: Immutable.Map<number, number>([
          [1, 0],
          [2, 0],
          [3, 0],
        ]),
      }),
    );
  });

  it('renders the deck statistics container', () => {
    renderDeckBuilderStatistics();
    expect(screen.getByText('Card Types')).toBeInTheDocument();
    expect(screen.getByText('Mana Symbols')).toBeInTheDocument();
    expect(screen.getByText('Mana Curve')).toBeInTheDocument();
  });

  it('calls calculation functions with the provided deck', () => {
    const deck = create(FakeDeck);
    renderDeckBuilderStatistics({ deck });

    expect(mockCalculateCardTypeRatios).toHaveBeenCalledWith(deck);
    expect(mockCalculateManaSymbolRatios).toHaveBeenCalledWith(deck);
    expect(mockCalculateManaSymbolStack).toHaveBeenCalledWith(deck);
  });
});
