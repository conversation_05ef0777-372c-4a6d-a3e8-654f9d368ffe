import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck, FakeDeckCard } from '../../../../tests/fake/FakeCardData';
import { FakeUser } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { Card } from '../../../models/Cards';
import { DeckCard } from '../../../models/Decks';
import { DeckLinkerWrapper } from './DeckLinkerWrapper';

// Mock the DeckLinker component
vi.mock('./DeckLinker', () => ({
  DeckLinker: vi.fn(({ deck, deckCards }) => (
    <div data-testid="deck-linker">
      <div>Mocked DeckLinker</div>
      <div>Card: {deck.get('cards').get(deckCards.get(0).get('cardId')).get('name')}</div>
    </div>
  )),
}));

// Mock Dialog component
vi.mock('../../components/Dialog', () => ({
  Dialog: vi.fn(({ isOpen, onDismiss, children, className, size }) => 
    isOpen ? (
      <div data-testid="dialog" className={className} data-size={size}>
        <button onClick={onDismiss} data-testid="dialog-dismiss">Close</button>
        {children}
      </div>
    ) : null
  ),
  DialogSize: {
    LARGE_DYNAMIC: 'large-dynamic',
  },
}));

type DeckLinkerWrapperProps = React.ComponentProps<typeof DeckLinkerWrapper>;

const createMockCard = (name: string = 'Lightning Bolt'): Card => {
  return new Card({
    id: 1,
    name,
    jsonID: 'test-json-id',
  });
};

const createMockDeckWithCards = () => {
  const card = createMockCard('Lightning Bolt');
  return create(FakeDeck, {
    cards: Immutable.Map({ 1: card }),
  });
};

const defaultProps: Omit<DeckLinkerWrapperProps, 'dispatcher' | 'me'> = {
  deck: createMockDeckWithCards(),
  deckCards: Immutable.List([create(FakeDeckCard, { id: 1, cardId: 1 })]),
  isOpen: true,
  onDismiss: vi.fn(),
};

const renderDeckLinkerWrapper = (props: Partial<DeckLinkerWrapperProps> = {}) => {
  return renderWithDispatcher(DeckLinkerWrapper, { 
    ...defaultProps, 
    me: create(FakeUser),
    ...props 
  });
};

describe('DeckLinkerWrapper', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when component renders', () => {
    it('renders dialog when isOpen is true', () => {
      renderDeckLinkerWrapper({ isOpen: true });
      
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
    });

    it('does not render dialog when isOpen is false', () => {
      renderDeckLinkerWrapper({ isOpen: false });
      
      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
    });

    it('returns null when isOpen is false', () => {
      const { container } = renderDeckLinkerWrapper({ isOpen: false });
      
      expect(container.firstChild).toBeNull();
    });

    it('renders DeckLinker component inside dialog', () => {
      renderDeckLinkerWrapper({ isOpen: true });
      
      expect(screen.getByTestId('deck-linker')).toBeInTheDocument();
      expect(screen.getByText('Mocked DeckLinker')).toBeInTheDocument();
    });

    it('passes correct props to DeckLinker', () => {
      const deck = createMockDeckWithCards();
      const deckCards = Immutable.List([create(FakeDeckCard, { id: 1, cardId: 1 })]);
      
      renderDeckLinkerWrapper({ deck, deckCards, isOpen: true });
      
      expect(screen.getByText('Card: Lightning Bolt')).toBeInTheDocument();
    });
  });

  describe('dialog configuration', () => {
    it('applies correct dialog className', () => {
      renderDeckLinkerWrapper({ isOpen: true });
      
      const dialog = screen.getByTestId('dialog');
      expect(dialog).toHaveClass('dialog-left-align');
    });

    it('uses LARGE_DYNAMIC dialog size', () => {
      renderDeckLinkerWrapper({ isOpen: true });
      
      const dialog = screen.getByTestId('dialog');
      expect(dialog).toHaveAttribute('data-size', 'large-dynamic');
    });

    it('passes isOpen prop to Dialog', () => {
      renderDeckLinkerWrapper({ isOpen: true });
      
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
    });

    it('passes onDismiss handler to Dialog', () => {
      const onDismiss = vi.fn();
      renderDeckLinkerWrapper({ isOpen: true, onDismiss });
      
      const dismissButton = screen.getByTestId('dialog-dismiss');
      fireEvent.click(dismissButton);
      
      expect(onDismiss).toHaveBeenCalled();
    });
  });

  describe('dismiss functionality', () => {
    it('calls onDismiss when dialog is dismissed', () => {
      const onDismiss = vi.fn();
      renderDeckLinkerWrapper({ isOpen: true, onDismiss });
      
      const dismissButton = screen.getByTestId('dialog-dismiss');
      fireEvent.click(dismissButton);
      
      expect(onDismiss).toHaveBeenCalledTimes(1);
    });

    it('prevents default event behavior when dismissed', () => {
      const onDismiss = vi.fn();
      renderDeckLinkerWrapper({ isOpen: true, onDismiss });
      
      const dismissButton = screen.getByTestId('dialog-dismiss');
      const clickEvent = new Event('click', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(clickEvent, 'preventDefault');
      const stopPropagationSpy = vi.spyOn(clickEvent, 'stopPropagation');
      
      fireEvent(dismissButton, clickEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
      expect(stopPropagationSpy).toHaveBeenCalled();
    });

    it('handles missing onDismiss prop gracefully', () => {
      renderDeckLinkerWrapper({ isOpen: true, onDismiss: undefined });
      
      const dismissButton = screen.getByTestId('dialog-dismiss');
      
      expect(() => fireEvent.click(dismissButton)).not.toThrow();
    });

    it('passes event to onDismiss callback', () => {
      const onDismiss = vi.fn();
      renderDeckLinkerWrapper({ isOpen: true, onDismiss });
      
      const dismissButton = screen.getByTestId('dialog-dismiss');
      fireEvent.click(dismissButton);
      
      expect(onDismiss).toHaveBeenCalledWith(expect.any(Object));
    });
  });

  describe('prop passing to DeckLinker', () => {
    it('passes dispatcher prop to DeckLinker', () => {
      const { dispatcher } = renderDeckLinkerWrapper({ isOpen: true });
      
      // DeckLinker should receive the dispatcher
      expect(screen.getByTestId('deck-linker')).toBeInTheDocument();
    });

    it('passes me prop to DeckLinker', () => {
      const me = create(FakeUser, { username: 'testuser' });
      renderDeckLinkerWrapper({ isOpen: true, me });
      
      // DeckLinker should receive the me prop
      expect(screen.getByTestId('deck-linker')).toBeInTheDocument();
    });

    it('passes deck prop to DeckLinker', () => {
      const deck = createMockDeckWithCards();
      renderDeckLinkerWrapper({ isOpen: true, deck });
      
      // DeckLinker should receive the deck prop
      expect(screen.getByText('Card: Lightning Bolt')).toBeInTheDocument();
    });

    it('passes deckCards prop to DeckLinker', () => {
      const deckCards = Immutable.List([
        create(FakeDeckCard, { id: 1, cardId: 1 }),
        create(FakeDeckCard, { id: 2, cardId: 1 }),
      ]);
      
      renderDeckLinkerWrapper({ isOpen: true, deckCards });
      
      // DeckLinker should receive the deckCards prop
      expect(screen.getByTestId('deck-linker')).toBeInTheDocument();
    });
  });

  describe('component lifecycle', () => {
    it('initializes with empty state', () => {
      renderDeckLinkerWrapper({ isOpen: true });
      
      // Component should initialize without errors
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
    });

    it('handles state changes correctly', () => {
      const { rerenderWithDispatcher } = renderDeckLinkerWrapper({ isOpen: false });
      
      // Initially not visible
      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
      
      // Re-render with isOpen true
      rerenderWithDispatcher({ isOpen: true, me: create(FakeUser) });
      
      // Should now be visible
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
    });
  });

  describe('edge cases', () => {
    it('handles empty deckCards list', () => {
      expect(() => {
        renderDeckLinkerWrapper({ 
          isOpen: true, 
          deckCards: Immutable.List() 
        });
      }).not.toThrow();
    });

    it('handles undefined onDismiss prop', () => {
      expect(() => {
        renderDeckLinkerWrapper({ 
          isOpen: true, 
          onDismiss: undefined 
        });
      }).not.toThrow();
    });

    it('handles deck with no cards', () => {
      const emptyDeck = create(FakeDeck, {
        cards: Immutable.Map(),
      });
      
      expect(() => {
        renderDeckLinkerWrapper({ 
          isOpen: true, 
          deck: emptyDeck 
        });
      }).not.toThrow();
    });
  });

  describe('accessibility', () => {
    it('provides proper dialog structure', () => {
      renderDeckLinkerWrapper({ isOpen: true });
      
      const dialog = screen.getByTestId('dialog');
      expect(dialog).toBeInTheDocument();
    });

    it('provides dismiss functionality', () => {
      const onDismiss = vi.fn();
      renderDeckLinkerWrapper({ isOpen: true, onDismiss });
      
      const dismissButton = screen.getByTestId('dialog-dismiss');
      expect(dismissButton).toBeInTheDocument();
    });
  });

  describe('conditional rendering', () => {
    it('renders nothing when isOpen is false', () => {
      const { container } = renderDeckLinkerWrapper({ isOpen: false });
      
      expect(container.firstChild).toBeNull();
    });

    it('renders dialog content when isOpen is true', () => {
      renderDeckLinkerWrapper({ isOpen: true });
      
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      expect(screen.getByTestId('deck-linker')).toBeInTheDocument();
    });

    it('toggles visibility based on isOpen prop', () => {
      const { rerenderWithDispatcher } = renderDeckLinkerWrapper({ isOpen: true });
      
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      
      rerenderWithDispatcher({ isOpen: false, me: create(FakeUser) });
      
      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
    });
  });

  describe('event handling', () => {
    it('binds onDismiss method correctly', () => {
      const onDismiss = vi.fn();
      renderDeckLinkerWrapper({ isOpen: true, onDismiss });
      
      const dismissButton = screen.getByTestId('dialog-dismiss');
      fireEvent.click(dismissButton);
      
      expect(onDismiss).toHaveBeenCalled();
    });

    it('maintains proper context in onDismiss method', () => {
      const onDismiss = vi.fn();
      renderDeckLinkerWrapper({ isOpen: true, onDismiss });
      
      const dismissButton = screen.getByTestId('dialog-dismiss');
      fireEvent.click(dismissButton);
      
      // The method should be called with the correct context
      expect(onDismiss).toHaveBeenCalledWith(expect.any(Object));
    });
  });
});
