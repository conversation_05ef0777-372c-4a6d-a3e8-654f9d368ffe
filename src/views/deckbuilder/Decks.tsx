import * as React from 'react';
import { Helmet } from 'react-helmet';
import * as DeckActions from '../../actions/DeckActions';
import * as MessageActions from '../../actions/MessageActions';
import * as HasDeck from '../../containers/HasDeck';
import * as HasDecks from '../../containers/HasDecks';
// Containers, actions, dispatchers, and stores
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
// Helpers
import displayCurrency from '../../helpers/currency_helper';
import { DateFormat, TextFormat } from '../../helpers/fmt';
import track from '../../helpers/ga_helper';
import history from '../../helpers/history';
import isServerside from '../../helpers/serverside';
import { CellBorders } from '../../models/CellBorder';
// Models
import { Deck } from '../../models/Decks';
import { DeckSorting as DeckSortingEnum } from '../../models/sorting/DeckSorting';
import { FreeLimits } from '../../models/Subscriptions';
import { Viewing } from '../../models/Viewing';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';
import { Placeholder } from '../components/Placeholder';
// Components
import * as Table from '../components/Table';
import { DeckColorFiltering } from './components/DeckColorFiltering';
import { DeckFiltering } from './components/DeckFiltering';
import { DeckColorCell } from './components/DeckListItem';
import { DeckSearch } from './components/DeckSearch';
import { DeckSorting } from './components/DeckSorting';
import { DeckTags } from './components/DeckTags';
import { DeckTile } from './components/DeckTile';

interface IProps {}

interface IState {
  loading: boolean;
}

export const Decks = HasMe.Attach<HasDispatcher.IProps & IProps>(
  HasDeck.Attach<HasDispatcher.IProps & HasMe.IProps & IProps>(
    HasDecks.Attach<HasDeck.IProps & HasDispatcher.IProps & HasMe.IProps & IProps>(
      class extends React.Component<
        HasDecks.IProps & HasDeck.IProps & HasDispatcher.IProps & HasMe.IProps & IProps,
        IState
      > {
        /**
         * @override
         * @constructor
         */
        constructor(props: HasDecks.IProps & HasDeck.IProps & HasDispatcher.IProps & HasMe.IProps & IProps) {
          super(props);

          const storeState = props.dispatcher.DeckStore().getState();

          this.state = {
            loading: true,
          };
        }

        /**
         * @override
         */
        componentDidMount() {
          if (this.props.currentDeck) {
            DeckActions.clearCurrentDeck(this.props.dispatcher);
          }
          DeckActions.decks(this.props.dispatcher).then(() => {
            this.setState({ loading: false });
          });
          DeckActions.allMyTags(this.props.dispatcher);
        }

        /**
         * @override
         */
        componentDidUpdate() {
          if (this.props.currentDeck) {
            history.push(`/decks/${this.props.currentDeck.get('uuid')}/build`);
          }
        }

        /**
         * @override
         */
        render() {
          if (isServerside()) {
            Helmet.renderStatic();
          }
          const color = this.props.queryColor.mergeWithMana(this.props.color);

          return (
            <div>
              <Helmet>
                <title>CardCastle: Decks</title>
              </Helmet>

              <Navbar dispatcher={this.props.dispatcher} me={this.props.me} selection="decks" isRibbon={true} />
              <div className="section has-footer has-navbar">
                {/* controls */}
                <div className="container-fluid">
                  <div className="row">
                    {/* searching, sorting, filtering */}
                    <div className="col-xs-12">
                      <div className="row">
                        {/* searching */}
                        <div className="col-xs-5">
                          <DeckSearch dispatcher={this.props.dispatcher} query={this.props.query} />
                        </div>

                        {/* sorting */}
                        <div className="col-xs-2">
                          <DeckSorting dispatcher={this.props.dispatcher} sorting={this.props.sorting} />
                        </div>

                        {/* filtering */}
                        <div className="col-xs-2">
                          <DeckFiltering dispatcher={this.props.dispatcher} legality={this.props.legality} />
                        </div>

                        {/* toggles */}
                        <div className="flex justify-end col-xs-3">
                          <div className="collection-toggle-container" onClick={this.onClickToggleView}>
                            <div
                              className={'collection-toggle' + (this.props.view === Viewing.GRID ? ' selected' : '')}
                            >
                              <img
                                src={
                                  this.props.view === Viewing.GRID
                                    ? '/images/grid-icon-alt.png'
                                    : '/images/grid-icon.png'
                                }
                              />
                            </div>
                            <div
                              className={'collection-toggle' + (this.props.view === Viewing.LIST ? ' selected' : '')}
                            >
                              <img
                                src={
                                  this.props.view === Viewing.LIST
                                    ? '/images/list-icon-alt.png'
                                    : '/images/list-icon.png'
                                }
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* color filtering */}
                      <div className="col-xs-12">
                        <DeckColorFiltering dispatcher={this.props.dispatcher} color={color} />
                      </div>

                      {/* tag filtering */}
                      <div className="col-xs-12 col-md-8">
                        <DeckTags
                          dispatcher={this.props.dispatcher}
                          deckTags={this.props.tags}
                          deckTagsSelected={this.props.tagsSelected}
                          deckTagsSelectedNot={this.props.tagsSelectedNot}
                        />
                      </div>

                      {/* divider */}
                      <div
                        className="col-xs-12"
                        style={{ content: '""', height: '1px', backgroundColor: '#CDCDCD', marginTop: '1rem' }}
                      />
                    </div>
                  </div>
                </div>

                {/* content */}
                <div className="container-fluid" style={{ marginTop: '2rem' }}>
                  <div className="row">
                    {/* actions and info */}
                    <div className="col-xs-12">
                      <div className="row">
                        {/* action buttons */}
                        <div className="col-xs-12 col-sm-6">
                          <button className="button-primary" onClick={this.onClickCreateNew}>
                            Create New
                          </button>
                        </div>

                        {/* display count */}
                        {this.displayCount()}
                      </div>
                    </div>

                    {/* deck tiles */}
                    <div className="col-xs-12">{this.renderDecks()}</div>
                  </div>
                </div>

                <Footer />
              </div>
            </div>
          );
        }

        private renderDecks = () => {
          if (this.props.numTotal !== 0 && this.props.numFiltered === 0) {
            return <Placeholder message={'No decks match your filters.'} button={null} href="/decks" />;
          } else if (this.props.numTotal === 0) {
            if (this.state.loading) {
              return <Placeholder message={'Loading...'} button={null} />;
            }
            return (
              <Placeholder
                message={"You haven't created any decks yet. Let's create one."}
                button={'Create New'}
                onClick={this.onClickCreateNew}
              />
            );
          } else {
            if (this.props.view === Viewing.LIST) {
              return this.renderList();
            } else {
              return this.renderGrid();
            }
          }
        };

        private displayCount = () => {
          const displayText = this.state.loading
            ? 'Loading...'
            : `Displaying decks ${this.props.numFiltered} of ${this.props.numTotal}`;
          return (
            <>
              <div className="hidden-md flex align-center selection-display-heading col-xs-12">{displayText}</div>
              <div className="visible-md flex align-center justify-end selection-display-heading col-sm-6">
                {displayText}
              </div>
            </>
          );
        };

        private renderGrid = () => {
          return (
            <div className="row" style={{ marginTop: '1rem' }}>
              {this.props.decks.valueSeq().map((deck: Deck, index: number) => (
                <DeckTile key={index} dispatcher={this.props.dispatcher} deck={deck} />
              ))}
            </div>
          );
        };

        private renderList = () => {
          return (
            <div className="deck-list__table">
              <Table.SortingHeader
                title="Name"
                sorting={this.props.sorting}
                asc={DeckSortingEnum.NAME_ASC}
                desc={DeckSortingEnum.NAME_DESC}
                cellBorders={CellBorders.default(false)}
                onClick={(sorting) => this.onClickSortingHeader(sorting as DeckSortingEnum)}
              />
              <div className="table__header cell-border-left">Color</div>
              <div className="table__header cell-border-left">Legality</div>
              <div className="table__header cell-border-left">Cards</div>
              <Table.SortingHeader
                title="Price"
                sorting={this.props.sorting}
                asc={DeckSortingEnum.PRICE_ASC}
                desc={DeckSortingEnum.PRICE_DESC}
                cellBorders={CellBorders.default(false)}
                onClick={(sorting) => this.onClickSortingHeader(sorting as DeckSortingEnum)}
              />
              <div className="table__header cell-border-left">Public</div>
              <Table.SortingHeader
                title="Date Edited"
                sorting={this.props.sorting}
                asc={DeckSortingEnum.DATE_EDITED_ASC}
                desc={DeckSortingEnum.DATE_EDITED_DESC}
                cellBorders={CellBorders.default(false)}
                onClick={(sorting) => this.onClickSortingHeader(sorting as DeckSortingEnum)}
              />
              <Table.SortingHeader
                title="Date Created"
                sorting={this.props.sorting}
                asc={DeckSortingEnum.DATE_ADDED_ASC}
                desc={DeckSortingEnum.DATE_ADDED_DESC}
                cellBorders={CellBorders.rightCell(false)}
                onClick={(sorting) => this.onClickSortingHeader(sorting as DeckSortingEnum)}
              />
              {this.props.decks.valueSeq().map((deck: Deck, index: number) => {
                let price = '-';
                if (deck.get('price')) {
                  price = displayCurrency(
                    deck.get('price'),
                    true,
                    this.props.me.get('preferences').get('localization').get('currency'),
                  );
                }
                const dark = index % 2 === 0 ? true : false;
                const final = this.props.decks.size - 1 === index;
                return (
                  <React.Fragment key={deck.get('uuid')}>
                    <Table.TextCell
                      text={deck.get('name')}
                      dark={dark}
                      cellBorders={CellBorders.default(final)}
                      onClick={() => this.openDeck(deck)}
                    />
                    <DeckColorCell
                      deck={deck}
                      dark={dark}
                      cellBorders={CellBorders.default(final)}
                      onClick={this.openDeck}
                    />
                    <Table.TextCell
                      text={TextFormat.capitalize(deck.get('legality'))}
                      dark={dark}
                      cellBorders={CellBorders.default(final)}
                      onClick={() => this.openDeck(deck)}
                    />
                    <Table.TextCell
                      text={`${deck.get('numCards')}`}
                      dark={dark}
                      cellBorders={CellBorders.default(final)}
                      onClick={() => this.openDeck(deck)}
                    />
                    <Table.TextCell
                      text={price}
                      dark={dark}
                      cellBorders={CellBorders.default(final)}
                      onClick={() => this.openDeck(deck)}
                    />
                    <Table.CheckCell
                      checked={deck.get('isPublic')}
                      dark={dark}
                      cellBorders={CellBorders.default(final)}
                      onClick={() => this.openDeck(deck)}
                    />
                    <Table.TextCell
                      text={this.humanDate(deck.get('dateEdited'))}
                      dark={dark}
                      cellBorders={CellBorders.default(final)}
                      onClick={() => this.openDeck(deck)}
                    />
                    <Table.TextCell
                      text={this.humanDate(deck.get('dateCreated'))}
                      dark={dark}
                      onClick={() => this.openDeck(deck)}
                      cellBorders={CellBorders.rightCell(final)}
                    />
                  </React.Fragment>
                );
              })}
            </div>
          );
        };

        private openDeck = (deck: Deck) => {
          history.push(`/decks/${deck.get('uuid')}`);
        };

        private humanDate = (date: Date): string => {
          const timezone = this.props.me.get('preferences').get('localization').get('timezone');

          return DateFormat.human(date, timezone);
        };

        private onClickCreateNew = (evt: React.SyntheticEvent<HTMLElement>) => {
          evt.preventDefault();
          if (!this.props.me.get('subscribed')) {
            if (this.props.decks.size >= FreeLimits.DECKS) {
              return MessageActions.errorButton(
                `Upgrade to Knight to create more than ${FreeLimits.DECKS} decks`,
                'Upgrade',
                () => {
                  history.push('/settings/subscription');
                },
                this.props.dispatcher,
              );
            }
          }

          track('decks', 'create');

          DeckActions.newDeck(this.props.dispatcher);
        };

        private onClickToggleView = (evt: React.SyntheticEvent<HTMLElement>) => {
          evt.preventDefault();
          if (this.props.view === Viewing.GRID) {
            DeckActions.view(Viewing.LIST, this.props.dispatcher);
          } else if (this.props.view === Viewing.LIST) {
            DeckActions.view(Viewing.GRID, this.props.dispatcher);
          }
        };

        private onClickSortingHeader = (sorting: DeckSortingEnum) => {
          DeckActions.sorting(sorting as DeckSortingEnum, this.props.dispatcher);
        };
      },
    ),
  ),
);
