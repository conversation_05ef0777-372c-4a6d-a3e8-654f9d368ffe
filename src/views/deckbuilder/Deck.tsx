import * as Immutable from 'immutable';
import * as React from 'react';
import { Helmet } from 'react-helmet';
import { match } from 'react-router-dom';
// Actions and API
import * as DeckActions from '../../actions/DeckActions';
// Containers and dispatcher
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import { CoreAssets } from '../../helpers/core_assets';
import { EnumHelper } from '../../helpers/enum';
import { TextFormat } from '../../helpers/fmt';
// Helpers
import history from '../../helpers/history';
import isServerside from '../../helpers/serverside';
import { Arranging } from '../../models/Arranging';
// Models
import { Deck } from '../../models/Decks';
import { Grouping } from '../../models/Grouping';
import { TabItem } from '../../models/TabItem';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';
import { SecondaryNavBar } from '../components/SecondaryNavBar';
import { SecondaryIconType, SecondaryNavBarData } from '../components/SecondaryNavButton';
import { DeckBuilder } from './components/DeckBuilder';
// Components
import { DeckExport } from './components/DeckExport';
import { DeckPlayer } from './components/DeckPlayer';
import { DeckStatistics } from './components/DeckStatistics';
import { DeckViewer } from './components/DeckViewer';

interface IProps {
  match?: match<DeckParams>;
}

interface DeckParams {
  deck: string;
  page?: string;
}

enum DeckPage {
  VIEW = 'view',
  BUILD = 'build',
  PLAYTEST = 'playtest',
  STATISTICS = 'statistics',
  EXPORT = 'export',
}

export enum OwnershipStatus {
  OWNED = 'owned',
  NOT_OWNED = 'not-owned',
  NO_USER = 'no-user',
}

interface IState {
  page: DeckPage;
  deck?: Deck;
  deckGrouping: Grouping;
  deckArranging: Arranging;
}

const DeckComponent = HasMe.Attach<HasDispatcher.IProps & IProps>(
  class extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    private deckStoreToken?: { remove: () => void };

    /**
     * @override
     * @constructor
     */
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      const deckID = this.getDeckID();
      const propsPage = (props.match && props.match.params.page) || DeckPage.VIEW;
      const page = EnumHelper.match(DeckPage, propsPage) as DeckPage;
      const deckStoreState = props.dispatcher.DeckStore().getState();

      this.state = {
        page: page || DeckPage.VIEW,
        deck: deckID ? deckStoreState.get('decks').get(deckID) : undefined,
        deckGrouping: deckStoreState.get('grouping'),
        deckArranging: deckStoreState.get('arranging'),
      };
    }

    /**
     * @override
     */
    componentDidMount() {
      if (this.getDeckID() === undefined) {
        history.push('/404');
      } else {
        this.deckStoreToken = this.props.dispatcher.DeckStore().addListener(this.onChangeDeckStore);
        this.loadDeck();
        CoreAssets.preload();
      }
    }

    /**
     * @override
     */
    componentDidUpdate(prevProps: IProps, prevState: IState) {
      if (this.state.deck !== undefined) {
        const uuid = this.state.deck.get('uuid');
        if (prevState.deck !== undefined && prevState.deck.get('uuid') !== uuid) {
          history.push(`/decks/${uuid}`);
        }
      } else if (this.pageDisabled(this.state.page)) {
        this.openPage(DeckPage.VIEW);
      }
    }

    /**
     * @override
     */
    componentWillUnmount() {
      this.deckStoreToken && this.deckStoreToken.remove();
    }

    /**
     * @override
     */
    render() {
      if (isServerside()) {
        Helmet.renderStatic();
      }
      const deckName = (this.state.deck && this.state.deck.get('name')) || '';
      const secondaryPages = EnumHelper.allCases(DeckPage)
        .map((page: DeckPage) => {
          return new TabItem({ title: page, disabled: this.pageDisabled(page) });
        })
        .toList();

      return (
        <div>
          <Helmet>
            <title>{deckName.length ? 'CardCastle: ' + deckName : 'CardCastle: Deck'}</title>
          </Helmet>

          <Navbar
            dispatcher={this.props.dispatcher}
            me={this.props.me}
            selection="decks"
            isRibbon={this.accessState() === OwnershipStatus.OWNED}
          />
          <div className="section has-footer has-navbar">
            <div className="container-fluid" style={{ marginBottom: '2rem' }}>
              <div className="center-row">
                <SecondaryNavBar
                  pages={secondaryPages}
                  selectedPage={this.state.page}
                  displayName={this.pageName}
                  openPage={this.openPage}
                  leftButton={
                    this.accessState() !== OwnershipStatus.NO_USER
                      ? new SecondaryNavBarData({
                          href: 'Back',
                          onClick: this.onClickBack.bind(this),
                          iconType: SecondaryIconType.CHEVRON_LEFT,
                        })
                      : undefined
                  }
                />
              </div>
            </div>
            <div>{this.renderPage()}</div>
          </div>

          <Footer />
        </div>
      );
    }

    private openPage = (page: string) => {
      // Find corresponding page
      const match = EnumHelper.match(DeckPage, page) as DeckPage;

      if (this.pageDisabled(match)) {
        return;
      }

      if (match) {
        history.push(`/decks/${this.getDeckID() || ''}/${match}`);
        this.setState({
          page: match,
        });
      }
    };

    private pageDisabled(page: DeckPage): boolean {
      const restrictedPages = Immutable.List([DeckPage.BUILD, DeckPage.EXPORT]);
      return restrictedPages.contains(page) && this.accessState() !== OwnershipStatus.OWNED;
    }

    private renderPage = () => {
      if (this.state.deck === undefined) {
        return null;
      }

      switch (this.state.page) {
        case DeckPage.VIEW:
          return (
            <DeckViewer
              dispatcher={this.props.dispatcher}
              accessState={this.accessState()}
              deck={this.state.deck}
              deckArranging={this.state.deckArranging}
              deckGrouping={this.state.deckGrouping}
              onDuplicate={this.onDuplicate.bind(this)}
            />
          );
        case DeckPage.BUILD:
          return (
            <DeckBuilder
              dispatcher={this.props.dispatcher}
              deck={this.state.deck}
              deckArranging={this.state.deckArranging}
              deckGrouping={this.state.deckGrouping}
            />
          );
        case DeckPage.PLAYTEST:
          return <DeckPlayer dispatcher={this.props.dispatcher} deck={this.state.deck} />;
        case DeckPage.STATISTICS:
          return <DeckStatistics dispatcher={this.props.dispatcher} deck={this.state.deck} />;
        case DeckPage.EXPORT:
          return <DeckExport dispatcher={this.props.dispatcher} deck={this.state.deck} />;
        default:
          return null;
      }
    };

    private loadDeck = async () => {
      const deckID = this.getDeckID();
      if (deckID) {
        await DeckActions.deck(deckID, this.props.dispatcher).catch((err) => {
          console.error('Could not retrieve deck');
          err && console.error(err);
          history.push('/404');
        });
        DeckActions.setDeckPublic(this.accessState() !== OwnershipStatus.OWNED, this.props.dispatcher);
      }
    };

    private onChangeDeckStore = () => {
      const deckID = this.getDeckID();
      const deckStoreState = this.props.dispatcher.DeckStore().getState();
      if (deckID) {
        const deck = deckStoreState.get('decks').get(deckID);
        this.setState({
          deck: deck,
          deckGrouping: deckStoreState.get('grouping'),
          deckArranging: deckStoreState.get('arranging'),
        });
      } else {
        this.setState({
          deckGrouping: deckStoreState.get('grouping'),
          deckArranging: deckStoreState.get('arranging'),
        });
      }
    };

    private accessState(): OwnershipStatus {
      if (!this.props.me.get('id')) {
        return OwnershipStatus.NO_USER;
      }
      if (this.state.deck) {
        return this.state.deck.get('userId') == this.props.me.get('id')
          ? OwnershipStatus.OWNED
          : OwnershipStatus.NOT_OWNED;
      } else {
        return OwnershipStatus.NO_USER;
      }
    }

    private getDeckID(): string | undefined {
      return this.props.match && this.props.match.params.deck;
    }

    private onClickBack = (evt: React.SyntheticEvent<HTMLElement>) => {
      evt.preventDefault();
      history.push(`/decks`);
    };

    private pageName = (deckPage: string): string => {
      const match = EnumHelper.match(DeckPage, deckPage) as DeckPage;
      switch (match) {
        case DeckPage.EXPORT:
          return 'Export / Share';
        case DeckPage.STATISTICS:
          return 'Statistics';
        case undefined:
          return '';
        default:
          return TextFormat.capitalize(match);
      }
    };

    private onDuplicate = async (uuid: string) => {
      // Check for non-public user
      if (this.props.me.get('username') === '') {
        history.push(`/login?redirect=${encodeURIComponent(`/decks/${uuid}`)}`);
        return;
      }
      const newDeck = await DeckActions.duplicateDeck(uuid, this.props.dispatcher);
      this.setState({ deck: newDeck });
    };
  },
);

export { DeckComponent as Deck };
