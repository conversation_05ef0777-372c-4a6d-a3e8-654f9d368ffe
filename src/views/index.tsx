import Bugsnag from '@bugsnag/js';
import BugsnagPluginReact from '@bugsnag/plugin-react';
import * as React from 'react';
import * as ReactDOM from 'react-dom';
import { Redirect, Route, Router, Switch } from 'react-router-dom';
import * as ChangelogsAPI from '../actions/ChangelogActions';
import * as MessageActions from '../actions/MessageActions';
import * as WindowActions from '../actions/WindowActions';
import * as CardsAPI from '../api/Cards';
import * as HasDispatcher from '../containers/HasDispatcher';
import * as HasMe from '../containers/HasMe';
import Dispatcher from '../dispatcher/Dispatcher';
import * as BugsnagHelper from '../helpers/bugsnag';
import history from '../helpers/history';
import { Account } from './account/Account';
import BuildBuilder from './builder/Builder';
import { CardListPage } from './builder/CardListPage';
import { CardBotConnect } from './cardbot/CardBotConnect';
import { CardBotControl } from './cardbot/CardBotControl';
import { Collection } from './collection/Collection';
import { Deck } from './deckbuilder/Deck';
import { Decks } from './deckbuilder/Decks';
import BuildNotFound from './not-found/NotFound';
import BuildOops from './not-found/Oops';
import BuildDownloadApps from './pages/DownloadApps';
import BuildLandingPage from './pages/LandingPage';
import BuildLogin from './pages/Login';
import BuildNewPassword from './pages/NewPassword';
import BuildPrivacyPolicy from './pages/PrivacyPolicy';
import BuildResendConfirmation from './pages/ResendConfirmation';
import BuildResetPassword from './pages/ResetPassword';
import BuildSignUp from './pages/SignUp';
import { Settings } from './settings/Settings';

BugsnagHelper.start([new BugsnagPluginReact()]);
const reactPlugin = Bugsnag.getPlugin('react');
if (!reactPlugin) {
  console.error('Failed to load Bugsnag react plugin');
}

const ErrorBoundary = (reactPlugin && reactPlugin.createErrorBoundary(React)) || React.Component;

interface IProps {}

interface IState {}

function BuildRouter(dispatcher: Dispatcher) {
  const LandingPage = BuildLandingPage(dispatcher);
  const DownloadApps = BuildDownloadApps(dispatcher);
  const PrivacyPolicy = BuildPrivacyPolicy(dispatcher);
  const Login = BuildLogin(dispatcher);
  const SignUp = BuildSignUp(dispatcher);
  const ResetPassword = BuildResetPassword(dispatcher);
  const NewPassword = BuildNewPassword(dispatcher);
  const ResendConfirmation = BuildResendConfirmation(dispatcher);
  const Builder = BuildBuilder(dispatcher);
  const NotFound = BuildNotFound(dispatcher);
  const Oops = BuildOops(dispatcher);

  class Routes extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    /**
     * @override
     * @constructor
     */
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = {};
    }

    /**
     * @override
     */
    componentDidUpdate() {
      CardsAPI.abort();
      ChangelogsAPI.abort();
      MessageActions.clear(this.props.dispatcher);
      window.scrollTo(0, 0);
    }

    /**
     * @override
     */
    shouldComponentUpdate(nextProps: HasDispatcher.IProps & HasMe.IProps & IProps, nextState: IState) {
      return this.props.me.get('id') !== nextProps.me.get('id');
    }

    /**
     * @override
     */
    render(): JSX.Element {
      if (this.props.me.get('id')) {
        return (
          <ErrorBoundary>
            <Router history={history}>
              <Switch>
                <Redirect exact from="/" to="/collection" />
                <Redirect exact from="/login" to="/collection" />
                <Redirect exact from="/signup" to="/collection" />
                <Redirect exact from="/reset" to="/collection" />
                <Redirect exact from="/builder" to="/builder/staging" />
                <Redirect exact from="/builder/add-cards" to="/builder/staging" />
                <Redirect exact from="/settings" to="/settings/preferences" />
                <Redirect exact from="/collection/public/:username" to="/:username/collection" />
                <Redirect exact from="/cardbot-control" to="/cardbot-connect" />
                <Redirect exact from="/decks/build/:deck" to="/decks/:deck/build/" />
                <Route
                  path="/settings/subscriptions"
                  component={(props: any) => {
                    // Redirect 'subscriptions' to 'subscription' to support deprecated PayPal cancellation URLs.
                    // react-router does not offer native methods of preserving queries in our current version,
                    // requiring the use of hacks. TODO: Update react-router and replace this with something less hacky.
                    return (
                      <Redirect
                        to={{
                          pathname: props.location.pathname.replace('subscriptions', 'subscription'),
                          search: props.location.search,
                        }}
                      />
                    );
                  }}
                />
                <Route exact path="/apps" component={DownloadApps} />
                <Route exact path="/privacy" component={PrivacyPolicy} />
                <Route exact path="/resend" component={ResendConfirmation} />
                <Route exact path="/builder/:page" component={Builder} />
                <Route
                  exact
                  path="/builder/card-lists/:id"
                  component={HasDispatcher.Attach(CardListPage, this.props.dispatcher)}
                />
                <Route
                  exact
                  path="/:username/collection"
                  component={HasDispatcher.Attach(Collection, this.props.dispatcher)}
                />
                {/* Deprecated, use '/decks/:deck' instead as the username is useless */}
                <Route
                  exact
                  path="/:username/decks/:deck/:page"
                  component={HasDispatcher.Attach(Deck, this.props.dispatcher)}
                />
                {/* Deprecated, use '/decks/:deck' instead as the username is useless */}
                <Route
                  exact
                  path="/:username/decks/:deck"
                  component={HasDispatcher.Attach(Deck, this.props.dispatcher)}
                />
                <Route exact path="/collection" component={HasDispatcher.Attach(Collection, this.props.dispatcher)} />
                <Route exact path="/decks/:deck/:page" component={HasDispatcher.Attach(Deck, this.props.dispatcher)} />
                <Route exact path="/decks/:deck" component={HasDispatcher.Attach(Deck, this.props.dispatcher)} />
                <Route exact path="/decks" component={HasDispatcher.Attach(Decks, this.props.dispatcher)} />
                <Route exact path="/statistics" component={HasDispatcher.Attach(Account, this.props.dispatcher)} />
                <Route exact path="/settings/:page" component={HasDispatcher.Attach(Settings, this.props.dispatcher)} />
                <Route
                  exact
                  path="/cardbot-connect"
                  component={HasDispatcher.Attach(CardBotConnect, this.props.dispatcher)}
                />
                <Route
                  exact
                  path="/cardbot-control/:deviceID"
                  component={HasDispatcher.Attach(CardBotControl, this.props.dispatcher)}
                />
                <Route exact path="/oops" component={Oops} />
                <Route exact path="*" component={NotFound} />
              </Switch>
            </Router>
          </ErrorBoundary>
        );
      }
      return (
        <ErrorBoundary>
          <Router history={history}>
            <Switch>
              <Redirect exact from="/builder" to="/login" />
              <Redirect exact from="/collection" to="/login" />
              <Redirect exact from="/statistics" to="/login" />
              <Redirect exact from="/settings" to="/login" />
              <Redirect exact from="/decks" to="/login" />
              <Redirect exact from="/collection/public/:username" to="/:username/collection" />
              <Route exact path="/decks/:deck/:page" component={HasDispatcher.Attach(Deck, this.props.dispatcher)} />
              <Route exact path="/decks/:deck" component={HasDispatcher.Attach(Deck, this.props.dispatcher)} />
              {/* Deprecated, use '/decks/:deck' instead as the username is useless */}
              <Route
                exact
                path="/:username/decks/:deck"
                component={HasDispatcher.Attach(Deck, this.props.dispatcher)}
              />
              <Route
                exact
                path="/:username/collection"
                component={HasDispatcher.Attach(Collection, this.props.dispatcher)}
              />
              <Route exact path="/" component={LandingPage} />
              <Route exact path="/apps" component={DownloadApps} />
              <Route exact path="/privacy" component={PrivacyPolicy} />
              <Route exact path="/login" component={Login} />
              <Route exact path="/signup" component={SignUp} />
              <Route exact path="/reset" component={ResetPassword} />
              <Route exact path="/new-password" component={NewPassword} />
              <Route exact path="/resend" component={ResendConfirmation} />
              <Route exact path="/oops" component={Oops} />
              <Route exact path="*" component={NotFound} />
            </Switch>
          </Router>
        </ErrorBoundary>
      );
    }
  }

  return HasDispatcher.Attach<IProps>(HasMe.Attach<HasDispatcher.IProps & IProps>(Routes), dispatcher);
}

const dispatcher = new Dispatcher();

// Pre-load stores that shouldn't be lazily loaded
dispatcher.IntercomStore();
dispatcher.MessageStore();
dispatcher.WindowStore();

WindowActions.init(dispatcher);

window.addEventListener('resize', () => {
  if (
    window.innerWidth !== dispatcher.WindowStore().getState().get('width') ||
    window.innerHeight !== dispatcher.WindowStore().getState().get('height')
  ) {
    WindowActions.resize(window.innerWidth, window.innerHeight, dispatcher);
  }
});

history.listen(() => {
  CardsAPI.abort();
  ChangelogsAPI.abort();
});

ReactDOM.render(React.createElement(BuildRouter(dispatcher)), document.getElementById('react-root'));
