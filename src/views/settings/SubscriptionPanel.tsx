import { CreateSubscriptionActions } from '@paypal/paypal-js/types/components/buttons';
import React from 'react';
import * as MessageActions from '../../actions/MessageActions';
import * as SubscriptionActions from '../../actions/SubscriptionActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import displayCurrency from '../../helpers/currency_helper';
import { PaymentProvider } from '../../models/PaymentEnums';
import {
  apiFromEnums,
  PlanLength,
  PlanType,
  planTypeFromSubscriptionType,
  priceString,
  specialOffers,
  subscriptionTitle,
  SubscriptionType,
  Token,
} from '../../models/Subscriptions';
import SubscriptionFeaturesList from './SubscriptionFeaturesList';
import SubscriptionPayment from './SubscriptionPayment';

interface IProps {
  dispatcher: Dispatcher;
  subscriptionType: SubscriptionType;
  planLength: PlanLength;
  onClickPayment: (paymentProvider: PaymentProvider, planType: PlanType, token?: Token) => void;
}

export default function SubscriptionPanel(props: IProps) {
  const planType = planTypeFromSubscriptionType(props.subscriptionType);

  const onCreatePayPalSubscription = (actions: CreateSubscriptionActions) => {
    if (planType) {
      const internalID = apiFromEnums(planType, props.planLength);
      const orderID = SubscriptionActions.planID(PaymentProvider.PAYPAL, internalID)
        .then((planID: string) => {
          return actions.subscription
            .create({ plan_id: planID })
            .then((orderID) => orderID)
            .catch((err) => {
              console.error(err);
              MessageActions.error('PayPal failed to create subscription', props.dispatcher);
              return 'ERROR';
            });
        })
        .catch((err) => {
          console.error(err);
          MessageActions.error('Could not retrieve plan ID', props.dispatcher);
          return 'ERROR';
        });

      return orderID;
    } else {
      // TODO: Bugsnag.
      console.error('planType undefined');
      return new Promise<string>(() => 'Error'); // PayPal action expects a string to be returned
    }
  };

  const onPayPalApproval = (subscriptionID?: string) => {
    if (planType) {
      if (subscriptionID) {
        props.onClickPayment(PaymentProvider.PAYPAL, planType, new Token({ id: subscriptionID }));
      } else {
        MessageActions.error('PayPal returned empty subscriptionID', props.dispatcher);
      }
    } else {
      // TODO: Bugsnag.
      console.error('planType undefined');
    }

    return new Promise<void>(() => {}); // PayPal requires an empty promise
  };

  const specialOffer = specialOffers(props.planLength, planType);

  let priceDisplay: JSX.Element;
  if (specialOffer) {
    priceDisplay = (
      <div className="heading-sm is-centered">
        <del>{displayCurrency(specialOffer.originalPrice, false)}</del>
        {priceString(props.subscriptionType, props.planLength)}
      </div>
    );
  } else {
    priceDisplay = (
      <div className="heading-sm is-centered">{priceString(props.subscriptionType, props.planLength)}</div>
    );
  }

  const panelContent = (
    <div className="flex vertical subscription-table-container">
      <div className="heading-lg is-centered">{subscriptionTitle(props.subscriptionType)}</div>
      {priceDisplay}
      <div className="section-divider" style={{ marginTop: '0.8rem' }} />
      <div className="features-list-container row is-centered">
        <SubscriptionFeaturesList subscriptionType={props.subscriptionType} />
      </div>
      {planType ? (
        <SubscriptionPayment
          onOpenPayPal={onCreatePayPalSubscription}
          onPayPalApproval={onPayPalApproval}
          onOpenStripe={() => props.onClickPayment(PaymentProvider.STRIPE, planType)}
        />
      ) : null}
    </div>
  );

  return (
    <div className="col-xs-12 col-lg-4">
      {specialOffer === undefined ? (
        panelContent
      ) : (
        <>
          <div className="subscription-panel-offer">
            <div className="heading-sm is-centered offer-text">
              {specialOffer.message}
              <sup>*</sup>
            </div>
            {panelContent}
          </div>
          <div className="offer-fine-print">
            <sup>* </sup>
            {specialOffer.finePrint}
          </div>
        </>
      )}
    </div>
  );
}
