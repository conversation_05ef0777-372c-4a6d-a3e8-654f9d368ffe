import IsEmail from 'isemail';
import * as React from 'react';
import * as CardActions from '../../actions/CardActions';
import * as DeckActions from '../../actions/DeckActions';
import * as MessageActions from '../../actions/MessageActions';
import * as UserActions from '../../actions/UserActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import * as HasSubscription from '../../containers/HasSubscription';
import { environment } from '../../helpers/environment';
import { Dialog } from '../components/Dialog';
import { ImageSelect } from '../components/ImageSelect';

interface IProps {}

interface IState {
  username?: string;
  email?: string;
  password?: string;
  newPassword?: string;
  confirmPassword?: string;
  showDeleteDialog?: boolean;
  showClearDialog?: boolean;
  showClearDecksDialog?: boolean;
  showDeleteError?: boolean;
}

export default class AccountSettings extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
  refs: {
    [key: string]: Element;
    deleteUsername: HTMLInputElement;
  };

  /**
   * @override
   * @constructor
   */
  constructor(props: HasDispatcher.IProps & HasMe.IProps & HasSubscription.IProps & IProps) {
    super(props);
    this.state = {
      username: props.me.get('username'),
      email: props.me.get('emailAddress'),
      password: '',
      newPassword: '',
      confirmPassword: '',
      showDeleteDialog: false,
      showClearDialog: false,
      showClearDecksDialog: false,
      showDeleteError: false,
    };
  }

  /**
   * @override
   */
  componentWillReceiveProps(props: HasDispatcher.IProps & HasMe.IProps & HasSubscription.IProps & IProps) {
    this.setState({
      username: props.me.get('username'),
      email: props.me.get('emailAddress'),
    });
  }

  /**
   * @override
   */
  render() {
    return (
      <div className="account-settings-container">
        <Dialog
          isOpen={this.state.showDeleteDialog || false}
          onDismiss={() => {
            this.setState({ showDeleteDialog: false });
          }}
        >
          <h3 className="query-dialog-heading">Are you sure?</h3>
          <p className="query-dialog-subheading">
            This will remove all of your profile, cards and statistics! This decision cannot be reversed!
          </p>
          {this.state.showDeleteError ? (
            <p className="query-dialog-subheading" style={{ color: '#CD0000' }}>
              To delete your account, please confirm your username.
            </p>
          ) : undefined}
          <input type="text" className="input" ref="deleteUsername" required placeholder="Enter Username" />
          <div className="flex justify-center" style={{ marginTop: '1rem' }}>
            <input
              type="submit"
              className="button-alert"
              value="Remove"
              autoComplete="off"
              onClick={this.onClickDeleteAccount.bind(this)}
            />
          </div>
        </Dialog>

        <Dialog
          isOpen={this.state.showClearDialog || false}
          onDismiss={() => {
            this.setState({ showClearDialog: false });
          }}
        >
          <h3 className="query-dialog-heading">Are you sure?</h3>
          <p className="query-dialog-subheading">
            This will remove all of your cards and statistics! This decision cannot be reversed!
          </p>
          {this.state.showDeleteError ? (
            <p className="query-dialog-subheading" style={{ color: '#CD0000' }}>
              To clear your collection, please confirm your username.
            </p>
          ) : undefined}
          <input type="text" className="input" ref="deleteUsername" required placeholder="Enter Username" />
          <div className="flex justify-center" style={{ marginTop: '1rem' }}>
            <input
              type="submit"
              className="button-alert"
              value="Remove"
              autoComplete="off"
              onClick={this.onClickClearCollection.bind(this)}
            />
          </div>
        </Dialog>

        <Dialog
          isOpen={this.state.showClearDecksDialog || false}
          onDismiss={() => {
            this.setState({ showClearDecksDialog: false });
          }}
        >
          <h3 className="query-dialog-heading">Are you sure?</h3>
          <p className="query-dialog-subheading">
            This will remove all of your decks! This decision cannot be reversed!
          </p>
          {this.state.showDeleteError ? (
            <p className="query-dialog-subheading" style={{ color: '#CD0000' }}>
              To clear your decks, please confirm your username.
            </p>
          ) : undefined}
          <input type="text" className="input" ref="deleteUsername" required placeholder="Enter Username" />
          <div className="flex justify-center" style={{ marginTop: '1rem' }}>
            <input
              type="submit"
              className="button-alert"
              value="Remove"
              autoComplete="off"
              onClick={this.onClickClearDecks.bind(this)}
            />
          </div>
        </Dialog>
        <form className="form offset-sm-3 col-sm-6">
          <div className="row align-center">
            <label className="label offset-sm-2 col-sm-3" htmlFor="email">
              Picture
            </label>
            <div className="col-sm-7" style={{ padding: '0rem' }}>
              <ImageSelect
                src={this.props.me.get('avatar').get('large')}
                onChangeImage={this.onChangeProfilePicture.bind(this)}
              />
            </div>
          </div>

          <div className="row">
            <label className="label col-sm-12 col-lg-2" htmlFor="email">
              Username
            </label>
            <input
              className="input col-sm-12 col-lg-10"
              ref="username"
              type="text"
              placeholder="username"
              value={this.state.username}
              onChange={this.onChangeForm.bind(this, 'username')}
            />
          </div>

          <div className="row">
            <label className="label col-sm-12 col-lg-2" htmlFor="email">
              Email
            </label>
            <input
              className="input col-sm-12 col-lg-10"
              ref="email"
              type="email"
              placeholder="<EMAIL>"
              value={this.state.email}
              onChange={this.onChangeForm.bind(this, 'email')}
            />
          </div>

          <div className="row">
            <label className="label col-sm-12 col-lg-2" htmlFor="email">
              Password<span className="label-caption">current</span>
            </label>
            <input
              className="input col-sm-12 col-lg-10"
              ref="password"
              type="password"
              value={this.state.password}
              onChange={this.onChangeForm.bind(this, 'password')}
            />
          </div>

          <div className="row">
            <label className="label col-sm-12 col-lg-2" htmlFor="email">
              Password<span className="label-caption">new</span>
            </label>
            <input
              className="input col-sm-12 col-lg-10"
              ref="newPassword"
              type="password"
              value={this.state.newPassword}
              onChange={this.onChangeForm.bind(this, 'newPassword')}
            />
          </div>

          <div className="row">
            <label className="label col-sm-12 col-lg-2" htmlFor="email">
              Password<span className="label-caption">confirmation</span>
            </label>
            <input
              className="input col-sm-12 col-lg-10"
              ref="confirmPassword"
              type="password"
              value={this.state.confirmPassword}
              onChange={this.onChangeForm.bind(this, 'confirmPassword')}
            />
          </div>

          <div className="row">
            <input
              className="button-primary offset-xs-6 col-xs-6 offset-sm-6 col-sm-6 offset-md-6 col-md-6 offset-sm-9 col-sm-3"
              type="submit"
              value="Update"
              onClick={this.onClickUpdate.bind(this)}
            />
          </div>

          <div className="row">
            <div className="flex horizontal justify-end col-xs-12">
              <ul className="form-options">
                <li>
                  <a onClick={this.onClickShowClearConfirmation.bind(this)}>
                    <strong>Clear Collection?</strong>
                  </a>
                </li>
                <li>
                  <a onClick={this.onClickShowClearDecksConfirmation.bind(this)}>
                    <strong>Clear Decks?</strong>
                  </a>
                </li>
                <li>
                  <a onClick={this.onClickShowDeleteConfirmation.bind(this)}>
                    <strong>Delete Account?</strong>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </form>
      </div>
    );
  }

  private onChangeForm(prop: string, evt: React.SyntheticEvent<HTMLElement>) {
    const target = evt.target;
    if (target instanceof HTMLInputElement) {
      const state: Record<string, HTMLInputElement['value']> = {};
      state[prop] = target.value;
      this.setState(state);
    }
  }

  private onClickUpdate(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    // check if password should be updated
    if (this.state.newPassword && this.state.newPassword.length) {
      // validate password inputs
      if (!this.state.password || !this.state.password.length) {
        return MessageActions.error('Please enter your password', this.props.dispatcher);
      }
      if (this.state.newPassword.length < 8) {
        return MessageActions.error('Please enter a password with at least 8 characters', this.props.dispatcher);
      }
      if (!this.state.confirmPassword || this.state.confirmPassword !== this.state.newPassword) {
        return MessageActions.error('Please confirm your new password', this.props.dispatcher);
      }
      // update password
      UserActions.updatePassword(this.state.newPassword, this.state.password, this.props.dispatcher)
        .then(() => {
          this.setState({ newPassword: '', password: '', confirmPassword: '' });
          return MessageActions.success('Password changed successfully', this.props.dispatcher);
        })
        .catch((err) => {
          if (err) {
            console.error(err);
            if (err.status === 400) {
              return MessageActions.error('Current password is incorrect', this.props.dispatcher);
            } else if (err.error && err.error.length) {
              return MessageActions.error(err.error, this.props.dispatcher);
            }
          }

          MessageActions.error('An unknown error occurred', this.props.dispatcher);
        });
    }
    // validate username inputs
    if (!this.state.username || !this.state.username.length) {
      return MessageActions.error('Please enter a username', this.props.dispatcher);
    }
    if (this.state.username.includes('/')) {
      return MessageActions.error('Username must not contain unsafe character "/"', this.props.dispatcher);
    }
    // validate email inputs
    if (!this.state.email || !this.state.email.length || !IsEmail.validate(this.state.email)) {
      return MessageActions.error('Please enter a valid email address', this.props.dispatcher);
    }
    // update
    if (
      this.state.username !== this.props.me.get('username') ||
      this.state.email !== this.props.me.get('emailAddress')
    ) {
      UserActions.update(this.state.username, this.state.email, this.props.dispatcher)
        .then(() => {
          MessageActions.success('Update successful', this.props.dispatcher);
        })
        .catch((err) => {
          if (err && err.error && err.error.status) {
            if (err.error.status === 400) {
              MessageActions.error('Email address or username already taken', this.props.dispatcher);
            }
          }
          MessageActions.error('An unknown error occurred', this.props.dispatcher);
        });
    }
  }

  private onClickShowClearConfirmation(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({ showClearDialog: true });
  }

  private onClickShowClearDecksConfirmation(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({ showClearDecksDialog: true });
  }

  private onClickShowDeleteConfirmation(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({ showDeleteDialog: true });
  }

  private onClickClearCollection(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    const username = this.refs.deleteUsername.value;

    if (username && username.length) {
      if (username === this.props.me.get('username')) {
        CardActions.clearAll(this.props.dispatcher)
          .then(() => {
            MessageActions.success('Your collection was successfully cleared', this.props.dispatcher);
            this.setState({ showClearDialog: false });
          })
          .catch((err) => {
            err && console.error(err);
            if (err && err.error && err.error.length) {
              return MessageActions.error(err.error, this.props.dispatcher);
            }
            MessageActions.error('An unknown error occurred', this.props.dispatcher);
          });
      } else {
        this.setState({ showDeleteError: true });
      }
    } else {
      this.setState({ showDeleteError: true });
    }
  }

  private onClickClearDecks(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    const username = this.refs.deleteUsername.value;

    if (username && username.length) {
      if (username === this.props.me.get('username')) {
        DeckActions.clearAll(this.props.dispatcher)
          .then(() => {
            MessageActions.success('Your decks were successfully cleared', this.props.dispatcher);
            this.setState({ showClearDecksDialog: false });
          })
          .catch((err) => {
            err && console.error(err);
            if (err && err.error && err.error.length) {
              return MessageActions.error(err.error, this.props.dispatcher);
            }
            MessageActions.error('An unknown error occurred', this.props.dispatcher);
          });
      } else {
        this.setState({ showDeleteError: true });
      }
    } else {
      this.setState({ showDeleteError: true });
    }
  }

  private onClickDeleteAccount(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    const username = this.refs.deleteUsername.value;

    if (username && username.length) {
      if (username === this.props.me.get('username')) {
        UserActions.destroy(this.props.dispatcher)
          .then(() => {
            location.assign(environment().homeURL());
          })
          .catch((err) => {
            err && console.error(err);
            if (err && err.error && err.error.length) {
              return MessageActions.error(err.error, this.props.dispatcher);
            }
            MessageActions.error('An unknown error occurred', this.props.dispatcher);
          });
      } else {
        this.setState({ showDeleteError: true });
      }
    } else {
      this.setState({ showDeleteError: true });
    }
  }

  private onChangeProfilePicture(file: File, crop: { x: number; y: number; width: number; height: number }) {
    UserActions.updateAvatar(file, crop, this.props.dispatcher)
      .then(() => {
        MessageActions.success('Upload successful. Refreshing...', this.props.dispatcher);
        setTimeout(function () {
          location.assign(location.href);
        }, 2000);
      })
      .catch((err) => {
        err && console.error(err);
        MessageActions.error('Your profile picture could not be updated. Try again later.', this.props.dispatcher);
      });
  }
}
