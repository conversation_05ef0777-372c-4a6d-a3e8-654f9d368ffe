import * as React from 'react';
import * as HasMe from '../../containers/HasMe';
import * as HasSubscription from '../../containers/HasSubscription';
import isServerside from '../../helpers/serverside';

const Clipboard: any = !isServerside() ? require('clipboard') : null;

interface IProps {}

interface IState {
  copyText: string;
}

export default class SubscriptionCredits extends React.Component<
  HasMe.IProps & HasSubscription.IProps & IProps,
  IState
> {
  private clipper: any;

  /**
   * @override
   * @constructor
   */
  constructor(props: HasMe.IProps & HasSubscription.IProps & IProps) {
    super(props);
    this.state = {
      copyText: 'Copy',
    };
  }

  /**
   * @override
   */
  componentDidMount() {
    this.clipper = new Clipboard('#share-button');
    this.clipper.on('success', (evt: any) => {
      evt.clearSelection();
      this.setState({ copyText: 'Copied' });
    });
    this.clipper.on('error', (evt: any) => {
      this.setState({ copyText: 'CMD+C to copy' });
    });
  }

  /**
   * @override
   */
  componentWillUnmount() {
    this.clipper.destroy();
  }

  /**
   * @override
   */
  render() {
    const creditAmount = 9;

    // TODO: CSS and alignment could probably be improved here. Not a priority.
    return (
      <div>
        <h1 className="page-heading has-underline">Earn Credits</h1>
        <h2 className="subscription-heading">Help us grow!</h2>
        <div className="row align-center col-xs-12 offset-lg-1 col-lg-10">
          <div className="row align-center">
            <p className="col-xs-12 paragraph-sm is-centered">
              {`Receive $${creditAmount} in credits for each person you refer to the platform. Share
          this link with your friends to start earning. We’ll also credit their
          account, so they can start their subscription with a free month.`}
            </p>
          </div>
          <div className="col-xs-12 offset-lg-1 col-lg-9">
            <input
              id="share-link"
              title="Copy me"
              className="input"
              value={this.props.me.get('referralLink')}
              readOnly
            />
          </div>
          <div className="col-xs-12 col-lg-2 flex">
            <div
              id="share-button"
              className="button-primary"
              style={{ margin: '1rem auto' }}
              data-clipboard-target="#share-link"
            >
              {this.state.copyText}
            </div>
          </div>
        </div>
      </div>
    );
  }
}
