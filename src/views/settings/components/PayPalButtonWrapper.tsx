import { CreateSubscriptionActions } from '@paypal/paypal-js/types/components/buttons';
import { PayPalButtons, usePayPalScriptReducer } from '@paypal/react-paypal-js';
import React from 'react';
interface IProps {
  onOpen: (actions: CreateSubscriptionActions) => Promise<string>;
  onApprove: (subscriptionID?: string) => Promise<void>;
}

enum PayPalState {
  LOADING,
  RESOLVED,
  READY,
  ERROR,
}

export default function PayPalButtonWrapper(props: IProps) {
  const [{ isResolved, isRejected }] = usePayPalScriptReducer();
  const [buttonInitialized, setInitialized] = React.useState<boolean>(false);

  let payPalState = PayPalState.LOADING;

  if (isResolved) {
    if (buttonInitialized) {
      payPalState = PayPalState.READY;
    } else {
      payPalState = PayPalState.RESOLVED;
    }
  } else if (isRejected) {
    payPalState = PayPalState.ERROR;
  }

  const button = (
    <PayPalButtons
      disabled={!buttonInitialized}
      forceReRender={[props.onOpen, props.onApprove]}
      style={{ color: 'blue', height: 32 }}
      createSubscription={(_data, actions) => props.onOpen(actions)}
      onApprove={(data, _actions) => {
        // Normalize null to undefined
        let subscriptionID = data.subscriptionID;
        if (!subscriptionID) subscriptionID = undefined;

        return props.onApprove(subscriptionID);
      }}
      onError={(err) => {
        console.error('PayPal Error: ', err && err.message);
      }}
      onInit={() => setInitialized(true)}
      fundingSource={'paypal'}
    />
  );

  switch (payPalState) {
    case PayPalState.LOADING:
      return <div className="spinner-xs" />;
    case PayPalState.RESOLVED:
      // Return spinner with hidden but initializing button
      return (
        <>
          <div className="spinner-xs" />
          <div style={{ display: 'none' }}>{button}</div>
        </>
      );
    case PayPalState.READY:
      // If script resolved and button initialized, just render the button
      return button;
    case PayPalState.ERROR:
      return null;
    default:
      return null;
  }
}
