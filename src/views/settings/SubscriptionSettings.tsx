import { PayPalScriptProvider } from '@paypal/react-paypal-js';
import moment from 'moment';
import * as React from 'react';
import * as MessageActions from '../../actions/MessageActions';
import * as SubscriptionActions from '../../actions/SubscriptionActions';
import * as UserActions from '../../actions/UserActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import * as HasSubscription from '../../containers/HasSubscription';
import displayCurrency from '../../helpers/currency_helper';
import { Environment } from '../../helpers/environment';
import track from '../../helpers/ga_helper';
import isServerside from '../../helpers/serverside';
import { PaymentProvider, PaymentSubscriptionType } from '../../models/PaymentEnums';
import {
  apiFromEnums,
  FreeLimits,
  PlanLength,
  PlanType,
  priceBuilder,
  Subscription,
  subscriptionTitle,
  SubscriptionType,
  Token,
} from '../../models/Subscriptions';
import { Dialog } from '../components/Dialog';
import UnsubscribeForm from '../components/UnsubscribeForm';
import SubscriptionPanel from './SubscriptionPanel';
import SubscriptionTabPanels from './SubscriptionTabPanels';

interface IProps {}

interface IState {
  planLength: PlanLength;
  showCancelDialog?: boolean;
  showUnsubscribeForm?: boolean;
  disableSubscribeButton?: boolean;
}

export default class SubscriptionSettings extends React.Component<
  HasDispatcher.IProps & HasMe.IProps & HasSubscription.IProps & IProps,
  IState
> {
  refs: {
    [key: string]: HTMLElement;
    couponCodeInput: HTMLInputElement;
  };

  private stripeHandler: StripeCheckoutHandler;

  /**
   * @override
   * @constructor
   */
  constructor(props: HasDispatcher.IProps & HasMe.IProps & HasSubscription.IProps & IProps) {
    super(props);
    this.state = {
      planLength: PlanLength.YEARLY,
      showCancelDialog: false,
      showUnsubscribeForm: false,
      disableSubscribeButton: false,
    };
  }

  /**
   * @override
   */
  componentDidMount() {
    this.stripeHandler = StripeCheckout.configure({
      key: window['STRIPE_PUBLISHABLE_KEY'],
      image: '/images/logo-purple-small.png',
      locale: 'auto',
    });
  }

  /**
   * @override
   */
  componentWillUnmount() {
    this.stripeHandler.close();
  }

  /**
   * @override
   */
  render() {
    return (
      <div className="subscription-container">
        <Dialog
          isOpen={this.state.showCancelDialog || false}
          onDismiss={() => {
            this.setState({ showCancelDialog: false });
          }}
        >
          <h3 className="query-dialog-heading">Are you sure you want to cancel your subscription?</h3>
          <p className="query-dialog-subheading">
            You will be limited to {FreeLimits.CARDS} visible cards, and not be able to track the value of your
            collection!
          </p>
          <div className="flex justify-center">
            <button
              className="button-primary"
              onClick={() => {
                this.setState({ showCancelDialog: false, showUnsubscribeForm: true });
              }}
            >
              Continue
            </button>
          </div>
        </Dialog>
        <UnsubscribeForm
          isOpen={this.state.showUnsubscribeForm || false}
          onComplete={this.onClickCancelSubscription.bind(this)}
          onDismiss={() => {
            this.setState({ showUnsubscribeForm: false });
          }}
        />
        {
          // TODO: These functions should be completely redone to construct strings in a less confusing way (for developers).
          this.props.me.get('subscribed')
            ? this.renderSubscriptionDetails()
            : this.renderSubscriptionOptions('You are a Squire', 'Upgrade today for unlimited cards and more!')
        }
      </div>
    );
  }

  private renderSubscriptionDetails() {
    const subscription = this.props.subscription;
    const activePremium = subscription.get('active') && (subscription.isKnight() || subscription.isMerchant());
    const subscriptionCost = subscription.get('cost');
    const credits = this.props.me.get('credit');

    let cost = '';
    if (
      activePremium &&
      subscription.get('provider') !== PaymentSubscriptionType.NONE &&
      this.props.me.get('credit') !== 0
    ) {
      let creditPayment = credits;
      if (creditPayment > subscriptionCost) {
        creditPayment = subscriptionCost;
      }
      if (subscription.get('provider') === PaymentSubscriptionType.STRIPE) {
        cost = `. You have ${displayCurrency(credits, false)} in credits. You will spend ${displayCurrency(
          creditPayment,
          false,
        )} in credits`;
        const difference = Math.max(creditPayment - credits, 0);
        if (difference > 0) {
          cost += ` and be charged ${displayCurrency(difference, false)}.`;
        } else {
          cost += '.';
        }
      }
      // if paypal and not cancelled
      else {
        cost = `. You have ${displayCurrency(
          credits,
          false,
        )} in credits. For PayPal customers, credits will be return in the form of refunds after each invoice. Please contact us if you do not receive your credits.`;
      }
    } else {
      cost = activePremium ? '. You will be charged ' + displayCurrency(subscriptionCost, false) : '.';
    }

    // Calculate the user message for subscription
    const expiry = subscription.get('expiresAt');
    let message = '';
    if (credits !== 0) {
      message = `You have ${displayCurrency(credits, false)} of credits`;
    }

    switch (subscription.get('type')) {
      case SubscriptionType.KNIGHT:
      case SubscriptionType.MERCHANT:
        if (subscription.get('active')) {
          message = 'Your subscription will automatically renew on ';
        } else {
          message = 'Your subscription was cancelled and will end on ';
        }
        message += moment(expiry).format('LL');
        message += cost;
        break;
      case SubscriptionType.KNIGHT_PROMOTIONAL:
      case SubscriptionType.MERCHANT_PROMOTIONAL:
        message = `You're currently subscribed for free to a ${subscriptionTitle(
          subscription.get('type'),
        )} account. It will expire on 
        ${moment(expiry).format('LL')}. If you like what we do, consider upgrading today to support CardCastle`;
        return this.renderSubscriptionOptions(`${subscription.get('name')}`, message);
    }

    // Render other subscriptions
    return (
      <div>
        <h2 className="subscription-heading">{'You are a ' + subscription.get('name')}</h2>
        <div className="col-xs-12 paragraph-sm is-centered">
          {message} {this.renderChangeSubscription(subscription)}
        </div>
        {subscription.get('active') ? (
          <div className="flex vertical align-center col-xs-12">
            <button className="button-alert" style={{ marginTop: '1rem' }} onClick={this.onClickCancel.bind(this)}>
              Cancel Subscription
            </button>
          </div>
        ) : undefined}
      </div>
    );
  }

  private renderSubscriptionOptions(heading: string, message: string) {
    return (
      <PayPalScriptProvider
        options={{
          'client-id': Environment.getVar('PAYPAL_CLIENT_ID'),
          components: 'buttons',
          intent: 'subscription',
          vault: true,
        }}
      >
        <div className="subscription-container">
          <h2 className="subscription-heading">{heading}</h2>
          <h3 className="col-xs-12 paragraph-sm is-centered">{message}</h3>
          <SubscriptionTabPanels planLength={this.state.planLength} onClickPlan={this.onClickPlan.bind(this)} />
          {this.props.me.get('credit') > 0}
          <div className="row" style={{ marginTop: '1rem', alignItems: 'stretch' }}>
            <SubscriptionPanel
              dispatcher={this.props.dispatcher}
              subscriptionType={SubscriptionType.SQUIRE}
              planLength={this.state.planLength}
              onClickPayment={this.onClickPayment.bind(this)}
            />
            <SubscriptionPanel
              dispatcher={this.props.dispatcher}
              subscriptionType={SubscriptionType.KNIGHT}
              planLength={this.state.planLength}
              onClickPayment={this.onClickPayment.bind(this)}
            />
            <SubscriptionPanel
              dispatcher={this.props.dispatcher}
              subscriptionType={SubscriptionType.MERCHANT}
              planLength={this.state.planLength}
              onClickPayment={this.onClickPayment.bind(this)}
            />
          </div>
          {this.props.me.get('subscribed') ? null : this.renderCoupons()}
        </div>
      </PayPalScriptProvider>
    );
  }

  private renderChangeSubscription(subscription: Subscription) {
    switch (subscription.get('type')) {
      case SubscriptionType.KNIGHT:
      case SubscriptionType.MERCHANT:
        let intercomMessage = '';
        if (subscription.get('active')) {
          intercomMessage = 'For any inquires relating to payment';
        } else {
          intercomMessage =
            'If you would like to resubscribe to our Knight or Merchant services before the cancellation date';
        }
        return (
          <>
            {`${intercomMessage}, please contact us via `}
            <strong>
              <div
                className="button-text"
                style={{ textDecoration: 'underline' }}
                onClick={this.onClickEmail.bind(this)}
              >
                email
              </div>
            </strong>
            {' or '}
            <strong>
              <div
                className="button-text"
                style={{ textDecoration: 'underline' }}
                onClick={this.onClickDiscuss.bind(this)}
              >
                live chat.
              </div>
            </strong>
          </>
        );
      default:
        return null;
    }
  }

  private renderCoupons() {
    return (
      <div className="row vertical" style={{ marginTop: '2rem' }}>
        <h2 className="subscription-heading">Have a Promotional Coupon?</h2>
        <div className="row align-center">
          <div className="flex vertical align-center col-xs-12">
            <div style={{ width: '100%', maxWidth: '560px' }}>
              <div className="info-heading">Code</div>
              <input ref="couponCodeInput" title="Copy me" className="input" placeholder="Enter coupon code" />
            </div>
            <button className="button-primary" style={{ marginTop: '1rem' }} onClick={this.onClickClaim.bind(this)}>
              Claim
            </button>
          </div>
        </div>
      </div>
    );
  }

  private onClickEmail(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    window.location.href = 'mailto:<EMAIL>';
  }

  private onClickDiscuss(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    if (!isServerside() && window['Intercom']) {
      window['Intercom']('show');
    }
  }

  private onClickClaim(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    if (!this.refs.couponCodeInput || !this.refs.couponCodeInput.value || !this.refs.couponCodeInput.value.length) {
      return;
    }
    SubscriptionActions.claimCoupon(this.refs.couponCodeInput.value, this.props.dispatcher)
      .then(() => {
        return UserActions.me(this.props.dispatcher);
      })
      .then(() => {
        MessageActions.success('Your coupon has been accepted', this.props.dispatcher);
      })
      .catch((err) => {
        err && console.error(err);
        if (err && err.error && err.error.length) {
          MessageActions.error(err.error, this.props.dispatcher);
        } else {
          MessageActions.error('The coupon code you entered is invalid', this.props.dispatcher);
        }
      });
  }

  private onClickPayment(paymentProvider: PaymentProvider, planType: PlanType, token?: Token) {
    // TODO: Use plan length from sub panel
    switch (paymentProvider) {
      case PaymentProvider.STRIPE:
        this.openStripe(planType, this.state.planLength);
        break;
      case PaymentProvider.PAYPAL:
        if (!token) {
          MessageActions.error('Could not subscription: Missing PayPal ID', this.props.dispatcher);
        } else {
          const plan = apiFromEnums(planType, this.state.planLength);
          const price = priceBuilder(planType, this.state.planLength);

          this.createSubscription(PaymentProvider.PAYPAL, plan, price, token);
        }
        break;
      default:
        MessageActions.error('Invalid payment provider', this.props.dispatcher);
        break;
    }
  }

  private openStripe(planType: PlanType, planLength: PlanLength) {
    if (this.state.disableSubscribeButton) return;

    const plan = apiFromEnums(planType, planLength);
    const price = priceBuilder(planType, planLength);
    const planString = `${planType === PlanType.KNIGHT ? 'Knight' : 'Merchant'} ${
      planLength === PlanLength.MONTHLY ? 'Monthly' : 'Yearly'
    }`;

    this.stripeHandler.open({
      name: 'CardCastle',
      description: planString,
      amount: price,
      email: this.props.me.get('emailAddress'),
      panelLabel: `Subscribe {{amount}}/${planLength === PlanLength.MONTHLY ? 'month' : 'year'}`,
      token: (token) => this.createSubscription(PaymentProvider.STRIPE, plan, price, new Token({ id: token.id })),
    });
  }

  private createSubscription(provider: PaymentProvider, plan: string, price: number, token: Token) {
    this.setState({ disableSubscribeButton: true });
    SubscriptionActions.subscribe(token, plan, provider, this.props.dispatcher)
      .then(() => {
        UserActions.me(this.props.dispatcher)
          .then(() => {
            return MessageActions.success(
              "You've been successfully subscribed. Thanks for the support!",
              this.props.dispatcher,
            );
          })
          .catch((err) => {
            err && console.error(err);
            if (err && err.error && err.error.length) {
              return MessageActions.error(err.error, this.props.dispatcher);
            }
            MessageActions.error('An unknown error occurred', this.props.dispatcher);
          });
        track('account', 'subscribe', plan, price / 100);
      })
      .catch((err) => {
        this.setState({ disableSubscribeButton: false });

        err && console.error(err);
        if (err && err.error && err.error.length) {
          return MessageActions.error(err.error, this.props.dispatcher);
        }
        MessageActions.error('An unknown error occurred', this.props.dispatcher);
      });
  }

  private onClickCancel(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({ showCancelDialog: true });
  }

  private onClickPlan(planLength: PlanLength, evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    this.setState({ planLength: planLength });
  }

  private onClickCancelSubscription(evt: React.SyntheticEvent<HTMLElement>, reason: string) {
    evt.preventDefault();
    SubscriptionActions.unsubscribe(reason, this.props.dispatcher)
      .then(() => {
        this.setState({ showCancelDialog: false, showUnsubscribeForm: false });
        return MessageActions.success('You have been unsubscribed. Sorry to see you go!', this.props.dispatcher);
      })
      .catch((err) => {
        err && console.error(err);
        this.setState({ showCancelDialog: false });
        if (err && err.error && err.error.length) {
          return MessageActions.error(err.error, this.props.dispatcher);
        }
        MessageActions.error('An unknown error occurred', this.props.dispatcher);
      });
  }
}
