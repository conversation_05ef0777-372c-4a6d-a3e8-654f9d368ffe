import React from 'react';
import { PlanLength } from '../../models/Subscriptions';

interface IProps {
  planLength: PlanLength;
  onClickPlan: (length: PlanLength, evt: React.SyntheticEvent<HTMLElement>) => void;
}

export default function SubscriptionTabPanels(props: IProps) {
  return (
    <div className="row align-center subscription-option-container">
      <div
        className={'subscription-option__left' + (props.planLength === PlanLength.YEARLY ? ' selected' : '')}
        onClick={(evt) => props.onClickPlan(PlanLength.YEARLY, evt)}
      >
        <div className="subscription-option-heading">Annual</div>
      </div>
      <div
        className={'subscription-option__right' + (props.planLength === PlanLength.MONTHLY ? ' selected' : '')}
        onClick={(evt) => props.onClickPlan(PlanLength.MONTHLY, evt)}
      >
        <div className="subscription-option-heading">Monthly</div>
      </div>
    </div>
  );
}
