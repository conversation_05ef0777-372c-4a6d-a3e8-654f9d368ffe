import access_time from '@iconify/icons-ic/baseline-access-time';
import attach_money from '@iconify/icons-ic/baseline-attach-money';
import sort from '@iconify/icons-ic/sort';
import style from '@iconify/icons-ic/style';
import trending_up from '@iconify/icons-ic/trending-up';
import * as moment from 'moment-timezone';
import * as React from 'react';
import * as CardActions from '../../actions/CardActions';
import * as MessageActions from '../../actions/MessageActions';
import * as UserActions from '../../actions/UserActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import { EnumHelper } from '../../helpers/enum';
import { TextFormat } from '../../helpers/fmt';
import track from '../../helpers/ga_helper';
import { Condition } from '../../models/Condition';
import { Grouping, type GroupingKey } from '../../models/Grouping';
import { MTGCardPage } from '../../models/mtg/MTGCardPage';
import { PricingSource, pricingSourceDisplayName } from '../../models/PricingSource';
import { Sorting, type SortingKey } from '../../models/sorting/Sorting';
import { Viewing } from '../../models/Viewing';
import { Checkbox } from '../components/Checkbox';
import { IconSelect } from '../components/IconSelect';
import { ConditionSelector } from '../components/selectors/ConditionSelector';

interface IProps {
  cardPage: MTGCardPage;
}

interface IState {}

export default class PreferenceSettings extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
    super(props);
    this.state = {};
  }

  /**
   * @override
   */
  render() {
    return (
      <div className="preference-settings-container">
        <div className="row">
          <div className="preference-left col-xs-12 col-md-6 col-lg-6">
            <h2 className="preference-heading">Collection</h2>
            {/* Grouping */}
            <div className="preference-row">
              <div className="col-xs-10">
                <div className="info-heading">Grouping</div>
                <IconSelect
                  className="icon-container--card-input"
                  icon={style}
                  value={this.props.me.get('preferences').get('collection').get('grouping')}
                  onChange={this.onChangeGroupBy.bind(this)}
                >
                  <>
                    <option disabled>Group By</option>
                    {Object.keys(Grouping).map((key) => (
                      <option key={key} value={Grouping[key as GroupingKey]}>
                        {TextFormat.capitalize(Grouping[key as GroupingKey])}
                      </option>
                    ))}
                  </>
                </IconSelect>
              </div>
            </div>
            {/* Sorting */}
            <div className="preference-row">
              <div className="col-xs-10">
                <div className="info-heading">Sorting</div>
                <IconSelect
                  className="icon-container--card-input"
                  icon={sort}
                  value={this.props.me.get('preferences').get('collection').get('sorting')}
                  onChange={this.onChangeSortBy.bind(this)}
                >
                  <>
                    <option disabled>Sort By</option>
                    {Object.keys(Sorting).map((key) => (
                      <option key={key} value={Sorting[key as SortingKey]}>
                        {Sorting[key as SortingKey]}
                      </option>
                    ))}
                  </>
                </IconSelect>
              </div>
            </div>

            {/* Condition */}
            <div className="preference-row">
              <div className="col-xs-10">
                <div className="info-heading">Default Condition</div>
                <ConditionSelector
                  condition={this.props.me.get('preferences').get('collection').get('condition')}
                  onChange={this.onChangeCondition.bind(this)}
                  hideTitle={true}
                />
              </div>
            </div>

            {/* View */}
            <div className="preference-row">
              <div className="col-xs-10">
                <div className="info-heading">View</div>
                <div className="collection-toggle-container" style={{ marginLeft: '0rem' }}>
                  <div
                    className={
                      'collection-toggle' +
                      (this.props.me.get('preferences').get('collection').get('viewing') === Viewing.GRID
                        ? ' selected'
                        : '')
                    }
                    onClick={this.onClickCollectionToggle.bind(this)}
                  >
                    <img
                      src={
                        this.props.me.get('preferences').get('collection').get('viewing') === Viewing.GRID
                          ? '/images/grid-icon-alt.png'
                          : '/images/grid-icon.png'
                      }
                    />
                  </div>
                  <div
                    className={
                      'collection-toggle' +
                      (this.props.me.get('preferences').get('collection').get('viewing') === Viewing.LIST
                        ? ' selected'
                        : '')
                    }
                    onClick={this.onClickCollectionToggle.bind(this)}
                  >
                    <img
                      src={
                        this.props.me.get('preferences').get('collection').get('viewing') === Viewing.LIST
                          ? '/images/list-icon-alt.png'
                          : '/images/list-icon.png'
                      }
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Publicity */}
            <div className="preference-row">
              <div className="col-xs-10">
                <div className="info-heading">Public</div>
                <div className="flex horizontal align-center">
                  <Checkbox
                    checked={this.props.me.get('preferences').get('privacy').get('public')}
                    onChange={this.onChangePublicity.bind(this)}
                  />
                  <div>
                    {this.props.me.get('preferences').get('privacy').get('public') ? (
                      <label className="preference-label">
                        <a
                          className="preference-link"
                          href={`/${this.props.me.get('username')}/collection`}
                          target="_blank"
                        >{`cardcastle.co/${this.props.me.get('username')}/collection`}</a>
                      </label>
                    ) : (
                      <div className="preference-label">Your collection is currently private</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="preference-right col-xs-12 col-md-6 col-lg-6">
            <h2 className="preference-heading">Account</h2>
            {/* Currency */}
            <div className="preference-row">
              <div className="col-xs-10">
                <div className="info-heading">Currency</div>
                <IconSelect
                  className="icon-container--card-input"
                  icon={attach_money}
                  value={this.props.me.get('preferences').get('localization').get('currency')}
                  onChange={this.onChangeCurrency.bind(this)}
                >
                  <>
                    <option disabled>Currency</option>
                    <option value="AUD">AUD</option>
                    <option value="USD">USD</option>
                    <option value="BRL">BRL</option>
                    <option value="CAD">CAD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                    <option value="ARS">ARS</option>
                    <option value="PLN">PLN</option>
                    <option value="CLP">CLP</option>
                    <option value="NZD">NZD</option>
                    <option value="COP">COP</option>
                    <option value="UYU">UYU</option>
                    <option value="TRY">TRY</option>
                    <option value="VEF">VEF</option>
                    <option value="RON">RON</option>
                    <option value="PEN">PEN</option>
                    <option value="HUF">HUF</option>
                  </>
                </IconSelect>
              </div>
            </div>
            {/* Pricing Source */}
            <div className="preference-row">
              <div className="col-xs-10">
                <div className="info-heading">Pricing Source</div>
                <IconSelect
                  className="icon-container--card-input"
                  icon={trending_up}
                  value={this.props.me.get('preferences').get('pricing').get('source')}
                  onChange={this.onChangePricingSource.bind(this)}
                >
                  <>
                    <option disabled>Pricing Source</option>
                    {EnumHelper.allCases(PricingSource).map((source: PricingSource) => (
                      <option key={source} value={source}>
                        {pricingSourceDisplayName(source)}
                      </option>
                    ))}
                  </>
                </IconSelect>
              </div>
            </div>
            {/* Timezone */}
            <div className="preference-row">
              <div className="col-xs-10">
                <div className="info-heading">Timezone</div>
                <IconSelect
                  className="icon-container--card-input"
                  icon={access_time}
                  value={this.props.me.get('preferences').get('localization').get('timezone')}
                  onChange={this.onChangeTimezone.bind(this)}
                >
                  <>
                    <option disabled>Timezone</option>
                    {moment.tz.names().map((name: string) => {
                      return (
                        <option key={'localization-' + name} value={name}>
                          {name}
                        </option>
                      );
                    })}
                  </>
                </IconSelect>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  private onClickCollectionToggle(evt: React.SyntheticEvent<HTMLElement>) {
    let collection = this.props.me.get('preferences').get('collection');
    switch (this.props.me.get('preferences').get('collection').get('viewing')) {
      case Viewing.GRID:
        collection = collection.set('viewing', Viewing.LIST);
        break;

      case Viewing.LIST:
        collection = collection.set('viewing', Viewing.GRID);
        break;
    }
    UserActions.updateCollection(collection, this.props.dispatcher)
      .then(() => {
        CardActions.updatePage(
          this.props.cardPage.merge({
            cardViewing: collection.get('viewing'),
            ownerUsername: this.props.me.get('username'),
          }),
          this.props.dispatcher,
        );
      })
      .catch((err) => {
        err && console.error(err);
      });
    track('account', 'preferences', 'view');
  }

  private onChangePublicity(isPublic: boolean) {
    let privacy = this.props.me.get('preferences').get('privacy');
    privacy = privacy.set('public', isPublic);
    UserActions.updatePrivacy(privacy, this.props.dispatcher)
      .then(() => {})
      .catch((err) => {
        err && console.error(err);
      });
    track('account', 'preferences', 'public');
  }

  private async onChangeGroupBy(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    let collection = this.props.me.get('preferences').get('collection');
    collection = collection.set('grouping', (evt.target as HTMLSelectElement).value as Grouping);
    try {
      const user = await UserActions.updateCollection(collection, this.props.dispatcher);
      CardActions.updatePage(
        this.props.cardPage.merge({
          cardGrouping: collection.get('grouping'),
          ownerUsername: user.get('username'),
        }),
        this.props.dispatcher,
      );
    } catch (err) {
      MessageActions.error('Failed to update preferences', this.props.dispatcher);
      err && console.error(err);
    }
  }

  private async onChangeSortBy(evt: React.SyntheticEvent<HTMLSelectElement>) {
    evt.preventDefault();
    const collection = this.props.me
      .get('preferences')
      .get('collection')
      .set('sorting', evt.currentTarget.value as Sorting);
    try {
      const user = await UserActions.updateCollection(collection, this.props.dispatcher);
      CardActions.updatePage(
        this.props.cardPage.merge({
          cardSorting: collection.get('sorting'),
          ownerUsername: user.get('username'),
        }),
        this.props.dispatcher,
      );
    } catch (err) {
      MessageActions.error('Failed to update preferences', this.props.dispatcher);
      err && console.error(err);
    }
  }

  private async onChangeCondition(condition: Condition) {
    const collection = this.props.me.get('preferences').get('collection').set('condition', condition);
    try {
      await UserActions.updateCollection(collection, this.props.dispatcher);
    } catch (err) {
      MessageActions.error('Failed to update preferences', this.props.dispatcher, err);
    }
  }

  private onChangeCurrency(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    let localization = this.props.me.get('preferences').get('localization');
    localization = localization.set('currency', (evt.target as HTMLSelectElement).value);
    UserActions.updateLocalization(localization, this.props.dispatcher).catch((err) => {
      err && console.error(err);
    });

    track('account', 'preferences', 'currency');
  }

  private onChangePricingSource(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    const pricing = this.props.me.get('preferences').get('pricing');
    const newValue =
      EnumHelper.match(PricingSource, (evt.target as HTMLSelectElement).value) || PricingSource.TCG_PLAYER;
    UserActions.updatePricing(pricing.set('source', newValue as PricingSource), this.props.dispatcher).catch((err) => {
      err && console.error(err);
    });

    track('account', 'preferences', 'currency');
  }

  private onChangeTimezone(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    let localization = this.props.me.get('preferences').get('localization');
    localization = localization.set('timezone', (evt.target as HTMLSelectElement).value);
    UserActions.updateLocalization(localization, this.props.dispatcher).catch((err) => {
      err && console.error(err);
    });

    track('account', 'preferences', 'timezone');
  }
}
