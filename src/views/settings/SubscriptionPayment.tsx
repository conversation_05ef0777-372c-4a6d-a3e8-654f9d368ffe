import { CreateSubscriptionActions } from '@paypal/paypal-js';
import React from 'react';
import { Size } from '../../models/Size';
import { IconButton, Position } from '../components/IconButton';
import PayPalButtonWrapper from './components/PayPalButtonWrapper';
const PayIcons = require('react-pay-icons');

interface IProps {
  onOpenPayPal: (actions: CreateSubscriptionActions) => Promise<string>;
  onPayPalApproval: (subscriptionID?: string) => Promise<void>;
  onOpenStripe: () => void;
}

export default function SubscriptionPayment(props: IProps) {
  const cardIconStyle = { height: '20px' };

  return (
    <>
      <div className="flex vertical align-center col-xs-12" style={{ marginTop: '1rem' }}>
        <div style={{ paddingTop: '1rem' }}>
          <IconButton
            buttonCSS="button-stripe"
            onClick={(evt) => {
              evt.preventDefault();
              props.onOpenStripe();
            }}
            imgSrc={'/svg/stripe.svg'}
            size={new Size({ height: '30px' })}
            position={Position.RIGHT}
            text={'Pay with '}
          />
        </div>
        <div className="row justify-center" style={{ marginTop: '0.5rem' }}>
          <div>
            <PayIcons.Visa style={cardIconStyle}></PayIcons.Visa>
          </div>
          <div className="credit-card">
            <PayIcons.Mastercard style={cardIconStyle}></PayIcons.Mastercard>
          </div>
          <div className="credit-card">
            <PayIcons.Amex style={cardIconStyle}></PayIcons.Amex>
          </div>
          <div className="credit-card">
            <PayIcons.UnionPay style={cardIconStyle}></PayIcons.UnionPay>
          </div>
          <div className="credit-card">
            <PayIcons.Jcb style={cardIconStyle}></PayIcons.Jcb>
          </div>
        </div>
      </div>
      <div className="flex vertical align-center col-xs-12" style={{ marginTop: '1rem' }}>
        <PayPalButtonWrapper
          onOpen={(actions) => props.onOpenPayPal(actions)}
          onApprove={(subscriptionID) => props.onPayPalApproval(subscriptionID)}
        />
      </div>
    </>
  );
}
