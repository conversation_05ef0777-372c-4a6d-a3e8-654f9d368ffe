import * as React from 'react';
import { Helmet } from 'react-helmet';
import { match } from 'react-router-dom';
import * as SubscriptionActions from '../../actions/SubscriptionActions';
// Actions
import * as UserActions from '../../actions/UserActions';
import * as HasCards from '../../containers/HasCards';
// Containers and dispatcher
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import * as HasSubscription from '../../containers/HasSubscription';
import Dispatcher from '../../dispatcher/Dispatcher';
import { EnumHelper } from '../../helpers/enum';
import { environment } from '../../helpers/environment';
import { TextFormat } from '../../helpers/fmt';
// Helpers
import history from '../../helpers/history';
import isServerside from '../../helpers/serverside';
import { MTGCardPage } from '../../models/mtg/MTGCardPage';
import { Subscription } from '../../models/Subscriptions';
import { pageList } from '../../models/TabItem';
// Models
import { User } from '../../models/Users';
import { Footer } from '../components/Footer';
// Components
import { Navbar } from '../components/Navbar';
import SecondaryNavBar from '../components/SecondaryNavBar';
import { SecondaryIconType, SecondaryNavBarData } from '../components/SecondaryNavButton';
// Sub-Components
import AccountSettings from './AccountSettings';
import PreferenceSettings from './PreferenceSettings';
import SubscriptionCredits from './SubscriptionCredits';
import SubscriptionSettings from './SubscriptionSettings';

interface IProps {
  match?: match<SettingsParams>;
}

interface SettingsParams {
  page: string;
}

enum SettingsPage {
  PREFERENCES = 'preferences',
  SUBSCRIPTION = 'subscription',
  ACCOUNT = 'account',
}

interface IState {
  page?: SettingsPage;
}

export const Settings = HasMe.Attach<HasDispatcher.IProps & IProps>(
  HasCards.Attach<HasDispatcher.IProps & HasMe.IProps & IProps>(
    HasSubscription.Attach<HasDispatcher.IProps & HasMe.IProps & HasCards.IProps & IProps>(
      class extends React.Component<
        HasDispatcher.IProps & HasMe.IProps & HasCards.IProps & HasSubscription.IProps & IProps,
        IState
      > {
        /**
         * @override
         * @constructor
         */
        constructor(props: HasDispatcher.IProps & HasMe.IProps & HasCards.IProps & HasSubscription.IProps & IProps) {
          super(props);
          const propsPage = (this.props.match && this.props.match.params.page) || SettingsPage.PREFERENCES;
          const page = EnumHelper.match(SettingsPage, propsPage) as SettingsPage;

          this.state = {
            page: page,
          };
        }

        /**
         * @override
         */
        componentDidMount() {
          if (this.state.page === undefined) {
            history.push('/404');
          }

          SubscriptionActions.subscription(this.props.dispatcher);
        }

        /**
         * @override
         */
        render() {
          if (isServerside()) {
            Helmet.renderStatic();
          }

          return (
            <div>
              <Helmet>
                <title>CardCastle: Settings</title>
              </Helmet>

              <Navbar dispatcher={this.props.dispatcher} me={this.props.me} selection="settings" isRibbon={true} />
              <div className="section has-footer has-navbar">
                <div className="container-fluid">
                  <div className="center-row">
                    <SecondaryNavBar
                      pages={pageList(EnumHelper.allCases(SettingsPage))}
                      selectedPage={this.state.page ? this.state.page : SettingsPage.PREFERENCES}
                      displayName={TextFormat.capitalize}
                      openPage={this.openPage}
                      rightButton={
                        new SecondaryNavBarData({
                          href: 'Log Out',
                          onClick: this.onClickLogout.bind(this),
                          iconType: SecondaryIconType.EXIT_TO_APP,
                        })
                      }
                    />
                  </div>
                  <div>{this.settingsSwitch()}</div>
                </div>

                <Footer />
              </div>
            </div>
          );
        }

        private async onClickLogout(evt: React.SyntheticEvent<HTMLElement>) {
          evt.preventDefault();
          try {
            await UserActions.logout(this.props.dispatcher);
          } catch (err) {
            console.error(err);
          }

          location.assign(environment().homeURL());
        }

        private openPage = (page: string) => {
          // Find corresponding page
          const match = EnumHelper.match(SettingsPage, page) as SettingsPage;

          if (match) {
            history.push('/settings/' + match);
            this.setState({
              page: match,
            });
          }
        };

        private settingsSwitch = () => {
          switch (this.state.page) {
            case SettingsPage.ACCOUNT:
              return <AccountSettings dispatcher={this.props.dispatcher} me={this.props.me} />;
            case SettingsPage.SUBSCRIPTION:
              return (
                <>
                  <SubscriptionSettings
                    dispatcher={this.props.dispatcher}
                    me={this.props.me}
                    subscription={this.props.subscription}
                  />
                  <SubscriptionCredits me={this.props.me} subscription={this.props.subscription} />
                </>
              );
            default:
              return (
                <PreferenceSettings
                  dispatcher={this.props.dispatcher}
                  me={this.props.me}
                  cardPage={this.props.cardPage}
                />
              );
          }
        };
      },
    ),
  ),
);

interface SettingsSwitchProps {
  dispatcher: Dispatcher;
  me: User;
  cardPage: MTGCardPage;
  subscription: Subscription;
  settingPage?: SettingsPage;
}
