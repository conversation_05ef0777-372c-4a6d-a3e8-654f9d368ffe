import { formatNumber } from 'accounting';
import React from 'react';
import { FreeLimits, SubscriptionType } from '../../models/Subscriptions';

interface IProps {
  subscriptionType: SubscriptionType;
}

export default function SubscriptionFeaturesList(props: IProps) {
  if (props.subscriptionType === SubscriptionType.KNIGHT_PROMOTIONAL) {
    return null;
  }
  return <div className="features-list">{generateList(props.subscriptionType)}</div>;
}

function generateList(subscriptionType: SubscriptionType) {
  switch (subscriptionType) {
    case SubscriptionType.SQUIRE:
      return (
        <>
          <p>
            Add up to <strong>{formatNumber(FreeLimits.CARDS).toString()}</strong> cards
          </p>
          <p>
            Create up to <strong>{FreeLimits.DECKS.toString()}</strong> decks
          </p>
          <p>
            Use <strong>#tagging</strong> to manage your collection
          </p>
          <p>
            Add up to <strong>{FreeLimits.TAGS}</strong> tags
          </p>
          <p>
            Real-time <strong>card prices</strong> - updated daily
          </p>
        </>
      );
    case SubscriptionType.KNIGHT:
      return (
        <>
          <p>
            Add <strong>unlimited</strong> cards
          </p>
          <p>
            Create <strong>unlimited</strong> decks
          </p>
          <p>
            Use <strong>#tagging</strong> to manage your cards
          </p>
          <p>
            Add <strong>unlimited</strong> tags
          </p>
          <p>
            Real-time <strong>card prices</strong> - updated daily
          </p>
          <p>
            Track the value of your <strong>entire collection</strong>
          </p>
          <p>
            <strong>Automatic entry</strong> into all giveaways and competitions
          </p>
          <p>
            Access to <strong>new features</strong> as they are released
          </p>
        </>
      );
    case SubscriptionType.MERCHANT:
      return (
        <>
          <p>
            Access to all <strong>Knight</strong> features
          </p>
          <p>
            <strong>Dedicated support</strong> for your CardBot
          </p>
          <div>
            <p style={{ marginTop: '0' }}>
              CardBot <strong>updates,</strong> including:
            </p>
            <ul>
              <li>
                New <strong>card sets</strong>
              </li>
              <li>
                New <strong>sorting features</strong>
              </li>
              <li>
                <strong>Speed</strong> and <strong>accuracy</strong> improvements
              </li>
            </ul>
          </div>
          <div>
            <p style={{ marginTop: '0' }}>Integrations, including:</p>
            <ul>
              <li>
                <strong>TCGPlayer</strong>
              </li>
              <li>
                <strong>Crystal Commerce</strong>
              </li>
              <li>
                <strong>BinderPOS</strong>
              </li>
              <li>
                <strong>CFB Marketplace</strong>
              </li>
            </ul>
          </div>
          <p>
            Custom <strong>exports</strong>
          </p>
          <p>
            Custom <strong>card prices</strong> (coming soon)
          </p>
        </>
      );
    default:
      return null;
  }
}
