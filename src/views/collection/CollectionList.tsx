import classNames from 'classnames';
import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardActions from '../../actions/CardActions';
import * as CardPanelActions from '../../actions/CardPanelActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasSelections from '../../containers/HasSelections';
import { CardInstance } from '../../models/CardInstances';
import { CardPanel as CardPanelModel } from '../../models/CardPanels';
import { CellBorders } from '../../models/CellBorder';
import { Game } from '../../models/Game';
import { Grouping } from '../../models/Grouping';
import { MTGCardGroup, MTGCardPage } from '../../models/mtg/MTGCardPage';
import { Sorting } from '../../models/sorting/Sorting';
import { Tag } from '../../models/Tags';
import { User } from '../../models/Users';
import { CardPanel, CSSGridContainer } from '../components/cardpanel/CardPanel';
import * as Table from '../components/Table';
import { ListItem } from './ListItem';

interface IProps {
  me: User;
  username: string;
  usernamePublic: boolean;
  cardPage: MTGCardPage;
  cardPanel: CardPanelModel;
  userTags: Immutable.OrderedSet<Tag>;
  isInitializing: boolean;
}

interface IState {}

export const CollectionList = HasSelections.Attach<IProps & HasDispatcher.IProps>(
  class extends React.Component<IProps & HasDispatcher.IProps & HasSelections.IProps, IState> {
    /**
     * @override
     */
    constructor(props: IProps & HasDispatcher.IProps & HasSelections.IProps) {
      super(props);
    }

    /**
     * @override
     */
    render() {
      const tableClassName = this.generateTableHeadingType(this.props.cardPage.get('cardGrouping')) as string;
      const colSpan =
        10 +
        (this.props.cardPage.get('cardGrouping') === Grouping.PRINTING ||
        this.props.cardPage.get('cardGrouping') === Grouping.NAME
          ? 1
          : 0);

      if (this.props.isInitializing) {
        const listItems = [];
        const dummyRowCount = 100;
        for (let i = 0; i < dummyRowCount; i++) {
          // Push the row
          listItems.push(
            <ListItem
              key={'dummy-collection-item-' + i}
              dummy
              dispatcher={this.props.dispatcher}
              index={i || 0}
              cardGroup={new MTGCardGroup()}
              selected={false}
              me={this.props.me}
              grouping={this.props.cardPage.get('cardGrouping')}
              final={i === dummyRowCount - 1}
            />,
          );
        }
        return (
          <div className="row">
            <div
              className={tableClassName}
              style={{
                gridColumnEnd: `span ${colSpan}`,
              }}
            >
              <div className={'table__header cell-border-left collection-cell-justification'}>Select</div>
              {this.props.cardPage.get('cardGrouping') !== Grouping.NONE ? (
                <div className={'table__header cell-border-left collection-cell-justification'}>Count</div>
              ) : null}
              <div className={'table__header cell-border-left collection-cell-justification'}>Card</div>
              <div className={'table__header cell-border-left collection-cell-justification'}>Mana Cost</div>
              <div className={'table__header cell-border-left collection-cell-justification'}>Type</div>
              <div className={'table__header cell-border-left collection-cell-justification'}>Subtype</div>
              <div className={'table__header cell-border-left collection-cell-justification'}>Foil</div>
              <div className={'table__header cell-border-left collection-cell-justification'}>Condition</div>
              <div className={'table__header cell-border-left collection-cell-justification'}>Language</div>
              <div className={'table__header cell-border-left collection-cell-justification'}>
                {'Price (' + this.props.me.get('preferences').get('localization').get('currency') + ')'}
              </div>
              <div className={'table__header cell-border-left collection-cell-justification'}>Time Added</div>
              {listItems}
            </div>
          </div>
        );
      }

      if (this.props.cardPage.get('cardGroups').size === 0) {
        return null;
      }

      const listItems: any[] = [];
      this.props.cardPage.get('cardGroups').forEach((cardGroup: MTGCardGroup, index: number) => {
        // Find any partially selected grid item
        const selected = cardGroup
          .get('cardInstances')
          .reduce((selected: boolean | 'multi' | undefined, cardInstance: CardInstance) => {
            switch (selected) {
              case undefined:
                return this.props.selections.get(cardInstance.get('id'));
              case true:
                return this.props.selections.get(cardInstance.get('id')) ? true : 'multi';
              case false:
                return this.props.selections.get(cardInstance.get('id')) ? 'multi' : false;
            }
            return 'multi';
          }, undefined);

        const final = this.props.cardPage.get('cardGroups').size - 1 === index;
        listItems.push(
          <ListItem
            key={'collection-item-' + index}
            dispatcher={this.props.dispatcher}
            index={index}
            cardGroup={cardGroup}
            selected={selected}
            me={this.props.me}
            grouping={this.props.cardPage.get('cardGrouping')}
            onClick={this.onClickListItem.bind(this, index, cardGroup)}
            final={final}
          />,
        );
        const cardPanelClass = classNames({
          'card-panel-container': this.props.cardPanel.get('y') === index && !final,
        });
        listItems.push(
          <div
            className={cardPanelClass}
            key={'list-row-card-panel-' + index}
            style={{
              gridColumnEnd: `span ${colSpan}`,
            }}
          >
            <CardPanel
              key={'card-panel-' + index}
              dispatcher={this.props.dispatcher}
              me={this.props.me}
              usernamePublic={this.props.usernamePublic}
              gridContainer={CSSGridContainer.COLLECTION}
              cardPage={this.props.cardPage}
              cardPanel={this.props.cardPanel}
              cardPanelRow={index}
              userTags={this.props.userTags}
              isGrid={false}
            />
          </div>,
        );
      });
      return (
        <div className="row">
          <div className={tableClassName}>
            <div className={'table__header cell-border-left collection-cell-justification'}>Select</div>
            {this.props.cardPage.get('cardGrouping') !== Grouping.NONE ? (
              <Table.SortingHeader
                title="Count"
                collectionCell={true}
                sorting={this.props.cardPage.get('cardSorting')}
                asc={Sorting.COUNT_ASC}
                desc={Sorting.COUNT_DESC}
                cellBorders={CellBorders.default(false)}
                onClick={(sorting) => {
                  this.onClickHeader.bind(this)(sorting);
                }}
              />
            ) : null}
            <Table.MultiSortingHeader
              title="Card"
              collectionCell={true}
              sorting={this.props.cardPage.get('cardSorting')}
              asc={[Sorting.NAME_ASC, Sorting.SET_NAME_ASC]}
              desc={[Sorting.NAME_DESC, Sorting.SET_NAME_DESC]}
              cellBorders={CellBorders.default(false)}
            />
            <Table.SortingHeader
              title="Mana Cost"
              collectionCell={true}
              sorting={this.props.cardPage.get('cardSorting')}
              asc={Sorting.MANA_VALUE_ASC}
              desc={Sorting.MANA_VALUE_DESC}
              cellBorders={CellBorders.default(false)}
              onClick={(sorting) => {
                this.onClickHeader.bind(this)(sorting);
              }}
            />
            <div className={'table__header cell-border-left collection-cell-justification'}>Type</div>
            <div className={'table__header cell-border-left collection-cell-justification'}>Subtype</div>
            <div className={'table__header cell-border-left collection-cell-justification'}>Foil</div>
            <div className={'table__header cell-border-left collection-cell-justification'}>Condition</div>
            <div className={'table__header cell-border-left collection-cell-justification'}>Language</div>
            <Table.SortingHeader
              title={'Price (' + this.props.me.get('preferences').get('localization').get('currency') + ')'}
              collectionCell={true}
              sorting={this.props.cardPage.get('cardSorting')}
              asc={Sorting.PRICE_DESC}
              desc={Sorting.PRICE_ASC}
              cellBorders={CellBorders.default(false)}
              onClick={(sorting) => {
                this.onClickHeader.bind(this)(sorting);
              }}
            />
            <Table.SortingHeader
              title={'Time Added'}
              collectionCell={true}
              sorting={this.props.cardPage.get('cardSorting')}
              asc={Sorting.DATE_ADDED_ASC}
              desc={Sorting.DATE_ADDED_DESC}
              cellBorders={CellBorders.rightCell(false)}
              onClick={(sorting) => {
                this.onClickHeader.bind(this)(sorting);
              }}
            />
            {listItems}
          </div>
        </div>
      );
    }

    private onClickListItem(y: number, cardGroup: MTGCardGroup, evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();
      const panelY = this.props.cardPanel.get('y');
      if (y === panelY) {
        CardPanelActions.closeCardPanel(this.props.dispatcher);
      } else {
        CardPanelActions.openCardPanel(
          this.props.username,
          0,
          y,
          Game.MTG,
          cardGroup,
          this.props.usernamePublic,
          this.props.dispatcher,
        );
      }
    }

    private onClickHeader(sortingCode: string) {
      CardActions.load(
        this.props.cardPage.merge({
          cardSorting: sortingCode as Sorting,
          filterOptions: this.props.cardPage.get('filterOptions').setPage(1),
        }),
        this.props.dispatcher,
      );
    }

    private generateTableHeadingType(grouping: Grouping): string {
      return classNames({
        'collection__table--with-count': grouping !== Grouping.NONE,
        collection__table: grouping === Grouping.NONE,
      });
    }
  },
);
