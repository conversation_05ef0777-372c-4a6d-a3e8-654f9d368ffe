import * as React from 'react';
import * as CardActions from '../../actions/CardActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import { MTGFilter } from '../../models/filters/mtg/MTGFilters';
import { Grouping } from '../../models/Grouping';
import { MTGCardPage } from '../../models/mtg/MTGCardPage';
import { SortingGroupingHelper } from '../../models/sorting/Helper';
import { Sorting } from '../../models/sorting/Sorting';
import { AdvancedSearchInput } from '../components/AdvancedSearchInput';
import { GroupingSelector } from '../components/selectors/GroupingSelector';
import { SortingSelector } from '../components/selectors/SortingSelector';

interface IProps {
  readonly defaultSorting: Sorting;
  readonly cardPage: MTGCardPage;
  readonly rawQuery?: string;
  readonly filterOptions: MTGFilter;
}

interface IState {
  grouping: Grouping;
  sorting: Sorting;
}

export class CollectionSearch extends React.Component<HasDispatcher.IProps & IProps, IState> {
  /**
   * @override
   */
  constructor(props: HasDispatcher.IProps & IProps) {
    super(props);
    this.state = {
      grouping: props.cardPage.get('cardGrouping'),
      sorting: props.cardPage.get('cardSorting'),
    };
  }

  /**
   * @override
   */
  public render() {
    return (
      <div className="row">
        <div className="col-xs-12 col-sm-5">
          <AdvancedSearchInput
            dispatcher={this.props.dispatcher}
            cardPage={this.props.cardPage}
            rawQuery={this.props.rawQuery}
            filterOptions={this.props.filterOptions}
          />
        </div>
        <div className="col-xs-6 col-sm-4">
          <SortingSelector
            sorting={this.state.sorting}
            grouping={this.state.grouping}
            onChange={this.onChangeSorting.bind(this)}
          />
        </div>
        <div className="col-xs-6 col-sm-3">
          <GroupingSelector grouping={this.state.grouping} onChange={this.onChangeGrouping.bind(this)} />
        </div>
      </div>
    );
  }

  private onChangeSorting(sorting: Sorting) {
    this.setState({
      sorting: sorting,
    });

    CardActions.load(
      this.props.cardPage.merge({
        cardSorting: sorting,
        filterOptions: this.props.cardPage.get('filterOptions').setPage(1),
      }),
      this.props.dispatcher,
    );
  }

  private onChangeGrouping(grouping: Grouping) {
    // If the current sorting is no longer valid, attempt to default to preferences or use date newest
    const sorting = this.props.cardPage.get('cardSorting');
    const defaultSorting = SortingGroupingHelper.validCombo(grouping, this.props.defaultSorting)
      ? this.props.defaultSorting
      : Sorting.DATE_ADDED_DESC;
    const newSorting = SortingGroupingHelper.validCombo(grouping, sorting) ? sorting : defaultSorting;

    this.setState({
      grouping: grouping,
      sorting: sorting,
    });

    CardActions.load(
      this.props.cardPage.merge({
        cardSorting: newSorting,
        cardGrouping: grouping,
        filterOptions: this.props.cardPage.get('filterOptions').setPage(1),
      }),
      this.props.dispatcher,
    );
  }
}
