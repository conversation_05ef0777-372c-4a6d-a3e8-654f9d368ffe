import clear from '@iconify/icons-ic/clear';
import * as React from 'react';
import * as FilterActions from '../../actions/FilterActions';
import Dispatcher from '../../dispatcher/Dispatcher';
import { MTGFilter } from '../../models/filters/mtg/MTGFilters';
import { MTGCardPage } from '../../models/mtg/MTGCardPage';
import { AllFilters } from '../components/filters/AllFilters';
import { Button, ButtonClass, UpdateFiltersButton } from '../components/filters/UpdateFiltersButton';

interface IProps {
  dispatcher: Dispatcher;
  cardPage: MTGCardPage;
  filterOptions: MTGFilter;
  advancedFilterOptions: MTGFilter;
  username: string;
}

export const CollectionFilterOptionsContainer = (props: IProps) => {
  const mergedFilters = props.filterOptions.mergeFilter(props.advancedFilterOptions);

  // Always use the MTGCardPage filter to display the current filter state. It
  // may differ from the UI filter state, because of other filters with a
  // higher priority.
  return (
    <>
      <UpdateFiltersButton
        activeFiltersCount={mergedFilters.countActiveFilters()}
        onClick={() => onClickUpdateFilters()}
      />
      <AllFilters
        dispatcher={props.dispatcher}
        mergedFilters={mergedFilters}
        filterOptions={props.filterOptions}
        advancedFilterOptions={props.advancedFilterOptions}
      />
      <Button class={ButtonClass.ALERT} icon={clear} title="Clear" onClick={onClickClear} />
    </>
  );

  function onClickClear() {
    FilterActions.clearFilters(props.cardPage, props.dispatcher);
  }

  async function onClickUpdateFilters() {
    await FilterActions.activateFilter(
      props.cardPage,
      props.filterOptions,
      props.advancedFilterOptions,
      props.dispatcher,
    );
  }
};
