// node modules
import * as React from 'react';
import { Helmet } from 'react-helmet';
import { match } from 'react-router-dom';
// Actions
import * as CardActions from '../../actions/CardActions';
import * as SubscriptionActions from '../../actions/SubscriptionActions';
import * as UserActions from '../../actions/UserActions';
import * as HasCardPanel from '../../containers/HasCardPanel';
import * as HasCards from '../../containers/HasCards';
// Hases
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasFilters from '../../containers/HasFilters';
import * as HasMe from '../../containers/HasMe';
import * as HasTags from '../../containers/HasTags';
import * as HasUsers from '../../containers/HasUsers';
import { CoreAssets } from '../../helpers/core_assets';
// Helpers
import isServerside from '../../helpers/serverside';
import { MTGFilter } from '../../models/filters/mtg/MTGFilters';
import { FreeLimits } from '../../models/Subscriptions';
import { Viewing } from '../../models/Viewing';
import { Footer } from '../components/Footer';
// Components
import { Navbar } from '../components/Navbar';
import { Paginator } from '../components/Paginator';
import { Placeholder } from '../components/Placeholder';
import { CollectionFilterOptionsContainer } from './CollectionFilterOptionsContainer';
import { CollectionGrid } from './CollectionGrid';
import { CollectionList } from './CollectionList';
import { CollectionSearch } from './CollectionSearch';
import { CollectionSelects } from './CollectionSelects';

interface IProps {
  match?: match<CollectionParams>;
  location?: any;
}
interface CollectionParams {
  username: string;
}

interface IState {
  username: string;
  isPublic: boolean;
}

export const Collection = HasMe.Attach<IProps & HasDispatcher.IProps>(
  HasCards.Attach<IProps & HasDispatcher.IProps & HasMe.IProps>(
    HasUsers.Attach<IProps & HasDispatcher.IProps & HasMe.IProps & HasCards.IProps>(
      HasCardPanel.Attach<IProps & HasDispatcher.IProps & HasMe.IProps & HasCards.IProps & HasUsers.IProps>(
        HasTags.Attach<
          IProps & HasDispatcher.IProps & HasMe.IProps & HasCards.IProps & HasUsers.IProps & HasCardPanel.IProps
        >(
          HasFilters.Attach<
            IProps &
              HasDispatcher.IProps &
              HasMe.IProps &
              HasCards.IProps &
              HasUsers.IProps &
              HasCardPanel.IProps &
              HasTags.IProps
          >(
            class extends React.Component<
              IProps &
                HasDispatcher.IProps &
                HasMe.IProps &
                HasCards.IProps &
                HasUsers.IProps &
                HasCardPanel.IProps &
                HasTags.IProps &
                HasFilters.IProps,
              IState
            > {
              /**
               * @override
               */
              constructor(
                props: IProps &
                  HasDispatcher.IProps &
                  HasMe.IProps &
                  HasCards.IProps &
                  HasUsers.IProps &
                  HasCardPanel.IProps &
                  HasTags.IProps &
                  HasFilters.IProps,
              ) {
                super(props);
                const username = props.match && props.match.params.username;

                // Collection is public if any username is present (include the username of the collection owner)
                const isPublic = (username && username.length > 0) || false;

                this.state = {
                  // Always fallback to the current user's username
                  username: isPublic ? username || props.me.get('username') : props.me.get('username'),
                  isPublic: isPublic,
                };
              }

              /**
               * @override
               */
              async componentDidMount() {
                if (!isServerside() && !this.props.collectionLoaded) {
                  let cardPage = this.props.cardPage;
                  if (this.state.username.toLowerCase() !== this.props.cardPage.get('ownerUsername')) {
                    cardPage = CardActions.updatePage(
                      this.props.cardPage.merge({
                        ownerUsername: this.state.username.toLowerCase(),
                      }),
                      this.props.dispatcher,
                    );
                  }

                  CardActions.load(cardPage, this.props.dispatcher);

                  // FIXME: This will search the collection again - set the filters before the initial load
                  // const locationUUID = new URLSearchParams(this.props.location.search).get('locationUUID');
                  // if (locationUUID !== null && locationUUID !== undefined && cardPage.get('ownerUsername') !== '') {
                  //   FilterActions.overrideWithLocation(cardPage, locationUUID, this.props.dispatcher).catch(() => {
                  //     console.error(`Invalid location UUID - ${locationUUID}`);
                  //   });
                  // }

                  if (this.state.username === this.props.me.get('username')) {
                    SubscriptionActions.subscription(this.props.dispatcher);
                  }
                  UserActions.updateCollectionPublicity(this.state.isPublic, this.props.dispatcher);
                  CardActions.totals(this.props.dispatcher, this.state.username.toLowerCase());
                  CoreAssets.preload();
                }
              }

              /**
               * @override
               */
              render() {
                const topOffset = 84;
                return (
                  <div>
                    {this.renderMetaTags()}
                    <Navbar
                      dispatcher={this.props.dispatcher}
                      me={this.props.me}
                      isRibbon={!this.state.isPublic}
                      selection="collection"
                    />
                    <section className="section has-navbar has-footer">
                      <div className="container-fluid">
                        <div className="row">
                          {this.state.isPublic ? (
                            <div className="col-xs-12 center-xs collection-public-account-container">
                              <div
                                className="collection-account-profile-image"
                                style={{
                                  backgroundImage:
                                    'url(' +
                                    this.props.usersByUsername
                                      .get(this.state.username.toLowerCase())
                                      .get('avatar')
                                      .get('large') +
                                    ')',
                                }}
                              />
                              <div className="flex vertical justify-start">
                                <div className="collection-username">
                                  {this.props.usersByUsername.get(this.state.username.toLowerCase()).get('username') +
                                    "'s collection"}
                                </div>
                              </div>
                            </div>
                          ) : null}
                          <div className="col-xs-12 col-md-12 col-lg-8">
                            <CollectionSearch
                              dispatcher={this.props.dispatcher}
                              defaultSorting={this.props.me.get('preferences').get('collection').get('sorting')}
                              cardPage={this.props.cardPage}
                              rawQuery={this.props.rawQuery}
                              filterOptions={this.props.filterOptions}
                            />
                          </div>
                          <div
                            className="col-xs-12 col-md-12 col-lg-4 flex justify-end-xs"
                            style={{ marginTop: '2rem', gap: '1rem' }}
                          >
                            <CollectionFilterOptionsContainer
                              dispatcher={this.props.dispatcher}
                              cardPage={this.props.cardPage}
                              filterOptions={this.props.filterOptions}
                              username={this.state.username}
                              advancedFilterOptions={MTGFilter.fromString(this.props.rawQuery)}
                            />
                          </div>
                        </div>
                      </div>

                      <CollectionSelects
                        dispatcher={this.props.dispatcher}
                        isLoading={!this.props.collectionLoaded}
                        me={this.props.me}
                        username={this.state.username}
                        usernamePublic={this.state.isPublic}
                        cardPanel={this.props.cardPanel}
                        userTags={this.props.userTags}
                      />

                      {this.props.cardPage.get('cardViewing') === Viewing.GRID ? (
                        <CollectionGrid
                          dispatcher={this.props.dispatcher}
                          me={this.props.me}
                          username={this.state.username}
                          usernamePublic={this.state.isPublic}
                          cardPage={this.props.cardPage}
                          cardPanel={this.props.cardPanel}
                          userTags={this.props.userTags}
                          isInitializing={!this.props.collectionLoaded}
                        />
                      ) : null}
                      {this.props.cardPage.get('cardViewing') === Viewing.LIST ? (
                        <CollectionList
                          dispatcher={this.props.dispatcher}
                          me={this.props.me}
                          username={this.state.username}
                          usernamePublic={this.state.isPublic}
                          cardPage={this.props.cardPage}
                          cardPanel={this.props.cardPanel}
                          userTags={this.props.userTags}
                          isInitializing={!this.props.collectionLoaded}
                        />
                      ) : null}

                      {this.renderPlaceholder()}
                      {this.renderPages()}
                      <Footer />
                    </section>
                  </div>
                );
              }

              private renderMetaTags() {
                if (this.state.isPublic) {
                  return (
                    <Helmet>
                      <title>{this.state.username + "'s Collection"}</title>
                      <meta
                        name="description"
                        content={
                          'Browse ' +
                          this.state.username +
                          "'s collection on the CardCastle web platform. Public collections make trading, borrowing and lending cards easier."
                        }
                      />
                    </Helmet>
                  );
                } else {
                  return (
                    <Helmet>
                      <title>CardCastle: Collection</title>
                    </Helmet>
                  );
                }
              }

              private renderPlaceholder() {
                if (!this.props.collectionLoaded || !this.state.username || !this.state.username.length) {
                  return null;
                }

                const isCollectionEmpty = this.props.collectionTotals.get('count') === 0;
                const isPageEmpty = this.props.cardPage.get('pageCount') === 0;
                const isPageTotalEmpty = this.props.cardPage.get('totalCount') === 0;
                const isSubscribed = this.props.usersByUsername
                  .get(this.state.username.toLowerCase())
                  .get('subscribed');
                const isMyCollection = this.state.username === this.props.me.get('username');
                const isFiltered = this.props.cardPage.get('totalCount') < this.props.collectionTotals.get('count');
                const isLimited = !isSubscribed && this.props.collectionTotals.get('count') >= FreeLimits.CARDS;

                if (isCollectionEmpty) {
                  if (isMyCollection) {
                    return (
                      <Placeholder
                        message={"Looks like your collection is empty, let's add some cards."}
                        button={'Add Cards'}
                        href="/builder/staging"
                      />
                    );
                  }
                  return (
                    <Placeholder
                      message={`Looks like ${this.state.username}'s collection is empty.`}
                      button={null}
                      href="/"
                    />
                  );
                }

                if (isPageEmpty) {
                  if ((this.props.cardPage.get('filterOptions').getPage() || 1) === 1) {
                    if (isSubscribed) {
                      if (isFiltered) {
                        return this.renderNoResults(isMyCollection);
                      }
                    } else {
                      return this.renderNoResultsUnsubscribed(isMyCollection);
                    }
                  }
                  return (
                    <Placeholder
                      message={"End of results. Maybe it's time to add more cards?"}
                      button="Add Cards"
                      href="/builder/staging"
                    />
                  );
                }

                if (isSubscribed) {
                  if (isPageTotalEmpty) {
                    if (isFiltered) {
                      return this.renderNoResults(isMyCollection);
                    }
                    throw new Error('error: arrived at inconsistent state');
                  }
                  return null;
                }

                if (isPageTotalEmpty) {
                  throw new Error('error: arrived at inconsistent state');
                }

                if (isFiltered) {
                  if (isMyCollection) {
                    return (
                      <Placeholder
                        message={`Some results may be hidden. As a Squire you are limited to viewing your first ${FreeLimits.CARDS} cards. Become a Knight today.`}
                        button="Upgrade"
                        href="/settings/subscription"
                      />
                    );
                  }
                  return (
                    <Placeholder
                      message={`Some results may be hidden. ${this.state.username} is limited to ${FreeLimits.CARDS} cards.`}
                      button={null}
                      href="/"
                    />
                  );
                }

                if (isLimited) {
                  if (isMyCollection) {
                    return (
                      <Placeholder
                        message={`Congratulations, you've scanned in ${FreeLimits.CARDS} cards! For unlimited cards and more exciting features, become a Knight today.`}
                        button={'Upgrade'}
                        href={'/settings/subscription'}
                      />
                    );
                  } else {
                    return (
                      <Placeholder
                        message={`${this.state.username} is limited to ${FreeLimits.CARDS} cards as they are yet to upgrade to Knight.`}
                        button={null}
                        href={'/'}
                      />
                    );
                  }
                }

                return null;
              }

              private renderNoResults(isOwner: boolean) {
                let message = 'No cards match your filters.';
                let button = null;
                let href = '/collection';
                if (isOwner) {
                  message = `${message} Maybe it's time to add more cards?`;
                  button = 'Add Cards';
                  href = '/builder/staging';
                }
                return <Placeholder message={message} button={button} href={href} />;
              }

              private renderNoResultsUnsubscribed(isOwner: boolean) {
                let message = 'No cards match your filters. This might be because';
                let button = null;
                let href = '/';

                if (isOwner) {
                  message = `${message} you are limited to searching your first ${FreeLimits.CARDS} cards. Become a Knight today.`;
                  button = 'Upgrade';
                  href = '/settings/subscription';
                } else {
                  message = `${message} ${this.state.username} is limited to ${FreeLimits.CARDS} cards.`;
                }

                return <Placeholder message={message} button={button} href={href} />;
              }

              private renderPages() {
                // this is to protect the server against accessing bad state. this happens because
                // the params are not available in the same way on the server, and so the username
                // cannot be calculated properly. this can be fixed by using ReactRouter on the server.
                if (!this.state.username || !this.state.username.length) {
                  return null;
                }

                if (!this.props.collectionLoaded) {
                  return null;
                } else {
                  const page = this.props.cardPage.get('filterOptions').getPage() || 1;
                  const totalGroups = this.props.cardPage.get('totalGroupCount');
                  return (
                    <Paginator page={page} totalItems={totalGroups} onClickPage={(page) => this.onClickPage(page)} />
                  );
                }
              }

              private onClickPage(page: number) {
                CardActions.load(
                  this.props.cardPage.set('filterOptions', this.props.cardPage.get('filterOptions').setPage(page)),
                  this.props.dispatcher,
                );
                window.scrollTo(0, 0);
              }
            },
          ),
        ),
      ),
    ),
  ),
);
