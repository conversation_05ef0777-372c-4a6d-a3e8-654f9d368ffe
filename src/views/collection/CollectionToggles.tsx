import * as React from 'react';
import * as CardActions from '../../actions/CardActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import { MTGCardPage } from '../../models/mtg/MTGCardPage';
import { Viewing } from '../../models/Viewing';

interface IProps {
  readonly cardPage: MTGCardPage;
}

interface IState {}

export class CollectionToggles extends React.Component<HasDispatcher.IProps & IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: HasDispatcher.IProps & IProps) {
    super(props);
  }

  /**
   * @override
   */
  render() {
    return (
      <div className="flex horizontal justify-start-xs justify-end-sm">
        <div className="collection-toggle-container" onClick={this.onClickToggle.bind(this)}>
          <div
            className={
              'collection-toggle' + (this.props.cardPage.get('cardViewing') === Viewing.GRID ? ' selected' : '')
            }
          >
            <img
              src={
                this.props.cardPage.get('cardViewing') === Viewing.GRID
                  ? '/images/grid-icon-alt.png'
                  : '/images/grid-icon.png'
              }
            />
          </div>
          <div
            className={
              'collection-toggle' + (this.props.cardPage.get('cardViewing') === Viewing.LIST ? ' selected' : '')
            }
          >
            <img
              src={
                this.props.cardPage.get('cardViewing') === Viewing.LIST
                  ? '/images/list-icon-alt.png'
                  : '/images/list-icon.png'
              }
            />
          </div>
        </div>
      </div>
    );
  }

  private onClickToggle(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    CardActions.switchView(this.props.cardPage, this.props.dispatcher);
  }
}
