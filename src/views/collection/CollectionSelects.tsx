import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardPanelActions from '../../actions/CardPanelActions';
import * as SelectActions from '../../actions/SelectActions';
import * as CardsAPI from '../../api/Cards';
import * as HasCards from '../../containers/HasCards';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import * as HasSelections from '../../containers/HasSelections';
import { CardPanel as CardPanelModel } from '../../models/CardPanels';
import { Game } from '../../models/Game';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { Tag } from '../../models/Tags';
import { User } from '../../models/Users';
import { CardPanel } from '../components/cardpanel/CardPanel';
import { CollectionToggles } from './CollectionToggles';

interface IProps {
  me: User;
  username: string;
  usernamePublic: boolean;
  cardPanel: CardPanelModel;
  userTags: Immutable.OrderedSet<Tag>;
  isLoading: boolean;
}

interface IState {}

export const CollectionSelects = HasMe.Attach<IProps & HasDispatcher.IProps>(
  HasCards.Attach<IProps & HasDispatcher.IProps & HasMe.IProps>(
    HasSelections.Attach<IProps & HasDispatcher.IProps & HasMe.IProps & HasCards.IProps>(
      class extends React.Component<
        IProps & HasDispatcher.IProps & HasMe.IProps & HasCards.IProps & HasSelections.IProps,
        IState
      > {
        /**
         * @override
         */
        constructor(props: IProps & HasDispatcher.IProps & HasMe.IProps & HasCards.IProps & HasSelections.IProps) {
          super(props);
          this.state = {};
        }

        /**
         * @override
         */
        render() {
          // HACK: Check if CardPanel is open - should be a boolean prop
          // If scroll is enabled when the CardPanel is closed, the select bar will be scrollable
          let className = 'collection-select-container';

          const cardPanelOpen = this.props.cardPanel.get('y') == -1;
          if (cardPanelOpen) {
            className += '--open';
          }

          return (
            <div className={className}>
              <div className="container-fluid" style={{ marginBottom: '1rem' }}>
                <div className="row">
                  <div
                    className="col-md-12 col-lg-6 flex"
                    // Having bottom/top margins on individual elements allows for better bootstrap usage
                    style={{ marginBottom: '1rem', gap: '1rem' }}
                  >
                    <button className="button-primary" onClick={this.onClickSelectAll.bind(this)}>
                      Select All
                    </button>
                    <button className="button-primary" onClick={this.onClickDeselectAll.bind(this)}>
                      Deselect All
                    </button>
                    <button className="button-primary" onClick={this.onClickView.bind(this)}>
                      View Selection
                    </button>
                  </div>
                  <div
                    className="col-xs-12 col-sm-10 col-md-10 col-lg-5 justify-start-xs justify-end-sm flex"
                    // Having bottom/top margins on individual elements allows for better bootstrap usage
                    style={{ marginBottom: '1rem' }}
                  >
                    <div>
                      {this.props.isLoading ? (
                        <div className="collection-selects-heading" style={{ marginTop: 0 }}>
                          Loading...
                        </div>
                      ) : (
                        <div className="collection-selects-heading" style={{ marginTop: 0 }}>
                          Displaying{' '}
                          {this.props.cardPage
                            .get('pageCount')
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}{' '}
                          of{' '}
                          {(this.props.cardPage.get('totalCount') || 0)
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        </div>
                      )}
                      <div className="collection-selects-subheading">
                        {this.props.selections.size
                          ? this.props.selections.size.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
                            ' cards selected'
                          : 'No cards selected'}
                      </div>
                    </div>
                  </div>
                  <div
                    className="col-xs-12 col-sm-2 col-md-2 col-lg-1 justify-start-xs justify-end-sm flex"
                    // Having bottom/top margins on individual elements allows for better bootstrap usage
                    style={{ marginBottom: '1rem' }}
                  >
                    <div style={{ width: 'min-content' }}>
                      <CollectionToggles dispatcher={this.props.dispatcher} cardPage={this.props.cardPage} />
                    </div>
                  </div>
                </div>
              </div>
              <div className="list-panel-container max-width">
                <CardPanel
                  dispatcher={this.props.dispatcher}
                  me={this.props.me}
                  usernamePublic={this.props.usernamePublic}
                  cardPage={this.props.cardPage}
                  cardPanel={this.props.cardPanel}
                  cardPanelRow={-1}
                  userTags={this.props.userTags}
                  isGrid={false}
                  isMultiple={true}
                />
              </div>
            </div>
          );
        }

        private onClickSelectAll(evt: React.SyntheticEvent<HTMLElement>) {
          evt.preventDefault();
          this.props.cardPage.get('cardGroups').forEach((cardGroup: MTGCardGroup) => {
            SelectActions.selectGroup(cardGroup, this.props.dispatcher);
          });
          evt.currentTarget.blur();
        }

        private onClickDeselectAll(evt: React.SyntheticEvent<HTMLElement>) {
          evt.preventDefault();
          SelectActions.deselectAll(this.props.dispatcher);
          evt.currentTarget.blur();
        }

        private async onClickView(evt: React.SyntheticEvent<HTMLElement>) {
          evt.preventDefault();
          evt.currentTarget.blur();
          if (this.props.cardPanel.get('y') === -1) {
            CardPanelActions.closeCardPanel(this.props.dispatcher);
          } else {
            const cardInstancesList = this.props.selections.keySeq().toList();
            if (cardInstancesList.size) {
              const cardInstances = await CardsAPI.lookup(this.props.username, cardInstancesList);
              CardPanelActions.openCardPanel(
                this.props.username,
                0,
                -1,
                Game.MTG,
                new MTGCardGroup({ cardInstances: cardInstances }),
                this.props.usernamePublic,
                this.props.dispatcher,
              );
            } else {
              CardPanelActions.openCardPanel(
                this.props.username,
                0,
                -1,
                Game.MTG,
                new MTGCardGroup(),
                this.props.usernamePublic,
                this.props.dispatcher,
              );
            }
          }
        }
      },
    ),
  ),
);
