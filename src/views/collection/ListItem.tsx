import * as React from 'react';
import * as SelectActions from '../../actions/SelectActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import { CoreAssets } from '../../helpers/core_assets';
import displayCurrency from '../../helpers/currency_helper';
import { DateFormat } from '../../helpers/fmt';
import { CardInstance, ResolveGroupValue, ResolvePrice } from '../../models/CardInstances';
import { CellBorders } from '../../models/CellBorder';
import { Foil } from '../../models/Foil';
import { Grouping } from '../../models/Grouping';
import { Language, LanguageName } from '../../models/Language';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { User } from '../../models/Users';
import * as Table from '../components/Table';

interface IProps {
  me: User;
  cardGroup: MTGCardGroup;
  selected: boolean | 'multi';
  grouping: Grouping;
  index: number;
  dummy?: boolean;
  onClick?: (evt: React.SyntheticEvent<HTMLElement>) => void;
  final: boolean;
}

interface IState {}

export class ListItem extends React.Component<HasDispatcher.IProps & IProps, IState> {
  /**
   * @override
   * @constructor
   */
  constructor(props: HasDispatcher.IProps & IProps) {
    super(props);
    this.state = {};
  }

  /**
   * @override
   */
  render() {
    const dark = this.props.index % 2 === 0 ? true : false;
    if (this.props.dummy) {
      return (
        <>
          <Table.CheckBoxCell
            collectionCell={true}
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            checked={this.props.selected === true}
            multiple={this.props.selected === 'multi'}
            onChange={this.onClick.bind(this)}
            large={true}
          />
          {this.props.grouping !== Grouping.NONE ? (
            <Table.AmountCell
              collectionCell={true}
              dark={dark}
              cellBorders={CellBorders.default(this.props.final)}
              amount={0}
              large={true}
            />
          ) : null}
          <Table.TextCell
            collectionCell={true}
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            text={'-'}
            large={true}
          />
          <Table.TextCell
            collectionCell={true}
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            text={'-'}
            large={true}
          />
          <Table.TextCell
            collectionCell={true}
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            text={'-'}
            large={true}
          />
          <Table.TextCell
            collectionCell={true}
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            text={'-'}
            large={true}
          />
          <Table.TextCell
            collectionCell={true}
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            text={'-'}
            large={true}
          />
          <Table.TextCell
            collectionCell={true}
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            text={'-'}
            large={true}
          />
          <Table.TextCell
            collectionCell={true}
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            text={'-'}
            large={true}
          />
          <Table.TextCell
            collectionCell={true}
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            text={'-'}
            large={true}
          />
          <Table.TextCell
            collectionCell={true}
            dark={dark}
            cellBorders={CellBorders.rightCell(this.props.final)}
            text={'-'}
            large={true}
          />
        </>
      );
    }

    // Check that there are card instances to render
    if (!this.props.cardGroup.get('cardInstances') || !this.props.cardGroup.get('cardInstances').size) {
      return null;
    }
    const cardInstances = this.props.cardGroup.get('cardInstances');
    const resolvedLanguage = ResolveGroupValue(cardInstances, 'language');
    const resolvedCondition = ResolveGroupValue(cardInstances, 'condition');
    const foil = this.props.cardGroup
      .get('cardInstances')
      .reduce((foil: boolean, cardInstance: CardInstance) => foil || cardInstance.get('foil') === Foil.On, false);

    const cardInstance = this.props.cardGroup.get('cardInstances').get(0);

    const price = ResolvePrice(this.props.cardGroup.get('cardInstances'));
    let priceDisplay = '-';
    if (price) {
      priceDisplay = displayCurrency(price, true, this.props.me.get('preferences').get('localization').get('currency'));
    }

    const timezone = this.props.me.get('preferences').get('localization').get('timezone');
    return (
      <>
        <Table.CheckBoxCell
          dark={dark}
          cellBorders={CellBorders.default(this.props.final)}
          collectionCell={true}
          checked={this.props.selected === true}
          multiple={this.props.selected === 'multi'}
          onChange={this.onClick.bind(this)}
          large={true}
        />
        {this.props.grouping !== Grouping.NONE ? (
          <Table.AmountCell
            dark={dark}
            cellBorders={CellBorders.default(this.props.final)}
            collectionCell={true}
            amount={this.props.cardGroup.get('cardInstanceCount')}
            large={true}
          />
        ) : null}
        <Table.MTGCardInstanceInfoCell
          cellStyle={{
            dark: dark,
            collectionCell: true,
            cellBorders: CellBorders.default(this.props.final),
            large: true,
            onClick: this.onClickOpenPanel.bind(this),
          }}
          cardInstance={cardInstance}
          grouping={this.props.grouping}
        />
        <Table.ParentCell
          dark={dark}
          cellBorders={CellBorders.default(this.props.final)}
          collectionCell={true}
          onClick={this.onClickOpenPanel.bind(this)}
          large={true}
        >
          {ListItem.renderManaCost(cardInstance.get('cardManaCost'))}
        </Table.ParentCell>
        <Table.TextCell
          dark={dark}
          cellBorders={CellBorders.default(this.props.final)}
          collectionCell={true}
          onClick={this.onClickOpenPanel.bind(this)}
          large={true}
          text={
            cardInstance.get('cardTypes') && cardInstance.get('cardTypes').size > 0
              ? cardInstance.get('cardTypes').join(' ')
              : '-'
          }
        />
        <Table.TextCell
          dark={dark}
          cellBorders={CellBorders.default(this.props.final)}
          collectionCell={true}
          onClick={this.onClickOpenPanel.bind(this)}
          large={true}
          text={
            cardInstance.get('cardSubTypes') && cardInstance.get('cardSubTypes').size > 0
              ? cardInstance.get('cardSubTypes').join(' ')
              : '-'
          }
        />
        <Table.CheckCell
          dark={dark}
          cellBorders={CellBorders.default(this.props.final)}
          collectionCell={true}
          onClick={this.onClickOpenPanel.bind(this)}
          large={true}
          checked={foil}
        />
        <Table.TextCell
          dark={dark}
          cellBorders={CellBorders.default(this.props.final)}
          collectionCell={true}
          large={true}
          text={resolvedCondition}
        />
        <Table.TextCell
          dark={dark}
          cellBorders={CellBorders.default(this.props.final)}
          collectionCell={true}
          large={true}
          text={resolvedLanguage === 'Multiple' ? resolvedLanguage : LanguageName(resolvedLanguage as Language)}
        />
        <Table.TextCell
          dark={dark}
          cellBorders={CellBorders.default(this.props.final)}
          collectionCell={true}
          large={true}
          text={priceDisplay}
        />
        <Table.TextCell
          dark={dark}
          cellBorders={CellBorders.rightCell(this.props.final)}
          collectionCell={true}
          large={true}
          text={DateFormat.human(this.props.cardGroup.get('cardInstanceLatest'), timezone)}
        />
      </>
    );
  }

  private static renderManaCost(manaCost: string) {
    if (!manaCost) return '-';

    const symbols = manaCost
      .toLowerCase()
      // TODO: Fix this overcomplicated mess of a regex.
      .replace(/{([^}\/]+)\/?([^\/}]*)(?:\/([^}]*))?}/gi, '$1$2$3 ')
      .trim()
      .split(' ');
    const path = `${CoreAssets.iconHost()}/mana_symbols/`;

    const result = [];
    for (let sym = 0; sym < symbols.length; sym++)
      result.push(
        <img className="collection-color" key={'color-symbol-' + result.length} src={path + symbols[sym] + '.png'} />,
      );
    return result.length > 0 ? result : '-';
  }

  private renderPrice() {
    const price = ResolvePrice(this.props.cardGroup.get('cardInstances'));

    return (
      <td onClick={this.onClickOpenPanel.bind(this)}>
        {price
          ? displayCurrency(price, true, this.props.me.get('preferences').get('localization').get('currency'))
          : '-'}
      </td>
    );
  }

  private renderLatest(latest: Date) {
    const timezone = this.props.me.get('preferences').get('localization').get('timezone');
    return <td onClick={this.onClickOpenPanel.bind(this)}>{DateFormat.human(latest, timezone)}</td>;
  }

  private onClick() {
    if (this.props.selected) {
      SelectActions.deselectGroup(this.props.cardGroup, this.props.dispatcher);
    } else {
      SelectActions.selectGroup(this.props.cardGroup, this.props.dispatcher);
    }
  }

  private onClickOpenPanel(evt: React.SyntheticEvent<HTMLElement>) {
    this.props.onClick && this.props.onClick(evt);
  }
}
