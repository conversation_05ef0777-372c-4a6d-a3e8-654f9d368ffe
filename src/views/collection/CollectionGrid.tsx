import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardPanelActions from '../../actions/CardPanelActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasSelections from '../../containers/HasSelections';
import isServerside from '../../helpers/serverside';
import { CardInstance } from '../../models/CardInstances';
import { CardPanel as CardPanelModel } from '../../models/CardPanels';
import { Game } from '../../models/Game';
import { MTGCardGroup, MTGCardPage } from '../../models/mtg/MTGCardPage';
import { Tag } from '../../models/Tags';
import { User } from '../../models/Users';
import { CardPanel } from '../components/cardpanel/CardPanel';
import { GridItem } from './GridItem';

interface IProps {
  me: User;
  username: string;
  usernamePublic: boolean;
  cardPage: MTGCardPage;
  cardPanel: CardPanelModel;
  userTags: Immutable.OrderedSet<Tag>;
  isInitializing: boolean;
}

interface IState {}

export const CollectionGrid = HasSelections.Attach<IProps & HasDispatcher.IProps>(
  class extends React.Component<IProps & HasDispatcher.IProps & HasSelections.IProps, IState> {
    /**
     * @override
     */
    constructor(props: IProps & HasDispatcher.IProps & HasSelections.IProps) {
      super(props);
    }

    /**
     * @override
     */
    render() {
      if (this.props.isInitializing) {
        const gridItems = [];
        for (let i = 0; i < 100; i++) {
          gridItems.push(
            <div
              key={'dummy-collection-item-' + i}
              className={'col-xs-6 col-sm-4 col-md-3 col-xl-2' + (i % 5 === 0 ? ' offset-xl-1' : '')}
            >
              <GridItem
                dispatcher={this.props.dispatcher}
                grouping={this.props.cardPage.get('cardGrouping')}
                cardGroup={new MTGCardGroup()}
                selected={false}
                dummy={true}
              />
            </div>,
          );
        }
        return (
          <div className="container-fluid collection-grid-container">
            <div className="row">{gridItems}</div>
          </div>
        );
      }

      let cardPanelPositionMod = 1;
      if (this.props.dispatcher.WindowStore().isSmall()) {
        cardPanelPositionMod = 3;
      } else if (this.props.dispatcher.WindowStore().isMedium()) {
        cardPanelPositionMod = 4;
      } else if (this.props.dispatcher.WindowStore().isLarge()) {
        cardPanelPositionMod = 4;
      } else {
        cardPanelPositionMod = 5;
      }

      const gridItems: Array<JSX.Element> = [];
      this.props.cardPage.get('cardGroups').forEach((cardGroup: MTGCardGroup, key: number) => {
        const x = Math.floor(key % cardPanelPositionMod);
        const y = Math.floor(key / cardPanelPositionMod);

        // Find any partially selected grid item
        const selected = cardGroup
          .get('cardInstances')
          .reduce((selected: boolean | 'multi' | undefined, cardInstance: CardInstance) => {
            switch (selected) {
              case undefined:
                return this.props.selections.get(cardInstance.get('id'));
              case true:
                return this.props.selections.get(cardInstance.get('id')) ? true : 'multi';
              case false:
                return this.props.selections.get(cardInstance.get('id')) ? 'multi' : false;
            }
            return 'multi';
          }, undefined);

        // Render the grid item
        gridItems.push(
          <div
            key={'collection-item-' + key}
            className={'col-xs-6 col-sm-4 col-md-3 col-xl-2' + (key % 5 === 0 ? ' offset-xl-1' : '')}
            onClick={this.onClickGridItem.bind(this, x, y, cardGroup)}
          >
            <GridItem
              dispatcher={this.props.dispatcher}
              grouping={this.props.cardPage.get('cardGrouping')}
              cardGroup={cardGroup}
              selected={selected}
            />
          </div>,
        );

        // Render the collection panel
        if (
          !isServerside() &&
          ((this.props.dispatcher.WindowStore().isExtraSmall() && key % 2 === 1) ||
            (this.props.dispatcher.WindowStore().isSmall() && key % 3 === 2) ||
            (this.props.dispatcher.WindowStore().isMedium() && key % 4 === 3) ||
            (this.props.dispatcher.WindowStore().isLarge() && key % 4 === 3) ||
            (this.props.dispatcher.WindowStore().isExtraLarge() && key % 5 === 4) ||
            key === this.props.cardPage.get('cardGroups').size - 1)
        ) {
          gridItems.push(
            <div key={'card-panel-container-' + key} className="col-xs-12 col-xl-10 offset-xl-1">
              <CardPanel
                key={'card-panel-' + key}
                dispatcher={this.props.dispatcher}
                me={this.props.me}
                usernamePublic={this.props.usernamePublic}
                cardPage={this.props.cardPage}
                cardPanel={this.props.cardPanel}
                cardPanelRow={y}
                userTags={this.props.userTags}
                isGrid={true}
              />
            </div>,
          );
        }
      });

      return (
        <div className="container-fluid collection-grid-container">
          <div className="row">{gridItems}</div>
        </div>
      );
    }

    private onClickGridItem(x: number, y: number, cardGroup: MTGCardGroup, evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();
      if (x === this.props.cardPanel.get('x') && y === this.props.cardPanel.get('y')) {
        CardPanelActions.closeCardPanel(this.props.dispatcher);
      } else {
        const offset = $('#panel-' + y).offset();
        const top = offset ? offset.top : 0;
        if (
          this.props.cardPanel.get('x') !== null &&
          this.props.cardPanel.get('y') !== null &&
          this.props.cardPanel.get('x')! < y &&
          this.props.cardPanel.get('y')! > 0
        ) {
          $('body').animate({ scrollTop: top - 770 }, 450, 'easeOutExpo'); // WARNING: the 770 offset is hardcoded from playing around with different offsets
        } else {
          $('body').animate({ scrollTop: top - 300 }, 450, 'easeOutExpo'); // WARNING: the 300 offset is hardcoded from playing around with different offsets
        }
        CardPanelActions.openCardPanel(
          this.props.username,
          x,
          y,
          Game.MTG,
          cardGroup,
          this.props.usernamePublic,
          this.props.dispatcher,
        );
      }
    }
  },
);
