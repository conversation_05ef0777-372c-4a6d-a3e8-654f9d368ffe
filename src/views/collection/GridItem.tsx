import * as React from 'react';
import * as SelectActions from '../../actions/SelectActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import { CoreAssets, ImageQuality } from '../../helpers/core_assets';
import { ResolveFoil, ResolveGroupValue } from '../../models/CardInstances';
import { Foil } from '../../models/Foil';
import { Game } from '../../models/Game';
import { Grouping } from '../../models/Grouping';
import { Language } from '../../models/Language';
import { MTGCardGroup } from '../../models/mtg/MTGCardPage';
import { CardImage } from '../components/CardImage';
import { Checkbox } from '../components/Checkbox';
import { SetSymbol } from '../components/SetSymbol';

interface IProps {
  cardGroup: MTGCardGroup;
  selected: boolean | 'multi';
  grouping: Grouping;
  dummy?: boolean;
}

interface IState {
  showRemoveDialog?: boolean;
}

export class GridItem extends React.Component<HasDispatcher.IProps & IProps, IState> {
  constructor(props: HasDispatcher.IProps & IProps) {
    super(props);
    this.state = {
      showRemoveDialog: false,
    };
  }

  public render() {
    if (this.props.dummy) {
      return (
        <div className="collection-grid-item-container">
          <div className="collection-grid-item-image">
            <CardImage
              src={CoreAssets.cardBack(Game.MTG)}
              alt={'MTG Card Back'}
              foil={Foil.Off}
              highlightOnHover={false}
              selected={false}
              quality={ImageQuality.LQ}
            />
          </div>

          <div className="collection-grid-item-name" style={{ width: '100%', height: '24.083px' }}>
            {/* force the height to match the non-dummy items */}
            <div className="collection-grid-item-name-overflow">
              <div className="collection-grid-item-name-inner"> </div>
            </div>
          </div>
        </div>
      );
    }

    // Check that there are card instances to render
    const cardInstances = this.props.cardGroup.get('cardInstances');
    if (!cardInstances || !cardInstances.size) {
      return null;
    }
    const first = cardInstances.get(0);
    const language = ResolveGroupValue(cardInstances, 'language');
    const imagePath = (language == 'Multiple' ? Language.ENGLISH : language) + '/' + first.get('cardJsonID') + '.jpg';
    const imageUrl = `${CoreAssets.imageHost()}/card_images/` + imagePath;

    const foil = this.isFoil(this.props.cardGroup);

    return (
      <div className="collection-grid-item-container">
        <div className="collection-grid-item-image">
          <CardImage
            src={imageUrl}
            alt={first.get('cardName')}
            foil={foil}
            highlightOnHover={true}
            selected={this.props.selected}
            quality={ImageQuality.LQ}
          />

          <div
            className={'collection-grid-item-remove' + (this.props.selected ? '--selected' : '')}
            onClick={(evt) => {
              evt.preventDefault();
              evt.stopPropagation();
            }}
          >
            <Checkbox
              checked={this.props.selected === true}
              multiple={this.props.selected === 'multi'}
              onChange={this.onChangeSelect.bind(this)}
            />
          </div>
        </div>

        <div className="collection-grid-item-name">
          <div className="collection-grid-item-name-overflow">
            {this.props.grouping === Grouping.NONE ? (
              <div className="set-symbol-container" style={{ position: 'initial', marginRight: '0.5rem' }}>
                {/* position: "initial" is because other elements in this hierarchy fulfill the position requirements */}
                <SetSymbol
                  setName={first.get('cardSetName')}
                  setCode={first.get('cardSetCode')}
                  rarity={first.get('cardRarity')}
                  collectorNumber={first.get('cardCollectorNumber')}
                  hoverText={true}
                />
              </div>
            ) : (
              <span className="collection-grid-amount">{this.props.cardGroup.get('cardInstanceCount')}</span>
            )}
            <div className="collection-grid-item-name-inner">{first.get('cardName') ? first.get('cardName') : '-'}</div>
          </div>
        </div>
      </div>
    );
  }

  private onChangeSelect() {
    if (this.props.selected === true || this.props.selected === 'multi') {
      SelectActions.deselectGroup(this.props.cardGroup, this.props.dispatcher);
    } else {
      SelectActions.selectGroup(this.props.cardGroup, this.props.dispatcher);
    }
  }

  private isFoil = (cardGroup: MTGCardGroup): Foil => {
    if (cardGroup.get('cardInstances').size) {
      return ResolveFoil(cardGroup.get('cardInstances'));
    }
    return Foil.Off;
  };
}
