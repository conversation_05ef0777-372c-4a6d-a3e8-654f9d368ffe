<!DOCTYPE html>
<html>
  <head>
    <title><%- pageTitle %></title>

    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, initial-scale=1.0, user-scalable=yes" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-title" content="CardCastle" />
    <meta name="application-name" content="CardCastle" />
    <meta name="msapplication-TileColor" content="#603cba" />
    <meta name="msapplication-TileImage" content="//icons.cardcastle.co/favicons/mstile-144x144.png" />
    <meta name="theme-color" content="#333333" />
    <meta
      name="description"
      content="Thousands of cards in your Magic: The Gathering card collection? Find it hard to keep a track of which cards you own and where they are? CardCastle helps MTG players and local game stores catalogue and organise their collections online!"
    />
    <meta name="msapplication-config" content="//icons.cardcastle.co/favicons/browserconfig.xml?v=Om5rkAON5W" />
    <meta name="theme-color" content="#ffffff" />

    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="//icons.cardcastle.co/favicons/apple-touch-icon.png?v=Om5rkAON5W"
    />
    <link
      rel="icon"
      type="image/png"
      href="//icons.cardcastle.co/favicons/favicon-32x32.png?v=Om5rkAON5W"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="//icons.cardcastle.co/favicons/favicon-16x16.png?v=Om5rkAON5W"
      sizes="16x16"
    />
    <link rel="manifest" href="//icons.cardcastle.co/favicons/manifest.json?v=Om5rkAON5W" />
    <link rel="mask-icon" href="//icons.cardcastle.co/favicons/safari-pinned-tab.svg?v=Om5rkAON5W" color="#4a3587" />
    <link rel="shortcut icon" href="//icons.cardcastle.co/favicons/favicon.ico?v=Om5rkAON5W" />
    <link rel="stylesheet" href="<%= assetFingerprint('/css/main.css') %>" />

    <% if (environment().production && releaseStage().production) { %>
    <!-- Google Analytics -->
    <script>
      (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        (i[r] =
          i[r] ||
          function () {
            (i[r].q = i[r].q || []).push(arguments);
          }),
          (i[r].l = 1 * new Date());
        (a = s.createElement(o)), (m = s.getElementsByTagName(o)[0]);
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m);
      })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

      ga('create', '***********-1', 'auto');
      ga('send', 'pageview');
    </script>

    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-MGLSCR');
    </script>
    <!-- End Google Tag Manager -->
    <% } %>

    <!-- Bugsnag header script -->
    <% if (environment().production) { %> <% if (releaseStage().kraj) { %>
    <script
      src="//d2wy8f7a9ursnm.cloudfront.net/bugsnag-2.min.js"
      data-apikey="94125deeb8ca4873bc99daf6cf342fed"
      data-releasestage="staging"
      async
    ></script>
    <% } else { %>
    <script
      src="//d2wy8f7a9ursnm.cloudfront.net/bugsnag-2.min.js"
      data-apikey="94125deeb8ca4873bc99daf6cf342fed"
      data-releasestage="production"
      async
    ></script>
    <% } %> <% } %>

    <!-- Environment header script -->
    <script type="text/javascript">
      window.NODE_ENV = '<%= process.env.NODE_ENV %>';
      window.APP_HOST = '<%= process.env.APP_HOST %>';
      window.PORT = '<%= environment().development ? process.env.PORT : undefined %>';
      window.STRIPE_PUBLISHABLE_KEY = '<%= process.env.STRIPE_PUBLISHABLE_KEY %>';
      window.PAYPAL_CLIENT_ID = '<%= process.env.PAYPAL_CLIENT_ID %>';
      window.INTERCOM_APP_KEY = '<%= process.env.INTERCOM_APP_ID %>';
      window.BUGSNAG_API_KEY = '<%= process.env.BUGSNAG_API_KEY %>';
    </script>

    <!-- React header script -->
    <script type="text/javascript">
      <%- reactState %>
    </script>
  </head>
  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-MGLSCR"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <!-- Facebook -->
    <div id="fb-root"></div>
    <script>
      (function (d, s, id) {
        var js,
          fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s);
        js.id = id;
        js.src = '//connect.facebook.net/en_US/sdk.js#xfbml=1&version=v2.7';
        fjs.parentNode.insertBefore(js, fjs);
      })(document, 'script', 'facebook-jssdk');
    </script>

    <!-- Twitter -->
    <script>
      window.twttr = (function (d, s, id) {
        var js,
          fjs = d.getElementsByTagName(s)[0],
          t = window.twttr || {};
        if (d.getElementById(id)) return t;
        js = d.createElement(s);
        js.id = id;
        js.src = 'https://platform.twitter.com/widgets.js';
        fjs.parentNode.insertBefore(js, fjs);
        t._e = [];
        t.ready = function (f) {
          t._e.push(f);
        };
        return t;
      })(document, 'script', 'twitter-wjs');
    </script>

    <% if (environment().production && releaseStage().production) { %> <% include shared/facebook %> <% } %>

    <!-- React -->
    <div id="message-no-cookies" class="message-no-cookies"></div>
    <div id="react-root">
      <noscript>
        <div class="message-no-script">You have JavaScript disabled. Some features will not work.</div>
      </noscript>
      <div><%- reactRoot %></div>
    </div>

    <!-- Intercom -->
    <% if (environment().production) { %> <% if (releaseStage().kraj) { %>
    <script src="https://widget.intercom.io/widget/ss2ds9gl" type="text/javascript" defer></script>
    <% } else { %>
    <script src="https://widget.intercom.io/widget/yrxvvmxx" type="text/javascript" defer></script>
    <% } %> <% } %>

    <!-- Scripts -->
    <script type="text/javascript" src="https://checkout.stripe.com/checkout.js" defer></script>
    <script type="text/javascript" src="<%= assetFingerprint('/js/jquery-2.2.4.min.js') %>" defer></script>
    <script type="text/javascript" src="<%= assetFingerprint('/js/jquery-ui.min.js') %>" defer></script>
    <script type="text/javascript" src="<%= assetFingerprint('/js/nprogress.js') %>" defer></script>
    <script type="text/javascript" src="<%= assetFingerprint('/js/index.js') %>" defer></script>

    <!-- Check for disabled cookies -->
    <script type="text/javascript" defer>
      if (!navigator.cookieEnabled) {
        document.getElementById('message-no-cookies').innerHTML =
          'You have cookies disabled. You will not be remembered.';
        document.getElementById('message-no-cookies').style.padding = '2rem';
      }
    </script>
  </body>
</html>
