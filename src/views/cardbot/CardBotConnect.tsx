import * as React from 'react';
import Helmet from 'react-helmet';
import { Redirect } from 'react-router-dom';
import * as CardBotActions from '../../actions/CardBotActions';
import * as MessageActions from '../../actions/MessageActions';
import * as HasCardBot from '../../containers/HasCardBot';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import { ConnectionStatus } from '../../models/CardBot';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';

interface IProps {}

interface IState {
  deviceID: string;
  mfaToken: string;
}

export const CardBotConnect = HasMe.Attach<IProps & HasDispatcher.IProps>(
  HasCardBot.Attach<IProps & HasDispatcher.IProps & HasMe.IProps>(
    class extends React.Component<IProps & HasCardBot.IProps & HasDispatcher.IProps & HasMe.IProps, IState> {
      /**
       * @override
       */
      constructor(props: IProps & HasCardBot.IProps & HasDispatcher.IProps & HasMe.IProps) {
        super(props);
        this.state = {
          deviceID: this.props.cardBot.get('deviceID'),
          mfaToken: this.props.cardBot.get('mfaToken'),
        };
      }

      /**
       * @override
       */
      componentWillUnmount() {
        if (
          this.props.cardBot.get('connectionStatus') === ConnectionStatus.ERROR ||
          this.props.cardBot.get('connectionStatus') === ConnectionStatus.DISCONNECTED
        ) {
          CardBotActions.reset(this.props.dispatcher);
        }
      }

      render() {
        if (
          this.props.cardBot.get('connectionStatus') === ConnectionStatus.PAIRING ||
          this.props.cardBot.get('connectionStatus') === ConnectionStatus.PAIRED
        ) {
          if (this.state.mfaToken) {
            return <Redirect to={`cardbot-control/${this.state.deviceID}?mfa_token=${this.state.mfaToken}`} />;
          } else {
            return <Redirect to={`cardbot-control/${this.state.deviceID}`} />;
          }
        }
        return (
          <>
            <Helmet>
              <title>CardCastle: CardBot</title>
            </Helmet>
            {
              // TODO: Make a generic Message component that can be used in other places as opposed to just using it here.
              this.props.cardBot.get('error') !== '' &&
              this.props.cardBot.get('connectionStatus') === ConnectionStatus.ERROR ? (
                <div className="message" onClick={this.onClickError.bind(this)}>
                  <div className="message-content is-error" onClick={this.onClickError.bind(this)}>
                    <span className="message-text">
                      {'Incorrect Device ID - Please check your Device ID and try again.'}
                    </span>
                  </div>
                </div>
              ) : null
            }
            <Navbar dispatcher={this.props.dispatcher} me={this.props.me} selection="cardbot-control" isRibbon={true} />
            <section className="section has-navbar has-footer">
              <div className="container-fluid">
                <h1 className="page-heading">Connect to CardBot</h1>
                <div className="info-text paragraph-sm is-centered">
                  Enter the credentials for the CardBot you'd like to connect to. You can find these credentials and the
                  full sharelink on the CardBot's admin console.
                </div>
                <form className="auth-form offset-sm-2 col-sm-8" onSubmit={this.onClickPair.bind(this)}>
                  <div className="row">
                    <label className="label col-sm-3 col-md-2" htmlFor="device-id">
                      Device ID
                    </label>
                    <input
                      className="input col-sm-9 col-md-9"
                      ref="device-id"
                      id="device-id"
                      type="text"
                      onChange={this.onChangeDeviceID.bind(this)}
                      value={this.state.deviceID}
                      placeholder={'Device ID'}
                    />
                  </div>

                  <div className="row">
                    <label className="label col-sm-3 col-md-2" htmlFor="mfa-token">
                      MFA Token
                    </label>
                    <input
                      className="input col-sm-9 col-md-9"
                      ref="mfa-token"
                      id="mfa-token"
                      type="text"
                      onChange={this.onChangeMFAToken.bind(this)}
                      value={this.state.mfaToken}
                      placeholder={'MFA Token (Optional)'}
                    />
                  </div>

                  <div className="row">
                    <input
                      className="button-primary offset-xs-6 col-xs-6 offset-sm-9 col-sm-3 offset-md-9 col-md-2"
                      type="submit"
                      value="Pair"
                      onClick={this.onClickPair.bind(this)}
                    />
                  </div>
                </form>
              </div>
            </section>
            <Footer />
          </>
        );
      }

      private onChangeDeviceID(evt: any) {
        this.setState({
          deviceID: evt.currentTarget.value || '',
        });
      }

      private onChangeMFAToken(evt: any) {
        this.setState({
          mfaToken: evt.currentTarget.value || '',
        });
      }

      private onClickError() {
        CardBotActions.reset(this.props.dispatcher);
      }

      private async onClickPair(evt: React.SyntheticEvent<HTMLElement>) {
        evt.preventDefault();
        evt.stopPropagation();
        if (this.state.deviceID) {
          CardBotActions.setPairing(this.props.dispatcher);
        } else {
          MessageActions.error('Please enter a device ID', this.props.dispatcher);
        }
      }
    },
  ),
);
