import filter_alt from '@iconify/icons-ic/outline-filter-alt';
import * as Immutable from 'immutable';
import * as React from 'react';
import * as CardBotActions from '../../../actions/CardBotActions';
import * as DialogActions from '../../../actions/DialogActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { CardBotOptions, CreateSessions } from '../../../models/CardBot';
import { Condition } from '../../../models/Condition';
import { Game } from '../../../models/Game';
import { Language, type LanguageKey } from '../../../models/Language';
import { PokeFinish } from '../../../models/pokemon/PokeFinish';
import { DialogStatus } from '../../../stores/DialogStore';
import { Checkbox } from '../../components/Checkbox';
import { Button, ButtonClass } from '../../components/filters/UpdateFiltersButton';
import { FormLabel } from '../../components/FormLabel';
import { ConditionSelector } from '../../components/selectors/ConditionSelector';
import { CreateSessionsSelector } from '../../components/selectors/CreateSessionsSelector';
import { GameSelector } from '../../components/selectors/GameSelector';
import { LanguageSelector } from '../../components/selectors/LanguageSelector';
import { PokeFinishSelector } from '../../components/selectors/PokeFinishSelector';

interface IProps {
  readonly dispatcher: Dispatcher;
  readonly options: CardBotOptions;
  readonly disabled: boolean;
}

export const CardBotOptionsPanel = (props: IProps) => {
  const createSessions = props.options.get('createSessions');
  const foil = props.options.get('foil');
  const condition = props.options.get('condition');
  const language = props.options.get('language');
  const uploadImages = props.options.get('uploadImages');
  const languageOptions = Immutable.OrderedSet<string>(Object.keys(Language))
    .map((languageKey: string) => {
      return Language[languageKey as LanguageKey];
    })
    .toOrderedSet() as Immutable.OrderedSet<Language>;

  const pokeFinish = props.options.get('pokeFinish');

  const onChangeGame = (game: Game) => {
    CardBotActions.updateOptions(props.options.set('game', game), props.dispatcher);
  };

  const onChangeCreateSessions = (selection: CreateSessions) => {
    CardBotActions.updateOptions(props.options.set('createSessions', selection), props.dispatcher);
  };

  const onChangeUploadImages = () => {
    const uploadImages = !props.options.get('uploadImages');
    CardBotActions.updateOptions(props.options.set('uploadImages', uploadImages), props.dispatcher);
  };

  const onChangeFoil = () => {
    CardBotActions.updateOptions(props.options.set('foil', !props.options.get('foil')), props.dispatcher);
  };

  const onChangePokeFinish = (finish: PokeFinish) => {
    CardBotActions.updateOptions(props.options.set('pokeFinish', finish), props.dispatcher);
  };

  const onChangeCondition = (condition: Condition) => {
    CardBotActions.updateOptions(props.options.set('condition', condition), props.dispatcher);
  };

  const onChangeLanguage = (language: Language) => {
    CardBotActions.updateOptions(props.options.set('language', language), props.dispatcher);
  };

  const filterCount = props.options.get('scanRestrictions').countActiveFilters();
  let filterCountPill: JSX.Element | undefined = undefined;
  if (filterCount !== 0) {
    filterCountPill = (
      <div className="filter-amount-container">
        <span className="collection-grid-item-name-inner">{filterCount}</span>
      </div>
    );
  }

  return (
    <div className="row" style={{ margin: '1rem' }}>
      <div className="col-sm-12 col-md-12 col-lg-6" style={{ marginTop: '1rem', marginBottom: '1rem' }}>
        <div className="heading-divider-wrapper">
          <div className="heading-md">Scan Options</div>
        </div>
        <div className="flex" style={{ gap: '1rem', flexWrap: 'wrap' }}>
          <div className="grow-xs-1">
            <GameSelector
              game={props.options.get('game')}
              onChange={onChangeGame.bind(this)}
              disabled={props.disabled}
            />
          </div>
          <div className="grow-xs-1">
            <CreateSessionsSelector
              disabled={props.disabled}
              selection={createSessions}
              onChange={onChangeCreateSessions.bind(this)}
            />
          </div>
          {createSessions !== CreateSessions.NONE ? (
            <div className="grow-xs-1">
              <FormLabel
                heading="Upload Images"
                subheading="Upload and append scanned images to the cards in your collection"
                addMargin={false}
              />
              <Checkbox disabled={props.disabled} checked={uploadImages} onChange={onChangeUploadImages.bind(this)} />
            </div>
          ) : null}
          {props.options.get('game') === Game.MTG && (
            <div className="grow-xs-1">
              <FormLabel
                heading="Scan Restrictions"
                subheading="Assert the characteristics of the cards to be scanned"
              />
              <Button
                class={ButtonClass.PRIMARY}
                disabled={props.disabled}
                title={'Scan Restrictions'}
                icon={filter_alt}
                onClick={(evt) => {
                  evt.preventDefault();
                  DialogActions.open(DialogStatus.SCAN_RESTRICTIONS, props.dispatcher);
                }}
              >
                {filterCountPill}
              </Button>
            </div>
          )}
        </div>
      </div>
      {createSessions !== CreateSessions.NONE ? (
        <div className="col-sm-12 col-md-12 col-lg-6" style={{ marginTop: '1rem', marginBottom: '1rem' }}>
          <div className="heading-divider-wrapper">
            <div className="heading-md">Card Attributes</div>
          </div>
          <div className="flex" style={{ gap: '1rem', flexWrap: 'wrap' }}>
            <div className="grow-xs-1">
              <ConditionSelector
                disabled={props.disabled}
                condition={condition}
                onChange={onChangeCondition.bind(this)}
              />
            </div>
            {props.options.get('game') == Game.MTG && (
              <div className="grow-xs-1">
                <LanguageSelector
                  language={language}
                  onChange={onChangeLanguage.bind(this)}
                  options={languageOptions}
                  disabled={props.disabled}
                />
              </div>
            )}
            {props.options.get('game') == Game.MTG && (
              <div className="grow-xs-1">
                <FormLabel heading="Foil" subheading="Sets foil attribute on cards" addMargin={false} />
                <Checkbox disabled={props.disabled} checked={foil} onChange={onChangeFoil.bind(this)} />
              </div>
            )}
            {props.options.get('game') == Game.POKEMON && (
              <div className="grow-xs-1">
                <PokeFinishSelector
                  finish={pokeFinish}
                  onChange={onChangePokeFinish.bind(this)}
                  disabled={props.disabled}
                  dispatcher={props.dispatcher}
                />
              </div>
            )}
          </div>
        </div>
      ) : null}
    </div>
  );
};
