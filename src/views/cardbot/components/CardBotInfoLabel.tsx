import * as React from 'react';

interface ICardBotInfoLabel {
  heading: string;
  subheading: string;
  spinner?: boolean;
}

export const CardBotInfoLabel = (props: ICardBotInfoLabel) => {
  return (
    <>
      <div className="heading" style={{ fontFamily: 'lato-bold' }}>
        {props.heading}
      </div>
      {props.spinner ? (
        <div style={{ display: 'flex', fontFamily: 'lato-regular' }}>
          <div className="spinner-xs" style={{ marginRight: '0.5rem' }}></div> <div>{props.subheading}</div>
        </div>
      ) : (
        <div style={{ fontFamily: 'lato-regular' }}>{props.subheading}</div>
      )}
    </>
  );
};
