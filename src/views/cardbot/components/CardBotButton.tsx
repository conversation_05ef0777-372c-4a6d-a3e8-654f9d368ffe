import classNames from 'classnames';
import * as React from 'react';
import { TextFormat } from '../../../helpers/fmt';
import { CommandType } from '../../../models/CardBot';

interface IProps {
  commandType: CommandType;
  onClickCommand: (type: CommandType) => void;
  disabled: boolean;
}

export class CardBotButton extends React.Component<IProps> {
  constructor(props: IProps) {
    super(props);
  }

  public render() {
    const className = classNames({
      'button-alert':
        this.props.commandType === CommandType.STOP ||
        this.props.commandType === CommandType.OKAY ||
        this.props.commandType === CommandType.DISCONNECT,
      'button-primary':
        (this.props.commandType === CommandType.START && !this.props.disabled) ||
        this.props.commandType === CommandType.RESET,
      'button-grey': this.props.commandType === CommandType.START && this.props.disabled,
    });
    return (
      <input
        className={className}
        type="submit"
        value={TextFormat.capitalizeWord(this.props.commandType.toString().toLowerCase())}
        onClick={() => this.handleClick(this.props.commandType)}
      />
    );
  }

  private handleClick(commandType: CommandType) {
    if (!(commandType === CommandType.START && this.props.disabled)) {
      this.props.onClickCommand(commandType);
    }
  }
}
