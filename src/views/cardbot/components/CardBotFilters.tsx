import * as React from 'react';
import * as CardBotActions from '../../../actions/CardBotActions';
import Dispatcher from '../../../dispatcher/Dispatcher';
import { FilterComponentType } from '../../../models/FilterComponent';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { AnyFilter } from '../../../models/filters/Filters';
import { Game } from '../../../models/Game';
import { Subscription } from '../../../models/Subscriptions';
import { ComposableFilter } from '../../components/filters/ComposableFilter';

interface IProps {
  readonly dispatcher: Dispatcher;
  readonly game: Game;
  readonly subscription: Subscription;
  readonly filter: AnyFilter;
  readonly disabled: boolean;
}

export const CardBotFilters = (props: IProps) => {
  const updateFilter = (newFilter: AnyFilter, game: Game) => {
    CardBotActions.updateFilter(newFilter, game, props.dispatcher);
  };

  const generateComponents = () => {
    let availableFilters = props.filter.filterOrder();
    if (!props.subscription.isMerchant()) {
      availableFilters = availableFilters.remove(FilterComponentType.CARD_LIST);
    }

    return availableFilters
      .map((filterType: FilterComponentType) => (
        <ComposableFilter
          key={filterType}
          filter={props.filter}
          dispatcher={props.dispatcher}
          game={props.game}
          type={filterType}
          displayType={FilterDisplay.PERMANENT}
          onUpdate={(filter: AnyFilter, game: Game) => {
            if (!props.disabled) {
              updateFilter(filter, game);
            }
          }}
          disabled={props.disabled}
        />
      ))
      .toArray();
  };

  return (
    <div className="row" style={{ margin: '1rem' }}>
      <div className="col-xs-12">
        <div className="heading-divider-wrapper">
          <div className="heading-md">Filters</div>
        </div>
        <div className="col-xs-12" style={{ padding: 0, margin: 0 }}>
          <div className="row" style={{ gap: '1rem', margin: 0 }}>
            {generateComponents()}
          </div>
        </div>
      </div>
    </div>
  );
};
