import * as React from 'react';
import Select, { components, OptionsType } from 'react-select';
import * as CardListAPI from '../../../api/CardList';
import { CardList, DropdownState } from '../../../models/CardList';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { CardListOrderingCodes, CardListSorting, CardListSortingCodes } from '../../../models/sorting/CardListSorting';
import { CardListOption } from '../../components/CardListSuggestionItem';
import { ClearFilterButton } from '../../components/filters/ClearFilterButton';
import { FilterTitle } from '../../components/filters/FilterTitle';
import { SimpleNoOptionsMessage, styleOverride } from '../../shared/ReactSelectHelper';

interface IProps {
  cardList?: CardList;
  onSelectCardList: (cardList?: CardList) => void;
  disabled?: boolean;
  additionalOptions?: CardListOption[];
  onClear: () => void;
  displayType: FilterDisplay;
  menuPortal?: HTMLDivElement;
}

const SingleValueCard = (props: any) => {
  return (
    <components.SingleValue {...props}>
      <div className="text__suggestion">
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__value">{props.data.label}</div>
        </div>
      </div>
    </components.SingleValue>
  );
};

export const CardListDropdown = (props: IProps) => {
  const [state, setState] = React.useState<DropdownState>(new DropdownState());
  React.useEffect(() => {
    const load = async () => {
      setState(
        new DropdownState({
          loaded: true,
          cardLists: await CardListAPI.all(
            CardListSortingCodes[CardListSorting.NAME_ASC],
            CardListOrderingCodes[CardListSorting.NAME_ASC],
          ),
        }),
      );
    };
    load();
  }, []);

  function cardListOptions(): OptionsType<CardListOption> {
    const additionalOptions: CardListOption[] = props.additionalOptions ? props.additionalOptions : [];
    return additionalOptions.concat(
      state
        .get('cardLists')
        .valueSeq()
        .map((cardList: CardList) => {
          return new CardListOption(cardList);
        })
        .toArray(),
    );
  }

  function onChange(cardListOption: CardListOption) {
    // We have to check for null instead of undefined due to a quirk of react-select
    props.onSelectCardList(cardListOption === null ? undefined : CardList.fromCardListOption(cardListOption));
  }

  let placeholder = 'Select...';
  if (!state.get('loaded')) {
    placeholder = 'Loading...';
  }

  return (
    <>
      <FilterTitle name="Card List">
        <ClearFilterButton
          disabled={props.disabled}
          label={props.displayType === FilterDisplay.PERMANENT ? 'Clear' : 'Remove'}
          onClick={() => {
            props.onSelectCardList(undefined);
            if (props.onClear !== undefined) {
              props.onClear();
            }
          }}
        />
      </FilterTitle>
      <div style={{ cursor: 'text' }}>
        <Select
          components={{
            SingleValue: SingleValueCard,
            NoOptionsMessage: SimpleNoOptionsMessage,
          }}
          formatOptionLabel={CardListOption.formatOptionLabel}
          isDisabled={props.disabled === true || !state.get('loaded')}
          placeholder={placeholder}
          styles={styleOverride}
          className={'react-select-thin'}
          classNamePrefix="react-select-thin"
          options={cardListOptions()}
          handleInputChange={props.onSelectCardList}
          onChange={onChange}
          value={props.cardList && new CardListOption(props.cardList)}
          menuPortalTarget={props.menuPortal}
          defaultOptions
        />
      </div>
    </>
  );
};
