import * as React from 'react';
import Helmet from 'react-helmet';
import { match, Redirect } from 'react-router-dom';
// TODO: Remove these when we get to Node 16 and replace them with: https://nodejs.org/api/timers.html
import { clearIntervalAsync, setIntervalAsync, SetIntervalAsyncTimer } from 'set-interval-async/dynamic';
import * as CardBotActions from '../../actions/CardBotActions';
import * as DialogActions from '../../actions/DialogActions';
import * as MessageActions from '../../actions/MessageActions';
import * as SubscriptionActions from '../../actions/SubscriptionActions';
import * as HasCardBot from '../../containers/HasCardBot';
import * as HasCardLists from '../../containers/HasCardLists';
import * as HasDialog from '../../containers/HasDialog';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import * as HasSubscription from '../../containers/HasSubscription';
import Dispatcher from '../../dispatcher/Dispatcher';
import { TextFormat } from '../../helpers/fmt';
import history from '../../helpers/history';
import {
  CardBotCommand,
  CardBotOptions,
  CommandType,
  ConnectionStatus,
  StartOptions,
  validCommandsFromReport,
} from '../../models/CardBot';
import { FilterComponentType } from '../../models/FilterComponent';
import { AnyFilter, disabledFilters, FilterContext, Filters } from '../../models/filters/Filters';
import { MTGFilter } from '../../models/filters/mtg/MTGFilters';
import { Game } from '../../models/Game';
import { ReportType } from '../../models/Report';
import { DialogStatus } from '../../stores/DialogStore';
import { FilterDialog } from '../components/filters/FilterDialog';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';
import { CardBotButton } from './components/CardBotButton';
import { CardBotFilters } from './components/CardBotFilters';
import { CardBotInfoLabel } from './components/CardBotInfoLabel';
import { CardBotOptionsPanel } from './components/CardBotOptionsPanel';

interface IProps {
  match?: match<CardBotParams>;
  location?: any;
  dispatcher: Dispatcher;
}

interface CardBotParams {
  deviceID: string;
  mfaToken: string;
}

export const CardBotControl = HasMe.Attach<IProps & HasDispatcher.IProps>(
  HasCardBot.Attach<IProps & HasDispatcher.IProps & HasMe.IProps>(
    HasDialog.Attach<IProps & HasDispatcher.IProps & HasMe.IProps & HasCardBot.IProps>(
      HasSubscription.Attach<IProps & HasDispatcher.IProps & HasMe.IProps & HasCardBot.IProps & HasDialog.IProps>(
        HasCardLists.Attach<
          IProps & HasDispatcher.IProps & HasMe.IProps & HasCardBot.IProps & HasDialog.IProps & HasSubscription.IProps
        >(
          class extends React.Component<
            IProps &
              HasDispatcher.IProps &
              HasMe.IProps &
              HasCardBot.IProps &
              HasDialog.IProps &
              HasSubscription.IProps &
              HasCardLists.IProps
          > {
            /**
             * @override
             */
            constructor(
              props: IProps &
                HasDispatcher.IProps &
                HasMe.IProps &
                HasCardBot.IProps &
                HasDialog.IProps &
                HasSubscription.IProps &
                HasCardLists.IProps,
            ) {
              super(props);
            }

            private interval?: SetIntervalAsyncTimer;

            /**
             * @override
             */
            async componentDidMount() {
              // Check if CardBot is already connected.
              if (this.props.cardBot.get('connectionStatus') === ConnectionStatus.PAIRED) {
                this.startListening(this.props.cardBot.get('deviceID'));
                return;
              }

              const queryDeviceID = this.props.match?.params?.deviceID;

              const queryMFAToken = new URLSearchParams(this.props.location.search).get('mfa_token') || undefined;
              if (queryDeviceID) {
                const options = new CardBotOptions({
                  condition: this.props.me.get('preferences').get('collection').get('condition'),
                });
                CardBotActions.pair(this.props.dispatcher, queryDeviceID, options, queryMFAToken);
                this.startListening(queryDeviceID);
              } else {
                MessageActions.error('No device ID provided', this.props.dispatcher);
                history.push('/cardbot-connect');
              }
              SubscriptionActions.subscription(this.props.dispatcher);
            }

            componentWillUnmount() {
              this.stopListening();
            }

            public render() {
              const cardBot = this.props.cardBot;
              if (cardBot.get('connectionStatus') === ConnectionStatus.DISCONNECTED) {
                return <Redirect to="/cardbot-connect" />;
              }

              const options = cardBot.get('options');
              const validCommands = validCommandsFromReport(cardBot.get('latestReport').get('reportType'));
              const latestReport = cardBot.get('latestReport');
              const reportType = latestReport.get('reportType');
              const scanningInfo = cardBot.get('scanningInfo');

              const disabled = !(
                reportType === ReportType.IDLE ||
                reportType === ReportType.SCAN_COMPLETED ||
                reportType === ReportType.PAIRED
              );

              if (reportType === ReportType.FATAL_ERROR) {
                // Prevent polling for reports if CardBot is in fatal error
                this.stopListening();
              }

              let filter: AnyFilter;

              // TODO: Unify - filter component accepts AnyFilter
              switch (options.get('game')) {
                case Game.MTG:
                  filter = cardBot.get('filter');
                  break;
                case Game.POKEMON:
                  filter = cardBot.get('pokeFilter');
                  break;
                case Game.YUGIOH:
                  filter = cardBot.get('yugiFilter');
                  break;
                case Game.LORCANA:
                  filter = cardBot.get('lorcanaFilter');
                  break;
              }

              let disabledScanRestrictions = disabledFilters(FilterContext.CARD);

              if (!this.props.subscription.isMerchant()) {
                disabledScanRestrictions = disabledScanRestrictions.push(FilterComponentType.CARD_LIST);
              }
              const unicodeWarningSign = String.fromCharCode(9888);
              return (
                <>
                  <Helmet>
                    <title>CardCastle: CardBot Control</title>
                  </Helmet>
                  <Navbar
                    dispatcher={this.props.dispatcher}
                    me={this.props.me}
                    selection="cardbot-control"
                    isRibbon={true}
                  />
                  <FilterDialog
                    title="Scan Restrictions"
                    description={
                      <>
                        <p>Use a filter to assert the characteristics of the cards to be scanned.</p>
                        <p>
                          {unicodeWarningSign}
                          <strong>Warning:</strong> Scanning cards which don't match the applied scan restrictions will
                          lead to chaotic results.
                        </p>
                      </>
                    }
                    dispatcher={this.props.dispatcher}
                    isOpen={this.props.dialogStatus === DialogStatus.SCAN_RESTRICTIONS}
                    filters={this.props.cardBot.get('options').get('scanRestrictions')}
                    cardLists={this.props.cardLists}
                    onUpdate={(filters: MTGFilter) =>
                      CardBotActions.updateOptions(
                        this.props.cardBot.get('options').set('scanRestrictions', filters),
                        this.props.dispatcher,
                      )
                    }
                    onDismiss={() => DialogActions.close(this.props.dispatcher)}
                    disabledFilters={disabledScanRestrictions}
                  />
                  <section className="section has-navbar has-footer">
                    <div className="container-fluid" style={{ marginTop: '2rem', marginBottom: '1rem' }}>
                      <div className="row center-xs">
                        <div className="cardbot-info--left col-sm-3">
                          <CardBotInfoLabel heading="CardBot" subheading={cardBot.shortID()} />
                        </div>
                        <div className="cardbot-info--right col-sm-3">
                          <CardBotInfoLabel
                            heading="Status"
                            // Disguises "Fatal Error" as "Error" as "Fatal Error" will freak out many end users.
                            subheading={
                              reportType === ReportType.FATAL_ERROR
                                ? 'Error'
                                : TextFormat.capitalize(reportType.replace('_', ' ').toLowerCase())
                            }
                            spinner={reportType === ReportType.PROCESSING || reportType === ReportType.SCANNING}
                          />
                        </div>
                      </div>
                      {latestReport.get('rawMessage') ? (
                        <div className="cardbot-info--center col-sm-12">{latestReport.get('rawMessage')}</div>
                      ) : null}
                      {scanningInfo ? (
                        <div className="row center-xs" style={{ marginBottom: '1rem' }}>
                          <div className="cardbot-info--center col-sm-2">
                            <CardBotInfoLabel heading="Total" subheading={scanningInfo.totalCount().toString()} />
                          </div>
                          <div className="cardbot-info--center col-sm-2">
                            <CardBotInfoLabel
                              heading="Accepted"
                              subheading={`${scanningInfo.get('acceptCount').toString()} (${scanningInfo
                                .percentage('acceptCount')
                                .toString()}%)`}
                            />
                          </div>
                          <div className="cardbot-info--center col-sm-2">
                            <CardBotInfoLabel
                              heading="Rejected"
                              subheading={`${scanningInfo.get('rejectCount').toString()} (${scanningInfo
                                .percentage('rejectCount')
                                .toString()}%)`}
                            />
                          </div>
                        </div>
                      ) : null}
                      <div className="control-buttons">
                        {validCommands.map((commandType: CommandType) => {
                          return (
                            <CardBotButton
                              key={commandType.toString()}
                              commandType={commandType}
                              onClickCommand={this.onClickCommand.bind(this)}
                              disabled={!filter.isValid()}
                            />
                          );
                        })}
                      </div>
                      <CardBotOptionsPanel dispatcher={this.props.dispatcher} options={options} disabled={disabled} />
                      <CardBotFilters
                        dispatcher={this.props.dispatcher}
                        subscription={this.props.subscription}
                        filter={filter}
                        game={options.get('game')}
                        disabled={disabled}
                      />
                    </div>
                  </section>
                  <Footer />
                </>
              );
            }

            private async startListening(deviceID: string) {
              this.interval = setIntervalAsync(() => {
                CardBotActions.getReports(this.props.dispatcher, deviceID);
              }, 500);
            }

            private stopListening() {
              if (this.interval) clearIntervalAsync(this.interval);
            }

            public async onClickCommand(commandType: CommandType) {
              const command = this.cardBotCommandFromType(commandType);
              if (commandType === CommandType.OKAY) {
                // CommandType.OKAY only happens on FATAL_ERROR. By definition the CardBot won't be connected if FATAL_ERROR.
                CardBotActions.setDisconnectedOnOkay(this.props.dispatcher);
              } else {
                if (commandType === CommandType.START) {
                  CardBotActions.resetScanningInfo(this.props.dispatcher);
                }
                CardBotActions.sendCommand(this.props.dispatcher, this.props.cardBot.get('deviceID'), command);
              }
            }

            private cardBotCommandFromType(commandType: CommandType): CardBotCommand {
              switch (commandType) {
                case CommandType.OKAY:
                  return new CardBotCommand();
                case CommandType.START:
                  let filters: Filters;
                  switch (this.props.cardBot.get('options').get('game')) {
                    case Game.MTG:
                      filters = this.props.cardBot.get('filter');
                      break;
                    case Game.POKEMON:
                      filters = this.props.cardBot.get('pokeFilter');
                      break;
                    case Game.YUGIOH:
                      filters = this.props.cardBot.get('yugiFilter');
                      break;
                    case Game.LORCANA:
                      filters = this.props.cardBot.get('lorcanaFilter');
                      break;
                  }
                  return new CardBotCommand({
                    commandType: CommandType.START,
                    startOptions: StartOptions.fromCardBotControl(this.props.cardBot.get('options'), filters),
                  });
                default:
                  return new CardBotCommand({
                    commandType: commandType,
                  });
              }
            }
          },
        ),
      ),
    ),
  ),
);
