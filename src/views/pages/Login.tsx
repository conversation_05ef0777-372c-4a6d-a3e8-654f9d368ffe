import * as React from 'react';
import * as MessageActions from '../../actions/MessageActions';
import * as UserActions from '../../actions/UserActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import * as HasMeta from '../../containers/HasMeta';
import Dispatcher from '../../dispatcher/Dispatcher';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';

interface IProps {
  me: any;
  location?: {
    search?: string;
  };
}

interface IState {
  email: string;
  password: string;
  loading: boolean;
}

function BuildLogin(dispatcher: Dispatcher) {
  const description =
    'Already have an account? Log in to browse and manage ' +
    'your Magic card collection and view card prices and statistics.';

  class Login extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = { email: '', password: '', loading: true };
    }

    componentDidMount(): void {
      this.setState({ loading: false });
    }

    public render() {
      return (
        <div>
          <Navbar dispatcher={this.props.dispatcher} me={this.props.me} isLogin={true} />
          <div className="section has-footer has-navbar">
            <div className="container">
              <h1 className="page-heading has-underline">Login</h1>
              {this.renderForm.bind(this)()}
            </div>
            <Footer />
          </div>
        </div>
      );
    }

    private renderForm() {
      if (this.state.loading) {
        return <div className="spinner-xl" style={{ marginTop: '6.5rem' }}></div>;
      }
      return (
        <form className="auth-form offset-sm-2 col-sm-8" onSubmit={this.onClickLogin.bind(this)}>
          <div className="row">
            <label className="label col-sm-3 offset-md-1 col-md-2" htmlFor="email">
              Email/Username
            </label>
            <input
              className="input col-sm-9 col-md-8"
              type="text"
              placeholder="<EMAIL>"
              value={this.state.email}
              onChange={this.onChangeEmail.bind(this)}
            />
          </div>
          <div className="row">
            <label className="label col-sm-3 offset-md-1 col-md-2" htmlFor="password">
              Password
            </label>
            <input
              className="input col-sm-9 col-md-8"
              type="password"
              placeholder="password"
              value={this.state.password}
              onChange={this.onChangePassword.bind(this)}
            />
          </div>
          <div className="row">
            <input
              className="button-primary offset-xs-6 col-xs-6 offset-sm-9 col-sm-3 offset-md-9 col-md-2"
              type="submit"
              value="Login"
              onClick={this.onClickLogin.bind(this)}
            />
          </div>
          <div className="row">
            <ul className="form-options">
              <li>
                <a href="/signup">
                  <strong>Create an account</strong>
                </a>
              </li>
              <li>
                <a href="/reset">Forgot your password?</a>
              </li>
              <li>
                <a href="/resend">Didn't receive confirmation instructions?</a>
              </li>
            </ul>
          </div>
        </form>
      );
    }

    private onChangeEmail(evt: React.SyntheticEvent<HTMLInputElement>) {
      this.setState({ email: evt.currentTarget.value as string });
    }

    private onChangePassword(evt: React.SyntheticEvent<HTMLInputElement>) {
      this.setState({ password: evt.currentTarget.value as string });
    }

    private async onClickLogin(evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();
      evt.stopPropagation();

      // login
      if (this.state.email !== '' && this.state.password !== '') {
        try {
          await UserActions.login(this.state.email, this.state.password, this.props.dispatcher);
          // handle redirect
          if (this.props.location) {
            const params = new URLSearchParams(this.props.location.search);
            const redirect = params.get('redirect');
            if (redirect) {
              location.assign(decodeURIComponent(redirect));
            } else {
              location.assign('/collection');
            }
          } else {
            location.assign('/collection');
          }
        } catch (err) {
          if (err.status === 401) {
            return MessageActions.error('Incorrect email address or password', this.props.dispatcher);
          } else if (err.status === 403) {
            return MessageActions.infoButton(
              'You need to confirm your account. Check your inbox.',
              'Resend Confirmation',
              () => {
                location.assign('/resend');
              },
              this.props.dispatcher,
            );
          } else if (err.status === 502) {
            return MessageActions.error(
              'We are deploying some new features, please try again soon',
              this.props.dispatcher,
            );
          }

          MessageActions.error('Something went wrong, please try again later', this.props.dispatcher);
        }
      }
    }
  }

  return HasDispatcher.Attach<IProps>(
    HasMeta.Attach<HasDispatcher.IProps & IProps>(HasMe.Attach<HasDispatcher.IProps & IProps>(Login), description),
    dispatcher,
  );
}

export default BuildLogin;
