import * as React from 'react';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import Dispatcher from '../../dispatcher/Dispatcher';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';

interface IProps {}

interface IState {}

function BuildPrivacyPolicy(dispatcher: Dispatcher) {
  class PrivacyPolicy extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = {};
    }

    public componentDidMount() {
      this.privacyLoader();
    }

    public render() {
      return (
        <div>
          <Navbar dispatcher={this.props.dispatcher} me={this.props.me} />
          <div className="section has-footer has-navbar">
            <div className="container">
              <h1 className="page-heading has-underline">Privacy Policy</h1>

              <a
                className="iubenda-nostyle no-brand iub-body-embed iubenda-embed"
                href="//www.iubenda.com/privacy-policy/773179"
                title="Privacy Policy"
              ></a>
            </div>
            <Footer />
          </div>
        </div>
      );
    }

    private privacyLoader() {
      if (typeof document === 'undefined') {
        return;
      }

      const scriptTag = document.createElement('script');
      const tag = document.getElementsByTagName('script')[0];
      scriptTag.src = '//cdn.iubenda.com/iubenda.js';
      tag!.parentNode!.insertBefore(scriptTag, tag);
    }
  }

  return HasDispatcher.Attach<IProps>(HasMe.Attach<HasDispatcher.IProps & IProps>(PrivacyPolicy), dispatcher);
}

export default BuildPrivacyPolicy;
