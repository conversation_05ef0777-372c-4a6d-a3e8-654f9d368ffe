import * as React from 'react';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import * as HasMeta from '../../containers/HasMeta';
import Dispatcher from '../../dispatcher/Dispatcher';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';

interface IProps {
  me: any;
}

interface IState {}

function BuildDownloadApps(dispatcher: Dispatcher) {
  const description =
    'The fastest way to build your digital collection. ' +
    'The CardCastle app uses image recognition to quickly detect, recognise ' +
    'and add your cards to your online collection.';

  class DownloadApps extends React.Component<HasDispatcher.IProps & IProps, IState> {
    constructor(props: HasDispatcher.IProps & IProps) {
      super(props);
      this.state = {};
    }

    public render() {
      return (
        <div>
          <Navbar dispatcher={this.props.dispatcher} me={this.props.me} />

          <div className="section has-background-grey has-underline has-navbar flex vertical align-center padding-md">
            <div className="container">
              <div className="row justify-center">
                <div className="flex justify-center col-xs-12 col-sm-3">
                  <img className="apps-device-image" src="/images/apps-iphone.png" />
                </div>
                <div className="flex justify-center col-sm-3 visible-sm">
                  <img className="apps-device-image" src="/images/apps-android.png" />
                </div>
              </div>

              <div className="section align-center">
                <h1 className="apps-heading">The CardCastle Mobile App</h1>
                <h2 className="apps-subheading">The quickest and easiest way to catalogue your cards!</h2>
              </div>

              <div className="row">
                <div className="col-xs-6 col-sm-4 offset-sm-2 col-md-3 offset-md-3">
                  <a href="/ios" target="_blank">
                    <img className="apps-download-image" src="/images/download-apple.png" />
                  </a>
                </div>
                <div className="col-xs-6 col-sm-4 col-md-3">
                  <a href="/android" target="_blank">
                    <img className="apps-download-image" src="/images/download-google.png" />
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="section flex vertical align-center padding-sm">
            <div className="container">
              <div className="row">
                <div className="col-xs-12 col-sm-6 col-md-3 apps-featurette-container">
                  <img src="/images/screenshot-capture.png" />
                </div>

                <div className="col-xs-12 col-sm-6 col-md-3 apps-featurette-container">
                  <img src="/images/screenshot-price.png" />
                </div>

                <div className="col-xs-12 col-sm-6 col-md-3 apps-featurette-container">
                  <img src="/images/screenshot-browse.png" />
                </div>

                <div className="col-xs-12 col-sm-6 col-md-3 apps-featurette-container">
                  <img src="/images/screenshot-lookup.png" />
                </div>
              </div>
            </div>
          </div>

          <div className="section has-background-cards padding-lg flex vertical align-center">
            <h1 className="section-heading--white">Scanning your deck is fun and easy!</h1>
            <h1 className="section-heading--white apps-subheading">Download for free</h1>

            <div className="container">
              <div className="row">
                <div className="col-xs-6 col-sm-4 offset-sm-2 col-md-3 offset-md-3">
                  <a href="/ios" target="_blank">
                    <img className="apps-download-image" src="/images/download-apple-alt.png" />
                  </a>
                </div>
                <div className="col-xs-6 col-sm-4 col-md-3">
                  <a href="/android" target="_blank">
                    <img className="apps-download-image" src="/images/download-google-alt.png" />
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="section has-footer align-center padding-lg has-footer">
            <div className="container">
              <div className="col-sm-8 offset-sm-2 align-center flex vertical">
                <h3 className="apps-tutorial-subheading">
                  Need help scanning your cards? Check out our tutorials for tips and techniques to improve the accuracy
                  of the app.
                </h3>
                <a className="button-primary" href="/tutorial">
                  Tutorial
                </a>
              </div>
            </div>

            <Footer />
          </div>
        </div>
      );
    }

    private onClickLogin(evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();
    }
  }

  return HasDispatcher.Attach<IProps>(
    HasMeta.Attach<HasDispatcher.IProps & IProps>(
      HasMe.Attach<HasDispatcher.IProps & IProps>(DownloadApps),
      description,
    ),
    dispatcher,
  );
}

export default BuildDownloadApps;
