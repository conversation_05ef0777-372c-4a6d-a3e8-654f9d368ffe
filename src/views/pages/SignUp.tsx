import IsEmail from 'isemail';
import * as React from 'react';
import * as MessageActions from '../../actions/MessageActions';
import * as UserActions from '../../actions/UserActions';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import * as Has<PERSON>eta from '../../containers/HasMeta';
import Dispatcher from '../../dispatcher/Dispatcher';
import track from '../../helpers/ga_helper';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';

interface IProps {}

interface IState {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  loading: boolean;
}

function BuildSignUp(dispatcher: Dispatcher) {
  const description =
    'Create an account and start organising your ' +
    'collection today. Get access to powerful management tools, card prices ' +
    'and statistics about your collection of Magic cards.';

  class SignUp extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = {
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        loading: true,
      };
    }

    componentDidMount(): void {
      this.setState({ loading: false });
    }

    public render() {
      return (
        <div>
          <Navbar dispatcher={this.props.dispatcher} me={this.props.me} />
          <div className="section has-footer has-navbar">
            <div className="container">
              <h1 className="page-heading has-underline">Sign Up</h1>
              {this.renderForm.bind(this)()}
            </div>
          </div>
          <Footer />
        </div>
      );
    }

    private renderForm() {
      if (this.state.loading) {
        return <div className="spinner-xl" style={{ marginTop: '6.5rem' }}></div>;
      }
      return (
        <form className="auth-form offset-sm-2 col-sm-8">
          <div className="row">
            <label className="label col-sm-3 offset-md-1 col-md-2" htmlFor="username">
              Username
            </label>
            <input
              className="input col-sm-9 col-md-8"
              type="text"
              placeholder="username"
              value={this.state.username}
              onChange={this.onChangeUsername.bind(this)}
            />
          </div>
          <div className="row">
            <label className="label col-sm-3 offset-md-1 col-md-2" htmlFor="email">
              Email
            </label>
            <input
              className="input col-sm-9 col-md-8"
              type="email"
              placeholder="<EMAIL>"
              value={this.state.email}
              onChange={this.onChangeEmail.bind(this)}
            />
          </div>
          <div className="row">
            <label className="label col-sm-3 offset-md-1 col-md-2" htmlFor="password">
              Password
            </label>
            <input
              className="input col-sm-9 col-md-8"
              type="password"
              placeholder="********"
              value={this.state.password}
              onChange={this.onChangePassword.bind(this)}
            />
          </div>

          <div className="row">
            <label className="label col-sm-3 offset-md-1 col-md-2" htmlFor="passwordConfirmation">
              Password Confirmation
            </label>
            <input
              className="input col-sm-9 col-md-8"
              type="password"
              placeholder="********"
              value={this.state.confirmPassword}
              onChange={this.onChangeConfirmPassword.bind(this)}
            />
          </div>
          <div className="row">
            <input
              className="button-primary offset-xs-6 col-xs-6 offset-sm-9 col-sm-3 offset-md-9 col-md-2"
              type="submit"
              value="Sign Up"
              onClick={this.onClickSignUp.bind(this)}
            />
          </div>
          <div className="row">
            <ul className="form-options">
              <li>
                <a href="/login">
                  <strong>Already have an account?</strong>
                </a>
              </li>
              <li>
                <a href="/reset">Forgot your password?</a>
              </li>
              <li>
                <a href="/resend">Didn't receive confirmation instructions?</a>
              </li>
            </ul>
          </div>
        </form>
      );
    }

    private onChangeEmail(evt: React.SyntheticEvent<HTMLInputElement>) {
      this.setState({ email: evt.currentTarget.value as string });
    }

    private onChangeUsername(evt: React.SyntheticEvent<HTMLInputElement>) {
      this.setState({ username: evt.currentTarget.value as string });
    }

    private onChangePassword(evt: React.SyntheticEvent<HTMLInputElement>) {
      this.setState({ password: evt.currentTarget.value as string });
    }

    private onChangeConfirmPassword(evt: React.SyntheticEvent<HTMLInputElement>) {
      this.setState({ confirmPassword: evt.currentTarget.value as string });
    }

    private async onClickSignUp(evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();

      // validate input
      if (this.state.username === '') {
        return MessageActions.error('Please enter a username', this.props.dispatcher);
      }
      if (this.state.username.includes('/')) {
        return MessageActions.error('Username must not contain unsafe character "/"', this.props.dispatcher);
      }
      if (this.state.email === '' || !IsEmail.validate(this.state.email)) {
        return MessageActions.error('Please enter a valid email address', this.props.dispatcher);
      }
      if (this.state.password === '' || this.state.password.length < 8) {
        return MessageActions.error('Please enter a password of at least 8 characters', this.props.dispatcher);
      }
      if (this.state.confirmPassword === '' || this.state.confirmPassword !== this.state.password) {
        return MessageActions.error('Your passwords do not match', this.props.dispatcher);
      }

      try {
        // attempt sign up
        await UserActions.signup(this.state.email, this.state.password, this.state.username, this.props.dispatcher);
        // navigation to collection on success
        if (typeof fbq !== 'undefined') {
          fbq('track', 'CompleteRegistration');
        }
        track('account', 'signup');
        location.assign('/builder');
      } catch (err) {
        if (err) {
          console.error(err);
          if (err.status === 400) {
            return MessageActions.error('Email address or username already taken', this.props.dispatcher);
          } else if (err.status === 502) {
            return MessageActions.error(
              'We are deploying some new features, please try again soon',
              this.props.dispatcher,
            );
          }
        }

        MessageActions.error('Something went wrong, please try again later', this.props.dispatcher);
      }
    }
  }

  return HasDispatcher.Attach<IProps>(
    HasMeta.Attach<HasDispatcher.IProps & IProps>(HasMe.Attach<HasDispatcher.IProps & IProps>(SignUp), description),
    dispatcher,
  );
}

export default BuildSignUp;
