import * as React from 'react';
import * as MessageActions from '../../actions/MessageActions';
import * as UsersAPI from '../../api/Users';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import Dispatcher from '../../dispatcher/Dispatcher';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';

interface IProps {}

interface IState {}

export default function (dispatcher: Dispatcher) {
  class ResetPassword extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    public refs: {
      email: HTMLInputElement;
      [key: string]: HTMLElement;
    };

    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = {};
    }

    public render() {
      return (
        <div>
          <Navbar dispatcher={this.props.dispatcher} me={this.props.me} />
          <div className="section has-footer has-navbar">
            <div className="container">
              <h1 className="page-heading has-underline">Forgot your password?</h1>
              <form className="auth-form offset-sm-2 col-sm-8">
                <div className="row">
                  <label className="label col-sm-3 offset-md-1 col-md-2" htmlFor="email">
                    Email/Username
                  </label>
                  <input className="input col-sm-9 col-md-8" ref="email" type="text" placeholder="<EMAIL>" />
                </div>

                <div className="row">
                  <input
                    className="button-primary offset-xs-6 col-xs-6 offset-sm-9 col-sm-3 offset-md-9 col-md-2"
                    type="submit"
                    value="Send"
                    onClick={this.onClickSend.bind(this)}
                  />
                </div>

                <div className="row">
                  <ul className="form-options">
                    <li>
                      <a href="/signup">
                        <strong>Create an account</strong>
                      </a>
                    </li>
                    <li>
                      <a href="/login">Already have an account?</a>
                    </li>
                    <li>
                      <a href="/resend">Didn't receive confirmation instructions?</a>
                    </li>
                  </ul>
                </div>
              </form>
            </div>
            <Footer />
          </div>
        </div>
      );
    }

    private async onClickSend(evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();
      // attempt reset password
      if (!this.refs.email.value || !this.refs.email.value.length) {
        return MessageActions.error('Please enter a email or username', this.props.dispatcher);
      }
      try {
        await UsersAPI.resetPassword(this.refs.email.value);
        MessageActions.success('A password reset email has been sent to your inbox', this.props.dispatcher);
        this.refs.email.value = '';
      } catch (err) {
        err && console.error(err);
        MessageActions.error('An unknown error occurred', this.props.dispatcher);
        // FIX: add proper error messaging
      }
    }
  }

  return HasDispatcher.Attach<IProps>(HasMe.Attach<HasDispatcher.IProps & IProps>(ResetPassword), dispatcher);
}
