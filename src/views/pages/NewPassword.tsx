import * as React from 'react';
import * as MessageActions from '../../actions/MessageActions';
import * as UsersAPI from '../../api/Users';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import Dispatcher from '../../dispatcher/Dispatcher';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';

interface IProps {}

interface IState {}

function BuildNewPassword(d: Dispatcher) {
  class NewPassword extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = {};
    }

    public render() {
      return (
        <div>
          <Navbar dispatcher={this.props.dispatcher} me={this.props.me} />
          <div className="section has-footer has-navbar">
            <div className="container">
              <h1 className="page-heading has-underline">New Password</h1>
              <form className="auth-form offset-sm-2 col-sm-8">
                <div className="row">
                  <label className="label col-sm-3 offset-md-1 col-md-2" htmlFor="password">
                    Password
                  </label>
                  <input className="input col-sm-9 col-md-8" ref="password" type="password" placeholder="********" />
                </div>

                <div className="row">
                  <label className="label col-sm-3 offset-md-1 col-md-2" htmlFor="passwordConfirmation">
                    Password Confirmation
                  </label>
                  <input
                    className="input col-sm-9 col-md-8"
                    ref="confirmPassword"
                    type="password"
                    placeholder="********"
                  />
                </div>

                <div className="row">
                  <input
                    className="button-primary offset-xs-6 col-xs-6 offset-sm-9 col-sm-3 offset-md-9 col-md-2"
                    type="submit"
                    value="Submit"
                    onClick={this.onClickSubmit.bind(this)}
                  />
                </div>

                <div className="row">
                  <ul className="form-options">
                    <li>
                      <a href="/signup">
                        <strong>Create an account</strong>
                      </a>
                    </li>
                    <li>
                      <a href="/login">Already have an account?</a>
                    </li>
                    <li>
                      <a href="/resend">Didn't receive confirmation instructions?</a>
                    </li>
                  </ul>
                </div>
              </form>
            </div>
          </div>
          <Footer />
        </div>
      );
    }

    private async onClickSubmit(evt: React.SyntheticEvent<HTMLElement>) {
      evt.preventDefault();
      const password = this.refs['password'];
      const confirm = this.refs['confirmPassword'];
      const resetToken = location.search.split('reset_password_token=')[1];

      // check for reset token
      if (!resetToken) {
        return console.error('No reset password token');
      }

      if (password instanceof HTMLInputElement && confirm instanceof HTMLInputElement) {
        // check fields have values
        if (!password.value || !confirm.value) {
          return MessageActions.error('Must enter a password and confirmation', this.props.dispatcher);
        }

        // check valid length
        if (password.value.length < 8) {
          return MessageActions.error('New password must be more than 8 characters', this.props.dispatcher);
        }

        // check fields match
        if (confirm.value !== password.value) {
          return MessageActions.error('Passwords do not match', this.props.dispatcher);
        }

        try {
          await UsersAPI.newPassword(resetToken, password.value, confirm.value);
          location.assign('/collection');
          MessageActions.success('Password has been updated', this.props.dispatcher);
        } catch (err) {
          err && console.error(err);
          if (err && err.error && err.error.length) {
            return MessageActions.error(err.error, this.props.dispatcher);
          }
          MessageActions.error('An unknown error occurred', this.props.dispatcher);
        }
      }
    }
  }

  return HasDispatcher.Attach<IProps>(HasMe.Attach<HasDispatcher.IProps & IProps>(NewPassword), d);
}

export default BuildNewPassword;
