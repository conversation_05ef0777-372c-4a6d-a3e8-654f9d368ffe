import * as React from 'react';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as <PERSON>Me from '../../containers/HasMe';
import * as HasMeta from '../../containers/HasMeta';
import Dispatcher from '../../dispatcher/Dispatcher';
import { environment, releaseStage } from '../../helpers/environment';
import { FreeLimits } from '../../models/Subscriptions';
import { Footer } from '../components/Footer';

interface IProps {}

interface IState {}

export default function (dispatcher: Dispatcher) {
  const description =
    'Manage your Magic: The Gathering card collection ' +
    'online. Browse and organise your cards on a powerful web platform. Access ' +
    'your cards, card prices and collection statistics anywhere.';

  class LandingPage extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = {};
    }

    public render() {
      if (environment().production && releaseStage().production) {
        // Redirect to Squarespace
        location.replace(environment().homeURL());
        return null;
      }

      return (
        <div>
          <div className="section has-background-grey has-underline flex vertical align-center">
            <div className="navigation-login-container">
              <a className="button-primary" href="/login">
                Login
              </a>
            </div>
            <img className="primary-logo-image" src="/images/logo-purple-small.png" />
            <h1 className="landing-page-heading">CardCastle</h1>
            <div className="landing-page-subheading">Cataloging your collection, made easy.</div>
            <a className="button-primary" href="/signup">
              Get Started
            </a>
          </div>

          <div className="section padding-md">
            <div className="row">
              <div className="featurette-container col-xs-12 col-sm-4">
                <img src="/images/featurette-1.png" />
                <h3>Your collection</h3>
                <p>
                  Thousands of MTG cards?
                  <br />
                  No idea which cards you own, or where they are if you do?
                </p>
              </div>
              <div className="featurette-container col-xs-12 col-sm-4">
                <img src="/images/featurette-2.png" />
                <h3>Digitised</h3>
                <p>Scan your cards into a digital catalog using your smartphone.</p>
              </div>
              <div className="featurette-container col-xs-12 col-sm-4">
                <img src="/images/featurette-3.png" />
                <h3>Organised</h3>
                <p>
                  Browse and manage your collection online. Get access to statistics like how valuable your cards are!
                </p>
              </div>
            </div>
            <div className="row center-xs">
              <a className="button-primary" href="/apps" target="_blank">
                Download Apps
              </a>
            </div>
          </div>

          <div className="section has-background-cards padding-lg flex vertical align-center">
            <h1 className="section-heading--white">Scan in your first deck for free!</h1>
            <a className="button-white" href="/signup">
              Get Started
            </a>
          </div>

          <div className="section has-footer flex vertical align-center justify-center">
            <div className="container">
              <div className="row justify-center">
                <div className="col-xs-12 col-sm-10">
                  <div className="row">
                    <div className="pricing-container-left col-xs-12 col-sm-4">
                      <div className="pricing-heading-container">
                        <h3 className="pricing-heading">Squire</h3>
                        <div className="pricing-subheading">Free</div>
                      </div>
                      <div className="pricing-body-container">
                        <ul>
                          <li>Add unlimited cards</li>
                          <li>Browse up to {FreeLimits.CARDS} cards</li>
                          <li>Create {FreeLimits.DECKS} decks</li>
                          <li>Use #tagging to manage your cards</li>
                          <li>Add up to {FreeLimits.TAGS} tags</li>
                          <li>Real-time card pricing data</li>
                        </ul>
                        <a className="button-primary" href="/signup">
                          Sign Up
                        </a>
                      </div>
                    </div>

                    <div className="pricing-container-center col-xs-12 col-sm-4">
                      <div className="pricing-heading-container">
                        <h3 className="pricing-heading">Knight</h3>
                        <div className="pricing-subheading">USD $4/mth</div>
                      </div>
                      <div className="pricing-body-container">
                        <ul>
                          <li>Add unlimited cards</li>
                          <li>Browse your entire collection</li>
                          <li>Use #tagging to manage your cards</li>
                          <li>Add unlimited tags</li>
                          <li>Create unlimited decks</li>
                          <li>Real-time card pricing data</li>
                          <li>Track the value of your entire collection</li>
                          <li>Access to exciting new features as they are released</li>
                        </ul>
                        <a className="button-primary" href="/signup">
                          Sign Up
                        </a>
                      </div>
                    </div>

                    <div className="pricing-container-right col-xs-12 col-sm-4">
                      <div className="pricing-heading-container">
                        <h3 className="pricing-heading">Merchant</h3>
                        <div className="pricing-subheading unavailable">Coming soon</div>
                      </div>
                      <div className="pricing-body-container">
                        <ul>
                          <li>Commercial scale collection management and stocktake</li>
                          <li>Create an online shopfront</li>
                          <li>Trade and sell cards worldwide</li>
                          <li>Sales analytics</li>
                        </ul>
                        <a
                          className="button-primary"
                          href="mailto:<EMAIL>?subject=CardCastle%20Merchant%20Account%20Enquiry"
                        >
                          Contact Us
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Footer />
          </div>
        </div>
      );
    }
  }

  return HasDispatcher.Attach<IProps>(
    HasMeta.Attach<HasDispatcher.IProps & IProps>(
      HasMe.Attach<HasDispatcher.IProps & IProps>(LandingPage),
      description,
    ),
    dispatcher,
  );
}
