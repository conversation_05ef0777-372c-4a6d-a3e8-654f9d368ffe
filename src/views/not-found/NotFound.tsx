import * as React from 'react';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import Dispatcher from '../../dispatcher/Dispatcher';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';

interface IProps {}

interface IState {}

export default function BuildNotFound(dispatcher: Dispatcher) {
  class NotFound extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = {};
    }

    public render() {
      return (
        <div className="page not-found">
          <Navbar dispatcher={this.props.dispatcher} me={this.props.me} />
          <div className="section">
            <h1 className="section-heading">404</h1>
            <h2 className="section-subheading">Looks like this page doesn't exist...</h2>
          </div>
          <Footer />
        </div>
      );
    }
  }

  return HasDispatcher.Attach<IProps>(HasMe.Attach<HasDispatcher.IProps & IProps>(NotFound), dispatcher);
}
