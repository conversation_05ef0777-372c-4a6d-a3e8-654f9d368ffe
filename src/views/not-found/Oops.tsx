import * as React from 'react';
import * as HasDispatcher from '../../containers/HasDispatcher';
import * as HasMe from '../../containers/HasMe';
import Dispatcher from '../../dispatcher/Dispatcher';
import { Footer } from '../components/Footer';
import { Navbar } from '../components/Navbar';

interface IProps {}

interface IState {}

export default function BuildOops(dispatcher: Dispatcher) {
  class Oops extends React.Component<HasDispatcher.IProps & HasMe.IProps & IProps, IState> {
    constructor(props: HasDispatcher.IProps & HasMe.IProps & IProps) {
      super(props);
      this.state = {};
    }

    public render() {
      return (
        <div>
          <Navbar dispatcher={this.props.dispatcher} me={this.props.me} />
          <div className="section has-background-not-found has-navbar align-center padding-lg has-footer">
            <h1 className="not-found-heading">Oops!</h1>
            <h2 className="not-found-subheading">
              Looks like something went wrong, but don't worry we are on top of it.
            </h2>
          </div>
          <Footer />
        </div>
      );
    }
  }

  return HasDispatcher.Attach<IProps>(HasMe.Attach<HasDispatcher.IProps & IProps>(Oops), dispatcher);
}
