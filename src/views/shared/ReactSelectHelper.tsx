import clear from '@iconify/icons-ic/clear';
import error from '@iconify/icons-ic/error-outline';
import pending from '@iconify/icons-ic/outline-pending';
import { Icon } from '@iconify/react';
import React from 'react';
import { components } from 'react-select';

export const styleOverride: Partial<any> = {
  clearIndicator: () => ({}),
  control: () => ({}),
  placeholder: () => ({}),
  dropdownIndicator: () => ({}),
  indicatorSeparator: () => ({}),
  indicatorsContainer: () => ({}),
  menu: () => ({}),
  menuPortalOverride: (base: any) => ({ ...base, zIndex: 9999 }),
  option: () => ({}),
  loadingMessage: () => ({}),
  noOptionsMessage: () => ({}),
  singleValue: () => ({}),
  input: () => ({}),
  multiValue: () => ({}),
  multiValueLabel: () => ({}),
  valueContainer: () => ({}),
};

export const NoOptionsMessage = (props: any) => {
  return (
    <components.NoOptionsMessage {...props}>
      <div className="react-select-unselectable__suggestion">
        <div className="react-select-unselectable__suggestion__set">
          <div className="flex set-symbol">
            <Icon height={'24px'} width={'24px'} icon={error} />
          </div>
        </div>
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__name">No Results</div>
        </div>
      </div>
    </components.NoOptionsMessage>
  );
};

export const SimpleNoOptionsMessage = () => {
  return (
    <div className="react-select-unselectable__suggestion">
      <div className="react-select__suggestion__label">No Options</div>
    </div>
  );
};

export const LoadingMessage = (props: any) => {
  return (
    <components.LoadingMessage {...props}>
      <div className="react-select-unselectable__suggestion">
        <div className="react-select-unselectable__suggestion__set">
          <div className="flex set-symbol">
            <Icon height={'24px'} width={'24px'} icon={pending} />
          </div>
        </div>
        <div className="react-select__suggestion__label">
          <div className="react-select__suggestion__label__name">Searching...</div>
        </div>
      </div>
    </components.LoadingMessage>
  );
};

export const ClearIndicator = (props: any) => {
  return (
    <components.ClearIndicator {...props}>
      <Icon height={'24px'} width={'24px'} icon={clear} />
    </components.ClearIndicator>
  );
};

export const LoadingIndicator = (props: any) => {
  return null;
};

export const LocationReactSelectInput = (props: any) => {
  return <components.Input {...props} maxLength={100} />;
};
