
FROM node:22-slim

RUN mkdir -p /cardcastle/build
RUN mkdir -p /cardcastle/dist

# Copy these files early to preserve cache layers
COPY ./package.json /cardcastle/dist/package.json
COPY ./yarn.lock /cardcastle/dist/yarn.lock

ENV NODE_ENV=production

WORKDIR /cardcastle/dist
RUN yarn global add gulp
RUN yarn install

COPY . /cardcastle/dist
RUN yarn run gulp build:production

EXPOSE 5000

RUN cp -r /cardcastle/dist/build/* /cardcastle/build