import * as Immutable from 'immutable';
import { intersectLanguageOptions, Language } from '../../src/models/Language';

function parseOptions(rawOptions = {}) {
  return Immutable.Map<string, Immutable.List<Language>>(rawOptions);
}

describe('Ensure languages intersect', () => {
  test('When language options are empty', () => {
    expect(intersectLanguageOptions(parseOptions()).count()).toBe(0);
  });

  test('When language options has one value', () => {
    const options = {
      '1': ['en'],
    };
    expect(intersectLanguageOptions(parseOptions(options)).count()).toBe(1);
  });

  test('When language options having nothing in common', () => {
    const options = {
      '1': ['en'],
      '2': ['de'],
    };
    expect(intersectLanguageOptions(parseOptions(options)).count()).toBe(0);
  });

  test('When language options have common values', () => {
    const options = {
      '1': ['en'],
      '2': ['en', 'de'],
    };

    const results = intersectLanguageOptions(parseOptions(options));
    expect(results.count()).toBe(1);
    expect(results.includes(Language.ENGLISH)).toBeTruthy();
  });

  test('When language options have many values', () => {
    const options = {
      '1': ['en', 'de', 'fr'],
      '2': ['en', 'de', 'fr'],
      '3': ['de', 'fr', 'it'],
      '4': ['de', 'fr', 'ko'],
      '5': ['de', 'fr', 'cn'],
    };

    const results = intersectLanguageOptions(parseOptions(options));
    expect(results.count()).toBe(2);
    expect(results.includes(Language.GERMAN)).toBeTruthy();
    expect(results.includes(Language.FRENCH)).toBeTruthy();
  });
});
