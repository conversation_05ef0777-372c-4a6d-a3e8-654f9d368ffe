import { faker } from '@faker-js/faker';
import * as Immutable from 'immutable';
import { CardSet, CardSetType } from '../../src/models/CardSets';
import { CardSetFilter } from '../../src/models/filters/CardSetFilter';
import { SetTypeFilter } from '../../src/models/filters/SetTypeFilter';
import { create, createArray } from './Fake';
import { randomDate } from './FakeHelper';
import { IFake } from './FakeInterface';

export class FakeCardSetFilter implements IFake<CardSetFilter> {
  public fake() {
    return new CardSetFilter({
      include: faker.datatype.boolean(),
      cardSet: create(FakeCardSet),
    });
  }
}

export class FakeCardSet implements IFake<CardSet> {
  public fake() {
    return new CardSet({
      id: faker.number.int({ min: 0 }),
      name: faker.string.sample(),
      releasedAt: randomDate(),
      setCode: fakeSetCode(),
      setType: faker.helpers.enumValue(CardSetType),
    });
  }
}

export class FakeSetTypeFilter implements IFake<SetTypeFilter> {
  public fake() {
    return new SetTypeFilter({
      include: faker.datatype.boolean(),
      setType: faker.helpers.enumValue(CardSetType),
    });
  }
}

function fakeSetCode(): string {
  function fakeSetChar(): string {
    const randomNumber = faker.number.int({ min: 0, max: 35 });
    if (randomNumber < 10) {
      return randomNumber.toString();
    }
    const charCode = 'A'.charCodeAt(1);
    return String.fromCharCode(charCode, randomNumber - 9);
  }
  return fakeSetChar() + fakeSetChar() + fakeSetChar();
}

export function fakeCardSetFilterMap(): Immutable.OrderedMap<string, CardSet> {
  return Immutable.OrderedMap<string, CardSet>(
    createArray(FakeCardSet, faker.number.int({ max: 5 })).map((cardSet: CardSet) => {
      return [cardSet.get('name'), cardSet];
    }),
  );
}

export function fakeCardSetTypeFilterMap(): Immutable.OrderedMap<string, SetTypeFilter> {
  return Immutable.OrderedMap<string, SetTypeFilter>(
    createArray(FakeSetTypeFilter, faker.number.int({ max: 5 })).map((setTypeFilter: SetTypeFilter) => {
      return [setTypeFilter.get('setType'), setTypeFilter];
    }),
  );
}
