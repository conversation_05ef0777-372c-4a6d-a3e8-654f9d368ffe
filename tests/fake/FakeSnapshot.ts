import { faker } from '@faker-js/faker';
import { Snapshot } from '../../src/models/Snapshots';
import { randomDate } from './FakeHelper';
import { IFake } from './FakeInterface';

export class FakeSnapshot implements IFake<Snapshot> {
  fake() {
    return new Snapshot({
      id: faker.number.int(),
      card_value: faker.number.int({ min: 100 }),
      card_count: faker.number.int(),
      created_at: JSON.stringify(randomDate()),
    });
  }
}
