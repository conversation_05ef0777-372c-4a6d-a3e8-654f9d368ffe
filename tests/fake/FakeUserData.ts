import { faker } from '@faker-js/faker';
import { SupportedCurrencyCodes } from '../../src/helpers/currency_helper';
import { Grouping } from '../../src/models/Grouping';
import { Sorting } from '../../src/models/sorting/Sorting';
import {
  CollectionPreferences,
  LocalizationPreferences,
  PrivacyPreferences,
  User,
  UserAvatar,
  UserPreferences,
} from '../../src/models/Users';
import { Viewing } from '../../src/models/Viewing';
import { create } from './Fake';
import { randomDate, timezoneArray } from './FakeHelper';
import { IFake } from './FakeInterface';

export class FakeUserAvatar implements IFake<UserAvatar> {
  fake() {
    return new UserAvatar({
      large: faker.image.url(),
      medium: faker.image.url(),
      thumb: faker.image.url(),
    });
  }
}

export class FakeCollectionPreferences implements IFake<CollectionPreferences> {
  fake() {
    return new CollectionPreferences({
      viewing: faker.helpers.enumValue(Viewing),
      grouping: faker.helpers.enumValue(Grouping),
      sorting: faker.helpers.enumValue(Sorting),
    });
  }
}

export class FakeLocalizationPreferences implements IFake<LocalizationPreferences> {
  fake() {
    return new LocalizationPreferences({
      currency: faker.helpers.enumValue(SupportedCurrencyCodes),
      timezone: faker.helpers.arrayElement(timezoneArray),
    });
  }
}

export class FakeLocalizationPreferencesWithOptions implements IFake<LocalizationPreferences> {
  fake(options: Record<string, any>) {
    return new LocalizationPreferences({
      currency: options?.currency || faker.helpers.enumValue(SupportedCurrencyCodes),
      timezone: faker.helpers.arrayElement(timezoneArray),
    });
  }
}

export class FakeUserPreferences implements IFake<UserPreferences> {
  fake() {
    return new UserPreferences({
      localization: create(FakeLocalizationPreferences),
      privacy: new PrivacyPreferences({ public: faker.datatype.boolean() }), // Privacy currently only contains a single boolean property
      collection: create(FakeCollectionPreferences),
    });
  }
}
export class FakeUserPreferencesWithOptions implements IFake<UserPreferences> {
  fake(options: Record<string, any>) {
    return new UserPreferences({
      localization: create(FakeLocalizationPreferences, options),
      privacy: new PrivacyPreferences({ public: faker.datatype.boolean() }), // Privacy currently only contains a single boolean property
      collection: create(FakeCollectionPreferences),
    });
  }
}
export class FakeUser implements IFake<User> {
  fake(overrides: Record<string, any>) {
    const createdAt = randomDate();
    return new User({
      id: faker.number.int({ min: 0 }),
      username: faker.internet.userName(),
      emailAddress: faker.internet.email(),
      subscribed: faker.datatype.boolean(),
      createdAt: createdAt,
      confirmedAt: faker.date.between({ from: createdAt, to: new Date() }),
      avatar: create(FakeUserAvatar),
      preferences: create(FakeUserPreferences),
      userHash: faker.string.sample(),
      ...overrides,
    });
  }
}

export class FakeUserWithOptions implements IFake<User> {
  fake(options: Record<string, any>) {
    const createdAt = randomDate();
    return new User({
      id: faker.number.int({ min: 0 }),
      username: faker.internet.userName(),
      emailAddress: faker.internet.email(),
      subscribed: typeof options.subscribed != 'undefined' ? options.subscribed : faker.datatype.boolean(),
      createdAt: createdAt,
      confirmedAt: faker.date.between({ from: createdAt, to: new Date() }),
      avatar: create(FakeUserAvatar),
      preferences: create(FakeUserPreferencesWithOptions, options),
      userHash: faker.string.sample(),
    });
  }
}
