import { faker } from '@faker-js/faker';
import { LorcanaFinish } from '../../src/models/lorcana/LorcanaFinish';
import { PokeFinish } from '../../src/models/pokemon/PokeFinish';
import { create } from './Fake';
import { IFake } from './FakeInterface';

export class FakePokeFinish implements IFake<PokeFinish> {
  fake(options?: Record<string, any>) {
    return new PokeFinish({
      uuid: faker.string.sample(),
      name: faker.string.sample(),
      fallbackOrder: options?.fallbackOrder || faker.number.int({ min: 1 }),
    });
  }
}
export const createFakePokeFinishes = (count: number) => {
  return Array.from({ length: count }, (_, index) => {
    return create(FakePokeFinish, { fallbackOrder: index + 1 });
  });
};
export class FakeLorcanaFinish implements IFake<LorcanaFinish> {
  fake(options?: Record<string, any>) {
    return new LorcanaFinish({
      uuid: faker.string.sample(),
      name: faker.string.sample(),
      fallbackOrder: options?.fallbackOrder || faker.number.int({ min: 1 }),
    });
  }
}
export const createFakeLorcanaFinishes = (count: number) => {
  return Array.from({ length: count }, (_, index) => {
    return create(FakeLorcanaFinish, { fallbackOrder: index + 1 });
  });
};
