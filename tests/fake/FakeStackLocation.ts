import { faker } from '@faker-js/faker';
import { StackLocation } from '../../src/models/StackLocation';
import { randomDate } from './FakeHelper';
import { IFake } from './FakeInterface';

export class FakeStackLocation implements IFake<StackLocation> {
  fake() {
    return new StackLocation({
      id: faker.number.int({ min: 0 }),
      uuid: faker.string.uuid(),
      name: faker.location.street(),
      parentId: faker.number.int({ min: 0 }),
      userId: faker.number.int({ min: 0 }),
      createdAt: randomDate(),
      updatedAt: randomDate(),
    });
  }
}
