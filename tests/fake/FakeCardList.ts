import { faker } from '@faker-js/faker';
import { CardList } from '../../src/models/CardList';
import { randomDate } from './FakeHelper';
import { IFake } from './FakeInterface';

// Currently we do not need listedCards and cards properties.
// TODO: Add listedCards and cards generation when needed.
export class FakeCardList implements IFake<CardList> {
  public fake() {
    const fakeDate = randomDate();
    return new CardList({
      name: faker.string.sample(),
      uuid: faker.string.uuid(),
      createdAt: fakeDate,
      updatedAt: fakeDate,
      cardCount: faker.number.int({ min: 0 }),
      description: faker.lorem.lines(1),
    });
  }
}
