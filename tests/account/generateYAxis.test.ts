import { faker } from '@faker-js/faker';
import * as Immutable from 'immutable';
import moment from 'moment';
import { Snapshot } from '../../src/models/Snapshots';
import { generateYAxis } from '../../src/views/account/CollectionValue';
import { createArray, FakeSnapshot } from '../fake/Fake';

test('Test that generateYAxis generates expected values', () => {
  // Generates needed constants.
  const todaysFakeValue = faker.number.int({ min: 100 });
  const day = moment().startOf('day');

  // Generates a list of fake snapshots and then corrects their dates manually by mapping to a new list.
  let fakedSnapshots = Immutable.List<Snapshot>(createArray<Snapshot>(FakeSnapshot, 6));
  fakedSnapshots = Immutable.List<Snapshot>(
    // fakedSnapshots is a Immutable.List<Snapshot>, yet snapshot? has to be optional otherwise there is a TypeScript error.
    // TODO: Investigate why TypeScript is being strange here.
    fakedSnapshots.map((snapshot?: Snapshot) => {
      const out = (snapshot as Snapshot).set(
        'created_at',
        day
          .subtract(1, 'day') // This is a side effect function.
          .toDate()
          .toISOString(),
      );
      return out;
    }),
  );

  // Extracts randomly generated values from the snapshots.
  let expectedValues: number[] = fakedSnapshots
    // fakedSnapshots is a Immutable.List<Snapshot>, yet snapshot? has to be optional otherwise there is a TypeScript error.
    // TODO: Investigate why TypeScript is being strange here.
    .map((snapshot?: Snapshot) => {
      return (snapshot as Snapshot).get('card_value');
    })
    .toArray();

  // Add today's value to the start of the array, which is where it belongs.
  expectedValues.unshift(todaysFakeValue);
  // Converts cents to dollars.
  expectedValues = expectedValues.map((num: number) => {
    return num / 100;
  });
  expect(
    generateYAxis(
      moment().startOf('day'),
      fakedSnapshots,
      moment().startOf('day').toDate().getDay() + 1,
      todaysFakeValue / 100,
    ),
  ).toEqual(expectedValues);
});
