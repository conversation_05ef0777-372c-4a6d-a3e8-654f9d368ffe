import { faker } from '@faker-js/faker';
import { Condition } from '../../src/models/Condition';
import { ColorOption } from '../../src/models/filters/ColorFilter';
import { FilterState } from '../../src/models/filters/FilterState';
import { PriceFilter } from '../../src/models/filters/PriceFilter';
import { TagFilterData } from '../../src/models/filters/TagFilterData';
import { TypeOption } from '../../src/models/filters/TypeFilters';
import { Legality } from '../../src/models/Legality';
import {
  create,
  FakeCardList,
  fakeCardSetFilterMap,
  fakeCardSetTypeFilterMap,
  FakeColorFilter,
  FakeMTGFilter,
  fakeTagFilterMap,
  FakeTypeFilters,
  generateNonEmptyRarityFilter,
  generateNonEmptySubtypesFilter,
  generateNonEmptySupertypesFilter,
} from '../fake/Fake';

const emptyFilter = create(FakeMTGFilter, { empty: true });

describe('When there are no filters, countActiveFilters() should return 0', () => {
  test('When there are no filters, countActiveFilters() should return 0', () => {
    expect(emptyFilter.countActiveFilters()).toBe(0);
  });
});

describe('If all non-quick filters are active, countActiveFilters() === MAX_FILTERS', () => {
  test('countActiveFilters() === MAX_FILTERS', () => {
    const MAX_FILTERS = 15;
    expect(create(FakeMTGFilter).countActiveFilters()).toBe(MAX_FILTERS);
  });
});

describe('Irrelevant variables should not increment countActiveFilters()', () => {
  test('Page number should not effect countActiveFilters()', () => {
    expect(emptyFilter.set('page', faker.number.int({ min: 1 })).countActiveFilters()).toBe(0);
  });

  test('pageSize should not effect countActiveFilters()', () => {
    expect(emptyFilter.set('pageSize', faker.number.int({ min: 1, max: 100 })).countActiveFilters()).toBe(0);
  });

  test('staged status should not effect countActiveFilters()', () => {
    expect(emptyFilter.set('staged', faker.datatype.boolean()).countActiveFilters()).toBe(0);
  });
});

// These tests avoid using create(FakePriceFilter) as
// we need to generate specific types of PriceFilters to achieve proper test coverage.
// Tests adapted from PriceFilter.test.ts
describe('Test PriceFilter interaction with countActiveFilters().', () => {
  test('No values countActiveFilters() === 0', () => {
    expect(emptyFilter.set('priceFilter', new PriceFilter()).countActiveFilters()).toBe(0);
  });

  test('Min only countActiveFilters() === 1', () => {
    expect(
      emptyFilter
        .set('priceFilter', new PriceFilter({ min: faker.number.int({ min: 0 }).toString() }))
        .countActiveFilters(),
    ).toBe(1);
  });

  test('Max only countActiveFilters() === 1', () => {
    expect(
      emptyFilter
        .set('priceFilter', new PriceFilter({ max: faker.number.int({ min: 0 }).toString() }))
        .countActiveFilters(),
    ).toBe(1);
  });

  test('Min == Max countActiveFilters() === 1', () => {
    const number = faker.number.int({ min: 0 }).toString();
    expect(emptyFilter.set('priceFilter', new PriceFilter({ max: number, min: number })).countActiveFilters()).toBe(1);
  });

  test('Min < Max countActiveFilters() === 1', () => {
    const number = faker.number.int({ min: 0 }).toString();
    expect(
      emptyFilter
        .set('priceFilter', new PriceFilter({ max: number + faker.number.int({ min: 0 }).toString(), min: number }))
        .countActiveFilters(),
    ).toBe(1);
  });

  test('Max negative, Min valid countActiveFilters() === 0', () => {
    expect(
      emptyFilter
        .set(
          'priceFilter',
          new PriceFilter({
            max: (faker.number.int({ min: 1 }) * -1).toString(),
            min: faker.number.int({ min: 0 }).toString(),
          }),
        )
        .countActiveFilters(),
    ).toBe(0);
  });

  test('Max garbage, Min valid countActiveFilters() === 0', () => {
    expect(
      emptyFilter
        .set(
          'priceFilter',
          new PriceFilter({
            max: faker.lorem.word(),
            min: faker.number.int({ min: 0 }).toString(),
          }),
        )
        .countActiveFilters(),
    ).toBe(0);
  });

  test('Max valid, Min negative countActiveFilters() === 0', () => {
    expect(
      emptyFilter
        .set(
          'priceFilter',
          new PriceFilter({
            max: faker.number.int({ min: 0 }).toString(),
            min: (faker.number.int({ min: 1 }) * -1).toString(),
          }),
        )
        .countActiveFilters(),
    ).toBe(0);
  });

  test('Both negative countActiveFilters() === 0', () => {
    expect(
      emptyFilter
        .set(
          'priceFilter',
          new PriceFilter({
            max: (faker.number.int({ min: 1 }) * -1).toString(),
            min: (faker.number.int({ min: 1 }) * -1).toString(),
          }),
        )
        .countActiveFilters(),
    ).toBe(0);
  });

  test('Max garbage, Min negative countActiveFilters() === 0', () => {
    expect(
      emptyFilter
        .set(
          'priceFilter',
          new PriceFilter({
            max: faker.lorem.word(),
            min: faker.number.int({ min: 0 }).toString(),
          }),
        )
        .countActiveFilters(),
    ).toBe(0);
  });

  test('Max valid, Min garbage countActiveFilters() === 0', () => {
    expect(
      emptyFilter
        .set(
          'priceFilter',
          new PriceFilter({
            max: faker.number.int({ min: 0 }).toString(),
            min: faker.lorem.word(),
          }),
        )
        .countActiveFilters(),
    ).toBe(0);
  });

  test('Max negative, Min garbage countActiveFilters() === 0', () => {
    expect(
      emptyFilter
        .set(
          'priceFilter',
          new PriceFilter({
            max: (faker.number.int({ min: 1 }) * -1).toString(),
            min: faker.lorem.word(),
          }),
        )
        .countActiveFilters(),
    ).toBe(0);
  });

  test('Both garbage, countActiveFilters() === 0', () => {
    expect(
      emptyFilter
        .set(
          'priceFilter',
          new PriceFilter({
            max: faker.lorem.word(),
            min: faker.lorem.word(),
          }),
        )
        .countActiveFilters(),
    ).toBe(0);
  });
});

describe('If a single non-quick filter is active, countActiveFilters() === 1', () => {
  test('Color filter active', () => {
    expect(
      emptyFilter
        .set('color', create(FakeColorFilter).set(faker.helpers.enumValue(ColorOption), FilterState.ON))
        .countActiveFilters(),
    ).toBe(1);
  });

  test('Type filter active', () => {
    expect(
      emptyFilter
        .set('cardTypes', create(FakeTypeFilters).set(faker.helpers.enumValue(TypeOption), FilterState.ON))
        .countActiveFilters(),
    ).toBe(1);
  });

  test('Rarity filter active', () => {
    expect(emptyFilter.set('rarities', generateNonEmptyRarityFilter()).countActiveFilters()).toBe(1);
  });

  test('Foil filter active', () => {
    expect(emptyFilter.set('foil', true).countActiveFilters()).toBe(1);
    expect(emptyFilter.set('foil', false).countActiveFilters()).toBe(1);
  });

  test('Reserve filter active', () => {
    expect(emptyFilter.set('isReserved', true).countActiveFilters()).toBe(1);
    expect(emptyFilter.set('isReserved', false).countActiveFilters()).toBe(1);
  });

  test('Card list filter active', () => {
    expect(emptyFilter.set('cardList', create(FakeCardList)).countActiveFilters()).toBe(1);
  });

  test('Query active', () => {
    expect(emptyFilter.set('query', faker.lorem.lines(1)).countActiveFilters()).toBe(1);
  });

  test('Tag filter active', () => {
    expect(
      emptyFilter
        .set('tagFilter', new TagFilterData({ tagFilter: fakeTagFilterMap(), excludeUnselected: false }))
        .countActiveFilters(),
    ).toBe(1);
  });

  test('Card set filter active', () => {
    expect(emptyFilter.set('setFilter', fakeCardSetFilterMap()).countActiveFilters()).toBe(1);
  });

  test('Card set type filter active', () => {
    expect(emptyFilter.set('setTypes', fakeCardSetTypeFilterMap()).countActiveFilters()).toBe(1);
  });

  test('Supertype filter active', () => {
    expect(emptyFilter.set('supertypes', generateNonEmptySupertypesFilter()).countActiveFilters()).toBe(1);
  });

  test('Subtype filter active', () => {
    expect(emptyFilter.set('subtypes', generateNonEmptySubtypesFilter()).countActiveFilters()).toBe(1);
  });

  test('Condition filter active', () => {
    expect(emptyFilter.set('condition', faker.helpers.enumValue(Condition)).countActiveFilters()).toBe(1);
  });

  test('Legality filter active', () => {
    expect(emptyFilter.set('legality', faker.helpers.enumValue(Legality)).countActiveFilters()).toBe(1);
  });
});
