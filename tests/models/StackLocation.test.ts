// These tests no longer serve a valid purpose as resolveMultipleLocations is no longer used.
// Elements of these tests can propbably be implemented into card panel tests when CardPanelContent.tsx
// when location link is implemented. // TODO: Implement when CardPanelContent.tsx tests are finalised.
import { faker } from '@faker-js/faker';
import * as Immutable from 'immutable';
import { StackLocation } from '../../src/models/StackLocation';
import { create, FakeStackLocation } from '../fake/Fake';

test('No locations', () => {
  let locationMap = Immutable.Map<string, StackLocation | undefined>();
  const totalLocations = faker.number.int({ min: 1, max: 5 });
  for (let i = 0; i < totalLocations; i++) {
    locationMap = locationMap.set(faker.string.uuid(), undefined);
  }
  expect(StackLocation.resolveMultipleLocations(locationMap, totalLocations)).toBe(undefined);
});

test('All cards have the same location', () => {
  const location = create(FakeStackLocation);
  let locationMap = Immutable.Map<string, StackLocation>();
  const totalLocations = faker.number.int({ min: 1, max: 5 });
  for (let i = 0; i < totalLocations; i++) {
    locationMap = locationMap.set(faker.string.uuid(), location);
  }
  expect(StackLocation.resolveMultipleLocations(locationMap, totalLocations)).toBe(location.get('uuid'));
});

test('Some cards lack locations, others share the location', () => {
  const location = create(FakeStackLocation);
  let locationMap = Immutable.Map<string, StackLocation | undefined>();
  const totalLocations = faker.number.int({ min: 1, max: 5 });
  const undefinedLocations = faker.number.int({ min: 1, max: 5 });
  for (let i = 0; i < totalLocations; i++) {
    locationMap = locationMap.set(faker.string.uuid(), location);
  }
  for (let i = 0; i < undefinedLocations; i++) {
    locationMap = locationMap.set(faker.string.uuid(), undefined);
  }
  expect(StackLocation.resolveMultipleLocations(locationMap, totalLocations)).toBe('Multiple Locations');
});

test("cardCount > locationMap.size. This shouldn't occur, but good to test for it regardless.", () => {
  const location = create(FakeStackLocation);
  let locationMap = Immutable.Map<string, StackLocation>();
  const totalLocations = faker.number.int({ min: 1, max: 5 });
  for (let i = 0; i < totalLocations; i++) {
    locationMap = locationMap.set(faker.string.uuid(), location);
  }
  expect(
    StackLocation.resolveMultipleLocations(locationMap, totalLocations + faker.number.int({ min: 1, max: 5 })),
  ).toBe('Multiple Locations');
});

test('Each card has a location but they are all different', () => {
  let locationMap = Immutable.Map<string, StackLocation>();
  const totalLocations = faker.number.int({ min: 2, max: 5 });
  for (let i = 0; i < totalLocations; i++) {
    locationMap = locationMap.set(faker.string.uuid(), create(FakeStackLocation));
  }
  expect(StackLocation.resolveMultipleLocations(locationMap, totalLocations)).toBe('Multiple Locations');
});
