import { faker } from '@faker-js/faker';
import { PriceFilter } from '../../src/models/filters/PriceFilter';

// These tests avoid using create(FakePriceFilter) as
// we need to generate specific types of PriceFilters to achieve proper test coverage.

test('No values', () => {
  expect(new PriceFilter().isValid()).toBe(true);
});

test('Min only', () => {
  expect(new PriceFilter({ min: faker.number.int({ min: 0 }).toString() }).isValid()).toBe(true);
});

test('Max only', () => {
  expect(new PriceFilter({ max: faker.number.int({ min: 0 }).toString() }).isValid()).toBe(true);
});

test('Min == Max', () => {
  const number = faker.number.int({ min: 0 }).toString();
  expect(new PriceFilter({ max: number, min: number }).isValid()).toBe(true);
});

test('Min < Max', () => {
  const number = faker.number.int({ min: 0 }).toString();
  expect(new PriceFilter({ max: number + faker.number.int({ min: 0 }).toString(), min: number }).isValid()).toBe(true);
});

test('Max negative, Min valid', () => {
  expect(
    new PriceFilter({
      max: (faker.number.int({ min: 1 }) * -1).toString(),
      min: faker.number.int({ min: 0 }).toString(),
    }).isValid(),
  ).toBe(false);
});

test('Max garbage, Min valid', () => {
  expect(
    new PriceFilter({
      max: faker.lorem.word(),
      min: faker.number.int({ min: 0 }).toString(),
    }).isValid(),
  ).toBe(false);
});

test('Max valid, Min negative', () => {
  expect(
    new PriceFilter({
      max: faker.number.int({ min: 0 }).toString(),
      min: (faker.number.int({ min: 1 }) * -1).toString(),
    }).isValid(),
  ).toBe(false);
});

test('Both negative', () => {
  expect(
    new PriceFilter({
      max: (faker.number.int({ min: 1 }) * -1).toString(),
      min: (faker.number.int({ min: 1 }) * -1).toString(),
    }).isValid(),
  ).toBe(false);
});

test('Max garbage, Min negative', () => {
  expect(
    new PriceFilter({
      max: faker.lorem.word(),
      min: faker.number.int({ min: 0 }).toString(),
    }).isValid(),
  ).toBe(false);
});

test('Max valid, Min garbage', () => {
  expect(
    new PriceFilter({
      max: faker.number.int({ min: 0 }).toString(),
      min: faker.lorem.word(),
    }).isValid(),
  ).toBe(false);
});

test('Max negative, Min garbage', () => {
  expect(
    new PriceFilter({
      max: (faker.number.int({ min: 1 }) * -1).toString(),
      min: faker.lorem.word(),
    }).isValid(),
  ).toBe(false);
});

test('Both garbage', () => {
  expect(
    new PriceFilter({
      max: faker.lorem.word(),
      min: faker.lorem.word(),
    }).isValid(),
  ).toBe(false);
});
