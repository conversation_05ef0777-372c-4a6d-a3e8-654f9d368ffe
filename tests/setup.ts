// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import * as superagent from 'superagent';
import { afterEach, vi } from 'vitest';
import 'vitest-dom/extend-expect';

vi.mock('superagent', async (importOriginal) => {
  const actual: typeof superagent = await importOriginal();
  const generalMock = vi.fn(() => ({
    set: vi.fn().mockReturnThis(),
    then: vi.fn().mockReturnThis(),
    end: vi.fn().mockReturnThis(),
    catch: vi.fn().mockReturnThis(),
    finally: vi.fn().mockReturnThis(),
    send: vi.fn().mockReturnThis(),
  }));

  return {
    ...actual,
    get: generalMock,
    post: generalMock,
    patch: generalMock,
    put: generalMock,
    del: generalMock,
  };
});

// Global mock for NProgress
globalThis.NProgress = {
  start: vi.fn().mockReturnThis(),
  done: vi.fn().mockReturnThis(),
  set: vi.fn().mockReturnThis(),
  inc: vi.fn().mockReturnThis(),
  configure: vi.fn().mockReturnThis(),
  isStarted: vi.fn().mockReturnValue(false),
  remove: vi.fn().mockReturnThis(),
  status: null,
  version: '1.0.0',
};

// Mock browser APIs that are not available in jsdom
Object.defineProperty(window, 'URL', {
  value: {
    createObjectURL: vi.fn(() => 'mock-object-url'),
    revokeObjectURL: vi.fn(),
  },
  writable: true,
});

// Mock HTMLCanvasElement.getContext
HTMLCanvasElement.prototype.getContext = vi.fn(() => ({
  fillRect: vi.fn(),
  clearRect: vi.fn(),
  getImageData: vi.fn(() => ({ data: new Array(4) })),
  putImageData: vi.fn(),
  createImageData: vi.fn(() => ({ data: new Array(4) })),
  setTransform: vi.fn(),
  drawImage: vi.fn(),
  save: vi.fn(),
  fillText: vi.fn(),
  restore: vi.fn(),
  beginPath: vi.fn(),
  moveTo: vi.fn(),
  lineTo: vi.fn(),
  closePath: vi.fn(),
  stroke: vi.fn(),
  translate: vi.fn(),
  scale: vi.fn(),
  rotate: vi.fn(),
  arc: vi.fn(),
  fill: vi.fn(),
  measureText: vi.fn(() => ({ width: 0 })),
  transform: vi.fn(),
  rect: vi.fn(),
  clip: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

afterEach(() => {
  cleanup();
});
