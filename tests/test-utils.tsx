import { RenderOptions, RenderResult, render as rtlRender } from '@testing-library/react';
import * as React from 'react';
import Dispatcher from '../src/dispatcher/Dispatcher';

/**
 * Renders a component with a real Dispatcher instance
 *
 * @param Component - React component that expects a dispatcher prop
 * @param props - Props to pass to the component (excluding dispatcher)
 * @param options - Additional render options for RTL
 * @returns React Testing Library render result
 *
 * @example
 * const { getByTestId } = renderWithDispatcher(
 *   CardComponent,
 *   { cardId: '456' },
 *   { wrapper: ThemeProvider }
 * );
 * expect(getByTestId('card-456')).toBeInTheDocument();
 */
export type RenderWithDispatcherResult<P> = RenderResult & {
  rerenderWithDispatcher: (newProps: Partial<Omit<P, 'dispatcher'>>) => void;
  dispatcher: Dispatcher;
};
export function renderWithDispatcher<P extends { dispatcher: Dispatcher }>(
  Component: React.ComponentType<P>,
  props: Omit<P, 'dispatcher'> = {} as Omit<P, 'dispatcher'>,
  options: RenderOptions = {},
): RenderWithDispatcherResult<P> {
  // Create a new dispatcher for each render
  const dispatcher = new Dispatcher();

  // Render the component with the dispatcher
  const { rerender, ...restRender } = rtlRender(<Component {...(props as P)} dispatcher={dispatcher} />, options);
  const rerenderWithDispatcher: RenderWithDispatcherResult<P>['rerenderWithDispatcher'] = (newProps = {}) => {
    rerender(<Component {...(props as P)} {...newProps} dispatcher={dispatcher} />);
  };

  return {
    ...restRender,
    rerender,
    rerenderWithDispatcher,
    dispatcher,
  };
}
