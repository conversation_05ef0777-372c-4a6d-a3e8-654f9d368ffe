import { faker } from '@faker-js/faker';
import { CardSet } from '../../../../src/models/CardSets';
import { CardSetFilter } from '../../../../src/models/filters/CardSetFilter';
import { Game } from '../../../../src/models/Game';
import { LorcanaSet } from '../../../../src/models/lorcana/LorcanaSet';
import { PokeSet } from '../../../../src/models/pokemon/PokeSet';
import { YugiSet } from '../../../../src/models/yugioh/YugiSet';
import { CardSetFilterSuggestion } from '../../../../src/views/components/react-select/CardSetFilterSuggestion';
import { IFake } from '../../../fake/FakeInterface';

// Helper function to create a mock card set
export class FakeCardSet implements IFake<CardSet | PokeSet | YugiSet | LorcanaSet> {
  fake(options?: Record<string, any>) {
    const game = options?.game || Game.MTG;
    const name = faker.commerce.productName();
    const setCode = faker.string.alphanumeric(3).toUpperCase();
    const uuid = faker.string.uuid();

    let cardSet;
    switch (game) {
      case Game.POKEMON:
        cardSet = new PokeSet({
          name,
          setCode,
          uuid,
        });
        break;
      case Game.YUGIOH:
        cardSet = new YugiSet({
          name,
          setCode,
          uuid,
        });
        break;
      case Game.LORCANA:
        cardSet = new LorcanaSet({
          name,
          setCode,
          uuid,
        });
        break;
      default:
        cardSet = new CardSet({
          name,
          setCode,
        });
        break;
    }

    return cardSet;
  }
}

// Helper function to create a Card Set Suggestion
export const createCardSetSuggestion = (include = true, game = Game.MTG) => {
  const cardSet = new FakeCardSet().fake({ game });
  const cardSetFilter = new CardSetFilter({
    include,
    cardSet,
  });

  return {
    cardSet,
    cardSetFilter,
    suggestion: new CardSetFilterSuggestion(cardSetFilter),
  };
};
