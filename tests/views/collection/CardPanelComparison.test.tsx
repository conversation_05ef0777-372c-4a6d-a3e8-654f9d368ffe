/* eslint-disable @typescript-eslint/no-namespace*/
import { faker } from '@faker-js/faker';
import { screen, within } from '@testing-library/react';
import * as Immutable from 'immutable';
import { CardInstance } from '../../../src/models/CardInstances';
import { CardPanel as CardPanelModel, CardPanelType } from '../../../src/models/CardPanels';
import { MTGCardGroup } from '../../../src/models/mtg/MTGCardPage';
import { CardPanel } from '../../../src/views/components/cardpanel/CardPanel';
import {
  create,
  createArray,
  FakeCardInstance,
  FakeCardPanel,
  FakeMTGCardGroup,
  FakeMTGCardPage,
  FakeTag,
  FakeUser,
} from '../../fake/Fake';
import { renderWithDispatcher } from '../../test-utils';

// CPC = CardPanelContent
// CPEC = CardPanelEditorCollection
// CPEB = CardPanelEditorBuilder

//
// SETUP
//

const y = faker.number.int({ min: 1 });
const myCardPage = create(FakeMTGCardPage);

const defaultProps = {
  me: create(FakeUser),
  usernamePublic: faker.datatype.boolean(),
  cardPage: myCardPage,
  cardPanelRow: y,
  isGrid: faker.datatype.boolean(),
  userTags: Immutable.OrderedSet([create(FakeTag)]),
};

function generateCardPanel() {
  return create(FakeCardPanel).set('y', y);
}

function renderCardPanels(cardPanel: CardPanelModel) {
  Object.keys(CardPanelType).map((key: string) => {
    renderWithDispatcher(CardPanel, {
      ...defaultProps,
      cardPanel: cardPanel.set('cardPanelType', CardPanelType[key as keyof typeof CardPanelType]),
      isMultiple: false,
    });
  });
}

//
// SHARED TESTS
//

// This is why we have to disable @typescript-eslint/no-namespace for this file.
interface CustomMatchers<R = unknown> {
  cardNamesLabelAreVisible(): R;
  labelDisplayed(labelText: string): R;
  labelConsistent(): R;
  priceLabel(): R;
  titleHasMultiplier(name: string): R;
}

declare global {
  namespace jest {
    interface Expect extends CustomMatchers {}
    interface Matchers<R> extends CustomMatchers<R> {}
    interface InverseAsymmetricMatchers extends CustomMatchers {}
  }
}

expect.extend({
  cardNamesLabelAreVisible() {
    if (this.isNot) {
      expect(screen.queryByTestId('cpc-card-names')).toBeNull();
      expect(screen.queryByTestId('cpec-card-names')).toBeNull();
      expect(screen.queryByTestId('cpeb-card-names')).toBeNull();
    } else {
      expect(screen.queryByTestId('cpc-card-names')).not.toBeNull();
      expect(screen.queryByTestId('cpec-card-names')).not.toBeNull();
      expect(screen.queryByTestId('cpeb-card-names')).not.toBeNull();
    }
    return { message: () => '', pass: !this.isNot };
  },

  labelDisplayed(recieved: string, labelText: string) {
    if (this.isNot) {
      expect(screen.getByTestId(recieved).textContent).not.toBe(labelText);
    } else {
      expect(screen.getByTestId(recieved).textContent).toBe(labelText);
    }
    return { message: () => '', pass: !this.isNot };
  },

  priceLabel(recieved: string) {
    if (this.isNot) {
      expect(within(screen.getByTestId('cpc-price-parent')).queryByText(recieved)).not.toEqual(expect.anything());
      expect(within(screen.getByTestId('cpec-price-parent')).queryByText(recieved)).not.toEqual(expect.anything());
      expect(within(screen.getByTestId('cpeb-price-parent')).queryByText(recieved)).not.toEqual(expect.anything());
    } else {
      expect(within(screen.getByTestId('cpc-price-parent')).queryByText(recieved)).toEqual(expect.anything());
      expect(within(screen.getByTestId('cpec-price-parent')).queryByText(recieved)).toEqual(expect.anything());
      expect(within(screen.getByTestId('cpeb-price-parent')).queryByText(recieved)).toEqual(expect.anything());
    }
    return { message: () => '', pass: !this.isNot };
  },

  labelConsistent(recieved: string) {
    if (this.isNot) {
      expect(screen.getByTestId(`cpc-${recieved}`).textContent).not.toBe(
        screen.getByTestId(`cpec-${recieved}`).textContent,
      );
      expect(screen.getByTestId(`cpc-${recieved}`).textContent).not.toBe(
        screen.getByTestId(`cpeb-${recieved}`).textContent,
      );
    } else {
      expect(screen.getByTestId(`cpc-${recieved}`).textContent).toBe(
        screen.getByTestId(`cpec-${recieved}`).textContent,
      );
      expect(screen.getByTestId(`cpc-${recieved}`).textContent).toBe(
        screen.getByTestId(`cpeb-${recieved}`).textContent,
      );
    }
    return { message: () => '', pass: !this.isNot };
  },

  dateAddedConsistent(recieved: any) {
    if (this.isNot) {
      expect(screen.getByTestId('cpc-time-parent').textContent).not.toEqual(
        screen.getByTestId('cpec-time-parent').textContent,
      );
    } else {
      expect(screen.getByTestId('cpc-time-parent').textContent).toEqual(
        screen.getByTestId('cpec-time-parent').textContent,
      );
    }
    return { message: () => '', pass: !this.isNot };
  },

  titleHasQuantity(recieved: number) {
    if (this.isNot) {
      screen.getAllByTestId('cardpanel-title').forEach((value: HTMLElement) => {
        expect(value.textContent).not.toBe(`Viewing ${recieved} Cards`);
      });
    } else {
      screen.getAllByTestId('cardpanel-title').forEach((value: HTMLElement) => {
        expect(value.textContent).toBe(`Viewing ${recieved} Cards`);
      });
    }
    return { message: () => '', pass: !this.isNot };
  },

  titleHasMultiplier(recieved: number, name: string) {
    if (this.isNot) {
      screen.getAllByTestId('cardpanel-title').forEach((value: HTMLElement) => {
        expect(value.textContent).not.toBe(`${recieved}x ${name}`);
      });
    } else {
      screen.getAllByTestId('cardpanel-title').forEach((value: HTMLElement) => {
        expect(value.textContent).toBe(`${recieved}x ${name}`);
      });
    }
    return { message: () => '', pass: !this.isNot };
  },
});

//
// TEST LOGIC
//

describe('With a single card', () => {
  let cardGroup: MTGCardGroup;
  let cardInstance: CardInstance;

  beforeEach(() => {
    cardGroup = create(FakeMTGCardGroup);
    cardInstance = cardGroup.getInstances().first();
    const cardPanel = generateCardPanel().set('cardGroup', cardGroup);
    renderCardPanels(cardPanel);
  });

  // Unique
  test('Card Name is displayed', () => {
    screen.getAllByTestId('cardpanel-title').forEach((value: HTMLElement) => {
      expect(value.textContent).toBe(cardInstance.get('cardName'));
    });
  });
  test('Card Names label is not displayed', () => {
    expect(null).not.cardNamesLabelAreVisible();
  });
  test('Set label is displayed', () => {
    expect('cpc-set-sets').labelDisplayed('Set');
  });
  test('Collector Number is displayed', () => {
    expect('cpc-collector').labelDisplayed('Collector Number');
  });
  test('Price label is displayed', () => {
    expect('Price').priceLabel();
  });
  test('Price section is the same', () => {
    expect('price-parent').labelConsistent();
  });
  test('Date Added section is the same', () => {
    expect(null).dateAddedConsistent();
  });
});

describe('With multiple of the same card', () => {
  let cardGroup: MTGCardGroup;
  let cardInstance: CardInstance;

  beforeEach(() => {
    cardGroup = create(FakeMTGCardGroup);
    cardInstance = cardGroup.getInstances().first();
    const cardPanel = generateCardPanel().set(
      'cardGroup',
      cardGroup.set(
        'cardInstances',
        Immutable.List([
          cardInstance,
          cardInstance.set('id', faker.number.int({ min: 0 })),
          cardInstance.set('id', faker.number.int({ min: 0 })),
          cardInstance.set('id', faker.number.int({ min: 0 })),
          cardInstance.set('id', faker.number.int({ min: 0 })),
        ]),
      ),
    );
    renderCardPanels(cardPanel);
  });

  test('Card Name formatted as "5x [name]"', () => {
    expect(5).titleHasMultiplier(cardInstance.get('cardName'));
  });
  test('Card Names label is not displayed', () => {
    expect(null).not.cardNamesLabelAreVisible();
  });
  test('Set label is displayed', () => {
    expect('cpc-set-sets').labelDisplayed('Set');
  });
  test('Collector Number is displayed', () => {
    expect('cpc-collector').labelDisplayed('Collector Number');
  });
  test('Total Price label is displayed', () => {
    expect('Total Price').priceLabel();
  });
  test('Price section is the same', () => {
    expect('price-parent').labelConsistent();
  });
  test('Date Added section is the same', () => {
    expect(null).dateAddedConsistent();
  });
});

describe('With cards of different collector numbers', () => {
  const name = faker.string.sample();
  const setName = faker.string.sample();
  const setCode = faker.string.sample();

  beforeEach(() => {
    let cardGroup = create(FakeMTGCardGroup);
    cardGroup = cardGroup.set(
      'cardInstances',
      Immutable.List<CardInstance>(
        createArray(FakeCardInstance, 5).map((c: CardInstance) => {
          return c.set('cardName', name).set('cardSetName', setName).set('cardSetCode', setCode);
        }),
      ),
    );
    const cardPanel = generateCardPanel().set('cardGroup', cardGroup);
    renderCardPanels(cardPanel);
  });

  test('Card Name formatted as "5x [name]"', () => {
    expect(5).titleHasMultiplier(name);
  });
  test('Card Names label is not displayed', () => {
    expect(null).not.cardNamesLabelAreVisible();
  });
  test('Set label is displayed', () => {
    expect('cpc-set-sets').labelDisplayed('Set');
  });
  test('Collector Numbers is displayed', () => {
    expect('cpc-collector').labelDisplayed('Collector Numbers');
  });
  test('Total Price label is displayed', () => {
    expect('Total Price').priceLabel();
  });
  test('Price section is the same', () => {
    expect('price-parent').labelConsistent();
  });
  test('Date Added section is the same', () => {
    expect(null).dateAddedConsistent();
  });
});

describe('With different cards with the same name and different sets', () => {
  let sharedName: string;
  beforeEach(() => {
    // Limitation: Metadata (e.g colour, manaValue) will likely not be the same across generated cards.
    sharedName = faker.string.sample();
    let cardGroup = create(FakeMTGCardGroup);
    cardGroup = cardGroup.set(
      'cardInstances',
      Immutable.List<CardInstance>(
        createArray(FakeCardInstance, 5).map((c: CardInstance) => {
          return c.set('cardName', sharedName);
        }),
      ),
    );
    const cardPanel = generateCardPanel().set('cardGroup', cardGroup);
    renderCardPanels(cardPanel);
  });

  test('Card Name formatted as "5x [name]"', () => {
    expect(5).titleHasMultiplier(sharedName);
  });
  test('Card Names label is not displayed', () => {
    expect(null).not.cardNamesLabelAreVisible();
  });
  test('Sets label is displayed', () => {
    expect('cpc-set-sets').labelDisplayed('Sets');
  });
  test('Collector Numbers is displayed', () => {
    expect('cpc-collector').labelDisplayed('Collector Numbers');
  });
  test('Total Price label is displayed', () => {
    expect('Total Price').priceLabel();
  });
  test('Price section is the same', () => {
    expect('price-parent').labelConsistent();
  });
  test('Date Added section is the same', () => {
    expect(null).dateAddedConsistent();
  });
});

describe('With different cards with the same set', () => {
  beforeEach(() => {
    const setName = faker.string.sample();
    const setCode = faker.string.sample();
    let cardGroup = create(FakeMTGCardGroup);
    cardGroup = cardGroup.set(
      'cardInstances',
      Immutable.List<CardInstance>(
        createArray(FakeCardInstance, 5).map((c: CardInstance) => {
          return c.set('cardSetName', setName).set('cardSetCode', setCode);
        }),
      ),
    );
    const cardPanel = generateCardPanel().set('cardGroup', cardGroup);
    renderCardPanels(cardPanel);
  });

  test('Card Name formatted as "Viewing 5 Cards"', () => {
    expect(5).titleHasQuantity();
  });
  test('Card Names label is displayed', () => {
    expect(null).cardNamesLabelAreVisible();
  });
  test('Card Names are the same', () => {
    expect('card-names').labelConsistent();
  });
  test('Set label is displayed', () => {
    expect('cpc-set-sets').labelDisplayed('Set');
  });
  test('Total Price label is displayed', () => {
    expect('Total Price').priceLabel();
  });
  test('Price section is the same', () => {
    expect('price-parent').labelConsistent();
  });
  test('Date Added section is the same', () => {
    expect(null).dateAddedConsistent();
  });
});

describe('With completely different cards', () => {
  beforeEach(() => {
    let cardGroup = create(FakeMTGCardGroup);
    cardGroup = cardGroup.set('cardInstances', Immutable.List<CardInstance>(createArray(FakeCardInstance, 5)));
    const cardPanel = generateCardPanel().set('cardGroup', cardGroup);
    renderCardPanels(cardPanel);
  });

  test('Card Name formatted as "Viewing 5 Cards"', () => {
    expect(5).titleHasQuantity();
  });
  test('Card Names label is displayed', () => {
    expect(null).cardNamesLabelAreVisible();
  });
  test('Card Names are the same', () => {
    expect('card-names').labelConsistent();
  });
  test('Sets label is displayed', () => {
    expect('cpc-set-sets').labelDisplayed('Sets');
  });
  test('Total Price label is displayed', () => {
    expect('Total Price').priceLabel();
  });
  test('Price section is the same', () => {
    expect('price-parent').labelConsistent();
  });
  test('Date Added section is the same', () => {
    expect(null).dateAddedConsistent();
  });
});
