'use strict';

const gulp = require('gulp');
const typescript = require('gulp-typescript');
const sass = require('gulp-sass')(require('sass'));
const nodemon = require('gulp-nodemon');
const cleancss = require('gulp-clean-css');

const sourcemaps = require('gulp-sourcemaps');
const uglifyes = require('uglify-es');
const browserify = require('browserify');
const del = require('del');
const source = require('vinyl-source-stream');
const buffer = require('vinyl-buffer');
const composer = require('gulp-uglify/composer');

const minify = composer(uglifyes, console);

if (process.env.NODE_ENV != 'production') {
  // Dotenv must be loaded here to apply NODE_OPTIONS to nodemon
  require('dotenv').config();
}

/*
 * Deletes the distribution folder.
 */
gulp.task('clean', function () {
  return del(['./build/*']);
});

/*
 * Compiles all typescript code into the distribution folder so that it can be
 * run by node. All typescript annotations are stripped. Source maps are not
 * generated.
 */
gulp.task('build:ts', function () {
  const project = typescript.createProject('./tsconfig.build.json');
  return project.src().pipe(project()).pipe(gulp.dest('./build'));
});

/*
 * Compiles all typescript code into the distribution folder so that it can be
 * run by node. All typescript annotations are stripped. Source maps are
 * embedded inline.
 */
gulp.task('build:ts:production', function () {
  const project = typescript.createProject('./tsconfig.build.json');
  return project
    .src()
    .pipe(sourcemaps.init())
    .pipe(project())
    .pipe(sourcemaps.write())
    .pipe(gulp.dest('./build'));
});

/*
 * Compiles all JavaScript code into the distribution folder as a single
 * Javascript file that can be loaded by the client. This must be done after
 * compiling typescript. The source maps are not generated.
 */
gulp.task('build:react', function () {
  return browserify({
    entries: ['./build/views/index.js'],
    extensions: ['.js'],
    paths: ['./src'],
    debug: true,
  })
    .bundle()
    .pipe(source('index.js'))
    .pipe(buffer())
    .pipe(gulp.dest('./build/public/js'));
});

/*
 * Compiles all JavaScript code into the distribution folder as a single
 * Javascript file that can be loaded by the client. This must be done after
 * compiling typescript. The source maps are created using inline source maps
 * built during TypeScript compilation.
 */
gulp.task('build:react:production', function () {
  return browserify({
    entries: ['./build/views/index.js'],
    extensions: ['.js'],
    paths: ['./src'],
    debug: true,
  })
    .bundle()
    .pipe(source('index.js'))
    .pipe(buffer())
    .pipe(sourcemaps.init({ loadMaps: true }))
    .pipe(minify({}))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest('./build/public/js'));
});

/*
 * Compiles all sass into the distribution folder. The generated CSS is not
 * minified.
 */
gulp.task('build:scss', async function () {
  const prefixcss = (await import('gulp-autoprefixer')).default;
  return gulp
    .src('./assets/scss/main.scss')
    .pipe(sass())
    .pipe(prefixcss())
    .pipe(gulp.dest('./build/public/css'));
});

/*
 * Compiles all sass into the distribution folder. The generated CSS is
 * minified.
 */
gulp.task('build:scss:production', async function () {
  const prefixcss = (await import('gulp-autoprefixer')).default;
  return gulp
    .src('./assets/scss/main.scss')
    .pipe(sass())
    .pipe(prefixcss())
    .pipe(cleancss())
    .pipe(gulp.dest('./build/public/css'));
});

/*
 * Compiles all EJS templates into the distribution folder.
 */
gulp.task('build:ejs', function () {
  return gulp.src(['./src/**/*.ejs']).pipe(gulp.dest('./build'));
});

/*
 * Compiles all assets into the distribution folder. All Sass files are
 * ignored.
 */
gulp.task('build:assets', function () {
  return gulp
    .src(['./assets/**/*', '!./assets/**/*.scss'], {
      encoding: false,
    })
    .pipe(gulp.dest('./build/public'));
});

/*
 * Runs all build tasks.
 */
gulp.task(
  'build:development',
  gulp.series(
    'build:ts',
    gulp.parallel('build:react', 'build:scss', 'build:ejs', 'build:assets'),
  ),
);

/*
 * Runs all build tasks.
 */
gulp.task(
  'build:production',
  gulp.series(
    'clean',
    'build:ts:production',
    gulp.parallel(
      'build:react:production',
      'build:scss:production',
      'build:ejs',
      'build:assets',
    ),
  ),
);

/*
 * Run the server in development mode.
 */
gulp.task('serve', async function (_done) {
  return nodemon({
    script: './build/index.js',
    watch: false,
    tasks: ['build:development'],
  });
});

gulp.task('default', gulp.series('build:development', 'serve'));
