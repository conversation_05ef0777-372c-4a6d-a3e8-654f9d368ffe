import { coverageConfigDefaults, defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    clearMocks: true,
    globals: true,
    include: ['./tests/**/*.{test,spec}.{js,ts,jsx,tsx}', './src/**/*.{test,spec}.{js,ts,jsx,tsx}'],
    exclude: ['./tests/setup.ts'],
    environment: 'jsdom',
    setupFiles: './tests/setup.ts',
    coverage: {
      enabled: true,
      exclude: [...coverageConfigDefaults.exclude, '**/build/*', '**/*.config.ts', './gulpfile.js', '**/assets/*'],
      reporter: ['html', 'text-summary', 'cobertura'],
      reportsDirectory: './coverage',
    },
  },
});
