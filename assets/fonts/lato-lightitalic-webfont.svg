<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="latolight_italic" horiz-adv-x="1187" >
<font-face units-per-em="2048" ascent="1649" descent="-399" />
<missing-glyph horiz-adv-x="516" />
<glyph unicode="&#xfb01;" horiz-adv-x="1022" d="M93 946l4 41h159l11 87q11 94 46 169t90.5 126.5t129.5 79.5t164 28q15 0 34.5 -1.5t37.5 -4.5t34.5 -7t27.5 -9l-9 -47q-2 -8 -9.5 -9.5t-21 0t-33 4t-46.5 2.5q-150 0 -241 -81.5t-112 -251.5l-12 -85h553l-122 -987h-96l113 914h-453l-111 -894l-43 -235 q-12 -42 -50 -42h-35l144 1169l-129 10q-27 3 -25 24z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1067" d="M93 946l4 41h159l8 68q11 90 45 166.5t88.5 132.5t128.5 88t165 32q71 0 136.5 -7t123.5 -7h54l-180 -1460h-95l169 1389q-48 2 -101.5 7.5t-102.5 5.5q-71 0 -128.5 -24t-101 -69t-71.5 -109.5t-38 -144.5l-8 -68h269l-8 -72h-267l-111 -895l-43 -235q-12 -42 -50 -42 h-35l144 1169l-129 10q-27 3 -25 24z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1595" d="M93 949l5 38h158l14 116q11 87 41 153t73.5 110t100 66t119.5 22q29 0 57.5 -5t51.5 -16l-11 -49q-3 -8 -9.5 -9.5t-18 -0.5t-28 3.5t-38.5 2.5q-46 0 -86.5 -14t-72.5 -46.5t-54.5 -84.5t-32.5 -129l-16 -119h483l12 87q11 94 46 169t90 126.5t129.5 79.5t164.5 28 q15 0 34 -1.5t37.5 -4.5t35 -7t27.5 -9l-9 -47q-2 -8 -10 -9.5t-21 0t-32.5 4t-47.5 2.5q-150 0 -241 -81.5t-111 -251.5l-12 -85h553l-9 -71h-1l-111 -916h-95l112 916h-455l-110 -896l-43 -235q-12 -42 -50 -42h-35l144 1173h-480l-109 -894l-43 -236q-12 -42 -50 -42h-35 l143 1170l-128 10q-27 3 -26 25z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1641" d="M93 948l5 38h157l14 117q11 87 40.5 153t73.5 110t100 66t120 22q29 0 57.5 -5t51.5 -16l-11 -49q-3 -8 -9.5 -9.5t-18 -0.5t-28 3.5t-38.5 2.5q-46 0 -86.5 -14t-72.5 -46.5t-54.5 -84.5t-32.5 -129l-16 -120h485l9 69q11 90 45 166.5t88 132.5t128.5 88t165.5 32 q71 0 136 -7t124 -7h53l-179 -1460h-95l169 1389q-48 2 -101.5 7.5t-102.5 5.5q-71 0 -129 -24t-101.5 -69t-71.5 -109.5t-37 -144.5l-8 -69h303l-9 -71h-301l-110 -895l-43 -235q-12 -42 -50 -42h-35l143 1172h-481l-109 -893l-43 -236q-12 -42 -50 -42h-35l143 1170 l-127 9q-28 3 -26 25z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="516" />
<glyph unicode="&#x09;" horiz-adv-x="516" />
<glyph unicode="&#xa0;" horiz-adv-x="516" />
<glyph unicode="!" horiz-adv-x="485" d="M91 79q0 19 7 36.5t20 30.5t30 20.5t37 7.5q19 0 37 -7.5t30.5 -20.5t20.5 -30.5t8 -36.5q0 -20 -8 -37t-20.5 -30t-30 -20t-37.5 -7q-40 0 -67 27t-27 67zM191 499q4 57 7.5 104t7 89.5t7.5 83.5t9 87l72 586h93l-72 -586q-6 -46 -12 -87t-13 -83.5t-16 -89.5t-19 -104 h-64z" />
<glyph unicode="&#x22;" horiz-adv-x="652" d="M202 992l10 158l39 299h80l-37 -299l-27 -158q-4 -16 -11.5 -25.5t-25.5 -9.5q-14 0 -20 9.5t-8 25.5zM475 992l10 158l39 299h80l-37 -299l-27 -158q-4 -16 -11.5 -25.5t-25.5 -9.5q-14 0 -20 9.5t-8 25.5z" />
<glyph unicode="#" d="M71 495l7 29h224l135 401h-260l9 38q7 32 50 31h220l140 416q12 39 50 39h42l-153 -455h307l152 455h42q16 0 24.5 -11.5t1.5 -30.5l-140 -413h230l-11 -37q-7 -33 -47 -32h-191l-135 -401h232q16 0 23.5 -9t2.5 -32l-8 -29h-269l-151 -454h-41q-16 0 -24.5 12t-0.5 32 l138 410h-308l-138 -414q-6 -22 -20 -31t-31 -9h-42l152 454h-188q-35 0 -24 41zM382 524h307l134 401h-306z" />
<glyph unicode="$" d="M81 173l32 39q14 14 30 14q9 0 21 -11t29.5 -28t41.5 -37.5t57.5 -38.5t77 -30.5t100.5 -15.5l114 656q-61 20 -119.5 45t-104.5 61.5t-73.5 89.5t-27.5 131q0 80 31.5 154t89.5 131.5t142 92.5t190 38l29 170q2 14 11.5 23t22.5 9h36l-35 -203q102 -8 175 -45.5 t129 -103.5l-25 -34q-12 -16 -28 -16q-12 0 -30 16.5t-47.5 38t-75.5 40.5t-112 24l-102 -589q66 -23 129.5 -47.5t114 -61t82 -89.5t31.5 -133q0 -94 -35 -178.5t-99.5 -149.5t-156 -105t-205.5 -45l-38 -218q-2 -12 -11 -21.5t-22 -9.5h-36l43 249q-131 6 -222 56 t-154 132zM349 1068q0 -55 20 -94.5t55 -68.5t80 -50.5t96 -40.5l98 569q-85 -4 -150.5 -31.5t-109.5 -70.5t-66.5 -98.5t-22.5 -114.5zM535 65q91 5 163 36.5t122 82t77 117t27 139.5q0 59 -23 100.5t-61.5 71t-89.5 50.5t-106 39z" />
<glyph unicode="%" horiz-adv-x="1479" d="M131 0l1134 1431q8 8 16 13t23 5h66l-1136 -1432q-6 -8 -15 -12.5t-20 -4.5h-68zM154 1037q0 105 26.5 185.5t71.5 134t105 80.5t126 27q52 0 96.5 -19.5t76.5 -57t50.5 -93.5t18.5 -130q0 -105 -28 -185t-74 -133t-105.5 -80t-123.5 -27q-52 0 -96.5 19.5t-76.5 57 t-49.5 93.5t-17.5 128zM227 1037q0 -61 13.5 -105t37 -73t56 -42.5t68.5 -13.5q49 0 94 20.5t78.5 64.5t54 112t20.5 163q0 61 -13 106t-37 74t-55 43.5t-68 14.5q-49 0 -94 -21t-79.5 -65.5t-55 -113t-20.5 -164.5zM782 281q0 105 27 185.5t72 134t105.5 81t126.5 27.5 q52 0 96 -19.5t76.5 -58t51 -94t18.5 -129.5q0 -105 -28.5 -185t-74.5 -133t-105.5 -80t-122.5 -27q-52 0 -96.5 19.5t-77 57t-50.5 93t-18 128.5zM857 281q0 -60 13.5 -104.5t36.5 -73t55 -42.5t69 -14q49 0 93.5 20.5t79 64.5t55 112.5t20.5 163.5q0 61 -13.5 105.5 t-37 73.5t-55 43t-68.5 14q-49 0 -94 -21t-79.5 -65.5t-54.5 -113t-20 -163.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1317" d="M73 324q0 84 29.5 158t80.5 136t120 109t147 78q-49 72 -74 140t-25 139q0 82 28 152t78 121t119.5 79.5t153.5 28.5q65 0 117.5 -21.5t90.5 -59.5t58.5 -91.5t20.5 -116.5l-52 -10q-27 -5 -32 24q-2 30 -14.5 65t-37.5 65.5t-64 50.5t-94 20q-63 0 -115 -22.5t-89.5 -62 t-58 -93.5t-20.5 -117q0 -71 32 -143.5t93 -147.5l362 -445q50 75 83 153.5t48 151.5q5 26 27 25h63q-14 -96 -57.5 -198t-112.5 -195l242 -297h-79q-26 0 -39 6t-28 24l-158 194q-48 -52 -103 -96.5t-117.5 -76.5t-131 -49.5t-142.5 -17.5q-72 0 -135.5 23t-111 66.5 t-75 107t-27.5 143.5zM170 336q0 -68 23.5 -119.5t61.5 -86.5t86.5 -52.5t99.5 -17.5q63 0 123 17t113.5 47.5t101.5 71.5t89 89l-373 460l-8 10q-73 -31 -131 -75t-99.5 -98t-64 -116.5t-22.5 -129.5z" />
<glyph unicode="'" horiz-adv-x="377" d="M202 992l10 158l39 299h80l-37 -299l-27 -158q-4 -16 -11.5 -25.5t-25.5 -9.5q-14 0 -20 9.5t-8 25.5z" />
<glyph unicode="(" horiz-adv-x="508" d="M109 449q0 88 5 172.5t20.5 169.5t42 172.5t69.5 181.5t104.5 197t145.5 220l40 -26q7 -5 9.5 -15.5t-8.5 -27.5q-85 -122 -149 -238.5t-107 -239.5t-65 -258.5t-22 -295.5q0 -81 8 -167t25 -171.5t42 -168t59 -156.5q8 -17 2.5 -27.5t-15.5 -15.5l-44 -25 q-43 90 -74 183t-50.5 184.5t-28.5 180.5t-9 171z" />
<glyph unicode=")" horiz-adv-x="508" d="M-28.5 -229.5q-2.5 10.5 9.5 27.5q84 122 148.5 244t107.5 248.5t65 260t22 279.5q0 81 -9 167t-26.5 171.5t-42.5 168t-59 156.5q-7 17 -2 27.5t15 15.5l46 26q43 -91 73.5 -184t50 -184t29 -179.5t9.5 -171.5q0 -88 -5.5 -172.5t-20 -169.5t-42 -172.5t-70.5 -182 t-104.5 -197.5t-145.5 -219l-38 25q-8 5 -10.5 15.5z" />
<glyph unicode="*" horiz-adv-x="805" d="M197 1091l189 112q16 9 29.5 13.5t33.5 6.5v1q-19 3 -31 7t-27 13l-163 113l31 45l163 -113q14 -9 22.5 -19t19.5 -28q-5 20 -7 34.5t0 32.5l27 220h52l-28 -219q-2 -18 -6.5 -32t-15.5 -34q13 17 25 26.5t29 18.5l190 113l19 -45l-191 -113q-16 -8 -29.5 -13.5 t-31.5 -7.5q18 -2 30.5 -6.5t25.5 -13.5l165 -112l-31 -44l-163 112q-14 8 -22.5 16t-18.5 23q4 -17 5 -29.5t-1 -29.5l-26 -219h-51l26 219q2 17 6.5 30.5t15.5 30.5q-14 -15 -25 -24t-26 -17l-191 -112z" />
<glyph unicode="+" d="M119 650l8 73h440l58 462h77l-57 -462h444l-10 -73h-442l-57 -464h-78l57 464h-440z" />
<glyph unicode="," horiz-adv-x="434" d="M60 87q0 36 25 61.5t65 25.5q44 0 68 -30t24 -79q0 -42 -11 -86t-31.5 -85.5t-49 -80.5t-63.5 -72l-16 15q-5 4 -6.5 9t-1.5 10q0 8 16 27t36.5 48t39 67t23.5 84q-15 -5 -32 -5q-38 0 -62 25.5t-24 65.5z" />
<glyph unicode="-" horiz-adv-x="729" d="M137 567l11 80h439l-9 -80h-441z" />
<glyph unicode="&#xad;" horiz-adv-x="729" d="M137 567l11 80h439l-9 -80h-441z" />
<glyph unicode="." horiz-adv-x="470" d="M78 79q0 19 7 36.5t20 30.5t30 20.5t37 7.5q19 0 36.5 -7.5t30.5 -20.5t20.5 -30.5t7.5 -36.5q0 -20 -7.5 -37t-20.5 -30t-30.5 -20t-36.5 -7q-40 0 -67 27t-27 67z" />
<glyph unicode="/" horiz-adv-x="828" d="M-9 -86l769 1529q17 37 57 37h40l-768 -1528q-9 -19 -26 -28.5t-33 -9.5h-39z" />
<glyph unicode="0" d="M116 566q0 227 47.5 396.5t128 281t185.5 166.5t220 55q86 0 159.5 -36t127 -109t84 -182.5t30.5 -254.5q0 -228 -48 -397.5t-128 -280.5t-185.5 -165.5t-220.5 -54.5q-86 0 -159.5 36t-127 109t-83.5 182t-30 254zM213 562q0 -129 24 -223t66.5 -155t99.5 -90t122 -29 q91 0 176.5 47t152 146.5t106 255.5t39.5 374q0 129 -24 222.5t-66.5 154.5t-99 90.5t-122.5 29.5q-91 0 -176.5 -47t-151.5 -147t-106 -256t-40 -373z" />
<glyph unicode="1" d="M228 0l11 73h305l144 1182q2 17 5 35.5t7 36.5l-325 -285q-16 -11 -29.5 -8t-19.5 12l-24 39l431 370h75l-169 -1382h289l-9 -73h-691z" />
<glyph unicode="2" d="M83 0l5 34q1 11 7.5 22.5t16.5 19.5l515 502q71 70 130.5 132.5t103 127t68 133t24.5 149.5q0 68 -20.5 117.5t-55.5 82.5t-81.5 48.5t-99.5 15.5q-62 0 -117.5 -21t-100.5 -58t-77.5 -89.5t-51.5 -114.5q-8 -22 -21 -29t-32 -5l-48 7q24 92 67.5 165t104 123t134.5 76.5 t157 26.5q71 0 132.5 -21.5t107.5 -63.5t73 -105t27 -146q0 -94 -29 -171.5t-78 -147.5t-113.5 -136t-136.5 -135l-477 -466q33 8 68.5 12t69.5 4h568q16 0 24 -9t6 -25l-7 -55h-863z" />
<glyph unicode="3" d="M118 354l39 16q17 7 34 4t22 -24q4 -12 9 -37.5t16 -57.5t30.5 -65.5t52 -61.5t80.5 -45.5t116 -17.5q90 0 161 34t120 89t74.5 122.5t25.5 133.5q0 56 -18.5 104.5t-60 84.5t-108 56.5t-161.5 20.5l8 68q93 2 166 27.5t123 72t76.5 110.5t26.5 144q0 65 -20 112 t-54 78.5t-80 46.5t-99 15q-62 0 -117.5 -21t-101 -58t-78.5 -89.5t-52 -114.5q-9 -22 -20.5 -29t-31.5 -5l-47 7q24 92 67.5 165t104 123t134.5 76.5t156 26.5q71 0 131.5 -20.5t105.5 -60t70.5 -99t25.5 -137.5q0 -83 -24 -148t-66 -114t-98.5 -82t-122.5 -49 q65 -13 113.5 -41t80.5 -68t48.5 -90.5t16.5 -110.5q0 -99 -39 -183.5t-105 -146.5t-153.5 -96.5t-185.5 -34.5q-114 0 -186 33t-114.5 86.5t-62 119.5t-28.5 131z" />
<glyph unicode="4" d="M71 487l788 963h84l-118 -957h242l-6 -52q-2 -11 -8.5 -17.5t-20.5 -6.5h-216l-51 -417h-83l51 417h-629q-14 0 -21 6.5t-8 17.5zM173 493h569l94 763q3 33 12 68z" />
<glyph unicode="5" d="M117 112l33 39q12 15 30 15q12 0 33 -15.5t55 -34.5t81.5 -34t113.5 -15q86 0 161.5 31.5t132 91.5t89.5 145t33 192q0 66 -18.5 121t-56 95t-95.5 62.5t-136 22.5q-53 0 -114.5 -9.5t-130.5 -30.5l-58 20l183 641h602l-5 -41q-2 -20 -17 -33t-45 -13h-477l-139 -486 q127 32 229 31q93 0 164 -28t117.5 -77.5t70 -118t23.5 -147.5q0 -128 -43.5 -230.5t-117 -174.5t-168.5 -109.5t-198 -37.5q-55 0 -104.5 10.5t-91.5 28.5t-76.5 41t-59.5 48z" />
<glyph unicode="6" horiz-adv-x="1092" d="M147 386q0 60 13.5 118.5t41 120t70.5 127t103 140.5l428 533q8 11 22.5 17.5t30.5 6.5h83l-466 -565q-33 -39 -60.5 -75t-51.5 -69q61 58 140.5 91.5t166.5 33.5q78 0 141.5 -26.5t109.5 -75t71 -117t25 -151.5q0 -112 -38.5 -205.5t-105.5 -162t-158.5 -106 t-198.5 -37.5q-85 0 -152.5 27.5t-115 79.5t-73.5 127t-26 168zM238 382q0 -70 18.5 -128t55 -100.5t89.5 -67t121 -24.5q89 0 162.5 32t125.5 88.5t81 133t29 166.5q0 71 -20 128.5t-57.5 97.5t-91 62t-118.5 22q-89 0 -162 -35t-124.5 -93t-80 -131.5t-28.5 -150.5z" />
<glyph unicode="7" d="M229 0l773 1314q9 14 18 26t19 22h-764q-11 0 -17.5 7.5t-5.5 19.5l7 60h889l-5 -43q-2 -17 -8 -29.5t-11 -20.5l-770 -1320q-9 -15 -24 -25.5t-36 -10.5h-65z" />
<glyph unicode="8" d="M127 333q0 92 25.5 163.5t71 124t108.5 86t138 50.5q-102 34 -155 111.5t-53 182.5q0 86 32 161.5t89.5 131.5t137.5 88.5t176 32.5q82 0 146 -25.5t108.5 -69.5t67.5 -104t23 -127q0 -70 -19 -131.5t-56.5 -111t-91 -85.5t-122.5 -55q56 -14 102 -41.5t79 -67 t51.5 -92.5t18.5 -120q0 -104 -37 -188t-102.5 -142.5t-154.5 -89.5t-194 -31q-88 0 -160 24.5t-123 70t-78.5 110t-27.5 144.5zM224 341q0 -63 21 -115t60.5 -89t94.5 -57.5t125 -20.5q83 0 153 27t120.5 76t79 118t28.5 155q0 81 -27.5 135t-71.5 86t-97.5 46t-104.5 14 q-40 0 -83.5 -7.5t-86 -24.5t-81 -45.5t-67 -70.5t-46 -98t-17.5 -129zM355 1052q0 -51 15 -98t47 -83t81 -57.5t116 -21.5q83 0 145.5 28t104.5 75.5t63.5 109.5t21.5 129q0 54 -17 101t-49.5 82t-81 55t-112.5 20q-78 0 -140 -26.5t-105 -72.5t-66 -107.5t-23 -133.5z" />
<glyph unicode="9" d="M215 974q0 105 38 194.5t103.5 156t153.5 103.5t188 37q81 0 146 -28.5t111 -79.5t71.5 -123t25.5 -159q0 -70 -14.5 -129t-42.5 -116.5t-69 -117.5t-95 -130l-416 -559q-8 -11 -22 -17t-29 -6h-86l468 599q33 43 62 80.5t52 73.5q-62 -65 -144 -100.5t-169 -35.5 q-74 0 -135 26t-105 72.5t-68 112.5t-24 146zM311 983q0 -68 19 -121.5t54.5 -91.5t86 -58.5t112.5 -20.5q86 0 156 34t119 88t76.5 124t27.5 141q0 68 -19.5 125t-56 97t-86.5 63t-112 23q-81 0 -149.5 -29t-119 -82t-79.5 -127.5t-29 -164.5z" />
<glyph unicode=":" horiz-adv-x="484" d="M85 79q0 19 7 36.5t20 30.5t30 20.5t37 7.5q19 0 36.5 -7.5t30.5 -20.5t20.5 -30.5t7.5 -36.5q0 -20 -7.5 -37t-20.5 -30t-30.5 -20t-36.5 -7q-40 0 -67 27t-27 67zM177 895q0 19 7 36.5t20 30.5t30 20.5t37 7.5q19 0 36.5 -7.5t30.5 -20.5t20.5 -30.5t7.5 -36.5 q0 -20 -7.5 -37t-20.5 -30t-30.5 -20t-36.5 -7q-40 0 -67 27t-27 67z" />
<glyph unicode=";" horiz-adv-x="504" d="M100 87q0 36 25 61.5t65 25.5q44 0 68 -30t24 -79q0 -42 -11 -86t-31.5 -85.5t-49 -80.5t-63.5 -72l-16 15q-5 4 -6.5 9t-1.5 10q0 8 16 27t36.5 48t39 67t23.5 84q-15 -5 -32 -5q-38 0 -62 25.5t-24 65.5zM187 895q0 19 7 36.5t20 30.5t30 20.5t37 7.5q19 0 36.5 -7.5 t30.5 -20.5t20.5 -30.5t7.5 -36.5q0 -20 -7.5 -37t-20.5 -30t-30.5 -20t-36.5 -7q-40 0 -67 27t-27 67z" />
<glyph unicode="&#x3c;" d="M185 667l3 31h1l1 11l817 394l-9 -67q-1 -22 -31 -36l-602 -286q-32 -15 -76 -26q38 -9 69 -26l532 -288q26 -12 23 -36l-9 -67z" />
<glyph unicode="=" d="M152 496l9 72h846l-9 -72h-846zM191 815l9 73h846l-9 -73h-846z" />
<glyph unicode="&#x3e;" d="M196 271l8 67q2 11 8.5 20t23.5 16l602 288q34 16 76 25q-38 9 -70 27l-532 286q-26 14 -22 36l8 67l720 -394l-4 -33l-1 -9z" />
<glyph unicode="?" horiz-adv-x="827" d="M171 1321q30 28 65 54t76.5 46t89 32t100.5 12q62 0 115 -19.5t91 -55t60 -85.5t22 -110q0 -85 -25.5 -145.5t-63.5 -105.5t-84.5 -78t-88 -64t-72 -62.5t-38.5 -72.5l-33 -168h-66l18 174q4 45 32 80t68.5 67t86.5 64t85 73t65 94.5t26 127.5q0 48 -16 86.5t-45.5 65.5 t-69 41t-84.5 14q-63 0 -110 -17.5t-80 -39t-53.5 -39.5t-30.5 -18q-6 0 -10 2t-10 10zM233 79q0 19 7.5 36.5t20 30.5t29.5 20.5t38 7.5q19 0 37 -7.5t31 -20.5t20.5 -30.5t7.5 -36.5q0 -40 -28 -67t-68 -27t-67.5 27t-27.5 67z" />
<glyph unicode="@" horiz-adv-x="1564" d="M104 444q0 132 31 251.5t85 221.5t129.5 184.5t165.5 140t191 89t207 31.5q115 0 218 -37.5t181 -110.5t123 -180.5t45 -248.5q0 -132 -35 -241t-93 -187t-133 -121t-157 -43t-118.5 47.5t-28.5 136.5q-65 -95 -136 -137.5t-149 -42.5q-46 0 -80 16t-55.5 45.5t-32 68.5 t-10.5 84q0 60 16.5 123.5t48.5 123t78 111.5t104 91t127.5 61.5t149.5 22.5q50 0 93 -8.5t77 -26.5l-130 -355q-34 -95 -40 -153.5t7 -90.5t40 -43t59 -11q60 0 119.5 37.5t105.5 106.5t74 167t28 218q0 127 -39 223.5t-106.5 160t-159.5 95.5t-198 32q-94 0 -184.5 -28 t-172 -80t-149.5 -127t-118 -169t-78 -205.5t-28 -235.5q0 -152 44 -268.5t122.5 -196.5t184.5 -121t231 -41q140 0 259.5 35t216.5 98q18 11 28.5 5.5t13.5 -16.5l7 -32q-114 -76 -245.5 -117t-286.5 -41q-141 0 -260 46.5t-204.5 135.5t-134 216.5t-48.5 289.5zM529 422 q0 -34 8 -63t24 -50.5t40 -34t56 -12.5q34 0 71 12.5t74 44.5t72.5 87.5t66.5 141.5l115 319q-22 6 -46 10t-55 4q-60 0 -115.5 -19t-102.5 -52.5t-85.5 -78.5t-65.5 -95.5t-42 -105.5t-15 -108z" />
<glyph unicode="A" horiz-adv-x="1246" d="M-48 0l723 1449h101l368 -1449h-79q-14 0 -22.5 7.5t-11.5 19.5l-103 424h-649l-208 -424q-5 -11 -16.5 -19t-24.5 -8h-78zM315 525h594l-179 738q-4 18 -8 40t-8 48q-10 -26 -20 -48t-18 -41z" />
<glyph unicode="B" horiz-adv-x="1204" d="M127 0l178 1449h386q104 0 180 -21.5t126.5 -62t74.5 -99t24 -131.5q0 -70 -22 -133t-63.5 -114.5t-101.5 -89.5t-135 -57q142 -24 216.5 -101t74.5 -201q0 -100 -34.5 -181.5t-99.5 -139t-158 -88t-211 -30.5h-435zM239 81h331q190 0 292 92.5t102 262.5q0 59 -21 107 t-61.5 81.5t-99.5 52t-135 18.5h-333zM324 769h285q99 0 172 29.5t120 78.5t69.5 111.5t22.5 128.5q0 122 -76 186.5t-235 64.5h-285z" />
<glyph unicode="C" horiz-adv-x="1288" d="M121 612q0 189 58 346.5t158.5 270t237.5 174.5t295 62q75 0 134.5 -11.5t109 -34t91 -56.5t80.5 -77l-33 -41q-11 -12 -26 -12q-9 0 -21 10.5t-30 26.5t-44 35t-63.5 35t-87.5 26.5t-116 10.5q-135 0 -251.5 -53t-202.5 -151.5t-135.5 -238.5t-49.5 -314q0 -127 34 -229 t94.5 -173.5t144.5 -109t185 -37.5q75 0 134.5 12.5t105.5 31.5t79 41t56.5 41t38 32t22.5 13q9 0 14 -6l36 -43q-94 -95 -216.5 -152t-285.5 -57q-125 0 -226 45t-172 128t-109.5 198.5t-38.5 256.5z" />
<glyph unicode="D" horiz-adv-x="1446" d="M127 0l178 1449h488q128 0 231 -43.5t175.5 -123.5t111.5 -193.5t39 -253.5q0 -128 -25.5 -240t-72 -204.5t-112.5 -165t-148 -123t-177 -76.5t-201 -26h-487zM239 83h385q135 0 248.5 50.5t196.5 147t129 236t46 316.5q0 124 -32 223t-92 167.5t-145.5 105.5t-192.5 37 h-385z" />
<glyph unicode="E" horiz-adv-x="1103" d="M127 0l178 1449h801l-11 -84h-698l-72 -590h580l-11 -82h-580l-74 -609h700l-11 -84h-802z" />
<glyph unicode="F" horiz-adv-x="1083" d="M127 0l178 1449h801l-11 -84h-698l-75 -613h605l-13 -85h-603l-82 -667h-102z" />
<glyph unicode="G" horiz-adv-x="1399" d="M122 614q0 191 56 348.5t157.5 269t242 172.5t308.5 61q79 0 143.5 -11.5t117.5 -34t97 -55.5t83 -75l-32 -40q-16 -26 -43 -11q-14 6 -38 28.5t-66.5 47t-108 44t-161.5 19.5q-144 0 -264 -53t-206.5 -151.5t-134.5 -239t-48 -316.5q0 -127 34 -228.5t96.5 -173.5 t150.5 -110t197 -38q69 0 126.5 7.5t107.5 23t96 37.5t92 49l49 394h-253q-11 0 -18 6.5t-5 15.5l6 56h372l-65 -514q-54 -38 -111 -67.5t-121 -49.5t-135.5 -30.5t-155.5 -10.5q-130 0 -235 45.5t-178.5 128.5t-113 199t-39.5 257z" />
<glyph unicode="H" horiz-adv-x="1436" d="M127 0l178 1449h103l-83 -677h827l83 677h102l-178 -1449h-102l85 695h-828l-85 -695h-102z" />
<glyph unicode="I" horiz-adv-x="506" d="M127 0l177 1449h103l-178 -1449h-102z" />
<glyph unicode="J" horiz-adv-x="799" d="M-38 13l7 56q2 8 8 15t20 7q10 0 22.5 -3.5t30.5 -7.5t43 -7.5t61 -3.5q60 0 115.5 18.5t100 63.5t75.5 118t44 181l123 999h102l-123 -997q-14 -120 -51.5 -208t-94 -146t-130.5 -86t-163 -28q-55 0 -100.5 7t-89.5 22z" />
<glyph unicode="K" horiz-adv-x="1208" d="M127 0l177 1451h103l-83 -673h78q17 0 30.5 1.5t25.5 6.5t22 12t22 19l606 602q16 18 31 24t35 6h82l-657 -652q-18 -19 -33.5 -31t-33.5 -18q36 -12 60 -52l523 -696h-82q-27 0 -37 6.5t-18 20.5l-481 628q-8 11 -16 19.5t-18.5 14t-25 8t-37.5 2.5h-86l-86 -699h-101z " />
<glyph unicode="L" horiz-adv-x="955" d="M127 0l178 1449h102l-168 -1362h630l-11 -87h-731z" />
<glyph unicode="M" horiz-adv-x="1726" d="M127 0l178 1449h70q14 0 21 -3.5t16 -15.5l413 -1020q4 -11 7.5 -23t6.5 -25q10 23 24 48l654 1020q10 12 17.5 15.5t21.5 3.5h71l-178 -1449h-89l152 1238q3 26 10 54l-645 -1013q-16 -26 -42 -26h-17q-28 0 -36 26l-411 1016q0 -31 -3 -56l-152 -1239h-89z" />
<glyph unicode="N" horiz-adv-x="1436" d="M127 0l178 1449h50q14 0 20.5 -3.5t14.5 -15.5l703 -1262q1 14 1.5 27t2.5 24l151 1230h89l-178 -1449h-47q-13 0 -21.5 5.5t-16.5 17.5l-702 1261q-1 -12 -1.5 -23.5t-2.5 -22.5l-152 -1238h-89z" />
<glyph unicode="O" horiz-adv-x="1508" d="M122 613q0 190 56 347.5t155 269.5t233.5 173.5t291.5 61.5q128 0 231 -45.5t175 -128.5t111 -199t39 -256q0 -191 -55.5 -348t-155 -268.5t-234.5 -173t-293 -61.5q-127 0 -229.5 45.5t-174.5 128.5t-111 198.5t-39 255.5zM225 616q0 -126 33 -226.5t93.5 -171.5 t145.5 -109t190 -38q135 0 249 52.5t196.5 152t129.5 240.5t47 318q0 126 -32.5 226.5t-93 172t-146 109.5t-190.5 38q-133 0 -247 -53t-197.5 -152.5t-130.5 -240.5t-47 -318z" />
<glyph unicode="P" horiz-adv-x="1103" d="M127 0l177 1449h348q213 0 319.5 -96t106.5 -269q0 -111 -38 -202t-107.5 -157.5t-167.5 -103t-219 -36.5h-246l-72 -585h-101zM310 667h246q96 0 173.5 30.5t132 85t84.5 129t30 162.5q0 139 -83.5 216.5t-250.5 77.5h-246z" />
<glyph unicode="Q" horiz-adv-x="1508" d="M122 613q0 190 56 347.5t155 269.5t233.5 173.5t291.5 61.5q128 0 231 -45.5t175 -128.5t111 -199t39 -256q0 -131 -27 -247t-76.5 -212t-120 -172t-156.5 -126l297 -398h-80q-40 0 -60 24l-244 332q-62 -26 -129.5 -39t-141.5 -13q-127 0 -229.5 45.5t-174.5 128.5 t-111 198.5t-39 255.5zM225 616q0 -126 33 -226.5t93.5 -171.5t145.5 -109t190 -38q135 0 249 52.5t196.5 152t129.5 240t47 318.5q0 126 -32.5 226.5t-93 172t-146 109.5t-190.5 38q-133 0 -247 -53t-197.5 -152.5t-130.5 -241t-47 -317.5z" />
<glyph unicode="R" horiz-adv-x="1155" d="M127 0l177 1449h344q210 0 314 -83.5t104 -242.5q0 -94 -30.5 -172.5t-87 -136.5t-136 -95.5t-175.5 -49.5q19 -12 36 -40l378 -629h-87q-16 0 -27 5.5t-19 20.5l-350 589q-14 23 -31 32t-55 9h-173l-81 -656h-101zM318 732h229q97 0 174.5 26.5t131.5 76t83 119 t29 154.5q0 131 -83 195.5t-243 64.5h-242z" />
<glyph unicode="S" horiz-adv-x="1019" d="M34 173l34 44q14 14 28 14q9 0 21 -12t29 -30t40 -39t55.5 -39t76 -30t101.5 -12q86 0 155.5 29t118.5 80t75.5 120.5t26.5 148.5q0 63 -26 106t-67.5 72.5t-94 50t-108 41t-108.5 45t-94.5 61t-66.5 89.5t-25 129q0 83 29.5 160t85.5 136t137 93.5t184 34.5 q104 0 183 -38t137 -112l-29 -41q-11 -17 -27 -18q-12 0 -29.5 19.5t-47.5 42.5t-78 42.5t-119 19.5q-78 0 -139 -25t-103.5 -68.5t-65 -101.5t-22.5 -123q0 -62 25.5 -105t67 -73t94.5 -52t108.5 -43t108.5 -46t94.5 -61t67 -87t25.5 -126q0 -98 -34 -186.5t-97 -155 t-153 -105t-201 -38.5q-131 0 -221.5 50.5t-151.5 138.5z" />
<glyph unicode="T" horiz-adv-x="1090" d="M117 1363l10 86h1037l-10 -86h-466l-168 -1363h-101l168 1363h-470z" />
<glyph unicode="U" horiz-adv-x="1385" d="M178 552l110 897h101l-109 -896q-5 -43 -5 -83q-1 -54 9 -106q16 -88 60.5 -153.5t116 -102t166.5 -36.5t174.5 36.5t139 101t98.5 153t52 189.5l110 897h102l-110 -897q-15 -120 -63.5 -224t-123.5 -180.5t-174.5 -120.5t-217.5 -44q-117 0 -204.5 44t-143.5 121 q-58 76 -80 180q-14 64 -13 134q0 44 5 90z" />
<glyph unicode="V" horiz-adv-x="1241" d="M127 1449h81q14 0 22.5 -7.5t10.5 -19.5l301 -1209q5 -22 10 -46.5t8 -50.5q9 27 19 51t21 46l597 1209q5 11 16 19t25 8h80l-727 -1449h-91z" />
<glyph unicode="W" horiz-adv-x="1921" d="M145 1449h79q31 0 33 -27l193 -1204q2 -19 3.5 -40t3.5 -44q7 23 14 44.5t15 39.5l524 1204q5 11 15 19t25 8h22q27 0 34 -27l226 -1204q3 -18 5.5 -39t4.5 -44q6 23 13.5 43.5t13.5 39.5l488 1204q3 11 15 19t26 8h78l-597 -1449h-91l-237 1263q-3 22 -7 54 q-5 -15 -9.5 -29t-9.5 -25l-548 -1263h-90z" />
<glyph unicode="X" horiz-adv-x="1193" d="M-50 0l598 774l-359 675h92q14 0 20 -5t12 -17l309 -601q7 14 15 27l442 578q12 18 33 18h101l-524 -670l406 -779h-91q-14 0 -20.5 8t-11.5 17l-354 696q-6 -14 -15 -25l-512 -671q-17 -25 -41 -25h-100z" />
<glyph unicode="Y" horiz-adv-x="1129" d="M123 1449h90q27 0 35 -26l306 -684q6 -17 11 -34t8 -34q8 17 17.5 34t20.5 34l475 684q7 11 16.5 18.5t23.5 7.5h85l-597 -852l-74 -597h-102l75 598z" />
<glyph unicode="Z" horiz-adv-x="1123" d="M-13 0l5 34q2 8 5.5 14.5t8.5 14.5l1000 1302h-827l11 84h965l-5 -34q-2 -9 -5.5 -15.5t-10.5 -16.5l-998 -1299h844l-11 -84h-982z" />
<glyph unicode="[" horiz-adv-x="572" d="M92 -269l223 1821h301l-3 -35q-2 -12 -10.5 -20t-21.5 -8h-193l-209 -1694h194q13 0 20 -8t5 -20l-5 -36h-301z" />
<glyph unicode="\" horiz-adv-x="819" d="M179 1480h40q40 0 49 -37l394 -1529h-39q-16 0 -31 9t-21 29z" />
<glyph unicode="]" horiz-adv-x="575" d="M-37 -269l4 36q2 12 11 20t23 8h193l206 1694h-191q-14 0 -21.5 7.5t-5.5 20.5l4 35h303l-224 -1821h-302z" />
<glyph unicode="^" d="M269 819l349 630h61l349 -630h-69q-11 0 -20 7t-14 17l-247 446q-19 35 -28 64q-5 -15 -11.5 -31t-15.5 -33l-246 -446q-5 -8 -13.5 -16t-22.5 -8h-72z" />
<glyph unicode="_" horiz-adv-x="908" d="M-27 -279l8 67h743l-9 -67h-742z" />
<glyph unicode="`" horiz-adv-x="753" d="M347 1465h87q23 0 33.5 -7t18.5 -25l120 -244h-51q-11 0 -19 3.5t-13 13.5z" />
<glyph unicode="a" horiz-adv-x="1020" d="M69 342q0 89 23 174.5t64 162.5t98 141.5t127 111.5t148.5 73.5t162.5 26.5q60 0 114 -10.5t105 -37.5l-119 -984h-48q-20 0 -27 11.5t-7 25.5l34 317q-39 -82 -85.5 -150t-99.5 -116t-111.5 -74.5t-120.5 -26.5q-63 0 -112 25t-81.5 71t-48.5 111.5t-16 147.5zM167 357 q0 -137 46.5 -213t144.5 -76q58 0 114.5 32.5t108.5 92t97 143t81 184.5l52 419q-31 11 -63.5 15.5t-66.5 4.5q-102 0 -195.5 -50t-164 -134t-112.5 -192.5t-42 -225.5z" />
<glyph unicode="b" horiz-adv-x="1055" d="M83 0l180 1490h95l-99 -815q40 81 88.5 147t103.5 113t115 73t121 26q67 0 117 -25.5t84 -72t51 -113t17 -148.5q0 -79 -16 -160.5t-46.5 -157.5t-74 -144t-98.5 -118t-121 -79t-140 -29q-86 0 -158.5 39.5t-115.5 116.5l-19 -110q-5 -16 -11 -24.5t-25 -8.5h-48z M202 211q23 -41 51 -69t59.5 -45.5t66 -25.5t70.5 -8q65 0 120.5 26.5t101 71.5t80.5 105t59.5 126.5t37 136.5t12.5 137q0 137 -52 213t-153 76q-58 0 -117 -32.5t-113 -92t-102.5 -143.5t-84.5 -186z" />
<glyph unicode="c" horiz-adv-x="932" d="M78 393q0 81 17 160.5t48.5 151t76.5 132t101.5 104.5t122.5 69t141 25q50 0 90.5 -9.5t74 -27.5t61.5 -43.5t51 -57.5l-28 -34q-9 -11 -22 -11q-10 0 -25 16.5t-40 35.5t-65 35t-100 16q-86 0 -160.5 -46t-129 -123.5t-86 -179.5t-31.5 -213q0 -71 16 -131t49 -104 t83.5 -68.5t118.5 -24.5q54 0 97.5 11.5t76.5 29t58 38.5t44.5 38.5t33.5 29t25 11.5t22 -10l21 -27q-54 -60 -102.5 -99t-95.5 -61.5t-95 -30.5t-102 -8q-88 0 -153 30t-108 84.5t-64.5 129t-21.5 162.5z" />
<glyph unicode="d" horiz-adv-x="1056" d="M74 348q0 78 16 159.5t46.5 157.5t74 143.5t99 117.5t121 79t139.5 29q83 0 153.5 -36t114.5 -108l74 600h95l-183 -1490h-47q-20 0 -27.5 11.5t-7.5 25.5l34 320q-40 -83 -89.5 -151t-105 -117t-115.5 -75.5t-123 -26.5q-67 0 -117 25.5t-83.5 72.5t-51 113.5 t-17.5 149.5zM170 356q0 -137 52 -213.5t153 -76.5q58 0 116.5 32.5t113 92t102.5 143t85 184.5l36 294q-23 40 -50.5 68t-59.5 45t-67 24.5t-69 7.5q-65 0 -120.5 -26t-101.5 -71t-81 -104.5t-59 -126t-37 -137t-13 -136.5z" />
<glyph unicode="e" horiz-adv-x="959" d="M77 387q0 77 15.5 155t45.5 149.5t74.5 134t102 108.5t127 72.5t150.5 26.5q75 0 126.5 -21t84 -52t46.5 -67.5t14 -68.5q0 -42 -11 -79.5t-40 -71.5t-78 -63.5t-125.5 -55t-183 -46t-249.5 -35.5q-2 -20 -3.5 -41.5t-1.5 -42.5q0 -153 69 -238.5t209 -85.5 q54 0 97.5 11.5t77.5 30t60.5 39t47 39t35 30t27.5 11.5q12 0 21 -10l23 -27q-50 -53 -98 -91.5t-96.5 -63t-101 -36t-111.5 -11.5q-85 0 -151 28t-111 79.5t-68.5 126t-23.5 166.5zM186 541q130 14 224.5 33t160 40t106.5 45.5t63 50.5t29.5 54t7.5 57q0 22 -9.5 47 t-32 45.5t-59 34.5t-89.5 14q-82 0 -148 -34.5t-117 -93t-85 -134.5t-51 -159z" />
<glyph unicode="f" horiz-adv-x="630" d="M93 949l4 38h156l16 129q11 87 41 153t74.5 110t101 66t120.5 22q29 0 57 -5.5t52 -15.5l-10 -49q-2 -8 -9.5 -10t-19 0t-28 4.5t-38.5 2.5q-47 0 -88 -15.5t-73.5 -49t-55 -86.5t-32.5 -130l-16 -126h288l-10 -72h-284l-109 -893l-43 -236q-12 -42 -50 -42h-35l143 1170 l-128 9q-12 2 -18.5 8t-5.5 18z" />
<glyph unicode="g" horiz-adv-x="946" d="M-48 -136q1 86 52.5 149.5t138 102t199.5 49.5q46 4 94 4q69 0 142 -9q-17 24 -29.5 50.5t-12.5 58.5q0 36 12 73t47 89q-41 -29 -88 -44t-103 -15q-58 0 -108 18.5t-86 54.5t-56.5 89t-20.5 122q0 71 25 139t73 121.5t117.5 85.5t158.5 32q105 0 180 -51h265l-3 -33 q-2 -10 -8 -17.5t-20 -9.5l-166 -14q18 -32 28.5 -70t10.5 -85q-1 -56 -15 -102.5t-34.5 -85t-44.5 -71.5t-44.5 -65t-34 -64.5t-13.5 -69.5q-1 -29 11.5 -53.5t31 -49t39.5 -49t39.5 -53.5t30 -62.5t11.5 -75.5q-1 -65 -33 -124.5t-91.5 -106t-143 -74t-186.5 -27.5 q-81 0 -148 16.5t-115.5 47.5t-75 76t-26.5 103zM44 -126q0 -44 20 -78.5t57 -57.5t89 -35t116 -12q81 0 146.5 21.5t112 57t72 82t25.5 95.5v6q0 46 -16 80q-16 36 -39 65l1 -3q-100 15 -182 15q-39 0 -75 -4q-110 -10 -182 -42.5t-108 -83t-37 -106.5zM222 656 q0 -109 55.5 -165t150.5 -56q66 0 117.5 27.5t87 72.5t54.5 101.5t19 114.5q0 106 -57 160t-151 54q-67 0 -118 -26.5t-86 -69.5t-53.5 -98.5t-18.5 -114.5z" />
<glyph unicode="h" horiz-adv-x="1055" d="M83 0l180 1490h96l-94 -777q41 76 91 135.5t104.5 101t112.5 63t115 21.5q67 0 114 -25.5t74 -74.5q28 -48 37 -120q4 -31 3 -65q0 -46 -6 -98l-76 -651h-97l77 651q5 46 5 85q-1 84 -25 138q-36 78 -138 79q-55 0 -112.5 -28t-111 -80t-101.5 -126.5t-85 -166.5 l-67 -552h-96z" />
<glyph unicode="i" horiz-adv-x="433" d="M94 0l124 1019h95l-124 -1019h-95zM215 1370q0 18 7 34.5t19 29.5t27.5 20t32.5 7q16 0 32 -7t28.5 -19.5t19.5 -29.5t7 -35t-7.5 -34t-20 -28.5t-28.5 -19.5t-32 -7q-17 0 -32.5 7t-27 19.5t-18.5 28.5t-7 34z" />
<glyph unicode="j" horiz-adv-x="432" d="M-175 -356l10 49q4 9 9 10.5t13 0t19.5 -4t30.5 -2.5q76 0 117.5 45t52.5 128l141 1149h95l-141 -1149q-6 -53 -26.5 -98t-53 -78.5t-77 -52.5t-98.5 -19q-30 0 -51.5 5t-40.5 17zM216 1370q0 18 7 34.5t19 29.5t27.5 20t32.5 7q16 0 32 -7t28.5 -19.5t19.5 -29.5t7 -35 t-7 -34t-19.5 -28.5t-28.5 -19.5t-32 -7q-17 0 -32.5 7t-27.5 19.5t-19 28.5t-7 34z" />
<glyph unicode="k" horiz-adv-x="906" d="M82 0l182 1490h95l-111 -908h39q17 0 29 4.5t26 18.5l422 388q11 12 23.5 19t29.5 7h83l-466 -427q-11 -11 -20.5 -19.5t-20.5 -14.5q13 -8 23 -19t18 -25l373 -514h-81q-14 0 -24.5 4.5t-18.5 19.5l-338 458q-13 19 -26.5 25.5t-42.5 6.5h-36l-63 -514h-95z" />
<glyph unicode="l" horiz-adv-x="428" d="M92 0l180 1490h96l-182 -1490h-94z" />
<glyph unicode="m" horiz-adv-x="1597" d="M83 0l123 1022h44q36 0 36 -37l-29 -285q80 159 184.5 246.5t220.5 87.5q59 0 98 -21.5t60.5 -63t27.5 -101.5q3 -30 3 -65t-3 -73q40 82 87.5 142.5t100.5 101t108 60.5t109 20q67 0 110.5 -25.5t66.5 -74.5q24 -48 28 -120q1 -21 2 -44q0 -55 -8 -119l-77 -651h-95 l77 651q8 63 8 113q-1 8 -1 16q-2 56 -16.5 94.5t-44.5 58t-80 19.5t-102 -23t-101.5 -69t-94 -115.5t-80.5 -163.5l-69 -581h-95l77 651q9 73 9 129.5t-13.5 94.5t-42.5 57.5t-79 19.5q-117 0 -216 -106.5t-173 -300.5l-65 -545h-95z" />
<glyph unicode="n" horiz-adv-x="1056" d="M84 0l122 1022h44q36 0 36 -37l-29 -294q41 81 91.5 145t107 108t116 67t119.5 23q66 0 112.5 -25.5t74.5 -74.5q28 -48 37 -120q4 -31 3 -65q0 -46 -6 -98l-76 -651h-97l77 651q5 46 5 85q-1 84 -25 138q-36 78 -138 78q-57 0 -115 -29t-112.5 -83t-102.5 -130t-85 -171 l-63 -539h-96z" />
<glyph unicode="o" horiz-adv-x="1050" d="M80 406q0 124 37 237.5t104 200.5t159 138.5t203 51.5q82 0 149 -28.5t114.5 -83t73.5 -131.5t26 -174q0 -124 -37.5 -237.5t-104 -201t-158.5 -139.5t-203 -52q-82 0 -149 28.5t-114.5 83t-73.5 131.5t-26 176zM177 406q0 -163 72.5 -253t202.5 -90q89 0 162 47 t125.5 124t80.5 177t28 205q0 164 -73 252t-202 88q-89 0 -162 -46t-125 -122t-80.5 -175.5t-28.5 -206.5z" />
<glyph unicode="p" horiz-adv-x="1056" d="M39 -360l168 1382h44q36 0 36 -37l-32 -320q40 83 89.5 151t105 116.5t116 75t122.5 26.5q67 0 117 -25.5t84 -72t51 -113t17 -148.5q0 -79 -16 -160.5t-46.5 -157.5t-74 -144t-98.5 -118t-121 -79t-140 -29q-83 0 -153 36.5t-114 108.5l-61 -492h-94zM203 210 q45 -81 110.5 -114t136.5 -33q65 0 120.5 26.5t101 71.5t80.5 105t59.5 126.5t37 136.5t12.5 137q0 137 -52 213t-153 76q-58 0 -117 -32.5t-113 -92t-102.5 -143t-84.5 -185.5z" />
<glyph unicode="q" horiz-adv-x="1021" d="M70 342q0 89 22.5 174.5t64 162.5t98.5 141.5t126.5 111.5t148 73.5t162.5 26.5q61 0 114.5 -10.5t105.5 -37.5l-164 -1344h-56q-24 0 -32 14.5t-5 39.5l81 642q-39 -79 -84.5 -143t-97 -110t-108.5 -71t-118 -25q-65 0 -113 25t-80.5 71t-48.5 111.5t-16 147.5zM167 357 q0 -137 46.5 -213t145.5 -76q58 0 114.5 32.5t108 92t97 143t81.5 184.5l52 419q-31 11 -63.5 15.5t-67.5 4.5q-69 0 -133.5 -23t-121 -64t-104.5 -97t-82.5 -123t-53.5 -142t-19 -153z" />
<glyph unicode="r" horiz-adv-x="672" d="M83 0l123 1022h44q36 0 36 -37l-30 -302q42 100 91.5 174t105.5 117q57 43 117 53q18 3 35 3q43 0 87 -17l-17 -91q-46 18 -89 18q-22 0 -44 -5q-62 -14 -116.5 -66t-101 -140t-83.5 -209l-63 -520h-95z" />
<glyph unicode="s" horiz-adv-x="804" d="M19 108l25 34q11 16 28 17q10 0 25.5 -16.5t42 -36.5t68.5 -36.5t105 -16.5q61 0 111 20t85.5 55t55 80.5t19.5 96.5q0 45 -19.5 76t-52 53t-73 37t-83.5 30t-83.5 33t-72.5 43.5t-51.5 62.5t-19.5 90q0 59 25.5 114t71.5 97.5t110 67.5t140 25q86 0 149 -26.5t112 -79.5 l-23 -34q-8 -14 -23 -14q-10 0 -25 12.5t-40 28.5t-63 29t-94 13q-52 0 -98.5 -17.5t-81 -47t-54.5 -67.5t-20 -81q0 -42 19.5 -71t51.5 -50t72.5 -36t83.5 -30.5t83.5 -34.5t73 -45.5t52 -65t19.5 -92.5q0 -66 -26.5 -127.5t-75 -109t-116.5 -76t-152 -28.5q-98 0 -165 34 t-116 90z" />
<glyph unicode="t" horiz-adv-x="698" d="M88 938l5 38l161 12l68 365q2 9 9 16t17 7h47l-48 -389h295l-8 -73h-295l-71 -575q-5 -42 -8.5 -68.5t-5 -43.5t-2 -25t-0.5 -13q0 -67 31 -96.5t81 -29.5q34 0 60 11t45 24t31.5 23.5t18.5 10.5q10 0 16 -10l18 -43q-41 -43 -99.5 -69t-116.5 -26q-84 0 -131.5 46 t-48.5 141q0 6 0.5 15.5t2 27.5t4.5 47.5t9 74.5l70 578h-134q-10 0 -16.5 6.5t-4.5 17.5z" />
<glyph unicode="u" horiz-adv-x="1056" d="M119 371l77 648h96l-77 -648q-5 -46 -6 -86q-1 -85 25 -139q36 -78 139 -78q56 0 114 28.5t112 82t102.5 129t84.5 169.5l65 542h96l-123 -1019h-45q-19 0 -28 10t-9 28l31 290q-42 -81 -92 -144.5t-106 -107t-116 -66.5t-119 -23q-132 1 -188 101q-40 71 -40 184 q0 46 7 99z" />
<glyph unicode="v" horiz-adv-x="949" d="M87 1019h75q14 0 21.5 -7.5t10.5 -17.5l207 -804q4 -24 7 -46.5t5 -46.5q7 24 15 46.5t19 46.5l407 804q5 11 14.5 18t21.5 7h73l-523 -1019h-80z" />
<glyph unicode="w" horiz-adv-x="1443" d="M97 1019h69q27 0 32 -25l136 -804q5 -45 6 -87q8 22 16.5 43.5t17.5 43.5l350 811q7 23 30 23h38q23 0 24 -23l151 -811q4 -23 6.5 -44t4.5 -43q7 20 14 42t16 45l333 804q5 11 14.5 18t20.5 7h68l-437 -1019h-70q-16 0 -19 22l-156 827q-5 25 -6 48q-4 -12 -8.5 -24 t-9.5 -24l-361 -827q-8 -22 -25 -22h-66z" />
<glyph unicode="x" horiz-adv-x="892" d="M-44 0l424 546l-257 473h82q13 0 19 -3.5t12 -11.5l214 -408q7 15 16 28l295 377q10 17 27 18h86l-373 -475l285 -544h-81q-25 0 -34 23l-240 467q-8 -16 -16 -28l-342 -442q-7 -8 -15 -14t-20 -6h-82z" />
<glyph unicode="y" horiz-adv-x="951" d="M90 1019h76q14 0 21.5 -6.5t9.5 -18.5l215 -792q4 -15 5.5 -30.5t3.5 -31.5q6 15 13 30.5t15 31.5l410 795q5 11 14.5 16.5t18.5 5.5h75l-708 -1351q-14 -28 -40 -28h-2h-69l216 399z" />
<glyph unicode="z" horiz-adv-x="846" d="M-10 0l4 39q0 8 5 18.5t13 19.5l689 865h-566l10 77h682l-5 -41q-1 -19 -19 -39l-687 -862h572l-8 -77h-690z" />
<glyph unicode="{" horiz-adv-x="567" d="M85 611l7 61q76 0 123 59.5t47 180.5q0 61 -7 132.5t-7 133.5q0 87 22 156t62 117.5t96.5 74.5t127.5 26h52l-4 -41q-2 -11 -11 -16.5t-18 -5.5h-29q-44 0 -82 -18t-66.5 -53t-45.5 -86.5t-18 -118.5q-2 -42 -0.5 -85t3.5 -85.5t4 -83.5t1 -79q-1 -53 -16 -95t-39 -71 t-53 -47.5t-60 -25.5q25 -6 46.5 -20t37 -35t25.5 -48t10 -60q0 -62 -17.5 -122.5t-38.5 -121.5t-38.5 -124t-17.5 -129q0 -42 11.5 -76t31.5 -58.5t48.5 -38t64.5 -13.5h32q8 0 15.5 -6t5.5 -17l-5 -41h-52q-57 0 -101 19t-73.5 52.5t-45 79t-15.5 99.5q0 70 18 132.5 t39.5 122.5t39.5 119.5t18 122.5q0 58 -35 95.5t-93 37.5z" />
<glyph unicode="|" horiz-adv-x="501" d="M210 -360v1913h77v-1913h-77z" />
<glyph unicode="}" horiz-adv-x="568" d="M-34 -269l6 41q1 11 10 17t17 6h31q43 0 81.5 18t67 53t45.5 86t18 118q1 42 0 85t-3 85.5t-4 83.5t-1 78q1 53 16 95.5t38.5 72t53 47.5t59.5 25q-49 12 -84 54t-35 109q0 62 17 122.5t38 121.5t38.5 124t17.5 130q0 84 -42 135t-113 51h-30q-9 0 -16.5 5.5t-5.5 16.5 l4 41h53q56 0 99.5 -19t73 -52.5t45 -79t15.5 -98.5q0 -70 -17.5 -133t-39 -123t-38.5 -119.5t-17 -122.5q0 -58 33.5 -95.5t94.5 -37.5l-9 -61q-75 0 -122.5 -59t-47.5 -181q0 -30 2 -64t5 -68.5t5 -68.5t2 -65q0 -87 -22 -156t-62 -117.5t-97 -74.5t-128 -26h-52z" />
<glyph unicode="~" d="M143 444q0 56 16.5 104.5t47.5 84.5t76 56.5t102 20.5q52 0 107 -20t108.5 -44.5t104 -44.5t94.5 -20q38 0 68.5 13.5t52 38t33.5 58.5t13 74h78q0 -56 -16 -104.5t-47 -84t-75.5 -56t-101.5 -20.5q-53 0 -108 20t-108 44.5t-103.5 44.5t-94.5 20q-39 0 -69.5 -14 t-52 -38.5t-33.5 -58.5t-12 -74h-80z" />
<glyph unicode="&#xa1;" horiz-adv-x="441" d="M75 -360l68 563q5 46 11 87t12 83.5t13 89.5t16 104h41q-5 -57 -10 -104t-9.5 -89.5t-9 -83.5t-10.5 -87l-69 -563h-53zM162 939q0 40 27 68t67 28q19 0 36.5 -7.5t30.5 -20.5t20.5 -31t7.5 -37q0 -20 -7.5 -37t-20.5 -30t-30.5 -20t-36.5 -7q-40 0 -67 27t-27 67z" />
<glyph unicode="&#xa2;" d="M152 430q0 132 38.5 242t111.5 190t177 125t235 47l36 212q3 12 11.5 22t21.5 10h36l-42 -246q88 -8 154.5 -38t114.5 -76l-28 -33q-5 -5 -10.5 -8.5t-13.5 -3.5q-9 0 -24.5 12t-42 26.5t-66 28t-96.5 18.5l-155 -898q77 2 132.5 19.5t92.5 37.5t58.5 36.5t33.5 16.5 q11 0 17 -9l22 -31q-27 -29 -65 -54t-85.5 -44t-103 -30.5t-114.5 -13.5l-37 -216q-2 -13 -10.5 -22t-22.5 -9h-35l42 247q-88 6 -159 38.5t-121 89.5t-76.5 136.5t-26.5 177.5zM245 435q0 -83 21 -149t60 -113.5t95 -75.5t127 -35l152 899q-108 -3 -192 -41.5t-142.5 -107 t-89.5 -164t-31 -213.5z" />
<glyph unicode="&#xa3;" d="M17 0l8 66q38 13 68.5 31t54.5 44.5t39.5 64t22.5 91.5l43 351h-180l5 38q2 11 10.5 19.5t24.5 8.5h148l37 304q12 95 50.5 177t101 142t147.5 94t189 34q83 0 142.5 -19t101.5 -51t68.5 -76t42.5 -93l-41 -23q-32 -14 -49 12q-16 33 -36 64t-50.5 54.5t-75.5 37 t-112 13.5q-80 0 -146 -26.5t-115 -75t-79.5 -115.5t-41.5 -148l-37 -305h516l-3 -37q-2 -12 -11 -20.5t-24 -8.5h-486l-40 -330q-6 -45 -17 -80.5t-28.5 -65t-41.5 -53.5t-56 -44q22 4 43 6t44 2h810l-4 -40q-2 -15 -15.5 -29t-32.5 -14h-995z" />
<glyph unicode="&#xa4;" d="M164 1066l51 51l157 -158q46 38 102 59t121 21q63 0 119.5 -21t102.5 -56l158 156l50 -51l-157 -157q37 -46 58 -102t21 -121q0 -63 -20.5 -119t-57.5 -102l157 -158l-50 -51l-158 158q-46 -37 -102.5 -58t-120.5 -21q-63 0 -118.5 20.5t-101.5 57.5l-159 -158l-50 51 l158 158q-37 46 -58 102.5t-21 119.5t20.5 119t57.5 102zM318 687q0 -57 22 -107.5t59.5 -87.5t88 -59t107.5 -22q58 0 109 22t88.5 59t59.5 87.5t22 107.5t-22 108t-59.5 89t-88.5 60t-109 22q-57 0 -107.5 -22t-88 -60t-59.5 -89t-22 -108z" />
<glyph unicode="&#xa5;" d="M139 394l7 58h376l15 120h-376l7 59h355l-332 818h80q28 0 34 -26l274 -684q6 -20 10.5 -37.5t6.5 -34.5q7 17 15.5 34.5t19.5 37.5l440 684q7 11 17 18.5t24 7.5h80l-534 -818h356l-6 -59h-376l-16 -120h376l-7 -58h-376l-48 -394h-94l48 394h-376z" />
<glyph unicode="&#xa6;" horiz-adv-x="501" d="M210 -360v795h77v-795h-77zM210 757v796h77v-796h-77z" />
<glyph unicode="&#xa7;" horiz-adv-x="925" d="M60 -11l26 35q6 8 12 11.5t18 3.5q13 0 29 -16.5t42 -35.5t67.5 -35.5t104.5 -16.5t113.5 20t85.5 54.5t53.5 80t18.5 96.5q0 62 -37.5 105t-94 75.5t-121.5 62.5t-121 66t-93.5 86t-37.5 122q0 87 51 158t167 113q-43 34 -69 78t-26 107q0 60 25 115.5t71 97.5t110 67 t142 25q85 0 148.5 -26.5t112.5 -80.5l-23 -34q-9 -13 -24 -13q-11 0 -26 12.5t-40 28.5t-62.5 29t-93.5 13q-55 0 -102.5 -18t-81 -48t-52.5 -69.5t-19 -82.5q0 -42 20.5 -74t54 -58t77 -48.5t88.5 -45.5t89 -49.5t77.5 -59.5t54 -75t20.5 -98q0 -95 -47.5 -166.5 t-142.5 -110.5q41 -35 66 -78.5t25 -103.5q0 -70 -25.5 -132t-73.5 -109t-116.5 -74t-155.5 -27q-98 0 -166 33.5t-118 89.5zM212 727q0 -58 36.5 -99t91.5 -74t119.5 -63.5t121.5 -67.5q80 39 113.5 93t33.5 125q0 41 -15.5 73t-41 58.5t-59.5 48t-72 41t-77 39t-74 41.5 q-98 -44 -137.5 -96.5t-39.5 -118.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="753" d="M275 1315q0 17 7 32.5t18.5 27t26.5 18.5t31 7t32.5 -7t27.5 -18.5t18 -27t7 -32.5q0 -16 -7 -31t-18 -26.5t-27 -18t-33 -6.5q-16 0 -31 6.5t-26.5 18t-18.5 26t-7 31.5zM615 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 32.5 -7t27.5 -18.5t18 -27t7 -32.5 q0 -16 -7 -31t-18 -26.5t-27 -18t-33 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1717" d="M133 725q0 101 26.5 196t74.5 177t115.5 149.5t149.5 115.5t176 74.5t196 26.5t196.5 -26.5t177 -74.5t150 -115.5t115.5 -149.5t74.5 -177t26.5 -196q0 -102 -26.5 -196.5t-74.5 -176.5t-115.5 -149.5t-150 -115.5t-176.5 -74.5t-197 -26.5q-101 0 -195.5 26.5 t-176.5 74.5t-149.5 115.5t-115.5 149.5t-74.5 176.5t-26.5 196.5zM194 725q0 -142 52.5 -266.5t144.5 -217.5t215.5 -146t264.5 -53t266 53t217 146t145 217.5t53 266.5t-53 266.5t-145 218t-216.5 147t-266.5 53.5q-141 0 -264.5 -53.5t-215.5 -147t-144.5 -218 t-52.5 -266.5zM433 726q0 102 34.5 187t96 146t146.5 95t186 34q53 0 97.5 -8t82.5 -22.5t71 -35.5t64 -49l-24 -34q-7 -11 -21 -11q-10 0 -27 13.5t-46.5 29t-76 29t-116.5 13.5q-85 0 -154.5 -27t-119 -77.5t-77 -122t-27.5 -160.5q0 -92 27.5 -164t75.5 -121.5t114 -75.5 t145 -26q55 0 96 7.5t73.5 20.5t60 31t56.5 38q9 1 17 -4l36 -36q-60 -60 -142.5 -96t-200.5 -36q-98 0 -181 33.5t-141.5 94t-91.5 145.5t-33 189z" />
<glyph unicode="&#xaa;" horiz-adv-x="699" d="M162 982q0 43 21 80t67.5 65t120.5 45t180 19l6 53q9 79 -18 119.5t-94 40.5q-40 0 -68 -11.5t-49 -24.5t-35 -24.5t-25 -11.5q-8 0 -12.5 4t-7.5 10l-9 23q51 48 104.5 71t117.5 23q49 0 83.5 -18t55 -48.5t27.5 -70t1 -82.5l-48 -385h-32q-11 0 -15 3.5t-8 15.5v70 q-53 -48 -103.5 -73t-119.5 -25q-29 0 -54.5 8t-44.5 24t-30 41t-11 59zM231 991q0 -47 29 -68t68 -21q63 0 109.5 27.5t90.5 68.5l17 144q-165 -6 -239.5 -44t-74.5 -107z" />
<glyph unicode="&#xab;" horiz-adv-x="750" d="M93 525l1 13l277 387l27 -15q11 -7 13 -19.5t-10 -28.5l-207 -302q-15 -22 -26 -29q9 -8 18 -28l135 -302q7 -16 3 -28.5t-18 -19.5l-33 -16zM333 525l1 13l277 387l27 -15q11 -7 13 -19.5t-10 -28.5l-207 -302q-15 -22 -26 -29q9 -8 18 -28l135 -302q7 -16 3 -28.5 t-18 -19.5l-33 -16z" />
<glyph unicode="&#xac;" d="M175 650l8 73h848l-8 -73h-1l-37 -302h-82l38 302h-766z" />
<glyph unicode="&#xae;" horiz-adv-x="1718" d="M133 725q0 101 26.5 196t74.5 177t115.5 149.5t149.5 115.5t176 74.5t196 26.5t196.5 -26.5t177 -74.5t150 -115.5t115.5 -149.5t74.5 -177t26.5 -196q0 -102 -26.5 -196.5t-74.5 -176.5t-115.5 -149.5t-150 -115.5t-176.5 -74.5t-197 -26.5q-101 0 -195.5 26.5 t-176.5 74.5t-149.5 115.5t-115.5 149.5t-74.5 176.5t-26.5 196.5zM194 725q0 -142 52.5 -266.5t144.5 -217.5t215.5 -146t264.5 -53t266 53t217 146t145 217.5t53 266.5t-53 266.5t-145 218t-216.5 147t-266.5 53.5q-141 0 -264.5 -53.5t-215.5 -147t-144.5 -218 t-52.5 -266.5zM598 272v907h253q157 0 237 -59t80 -180q0 -101 -66 -167t-184 -83q22 -12 39 -41l292 -377h-82q-10 0 -17.5 3.5t-13.5 13.5l-277 359q-8 11 -21 18t-40 7h-113v-401h-87zM685 738h152q124 0 184.5 50t60.5 143q0 94 -55 137t-176 43h-166v-373z" />
<glyph unicode="&#xaf;" horiz-adv-x="753" d="M264 1282l7 67h514l-8 -67h-513z" />
<glyph unicode="&#xb0;" horiz-adv-x="833" d="M173 1161q0 63 24 119t66 97.5t97.5 65t118.5 23.5q65 0 121.5 -23.5t98 -65t65.5 -97t24 -119.5q0 -63 -24 -118.5t-65.5 -97t-98 -65.5t-121.5 -24q-63 0 -118.5 24t-97.5 65.5t-66 97t-24 118.5zM243 1161q0 -49 18.5 -92t50.5 -74.5t75 -50t92 -18.5t92 18.5t75 50 t50.5 74.5t18.5 92t-18.5 92t-50.5 75.5t-75 51t-92 18.5t-92 -18.5t-75 -51t-50.5 -75.5t-18.5 -92z" />
<glyph unicode="&#xb1;" d="M52 82l8 73h962l-9 -73h-961zM128 690l8 73h441l51 416h77l-52 -416h444l-8 -73h-444l-51 -414h-77l51 414h-440z" />
<glyph unicode="&#xb2;" horiz-adv-x="684" d="M136 922l2 25q2 7 5.5 15t11.5 15l272 243q35 32 66.5 62t54 62t35.5 64.5t13 69.5q0 32 -10 55t-28 38.5t-42 23t-51 7.5q-63 0 -107.5 -36t-66.5 -94q-7 -11 -15 -16t-24 -3l-37 7q26 98 96 149.5t165 51.5q41 0 76.5 -11t61.5 -33.5t40.5 -55t14.5 -75.5 q0 -46 -16 -85t-42 -73.5t-60.5 -67t-71.5 -66.5l-240 -217q18 5 38.5 8t40.5 3h284q25 0 23 -24l-5 -42h-484z" />
<glyph unicode="&#xb3;" horiz-adv-x="684" d="M151 1102l33 14q12 5 23.5 3t16.5 -16q2 -8 7 -29.5t21 -43.5t45.5 -39.5t81.5 -17.5q45 0 80.5 15.5t59.5 40t37 54.5t13 61q0 29 -10 52t-32 40t-56.5 26t-84.5 9l7 51q99 2 150.5 44t51.5 116q0 31 -10 53t-27.5 37t-41 22t-51.5 7q-65 0 -109 -35.5t-65 -92.5 q-7 -13 -14 -17.5t-22 -2.5l-37 7q12 49 37.5 86.5t59 63t74.5 38.5t87 13q41 0 76 -11t60.5 -32t40 -52t14.5 -70q0 -77 -43.5 -125t-114.5 -68q66 -15 99 -53.5t33 -97.5q0 -54 -22 -97.5t-58.5 -75t-85 -48.5t-100.5 -17q-61 0 -101 15.5t-65 41.5t-38 60t-20 71z" />
<glyph unicode="&#xb4;" horiz-adv-x="753" d="M430 1189l175 244q12 18 24.5 25t35.5 7h89l-234 -259q-8 -10 -16 -13.5t-20 -3.5h-54z" />
<glyph unicode="&#xb5;" horiz-adv-x="1215" d="M89 -317l165 1336h97l-82 -671q-8 -128 42 -200.5t164 -72.5q96 0 181 56t153 159l90 729h97l-95 -775q-11 -93 29 -135.5t112 -42.5h42l-5 -40q-2 -13 -23 -24t-60 -11q-40 0 -74 12t-59.5 38t-39.5 65.5t-13 95.5q-74 -97 -162.5 -152.5t-187.5 -55.5 q-90 0 -146.5 36.5t-82.5 106.5q-2 -42 -5.5 -84t-8.5 -78l-41 -335h-48q-22 0 -31.5 11t-7.5 32z" />
<glyph unicode="&#xb6;" horiz-adv-x="1332" d="M125 1046q0 88 35.5 161.5t101 127.5t157 84t205.5 30h741l-10 -84h-231l-191 -1560h-88l191 1560h-336l-191 -1560h-87l112 911q-93 0 -169 23t-129.5 65.5t-82 103.5t-28.5 138z" />
<glyph unicode="&#xb7;" horiz-adv-x="482" d="M135 606q0 22 8.5 41.5t22 33.5t32.5 22.5t40 8.5q22 0 41 -8.5t33.5 -22.5t22.5 -33.5t8 -41.5q0 -20 -8 -39t-22.5 -33t-34 -22t-40.5 -8q-20 0 -39.5 8t-33 22t-22 33t-8.5 39z" />
<glyph unicode="&#xb8;" horiz-adv-x="753" d="M202 -323l14 29q6 11 17 11q5 0 12.5 -5.5t19 -11.5t29 -11.5t44.5 -5.5q52 0 80.5 25t28.5 68q0 23 -11 38t-31.5 25t-48.5 16t-62 11l62 146h58l-43 -108q77 -15 116 -43t39 -80q0 -35 -14.5 -62t-40.5 -46t-61.5 -29t-76.5 -10q-38 0 -73 12t-58 31z" />
<glyph unicode="&#xb9;" horiz-adv-x="684" d="M218 922l8 54h162l68 551l9 38l-165 -130q-11 -8 -20 -5.5t-14 7.5l-19 32l239 187h58l-83 -680h146l-7 -54h-382z" />
<glyph unicode="&#xba;" horiz-adv-x="770" d="M176 1105q0 81 22 147t62.5 113t98 72.5t129.5 25.5q56 0 100 -18.5t74 -52.5t46 -81.5t16 -106.5q0 -81 -22 -147t-62.5 -112.5t-98 -72t-127.5 -25.5q-57 0 -101.5 18.5t-74.5 52t-46 81.5t-16 106zM251 1104q0 -91 42 -145.5t128 -54.5q56 0 99 21.5t72 61t43.5 95 t14.5 122.5q0 91 -42 146t-126 55q-59 0 -102.5 -22t-72 -62t-42.5 -95.5t-14 -121.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="750" d="M88 201l209 302q13 20 25 28q-9 8 -19 29l-134 302q-7 15 -2.5 28t18.5 20l31 15l180 -387l-1 -13l-274 -388l-29 16q-27 14 -4 48zM328 201l209 302q13 20 25 28q-9 8 -19 29l-134 302q-7 15 -2.5 28t18.5 20l31 15l180 -387l-1 -13l-274 -388l-29 16q-27 14 -4 48z" />
<glyph unicode="&#xbc;" horiz-adv-x="1403" d="M149 718l8 54h162l68 551l9 38l-165 -130q-11 -8 -20 -5.5t-14 7.5l-19 32l239 187h58l-83 -680h146l-7 -54h-382zM186 0l928 1413q11 17 25.5 26.5t34.5 9.5h41l-932 -1417q-13 -18 -27 -25t-31 -7h-39zM727 252l420 479h67l-58 -474h129l-3 -40q-2 -8 -6.5 -12 t-13.5 -4h-113l-24 -201h-63l25 201h-334q-24 0 -24 16zM802 257h292l42 342q2 12 4 26.5t6 28.5z" />
<glyph unicode="&#xbd;" horiz-adv-x="1380" d="M145 0l928 1413q11 17 25.5 26.5t34.5 9.5h41l-932 -1417q-13 -18 -27 -25t-31 -7h-39zM152 718l8 54h162l68 551l9 38l-165 -130q-11 -8 -20 -5.5t-14 7.5l-19 32l239 187h58l-83 -680h146l-7 -54h-382zM735 0l2 25q2 7 5.5 15t11.5 15l272 243q35 32 66.5 62t54 62 t35.5 64.5t13 69.5q0 32 -10 55t-28 38.5t-42 23t-51 7.5q-63 0 -107.5 -36t-66.5 -94q-7 -11 -15 -16t-24 -3l-37 7q26 98 96 149.5t165 51.5q41 0 76.5 -11t61.5 -33.5t40.5 -55t14.5 -75.5q0 -46 -16 -85t-42 -73.5t-60.5 -67t-71.5 -66.5l-240 -217q18 5 38.5 8t40.5 3 h284q25 0 23 -24l-5 -42h-484z" />
<glyph unicode="&#xbe;" horiz-adv-x="1424" d="M130 898l33 14q12 5 23.5 3t16.5 -16q2 -8 7 -29.5t21 -43.5t45.5 -39.5t81.5 -17.5q45 0 80.5 15.5t59.5 40t37 54.5t13 61q0 29 -10 52t-32 40t-56.5 26t-84.5 9l7 51q99 2 150.5 44t51.5 116q0 31 -10 53t-27.5 37t-41 22t-51.5 7q-65 0 -109 -35.5t-65 -92.5 q-7 -13 -14 -17.5t-22 -2.5l-37 7q12 49 37.5 86.5t59 63t74.5 38.5t87 13q41 0 76 -11t60.5 -32t40 -52t14.5 -70q0 -77 -43.5 -125t-114.5 -68q66 -15 99 -53.5t33 -97.5q0 -54 -22 -97.5t-58.5 -75t-85 -48.5t-100.5 -17q-61 0 -101 15.5t-65 41.5t-38 60t-20 71zM215 0 l928 1413q11 17 25.5 26.5t34.5 9.5h41l-932 -1417q-13 -18 -27 -25t-31 -7h-39zM748 252l420 479h67l-58 -474h129l-3 -40q-2 -8 -6.5 -12t-13.5 -4h-113l-24 -201h-63l25 201h-334q-24 0 -24 16zM823 257h292l42 342q2 12 4 26.5t6 28.5z" />
<glyph unicode="&#xbf;" horiz-adv-x="803" d="M3 -101q0 84 25.5 143t64 101.5t84.5 74t87 59t71.5 56t37.5 66.5l33 168h62l-15 -175q-4 -44 -32 -76t-67.5 -61.5t-84 -58.5t-83 -67.5t-63.5 -90t-25 -124.5q0 -49 17 -88t45.5 -66t66.5 -41t80 -14q62 0 110 18.5t81.5 40t53.5 40t29 18.5q11 0 18 -12l22 -39 q-30 -28 -65 -54t-76 -46t-88 -32t-100 -12q-61 0 -114 19t-92 54t-61 85t-22 114zM399 940q0 19 7.5 36.5t20 30.5t29.5 20.5t38 7.5q19 0 36.5 -7.5t30 -20.5t20.5 -30.5t8 -36.5q0 -20 -8 -37t-20.5 -30t-30 -20t-36.5 -7q-40 0 -67.5 27t-27.5 67z" />
<glyph unicode="&#xc0;" horiz-adv-x="1246" d="M-48 0l723 1449h101l368 -1449h-79q-14 0 -22.5 7.5t-11.5 19.5l-103 424h-649l-208 -424q-5 -11 -16.5 -19t-24.5 -8h-78zM315 525h594l-179 738q-4 18 -8 40t-8 48q-10 -26 -20 -48t-18 -41zM458 1885h101q24 0 35 -3.5t20 -18.5l193 -284h-62q-11 0 -19 2t-15 10z" />
<glyph unicode="&#xc1;" horiz-adv-x="1246" d="M-48 0l723 1449h101l368 -1449h-79q-14 0 -22.5 7.5t-11.5 19.5l-103 424h-649l-208 -424q-5 -11 -16.5 -19t-24.5 -8h-78zM315 525h594l-179 738q-4 18 -8 40t-8 48q-10 -26 -20 -48t-18 -41zM633 1579l276 284q13 14 25 18t35 4h105l-341 -294q-10 -8 -18 -10t-21 -2 h-61z" />
<glyph unicode="&#xc2;" horiz-adv-x="1246" d="M-48 0l723 1449h101l368 -1449h-79q-14 0 -22.5 7.5t-11.5 19.5l-103 424h-649l-208 -424q-5 -11 -16.5 -19t-24.5 -8h-78zM315 525h594l-179 738q-4 18 -8 40t-8 48q-10 -26 -20 -48t-18 -41zM493 1579l253 273h78l190 -273h-67q-7 0 -17 3t-18 14l-129 191q-2 3 -3 5.5 t-2 5.5l-9 -11l-172 -191q-11 -11 -21 -14t-18 -3h-65z" />
<glyph unicode="&#xc3;" horiz-adv-x="1246" d="M-48 0l723 1449h101l368 -1449h-79q-14 0 -22.5 7.5t-11.5 19.5l-103 424h-649l-208 -424q-5 -11 -16.5 -19t-24.5 -8h-78zM315 525h594l-179 738q-4 18 -8 40t-8 48q-10 -26 -20 -48t-18 -41zM493 1590q4 35 17.5 67.5t35 57t49.5 39t62 14.5q37 0 66.5 -17.5 t55.5 -38.5t50.5 -39t51.5 -18q23 0 41.5 9t32.5 24.5t22.5 35.5t12.5 41h55q-4 -36 -17 -68t-34 -56.5t-49 -39t-63 -14.5q-36 0 -65 18t-55 39t-51.5 39t-51.5 18q-23 0 -41.5 -9.5t-32.5 -24.5t-23 -35t-12 -42h-57z" />
<glyph unicode="&#xc4;" horiz-adv-x="1246" d="M-48 0l723 1449h101l368 -1449h-79q-14 0 -22.5 7.5t-11.5 19.5l-103 424h-649l-208 -424q-5 -11 -16.5 -19t-24.5 -8h-78zM315 525h594l-179 738q-4 18 -8 40t-8 48q-10 -26 -20 -48t-18 -41zM485 1702q0 17 6.5 32.5t18.5 27.5t27 18.5t32 6.5q16 0 31.5 -6.5 t27.5 -18.5t19 -27.5t7 -32.5t-7 -32t-19 -26.5t-27.5 -18t-31.5 -6.5q-34 0 -59 24.5t-25 58.5zM852 1702q0 35 24.5 60t59.5 25q16 0 31.5 -6.5t26.5 -18.5t18.5 -27.5t7.5 -32.5t-7.5 -32t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-35 0 -59.5 24.5t-24.5 58.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1246" d="M-48 0l723 1449h101l368 -1449h-79q-14 0 -22.5 7.5t-11.5 19.5l-103 424h-649l-208 -424q-5 -11 -16.5 -19t-24.5 -8h-78zM315 525h594l-179 738q-4 18 -8 40t-8 48q-10 -26 -20 -48t-18 -41zM602 1725q4 35 20.5 65.5t40 52t53.5 33.5t62 12q33 0 60 -12t46.5 -33.5 t28.5 -52t5 -65.5q-4 -36 -20.5 -65.5t-40.5 -51t-54 -33.5t-62 -12t-59 12t-46.5 33.5t-28.5 51t-5 65.5zM653 1725q-6 -51 20.5 -83.5t74.5 -32.5q23 0 43.5 8.5t36.5 24t26.5 37t13.5 46.5q5 51 -21 84t-73 33q-48 0 -82 -33t-39 -84z" />
<glyph unicode="&#xc6;" horiz-adv-x="1766" d="M-53 0l908 1449h911l-10 -84h-769v-591h580l-12 -82h-568v-608h614l-13 -84h-696v451h-558l-264 -424q-7 -11 -18.5 -19t-25.5 -8h-79zM380 525h512v841q-10 -25 -21 -47.5t-24 -43.5z" />
<glyph unicode="&#xc7;" horiz-adv-x="1288" d="M121 612q0 189 58 346.5t158.5 270t237.5 174.5t295 62q75 0 134.5 -11.5t109 -34t91 -56.5t80.5 -77l-33 -41q-11 -12 -26 -12q-9 0 -21 10.5t-30 26.5t-44 35t-63.5 35t-87.5 26.5t-116 10.5q-135 0 -251.5 -53t-202.5 -151.5t-135.5 -238.5t-49.5 -314q0 -127 34 -229 t94.5 -173.5t144.5 -109t185 -37.5q75 0 134.5 12.5t105.5 31.5t79 41t56.5 41t38 32t22.5 13q9 0 14 -6l36 -43q-92 -93 -207.5 -148.5t-271.5 -60.5l-32 -80q77 -15 116.5 -43t39.5 -80q0 -35 -15 -62t-40.5 -46t-61 -29t-76.5 -10q-38 0 -73 12t-59 31l15 29q6 11 17 11 q5 0 12.5 -5.5t19 -11.5t29 -11.5t44.5 -5.5q52 0 80.5 25t28.5 68q0 23 -11 38t-31.5 25t-49 16t-61.5 11l50 119q-118 6 -212 53.5t-160.5 130t-102.5 195t-36 248.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1103" d="M127 0l178 1449h801l-11 -84h-698l-72 -590h580l-11 -82h-580l-74 -609h700l-11 -84h-802zM481 1885h101q24 0 35 -3.5t20 -18.5l193 -284h-62q-11 0 -19 2t-15 10z" />
<glyph unicode="&#xc9;" horiz-adv-x="1103" d="M127 0l178 1449h801l-11 -84h-698l-72 -590h580l-11 -82h-580l-74 -609h700l-11 -84h-802zM640 1579l276 284q13 14 25 18t35 4h105l-341 -294q-10 -8 -18 -10t-21 -2h-61z" />
<glyph unicode="&#xca;" horiz-adv-x="1103" d="M127 0l178 1449h801l-11 -84h-698l-72 -590h580l-11 -82h-580l-74 -609h700l-11 -84h-802zM457 1579l253 273h78l190 -273h-67q-7 0 -17 3t-18 14l-129 191q-2 3 -3 5.5t-2 5.5l-9 -11l-172 -191q-11 -11 -21 -14t-18 -3h-65z" />
<glyph unicode="&#xcb;" horiz-adv-x="1103" d="M127 0l178 1449h801l-11 -84h-698l-72 -590h580l-11 -82h-580l-74 -609h700l-11 -84h-802zM468 1702q0 17 6.5 32.5t18.5 27.5t27 18.5t32 6.5q16 0 31.5 -6.5t27.5 -18.5t19 -27.5t7 -32.5t-7 -32t-19 -26.5t-27.5 -18t-31.5 -6.5q-34 0 -59 24.5t-25 58.5zM835 1702 q0 35 24.5 60t59.5 25q16 0 31.5 -6.5t26.5 -18.5t18.5 -27.5t7.5 -32.5t-7.5 -32t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-35 0 -59.5 24.5t-24.5 58.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="506" d="M108 1885h101q24 0 35 -3.5t20 -18.5l193 -284h-62q-11 0 -19 2t-15 10zM127 0l177 1449h103l-178 -1449h-102z" />
<glyph unicode="&#xcd;" horiz-adv-x="506" d="M127 0l177 1449h103l-178 -1449h-102zM289 1579l276 284q13 14 25 18t35 4h105l-341 -294q-10 -8 -18 -10t-21 -2h-61z" />
<glyph unicode="&#xce;" horiz-adv-x="506" d="M116 1579l253 273h78l190 -273h-67q-7 0 -17 3t-18 14l-129 191q-2 3 -3 5.5t-2 5.5l-9 -11l-172 -191q-11 -11 -21 -14t-18 -3h-65zM127 0l177 1449h103l-178 -1449h-102z" />
<glyph unicode="&#xcf;" horiz-adv-x="506" d="M127 0l177 1449h103l-178 -1449h-102zM127 1702q0 17 6.5 32.5t18.5 27.5t27 18.5t32 6.5q16 0 31.5 -6.5t27.5 -18.5t19 -27.5t7 -32.5t-7 -32t-19 -26.5t-27.5 -18t-31.5 -6.5q-34 0 -59 24.5t-25 58.5zM494 1702q0 35 24.5 60t59.5 25q16 0 31.5 -6.5t26.5 -18.5 t18.5 -27.5t7.5 -32.5t-7.5 -32t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-35 0 -59.5 24.5t-24.5 58.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1454" d="M20 702l9 64h202l84 683h488q128 0 231 -43.5t175 -123.5t111 -193.5t39 -253.5q0 -191 -55.5 -346t-153.5 -263.5t-233 -167t-292 -58.5h-488l86 702h-203zM250 83h385q135 0 248 50.5t195.5 147t128.5 235.5t46 317q0 124 -32 223t-92 167.5t-145.5 105.5t-190.5 37 h-386l-74 -600h380l-7 -64h-380z" />
<glyph unicode="&#xd1;" horiz-adv-x="1436" d="M127 0l178 1449h50q14 0 20.5 -3.5t14.5 -15.5l703 -1262q1 14 1.5 27t2.5 24l151 1230h89l-178 -1449h-47q-13 0 -21.5 5.5t-16.5 17.5l-702 1261q-1 -12 -1.5 -23.5t-2.5 -22.5l-152 -1238h-89zM590 1589q4 35 17.5 67.5t35 57t49.5 39t62 14.5q37 0 66.5 -17.5 t55.5 -38.5t50.5 -39t51.5 -18q23 0 41.5 9t32.5 24.5t22.5 35.5t12.5 41h55q-4 -36 -17 -68t-34 -56.5t-49 -39t-63 -14.5q-36 0 -65 18t-55 39t-51.5 39t-51.5 18q-23 0 -41.5 -9.5t-32.5 -24.5t-23 -35t-12 -42h-57z" />
<glyph unicode="&#xd2;" horiz-adv-x="1508" d="M122 613q0 190 56 347.5t155 269.5t233.5 173.5t291.5 61.5q128 0 231 -45.5t175 -128.5t111 -199t39 -256q0 -191 -55.5 -348t-155 -268.5t-234.5 -173t-293 -61.5q-127 0 -229.5 45.5t-174.5 128.5t-111 198.5t-39 255.5zM225 616q0 -126 33 -226.5t93.5 -171.5 t145.5 -109t190 -38q135 0 249 52.5t196.5 152t129.5 240.5t47 318q0 126 -32.5 226.5t-93 172t-146 109.5t-190.5 38q-133 0 -247 -53t-197.5 -152.5t-130.5 -240.5t-47 -318zM633 1885h101q24 0 35 -3.5t20 -18.5l193 -284h-62q-11 0 -19 2t-15 10z" />
<glyph unicode="&#xd3;" horiz-adv-x="1508" d="M122 613q0 190 56 347.5t155 269.5t233.5 173.5t291.5 61.5q128 0 231 -45.5t175 -128.5t111 -199t39 -256q0 -191 -55.5 -348t-155 -268.5t-234.5 -173t-293 -61.5q-127 0 -229.5 45.5t-174.5 128.5t-111 198.5t-39 255.5zM225 616q0 -126 33 -226.5t93.5 -171.5 t145.5 -109t190 -38q135 0 249 52.5t196.5 152t129.5 240.5t47 318q0 126 -32.5 226.5t-93 172t-146 109.5t-190.5 38q-133 0 -247 -53t-197.5 -152.5t-130.5 -240.5t-47 -318zM756 1579l276 284q13 14 25 18t35 4h105l-341 -294q-10 -8 -18 -10t-21 -2h-61z" />
<glyph unicode="&#xd4;" horiz-adv-x="1508" d="M122 613q0 190 56 347.5t155 269.5t233.5 173.5t291.5 61.5q128 0 231 -45.5t175 -128.5t111 -199t39 -256q0 -191 -55.5 -348t-155 -268.5t-234.5 -173t-293 -61.5q-127 0 -229.5 45.5t-174.5 128.5t-111 198.5t-39 255.5zM225 616q0 -126 33 -226.5t93.5 -171.5 t145.5 -109t190 -38q135 0 249 52.5t196.5 152t129.5 240.5t47 318q0 126 -32.5 226.5t-93 172t-146 109.5t-190.5 38q-133 0 -247 -53t-197.5 -152.5t-130.5 -240.5t-47 -318zM606 1579l253 273h78l190 -273h-67q-7 0 -17 3t-18 14l-129 191q-2 3 -3 5.5t-2 5.5l-9 -11 l-172 -191q-11 -11 -21 -14t-18 -3h-65z" />
<glyph unicode="&#xd5;" horiz-adv-x="1508" d="M122 613q0 190 56 347.5t155 269.5t233.5 173.5t291.5 61.5q128 0 231 -45.5t175 -128.5t111 -199t39 -256q0 -191 -55.5 -348t-155 -268.5t-234.5 -173t-293 -61.5q-127 0 -229.5 45.5t-174.5 128.5t-111 198.5t-39 255.5zM225 616q0 -126 33 -226.5t93.5 -171.5 t145.5 -109t190 -38q135 0 249 52.5t196.5 152t129.5 240.5t47 318q0 126 -32.5 226.5t-93 172t-146 109.5t-190.5 38q-133 0 -247 -53t-197.5 -152.5t-130.5 -240.5t-47 -318zM606 1590q4 35 17.5 67.5t35 57t49.5 39t62 14.5q37 0 66.5 -17.5t55.5 -38.5t50.5 -39 t51.5 -18q23 0 41.5 9t32.5 24.5t22.5 35.5t12.5 41h55q-4 -36 -17 -68t-34 -56.5t-49 -39t-63 -14.5q-36 0 -65 18t-55 39t-51.5 39t-51.5 18q-23 0 -41.5 -9.5t-32.5 -24.5t-23 -35t-12 -42h-57z" />
<glyph unicode="&#xd6;" horiz-adv-x="1508" d="M122 613q0 190 56 347.5t155 269.5t233.5 173.5t291.5 61.5q128 0 231 -45.5t175 -128.5t111 -199t39 -256q0 -191 -55.5 -348t-155 -268.5t-234.5 -173t-293 -61.5q-127 0 -229.5 45.5t-174.5 128.5t-111 198.5t-39 255.5zM225 616q0 -126 33 -226.5t93.5 -171.5 t145.5 -109t190 -38q135 0 249 52.5t196.5 152t129.5 240.5t47 318q0 126 -32.5 226.5t-93 172t-146 109.5t-190.5 38q-133 0 -247 -53t-197.5 -152.5t-130.5 -240.5t-47 -318zM632 1702q0 17 6.5 32.5t18.5 27.5t27 18.5t32 6.5q16 0 31.5 -6.5t27.5 -18.5t19 -27.5 t7 -32.5t-7 -32t-19 -26.5t-27.5 -18t-31.5 -6.5q-34 0 -59 24.5t-25 58.5zM999 1702q0 35 24.5 60t59.5 25q16 0 31.5 -6.5t26.5 -18.5t18.5 -27.5t7.5 -32.5t-7.5 -32t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-35 0 -59.5 24.5t-24.5 58.5z" />
<glyph unicode="&#xd7;" d="M121 303l430 383l-332 378l60 53l330 -379l423 377l46 -53l-423 -376l334 -381l-56 -52l-336 382l-432 -384z" />
<glyph unicode="&#xd8;" horiz-adv-x="1508" d="M69 -108l209 260q-75 83 -115.5 200t-40.5 261q0 190 56 347.5t155 269.5t233.5 173.5t291.5 61.5q113 0 205 -35t163 -100l125 155q14 16 23.5 22.5t29.5 6.5h53l-186 -231q70 -83 106.5 -196t36.5 -251q0 -191 -55.5 -348t-155 -268.5t-234.5 -173t-293 -61.5 q-106 0 -195 31.5t-157 91.5l-148 -184q-14 -17 -31.5 -24.5t-33.5 -7.5h-42zM225 616q0 -121 30.5 -218t85.5 -168l823 1023q-59 61 -139 94t-178 33q-133 0 -247 -53t-197.5 -152.5t-130.5 -240.5t-47 -318zM384 183q58 -55 134.5 -83.5t168.5 -28.5q135 0 249 52.5 t196.5 152t129.5 240t47 318.5q0 114 -27 206.5t-76 162.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1385" d="M178 552l110 897h101l-109 -896q-12 -101 4.5 -189.5t60.5 -153.5t115.5 -101.5t166.5 -36.5t174.5 36.5t139 101t98.5 153t52 189.5l110 897h102l-110 -897q-15 -120 -63.5 -224t-123.5 -180.5t-174.5 -120.5t-217.5 -44q-117 0 -204.5 44t-144 120.5t-79 180.5 t-8.5 224zM542 1885h101q24 0 35 -3.5t20 -18.5l193 -284h-62q-11 0 -19 2t-15 10z" />
<glyph unicode="&#xda;" horiz-adv-x="1385" d="M178 552l110 897h101l-109 -896q-12 -101 4.5 -189.5t60.5 -153.5t115.5 -101.5t166.5 -36.5t174.5 36.5t139 101t98.5 153t52 189.5l110 897h102l-110 -897q-15 -120 -63.5 -224t-123.5 -180.5t-174.5 -120.5t-217.5 -44q-117 0 -204.5 44t-144 120.5t-79 180.5 t-8.5 224zM702 1579l276 284q13 14 25 18t35 4h105l-341 -294q-10 -8 -18 -10t-21 -2h-61z" />
<glyph unicode="&#xdb;" horiz-adv-x="1385" d="M178 552l110 897h101l-109 -896q-12 -101 4.5 -189.5t60.5 -153.5t115.5 -101.5t166.5 -36.5t174.5 36.5t139 101t98.5 153t52 189.5l110 897h102l-110 -897q-15 -120 -63.5 -224t-123.5 -180.5t-174.5 -120.5t-217.5 -44q-117 0 -204.5 44t-144 120.5t-79 180.5 t-8.5 224zM560 1579l253 273h78l190 -273h-67q-7 0 -17 3t-18 14l-129 191q-2 3 -3 5.5t-2 5.5l-9 -11l-172 -191q-11 -11 -21 -14t-18 -3h-65z" />
<glyph unicode="&#xdc;" horiz-adv-x="1385" d="M178 552l110 897h101l-109 -896q-12 -101 4.5 -189.5t60.5 -153.5t115.5 -101.5t166.5 -36.5t174.5 36.5t139 101t98.5 153t52 189.5l110 897h102l-110 -897q-15 -120 -63.5 -224t-123.5 -180.5t-174.5 -120.5t-217.5 -44q-117 0 -204.5 44t-144 120.5t-79 180.5 t-8.5 224zM568 1702q0 17 6.5 32.5t18.5 27.5t27 18.5t32 6.5q16 0 31.5 -6.5t27.5 -18.5t19 -27.5t7 -32.5t-7 -32t-19 -26.5t-27.5 -18t-31.5 -6.5q-34 0 -59 24.5t-25 58.5zM935 1702q0 35 24.5 60t59.5 25q16 0 31.5 -6.5t26.5 -18.5t18.5 -27.5t7.5 -32.5t-7.5 -32 t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-35 0 -59.5 24.5t-24.5 58.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1129" d="M123 1449h90q27 0 35 -26l306 -684q6 -17 11 -34t8 -34q8 17 17.5 34t20.5 34l475 684q7 11 16.5 18.5t23.5 7.5h85l-597 -852l-74 -597h-102l75 598zM586 1579l276 284q13 14 25 18t35 4h105l-341 -294q-10 -8 -18 -10t-21 -2h-61z" />
<glyph unicode="&#xde;" horiz-adv-x="1102" d="M127 0l177 1449h103l-36 -293h246q212 0 319 -95t107 -268q0 -111 -37.5 -202.5t-108 -157.5t-168.5 -103t-218 -37h-247l-36 -293h-101zM274 375h247q96 0 173.5 30.5t132.5 85t84.5 129t29.5 162.5q0 140 -83 216.5t-252 76.5h-246z" />
<glyph unicode="&#xdf;" horiz-adv-x="1115" d="M91 947l3 39h166q18 97 62.5 184.5t108 153.5t142 104t166.5 38q75 0 132 -23t94 -60t56 -84.5t19 -94.5q0 -72 -26 -123t-66 -90t-85.5 -69.5t-85.5 -60.5t-66 -63t-26 -77q0 -42 23.5 -68t59 -46t77 -39.5t76.5 -47.5t59 -71t24 -111q0 -78 -28 -143t-76.5 -112 t-115.5 -73t-146 -26q-48 0 -87.5 9t-73 25t-61.5 39t-52 51l26 33q10 15 30 16q12 0 28 -16.5t41.5 -35.5t65 -35.5t97.5 -16.5q59 0 108 20.5t83 56t52 83t18 101.5q0 57 -24 93t-60.5 61t-78.5 42.5t-79 40.5t-61 57t-24 88q0 57 27 99.5t67.5 76.5t88 65.5t88 67.5 t67.5 82.5t27 110.5q0 30 -11 64.5t-37.5 64t-69.5 49t-107 19.5q-67 0 -129 -34t-113.5 -94.5t-87 -143t-47.5 -178.5l-115 -923l-43 -236q-12 -42 -50 -42h-35l144 1169l-134 10q-29 3 -25 24z" />
<glyph unicode="&#xe0;" horiz-adv-x="1020" d="M69 342q0 89 23 174.5t64 162.5t98 141.5t127 111.5t148.5 73.5t162.5 26.5q60 0 114 -10.5t105 -37.5l-119 -984h-48q-20 0 -27 11.5t-7 25.5l34 317q-39 -82 -85.5 -150t-99.5 -116t-111.5 -74.5t-120.5 -26.5q-63 0 -112 25t-81.5 71t-48.5 111.5t-16 147.5zM167 357 q0 -137 46.5 -213t144.5 -76q58 0 114.5 32.5t108.5 92t97 143t81 184.5l52 419q-31 11 -63.5 15.5t-66.5 4.5q-102 0 -195.5 -50t-164 -134t-112.5 -192.5t-42 -225.5zM513 1465h87q23 0 33.5 -7t18.5 -25l120 -244h-51q-11 0 -19 3.5t-13 13.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="1020" d="M69 342q0 89 23 174.5t64 162.5t98 141.5t127 111.5t148.5 73.5t162.5 26.5q60 0 114 -10.5t105 -37.5l-119 -984h-48q-20 0 -27 11.5t-7 25.5l34 317q-39 -82 -85.5 -150t-99.5 -116t-111.5 -74.5t-120.5 -26.5q-63 0 -112 25t-81.5 71t-48.5 111.5t-16 147.5zM167 357 q0 -137 46.5 -213t144.5 -76q58 0 114.5 32.5t108.5 92t97 143t81 184.5l52 419q-31 11 -63.5 15.5t-66.5 4.5q-102 0 -195.5 -50t-164 -134t-112.5 -192.5t-42 -225.5zM640 1189l175 244q12 18 24.5 25t35.5 7h89l-234 -259q-8 -10 -16 -13.5t-20 -3.5h-54z" />
<glyph unicode="&#xe2;" horiz-adv-x="1020" d="M69 342q0 89 23 174.5t64 162.5t98 141.5t127 111.5t148.5 73.5t162.5 26.5q60 0 114 -10.5t105 -37.5l-119 -984h-48q-20 0 -27 11.5t-7 25.5l34 317q-39 -82 -85.5 -150t-99.5 -116t-111.5 -74.5t-120.5 -26.5q-63 0 -112 25t-81.5 71t-48.5 111.5t-16 147.5zM167 357 q0 -137 46.5 -213t144.5 -76q58 0 114.5 32.5t108.5 92t97 143t81 184.5l52 419q-31 11 -63.5 15.5t-66.5 4.5q-102 0 -195.5 -50t-164 -134t-112.5 -192.5t-42 -225.5zM414 1197l233 252h80l177 -252h-61q-6 0 -13.5 3t-12.5 11l-126 170l-9 13q-3 -4 -11 -13l-165 -170 q-12 -14 -29 -14h-63z" />
<glyph unicode="&#xe3;" horiz-adv-x="1020" d="M69 342q0 89 23 174.5t64 162.5t98 141.5t127 111.5t148.5 73.5t162.5 26.5q60 0 114 -10.5t105 -37.5l-119 -984h-48q-20 0 -27 11.5t-7 25.5l34 317q-39 -82 -85.5 -150t-99.5 -116t-111.5 -74.5t-120.5 -26.5q-63 0 -112 25t-81.5 71t-48.5 111.5t-16 147.5zM167 357 q0 -137 46.5 -213t144.5 -76q58 0 114.5 32.5t108.5 92t97 143t81 184.5l52 419q-31 11 -63.5 15.5t-66.5 4.5q-102 0 -195.5 -50t-164 -134t-112.5 -192.5t-42 -225.5zM437 1243q4 37 17 69.5t33 56t47 36.5t59 13t58 -17t48.5 -36.5t44.5 -36.5t47 -17q41 0 65 28t30 76 h56q-4 -37 -16.5 -69t-32.5 -55.5t-47 -37t-59 -13.5t-57.5 17t-48.5 37t-45.5 37t-47.5 17q-39 0 -63.5 -29.5t-30.5 -75.5h-57z" />
<glyph unicode="&#xe4;" horiz-adv-x="1020" d="M69 342q0 89 23 174.5t64 162.5t98 141.5t127 111.5t148.5 73.5t162.5 26.5q60 0 114 -10.5t105 -37.5l-119 -984h-48q-20 0 -27 11.5t-7 25.5l34 317q-39 -82 -85.5 -150t-99.5 -116t-111.5 -74.5t-120.5 -26.5q-63 0 -112 25t-81.5 71t-48.5 111.5t-16 147.5zM167 357 q0 -137 46.5 -213t144.5 -76q58 0 114.5 32.5t108.5 92t97 143t81 184.5l52 419q-31 11 -63.5 15.5t-66.5 4.5q-102 0 -195.5 -50t-164 -134t-112.5 -192.5t-42 -225.5zM452 1315q0 17 7 32.5t18.5 27t26.5 18.5t31 7t32.5 -7t27.5 -18.5t18 -27t7 -32.5q0 -16 -7 -31 t-18 -26.5t-27 -18t-33 -6.5q-16 0 -31 6.5t-26.5 18t-18.5 26t-7 31.5zM792 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 32.5 -7t27.5 -18.5t18 -27t7 -32.5q0 -16 -7 -31t-18 -26.5t-27 -18t-33 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1020" d="M69 342q0 89 23 174.5t64 162.5t98 141.5t127 111.5t148.5 73.5t162.5 26.5q60 0 114 -10.5t105 -37.5l-119 -984h-48q-20 0 -27 11.5t-7 25.5l34 317q-39 -82 -85.5 -150t-99.5 -116t-111.5 -74.5t-120.5 -26.5q-63 0 -112 25t-81.5 71t-48.5 111.5t-16 147.5zM167 357 q0 -137 46.5 -213t144.5 -76q58 0 114.5 32.5t108.5 92t97 143t81 184.5l52 419q-31 11 -63.5 15.5t-66.5 4.5q-102 0 -195.5 -50t-164 -134t-112.5 -192.5t-42 -225.5zM552 1333q4 37 20 68t41 53t55.5 34.5t64.5 12.5q33 0 61 -12.5t48 -34.5t29.5 -53t5.5 -68 q-5 -37 -21.5 -67t-41 -52.5t-55.5 -35t-64 -12.5q-34 0 -61.5 12.5t-47 35t-29 52.5t-5.5 67zM606 1333q-5 -51 20.5 -83.5t74.5 -32.5q47 0 81 32.5t40 83.5t-21 84t-74 33q-48 0 -81 -33t-40 -84z" />
<glyph unicode="&#xe6;" horiz-adv-x="1509" d="M37 219q0 77 36 141t112.5 110.5t197.5 74t291 32.5l11 87q17 142 -28.5 217.5t-153.5 75.5q-68 0 -117.5 -20.5t-85.5 -44.5t-59.5 -44.5t-37.5 -20.5q-17 0 -29 19l-12 28q83 80 166.5 121t184.5 41q117 0 176.5 -61.5t70.5 -169.5q58 109 151.5 169.5t214.5 60.5 q56 0 104 -16.5t83 -46.5t54 -71.5t19 -90.5q0 -59 -31.5 -112t-105 -93.5t-195.5 -65t-301 -25.5q-3 -40 -3 -84q0 -176 71 -270.5t195 -94.5q54 0 97.5 11.5t78.5 30t61 39t46.5 39t35.5 30t26 11.5q12 0 21 -10l23 -27q-49 -52 -96 -90.5t-96.5 -63.5t-102 -36.5 t-112.5 -11.5q-119 0 -199 72t-110 208q-29 -79 -75.5 -133t-100 -87.5t-110.5 -48t-107 -14.5q-57 0 -105 13.5t-82 42.5t-53 73.5t-19 105.5zM129 232q0 -47 13.5 -80.5t38 -55.5t58 -33t73.5 -11q62 0 119 23t103 66.5t76.5 107t40.5 145.5l16 120q-141 -5 -243 -25.5 t-167.5 -56t-96.5 -85.5t-31 -115zM760 573q155 5 258 25t166 51t89.5 71t26.5 86q0 74 -50 115.5t-134 41.5q-73 0 -132 -28t-104.5 -78.5t-76 -122t-43.5 -161.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="932" d="M78 393q0 81 17 160.5t48.5 151t76.5 132t101.5 104.5t122.5 69t141 25q50 0 90.5 -9.5t74 -27.5t61.5 -43.5t51 -57.5l-28 -34q-9 -11 -22 -11q-10 0 -25 16.5t-40 35.5t-65 35t-100 16q-86 0 -160.5 -46t-129 -123.5t-86 -179.5t-31.5 -213q0 -71 16 -131t49 -104 t83.5 -68.5t118.5 -24.5q54 0 97.5 11.5t76.5 29t58 38.5t44.5 38.5t33.5 29t25 11.5t22 -10l21 -27q-51 -57 -96.5 -94.5t-89.5 -60t-88.5 -32.5t-93.5 -12l-33 -83q77 -15 116 -43t39 -80q0 -35 -14.5 -62t-40 -46t-61.5 -29t-77 -10q-38 0 -73 12t-58 31l14 29 q6 11 18 11q5 0 12 -5.5t19 -11.5t29 -11.5t44 -5.5q52 0 81 25t29 68q0 23 -11.5 38t-32 25t-48.5 16t-62 11l52 122q-80 5 -139.5 37t-98.5 86t-58.5 125.5t-19.5 156.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="959" d="M77 387q0 77 15.5 155t45.5 149.5t74.5 134t102 108.5t127 72.5t150.5 26.5q75 0 126.5 -21t84 -52t46.5 -67.5t14 -68.5q0 -42 -11 -79.5t-40 -71.5t-78 -63.5t-125.5 -55t-183 -46t-249.5 -35.5q-2 -20 -3.5 -41.5t-1.5 -42.5q0 -153 69 -238.5t209 -85.5 q54 0 97.5 11.5t77.5 30t60.5 39t47 39t35 30t27.5 11.5q12 0 21 -10l23 -27q-50 -53 -98 -91.5t-96.5 -63t-101 -36t-111.5 -11.5q-85 0 -151 28t-111 79.5t-68.5 126t-23.5 166.5zM186 541q130 14 224.5 33t160 40t106.5 45.5t63 50.5t29.5 54t7.5 57q0 22 -9.5 47 t-32 45.5t-59 34.5t-89.5 14q-82 0 -148 -34.5t-117 -93t-85 -134.5t-51 -159zM400 1465h87q23 0 33.5 -7t18.5 -25l120 -244h-51q-11 0 -19 3.5t-13 13.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="959" d="M77 387q0 77 15.5 155t45.5 149.5t74.5 134t102 108.5t127 72.5t150.5 26.5q75 0 126.5 -21t84 -52t46.5 -67.5t14 -68.5q0 -42 -11 -79.5t-40 -71.5t-78 -63.5t-125.5 -55t-183 -46t-249.5 -35.5q-2 -20 -3.5 -41.5t-1.5 -42.5q0 -153 69 -238.5t209 -85.5 q54 0 97.5 11.5t77.5 30t60.5 39t47 39t35 30t27.5 11.5q12 0 21 -10l23 -27q-50 -53 -98 -91.5t-96.5 -63t-101 -36t-111.5 -11.5q-85 0 -151 28t-111 79.5t-68.5 126t-23.5 166.5zM186 541q130 14 224.5 33t160 40t106.5 45.5t63 50.5t29.5 54t7.5 57q0 22 -9.5 47 t-32 45.5t-59 34.5t-89.5 14q-82 0 -148 -34.5t-117 -93t-85 -134.5t-51 -159zM536 1189l175 244q12 18 24.5 25t35.5 7h89l-234 -259q-8 -10 -16 -13.5t-20 -3.5h-54z" />
<glyph unicode="&#xea;" horiz-adv-x="959" d="M77 387q0 77 15.5 155t45.5 149.5t74.5 134t102 108.5t127 72.5t150.5 26.5q75 0 126.5 -21t84 -52t46.5 -67.5t14 -68.5q0 -42 -11 -79.5t-40 -71.5t-78 -63.5t-125.5 -55t-183 -46t-249.5 -35.5q-2 -20 -3.5 -41.5t-1.5 -42.5q0 -153 69 -238.5t209 -85.5 q54 0 97.5 11.5t77.5 30t60.5 39t47 39t35 30t27.5 11.5q12 0 21 -10l23 -27q-50 -53 -98 -91.5t-96.5 -63t-101 -36t-111.5 -11.5q-85 0 -151 28t-111 79.5t-68.5 126t-23.5 166.5zM186 541q130 14 224.5 33t160 40t106.5 45.5t63 50.5t29.5 54t7.5 57q0 22 -9.5 47 t-32 45.5t-59 34.5t-89.5 14q-82 0 -148 -34.5t-117 -93t-85 -134.5t-51 -159zM348 1197l234 252h80l177 -252h-62q-6 0 -13 3t-12 11l-126 170l-10 13q-3 -4 -11 -13l-165 -170q-12 -14 -28 -14h-64z" />
<glyph unicode="&#xeb;" horiz-adv-x="959" d="M77 387q0 77 15.5 155t45.5 149.5t74.5 134t102 108.5t127 72.5t150.5 26.5q75 0 126.5 -21t84 -52t46.5 -67.5t14 -68.5q0 -42 -11 -79.5t-40 -71.5t-78 -63.5t-125.5 -55t-183 -46t-249.5 -35.5q-2 -20 -3.5 -41.5t-1.5 -42.5q0 -153 69 -238.5t209 -85.5 q54 0 97.5 11.5t77.5 30t60.5 39t47 39t35 30t27.5 11.5q12 0 21 -10l23 -27q-50 -53 -98 -91.5t-96.5 -63t-101 -36t-111.5 -11.5q-85 0 -151 28t-111 79.5t-68.5 126t-23.5 166.5zM186 541q130 14 224.5 33t160 40t106.5 45.5t63 50.5t29.5 54t7.5 57q0 22 -9.5 47 t-32 45.5t-59 34.5t-89.5 14q-82 0 -148 -34.5t-117 -93t-85 -134.5t-51 -159zM368 1315q0 17 7 32.5t18.5 27t26.5 18.5t31 7t32.5 -7t27.5 -18.5t18 -27t7 -32.5q0 -16 -7 -31t-18 -26.5t-27 -18t-33 -6.5q-16 0 -31 6.5t-26.5 18t-18.5 26t-7 31.5zM708 1315q0 17 7 32.5 t18.5 27t26.5 18.5t32 7q16 0 32.5 -7t27.5 -18.5t18 -27t7 -32.5q0 -16 -7 -31t-18 -26.5t-27 -18t-33 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5z" />
<glyph unicode="&#xec;" horiz-adv-x="433" d="M94 0l124 1019h95l-124 -1019h-95zM111 1465h87q23 0 33.5 -7t18.5 -25l120 -244h-51q-11 0 -19 3.5t-13 13.5z" />
<glyph unicode="&#xed;" horiz-adv-x="433" d="M94 0l124 1019h95l-124 -1019h-95zM201 1189l175 244q12 18 24.5 25t35.5 7h89l-234 -259q-8 -10 -16 -13.5t-20 -3.5h-54z" />
<glyph unicode="&#xee;" horiz-adv-x="433" d="M42 1197l233 252h80l177 -252h-61q-6 0 -13 3t-13 11l-126 170l-9 13q-3 -4 -11 -13l-165 -170q-12 -14 -29 -14h-63zM94 0l124 1019h95l-124 -1019h-95z" />
<glyph unicode="&#xef;" horiz-adv-x="433" d="M84 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 31.5 -7t27.5 -18.5t19 -27t7 -32.5q0 -16 -7 -31t-19 -26.5t-27 -18t-32 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5zM94 0l124 1019h95l-124 -1019h-95zM348 1315q0 35 25 60t59 25q16 0 31.5 -7t27 -18.5t18.5 -27 t7 -32.5q0 -16 -7 -31t-18.5 -26.5t-26.5 -18t-32 -6.5t-32.5 6.5t-26.5 18t-18 26t-7 31.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1067" d="M78 397q0 112 33 211t95 173.5t149.5 118t195.5 43.5q50 0 99 -14t92 -43t78.5 -74t58.5 -106q6 178 -41.5 303.5t-139.5 205.5l-175 -149l-17 30q-5 7 -4 15.5t8 14.5l144 124q-52 37 -112.5 62t-130.5 41q-33 9 -20 41l13 30q83 -14 162.5 -45t148.5 -84l165 140 l16 -30q4 -7 3.5 -16.5t-10.5 -16.5l-131 -112q45 -42 82 -94.5t63 -117t40 -141.5t14 -168q0 -163 -32.5 -300.5t-98 -238t-162.5 -156.5t-226 -56q-78 0 -144 29t-114 82.5t-75 129t-27 168.5zM172 402q0 -79 21.5 -141.5t58.5 -106.5t88 -68t109 -24q167 0 270.5 130 t137.5 391q-9 52 -30.5 103t-58 91.5t-88.5 65.5t-122 25q-92 0 -164 -36.5t-121.5 -100t-75 -148t-25.5 -181.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1056" d="M84 0l122 1022h44q36 0 36 -37l-29 -294q41 81 91.5 145t107 108t116 67t119.5 23q66 0 112.5 -25.5t74.5 -74.5t37 -120.5t-3 -162.5l-76 -651h-97l77 651q17 145 -19.5 223t-138.5 78q-57 0 -115 -29t-112.5 -83t-102.5 -130t-85 -171l-63 -539h-96zM351 1243 q4 37 17 69.5t33 56t47 36.5t59 13t58 -17t48.5 -36.5t44.5 -36.5t47 -17q41 0 65 28t30 76h56q-4 -37 -16.5 -69t-32.5 -55.5t-47 -37t-59 -13.5t-57.5 17t-48.5 37t-45.5 37t-47.5 17q-39 0 -63.5 -29.5t-30.5 -75.5h-57z" />
<glyph unicode="&#xf2;" horiz-adv-x="1050" d="M80 406q0 124 37 237.5t104 200.5t159 138.5t203 51.5q82 0 149 -28.5t114.5 -83t73.5 -131.5t26 -174q0 -124 -37.5 -237.5t-104 -201t-158.5 -139.5t-203 -52q-82 0 -149 28.5t-114.5 83t-73.5 131.5t-26 176zM177 406q0 -163 72.5 -253t202.5 -90q89 0 162 47 t125.5 124t80.5 177t28 205q0 164 -73 252t-202 88q-89 0 -162 -46t-125 -122t-80.5 -175.5t-28.5 -206.5zM427 1465h87q23 0 33.5 -7t18.5 -25l120 -244h-51q-11 0 -19 3.5t-13 13.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1050" d="M80 406q0 124 37 237.5t104 200.5t159 138.5t203 51.5q82 0 149 -28.5t114.5 -83t73.5 -131.5t26 -174q0 -124 -37.5 -237.5t-104 -201t-158.5 -139.5t-203 -52q-82 0 -149 28.5t-114.5 83t-73.5 131.5t-26 176zM177 406q0 -163 72.5 -253t202.5 -90q89 0 162 47 t125.5 124t80.5 177t28 205q0 164 -73 252t-202 88q-89 0 -162 -46t-125 -122t-80.5 -175.5t-28.5 -206.5zM510 1189l175 244q12 18 24.5 25t35.5 7h89l-234 -259q-8 -10 -16 -13.5t-20 -3.5h-54z" />
<glyph unicode="&#xf4;" horiz-adv-x="1050" d="M80 406q0 124 37 237.5t104 200.5t159 138.5t203 51.5q82 0 149 -28.5t114.5 -83t73.5 -131.5t26 -174q0 -124 -37.5 -237.5t-104 -201t-158.5 -139.5t-203 -52q-82 0 -149 28.5t-114.5 83t-73.5 131.5t-26 176zM177 406q0 -163 72.5 -253t202.5 -90q89 0 162 47 t125.5 124t80.5 177t28 205q0 164 -73 252t-202 88q-89 0 -162 -46t-125 -122t-80.5 -175.5t-28.5 -206.5zM356 1197l234 252h80l177 -252h-62q-6 0 -13 3t-12 11l-126 170l-9 13q-3 -4 -12 -13l-164 -170q-12 -14 -29 -14h-64z" />
<glyph unicode="&#xf5;" horiz-adv-x="1050" d="M80 406q0 124 37 237.5t104 200.5t159 138.5t203 51.5q82 0 149 -28.5t114.5 -83t73.5 -131.5t26 -174q0 -124 -37.5 -237.5t-104 -201t-158.5 -139.5t-203 -52q-82 0 -149 28.5t-114.5 83t-73.5 131.5t-26 176zM177 406q0 -163 72.5 -253t202.5 -90q89 0 162 47 t125.5 124t80.5 177t28 205q0 164 -73 252t-202 88q-89 0 -162 -46t-125 -122t-80.5 -175.5t-28.5 -206.5zM376 1243q4 37 17 69.5t33 56t47 36.5t59 13t58 -17t48.5 -36.5t44.5 -36.5t47 -17q41 0 65 28t30 76h56q-4 -37 -16.5 -69t-32.5 -55.5t-47 -37t-59 -13.5t-57.5 17 t-48.5 37t-45.5 37t-47.5 17q-39 0 -63.5 -29.5t-30.5 -75.5h-57z" />
<glyph unicode="&#xf6;" horiz-adv-x="1050" d="M80 406q0 124 37 237.5t104 200.5t159 138.5t203 51.5q82 0 149 -28.5t114.5 -83t73.5 -131.5t26 -174q0 -124 -37.5 -237.5t-104 -201t-158.5 -139.5t-203 -52q-82 0 -149 28.5t-114.5 83t-73.5 131.5t-26 176zM177 406q0 -163 72.5 -253t202.5 -90q89 0 162 47 t125.5 124t80.5 177t28 205q0 164 -73 252t-202 88q-89 0 -162 -46t-125 -122t-80.5 -175.5t-28.5 -206.5zM389 1315q0 17 7 32.5t18.5 27t26.5 18.5t31 7t32.5 -7t27.5 -18.5t18 -27t7 -32.5q0 -16 -7 -31t-18 -26.5t-27 -18t-33 -6.5q-16 0 -31 6.5t-26.5 18t-18.5 26 t-7 31.5zM729 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 32.5 -7t27.5 -18.5t18 -27t7 -32.5q0 -16 -7 -31t-18 -26.5t-27 -18t-33 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5z" />
<glyph unicode="&#xf7;" d="M117 650l8 73h961l-9 -73h-960zM461 309q0 22 8.5 41.5t23 34t33.5 23t40 8.5q36 0 59.5 -25t23.5 -61q0 -22 -8.5 -41t-23 -32.5t-34 -21.5t-40.5 -8q-36 0 -59 24t-23 58zM551 1038q0 22 8.5 41.5t23 34t33.5 23t40 8.5q36 0 59.5 -25t23.5 -61q0 -22 -8.5 -41 t-23.5 -32.5t-34.5 -21.5t-39.5 -8q-37 0 -59.5 24t-22.5 58z" />
<glyph unicode="&#xf8;" horiz-adv-x="1050" d="M40 -68l130 178q-43 53 -66.5 127.5t-23.5 168.5q0 124 37 238t104 201t159 138.5t203 51.5q70 0 127.5 -20.5t103.5 -59.5l63 85q12 16 21 23.5t30 7.5h51l-120 -163q42 -54 64.5 -126.5t22.5 -164.5q0 -124 -37.5 -237.5t-104 -201t-158.5 -139.5t-203 -52 q-136 0 -228 76l-72 -98q-13 -17 -30 -25t-34 -8h-39zM172 406q0 -71 15 -128.5t41 -100.5l531 715q-36 35 -82 53.5t-104 18.5q-89 0 -162.5 -46t-127 -122.5t-82.5 -178t-29 -211.5zM269 127q72 -69 183 -69q89 0 163 46.5t127 123.5t82 178t29 210q0 135 -53 224z" />
<glyph unicode="&#xf9;" horiz-adv-x="1056" d="M119 371l77 648h96l-77 -648q-17 -146 19.5 -224.5t138.5 -78.5q56 0 114 28.5t112 82t102.5 129t84.5 169.5l65 542h96l-123 -1019h-45q-19 0 -28 10t-9 28l31 290q-42 -81 -92 -144.5t-106 -107t-116 -66.5t-119 -23q-131 0 -187.5 100.5t-33.5 283.5zM417 1465h87 q23 0 33.5 -7t18.5 -25l120 -244h-51q-11 0 -19 3.5t-13 13.5z" />
<glyph unicode="&#xfa;" horiz-adv-x="1056" d="M119 371l77 648h96l-77 -648q-17 -146 19.5 -224.5t138.5 -78.5q56 0 114 28.5t112 82t102.5 129t84.5 169.5l65 542h96l-123 -1019h-45q-19 0 -28 10t-9 28l31 290q-42 -81 -92 -144.5t-106 -107t-116 -66.5t-119 -23q-131 0 -187.5 100.5t-33.5 283.5zM542 1189 l175 244q12 18 24.5 25t35.5 7h89l-234 -259q-8 -10 -16 -13.5t-20 -3.5h-54z" />
<glyph unicode="&#xfb;" horiz-adv-x="1056" d="M119 371l77 648h96l-77 -648q-17 -146 19.5 -224.5t138.5 -78.5q56 0 114 28.5t112 82t102.5 129t84.5 169.5l65 542h96l-123 -1019h-45q-19 0 -28 10t-9 28l31 290q-42 -81 -92 -144.5t-106 -107t-116 -66.5t-119 -23q-131 0 -187.5 100.5t-33.5 283.5zM352 1197 l234 252h80l177 -252h-62q-6 0 -13 3t-12 11l-126 170l-9 13q-3 -4 -12 -13l-165 -170q-12 -14 -28 -14h-64z" />
<glyph unicode="&#xfc;" horiz-adv-x="1056" d="M119 371l77 648h96l-77 -648q-17 -146 19.5 -224.5t138.5 -78.5q56 0 114 28.5t112 82t102.5 129t84.5 169.5l65 542h96l-123 -1019h-45q-19 0 -28 10t-9 28l31 290q-42 -81 -92 -144.5t-106 -107t-116 -66.5t-119 -23q-131 0 -187.5 100.5t-33.5 283.5zM358 1315 q0 17 7 32.5t18.5 27t26.5 18.5t31 7t32.5 -7t27.5 -18.5t18 -27t7 -32.5q0 -16 -7 -31t-18 -26.5t-27 -18t-33 -6.5q-16 0 -31 6.5t-26.5 18t-18.5 26t-7 31.5zM698 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 32.5 -7t27.5 -18.5t18 -27t7 -32.5q0 -16 -7 -31 t-18 -26.5t-27 -18t-33 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="951" d="M90 1019h76q14 0 21.5 -6.5t9.5 -18.5l215 -792q4 -15 5.5 -30.5t3.5 -31.5q6 15 13 30.5t15 31.5l410 795q5 11 14.5 16.5t18.5 5.5h75l-708 -1351q-14 -29 -42 -28h-69l216 399zM486 1189l175 244q12 18 24.5 25t35.5 7h89l-234 -259q-8 -10 -16 -13.5t-20 -3.5h-54z " />
<glyph unicode="&#xfe;" horiz-adv-x="1056" d="M39 -360l225 1850h95l-100 -816q40 81 89 147.5t104 113.5t115 73t121 26q67 0 117 -25.5t84 -72t51 -113t17 -148.5q0 -79 -16 -160.5t-46.5 -157.5t-74 -144t-98.5 -118t-121 -79t-140 -29q-83 0 -153 36.5t-115 109.5l-56 -454q-2 -17 -13.5 -28t-29.5 -11h-55z M203 211q22 -41 50 -69t60 -45.5t66.5 -25.5t70.5 -8q65 0 120.5 26.5t101 71.5t80.5 105t59.5 126.5t37 136.5t12.5 137q0 137 -52 213t-153 76q-58 0 -117.5 -32.5t-113.5 -92.5t-102.5 -144t-84.5 -186z" />
<glyph unicode="&#xff;" horiz-adv-x="951" d="M90 1019h76q14 0 21.5 -6.5t9.5 -18.5l215 -792q4 -15 5.5 -30.5t3.5 -31.5q6 15 13 30.5t15 31.5l410 795q5 11 14.5 16.5t18.5 5.5h75l-708 -1351q-14 -29 -42 -28h-69l216 399zM317 1315q0 17 7 32.5t18.5 27t26.5 18.5t31 7t32.5 -7t27.5 -18.5t18 -27t7 -32.5 q0 -16 -7 -31t-18 -26.5t-27 -18t-33 -6.5q-16 0 -31 6.5t-26.5 18t-18.5 26t-7 31.5zM657 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 32.5 -7t27.5 -18.5t18 -27t7 -32.5q0 -16 -7 -31t-18 -26.5t-27 -18t-33 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2085" d="M124 603q0 194 51.5 352.5t142.5 271.5t213 175.5t265 62.5q89 0 163 -27t132.5 -76.5t100 -121t64.5 -159.5l46 368h785l-9 -84h-695l-72 -591h575l-9 -82h-575l-75 -608h693l-11 -84h-786l40 326q-44 -80 -101.5 -143t-127.5 -106.5t-149.5 -66.5t-169.5 -23 q-114 0 -204 44.5t-154 125.5t-98.5 194.5t-34.5 251.5zM227 606q0 -123 29 -222t82 -169.5t129 -107.5t170 -37q123 0 227.5 54t180 154.5t118.5 244t43 321.5q0 123 -28 222t-81 169.5t-128.5 108.5t-170.5 38q-122 0 -226.5 -54.5t-181 -156t-120 -245t-43.5 -320.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1652" d="M78 346q0 165 40 293t109 216t162 134t201 46q124 0 197.5 -70t97.5 -190q59 121 159.5 190.5t233.5 69.5q62 0 112 -16.5t84.5 -45.5t52.5 -69.5t18 -89.5q0 -57 -27.5 -109t-101 -95t-201.5 -76.5t-329 -55.5q-1 -11 -1 -23.5v-24.5q0 -176 71 -270.5t195 -94.5 q54 0 97.5 11.5t78 30t61 39t47 39t35.5 30t26 11.5q12 0 22 -10l22 -27q-49 -52 -96 -90.5t-96.5 -63.5t-102 -36.5t-111.5 -11.5q-124 0 -205.5 78t-108.5 225q-31 -71 -74.5 -127.5t-97.5 -95t-118 -59.5t-138 -21q-81 0 -140 29.5t-97.5 79t-57.5 114.5t-19 136z M174 352q0 -55 13 -107t41.5 -93t74.5 -66t113 -25q91 0 162 40.5t122 113.5t80 172.5t36 219.5q5 78 -6.5 143t-41 112t-76.5 73t-112 26q-92 0 -167 -43.5t-128.5 -122.5t-82 -191t-28.5 -252zM891 541q178 20 290 50t175 65t85.5 75t22.5 80q0 31 -11.5 58.5t-36 48.5 t-62.5 34t-90 13q-74 0 -136.5 -30t-111 -85t-81 -133.5t-44.5 -175.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1129" d="M123 1449h90q27 0 35 -26l306 -684q6 -17 11 -34t8 -34q8 17 17.5 34t20.5 34l475 684q7 11 16.5 18.5t23.5 7.5h85l-597 -852l-74 -597h-102l75 598zM421 1702q0 17 6.5 32.5t18.5 27.5t27 18.5t32 6.5q16 0 31.5 -6.5t27.5 -18.5t19 -27.5t7 -32.5t-7 -32t-19 -26.5 t-27.5 -18t-31.5 -6.5q-34 0 -59 24.5t-25 58.5zM788 1702q0 35 24.5 60t59.5 25q16 0 31.5 -6.5t26.5 -18.5t18.5 -27.5t7.5 -32.5t-7.5 -32t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-35 0 -59.5 24.5t-24.5 58.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="753" d="M266 1197l234 252h80l177 -252h-62q-6 0 -13 3t-12 11l-126 170l-9 13q-3 -4 -12 -13l-165 -170q-12 -14 -28 -14h-64z" />
<glyph unicode="&#x2dc;" horiz-adv-x="753" d="M278 1243q4 37 17 69.5t33 56t47 36.5t59 13t58 -17t48.5 -36.5t44.5 -36.5t47 -17q41 0 65 28t30 76h56q-4 -37 -16.5 -69t-32.5 -55.5t-47 -37t-59 -13.5t-57.5 17t-48.5 37t-45.5 37t-47.5 17q-39 0 -63.5 -29.5t-30.5 -75.5h-57z" />
<glyph unicode="&#x2000;" horiz-adv-x="944" />
<glyph unicode="&#x2001;" horiz-adv-x="1888" />
<glyph unicode="&#x2002;" horiz-adv-x="944" />
<glyph unicode="&#x2003;" horiz-adv-x="1888" />
<glyph unicode="&#x2004;" horiz-adv-x="629" />
<glyph unicode="&#x2005;" horiz-adv-x="472" />
<glyph unicode="&#x2006;" horiz-adv-x="314" />
<glyph unicode="&#x2007;" horiz-adv-x="314" />
<glyph unicode="&#x2008;" horiz-adv-x="236" />
<glyph unicode="&#x2009;" horiz-adv-x="377" />
<glyph unicode="&#x200a;" horiz-adv-x="104" />
<glyph unicode="&#x2010;" horiz-adv-x="729" d="M137 567l11 80h439l-9 -80h-441z" />
<glyph unicode="&#x2011;" horiz-adv-x="729" d="M137 567l11 80h439l-9 -80h-441z" />
<glyph unicode="&#x2012;" horiz-adv-x="729" d="M137 567l11 80h439l-9 -80h-441z" />
<glyph unicode="&#x2013;" d="M214 556l8 71h743l-9 -71h-742z" />
<glyph unicode="&#x2014;" horiz-adv-x="1507" d="M134 556l8 71h1224l-10 -71h-1222z" />
<glyph unicode="&#x2018;" horiz-adv-x="400" d="M199.5 1203q1.5 60 22.5 116t58.5 108t87.5 96l23 -17q6 -5 6 -12q0 -5 -2.5 -10t-10.5 -14q-29 -34 -52 -73t-36 -81t-12.5 -85.5t18.5 -86.5q5 -10 1 -19.5t-16 -14.5l-64 -26q-25 59 -23.5 119z" />
<glyph unicode="&#x2019;" horiz-adv-x="408" d="M182 1101q0 5 3 9.5t11 13.5q29 34 52 73.5t36 81.5t13 85.5t-19 86.5q-10 23 16 34l63 25q25 -59 24 -118.5t-22 -116t-59 -108t-87 -95.5l-24 16q-7 5 -7 13z" />
<glyph unicode="&#x201a;" horiz-adv-x="393" d="M-1 -231q0 5 3 9.5t11 13.5q29 34 52 73.5t36 81.5t13 85.5t-19 86.5q-10 23 16 34l63 25q25 -59 24 -118.5t-22 -116t-59 -108t-87 -95.5l-24 16q-7 5 -7 13z" />
<glyph unicode="&#x201c;" horiz-adv-x="654" d="M199.5 1203q1.5 60 22.5 116t58.5 108t87.5 96l23 -17q6 -5 6 -12q0 -5 -2.5 -10t-10.5 -14q-29 -34 -52 -73t-36 -81t-12.5 -85.5t18.5 -86.5q5 -10 1 -19.5t-16 -14.5l-64 -26q-25 59 -23.5 119zM453.5 1203q1.5 60 22.5 116t58.5 108t87.5 96l23 -17q6 -5 6 -12 q0 -5 -2.5 -10t-10.5 -14q-29 -34 -52 -73t-36 -81t-12.5 -85.5t18.5 -86.5q5 -10 1 -19.5t-16 -14.5l-64 -26q-25 59 -23.5 119z" />
<glyph unicode="&#x201d;" horiz-adv-x="660" d="M182 1101q0 5 3 9.5t11 13.5q29 34 52 73.5t36 81.5t13 85.5t-19 86.5q-10 23 16 34l63 25q25 -59 24 -118.5t-22 -116t-59 -108t-87 -95.5l-24 16q-7 5 -7 13zM436 1101q0 5 3 9.5t11 13.5q29 34 52 73.5t36 81.5t13 85.5t-19 86.5q-10 23 16 34l63 25q25 -59 24 -118.5 t-22 -116t-59 -108t-87 -95.5l-24 16q-7 5 -7 13z" />
<glyph unicode="&#x201e;" horiz-adv-x="647" d="M-1 -231q0 5 3 9.5t11 13.5q29 34 52 73.5t36 81.5t13 85.5t-19 86.5q-10 23 16 34l63 25q25 -59 24 -118.5t-22 -116t-59 -108t-87 -95.5l-24 16q-7 5 -7 13zM253 -231q0 5 3 9.5t11 13.5q29 34 52 73.5t36 81.5t13 85.5t-19 86.5q-10 23 16 34l63 25q25 -59 24 -118.5 t-22 -116t-59 -108t-87 -95.5l-24 16q-7 5 -7 13z" />
<glyph unicode="&#x2022;" d="M291 610q0 62 23.5 117.5t64.5 97t95 65t116 23.5t117.5 -23.5t96.5 -65t64.5 -96.5t23.5 -118q0 -61 -23.5 -115t-64.5 -95t-96.5 -64.5t-117.5 -23.5q-61 0 -115.5 23.5t-95.5 64.5t-64.5 95t-23.5 115z" />
<glyph unicode="&#x2026;" horiz-adv-x="1474" d="M78 79q0 19 7 36.5t20 30.5t30 20.5t37 7.5q19 0 36.5 -7.5t30.5 -20.5t20.5 -30.5t7.5 -36.5q0 -20 -7.5 -37t-20.5 -30t-30.5 -20t-36.5 -7q-40 0 -67 27t-27 67zM580 79q0 19 7 36.5t20 30.5t29.5 20.5t37.5 7.5q19 0 37 -7.5t31 -20.5t20.5 -30.5t7.5 -36.5 q0 -40 -28 -67t-68 -27t-67 27t-27 67zM1082 79q0 19 7.5 36.5t20 30.5t29.5 20.5t38 7.5q19 0 36.5 -7.5t30 -20.5t20.5 -30.5t8 -36.5q0 -20 -8 -37t-20.5 -30t-30 -20t-36.5 -7q-40 0 -67.5 27t-27.5 67z" />
<glyph unicode="&#x202f;" horiz-adv-x="377" />
<glyph unicode="&#x2039;" horiz-adv-x="512" d="M94 525l1 13l277 387l27 -15q11 -7 13 -19.5t-10 -28.5l-207 -302q-15 -22 -26 -29q9 -8 18 -28l135 -302q7 -16 3 -28.5t-18 -19.5l-33 -16z" />
<glyph unicode="&#x203a;" horiz-adv-x="509" d="M87 201l209 302q13 20 25 28q-9 8 -19 29l-134 302q-7 15 -2.5 28t18.5 20l31 15l180 -387l-1 -13l-274 -388l-29 16q-27 14 -4 48z" />
<glyph unicode="&#x205f;" horiz-adv-x="472" />
<glyph unicode="&#x20ac;" d="M17 584l7 58h160q2 48 6.5 93.5t12.5 88.5h-156l6 59h162q31 136 88.5 243.5t138.5 182.5t182.5 115t218.5 40q66 0 120 -11.5t98.5 -34t82 -56t69.5 -76.5l-35 -35q-5 -6 -10 -9t-13 -3t-18.5 11t-25.5 26.5t-37.5 34t-55.5 34.5t-77.5 26.5t-103.5 10.5 q-97 0 -181.5 -32.5t-152 -96.5t-116.5 -157.5t-77 -212.5h628l-3 -30q-2 -11 -10.5 -20t-25.5 -9h-600q-7 -43 -11 -88t-6 -94h533l-3 -30q-2 -12 -11 -20t-25 -8h-495q0 -125 26.5 -221.5t76 -162t121.5 -99.5t163 -34q63 0 114 12t90 31t69 41t51 41t35 31.5t24 12.5 q8 0 16 -10l33 -33q-42 -47 -90 -85.5t-103.5 -66t-119 -42t-135.5 -14.5q-113 0 -200 41.5t-147 118.5t-91 187.5t-31 248.5v3h-166z" />
<glyph unicode="&#x2122;" horiz-adv-x="1399" d="M177 1390l9 59h444l-7 -59h-187l-65 -530h-70l65 530h-189zM675 860l70 589h57q10 0 14 -2.5t9 -10.5l157 -375q5 -16 8 -29q7 16 16 29l243 375q6 8 11 10.5t15 2.5h56l-71 -589h-62l56 457l10 43l-251 -390q-9 -15 -28 -16h-10q-8 0 -14.5 4t-8.5 12l-158 387l-1 -40 l-58 -457h-60z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1018" d="M0 0v1019h1019v-1019h-1019z" />
<hkern u1="&#x20;" u2="&#x178;" k="78" />
<hkern u1="&#x20;" u2="&#xff;" k="49" />
<hkern u1="&#x20;" u2="&#xfd;" k="49" />
<hkern u1="&#x20;" u2="&#xe6;" k="53" />
<hkern u1="&#x20;" u2="&#xdd;" k="78" />
<hkern u1="&#x20;" u2="&#xc6;" k="67" />
<hkern u1="&#x20;" u2="&#xc5;" k="57" />
<hkern u1="&#x20;" u2="&#xc4;" k="57" />
<hkern u1="&#x20;" u2="&#xc3;" k="57" />
<hkern u1="&#x20;" u2="&#xc2;" k="57" />
<hkern u1="&#x20;" u2="&#xc1;" k="57" />
<hkern u1="&#x20;" u2="&#xc0;" k="57" />
<hkern u1="&#x20;" u2="y" k="49" />
<hkern u1="&#x20;" u2="w" k="33" />
<hkern u1="&#x20;" u2="v" k="49" />
<hkern u1="&#x20;" u2="t" k="25" />
<hkern u1="&#x20;" u2="Y" k="78" />
<hkern u1="&#x20;" u2="W" k="45" />
<hkern u1="&#x20;" u2="V" k="71" />
<hkern u1="&#x20;" u2="T" k="62" />
<hkern u1="&#x20;" u2="J" k="52" />
<hkern u1="&#x20;" u2="A" k="57" />
<hkern u1="&#x22;" u2="&#xf0;" k="49" />
<hkern u1="&#x22;" u2="&#xef;" k="-12" />
<hkern u1="&#x22;" u2="&#xee;" k="-16" />
<hkern u1="&#x22;" u2="&#xec;" k="-13" />
<hkern u1="&#x22;" u2="&#x2f;" k="105" />
<hkern u1="&#x22;" u2="&#x2c;" k="188" />
<hkern u1="&#x22;" u2="&#x26;" k="48" />
<hkern u1="&#x26;" u2="&#x201d;" k="81" />
<hkern u1="&#x26;" u2="&#x2019;" k="81" />
<hkern u1="&#x26;" u2="&#x178;" k="94" />
<hkern u1="&#x26;" u2="&#xff;" k="31" />
<hkern u1="&#x26;" u2="&#xfd;" k="31" />
<hkern u1="&#x26;" u2="&#xdd;" k="94" />
<hkern u1="&#x26;" u2="&#xc6;" k="-19" />
<hkern u1="&#x26;" u2="&#xc5;" k="-20" />
<hkern u1="&#x26;" u2="&#xc4;" k="-20" />
<hkern u1="&#x26;" u2="&#xc3;" k="-20" />
<hkern u1="&#x26;" u2="&#xc2;" k="-20" />
<hkern u1="&#x26;" u2="&#xc1;" k="-20" />
<hkern u1="&#x26;" u2="&#xc0;" k="-20" />
<hkern u1="&#x26;" u2="y" k="31" />
<hkern u1="&#x26;" u2="x" k="-18" />
<hkern u1="&#x26;" u2="v" k="30" />
<hkern u1="&#x26;" u2="g" k="-9" />
<hkern u1="&#x26;" u2="Z" k="-9" />
<hkern u1="&#x26;" u2="Y" k="94" />
<hkern u1="&#x26;" u2="X" k="-23" />
<hkern u1="&#x26;" u2="W" k="52" />
<hkern u1="&#x26;" u2="V" k="77" />
<hkern u1="&#x26;" u2="T" k="85" />
<hkern u1="&#x26;" u2="J" k="-4" />
<hkern u1="&#x26;" u2="A" k="-20" />
<hkern u1="&#x26;" u2="&#x27;" k="73" />
<hkern u1="&#x26;" u2="&#x22;" k="73" />
<hkern u1="&#x27;" u2="&#xf0;" k="49" />
<hkern u1="&#x27;" u2="&#xef;" k="-12" />
<hkern u1="&#x27;" u2="&#xee;" k="-16" />
<hkern u1="&#x27;" u2="&#xec;" k="-13" />
<hkern u1="&#x27;" u2="&#x2f;" k="105" />
<hkern u1="&#x27;" u2="&#x2c;" k="188" />
<hkern u1="&#x27;" u2="&#x26;" k="48" />
<hkern u1="&#x28;" u2="&#x153;" k="57" />
<hkern u1="&#x28;" u2="&#x152;" k="41" />
<hkern u1="&#x28;" u2="&#xff;" k="32" />
<hkern u1="&#x28;" u2="&#xfd;" k="32" />
<hkern u1="&#x28;" u2="&#xfc;" k="43" />
<hkern u1="&#x28;" u2="&#xfb;" k="43" />
<hkern u1="&#x28;" u2="&#xfa;" k="43" />
<hkern u1="&#x28;" u2="&#xf9;" k="43" />
<hkern u1="&#x28;" u2="&#xf8;" k="57" />
<hkern u1="&#x28;" u2="&#xf6;" k="57" />
<hkern u1="&#x28;" u2="&#xf5;" k="57" />
<hkern u1="&#x28;" u2="&#xf4;" k="57" />
<hkern u1="&#x28;" u2="&#xf3;" k="57" />
<hkern u1="&#x28;" u2="&#xf2;" k="57" />
<hkern u1="&#x28;" u2="&#xf1;" k="25" />
<hkern u1="&#x28;" u2="&#xf0;" k="48" />
<hkern u1="&#x28;" u2="&#xef;" k="-12" />
<hkern u1="&#x28;" u2="&#xec;" k="-23" />
<hkern u1="&#x28;" u2="&#xeb;" k="57" />
<hkern u1="&#x28;" u2="&#xea;" k="57" />
<hkern u1="&#x28;" u2="&#xe9;" k="57" />
<hkern u1="&#x28;" u2="&#xe8;" k="57" />
<hkern u1="&#x28;" u2="&#xe7;" k="57" />
<hkern u1="&#x28;" u2="&#xe6;" k="53" />
<hkern u1="&#x28;" u2="&#xe5;" k="53" />
<hkern u1="&#x28;" u2="&#xe4;" k="53" />
<hkern u1="&#x28;" u2="&#xe3;" k="53" />
<hkern u1="&#x28;" u2="&#xe2;" k="53" />
<hkern u1="&#x28;" u2="&#xe1;" k="53" />
<hkern u1="&#x28;" u2="&#xe0;" k="53" />
<hkern u1="&#x28;" u2="&#xd8;" k="41" />
<hkern u1="&#x28;" u2="&#xd6;" k="41" />
<hkern u1="&#x28;" u2="&#xd5;" k="41" />
<hkern u1="&#x28;" u2="&#xd4;" k="41" />
<hkern u1="&#x28;" u2="&#xd3;" k="41" />
<hkern u1="&#x28;" u2="&#xd2;" k="41" />
<hkern u1="&#x28;" u2="&#xc7;" k="41" />
<hkern u1="&#x28;" u2="y" k="32" />
<hkern u1="&#x28;" u2="w" k="43" />
<hkern u1="&#x28;" u2="v" k="49" />
<hkern u1="&#x28;" u2="u" k="43" />
<hkern u1="&#x28;" u2="t" k="10" />
<hkern u1="&#x28;" u2="s" k="26" />
<hkern u1="&#x28;" u2="r" k="25" />
<hkern u1="&#x28;" u2="q" k="53" />
<hkern u1="&#x28;" u2="p" k="25" />
<hkern u1="&#x28;" u2="o" k="57" />
<hkern u1="&#x28;" u2="n" k="25" />
<hkern u1="&#x28;" u2="m" k="25" />
<hkern u1="&#x28;" u2="j" k="-12" />
<hkern u1="&#x28;" u2="e" k="57" />
<hkern u1="&#x28;" u2="d" k="54" />
<hkern u1="&#x28;" u2="c" k="57" />
<hkern u1="&#x28;" u2="a" k="53" />
<hkern u1="&#x28;" u2="Q" k="41" />
<hkern u1="&#x28;" u2="O" k="41" />
<hkern u1="&#x28;" u2="G" k="41" />
<hkern u1="&#x28;" u2="C" k="41" />
<hkern u1="&#x2a;" u2="&#x153;" k="68" />
<hkern u1="&#x2a;" u2="&#xf8;" k="68" />
<hkern u1="&#x2a;" u2="&#xf6;" k="68" />
<hkern u1="&#x2a;" u2="&#xf5;" k="68" />
<hkern u1="&#x2a;" u2="&#xf4;" k="68" />
<hkern u1="&#x2a;" u2="&#xf3;" k="68" />
<hkern u1="&#x2a;" u2="&#xf2;" k="68" />
<hkern u1="&#x2a;" u2="&#xf1;" k="18" />
<hkern u1="&#x2a;" u2="&#xf0;" k="63" />
<hkern u1="&#x2a;" u2="&#xef;" k="-18" />
<hkern u1="&#x2a;" u2="&#xee;" k="-4" />
<hkern u1="&#x2a;" u2="&#xeb;" k="68" />
<hkern u1="&#x2a;" u2="&#xea;" k="68" />
<hkern u1="&#x2a;" u2="&#xe9;" k="68" />
<hkern u1="&#x2a;" u2="&#xe8;" k="68" />
<hkern u1="&#x2a;" u2="&#xe7;" k="68" />
<hkern u1="&#x2a;" u2="&#xe6;" k="86" />
<hkern u1="&#x2a;" u2="&#xe5;" k="86" />
<hkern u1="&#x2a;" u2="&#xe4;" k="86" />
<hkern u1="&#x2a;" u2="&#xe3;" k="86" />
<hkern u1="&#x2a;" u2="&#xe2;" k="86" />
<hkern u1="&#x2a;" u2="&#xe1;" k="86" />
<hkern u1="&#x2a;" u2="&#xe0;" k="86" />
<hkern u1="&#x2a;" u2="&#xc6;" k="186" />
<hkern u1="&#x2a;" u2="&#xc5;" k="127" />
<hkern u1="&#x2a;" u2="&#xc4;" k="127" />
<hkern u1="&#x2a;" u2="&#xc3;" k="127" />
<hkern u1="&#x2a;" u2="&#xc2;" k="127" />
<hkern u1="&#x2a;" u2="&#xc1;" k="127" />
<hkern u1="&#x2a;" u2="&#xc0;" k="127" />
<hkern u1="&#x2a;" u2="s" k="49" />
<hkern u1="&#x2a;" u2="r" k="18" />
<hkern u1="&#x2a;" u2="q" k="86" />
<hkern u1="&#x2a;" u2="p" k="18" />
<hkern u1="&#x2a;" u2="o" k="68" />
<hkern u1="&#x2a;" u2="n" k="18" />
<hkern u1="&#x2a;" u2="m" k="18" />
<hkern u1="&#x2a;" u2="g" k="55" />
<hkern u1="&#x2a;" u2="e" k="68" />
<hkern u1="&#x2a;" u2="d" k="79" />
<hkern u1="&#x2a;" u2="c" k="68" />
<hkern u1="&#x2a;" u2="a" k="86" />
<hkern u1="&#x2a;" u2="J" k="156" />
<hkern u1="&#x2a;" u2="A" k="127" />
<hkern u1="&#x2c;" g2="uniFB04" k="7" />
<hkern u1="&#x2c;" g2="uniFB03" k="7" />
<hkern u1="&#x2c;" g2="uniFB02" k="7" />
<hkern u1="&#x2c;" g2="uniFB01" k="7" />
<hkern u1="&#x2c;" u2="&#x201d;" k="204" />
<hkern u1="&#x2c;" u2="&#x201c;" k="201" />
<hkern u1="&#x2c;" u2="&#x2019;" k="204" />
<hkern u1="&#x2c;" u2="&#x2018;" k="201" />
<hkern u1="&#x2c;" u2="&#x178;" k="165" />
<hkern u1="&#x2c;" u2="&#x152;" k="90" />
<hkern u1="&#x2c;" u2="&#xff;" k="75" />
<hkern u1="&#x2c;" u2="&#xfd;" k="75" />
<hkern u1="&#x2c;" u2="&#xdf;" k="7" />
<hkern u1="&#x2c;" u2="&#xdd;" k="165" />
<hkern u1="&#x2c;" u2="&#xdc;" k="78" />
<hkern u1="&#x2c;" u2="&#xdb;" k="78" />
<hkern u1="&#x2c;" u2="&#xda;" k="78" />
<hkern u1="&#x2c;" u2="&#xd9;" k="78" />
<hkern u1="&#x2c;" u2="&#xd8;" k="90" />
<hkern u1="&#x2c;" u2="&#xd6;" k="90" />
<hkern u1="&#x2c;" u2="&#xd5;" k="90" />
<hkern u1="&#x2c;" u2="&#xd4;" k="90" />
<hkern u1="&#x2c;" u2="&#xd3;" k="90" />
<hkern u1="&#x2c;" u2="&#xd2;" k="90" />
<hkern u1="&#x2c;" u2="&#xc7;" k="90" />
<hkern u1="&#x2c;" u2="y" k="75" />
<hkern u1="&#x2c;" u2="w" k="58" />
<hkern u1="&#x2c;" u2="v" k="85" />
<hkern u1="&#x2c;" u2="t" k="9" />
<hkern u1="&#x2c;" u2="f" k="7" />
<hkern u1="&#x2c;" u2="Y" k="165" />
<hkern u1="&#x2c;" u2="W" k="126" />
<hkern u1="&#x2c;" u2="V" k="167" />
<hkern u1="&#x2c;" u2="U" k="78" />
<hkern u1="&#x2c;" u2="T" k="151" />
<hkern u1="&#x2c;" u2="Q" k="90" />
<hkern u1="&#x2c;" u2="O" k="90" />
<hkern u1="&#x2c;" u2="G" k="90" />
<hkern u1="&#x2c;" u2="C" k="90" />
<hkern u1="&#x2c;" u2="&#x27;" k="188" />
<hkern u1="&#x2c;" u2="&#x22;" k="188" />
<hkern u1="&#x2e;" g2="uniFB04" k="7" />
<hkern u1="&#x2e;" g2="uniFB03" k="7" />
<hkern u1="&#x2e;" g2="uniFB02" k="7" />
<hkern u1="&#x2e;" g2="uniFB01" k="7" />
<hkern u1="&#x2e;" u2="&#x201d;" k="204" />
<hkern u1="&#x2e;" u2="&#x201c;" k="201" />
<hkern u1="&#x2e;" u2="&#x2019;" k="204" />
<hkern u1="&#x2e;" u2="&#x2018;" k="201" />
<hkern u1="&#x2e;" u2="&#x2014;" k="144" />
<hkern u1="&#x2e;" u2="&#x178;" k="172" />
<hkern u1="&#x2e;" u2="&#x152;" k="86" />
<hkern u1="&#x2e;" u2="&#xff;" k="87" />
<hkern u1="&#x2e;" u2="&#xfd;" k="87" />
<hkern u1="&#x2e;" u2="&#xdf;" k="7" />
<hkern u1="&#x2e;" u2="&#xdd;" k="172" />
<hkern u1="&#x2e;" u2="&#xdc;" k="74" />
<hkern u1="&#x2e;" u2="&#xdb;" k="74" />
<hkern u1="&#x2e;" u2="&#xda;" k="74" />
<hkern u1="&#x2e;" u2="&#xd9;" k="74" />
<hkern u1="&#x2e;" u2="&#xd8;" k="86" />
<hkern u1="&#x2e;" u2="&#xd6;" k="86" />
<hkern u1="&#x2e;" u2="&#xd5;" k="86" />
<hkern u1="&#x2e;" u2="&#xd4;" k="86" />
<hkern u1="&#x2e;" u2="&#xd3;" k="86" />
<hkern u1="&#x2e;" u2="&#xd2;" k="86" />
<hkern u1="&#x2e;" u2="&#xc7;" k="86" />
<hkern u1="&#x2e;" u2="y" k="87" />
<hkern u1="&#x2e;" u2="w" k="60" />
<hkern u1="&#x2e;" u2="v" k="85" />
<hkern u1="&#x2e;" u2="t" k="9" />
<hkern u1="&#x2e;" u2="f" k="7" />
<hkern u1="&#x2e;" u2="Y" k="172" />
<hkern u1="&#x2e;" u2="W" k="126" />
<hkern u1="&#x2e;" u2="V" k="166" />
<hkern u1="&#x2e;" u2="U" k="74" />
<hkern u1="&#x2e;" u2="T" k="154" />
<hkern u1="&#x2e;" u2="Q" k="86" />
<hkern u1="&#x2e;" u2="O" k="86" />
<hkern u1="&#x2e;" u2="G" k="86" />
<hkern u1="&#x2e;" u2="C" k="86" />
<hkern u1="&#x2e;" u2="&#x2d;" k="144" />
<hkern u1="&#x2e;" u2="&#x27;" k="188" />
<hkern u1="&#x2e;" u2="&#x22;" k="188" />
<hkern u1="&#x2f;" u2="&#x178;" k="-3" />
<hkern u1="&#x2f;" u2="&#x153;" k="77" />
<hkern u1="&#x2f;" u2="&#xfc;" k="43" />
<hkern u1="&#x2f;" u2="&#xfb;" k="43" />
<hkern u1="&#x2f;" u2="&#xfa;" k="43" />
<hkern u1="&#x2f;" u2="&#xf9;" k="43" />
<hkern u1="&#x2f;" u2="&#xf8;" k="77" />
<hkern u1="&#x2f;" u2="&#xf6;" k="77" />
<hkern u1="&#x2f;" u2="&#xf5;" k="77" />
<hkern u1="&#x2f;" u2="&#xf4;" k="77" />
<hkern u1="&#x2f;" u2="&#xf3;" k="77" />
<hkern u1="&#x2f;" u2="&#xf2;" k="77" />
<hkern u1="&#x2f;" u2="&#xf1;" k="45" />
<hkern u1="&#x2f;" u2="&#xf0;" k="54" />
<hkern u1="&#x2f;" u2="&#xef;" k="-10" />
<hkern u1="&#x2f;" u2="&#xec;" k="-22" />
<hkern u1="&#x2f;" u2="&#xeb;" k="77" />
<hkern u1="&#x2f;" u2="&#xea;" k="77" />
<hkern u1="&#x2f;" u2="&#xe9;" k="77" />
<hkern u1="&#x2f;" u2="&#xe8;" k="77" />
<hkern u1="&#x2f;" u2="&#xe7;" k="77" />
<hkern u1="&#x2f;" u2="&#xe6;" k="90" />
<hkern u1="&#x2f;" u2="&#xe5;" k="90" />
<hkern u1="&#x2f;" u2="&#xe4;" k="90" />
<hkern u1="&#x2f;" u2="&#xe3;" k="90" />
<hkern u1="&#x2f;" u2="&#xe2;" k="90" />
<hkern u1="&#x2f;" u2="&#xe1;" k="90" />
<hkern u1="&#x2f;" u2="&#xe0;" k="90" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-3" />
<hkern u1="&#x2f;" u2="&#xc6;" k="130" />
<hkern u1="&#x2f;" u2="&#xc5;" k="94" />
<hkern u1="&#x2f;" u2="&#xc4;" k="94" />
<hkern u1="&#x2f;" u2="&#xc3;" k="94" />
<hkern u1="&#x2f;" u2="&#xc2;" k="94" />
<hkern u1="&#x2f;" u2="&#xc1;" k="94" />
<hkern u1="&#x2f;" u2="&#xc0;" k="94" />
<hkern u1="&#x2f;" u2="z" k="11" />
<hkern u1="&#x2f;" u2="x" k="37" />
<hkern u1="&#x2f;" u2="u" k="43" />
<hkern u1="&#x2f;" u2="s" k="62" />
<hkern u1="&#x2f;" u2="r" k="45" />
<hkern u1="&#x2f;" u2="q" k="90" />
<hkern u1="&#x2f;" u2="p" k="45" />
<hkern u1="&#x2f;" u2="o" k="77" />
<hkern u1="&#x2f;" u2="n" k="45" />
<hkern u1="&#x2f;" u2="m" k="45" />
<hkern u1="&#x2f;" u2="g" k="71" />
<hkern u1="&#x2f;" u2="e" k="77" />
<hkern u1="&#x2f;" u2="d" k="84" />
<hkern u1="&#x2f;" u2="c" k="77" />
<hkern u1="&#x2f;" u2="a" k="90" />
<hkern u1="&#x2f;" u2="Y" k="-3" />
<hkern u1="&#x2f;" u2="V" k="-3" />
<hkern u1="&#x2f;" u2="J" k="106" />
<hkern u1="&#x2f;" u2="A" k="94" />
<hkern u1="&#x2f;" u2="&#x2f;" k="390" />
<hkern u1="&#x3a;" u2="&#x201d;" k="6" />
<hkern u1="&#x3a;" u2="&#x2019;" k="6" />
<hkern u1="&#x3a;" u2="&#x178;" k="79" />
<hkern u1="&#x3a;" u2="&#xdd;" k="79" />
<hkern u1="&#x3a;" u2="Y" k="79" />
<hkern u1="&#x3a;" u2="W" k="10" />
<hkern u1="&#x3a;" u2="V" k="46" />
<hkern u1="&#x3a;" u2="T" k="113" />
<hkern u1="&#x3a;" u2="&#x27;" k="8" />
<hkern u1="&#x3a;" u2="&#x22;" k="8" />
<hkern u1="&#x3b;" u2="&#x201d;" k="6" />
<hkern u1="&#x3b;" u2="&#x2019;" k="6" />
<hkern u1="&#x3b;" u2="&#x178;" k="78" />
<hkern u1="&#x3b;" u2="&#xdd;" k="78" />
<hkern u1="&#x3b;" u2="Y" k="78" />
<hkern u1="&#x3b;" u2="W" k="10" />
<hkern u1="&#x3b;" u2="V" k="42" />
<hkern u1="&#x3b;" u2="T" k="111" />
<hkern u1="&#x3b;" u2="&#x27;" k="8" />
<hkern u1="&#x3b;" u2="&#x22;" k="8" />
<hkern u1="&#x40;" u2="&#x201d;" k="27" />
<hkern u1="&#x40;" u2="&#x2019;" k="27" />
<hkern u1="&#x40;" u2="&#x178;" k="67" />
<hkern u1="&#x40;" u2="&#xdd;" k="67" />
<hkern u1="&#x40;" u2="Y" k="67" />
<hkern u1="&#x40;" u2="V" k="11" />
<hkern u1="&#x40;" u2="T" k="84" />
<hkern u1="A" u2="&#x2122;" k="139" />
<hkern u1="A" u2="&#xf0;" k="30" />
<hkern u1="A" u2="&#xd0;" k="4" />
<hkern u1="A" u2="&#xba;" k="122" />
<hkern u1="A" u2="&#xae;" k="45" />
<hkern u1="A" u2="&#xaa;" k="110" />
<hkern u1="A" u2="&#xa9;" k="44" />
<hkern u1="A" u2="&#x7d;" k="30" />
<hkern u1="A" u2="&#x7c;" k="9" />
<hkern u1="A" u2="]" k="38" />
<hkern u1="A" u2="\" k="105" />
<hkern u1="A" u2="&#x3f;" k="39" />
<hkern u1="A" u2="&#x2a;" k="134" />
<hkern u1="A" u2="&#x20;" k="66" />
<hkern u1="B" u2="&#x2122;" k="22" />
<hkern u1="B" u2="&#xf0;" k="16" />
<hkern u1="B" u2="&#xee;" k="15" />
<hkern u1="B" u2="&#x7d;" k="30" />
<hkern u1="B" u2="]" k="35" />
<hkern u1="C" u2="&#xf0;" k="25" />
<hkern u1="C" u2="&#xef;" k="-19" />
<hkern u1="C" u2="&#xee;" k="-10" />
<hkern u1="D" u2="&#x2122;" k="38" />
<hkern u1="D" u2="&#xf0;" k="14" />
<hkern u1="D" u2="&#xd0;" k="4" />
<hkern u1="D" u2="&#x7d;" k="40" />
<hkern u1="D" u2="]" k="49" />
<hkern u1="D" u2="&#x2c;" k="71" />
<hkern u1="D" u2="&#x29;" k="41" />
<hkern u1="E" u2="&#xf0;" k="60" />
<hkern u1="E" u2="&#xef;" k="-1" />
<hkern u1="E" u2="&#xee;" k="-2" />
<hkern u1="E" u2="&#xec;" k="-4" />
<hkern u1="F" u2="&#xf0;" k="102" />
<hkern u1="F" u2="&#xef;" k="-1" />
<hkern u1="F" u2="&#xee;" k="-1" />
<hkern u1="F" u2="&#xed;" k="39" />
<hkern u1="F" u2="&#xec;" k="-4" />
<hkern u1="F" u2="&#xdf;" k="66" />
<hkern u1="F" u2="&#x3b;" k="38" />
<hkern u1="F" u2="&#x3a;" k="38" />
<hkern u1="F" u2="&#x2f;" k="83" />
<hkern u1="F" u2="&#x2c;" k="195" />
<hkern u1="F" u2="&#x20;" k="41" />
<hkern u1="G" u2="&#xf0;" k="13" />
<hkern u1="G" u2="&#xef;" k="11" />
<hkern u1="G" u2="&#xaa;" k="22" />
<hkern u1="G" u2="]" k="27" />
<hkern u1="G" u2="&#x2a;" k="19" />
<hkern u1="H" u2="&#xf0;" k="34" />
<hkern u1="I" u2="&#xf0;" k="34" />
<hkern u1="J" u2="&#xf0;" k="40" />
<hkern u1="J" u2="&#xee;" k="13" />
<hkern u1="J" u2="&#x2c;" k="43" />
<hkern u1="K" u2="&#x2122;" k="-3" />
<hkern u1="K" u2="&#xf8;" k="68" />
<hkern u1="K" u2="&#xf0;" k="79" />
<hkern u1="K" u2="&#xef;" k="-8" />
<hkern u1="K" u2="&#xed;" k="4" />
<hkern u1="K" u2="&#xec;" k="-26" />
<hkern u1="K" u2="&#xae;" k="44" />
<hkern u1="K" u2="&#xa9;" k="44" />
<hkern u1="L" u2="&#x2122;" k="203" />
<hkern u1="L" u2="&#xf0;" k="96" />
<hkern u1="L" u2="&#xba;" k="200" />
<hkern u1="L" u2="&#xb7;" k="312" />
<hkern u1="L" u2="&#xae;" k="105" />
<hkern u1="L" u2="&#xaa;" k="200" />
<hkern u1="L" u2="&#xa9;" k="105" />
<hkern u1="L" u2="&#x7c;" k="9" />
<hkern u1="L" u2="\" k="126" />
<hkern u1="L" u2="&#x3f;" k="35" />
<hkern u1="L" u2="&#x2a;" k="207" />
<hkern u1="L" u2="&#x20;" k="63" />
<hkern u1="M" u2="&#xf0;" k="34" />
<hkern u1="N" u2="&#xf0;" k="34" />
<hkern u1="O" u2="&#x2122;" k="36" />
<hkern u1="O" u2="&#xf0;" k="14" />
<hkern u1="O" u2="&#xd0;" k="4" />
<hkern u1="O" u2="&#x7d;" k="40" />
<hkern u1="O" u2="]" k="47" />
<hkern u1="O" u2="&#x2c;" k="70" />
<hkern u1="O" u2="&#x29;" k="40" />
<hkern u1="P" u2="&#xf0;" k="77" />
<hkern u1="P" u2="&#xee;" k="-5" />
<hkern u1="P" u2="]" k="30" />
<hkern u1="P" u2="&#x2f;" k="81" />
<hkern u1="P" u2="&#x2c;" k="197" />
<hkern u1="P" u2="&#x20;" k="52" />
<hkern u1="Q" u2="&#x2122;" k="36" />
<hkern u1="Q" u2="&#x201e;" k="72" />
<hkern u1="Q" u2="&#x201a;" k="72" />
<hkern u1="Q" u2="&#xf0;" k="14" />
<hkern u1="Q" u2="&#xd0;" k="4" />
<hkern u1="Q" u2="&#x7d;" k="27" />
<hkern u1="Q" u2="]" k="32" />
<hkern u1="Q" u2="&#x2c;" k="70" />
<hkern u1="Q" u2="&#x29;" k="40" />
<hkern u1="R" u2="&#xf8;" k="56" />
<hkern u1="R" u2="&#xf0;" k="71" />
<hkern u1="R" u2="&#xee;" k="12" />
<hkern u1="R" u2="&#xd0;" k="4" />
<hkern u1="S" u2="&#xf0;" k="9" />
<hkern u1="S" u2="&#xef;" k="8" />
<hkern u1="S" u2="&#xaa;" k="23" />
<hkern u1="S" u2="]" k="27" />
<hkern u1="T" g2="uniFB02" k="128" />
<hkern u1="T" g2="uniFB01" k="120" />
<hkern u1="T" u2="&#x2122;" k="-23" />
<hkern u1="T" u2="&#xf6;" k="218" />
<hkern u1="T" u2="&#xf0;" k="150" />
<hkern u1="T" u2="&#xef;" k="-18" />
<hkern u1="T" u2="&#xee;" k="-20" />
<hkern u1="T" u2="&#xed;" k="67" />
<hkern u1="T" u2="&#xec;" k="-22" />
<hkern u1="T" u2="&#xeb;" k="215" />
<hkern u1="T" u2="&#xea;" k="221" />
<hkern u1="T" u2="&#xe8;" k="217" />
<hkern u1="T" u2="&#xdf;" k="152" />
<hkern u1="T" u2="&#xae;" k="32" />
<hkern u1="T" u2="&#xa9;" k="33" />
<hkern u1="T" u2="&#x40;" k="92" />
<hkern u1="T" u2="&#x3b;" k="108" />
<hkern u1="T" u2="&#x3a;" k="110" />
<hkern u1="T" u2="&#x2f;" k="109" />
<hkern u1="T" u2="&#x2c;" k="151" />
<hkern u1="T" u2="&#x26;" k="40" />
<hkern u1="T" u2="&#x20;" k="53" />
<hkern u1="U" u2="&#xf0;" k="44" />
<hkern u1="U" u2="&#xee;" k="18" />
<hkern u1="U" u2="&#xed;" k="28" />
<hkern u1="U" u2="&#xec;" k="22" />
<hkern u1="U" u2="&#x2f;" k="9" />
<hkern u1="U" u2="&#x2c;" k="65" />
<hkern u1="V" u2="&#x2122;" k="-23" />
<hkern u1="V" u2="&#xff;" k="37" />
<hkern u1="V" u2="&#xf0;" k="113" />
<hkern u1="V" u2="&#xef;" k="-22" />
<hkern u1="V" u2="&#xee;" k="-10" />
<hkern u1="V" u2="&#xed;" k="22" />
<hkern u1="V" u2="&#xec;" k="-34" />
<hkern u1="V" u2="&#xe8;" k="120" />
<hkern u1="V" u2="&#x7d;" k="-3" />
<hkern u1="V" u2="]" k="-5" />
<hkern u1="V" u2="\" k="-4" />
<hkern u1="V" u2="&#x40;" k="37" />
<hkern u1="V" u2="&#x3b;" k="13" />
<hkern u1="V" u2="&#x3a;" k="33" />
<hkern u1="V" u2="&#x2f;" k="103" />
<hkern u1="V" u2="&#x2c;" k="160" />
<hkern u1="V" u2="&#x26;" k="40" />
<hkern u1="V" u2="&#x20;" k="62" />
<hkern u1="W" u2="&#x2122;" k="-6" />
<hkern u1="W" u2="&#xf0;" k="92" />
<hkern u1="W" u2="&#xef;" k="-18" />
<hkern u1="W" u2="&#xee;" k="-11" />
<hkern u1="W" u2="&#xed;" k="16" />
<hkern u1="W" u2="&#xec;" k="-29" />
<hkern u1="W" u2="&#x3b;" k="7" />
<hkern u1="W" u2="&#x3a;" k="7" />
<hkern u1="W" u2="&#x2f;" k="73" />
<hkern u1="W" u2="&#x2c;" k="122" />
<hkern u1="W" u2="&#x26;" k="10" />
<hkern u1="W" u2="&#x20;" k="53" />
<hkern u1="X" u2="&#xf8;" k="53" />
<hkern u1="X" u2="&#xf0;" k="66" />
<hkern u1="X" u2="&#xef;" k="-7" />
<hkern u1="X" u2="&#xec;" k="-24" />
<hkern u1="X" u2="&#xae;" k="37" />
<hkern u1="X" u2="&#xa9;" k="37" />
<hkern u1="Y" g2="uniFB02" k="86" />
<hkern u1="Y" u2="&#x2122;" k="-28" />
<hkern u1="Y" u2="&#xff;" k="98" />
<hkern u1="Y" u2="&#xf9;" k="142" />
<hkern u1="Y" u2="&#xf6;" k="183" />
<hkern u1="Y" u2="&#xf2;" k="178" />
<hkern u1="Y" u2="&#xf0;" k="134" />
<hkern u1="Y" u2="&#xef;" k="-18" />
<hkern u1="Y" u2="&#xed;" k="39" />
<hkern u1="Y" u2="&#xec;" k="-34" />
<hkern u1="Y" u2="&#xeb;" k="180" />
<hkern u1="Y" u2="&#xe8;" k="169" />
<hkern u1="Y" u2="&#xdf;" k="96" />
<hkern u1="Y" u2="&#xae;" k="11" />
<hkern u1="Y" u2="&#xa9;" k="11" />
<hkern u1="Y" u2="&#x7d;" k="-3" />
<hkern u1="Y" u2="]" k="-5" />
<hkern u1="Y" u2="\" k="-4" />
<hkern u1="Y" u2="&#x40;" k="74" />
<hkern u1="Y" u2="&#x3b;" k="65" />
<hkern u1="Y" u2="&#x3a;" k="67" />
<hkern u1="Y" u2="&#x2f;" k="122" />
<hkern u1="Y" u2="&#x2c;" k="165" />
<hkern u1="Y" u2="&#x26;" k="60" />
<hkern u1="Y" u2="&#x20;" k="67" />
<hkern u1="Z" u2="&#xf0;" k="56" />
<hkern u1="Z" u2="&#xef;" k="-15" />
<hkern u1="Z" u2="&#xed;" k="4" />
<hkern u1="Z" u2="&#xec;" k="-18" />
<hkern u1="[" u2="&#x178;" k="-4" />
<hkern u1="[" u2="&#x153;" k="63" />
<hkern u1="[" u2="&#x152;" k="47" />
<hkern u1="[" u2="&#xff;" k="36" />
<hkern u1="[" u2="&#xfd;" k="36" />
<hkern u1="[" u2="&#xfc;" k="53" />
<hkern u1="[" u2="&#xfb;" k="53" />
<hkern u1="[" u2="&#xfa;" k="53" />
<hkern u1="[" u2="&#xf9;" k="53" />
<hkern u1="[" u2="&#xf8;" k="63" />
<hkern u1="[" u2="&#xf6;" k="63" />
<hkern u1="[" u2="&#xf5;" k="63" />
<hkern u1="[" u2="&#xf4;" k="63" />
<hkern u1="[" u2="&#xf3;" k="63" />
<hkern u1="[" u2="&#xf2;" k="63" />
<hkern u1="[" u2="&#xf1;" k="50" />
<hkern u1="[" u2="&#xf0;" k="57" />
<hkern u1="[" u2="&#xef;" k="-14" />
<hkern u1="[" u2="&#xec;" k="-27" />
<hkern u1="[" u2="&#xeb;" k="63" />
<hkern u1="[" u2="&#xea;" k="63" />
<hkern u1="[" u2="&#xe9;" k="63" />
<hkern u1="[" u2="&#xe8;" k="63" />
<hkern u1="[" u2="&#xe7;" k="63" />
<hkern u1="[" u2="&#xe6;" k="65" />
<hkern u1="[" u2="&#xe5;" k="65" />
<hkern u1="[" u2="&#xe4;" k="65" />
<hkern u1="[" u2="&#xe3;" k="65" />
<hkern u1="[" u2="&#xe2;" k="65" />
<hkern u1="[" u2="&#xe1;" k="65" />
<hkern u1="[" u2="&#xe0;" k="65" />
<hkern u1="[" u2="&#xdd;" k="-4" />
<hkern u1="[" u2="&#xd8;" k="47" />
<hkern u1="[" u2="&#xd6;" k="47" />
<hkern u1="[" u2="&#xd5;" k="47" />
<hkern u1="[" u2="&#xd4;" k="47" />
<hkern u1="[" u2="&#xd3;" k="47" />
<hkern u1="[" u2="&#xd2;" k="47" />
<hkern u1="[" u2="&#xcf;" k="-13" />
<hkern u1="[" u2="&#xce;" k="-20" />
<hkern u1="[" u2="&#xc7;" k="47" />
<hkern u1="[" u2="&#xc6;" k="49" />
<hkern u1="[" u2="&#xc5;" k="30" />
<hkern u1="[" u2="&#xc4;" k="30" />
<hkern u1="[" u2="&#xc3;" k="30" />
<hkern u1="[" u2="&#xc2;" k="30" />
<hkern u1="[" u2="&#xc1;" k="30" />
<hkern u1="[" u2="&#xc0;" k="30" />
<hkern u1="[" u2="z" k="41" />
<hkern u1="[" u2="y" k="36" />
<hkern u1="[" u2="x" k="33" />
<hkern u1="[" u2="w" k="50" />
<hkern u1="[" u2="v" k="54" />
<hkern u1="[" u2="u" k="53" />
<hkern u1="[" u2="t" k="47" />
<hkern u1="[" u2="s" k="54" />
<hkern u1="[" u2="r" k="50" />
<hkern u1="[" u2="q" k="65" />
<hkern u1="[" u2="p" k="50" />
<hkern u1="[" u2="o" k="63" />
<hkern u1="[" u2="n" k="50" />
<hkern u1="[" u2="m" k="50" />
<hkern u1="[" u2="j" k="-15" />
<hkern u1="[" u2="e" k="63" />
<hkern u1="[" u2="d" k="65" />
<hkern u1="[" u2="c" k="63" />
<hkern u1="[" u2="a" k="65" />
<hkern u1="[" u2="Y" k="-4" />
<hkern u1="[" u2="V" k="-5" />
<hkern u1="[" u2="S" k="28" />
<hkern u1="[" u2="Q" k="47" />
<hkern u1="[" u2="O" k="47" />
<hkern u1="[" u2="G" k="47" />
<hkern u1="[" u2="C" k="47" />
<hkern u1="[" u2="A" k="30" />
<hkern u1="\" u2="&#x201d;" k="117" />
<hkern u1="\" u2="&#x2019;" k="117" />
<hkern u1="\" u2="&#x178;" k="131" />
<hkern u1="\" u2="&#x152;" k="36" />
<hkern u1="\" u2="&#xff;" k="61" />
<hkern u1="\" u2="&#xfd;" k="61" />
<hkern u1="\" u2="&#xdd;" k="131" />
<hkern u1="\" u2="&#xdc;" k="11" />
<hkern u1="\" u2="&#xdb;" k="11" />
<hkern u1="\" u2="&#xda;" k="11" />
<hkern u1="\" u2="&#xd9;" k="11" />
<hkern u1="\" u2="&#xd8;" k="36" />
<hkern u1="\" u2="&#xd6;" k="36" />
<hkern u1="\" u2="&#xd5;" k="36" />
<hkern u1="\" u2="&#xd4;" k="36" />
<hkern u1="\" u2="&#xd3;" k="36" />
<hkern u1="\" u2="&#xd2;" k="36" />
<hkern u1="\" u2="&#xc7;" k="36" />
<hkern u1="\" u2="y" k="61" />
<hkern u1="\" u2="w" k="49" />
<hkern u1="\" u2="v" k="68" />
<hkern u1="\" u2="t" k="13" />
<hkern u1="\" u2="Y" k="131" />
<hkern u1="\" u2="W" k="82" />
<hkern u1="\" u2="V" k="116" />
<hkern u1="\" u2="U" k="11" />
<hkern u1="\" u2="T" k="117" />
<hkern u1="\" u2="Q" k="36" />
<hkern u1="\" u2="O" k="36" />
<hkern u1="\" u2="G" k="36" />
<hkern u1="\" u2="C" k="36" />
<hkern u1="\" u2="&#x27;" k="108" />
<hkern u1="\" u2="&#x22;" k="108" />
<hkern u1="a" u2="&#x2122;" k="36" />
<hkern u1="a" u2="&#x7d;" k="44" />
<hkern u1="a" u2="]" k="52" />
<hkern u1="a" u2="\" k="50" />
<hkern u1="a" u2="&#x3f;" k="9" />
<hkern u1="a" u2="&#x2a;" k="28" />
<hkern u1="b" u2="&#x2122;" k="56" />
<hkern u1="b" u2="&#xba;" k="41" />
<hkern u1="b" u2="&#xaa;" k="30" />
<hkern u1="b" u2="&#x7d;" k="55" />
<hkern u1="b" u2="&#x7c;" k="9" />
<hkern u1="b" u2="]" k="65" />
<hkern u1="b" u2="\" k="72" />
<hkern u1="b" u2="&#x3f;" k="57" />
<hkern u1="b" u2="&#x2f;" k="9" />
<hkern u1="b" u2="&#x2a;" k="68" />
<hkern u1="b" u2="&#x29;" k="58" />
<hkern u1="c" u2="&#x2122;" k="46" />
<hkern u1="c" u2="&#xf0;" k="19" />
<hkern u1="c" u2="&#xba;" k="6" />
<hkern u1="c" u2="&#x7d;" k="50" />
<hkern u1="c" u2="]" k="55" />
<hkern u1="c" u2="\" k="58" />
<hkern u1="c" u2="&#x3f;" k="39" />
<hkern u1="c" u2="&#x2a;" k="50" />
<hkern u1="c" u2="&#x29;" k="27" />
<hkern u1="d" u2="&#xee;" k="-6" />
<hkern u1="d" u2="&#xec;" k="-7" />
<hkern u1="e" u2="&#x2122;" k="49" />
<hkern u1="e" u2="&#xf0;" k="8" />
<hkern u1="e" u2="&#xba;" k="31" />
<hkern u1="e" u2="&#xaa;" k="7" />
<hkern u1="e" u2="&#x7d;" k="52" />
<hkern u1="e" u2="]" k="57" />
<hkern u1="e" u2="\" k="66" />
<hkern u1="e" u2="&#x3f;" k="46" />
<hkern u1="e" u2="&#x2a;" k="56" />
<hkern u1="e" u2="&#x29;" k="40" />
<hkern u1="f" u2="&#x2122;" k="-6" />
<hkern u1="f" u2="&#xf0;" k="59" />
<hkern u1="f" u2="&#xef;" k="-29" />
<hkern u1="f" u2="&#xee;" k="-8" />
<hkern u1="f" u2="&#xec;" k="-34" />
<hkern u1="f" u2="&#x7d;" k="-5" />
<hkern u1="f" u2="&#x7c;" k="-4" />
<hkern u1="f" u2="]" k="-7" />
<hkern u1="f" u2="\" k="-6" />
<hkern u1="f" u2="&#x2f;" k="52" />
<hkern u1="f" u2="&#x2c;" k="55" />
<hkern u1="f" u2="&#x2a;" k="14" />
<hkern u1="f" u2="&#x29;" k="-4" />
<hkern u1="f" u2="&#x20;" k="9" />
<hkern u1="g" u2="&#x2122;" k="7" />
<hkern u1="g" u2="&#xf0;" k="35" />
<hkern u1="g" u2="\" k="9" />
<hkern u1="h" u2="&#x2122;" k="50" />
<hkern u1="h" u2="&#xba;" k="35" />
<hkern u1="h" u2="&#xaa;" k="8" />
<hkern u1="h" u2="&#x7d;" k="49" />
<hkern u1="h" u2="]" k="55" />
<hkern u1="h" u2="\" k="70" />
<hkern u1="h" u2="&#x3f;" k="45" />
<hkern u1="h" u2="&#x2a;" k="56" />
<hkern u1="h" u2="&#x29;" k="25" />
<hkern u1="i" u2="&#xef;" k="-5" />
<hkern u1="j" u2="&#xef;" k="-5" />
<hkern u1="k" u2="&#x2122;" k="30" />
<hkern u1="k" u2="&#xf0;" k="69" />
<hkern u1="k" u2="&#x7d;" k="32" />
<hkern u1="k" u2="]" k="43" />
<hkern u1="k" u2="&#x2a;" k="18" />
<hkern u1="l" u2="&#xee;" k="-6" />
<hkern u1="l" u2="&#xec;" k="-6" />
<hkern u1="l" u2="&#xb7;" k="60" />
<hkern u1="m" u2="&#x2122;" k="50" />
<hkern u1="m" u2="&#xba;" k="35" />
<hkern u1="m" u2="&#xaa;" k="8" />
<hkern u1="m" u2="&#x7d;" k="49" />
<hkern u1="m" u2="]" k="55" />
<hkern u1="m" u2="\" k="70" />
<hkern u1="m" u2="&#x3f;" k="45" />
<hkern u1="m" u2="&#x2a;" k="56" />
<hkern u1="m" u2="&#x29;" k="25" />
<hkern u1="n" u2="&#x2122;" k="50" />
<hkern u1="n" u2="&#xba;" k="35" />
<hkern u1="n" u2="&#xaa;" k="8" />
<hkern u1="n" u2="&#x7d;" k="49" />
<hkern u1="n" u2="]" k="55" />
<hkern u1="n" u2="\" k="70" />
<hkern u1="n" u2="&#x3f;" k="45" />
<hkern u1="n" u2="&#x2a;" k="56" />
<hkern u1="n" u2="&#x29;" k="25" />
<hkern u1="o" u2="&#x2122;" k="59" />
<hkern u1="o" u2="&#xba;" k="46" />
<hkern u1="o" u2="&#xaa;" k="34" />
<hkern u1="o" u2="&#x7d;" k="55" />
<hkern u1="o" u2="&#x7c;" k="9" />
<hkern u1="o" u2="]" k="65" />
<hkern u1="o" u2="\" k="78" />
<hkern u1="o" u2="&#x3f;" k="57" />
<hkern u1="o" u2="&#x2a;" k="74" />
<hkern u1="o" u2="&#x29;" k="57" />
<hkern u1="p" u2="&#x2122;" k="56" />
<hkern u1="p" u2="&#xba;" k="41" />
<hkern u1="p" u2="&#xaa;" k="30" />
<hkern u1="p" u2="&#x7d;" k="55" />
<hkern u1="p" u2="&#x7c;" k="9" />
<hkern u1="p" u2="]" k="65" />
<hkern u1="p" u2="\" k="72" />
<hkern u1="p" u2="&#x3f;" k="57" />
<hkern u1="p" u2="&#x2f;" k="9" />
<hkern u1="p" u2="&#x2a;" k="68" />
<hkern u1="p" u2="&#x29;" k="58" />
<hkern u1="q" u2="&#x2122;" k="34" />
<hkern u1="q" u2="&#x7d;" k="44" />
<hkern u1="q" u2="]" k="51" />
<hkern u1="q" u2="\" k="46" />
<hkern u1="q" u2="&#x3f;" k="8" />
<hkern u1="q" u2="&#x2a;" k="23" />
<hkern u1="r" u2="&#xf0;" k="70" />
<hkern u1="r" u2="&#x7d;" k="39" />
<hkern u1="r" u2="]" k="48" />
<hkern u1="r" u2="&#x2f;" k="75" />
<hkern u1="r" u2="&#x2c;" k="84" />
<hkern u1="r" u2="&#x29;" k="37" />
<hkern u1="r" u2="&#x20;" k="40" />
<hkern u1="s" u2="&#x2122;" k="39" />
<hkern u1="s" u2="&#x7d;" k="46" />
<hkern u1="s" u2="]" k="54" />
<hkern u1="s" u2="\" k="50" />
<hkern u1="s" u2="&#x3f;" k="10" />
<hkern u1="s" u2="&#x2a;" k="41" />
<hkern u1="s" u2="&#x29;" k="38" />
<hkern u1="t" u2="&#x2122;" k="34" />
<hkern u1="t" u2="&#xf0;" k="28" />
<hkern u1="t" u2="&#x7d;" k="44" />
<hkern u1="t" u2="]" k="48" />
<hkern u1="t" u2="\" k="40" />
<hkern u1="t" u2="&#x2a;" k="35" />
<hkern u1="u" u2="&#x2122;" k="34" />
<hkern u1="u" u2="&#x7d;" k="44" />
<hkern u1="u" u2="]" k="51" />
<hkern u1="u" u2="\" k="46" />
<hkern u1="u" u2="&#x3f;" k="8" />
<hkern u1="u" u2="&#x2a;" k="23" />
<hkern u1="v" u2="&#xf0;" k="28" />
<hkern u1="v" u2="&#x7d;" k="41" />
<hkern u1="v" u2="]" k="51" />
<hkern u1="v" u2="&#x2f;" k="60" />
<hkern u1="v" u2="&#x2c;" k="76" />
<hkern u1="v" u2="&#x29;" k="32" />
<hkern u1="v" u2="&#x20;" k="44" />
<hkern u1="w" u2="&#xf0;" k="16" />
<hkern u1="w" u2="&#x7d;" k="37" />
<hkern u1="w" u2="]" k="45" />
<hkern u1="w" u2="&#x2f;" k="45" />
<hkern u1="w" u2="&#x2c;" k="54" />
<hkern u1="w" u2="&#x29;" k="29" />
<hkern u1="w" u2="&#x20;" k="38" />
<hkern u1="x" u2="&#x2122;" k="19" />
<hkern u1="x" u2="&#xf0;" k="47" />
<hkern u1="x" u2="&#x7d;" k="31" />
<hkern u1="x" u2="]" k="33" />
<hkern u1="y" u2="&#xf0;" k="29" />
<hkern u1="y" u2="&#x7d;" k="39" />
<hkern u1="y" u2="]" k="48" />
<hkern u1="y" u2="&#x2f;" k="59" />
<hkern u1="y" u2="&#x2c;" k="80" />
<hkern u1="y" u2="&#x29;" k="30" />
<hkern u1="y" u2="&#x20;" k="44" />
<hkern u1="z" u2="&#x2122;" k="8" />
<hkern u1="z" u2="&#xf0;" k="32" />
<hkern u1="z" u2="&#x7d;" k="28" />
<hkern u1="z" u2="]" k="42" />
<hkern u1="z" u2="\" k="9" />
<hkern u1="&#x7b;" u2="&#x178;" k="-3" />
<hkern u1="&#x7b;" u2="&#x153;" k="54" />
<hkern u1="&#x7b;" u2="&#x152;" k="40" />
<hkern u1="&#x7b;" u2="&#xff;" k="31" />
<hkern u1="&#x7b;" u2="&#xfd;" k="31" />
<hkern u1="&#x7b;" u2="&#xfc;" k="45" />
<hkern u1="&#x7b;" u2="&#xfb;" k="45" />
<hkern u1="&#x7b;" u2="&#xfa;" k="45" />
<hkern u1="&#x7b;" u2="&#xf9;" k="45" />
<hkern u1="&#x7b;" u2="&#xf8;" k="54" />
<hkern u1="&#x7b;" u2="&#xf6;" k="54" />
<hkern u1="&#x7b;" u2="&#xf5;" k="54" />
<hkern u1="&#x7b;" u2="&#xf4;" k="54" />
<hkern u1="&#x7b;" u2="&#xf3;" k="54" />
<hkern u1="&#x7b;" u2="&#xf2;" k="54" />
<hkern u1="&#x7b;" u2="&#xf1;" k="43" />
<hkern u1="&#x7b;" u2="&#xf0;" k="49" />
<hkern u1="&#x7b;" u2="&#xef;" k="-14" />
<hkern u1="&#x7b;" u2="&#xec;" k="-25" />
<hkern u1="&#x7b;" u2="&#xeb;" k="54" />
<hkern u1="&#x7b;" u2="&#xea;" k="54" />
<hkern u1="&#x7b;" u2="&#xe9;" k="54" />
<hkern u1="&#x7b;" u2="&#xe8;" k="54" />
<hkern u1="&#x7b;" u2="&#xe7;" k="54" />
<hkern u1="&#x7b;" u2="&#xe6;" k="55" />
<hkern u1="&#x7b;" u2="&#xe5;" k="55" />
<hkern u1="&#x7b;" u2="&#xe4;" k="55" />
<hkern u1="&#x7b;" u2="&#xe3;" k="55" />
<hkern u1="&#x7b;" u2="&#xe2;" k="55" />
<hkern u1="&#x7b;" u2="&#xe1;" k="55" />
<hkern u1="&#x7b;" u2="&#xe0;" k="55" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-3" />
<hkern u1="&#x7b;" u2="&#xd8;" k="40" />
<hkern u1="&#x7b;" u2="&#xd6;" k="40" />
<hkern u1="&#x7b;" u2="&#xd5;" k="40" />
<hkern u1="&#x7b;" u2="&#xd4;" k="40" />
<hkern u1="&#x7b;" u2="&#xd3;" k="40" />
<hkern u1="&#x7b;" u2="&#xd2;" k="40" />
<hkern u1="&#x7b;" u2="&#xcf;" k="-11" />
<hkern u1="&#x7b;" u2="&#xce;" k="-19" />
<hkern u1="&#x7b;" u2="&#xc7;" k="40" />
<hkern u1="&#x7b;" u2="&#xc6;" k="48" />
<hkern u1="&#x7b;" u2="&#xc5;" k="30" />
<hkern u1="&#x7b;" u2="&#xc4;" k="30" />
<hkern u1="&#x7b;" u2="&#xc3;" k="30" />
<hkern u1="&#x7b;" u2="&#xc2;" k="30" />
<hkern u1="&#x7b;" u2="&#xc1;" k="30" />
<hkern u1="&#x7b;" u2="&#xc0;" k="30" />
<hkern u1="&#x7b;" u2="z" k="30" />
<hkern u1="&#x7b;" u2="y" k="31" />
<hkern u1="&#x7b;" u2="x" k="32" />
<hkern u1="&#x7b;" u2="w" k="41" />
<hkern u1="&#x7b;" u2="v" k="46" />
<hkern u1="&#x7b;" u2="u" k="45" />
<hkern u1="&#x7b;" u2="t" k="39" />
<hkern u1="&#x7b;" u2="s" k="49" />
<hkern u1="&#x7b;" u2="r" k="43" />
<hkern u1="&#x7b;" u2="q" k="55" />
<hkern u1="&#x7b;" u2="p" k="43" />
<hkern u1="&#x7b;" u2="o" k="54" />
<hkern u1="&#x7b;" u2="n" k="43" />
<hkern u1="&#x7b;" u2="m" k="43" />
<hkern u1="&#x7b;" u2="j" k="-14" />
<hkern u1="&#x7b;" u2="e" k="54" />
<hkern u1="&#x7b;" u2="d" k="55" />
<hkern u1="&#x7b;" u2="c" k="54" />
<hkern u1="&#x7b;" u2="a" k="55" />
<hkern u1="&#x7b;" u2="Y" k="-3" />
<hkern u1="&#x7b;" u2="V" k="-3" />
<hkern u1="&#x7b;" u2="S" k="26" />
<hkern u1="&#x7b;" u2="Q" k="40" />
<hkern u1="&#x7b;" u2="O" k="40" />
<hkern u1="&#x7b;" u2="G" k="40" />
<hkern u1="&#x7b;" u2="C" k="40" />
<hkern u1="&#x7b;" u2="A" k="30" />
<hkern u1="&#x7c;" u2="&#x201d;" k="54" />
<hkern u1="&#x7c;" u2="&#x2019;" k="54" />
<hkern u1="&#x7c;" u2="&#x178;" k="58" />
<hkern u1="&#x7c;" u2="&#xdd;" k="58" />
<hkern u1="&#x7c;" u2="w" k="8" />
<hkern u1="&#x7c;" u2="v" k="36" />
<hkern u1="&#x7c;" u2="t" k="9" />
<hkern u1="&#x7c;" u2="j" k="-16" />
<hkern u1="&#x7c;" u2="Y" k="58" />
<hkern u1="&#x7c;" u2="W" k="40" />
<hkern u1="&#x7c;" u2="V" k="51" />
<hkern u1="&#x7c;" u2="T" k="37" />
<hkern u1="&#x7c;" u2="&#x27;" k="50" />
<hkern u1="&#x7c;" u2="&#x22;" k="50" />
<hkern u1="&#xa1;" u2="&#x178;" k="78" />
<hkern u1="&#xa1;" u2="&#xdd;" k="78" />
<hkern u1="&#xa1;" u2="Y" k="78" />
<hkern u1="&#xa1;" u2="W" k="11" />
<hkern u1="&#xa1;" u2="V" k="47" />
<hkern u1="&#xa1;" u2="T" k="129" />
<hkern u1="&#xb7;" u2="l" k="60" />
<hkern u1="&#xbf;" u2="&#x178;" k="127" />
<hkern u1="&#xbf;" u2="&#x153;" k="60" />
<hkern u1="&#xbf;" u2="&#x152;" k="48" />
<hkern u1="&#xbf;" u2="&#xff;" k="26" />
<hkern u1="&#xbf;" u2="&#xfe;" k="34" />
<hkern u1="&#xbf;" u2="&#xfd;" k="26" />
<hkern u1="&#xbf;" u2="&#xfc;" k="44" />
<hkern u1="&#xbf;" u2="&#xfb;" k="44" />
<hkern u1="&#xbf;" u2="&#xfa;" k="44" />
<hkern u1="&#xbf;" u2="&#xf9;" k="44" />
<hkern u1="&#xbf;" u2="&#xf8;" k="60" />
<hkern u1="&#xbf;" u2="&#xf6;" k="60" />
<hkern u1="&#xbf;" u2="&#xf5;" k="60" />
<hkern u1="&#xbf;" u2="&#xf4;" k="60" />
<hkern u1="&#xbf;" u2="&#xf3;" k="60" />
<hkern u1="&#xbf;" u2="&#xf2;" k="60" />
<hkern u1="&#xbf;" u2="&#xf1;" k="34" />
<hkern u1="&#xbf;" u2="&#xf0;" k="65" />
<hkern u1="&#xbf;" u2="&#xef;" k="34" />
<hkern u1="&#xbf;" u2="&#xee;" k="34" />
<hkern u1="&#xbf;" u2="&#xed;" k="34" />
<hkern u1="&#xbf;" u2="&#xec;" k="34" />
<hkern u1="&#xbf;" u2="&#xeb;" k="60" />
<hkern u1="&#xbf;" u2="&#xea;" k="60" />
<hkern u1="&#xbf;" u2="&#xe9;" k="60" />
<hkern u1="&#xbf;" u2="&#xe8;" k="60" />
<hkern u1="&#xbf;" u2="&#xe7;" k="60" />
<hkern u1="&#xbf;" u2="&#xe6;" k="62" />
<hkern u1="&#xbf;" u2="&#xe5;" k="62" />
<hkern u1="&#xbf;" u2="&#xe4;" k="62" />
<hkern u1="&#xbf;" u2="&#xe3;" k="62" />
<hkern u1="&#xbf;" u2="&#xe2;" k="62" />
<hkern u1="&#xbf;" u2="&#xe1;" k="62" />
<hkern u1="&#xbf;" u2="&#xe0;" k="62" />
<hkern u1="&#xbf;" u2="&#xde;" k="31" />
<hkern u1="&#xbf;" u2="&#xdd;" k="127" />
<hkern u1="&#xbf;" u2="&#xdc;" k="48" />
<hkern u1="&#xbf;" u2="&#xdb;" k="48" />
<hkern u1="&#xbf;" u2="&#xda;" k="48" />
<hkern u1="&#xbf;" u2="&#xd9;" k="48" />
<hkern u1="&#xbf;" u2="&#xd8;" k="48" />
<hkern u1="&#xbf;" u2="&#xd6;" k="48" />
<hkern u1="&#xbf;" u2="&#xd5;" k="48" />
<hkern u1="&#xbf;" u2="&#xd4;" k="48" />
<hkern u1="&#xbf;" u2="&#xd3;" k="48" />
<hkern u1="&#xbf;" u2="&#xd2;" k="48" />
<hkern u1="&#xbf;" u2="&#xd1;" k="31" />
<hkern u1="&#xbf;" u2="&#xd0;" k="42" />
<hkern u1="&#xbf;" u2="&#xcf;" k="31" />
<hkern u1="&#xbf;" u2="&#xce;" k="31" />
<hkern u1="&#xbf;" u2="&#xcd;" k="31" />
<hkern u1="&#xbf;" u2="&#xcc;" k="31" />
<hkern u1="&#xbf;" u2="&#xcb;" k="31" />
<hkern u1="&#xbf;" u2="&#xca;" k="31" />
<hkern u1="&#xbf;" u2="&#xc9;" k="31" />
<hkern u1="&#xbf;" u2="&#xc8;" k="31" />
<hkern u1="&#xbf;" u2="&#xc7;" k="48" />
<hkern u1="&#xbf;" u2="&#xc6;" k="59" />
<hkern u1="&#xbf;" u2="&#xc5;" k="48" />
<hkern u1="&#xbf;" u2="&#xc4;" k="48" />
<hkern u1="&#xbf;" u2="&#xc3;" k="48" />
<hkern u1="&#xbf;" u2="&#xc2;" k="48" />
<hkern u1="&#xbf;" u2="&#xc1;" k="48" />
<hkern u1="&#xbf;" u2="&#xc0;" k="48" />
<hkern u1="&#xbf;" u2="z" k="38" />
<hkern u1="&#xbf;" u2="y" k="26" />
<hkern u1="&#xbf;" u2="x" k="37" />
<hkern u1="&#xbf;" u2="w" k="29" />
<hkern u1="&#xbf;" u2="v" k="42" />
<hkern u1="&#xbf;" u2="u" k="44" />
<hkern u1="&#xbf;" u2="t" k="29" />
<hkern u1="&#xbf;" u2="s" k="54" />
<hkern u1="&#xbf;" u2="r" k="34" />
<hkern u1="&#xbf;" u2="q" k="62" />
<hkern u1="&#xbf;" u2="p" k="34" />
<hkern u1="&#xbf;" u2="o" k="60" />
<hkern u1="&#xbf;" u2="n" k="34" />
<hkern u1="&#xbf;" u2="m" k="34" />
<hkern u1="&#xbf;" u2="l" k="34" />
<hkern u1="&#xbf;" u2="k" k="34" />
<hkern u1="&#xbf;" u2="j" k="22" />
<hkern u1="&#xbf;" u2="i" k="34" />
<hkern u1="&#xbf;" u2="h" k="34" />
<hkern u1="&#xbf;" u2="e" k="60" />
<hkern u1="&#xbf;" u2="d" k="60" />
<hkern u1="&#xbf;" u2="c" k="60" />
<hkern u1="&#xbf;" u2="b" k="34" />
<hkern u1="&#xbf;" u2="a" k="62" />
<hkern u1="&#xbf;" u2="Z" k="41" />
<hkern u1="&#xbf;" u2="Y" k="127" />
<hkern u1="&#xbf;" u2="X" k="48" />
<hkern u1="&#xbf;" u2="W" k="75" />
<hkern u1="&#xbf;" u2="V" k="98" />
<hkern u1="&#xbf;" u2="U" k="48" />
<hkern u1="&#xbf;" u2="T" k="169" />
<hkern u1="&#xbf;" u2="S" k="33" />
<hkern u1="&#xbf;" u2="R" k="31" />
<hkern u1="&#xbf;" u2="Q" k="48" />
<hkern u1="&#xbf;" u2="P" k="31" />
<hkern u1="&#xbf;" u2="O" k="48" />
<hkern u1="&#xbf;" u2="N" k="31" />
<hkern u1="&#xbf;" u2="M" k="31" />
<hkern u1="&#xbf;" u2="L" k="31" />
<hkern u1="&#xbf;" u2="K" k="31" />
<hkern u1="&#xbf;" u2="J" k="70" />
<hkern u1="&#xbf;" u2="I" k="31" />
<hkern u1="&#xbf;" u2="H" k="31" />
<hkern u1="&#xbf;" u2="G" k="48" />
<hkern u1="&#xbf;" u2="F" k="31" />
<hkern u1="&#xbf;" u2="E" k="31" />
<hkern u1="&#xbf;" u2="D" k="31" />
<hkern u1="&#xbf;" u2="C" k="48" />
<hkern u1="&#xbf;" u2="B" k="31" />
<hkern u1="&#xbf;" u2="A" k="48" />
<hkern u1="&#xc0;" u2="&#x2122;" k="139" />
<hkern u1="&#xc0;" u2="&#xf0;" k="30" />
<hkern u1="&#xc0;" u2="&#xd0;" k="4" />
<hkern u1="&#xc0;" u2="&#xba;" k="122" />
<hkern u1="&#xc0;" u2="&#xae;" k="45" />
<hkern u1="&#xc0;" u2="&#xaa;" k="110" />
<hkern u1="&#xc0;" u2="&#xa9;" k="44" />
<hkern u1="&#xc0;" u2="&#x7d;" k="30" />
<hkern u1="&#xc0;" u2="&#x7c;" k="9" />
<hkern u1="&#xc0;" u2="]" k="38" />
<hkern u1="&#xc0;" u2="\" k="105" />
<hkern u1="&#xc0;" u2="&#x3f;" k="39" />
<hkern u1="&#xc0;" u2="&#x2a;" k="134" />
<hkern u1="&#xc0;" u2="&#x20;" k="66" />
<hkern u1="&#xc1;" u2="&#x2122;" k="139" />
<hkern u1="&#xc1;" u2="&#xf0;" k="30" />
<hkern u1="&#xc1;" u2="&#xd0;" k="4" />
<hkern u1="&#xc1;" u2="&#xba;" k="122" />
<hkern u1="&#xc1;" u2="&#xae;" k="45" />
<hkern u1="&#xc1;" u2="&#xaa;" k="110" />
<hkern u1="&#xc1;" u2="&#xa9;" k="44" />
<hkern u1="&#xc1;" u2="&#x7d;" k="30" />
<hkern u1="&#xc1;" u2="&#x7c;" k="9" />
<hkern u1="&#xc1;" u2="]" k="38" />
<hkern u1="&#xc1;" u2="\" k="105" />
<hkern u1="&#xc1;" u2="&#x3f;" k="39" />
<hkern u1="&#xc1;" u2="&#x2a;" k="134" />
<hkern u1="&#xc1;" u2="&#x20;" k="66" />
<hkern u1="&#xc2;" u2="&#x2122;" k="139" />
<hkern u1="&#xc2;" u2="&#xf0;" k="30" />
<hkern u1="&#xc2;" u2="&#xd0;" k="4" />
<hkern u1="&#xc2;" u2="&#xba;" k="122" />
<hkern u1="&#xc2;" u2="&#xae;" k="45" />
<hkern u1="&#xc2;" u2="&#xaa;" k="110" />
<hkern u1="&#xc2;" u2="&#xa9;" k="44" />
<hkern u1="&#xc2;" u2="&#x7d;" k="30" />
<hkern u1="&#xc2;" u2="&#x7c;" k="9" />
<hkern u1="&#xc2;" u2="]" k="38" />
<hkern u1="&#xc2;" u2="\" k="105" />
<hkern u1="&#xc2;" u2="&#x3f;" k="39" />
<hkern u1="&#xc2;" u2="&#x2a;" k="134" />
<hkern u1="&#xc2;" u2="&#x20;" k="66" />
<hkern u1="&#xc3;" u2="&#x2122;" k="139" />
<hkern u1="&#xc3;" u2="&#xf0;" k="30" />
<hkern u1="&#xc3;" u2="&#xd0;" k="4" />
<hkern u1="&#xc3;" u2="&#xba;" k="122" />
<hkern u1="&#xc3;" u2="&#xae;" k="45" />
<hkern u1="&#xc3;" u2="&#xaa;" k="110" />
<hkern u1="&#xc3;" u2="&#xa9;" k="44" />
<hkern u1="&#xc3;" u2="&#x7d;" k="30" />
<hkern u1="&#xc3;" u2="&#x7c;" k="9" />
<hkern u1="&#xc3;" u2="]" k="38" />
<hkern u1="&#xc3;" u2="\" k="105" />
<hkern u1="&#xc3;" u2="&#x3f;" k="39" />
<hkern u1="&#xc3;" u2="&#x2a;" k="134" />
<hkern u1="&#xc3;" u2="&#x20;" k="66" />
<hkern u1="&#xc4;" u2="&#x2122;" k="139" />
<hkern u1="&#xc4;" u2="&#xf0;" k="30" />
<hkern u1="&#xc4;" u2="&#xd0;" k="4" />
<hkern u1="&#xc4;" u2="&#xba;" k="122" />
<hkern u1="&#xc4;" u2="&#xae;" k="45" />
<hkern u1="&#xc4;" u2="&#xaa;" k="110" />
<hkern u1="&#xc4;" u2="&#xa9;" k="44" />
<hkern u1="&#xc4;" u2="&#x7d;" k="30" />
<hkern u1="&#xc4;" u2="&#x7c;" k="9" />
<hkern u1="&#xc4;" u2="]" k="38" />
<hkern u1="&#xc4;" u2="\" k="105" />
<hkern u1="&#xc4;" u2="&#x3f;" k="39" />
<hkern u1="&#xc4;" u2="&#x2a;" k="134" />
<hkern u1="&#xc4;" u2="&#x20;" k="66" />
<hkern u1="&#xc5;" u2="&#x2122;" k="139" />
<hkern u1="&#xc5;" u2="&#xf0;" k="30" />
<hkern u1="&#xc5;" u2="&#xd0;" k="4" />
<hkern u1="&#xc5;" u2="&#xba;" k="122" />
<hkern u1="&#xc5;" u2="&#xae;" k="45" />
<hkern u1="&#xc5;" u2="&#xaa;" k="110" />
<hkern u1="&#xc5;" u2="&#xa9;" k="44" />
<hkern u1="&#xc5;" u2="&#x7d;" k="30" />
<hkern u1="&#xc5;" u2="&#x7c;" k="9" />
<hkern u1="&#xc5;" u2="]" k="38" />
<hkern u1="&#xc5;" u2="\" k="105" />
<hkern u1="&#xc5;" u2="&#x3f;" k="39" />
<hkern u1="&#xc5;" u2="&#x2a;" k="134" />
<hkern u1="&#xc5;" u2="&#x20;" k="66" />
<hkern u1="&#xc6;" u2="&#xf0;" k="60" />
<hkern u1="&#xc6;" u2="&#xef;" k="-1" />
<hkern u1="&#xc6;" u2="&#xee;" k="-2" />
<hkern u1="&#xc6;" u2="&#xec;" k="-4" />
<hkern u1="&#xc7;" u2="&#xf0;" k="25" />
<hkern u1="&#xc7;" u2="&#xef;" k="-19" />
<hkern u1="&#xc7;" u2="&#xee;" k="-10" />
<hkern u1="&#xc8;" u2="&#xf0;" k="60" />
<hkern u1="&#xc8;" u2="&#xef;" k="-1" />
<hkern u1="&#xc8;" u2="&#xee;" k="-2" />
<hkern u1="&#xc8;" u2="&#xec;" k="-4" />
<hkern u1="&#xc9;" u2="&#xf0;" k="60" />
<hkern u1="&#xc9;" u2="&#xef;" k="-1" />
<hkern u1="&#xc9;" u2="&#xee;" k="-2" />
<hkern u1="&#xc9;" u2="&#xec;" k="-4" />
<hkern u1="&#xca;" u2="&#xf0;" k="60" />
<hkern u1="&#xca;" u2="&#xef;" k="-1" />
<hkern u1="&#xca;" u2="&#xee;" k="-2" />
<hkern u1="&#xca;" u2="&#xec;" k="-4" />
<hkern u1="&#xcb;" u2="&#xf0;" k="60" />
<hkern u1="&#xcb;" u2="&#xef;" k="-1" />
<hkern u1="&#xcb;" u2="&#xee;" k="-2" />
<hkern u1="&#xcb;" u2="&#xec;" k="-4" />
<hkern u1="&#xcc;" u2="&#xf0;" k="34" />
<hkern u1="&#xcd;" u2="&#xf0;" k="34" />
<hkern u1="&#xce;" u2="&#xf0;" k="34" />
<hkern u1="&#xce;" u2="&#x7d;" k="-19" />
<hkern u1="&#xce;" u2="&#x7c;" k="-22" />
<hkern u1="&#xce;" u2="]" k="-22" />
<hkern u1="&#xcf;" u2="&#xf0;" k="34" />
<hkern u1="&#xcf;" u2="&#x7d;" k="-16" />
<hkern u1="&#xcf;" u2="&#x7c;" k="-16" />
<hkern u1="&#xcf;" u2="]" k="-17" />
<hkern u1="&#xd0;" u2="&#x2122;" k="38" />
<hkern u1="&#xd0;" u2="&#xf0;" k="14" />
<hkern u1="&#xd0;" u2="&#xd0;" k="4" />
<hkern u1="&#xd0;" u2="&#x7d;" k="40" />
<hkern u1="&#xd0;" u2="]" k="49" />
<hkern u1="&#xd0;" u2="&#x2c;" k="71" />
<hkern u1="&#xd0;" u2="&#x29;" k="41" />
<hkern u1="&#xd1;" u2="&#xf0;" k="34" />
<hkern u1="&#xd2;" u2="&#x2122;" k="36" />
<hkern u1="&#xd2;" u2="&#xf0;" k="14" />
<hkern u1="&#xd2;" u2="&#xd0;" k="4" />
<hkern u1="&#xd2;" u2="&#x7d;" k="40" />
<hkern u1="&#xd2;" u2="]" k="47" />
<hkern u1="&#xd2;" u2="&#x2c;" k="70" />
<hkern u1="&#xd2;" u2="&#x29;" k="40" />
<hkern u1="&#xd3;" u2="&#x2122;" k="36" />
<hkern u1="&#xd3;" u2="&#xf0;" k="14" />
<hkern u1="&#xd3;" u2="&#xd0;" k="4" />
<hkern u1="&#xd3;" u2="&#x7d;" k="40" />
<hkern u1="&#xd3;" u2="]" k="47" />
<hkern u1="&#xd3;" u2="&#x2c;" k="70" />
<hkern u1="&#xd3;" u2="&#x29;" k="40" />
<hkern u1="&#xd4;" u2="&#x2122;" k="36" />
<hkern u1="&#xd4;" u2="&#xf0;" k="14" />
<hkern u1="&#xd4;" u2="&#xd0;" k="4" />
<hkern u1="&#xd4;" u2="&#x7d;" k="40" />
<hkern u1="&#xd4;" u2="]" k="47" />
<hkern u1="&#xd4;" u2="&#x2c;" k="70" />
<hkern u1="&#xd4;" u2="&#x29;" k="40" />
<hkern u1="&#xd5;" u2="&#x2122;" k="36" />
<hkern u1="&#xd5;" u2="&#xf0;" k="14" />
<hkern u1="&#xd5;" u2="&#xd0;" k="4" />
<hkern u1="&#xd5;" u2="&#x7d;" k="40" />
<hkern u1="&#xd5;" u2="]" k="47" />
<hkern u1="&#xd5;" u2="&#x2c;" k="70" />
<hkern u1="&#xd5;" u2="&#x29;" k="40" />
<hkern u1="&#xd6;" u2="&#x2122;" k="36" />
<hkern u1="&#xd6;" u2="&#xf0;" k="14" />
<hkern u1="&#xd6;" u2="&#xd0;" k="4" />
<hkern u1="&#xd6;" u2="&#x7d;" k="40" />
<hkern u1="&#xd6;" u2="]" k="47" />
<hkern u1="&#xd6;" u2="&#x2c;" k="70" />
<hkern u1="&#xd6;" u2="&#x29;" k="40" />
<hkern u1="&#xd8;" u2="&#x2122;" k="36" />
<hkern u1="&#xd8;" u2="&#x178;" k="75" />
<hkern u1="&#xd8;" u2="&#xf0;" k="14" />
<hkern u1="&#xd8;" u2="&#xdd;" k="75" />
<hkern u1="&#xd8;" u2="&#xd0;" k="4" />
<hkern u1="&#xd8;" u2="&#x7d;" k="40" />
<hkern u1="&#xd8;" u2="]" k="47" />
<hkern u1="&#xd8;" u2="Y" k="75" />
<hkern u1="&#xd8;" u2="&#x2c;" k="70" />
<hkern u1="&#xd8;" u2="&#x29;" k="40" />
<hkern u1="&#xd9;" u2="&#xf0;" k="44" />
<hkern u1="&#xd9;" u2="&#xee;" k="18" />
<hkern u1="&#xd9;" u2="&#xed;" k="28" />
<hkern u1="&#xd9;" u2="&#xec;" k="22" />
<hkern u1="&#xd9;" u2="&#x2f;" k="9" />
<hkern u1="&#xd9;" u2="&#x2c;" k="65" />
<hkern u1="&#xda;" u2="&#xf0;" k="44" />
<hkern u1="&#xda;" u2="&#xee;" k="18" />
<hkern u1="&#xda;" u2="&#xed;" k="28" />
<hkern u1="&#xda;" u2="&#xec;" k="22" />
<hkern u1="&#xda;" u2="&#x2f;" k="9" />
<hkern u1="&#xda;" u2="&#x2c;" k="65" />
<hkern u1="&#xdb;" u2="&#xf0;" k="44" />
<hkern u1="&#xdb;" u2="&#xee;" k="18" />
<hkern u1="&#xdb;" u2="&#xed;" k="28" />
<hkern u1="&#xdb;" u2="&#xec;" k="22" />
<hkern u1="&#xdb;" u2="&#x2f;" k="9" />
<hkern u1="&#xdb;" u2="&#x2c;" k="65" />
<hkern u1="&#xdc;" u2="&#xf0;" k="44" />
<hkern u1="&#xdc;" u2="&#xee;" k="18" />
<hkern u1="&#xdc;" u2="&#xed;" k="28" />
<hkern u1="&#xdc;" u2="&#xec;" k="22" />
<hkern u1="&#xdc;" u2="&#x2f;" k="9" />
<hkern u1="&#xdc;" u2="&#x2c;" k="65" />
<hkern u1="&#xdd;" g2="uniFB02" k="86" />
<hkern u1="&#xdd;" u2="&#x2122;" k="-28" />
<hkern u1="&#xdd;" u2="&#xff;" k="98" />
<hkern u1="&#xdd;" u2="&#xf9;" k="142" />
<hkern u1="&#xdd;" u2="&#xf6;" k="183" />
<hkern u1="&#xdd;" u2="&#xf2;" k="178" />
<hkern u1="&#xdd;" u2="&#xf0;" k="134" />
<hkern u1="&#xdd;" u2="&#xef;" k="-18" />
<hkern u1="&#xdd;" u2="&#xed;" k="39" />
<hkern u1="&#xdd;" u2="&#xec;" k="-34" />
<hkern u1="&#xdd;" u2="&#xeb;" k="180" />
<hkern u1="&#xdd;" u2="&#xe8;" k="169" />
<hkern u1="&#xdd;" u2="&#xdf;" k="96" />
<hkern u1="&#xdd;" u2="&#xae;" k="11" />
<hkern u1="&#xdd;" u2="&#xa9;" k="11" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-3" />
<hkern u1="&#xdd;" u2="]" k="-5" />
<hkern u1="&#xdd;" u2="\" k="-4" />
<hkern u1="&#xdd;" u2="&#x40;" k="74" />
<hkern u1="&#xdd;" u2="&#x3b;" k="65" />
<hkern u1="&#xdd;" u2="&#x3a;" k="67" />
<hkern u1="&#xdd;" u2="&#x2f;" k="122" />
<hkern u1="&#xdd;" u2="&#x2c;" k="165" />
<hkern u1="&#xdd;" u2="&#x26;" k="60" />
<hkern u1="&#xdd;" u2="&#x20;" k="67" />
<hkern u1="&#xde;" u2="&#x2122;" k="41" />
<hkern u1="&#xde;" u2="&#x2026;" k="92" />
<hkern u1="&#xde;" u2="&#x201e;" k="85" />
<hkern u1="&#xde;" u2="&#x201d;" k="39" />
<hkern u1="&#xde;" u2="&#x201c;" k="48" />
<hkern u1="&#xde;" u2="&#x201a;" k="85" />
<hkern u1="&#xde;" u2="&#x2019;" k="39" />
<hkern u1="&#xde;" u2="&#x2018;" k="48" />
<hkern u1="&#xde;" u2="&#x178;" k="95" />
<hkern u1="&#xde;" u2="&#xfe;" k="8" />
<hkern u1="&#xde;" u2="&#xf1;" k="8" />
<hkern u1="&#xde;" u2="&#xef;" k="8" />
<hkern u1="&#xde;" u2="&#xee;" k="8" />
<hkern u1="&#xde;" u2="&#xed;" k="8" />
<hkern u1="&#xde;" u2="&#xec;" k="8" />
<hkern u1="&#xde;" u2="&#xe6;" k="7" />
<hkern u1="&#xde;" u2="&#xe5;" k="7" />
<hkern u1="&#xde;" u2="&#xe4;" k="7" />
<hkern u1="&#xde;" u2="&#xe3;" k="7" />
<hkern u1="&#xde;" u2="&#xe2;" k="7" />
<hkern u1="&#xde;" u2="&#xe1;" k="7" />
<hkern u1="&#xde;" u2="&#xe0;" k="7" />
<hkern u1="&#xde;" u2="&#xdd;" k="95" />
<hkern u1="&#xde;" u2="&#xc5;" k="32" />
<hkern u1="&#xde;" u2="&#xc4;" k="32" />
<hkern u1="&#xde;" u2="&#xc3;" k="32" />
<hkern u1="&#xde;" u2="&#xc2;" k="32" />
<hkern u1="&#xde;" u2="&#xc1;" k="32" />
<hkern u1="&#xde;" u2="&#xc0;" k="32" />
<hkern u1="&#xde;" u2="&#x7d;" k="25" />
<hkern u1="&#xde;" u2="z" k="7" />
<hkern u1="&#xde;" u2="x" k="23" />
<hkern u1="&#xde;" u2="r" k="8" />
<hkern u1="&#xde;" u2="q" k="7" />
<hkern u1="&#xde;" u2="p" k="8" />
<hkern u1="&#xde;" u2="n" k="8" />
<hkern u1="&#xde;" u2="m" k="8" />
<hkern u1="&#xde;" u2="l" k="8" />
<hkern u1="&#xde;" u2="k" k="8" />
<hkern u1="&#xde;" u2="j" k="8" />
<hkern u1="&#xde;" u2="i" k="8" />
<hkern u1="&#xde;" u2="h" k="8" />
<hkern u1="&#xde;" u2="b" k="8" />
<hkern u1="&#xde;" u2="a" k="7" />
<hkern u1="&#xde;" u2="]" k="43" />
<hkern u1="&#xde;" u2="Z" k="48" />
<hkern u1="&#xde;" u2="Y" k="95" />
<hkern u1="&#xde;" u2="X" k="75" />
<hkern u1="&#xde;" u2="W" k="17" />
<hkern u1="&#xde;" u2="V" k="41" />
<hkern u1="&#xde;" u2="T" k="94" />
<hkern u1="&#xde;" u2="S" k="8" />
<hkern u1="&#xde;" u2="J" k="95" />
<hkern u1="&#xde;" u2="A" k="32" />
<hkern u1="&#xde;" u2="&#x3f;" k="28" />
<hkern u1="&#xde;" u2="&#x2e;" k="92" />
<hkern u1="&#xde;" u2="&#x2c;" k="74" />
<hkern u1="&#xde;" u2="&#x2a;" k="30" />
<hkern u1="&#xde;" u2="&#x29;" k="34" />
<hkern u1="&#xdf;" g2="uniFB04" k="17" />
<hkern u1="&#xdf;" g2="uniFB03" k="17" />
<hkern u1="&#xdf;" g2="uniFB02" k="17" />
<hkern u1="&#xdf;" g2="uniFB01" k="17" />
<hkern u1="&#xdf;" u2="&#x2122;" k="10" />
<hkern u1="&#xdf;" u2="&#x201d;" k="7" />
<hkern u1="&#xdf;" u2="&#x2019;" k="7" />
<hkern u1="&#xdf;" u2="&#xff;" k="32" />
<hkern u1="&#xdf;" u2="&#xfd;" k="32" />
<hkern u1="&#xdf;" u2="&#xdf;" k="17" />
<hkern u1="&#xdf;" u2="&#x7d;" k="39" />
<hkern u1="&#xdf;" u2="y" k="32" />
<hkern u1="&#xdf;" u2="x" k="4" />
<hkern u1="&#xdf;" u2="w" k="18" />
<hkern u1="&#xdf;" u2="v" k="31" />
<hkern u1="&#xdf;" u2="t" k="17" />
<hkern u1="&#xdf;" u2="g" k="4" />
<hkern u1="&#xdf;" u2="f" k="17" />
<hkern u1="&#xdf;" u2="]" k="43" />
<hkern u1="&#xdf;" u2="\" k="9" />
<hkern u1="&#xdf;" u2="&#x2a;" k="7" />
<hkern u1="&#xe0;" u2="&#x2122;" k="36" />
<hkern u1="&#xe0;" u2="&#x7d;" k="44" />
<hkern u1="&#xe0;" u2="]" k="52" />
<hkern u1="&#xe0;" u2="\" k="50" />
<hkern u1="&#xe0;" u2="&#x3f;" k="9" />
<hkern u1="&#xe0;" u2="&#x2a;" k="28" />
<hkern u1="&#xe1;" u2="&#x2122;" k="36" />
<hkern u1="&#xe1;" u2="&#x7d;" k="44" />
<hkern u1="&#xe1;" u2="]" k="52" />
<hkern u1="&#xe1;" u2="\" k="47" />
<hkern u1="&#xe1;" u2="&#x3f;" k="9" />
<hkern u1="&#xe1;" u2="&#x2a;" k="28" />
<hkern u1="&#xe2;" u2="&#x2122;" k="36" />
<hkern u1="&#xe2;" u2="&#x7d;" k="44" />
<hkern u1="&#xe2;" u2="]" k="52" />
<hkern u1="&#xe2;" u2="\" k="50" />
<hkern u1="&#xe2;" u2="&#x3f;" k="9" />
<hkern u1="&#xe2;" u2="&#x2a;" k="28" />
<hkern u1="&#xe3;" u2="&#x2122;" k="36" />
<hkern u1="&#xe3;" u2="&#x7d;" k="44" />
<hkern u1="&#xe3;" u2="]" k="52" />
<hkern u1="&#xe3;" u2="\" k="50" />
<hkern u1="&#xe3;" u2="&#x3f;" k="9" />
<hkern u1="&#xe3;" u2="&#x2a;" k="28" />
<hkern u1="&#xe4;" u2="&#x2122;" k="36" />
<hkern u1="&#xe4;" u2="&#x7d;" k="44" />
<hkern u1="&#xe4;" u2="]" k="52" />
<hkern u1="&#xe4;" u2="\" k="50" />
<hkern u1="&#xe4;" u2="&#x3f;" k="9" />
<hkern u1="&#xe4;" u2="&#x2a;" k="28" />
<hkern u1="&#xe5;" u2="&#x2122;" k="36" />
<hkern u1="&#xe5;" u2="&#x7d;" k="44" />
<hkern u1="&#xe5;" u2="]" k="52" />
<hkern u1="&#xe5;" u2="\" k="50" />
<hkern u1="&#xe5;" u2="&#x3f;" k="9" />
<hkern u1="&#xe5;" u2="&#x2a;" k="28" />
<hkern u1="&#xe6;" u2="&#x2122;" k="49" />
<hkern u1="&#xe6;" u2="&#xf0;" k="8" />
<hkern u1="&#xe6;" u2="&#xba;" k="31" />
<hkern u1="&#xe6;" u2="&#xaa;" k="7" />
<hkern u1="&#xe6;" u2="&#x7d;" k="52" />
<hkern u1="&#xe6;" u2="]" k="57" />
<hkern u1="&#xe6;" u2="\" k="66" />
<hkern u1="&#xe6;" u2="&#x3f;" k="46" />
<hkern u1="&#xe6;" u2="&#x2a;" k="56" />
<hkern u1="&#xe6;" u2="&#x29;" k="40" />
<hkern u1="&#xe7;" u2="&#x2122;" k="46" />
<hkern u1="&#xe7;" u2="&#xf0;" k="19" />
<hkern u1="&#xe7;" u2="&#xba;" k="6" />
<hkern u1="&#xe7;" u2="&#x7d;" k="50" />
<hkern u1="&#xe7;" u2="]" k="55" />
<hkern u1="&#xe7;" u2="\" k="58" />
<hkern u1="&#xe7;" u2="&#x3f;" k="39" />
<hkern u1="&#xe7;" u2="&#x2a;" k="50" />
<hkern u1="&#xe7;" u2="&#x29;" k="27" />
<hkern u1="&#xe8;" u2="&#x2122;" k="49" />
<hkern u1="&#xe8;" u2="&#xf0;" k="8" />
<hkern u1="&#xe8;" u2="&#xba;" k="31" />
<hkern u1="&#xe8;" u2="&#xaa;" k="7" />
<hkern u1="&#xe8;" u2="&#x7d;" k="52" />
<hkern u1="&#xe8;" u2="]" k="57" />
<hkern u1="&#xe8;" u2="\" k="66" />
<hkern u1="&#xe8;" u2="&#x3f;" k="46" />
<hkern u1="&#xe8;" u2="&#x2a;" k="56" />
<hkern u1="&#xe8;" u2="&#x29;" k="40" />
<hkern u1="&#xe9;" u2="&#x2122;" k="49" />
<hkern u1="&#xe9;" u2="&#xf0;" k="8" />
<hkern u1="&#xe9;" u2="&#xba;" k="31" />
<hkern u1="&#xe9;" u2="&#xaa;" k="7" />
<hkern u1="&#xe9;" u2="&#x7d;" k="52" />
<hkern u1="&#xe9;" u2="]" k="57" />
<hkern u1="&#xe9;" u2="\" k="66" />
<hkern u1="&#xe9;" u2="&#x3f;" k="46" />
<hkern u1="&#xe9;" u2="&#x2a;" k="56" />
<hkern u1="&#xe9;" u2="&#x29;" k="40" />
<hkern u1="&#xea;" u2="&#x2122;" k="49" />
<hkern u1="&#xea;" u2="&#xf0;" k="8" />
<hkern u1="&#xea;" u2="&#xba;" k="31" />
<hkern u1="&#xea;" u2="&#xaa;" k="7" />
<hkern u1="&#xea;" u2="&#x7d;" k="52" />
<hkern u1="&#xea;" u2="]" k="57" />
<hkern u1="&#xea;" u2="\" k="66" />
<hkern u1="&#xea;" u2="&#x3f;" k="46" />
<hkern u1="&#xea;" u2="&#x2a;" k="56" />
<hkern u1="&#xea;" u2="&#x29;" k="40" />
<hkern u1="&#xeb;" u2="&#x2122;" k="49" />
<hkern u1="&#xeb;" u2="&#xf0;" k="8" />
<hkern u1="&#xeb;" u2="&#xba;" k="31" />
<hkern u1="&#xeb;" u2="&#xaa;" k="7" />
<hkern u1="&#xeb;" u2="&#x7d;" k="52" />
<hkern u1="&#xeb;" u2="]" k="57" />
<hkern u1="&#xeb;" u2="\" k="66" />
<hkern u1="&#xeb;" u2="&#x3f;" k="46" />
<hkern u1="&#xeb;" u2="&#x2a;" k="56" />
<hkern u1="&#xeb;" u2="&#x29;" k="40" />
<hkern u1="&#xec;" u2="&#xef;" k="-5" />
<hkern u1="&#xed;" u2="&#x2122;" k="-19" />
<hkern u1="&#xed;" u2="&#x201d;" k="-11" />
<hkern u1="&#xed;" u2="&#x2019;" k="-11" />
<hkern u1="&#xed;" u2="&#xfe;" k="-4" />
<hkern u1="&#xed;" u2="&#xef;" k="-5" />
<hkern u1="&#xed;" u2="&#x7d;" k="-23" />
<hkern u1="&#xed;" u2="&#x7c;" k="-22" />
<hkern u1="&#xed;" u2="l" k="-4" />
<hkern u1="&#xed;" u2="k" k="-4" />
<hkern u1="&#xed;" u2="h" k="-4" />
<hkern u1="&#xed;" u2="b" k="-4" />
<hkern u1="&#xed;" u2="]" k="-24" />
<hkern u1="&#xed;" u2="\" k="-20" />
<hkern u1="&#xed;" u2="&#x29;" k="-20" />
<hkern u1="&#xed;" u2="&#x27;" k="-11" />
<hkern u1="&#xed;" u2="&#x22;" k="-11" />
<hkern u1="&#xee;" u2="&#x2122;" k="-6" />
<hkern u1="&#xee;" u2="&#x201d;" k="-4" />
<hkern u1="&#xee;" u2="&#x201c;" k="-23" />
<hkern u1="&#xee;" u2="&#x2019;" k="-4" />
<hkern u1="&#xee;" u2="&#x2018;" k="-23" />
<hkern u1="&#xee;" u2="&#xfe;" k="-7" />
<hkern u1="&#xee;" u2="&#xef;" k="-5" />
<hkern u1="&#xee;" u2="&#xba;" k="-25" />
<hkern u1="&#xee;" u2="&#x7c;" k="-14" />
<hkern u1="&#xee;" u2="l" k="-7" />
<hkern u1="&#xee;" u2="k" k="-7" />
<hkern u1="&#xee;" u2="h" k="-7" />
<hkern u1="&#xee;" u2="b" k="-7" />
<hkern u1="&#xee;" u2="&#x2a;" k="-5" />
<hkern u1="&#xee;" u2="&#x27;" k="-18" />
<hkern u1="&#xee;" u2="&#x22;" k="-18" />
<hkern u1="&#xee;" u2="&#x21;" k="-5" />
<hkern u1="&#xef;" u2="&#x2122;" k="-24" />
<hkern u1="&#xef;" u2="&#x201c;" k="-19" />
<hkern u1="&#xef;" u2="&#x2018;" k="-19" />
<hkern u1="&#xef;" u2="&#xfe;" k="-5" />
<hkern u1="&#xef;" u2="&#xef;" k="-5" />
<hkern u1="&#xef;" u2="&#xee;" k="-10" />
<hkern u1="&#xef;" u2="&#xed;" k="-10" />
<hkern u1="&#xef;" u2="&#xec;" k="-10" />
<hkern u1="&#xef;" u2="&#xba;" k="-19" />
<hkern u1="&#xef;" u2="&#xaa;" k="-6" />
<hkern u1="&#xef;" u2="&#x7d;" k="-11" />
<hkern u1="&#xef;" u2="&#x7c;" k="-15" />
<hkern u1="&#xef;" u2="l" k="-5" />
<hkern u1="&#xef;" u2="k" k="-5" />
<hkern u1="&#xef;" u2="j" k="-13" />
<hkern u1="&#xef;" u2="i" k="-10" />
<hkern u1="&#xef;" u2="h" k="-5" />
<hkern u1="&#xef;" u2="b" k="-5" />
<hkern u1="&#xef;" u2="]" k="-11" />
<hkern u1="&#xef;" u2="\" k="-10" />
<hkern u1="&#xef;" u2="&#x3f;" k="-20" />
<hkern u1="&#xef;" u2="&#x2a;" k="-23" />
<hkern u1="&#xef;" u2="&#x29;" k="-10" />
<hkern u1="&#xef;" u2="&#x27;" k="-15" />
<hkern u1="&#xef;" u2="&#x22;" k="-15" />
<hkern u1="&#xf0;" g2="uniFB04" k="4" />
<hkern u1="&#xf0;" g2="uniFB03" k="4" />
<hkern u1="&#xf0;" g2="uniFB02" k="4" />
<hkern u1="&#xf0;" g2="uniFB01" k="4" />
<hkern u1="&#xf0;" u2="&#x2122;" k="32" />
<hkern u1="&#xf0;" u2="&#x201d;" k="27" />
<hkern u1="&#xf0;" u2="&#x201c;" k="27" />
<hkern u1="&#xf0;" u2="&#x2019;" k="27" />
<hkern u1="&#xf0;" u2="&#x2018;" k="27" />
<hkern u1="&#xf0;" u2="&#xff;" k="5" />
<hkern u1="&#xf0;" u2="&#xfd;" k="5" />
<hkern u1="&#xf0;" u2="&#xdf;" k="4" />
<hkern u1="&#xf0;" u2="&#x7d;" k="43" />
<hkern u1="&#xf0;" u2="z" k="14" />
<hkern u1="&#xf0;" u2="y" k="5" />
<hkern u1="&#xf0;" u2="x" k="33" />
<hkern u1="&#xf0;" u2="v" k="5" />
<hkern u1="&#xf0;" u2="f" k="4" />
<hkern u1="&#xf0;" u2="]" k="50" />
<hkern u1="&#xf0;" u2="\" k="27" />
<hkern u1="&#xf0;" u2="&#x3f;" k="25" />
<hkern u1="&#xf0;" u2="&#x2a;" k="33" />
<hkern u1="&#xf0;" u2="&#x29;" k="42" />
<hkern u1="&#xf1;" u2="&#x2122;" k="50" />
<hkern u1="&#xf1;" u2="&#xba;" k="35" />
<hkern u1="&#xf1;" u2="&#xaa;" k="8" />
<hkern u1="&#xf1;" u2="&#x7d;" k="49" />
<hkern u1="&#xf1;" u2="]" k="55" />
<hkern u1="&#xf1;" u2="\" k="70" />
<hkern u1="&#xf1;" u2="&#x3f;" k="45" />
<hkern u1="&#xf1;" u2="&#x2a;" k="56" />
<hkern u1="&#xf1;" u2="&#x29;" k="25" />
<hkern u1="&#xf2;" u2="&#x2122;" k="59" />
<hkern u1="&#xf2;" u2="&#xba;" k="46" />
<hkern u1="&#xf2;" u2="&#xaa;" k="34" />
<hkern u1="&#xf2;" u2="&#x7d;" k="55" />
<hkern u1="&#xf2;" u2="&#x7c;" k="9" />
<hkern u1="&#xf2;" u2="]" k="65" />
<hkern u1="&#xf2;" u2="\" k="78" />
<hkern u1="&#xf2;" u2="&#x3f;" k="57" />
<hkern u1="&#xf2;" u2="&#x2a;" k="74" />
<hkern u1="&#xf2;" u2="&#x29;" k="57" />
<hkern u1="&#xf3;" u2="&#x2122;" k="59" />
<hkern u1="&#xf3;" u2="&#xba;" k="46" />
<hkern u1="&#xf3;" u2="&#xaa;" k="34" />
<hkern u1="&#xf3;" u2="&#x7d;" k="55" />
<hkern u1="&#xf3;" u2="&#x7c;" k="9" />
<hkern u1="&#xf3;" u2="]" k="65" />
<hkern u1="&#xf3;" u2="\" k="78" />
<hkern u1="&#xf3;" u2="&#x3f;" k="57" />
<hkern u1="&#xf3;" u2="&#x2a;" k="74" />
<hkern u1="&#xf3;" u2="&#x29;" k="57" />
<hkern u1="&#xf4;" u2="&#x2122;" k="59" />
<hkern u1="&#xf4;" u2="&#xba;" k="46" />
<hkern u1="&#xf4;" u2="&#xaa;" k="34" />
<hkern u1="&#xf4;" u2="&#x7d;" k="55" />
<hkern u1="&#xf4;" u2="&#x7c;" k="9" />
<hkern u1="&#xf4;" u2="]" k="65" />
<hkern u1="&#xf4;" u2="\" k="78" />
<hkern u1="&#xf4;" u2="&#x3f;" k="57" />
<hkern u1="&#xf4;" u2="&#x2a;" k="74" />
<hkern u1="&#xf4;" u2="&#x29;" k="57" />
<hkern u1="&#xf5;" u2="&#x2122;" k="59" />
<hkern u1="&#xf5;" u2="&#xba;" k="46" />
<hkern u1="&#xf5;" u2="&#xaa;" k="34" />
<hkern u1="&#xf5;" u2="&#x7d;" k="55" />
<hkern u1="&#xf5;" u2="&#x7c;" k="9" />
<hkern u1="&#xf5;" u2="]" k="65" />
<hkern u1="&#xf5;" u2="\" k="78" />
<hkern u1="&#xf5;" u2="&#x3f;" k="57" />
<hkern u1="&#xf5;" u2="&#x2a;" k="74" />
<hkern u1="&#xf5;" u2="&#x29;" k="57" />
<hkern u1="&#xf6;" u2="&#x2122;" k="59" />
<hkern u1="&#xf6;" u2="&#xba;" k="46" />
<hkern u1="&#xf6;" u2="&#xaa;" k="34" />
<hkern u1="&#xf6;" u2="&#x7d;" k="55" />
<hkern u1="&#xf6;" u2="&#x7c;" k="9" />
<hkern u1="&#xf6;" u2="]" k="65" />
<hkern u1="&#xf6;" u2="\" k="78" />
<hkern u1="&#xf6;" u2="&#x3f;" k="57" />
<hkern u1="&#xf6;" u2="&#x2a;" k="74" />
<hkern u1="&#xf6;" u2="&#x29;" k="57" />
<hkern u1="&#xf8;" u2="&#x2122;" k="59" />
<hkern u1="&#xf8;" u2="&#xba;" k="43" />
<hkern u1="&#xf8;" u2="&#xaa;" k="34" />
<hkern u1="&#xf8;" u2="&#x7d;" k="55" />
<hkern u1="&#xf8;" u2="&#x7c;" k="9" />
<hkern u1="&#xf8;" u2="]" k="65" />
<hkern u1="&#xf8;" u2="\" k="78" />
<hkern u1="&#xf8;" u2="&#x3f;" k="57" />
<hkern u1="&#xf8;" u2="&#x2a;" k="69" />
<hkern u1="&#xf8;" u2="&#x29;" k="57" />
<hkern u1="&#xf9;" u2="&#x2122;" k="34" />
<hkern u1="&#xf9;" u2="&#x7d;" k="44" />
<hkern u1="&#xf9;" u2="]" k="51" />
<hkern u1="&#xf9;" u2="\" k="46" />
<hkern u1="&#xf9;" u2="&#x3f;" k="8" />
<hkern u1="&#xf9;" u2="&#x2a;" k="23" />
<hkern u1="&#xfa;" u2="&#x2122;" k="34" />
<hkern u1="&#xfa;" u2="&#x7d;" k="44" />
<hkern u1="&#xfa;" u2="]" k="51" />
<hkern u1="&#xfa;" u2="\" k="46" />
<hkern u1="&#xfa;" u2="&#x3f;" k="8" />
<hkern u1="&#xfa;" u2="&#x2a;" k="23" />
<hkern u1="&#xfb;" u2="&#x2122;" k="34" />
<hkern u1="&#xfb;" u2="&#x7d;" k="44" />
<hkern u1="&#xfb;" u2="]" k="51" />
<hkern u1="&#xfb;" u2="\" k="46" />
<hkern u1="&#xfb;" u2="&#x3f;" k="8" />
<hkern u1="&#xfb;" u2="&#x2a;" k="23" />
<hkern u1="&#xfc;" u2="&#x2122;" k="34" />
<hkern u1="&#xfc;" u2="&#x7d;" k="44" />
<hkern u1="&#xfc;" u2="]" k="51" />
<hkern u1="&#xfc;" u2="\" k="46" />
<hkern u1="&#xfc;" u2="&#x3f;" k="8" />
<hkern u1="&#xfc;" u2="&#x2a;" k="23" />
<hkern u1="&#xfd;" u2="&#xf0;" k="29" />
<hkern u1="&#xfd;" u2="&#x7d;" k="39" />
<hkern u1="&#xfd;" u2="]" k="48" />
<hkern u1="&#xfd;" u2="&#x2f;" k="59" />
<hkern u1="&#xfd;" u2="&#x2c;" k="80" />
<hkern u1="&#xfd;" u2="&#x29;" k="30" />
<hkern u1="&#xfd;" u2="&#x20;" k="44" />
<hkern u1="&#xfe;" u2="&#x2122;" k="56" />
<hkern u1="&#xfe;" u2="&#xba;" k="41" />
<hkern u1="&#xfe;" u2="&#xaa;" k="30" />
<hkern u1="&#xfe;" u2="&#x7d;" k="55" />
<hkern u1="&#xfe;" u2="&#x7c;" k="9" />
<hkern u1="&#xfe;" u2="]" k="65" />
<hkern u1="&#xfe;" u2="\" k="72" />
<hkern u1="&#xfe;" u2="&#x3f;" k="57" />
<hkern u1="&#xfe;" u2="&#x2f;" k="9" />
<hkern u1="&#xfe;" u2="&#x2a;" k="68" />
<hkern u1="&#xfe;" u2="&#x29;" k="58" />
<hkern u1="&#xff;" u2="&#xf0;" k="29" />
<hkern u1="&#xff;" u2="&#x7d;" k="39" />
<hkern u1="&#xff;" u2="]" k="48" />
<hkern u1="&#xff;" u2="&#x2f;" k="59" />
<hkern u1="&#xff;" u2="&#x2c;" k="80" />
<hkern u1="&#xff;" u2="&#x29;" k="30" />
<hkern u1="&#xff;" u2="&#x20;" k="44" />
<hkern u1="&#x152;" u2="&#xf0;" k="60" />
<hkern u1="&#x152;" u2="&#xef;" k="-1" />
<hkern u1="&#x152;" u2="&#xee;" k="-2" />
<hkern u1="&#x152;" u2="&#xec;" k="-4" />
<hkern u1="&#x153;" u2="&#x2122;" k="49" />
<hkern u1="&#x153;" u2="&#xf0;" k="8" />
<hkern u1="&#x153;" u2="&#xba;" k="31" />
<hkern u1="&#x153;" u2="&#xaa;" k="7" />
<hkern u1="&#x153;" u2="&#x7d;" k="52" />
<hkern u1="&#x153;" u2="]" k="57" />
<hkern u1="&#x153;" u2="\" k="66" />
<hkern u1="&#x153;" u2="&#x3f;" k="46" />
<hkern u1="&#x153;" u2="&#x2a;" k="56" />
<hkern u1="&#x153;" u2="&#x29;" k="40" />
<hkern u1="&#x178;" g2="uniFB02" k="86" />
<hkern u1="&#x178;" u2="&#x2122;" k="-28" />
<hkern u1="&#x178;" u2="&#xff;" k="98" />
<hkern u1="&#x178;" u2="&#xf9;" k="142" />
<hkern u1="&#x178;" u2="&#xf6;" k="183" />
<hkern u1="&#x178;" u2="&#xf2;" k="178" />
<hkern u1="&#x178;" u2="&#xf0;" k="134" />
<hkern u1="&#x178;" u2="&#xef;" k="-18" />
<hkern u1="&#x178;" u2="&#xed;" k="39" />
<hkern u1="&#x178;" u2="&#xec;" k="-34" />
<hkern u1="&#x178;" u2="&#xeb;" k="180" />
<hkern u1="&#x178;" u2="&#xe8;" k="169" />
<hkern u1="&#x178;" u2="&#xdf;" k="96" />
<hkern u1="&#x178;" u2="&#xae;" k="11" />
<hkern u1="&#x178;" u2="&#xa9;" k="11" />
<hkern u1="&#x178;" u2="&#x7d;" k="-3" />
<hkern u1="&#x178;" u2="]" k="-5" />
<hkern u1="&#x178;" u2="\" k="-4" />
<hkern u1="&#x178;" u2="&#x40;" k="74" />
<hkern u1="&#x178;" u2="&#x3b;" k="65" />
<hkern u1="&#x178;" u2="&#x3a;" k="67" />
<hkern u1="&#x178;" u2="&#x2f;" k="122" />
<hkern u1="&#x178;" u2="&#x2c;" k="165" />
<hkern u1="&#x178;" u2="&#x26;" k="60" />
<hkern u1="&#x178;" u2="&#x20;" k="67" />
<hkern u1="&#x2018;" u2="&#xf0;" k="56" />
<hkern u1="&#x2018;" u2="&#xee;" k="-7" />
<hkern u1="&#x2018;" u2="&#xec;" k="-23" />
<hkern u1="&#x2018;" u2="&#xce;" k="-7" />
<hkern u1="&#x2018;" u2="&#x2c;" k="201" />
<hkern u1="&#x2019;" u2="&#xf0;" k="50" />
<hkern u1="&#x2019;" u2="&#xef;" k="-18" />
<hkern u1="&#x2019;" u2="&#xee;" k="-20" />
<hkern u1="&#x2019;" u2="&#xec;" k="-15" />
<hkern u1="&#x2019;" u2="&#x40;" k="58" />
<hkern u1="&#x2019;" u2="&#x3b;" k="8" />
<hkern u1="&#x2019;" u2="&#x3a;" k="8" />
<hkern u1="&#x2019;" u2="&#x2f;" k="128" />
<hkern u1="&#x2019;" u2="&#x2c;" k="204" />
<hkern u1="&#x2019;" u2="&#x26;" k="52" />
<hkern u1="&#x201c;" u2="&#xf0;" k="56" />
<hkern u1="&#x201c;" u2="&#xee;" k="-7" />
<hkern u1="&#x201c;" u2="&#xec;" k="-23" />
<hkern u1="&#x201c;" u2="&#xce;" k="-7" />
<hkern u1="&#x201c;" u2="&#x2c;" k="201" />
<hkern u1="&#x201d;" u2="&#xf0;" k="50" />
<hkern u1="&#x201d;" u2="&#xef;" k="-18" />
<hkern u1="&#x201d;" u2="&#xee;" k="-20" />
<hkern u1="&#x201d;" u2="&#xec;" k="-15" />
<hkern u1="&#x201d;" u2="&#x40;" k="58" />
<hkern u1="&#x201d;" u2="&#x3b;" k="8" />
<hkern u1="&#x201d;" u2="&#x3a;" k="8" />
<hkern u1="&#x201d;" u2="&#x2f;" k="128" />
<hkern u1="&#x201d;" u2="&#x2c;" k="204" />
<hkern u1="&#x201d;" u2="&#x26;" k="52" />
<hkern g1="uniFB01" u2="&#xef;" k="-5" />
<hkern g1="uniFB02" u2="&#xee;" k="-6" />
<hkern g1="uniFB02" u2="&#xec;" k="-6" />
<hkern g1="uniFB02" u2="&#xb7;" k="60" />
<hkern g1="uniFB03" u2="&#xef;" k="-5" />
<hkern g1="uniFB04" u2="&#xee;" k="-6" />
<hkern g1="uniFB04" u2="&#xec;" k="-6" />
<hkern g1="uniFB04" u2="&#xb7;" k="60" />
<hkern g1="B" 	g2="V" 	k="27" />
<hkern g1="B" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="B" 	g2="t" 	k="27" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="B" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="28" />
<hkern g1="B" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="B" 	g2="T" 	k="54" />
<hkern g1="B" 	g2="W" 	k="4" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="46" />
<hkern g1="B" 	g2="d" 	k="14" />
<hkern g1="B" 	g2="v" 	k="28" />
<hkern g1="B" 	g2="w" 	k="18" />
<hkern g1="B" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="15" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="b,h,k,thorn" 	k="23" />
<hkern g1="B" 	g2="J" 	k="16" />
<hkern g1="B" 	g2="l" 	k="23" />
<hkern g1="B" 	g2="g" 	k="48" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="B" 	g2="s" 	k="42" />
<hkern g1="B" 	g2="AE" 	k="33" />
<hkern g1="B" 	g2="x" 	k="37" />
<hkern g1="B" 	g2="z" 	k="32" />
<hkern g1="B" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="23" />
<hkern g1="B" 	g2="m,n,p,r,ntilde" 	k="24" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="7" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="19" />
<hkern g1="C,Ccedilla" 	g2="hyphen,emdash" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="5" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="15" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="26" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="19" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="3" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,ntilde" 	k="13" />
<hkern g1="D,Eth" 	g2="V" 	k="39" />
<hkern g1="D,Eth" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="D,Eth" 	g2="t" 	k="3" />
<hkern g1="D,Eth" 	g2="y,yacute,ydieresis" 	k="11" />
<hkern g1="D,Eth" 	g2="S" 	k="3" />
<hkern g1="D,Eth" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="11" />
<hkern g1="D,Eth" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="19" />
<hkern g1="D,Eth" 	g2="T" 	k="89" />
<hkern g1="D,Eth" 	g2="W" 	k="17" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="81" />
<hkern g1="D,Eth" 	g2="d" 	k="15" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="D,Eth" 	g2="v" 	k="11" />
<hkern g1="D,Eth" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="D,Eth" 	g2="X" 	k="56" />
<hkern g1="D,Eth" 	g2="b,h,k,thorn" 	k="20" />
<hkern g1="D,Eth" 	g2="J" 	k="77" />
<hkern g1="D,Eth" 	g2="l" 	k="20" />
<hkern g1="D,Eth" 	g2="g" 	k="23" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="27" />
<hkern g1="D,Eth" 	g2="s" 	k="19" />
<hkern g1="D,Eth" 	g2="AE" 	k="52" />
<hkern g1="D,Eth" 	g2="x" 	k="46" />
<hkern g1="D,Eth" 	g2="z" 	k="26" />
<hkern g1="D,Eth" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="22" />
<hkern g1="D,Eth" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="80" />
<hkern g1="D,Eth" 	g2="period,ellipsis" 	k="66" />
<hkern g1="D,Eth" 	g2="Z" 	k="32" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="59" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="54" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="hyphen,emdash" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="59" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="59" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="b,h,k,thorn" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="l" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="m,n,p,r,ntilde" 	k="37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="36" />
<hkern g1="F" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="119" />
<hkern g1="F" 	g2="t" 	k="46" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="49" />
<hkern g1="F" 	g2="S" 	k="49" />
<hkern g1="F" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="53" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="87" />
<hkern g1="F" 	g2="hyphen,emdash" 	k="38" />
<hkern g1="F" 	g2="d" 	k="103" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="F" 	g2="v" 	k="49" />
<hkern g1="F" 	g2="w" 	k="42" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="90" />
<hkern g1="F" 	g2="b,h,k,thorn" 	k="25" />
<hkern g1="F" 	g2="J" 	k="193" />
<hkern g1="F" 	g2="l" 	k="25" />
<hkern g1="F" 	g2="g" 	k="95" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="F" 	g2="s" 	k="100" />
<hkern g1="F" 	g2="AE" 	k="169" />
<hkern g1="F" 	g2="x" 	k="105" />
<hkern g1="F" 	g2="z" 	k="103" />
<hkern g1="F" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="13" />
<hkern g1="F" 	g2="m,n,p,r,ntilde" 	k="96" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="182" />
<hkern g1="F" 	g2="period,ellipsis" 	k="213" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="44" />
<hkern g1="G" 	g2="V" 	k="23" />
<hkern g1="G" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="12" />
<hkern g1="G" 	g2="t" 	k="34" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="43" />
<hkern g1="G" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="33" />
<hkern g1="G" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="22" />
<hkern g1="G" 	g2="T" 	k="12" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="29" />
<hkern g1="G" 	g2="d" 	k="12" />
<hkern g1="G" 	g2="v" 	k="43" />
<hkern g1="G" 	g2="w" 	k="31" />
<hkern g1="G" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="13" />
<hkern g1="G" 	g2="b,h,k,thorn" 	k="19" />
<hkern g1="G" 	g2="l" 	k="19" />
<hkern g1="G" 	g2="g" 	k="19" />
<hkern g1="G" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="G" 	g2="s" 	k="14" />
<hkern g1="G" 	g2="AE" 	k="17" />
<hkern g1="G" 	g2="x" 	k="15" />
<hkern g1="G" 	g2="z" 	k="18" />
<hkern g1="G" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="14" />
<hkern g1="G" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="t" 	k="28" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="y,yacute,ydieresis" 	k="26" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="29" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="d" 	k="30" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="v" 	k="26" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="w" 	k="24" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="32" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="b,h,k,thorn" 	k="14" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="l" 	k="14" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="g" 	k="40" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="s" 	k="34" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="x" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="z" 	k="23" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="14" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="m,n,p,r,ntilde" 	k="14" />
<hkern g1="J" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="40" />
<hkern g1="J" 	g2="t" 	k="20" />
<hkern g1="J" 	g2="y,yacute,ydieresis" 	k="19" />
<hkern g1="J" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="24" />
<hkern g1="J" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="J" 	g2="d" 	k="38" />
<hkern g1="J" 	g2="v" 	k="19" />
<hkern g1="J" 	g2="w" 	k="14" />
<hkern g1="J" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="38" />
<hkern g1="J" 	g2="b,h,k,thorn" 	k="17" />
<hkern g1="J" 	g2="J" 	k="18" />
<hkern g1="J" 	g2="l" 	k="17" />
<hkern g1="J" 	g2="g" 	k="54" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="J" 	g2="s" 	k="45" />
<hkern g1="J" 	g2="AE" 	k="20" />
<hkern g1="J" 	g2="x" 	k="29" />
<hkern g1="J" 	g2="z" 	k="32" />
<hkern g1="J" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="18" />
<hkern g1="J" 	g2="m,n,p,r,ntilde" 	k="18" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="51" />
<hkern g1="J" 	g2="period,ellipsis" 	k="41" />
<hkern g1="K" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="54" />
<hkern g1="K" 	g2="t" 	k="53" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="95" />
<hkern g1="K" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="39" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="52" />
<hkern g1="K" 	g2="hyphen,emdash" 	k="74" />
<hkern g1="K" 	g2="d" 	k="59" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="93" />
<hkern g1="K" 	g2="v" 	k="96" />
<hkern g1="K" 	g2="w" 	k="76" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="80" />
<hkern g1="K" 	g2="s" 	k="10" />
<hkern g1="K" 	g2="m,n,p,r,ntilde" 	k="4" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="L" 	g2="V" 	k="170" />
<hkern g1="L" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="45" />
<hkern g1="L" 	g2="t" 	k="76" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="181" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="185" />
<hkern g1="L" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="50" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="60" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="198" />
<hkern g1="L" 	g2="T" 	k="181" />
<hkern g1="L" 	g2="hyphen,emdash" 	k="155" />
<hkern g1="L" 	g2="W" 	k="118" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="L" 	g2="d" 	k="54" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="84" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="194" />
<hkern g1="L" 	g2="v" 	k="181" />
<hkern g1="L" 	g2="w" 	k="135" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="97" />
<hkern g1="L" 	g2="b,h,k,thorn" 	k="3" />
<hkern g1="L" 	g2="l" 	k="3" />
<hkern g1="L" 	g2="s" 	k="15" />
<hkern g1="L" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="3" />
<hkern g1="L" 	g2="m,n,p,r,ntilde" 	k="3" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="114" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="50" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="38" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="15" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="y,yacute,ydieresis" 	k="8" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="S" 	k="3" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="11" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="89" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="8" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="79" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="d" 	k="11" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="26" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="v" 	k="11" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="53" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="b,h,k,thorn" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="69" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="l" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="g" 	k="22" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="27" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="s" 	k="19" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="51" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="43" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="25" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="79" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="period,ellipsis" 	k="65" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="31" />
<hkern g1="P" 	g2="V" 	k="10" />
<hkern g1="P" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="89" />
<hkern g1="P" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="11" />
<hkern g1="P" 	g2="hyphen,emdash" 	k="74" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="26" />
<hkern g1="P" 	g2="d" 	k="69" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="56" />
<hkern g1="P" 	g2="X" 	k="56" />
<hkern g1="P" 	g2="b,h,k,thorn" 	k="13" />
<hkern g1="P" 	g2="J" 	k="164" />
<hkern g1="P" 	g2="l" 	k="13" />
<hkern g1="P" 	g2="g" 	k="38" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="95" />
<hkern g1="P" 	g2="s" 	k="25" />
<hkern g1="P" 	g2="AE" 	k="159" />
<hkern g1="P" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="8" />
<hkern g1="P" 	g2="m,n,p,r,ntilde" 	k="18" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="184" />
<hkern g1="P" 	g2="period,ellipsis" 	k="215" />
<hkern g1="P" 	g2="Z" 	k="4" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="4" />
<hkern g1="R" 	g2="V" 	k="29" />
<hkern g1="R" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="46" />
<hkern g1="R" 	g2="t" 	k="24" />
<hkern g1="R" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="R" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="19" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="37" />
<hkern g1="R" 	g2="T" 	k="50" />
<hkern g1="R" 	g2="W" 	k="8" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="44" />
<hkern g1="R" 	g2="d" 	k="48" />
<hkern g1="R" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="R" 	g2="v" 	k="25" />
<hkern g1="R" 	g2="w" 	k="17" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="63" />
<hkern g1="R" 	g2="b,h,k,thorn" 	k="14" />
<hkern g1="R" 	g2="l" 	k="14" />
<hkern g1="R" 	g2="s" 	k="14" />
<hkern g1="R" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="15" />
<hkern g1="R" 	g2="m,n,p,r,ntilde" 	k="15" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="S" 	g2="V" 	k="23" />
<hkern g1="S" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="8" />
<hkern g1="S" 	g2="t" 	k="41" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="56" />
<hkern g1="S" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="42" />
<hkern g1="S" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="22" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="25" />
<hkern g1="S" 	g2="d" 	k="8" />
<hkern g1="S" 	g2="v" 	k="55" />
<hkern g1="S" 	g2="w" 	k="40" />
<hkern g1="S" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="9" />
<hkern g1="S" 	g2="b,h,k,thorn" 	k="19" />
<hkern g1="S" 	g2="l" 	k="19" />
<hkern g1="S" 	g2="g" 	k="35" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="S" 	g2="s" 	k="29" />
<hkern g1="S" 	g2="AE" 	k="35" />
<hkern g1="S" 	g2="x" 	k="40" />
<hkern g1="S" 	g2="z" 	k="29" />
<hkern g1="S" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="14" />
<hkern g1="S" 	g2="m,n,p,r,ntilde" 	k="24" />
<hkern g1="S" 	g2="quotesinglbase,quotedblbase" 	k="27" />
<hkern g1="T" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="229" />
<hkern g1="T" 	g2="t" 	k="98" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="163" />
<hkern g1="T" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="102" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="195" />
<hkern g1="T" 	g2="hyphen,emdash" 	k="119" />
<hkern g1="T" 	g2="d" 	k="226" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="T" 	g2="v" 	k="163" />
<hkern g1="T" 	g2="w" 	k="153" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="227" />
<hkern g1="T" 	g2="b,h,k,thorn" 	k="14" />
<hkern g1="T" 	g2="J" 	k="121" />
<hkern g1="T" 	g2="l" 	k="14" />
<hkern g1="T" 	g2="g" 	k="222" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="127" />
<hkern g1="T" 	g2="s" 	k="216" />
<hkern g1="T" 	g2="AE" 	k="176" />
<hkern g1="T" 	g2="x" 	k="156" />
<hkern g1="T" 	g2="z" 	k="183" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="203" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="155" />
<hkern g1="T" 	g2="period,ellipsis" 	k="153" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="147" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="137" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="45" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="t" 	k="19" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="24" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="32" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="d" 	k="42" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="v" 	k="17" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="w" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="b,h,k,thorn" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="63" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="l" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="57" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="19" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="52" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="46" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="36" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="35" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="m,n,p,r,ntilde" 	k="26" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="73" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="period,ellipsis" 	k="59" />
<hkern g1="V" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="132" />
<hkern g1="V" 	g2="t" 	k="35" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="40" />
<hkern g1="V" 	g2="S" 	k="24" />
<hkern g1="V" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="38" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="91" />
<hkern g1="V" 	g2="hyphen,emdash" 	k="62" />
<hkern g1="V" 	g2="d" 	k="125" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="26" />
<hkern g1="V" 	g2="v" 	k="39" />
<hkern g1="V" 	g2="w" 	k="42" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="129" />
<hkern g1="V" 	g2="J" 	k="132" />
<hkern g1="V" 	g2="g" 	k="132" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="91" />
<hkern g1="V" 	g2="s" 	k="118" />
<hkern g1="V" 	g2="AE" 	k="146" />
<hkern g1="V" 	g2="x" 	k="70" />
<hkern g1="V" 	g2="z" 	k="73" />
<hkern g1="V" 	g2="m,n,p,r,ntilde" 	k="96" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="166" />
<hkern g1="V" 	g2="period,ellipsis" 	k="159" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="81" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="54" />
<hkern g1="W" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="96" />
<hkern g1="W" 	g2="t" 	k="24" />
<hkern g1="W" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="W" 	g2="S" 	k="14" />
<hkern g1="W" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="26" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="65" />
<hkern g1="W" 	g2="hyphen,emdash" 	k="38" />
<hkern g1="W" 	g2="d" 	k="90" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="7" />
<hkern g1="W" 	g2="v" 	k="22" />
<hkern g1="W" 	g2="w" 	k="23" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="95" />
<hkern g1="W" 	g2="J" 	k="95" />
<hkern g1="W" 	g2="g" 	k="93" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="67" />
<hkern g1="W" 	g2="s" 	k="83" />
<hkern g1="W" 	g2="AE" 	k="124" />
<hkern g1="W" 	g2="x" 	k="44" />
<hkern g1="W" 	g2="z" 	k="47" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="70" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="128" />
<hkern g1="W" 	g2="period,ellipsis" 	k="121" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="54" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="11" />
<hkern g1="X" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="X" 	g2="t" 	k="62" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="92" />
<hkern g1="X" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="46" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="52" />
<hkern g1="X" 	g2="hyphen,emdash" 	k="53" />
<hkern g1="X" 	g2="d" 	k="46" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="55" />
<hkern g1="X" 	g2="v" 	k="92" />
<hkern g1="X" 	g2="w" 	k="80" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="65" />
<hkern g1="X" 	g2="b,h,k,thorn" 	k="8" />
<hkern g1="X" 	g2="l" 	k="8" />
<hkern g1="X" 	g2="m,n,p,r,ntilde" 	k="15" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="217" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="69" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="101" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="147" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,emdash" 	k="132" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="199" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="93" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="193" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="131" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="190" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="191" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="174" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="106" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="129" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="145" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="170" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="period,ellipsis" 	k="172" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="134" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="94" />
<hkern g1="Z" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="38" />
<hkern g1="Z" 	g2="t" 	k="41" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="67" />
<hkern g1="Z" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="39" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="50" />
<hkern g1="Z" 	g2="hyphen,emdash" 	k="36" />
<hkern g1="Z" 	g2="d" 	k="42" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="Z" 	g2="v" 	k="68" />
<hkern g1="Z" 	g2="w" 	k="57" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="55" />
<hkern g1="Z" 	g2="b,h,k,thorn" 	k="9" />
<hkern g1="Z" 	g2="l" 	k="9" />
<hkern g1="Z" 	g2="g" 	k="4" />
<hkern g1="Z" 	g2="s" 	k="14" />
<hkern g1="Z" 	g2="z" 	k="3" />
<hkern g1="Z" 	g2="m,n,p,r,ntilde" 	k="31" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="26" />
<hkern g1="b,p,thorn" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="85" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ydieresis" 	k="185" />
<hkern g1="b,p,thorn" 	g2="J" 	k="82" />
<hkern g1="b,p,thorn" 	g2="W" 	k="90" />
<hkern g1="b,p,thorn" 	g2="S" 	k="34" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="93" />
<hkern g1="b,p,thorn" 	g2="X" 	k="70" />
<hkern g1="b,p,thorn" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="30" />
<hkern g1="b,p,thorn" 	g2="Z" 	k="61" />
<hkern g1="b,p,thorn" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="b,p,thorn" 	g2="V" 	k="132" />
<hkern g1="b,p,thorn" 	g2="T" 	k="227" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="b,p,thorn" 	g2="g" 	k="3" />
<hkern g1="b,p,thorn" 	g2="z" 	k="22" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="46" />
<hkern g1="b,p,thorn" 	g2="x" 	k="40" />
<hkern g1="b,p,thorn" 	g2="v" 	k="20" />
<hkern g1="b,p,thorn" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="11" />
<hkern g1="b,p,thorn" 	g2="w" 	k="9" />
<hkern g1="b,p,thorn" 	g2="t" 	k="4" />
<hkern g1="b,p,thorn" 	g2="quotesinglbase,quotedblbase" 	k="27" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="c,ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="42" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="181" />
<hkern g1="c,ccedilla" 	g2="J" 	k="20" />
<hkern g1="c,ccedilla" 	g2="W" 	k="93" />
<hkern g1="c,ccedilla" 	g2="S" 	k="20" />
<hkern g1="c,ccedilla" 	g2="quoteleft,quotedblleft" 	k="54" />
<hkern g1="c,ccedilla" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="30" />
<hkern g1="c,ccedilla" 	g2="Z" 	k="8" />
<hkern g1="c,ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="56" />
<hkern g1="c,ccedilla" 	g2="V" 	k="137" />
<hkern g1="c,ccedilla" 	g2="T" 	k="216" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="c,ccedilla" 	g2="quotedbl,quotesingle" 	k="32" />
<hkern g1="c,ccedilla" 	g2="v" 	k="10" />
<hkern g1="c,ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="17" />
<hkern g1="c,ccedilla" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="12" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="9" />
<hkern g1="c,ccedilla" 	g2="d" 	k="12" />
<hkern g1="c,ccedilla" 	g2="hyphen,emdash" 	k="34" />
<hkern g1="d" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="d" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="d" 	g2="J" 	k="4" />
<hkern g1="d" 	g2="W" 	k="7" />
<hkern g1="d" 	g2="S" 	k="14" />
<hkern g1="d" 	g2="X" 	k="10" />
<hkern g1="d" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="15" />
<hkern g1="d" 	g2="Z" 	k="18" />
<hkern g1="d" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="d" 	g2="V" 	k="11" />
<hkern g1="d" 	g2="T" 	k="17" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="28" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="202" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="26" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="91" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="13" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="52" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="12" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Z" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="49" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="129" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="215" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="15" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="4" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="3" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="7" />
<hkern g1="f" 	g2="Y,Yacute,Ydieresis" 	k="-16" />
<hkern g1="f" 	g2="J" 	k="119" />
<hkern g1="f" 	g2="W" 	k="-13" />
<hkern g1="f" 	g2="S" 	k="8" />
<hkern g1="f" 	g2="X" 	k="-4" />
<hkern g1="f" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="7" />
<hkern g1="f" 	g2="V" 	k="-16" />
<hkern g1="f" 	g2="T" 	k="-4" />
<hkern g1="f" 	g2="g" 	k="8" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="54" />
<hkern g1="f" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="84" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="f" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="57" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="28" />
<hkern g1="f" 	g2="d" 	k="47" />
<hkern g1="f" 	g2="hyphen,emdash" 	k="46" />
<hkern g1="f" 	g2="period,ellipsis" 	k="57" />
<hkern g1="g" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="g" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="g" 	g2="J" 	k="7" />
<hkern g1="g" 	g2="W" 	k="22" />
<hkern g1="g" 	g2="S" 	k="15" />
<hkern g1="g" 	g2="quoteleft,quotedblleft" 	k="24" />
<hkern g1="g" 	g2="X" 	k="4" />
<hkern g1="g" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="16" />
<hkern g1="g" 	g2="Z" 	k="6" />
<hkern g1="g" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="15" />
<hkern g1="g" 	g2="V" 	k="50" />
<hkern g1="g" 	g2="T" 	k="190" />
<hkern g1="g" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="26" />
<hkern g1="g" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="34" />
<hkern g1="g" 	g2="d" 	k="27" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="24" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="Y,Yacute,Ydieresis" 	k="4" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="J" 	k="3" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="S" 	k="16" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="14" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="Z" 	k="14" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="T" 	k="5" />
<hkern g1="k" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="42" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="39" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="k" 	g2="W" 	k="26" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="42" />
<hkern g1="k" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="28" />
<hkern g1="k" 	g2="V" 	k="57" />
<hkern g1="k" 	g2="T" 	k="182" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="62" />
<hkern g1="k" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="53" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="52" />
<hkern g1="k" 	g2="d" 	k="54" />
<hkern g1="k" 	g2="hyphen,emdash" 	k="58" />
<hkern g1="h,m,n,ntilde" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="47" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="197" />
<hkern g1="h,m,n,ntilde" 	g2="W" 	k="76" />
<hkern g1="h,m,n,ntilde" 	g2="S" 	k="22" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="54" />
<hkern g1="h,m,n,ntilde" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="Z" 	k="22" />
<hkern g1="h,m,n,ntilde" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="h,m,n,ntilde" 	g2="V" 	k="118" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="236" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="24" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="37" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="22" />
<hkern g1="h,m,n,ntilde" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="13" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="17" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="67" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Y,Yacute,Ydieresis" 	k="206" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="J" 	k="73" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="W" 	k="96" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="S" 	k="30" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="74" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="X" 	k="63" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="32" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Z" 	k="59" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="43" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="V" 	k="136" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="T" 	k="228" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="g" 	k="3" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="43" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="28" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="13" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="15" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="86" />
<hkern g1="r" 	g2="J" 	k="157" />
<hkern g1="r" 	g2="W" 	k="8" />
<hkern g1="r" 	g2="S" 	k="14" />
<hkern g1="r" 	g2="X" 	k="96" />
<hkern g1="r" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="19" />
<hkern g1="r" 	g2="Z" 	k="60" />
<hkern g1="r" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="8" />
<hkern g1="r" 	g2="V" 	k="31" />
<hkern g1="r" 	g2="T" 	k="167" />
<hkern g1="r" 	g2="g" 	k="23" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="88" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="100" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="53" />
<hkern g1="r" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="74" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="42" />
<hkern g1="r" 	g2="d" 	k="54" />
<hkern g1="r" 	g2="hyphen,emdash" 	k="62" />
<hkern g1="r" 	g2="period,ellipsis" 	k="87" />
<hkern g1="r" 	g2="s" 	k="10" />
<hkern g1="s" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="19" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="172" />
<hkern g1="s" 	g2="J" 	k="17" />
<hkern g1="s" 	g2="W" 	k="86" />
<hkern g1="s" 	g2="S" 	k="8" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="44" />
<hkern g1="s" 	g2="X" 	k="19" />
<hkern g1="s" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="34" />
<hkern g1="s" 	g2="Z" 	k="19" />
<hkern g1="s" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="44" />
<hkern g1="s" 	g2="V" 	k="105" />
<hkern g1="s" 	g2="T" 	k="215" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="13" />
<hkern g1="s" 	g2="g" 	k="3" />
<hkern g1="s" 	g2="v" 	k="12" />
<hkern g1="s" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="11" />
<hkern g1="t" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="134" />
<hkern g1="t" 	g2="W" 	k="58" />
<hkern g1="t" 	g2="S" 	k="16" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="54" />
<hkern g1="t" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="30" />
<hkern g1="t" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="47" />
<hkern g1="t" 	g2="V" 	k="94" />
<hkern g1="t" 	g2="T" 	k="185" />
<hkern g1="t" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="24" />
<hkern g1="t" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="18" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="t" 	g2="d" 	k="16" />
<hkern g1="t" 	g2="hyphen,emdash" 	k="34" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="154" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="J" 	k="4" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="71" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="S" 	k="22" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="9" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="15" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Z" 	k="23" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="26" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="109" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="226" />
<hkern g1="v" 	g2="Y,Yacute,Ydieresis" 	k="106" />
<hkern g1="v" 	g2="J" 	k="146" />
<hkern g1="v" 	g2="W" 	k="16" />
<hkern g1="v" 	g2="S" 	k="14" />
<hkern g1="v" 	g2="X" 	k="87" />
<hkern g1="v" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="22" />
<hkern g1="v" 	g2="Z" 	k="56" />
<hkern g1="v" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="12" />
<hkern g1="v" 	g2="V" 	k="42" />
<hkern g1="v" 	g2="T" 	k="198" />
<hkern g1="v" 	g2="g" 	k="15" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="81" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="69" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="19" />
<hkern g1="v" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="32" />
<hkern g1="v" 	g2="d" 	k="25" />
<hkern g1="v" 	g2="period,ellipsis" 	k="76" />
<hkern g1="v" 	g2="s" 	k="13" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="w" 	g2="J" 	k="117" />
<hkern g1="w" 	g2="W" 	k="18" />
<hkern g1="w" 	g2="S" 	k="11" />
<hkern g1="w" 	g2="X" 	k="78" />
<hkern g1="w" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="16" />
<hkern g1="w" 	g2="Z" 	k="49" />
<hkern g1="w" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="w" 	g2="V" 	k="48" />
<hkern g1="w" 	g2="T" 	k="196" />
<hkern g1="w" 	g2="g" 	k="5" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="59" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="w" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="19" />
<hkern g1="w" 	g2="d" 	k="14" />
<hkern g1="w" 	g2="period,ellipsis" 	k="56" />
<hkern g1="w" 	g2="s" 	k="4" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="34" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="108" />
<hkern g1="x" 	g2="W" 	k="38" />
<hkern g1="x" 	g2="S" 	k="7" />
<hkern g1="x" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="14" />
<hkern g1="x" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="36" />
<hkern g1="x" 	g2="V" 	k="69" />
<hkern g1="x" 	g2="T" 	k="193" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="42" />
<hkern g1="x" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="38" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="x" 	g2="d" 	k="36" />
<hkern g1="x" 	g2="hyphen,emdash" 	k="39" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="105" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="148" />
<hkern g1="y,yacute,ydieresis" 	g2="W" 	k="14" />
<hkern g1="y,yacute,ydieresis" 	g2="S" 	k="14" />
<hkern g1="y,yacute,ydieresis" 	g2="X" 	k="87" />
<hkern g1="y,yacute,ydieresis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="23" />
<hkern g1="y,yacute,ydieresis" 	g2="Z" 	k="54" />
<hkern g1="y,yacute,ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="11" />
<hkern g1="y,yacute,ydieresis" 	g2="V" 	k="39" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="195" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="15" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="85" />
<hkern g1="y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="71" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="33" />
<hkern g1="y,yacute,ydieresis" 	g2="d" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="period,ellipsis" 	k="79" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="13" />
<hkern g1="z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="15" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="127" />
<hkern g1="z" 	g2="W" 	k="37" />
<hkern g1="z" 	g2="S" 	k="11" />
<hkern g1="z" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="28" />
<hkern g1="z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="35" />
<hkern g1="z" 	g2="V" 	k="67" />
<hkern g1="z" 	g2="T" 	k="201" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="22" />
<hkern g1="z" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="26" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="z" 	g2="d" 	k="22" />
<hkern g1="z" 	g2="hyphen,emdash" 	k="28" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="66" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="58" />
<hkern g1="quoteright,quotedblright" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="19" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="147" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="d" 	k="116" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="197" />
<hkern g1="quoteright,quotedblright" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="103" />
<hkern g1="quoteright,quotedblright" 	g2="period,ellipsis" 	k="204" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="28" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="153" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,emdash" 	k="159" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="97" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="143" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="19" />
<hkern g1="quoteright,quotedblright" 	g2="quotesinglbase,quotedblbase" 	k="197" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="7" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="9" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="87" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="125" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="83" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="165" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="57" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="188" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="197" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="151" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="75" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="75" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="165" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="32" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="12" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="103" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quoteright,quotedblright" 	k="90" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="139" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="9" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="85" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="45" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="62" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="9" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="142" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="75" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="136" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="148" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="36" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="94" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="41" />
<hkern g1="hyphen,emdash" 	g2="z" 	k="39" />
<hkern g1="hyphen,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="27" />
<hkern g1="hyphen,emdash" 	g2="AE" 	k="40" />
<hkern g1="hyphen,emdash" 	g2="J" 	k="118" />
<hkern g1="hyphen,emdash" 	g2="x" 	k="45" />
<hkern g1="hyphen,emdash" 	g2="W" 	k="39" />
<hkern g1="hyphen,emdash" 	g2="Y,Yacute,Ydieresis" 	k="136" />
<hkern g1="hyphen,emdash" 	g2="quotedbl,quotesingle" 	k="138" />
<hkern g1="hyphen,emdash" 	g2="quoteright,quotedblright" 	k="171" />
<hkern g1="hyphen,emdash" 	g2="T" 	k="120" />
<hkern g1="hyphen,emdash" 	g2="y,yacute,ydieresis" 	k="7" />
<hkern g1="hyphen,emdash" 	g2="V" 	k="68" />
<hkern g1="hyphen,emdash" 	g2="Z" 	k="36" />
<hkern g1="hyphen,emdash" 	g2="X" 	k="52" />
<hkern g1="hyphen,emdash" 	g2="S" 	k="7" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="34" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="9" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="119" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="46" />
<hkern g1="quotedbl,quotesingle" 	g2="d" 	k="59" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="176" />
<hkern g1="quotedbl,quotesingle" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="65" />
<hkern g1="quotedbl,quotesingle" 	g2="period,ellipsis" 	k="188" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="151" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,emdash" 	k="115" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="60" />
<hkern g1="quotedbl,quotesingle" 	g2="quotesinglbase,quotedblbase" 	k="188" />
<hkern g1="l,uniFB02,uniFB04" 	g2="V" 	k="11" />
<hkern g1="l,uniFB02,uniFB04" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="26" />
<hkern g1="l,uniFB02,uniFB04" 	g2="X" 	k="10" />
<hkern g1="l,uniFB02,uniFB04" 	g2="W" 	k="7" />
<hkern g1="l,uniFB02,uniFB04" 	g2="S" 	k="14" />
<hkern g1="l,uniFB02,uniFB04" 	g2="T" 	k="17" />
<hkern g1="l,uniFB02,uniFB04" 	g2="J" 	k="4" />
<hkern g1="l,uniFB02,uniFB04" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="14" />
<hkern g1="l,uniFB02,uniFB04" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="l,uniFB02,uniFB04" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="l,uniFB02,uniFB04" 	g2="Z" 	k="18" />
</font>
</defs></svg> 