<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="latoitalic" horiz-adv-x="1187" >
<font-face units-per-em="2048" ascent="1649" descent="-399" />
<missing-glyph horiz-adv-x="479" />
<glyph unicode="&#xfb01;" horiz-adv-x="1092" d="M85 940l8 77h151l7 57q11 95 49 174t100 135.5t148.5 87.5t195.5 31q18 0 38.5 -1.5t40.5 -4t38 -6t31 -8.5l-19 -92q-2 -10 -9.5 -14.5t-20 -5.5t-28.5 0t-38 1q-85 0 -147.5 -18.5t-105.5 -55t-68 -93t-34 -133.5l-7 -54h580l-124 -1017h-179l109 885h-395l-105 -852 l-48 -212q-11 -37 -33 -57t-61 -20h-72l140 1139l-100 12q-22 5 -33 15t-9 30z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1124" d="M85 940l9 77h150l7 56q10 86 44 163t89.5 134t133.5 90.5t177 33.5q74 0 140 -6t131 -7h123l-183 -1481h-177l165 1348q-46 4 -94 9t-93 5q-120 0 -194.5 -75.5t-90.5 -213.5l-6 -56h243l-15 -131h-237l-106 -853l-48 -212q-11 -37 -33 -57t-61 -20h-72l140 1139l-100 12 q-22 5 -33 15t-9 30z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1686" d="M85 945l9 72h151l10 85q11 89 44 158t82.5 116.5t114 72.5t138.5 25q62 0 116 -21l-18 -95q-3 -10 -10 -14.5t-18.5 -5.5t-26 -0.5t-32.5 0.5q-43 0 -79.5 -11t-64.5 -37.5t-47 -71t-27 -111.5l-12 -90h424l7 57q11 95 49 174t100 135.5t148.5 87.5t195.5 31 q17 0 38 -1.5t41 -4t38 -6t30 -8.5l-18 -92q-2 -10 -9.5 -14.5t-20 -5.5t-28.5 0t-38 1q-169 0 -253 -73.5t-102 -226.5l-7 -54h580l-15 -128h-1l-109 -889h-176l109 889h-398l-104 -856l-48 -212q-11 -37 -33 -57t-61 -20h-72l140 1145h-415l-105 -855l-47 -212 q-11 -37 -33 -57t-61 -20h-72l139 1142l-101 13q-20 3 -32 14t-10 31z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1717" d="M85 943l9 73h147l11 86q11 89 44 158t82.5 116.5t114 72.5t138.5 25q63 0 115 -21l-17 -95q-3 -10 -10 -14.5t-18.5 -5.5t-26.5 -0.5t-32 0.5q-43 0 -79.5 -11t-64.5 -37.5t-47 -71t-27 -111.5l-12 -91h425l7 57q10 86 44 163t90 134t134 90.5t177 33.5q79 0 150.5 -6.5 t141.5 -6.5h100l-181 -1481h-177l164 1348q-46 4 -94 9t-93 5q-121 0 -195 -75.5t-89 -213.5l-6 -57h273l-16 -129h-268l-105 -854l-47 -212q-11 -37 -33 -57t-61 -20h-72l139 1143h-418l-102 -853l-48 -212q-11 -37 -33 -57t-61 -20h-72l139 1141l-98 12q-20 4 -32 14.5 t-10 30.5z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="479" />
<glyph unicode="&#x09;" horiz-adv-x="479" />
<glyph unicode="&#xa0;" horiz-adv-x="479" />
<glyph unicode="!" horiz-adv-x="517" d="M77 113q0 27 9.5 50t26.5 40.5t40 28t51 10.5q27 0 49.5 -10.5t40 -28t28 -41t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-40.5 -27t-49 -9.5q-28 0 -51 9.5t-40 27t-26.5 40.5t-9.5 51zM179 511q1 53 2.5 100t4 91t6.5 88t10 90l71 587h167l-71 -587q-6 -46 -13.5 -90 t-16 -88t-19 -91t-22.5 -100h-119z" />
<glyph unicode="&#x22;" horiz-adv-x="711" d="M184 1011l3 158l38 296h153l-37 -296l-35 -158q-8 -32 -23.5 -49.5t-45.5 -17.5q-27 0 -39 17.5t-14 49.5zM497 1011l3 158l38 296h153l-37 -296l-35 -158q-8 -32 -23.5 -49.5t-45.5 -17.5q-27 0 -39 17.5t-14 49.5z" />
<glyph unicode="#" d="M48 451q-11 17 0 57l15 56h210l110 339h-239l21 73q8 29 28 42t59 13h165l121 372q10 30 33 46t53 16h79l-141 -434h256l141 434h79q28 0 43 -17.5t4 -47.5l-120 -369h219l-23 -73q-8 -29 -27.5 -42t-57.5 -13h-145l-110 -339h187q28 0 39 -16.5t1 -57.5l-17 -56h-245 l-139 -434h-81q-27 0 -40 22t-2 55l117 357h-257l-118 -366q-11 -37 -35.5 -52.5t-54.5 -15.5h-79l142 434h-152q-28 0 -39 17zM419 564h257l109 339h-257z" />
<glyph unicode="$" d="M46 175l63 80q9 11 22 18.5t28 7.5q18 0 40.5 -19.5t55.5 -45t80.5 -49.5t117.5 -32l104 553q-63 19 -123.5 44.5t-109 64t-77.5 95t-29 140.5q0 85 34 164.5t97.5 141t154.5 100t205 43.5l27 147q3 19 18.5 34t37.5 15h65l-38 -201q104 -12 177.5 -54.5t125.5 -101.5 l-49 -66q-23 -30 -49 -29q-14 0 -33 13t-47 30.5t-65.5 34.5t-87.5 24l-96 -503q66 -22 130.5 -46.5t115 -62.5t82.5 -93.5t32 -136.5q0 -97 -36 -184.5t-103.5 -155.5t-164 -110.5t-215.5 -49.5l-35 -183q-2 -18 -18.5 -32.5t-37.5 -14.5h-65l44 232q-122 11 -217.5 61 t-160.5 127zM388 1072q0 -43 16 -75t43 -57.5t64 -44t79 -35.5l89 469q-74 -6 -128.5 -29t-91 -57.5t-54 -78.5t-17.5 -92zM564 134q76 7 135 33t100 66.5t62 94t21 114.5q0 45 -17.5 78.5t-47.5 58t-69.5 42.5t-84.5 33z" />
<glyph unicode="%" horiz-adv-x="1523" d="M96 0l1166 1437q11 11 25.5 19.5t37.5 8.5h128l-1168 -1436q-10 -12 -24.5 -20.5t-35.5 -8.5h-129zM142 1054q0 101 28 180.5t75.5 134t111.5 83.5t136 29q57 0 105 -21t82.5 -61t53.5 -97.5t19 -130.5q0 -101 -30 -180t-79 -133t-112.5 -82t-130.5 -28q-57 0 -104.5 21 t-82 60t-53.5 96t-19 129zM279 1054q0 -50 10 -85.5t28.5 -58.5t43.5 -34t55 -11q40 0 76 17t64 53.5t43.5 94t15.5 140.5q0 51 -10.5 88t-29 60.5t-43.5 35t-54 11.5q-41 0 -77 -17.5t-63.5 -55.5t-43 -96.5t-15.5 -141.5zM799 288q0 101 27.5 180.5t75 134.5t111 84 t135.5 29q57 0 105.5 -21t82.5 -61t53.5 -97t19.5 -130q0 -101 -30 -181t-79.5 -133.5t-112.5 -81.5t-129 -28q-57 0 -105 21t-82 60t-53 95.5t-19 128.5zM935 288q0 -50 10 -86t28.5 -59t43.5 -34t55 -11q40 0 76.5 17t63.5 54t43 95.5t16 141.5q0 51 -10.5 87t-29 60 t-43.5 34.5t-54 10.5q-41 0 -77.5 -17.5t-63 -55.5t-42.5 -96t-16 -141z" />
<glyph unicode="&#x26;" horiz-adv-x="1345" d="M56 343q0 81 27.5 153t75 132t111.5 107t138 79q-40 68 -59 133.5t-19 129.5q0 84 29 158.5t83 128.5t129.5 86t167.5 32q75 0 135 -24t101 -66t62 -99t20 -123l-104 -19q-24 -5 -35.5 2t-17.5 30q-5 29 -17.5 57.5t-34 51.5t-52 38t-72.5 15q-52 0 -94 -19.5t-72.5 -54 t-46.5 -81t-16 -99.5q0 -116 99 -246l330 -418q42 68 70 140t41 144q4 18 13.5 29.5t28.5 11.5h122q-15 -118 -62 -231.5t-120 -212.5l244 -308h-152q-22 0 -37.5 2.5t-28 8.5t-23 16.5t-23.5 24.5l-97 125q-97 -89 -214.5 -141t-250.5 -52q-84 0 -153.5 25t-119.5 71.5 t-78 113t-28 149.5zM239 366q0 -57 17.5 -102t49 -75.5t74.5 -46.5t93 -16q97 0 183.5 44t157.5 116l-340 435q-118 -63 -176.5 -155t-58.5 -200z" />
<glyph unicode="'" horiz-adv-x="398" d="M184 1011l3 158l38 296h153l-37 -296l-35 -158q-8 -32 -23.5 -49.5t-45.5 -17.5q-27 0 -39 17.5t-14 49.5z" />
<glyph unicode="(" horiz-adv-x="529" d="M87 435q0 143 16.5 279t60 274t119 285t195.5 313l73 -48q13 -10 19.5 -26.5t-13.5 -46.5q-76 -117 -134 -233t-98 -239t-61 -256t-21 -285q0 -76 7 -157.5t21 -163.5t35.5 -161t48.5 -147q11 -31 1.5 -47.5t-25.5 -26.5l-83 -47q-43 88 -73.5 181.5t-50 188t-28.5 186.5 t-9 177z" />
<glyph unicode=")" horiz-adv-x="528" d="M-36 -224.5q-2 8.5 1 20t13 27.5q75 117 133.5 235.5t99 244t61.5 258.5t21 276q0 75 -7 156.5t-22 163.5t-36 161t-49 147q-11 30 -2 46.5t26 26.5l85 48q42 -89 72 -182t49 -187.5t28 -186.5t9 -178q0 -143 -16 -279t-59 -274t-119 -284.5t-196 -312.5l-72 47 q-7 5 -12.5 11.5t-7.5 15z" />
<glyph unicode="*" horiz-adv-x="808" d="M172 1085l172 104q19 11 37 16.5t39 7.5q-38 3 -70 25l-148 104l52 75l148 -103q15 -10 27.5 -22t21.5 -27q-10 35 -7 72l26 204h87l-26 -203q-2 -20 -7 -37t-15 -34q12 14 27.5 25.5t32.5 21.5l173 102l33 -75l-173 -103q-18 -10 -35.5 -16.5t-35.5 -8.5 q17 -2 33.5 -7.5t31.5 -16.5l148 -105l-51 -73l-148 102q-15 10 -27.5 20.5t-20.5 24.5q10 -30 5 -68l-26 -204h-87l26 203q5 40 20 68q-11 -13 -25.5 -23.5t-31.5 -20.5l-173 -102z" />
<glyph unicode="+" d="M104 621l17 133h417l54 439h143l-53 -439h419l-17 -133h-418l-55 -442h-144l54 442h-417z" />
<glyph unicode="," horiz-adv-x="459" d="M44 126q0 48 33.5 82t88.5 34q31 0 54.5 -11.5t39 -31.5t24 -46t8.5 -57q0 -46 -13 -95.5t-38 -98t-61 -95t-82 -85.5l-31 30q-13 12 -14 29q0 12 16.5 31.5t38 46.5t42 62.5t27.5 79.5q-4 -1 -13 -1q-53 0 -86.5 35.5t-33.5 90.5z" />
<glyph unicode="-" horiz-adv-x="718" d="M115 539l18 149h468l-18 -149h-468z" />
<glyph unicode="&#xad;" horiz-adv-x="718" d="M115 539l18 149h468l-18 -149h-468z" />
<glyph unicode="." horiz-adv-x="479" d="M53 113q0 27 10 50t26.5 40.5t40.5 28t50 10.5q27 0 50.5 -10.5t41 -28t27.5 -41t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-41 -27t-50.5 -9.5t-50.5 9.5t-40 27t-26.5 40.5t-10 51z" />
<glyph unicode="/" horiz-adv-x="856" d="M-22 -93l743 1514q15 30 40.5 47.5t58.5 17.5h75l-738 -1512q-16 -33 -46 -50t-58 -17h-75z" />
<glyph unicode="0" d="M103 572q0 230 48 401.5t129 284.5t189.5 169t228.5 56q89 0 165 -37t131 -110t85.5 -184t30.5 -258q0 -230 -48 -401t-129.5 -284.5t-190 -168.5t-228.5 -55q-90 0 -165.5 36.5t-130 110t-85 183.5t-30.5 257zM285 562q0 -116 19.5 -197.5t52.5 -133t78 -75t96 -23.5 q75 0 147 40.5t127.5 132t89.5 238.5t34 359q0 116 -19.5 198t-53.5 133.5t-79.5 75.5t-96.5 24q-75 0 -146 -41.5t-126.5 -133t-89 -238.5t-33.5 -359z" />
<glyph unicode="1" d="M209 0l17 134h288l125 1015q2 20 5.5 41.5t7.5 44.5l-262 -225q-14 -10 -27.5 -13.5t-24 -1t-19 8.5t-12.5 12l-45 77l449 377h144l-164 -1336h263l-17 -134h-728z" />
<glyph unicode="2" d="M60 0l8 60q2 18 12 39t30 37l487 474q69 68 124 128t93 120t58.5 121.5t20.5 134.5q0 54 -16 94.5t-43.5 68t-65.5 41t-83 13.5q-50 0 -94 -16.5t-80.5 -46t-64 -70.5t-43.5 -90q-15 -35 -36.5 -46.5t-57.5 -5.5l-90 15q26 100 73.5 177t112 129.5t143 79t166.5 26.5 q80 0 146 -24t114 -70t74.5 -111t26.5 -148q0 -94 -28 -171.5t-75.5 -147t-110.5 -134.5t-134 -133l-411 -403q43 11 86 18.5t82 7.5h440q29 0 43.5 -17t10.5 -44l-12 -106h-906z" />
<glyph unicode="3" d="M94 366l80 31q33 13 60 7t36 -32q10 -28 23.5 -68.5t40 -78.5t72 -64.5t117.5 -26.5q75 0 134 29.5t99.5 76.5t62 106.5t21.5 120.5q0 47 -15.5 87.5t-51.5 70t-96.5 46.5t-150.5 17l16 127q180 4 266 84.5t86 220.5q0 52 -15.5 92t-42.5 66.5t-65 39.5t-83 13 q-50 0 -94.5 -16.5t-81 -46t-63.5 -70.5t-42 -90q-17 -35 -38 -46.5t-57 -5.5l-90 15q26 100 73.5 177t112 129.5t143 79t166.5 26.5q80 0 145 -23.5t111 -66.5t71 -103.5t25 -135.5q0 -82 -20 -145t-57 -110t-89 -79t-117 -50q115 -35 172.5 -112t57.5 -189t-42 -201.5 t-112 -152.5t-162.5 -97t-192.5 -34q-106 0 -179 29t-120 80.5t-73.5 121.5t-40.5 151z" />
<glyph unicode="4" d="M55 518l772 949h165l-115 -940h200l-12 -101q-2 -16 -13.5 -27t-29.5 -11h-162l-48 -388h-154l48 388h-596q-20 0 -32.5 11.5t-15.5 28.5zM243 527h480l76 617q2 24 7 50t11 54z" />
<glyph unicode="5" d="M89 136l63 76q22 25 50 25q19 0 41 -16t53.5 -35t75 -35t105.5 -16q79 0 143.5 30t110 83t70 126t24.5 161q0 57 -15.5 104t-48 80.5t-81 52t-113.5 18.5q-50 0 -105 -9.5t-116 -28.5l-106 31l193 683h644l-10 -76q-4 -37 -30.5 -61.5t-83.5 -24.5h-415l-111 -392 q54 13 102.5 19t92.5 6q95 0 167 -29t119.5 -81t72 -121.5t24.5 -150.5q0 -130 -45 -235.5t-121 -180t-177 -115t-213 -40.5q-60 0 -113.5 12.5t-99 33.5t-82.5 48.5t-65 57.5z" />
<glyph unicode="6" horiz-adv-x="1092" d="M123 409q0 65 13.5 126t41 123.5t69.5 127.5t99 136l403 501q15 18 41.5 30.5t57.5 12.5h157l-469 -551q-19 -24 -37 -45t-35 -42q50 33 108.5 51t122.5 18q74 0 138 -25.5t111 -73.5t74.5 -118t27.5 -159q0 -114 -40.5 -212t-111.5 -170t-167.5 -113.5t-209.5 -41.5 q-92 0 -165 30t-124 86t-78 134t-27 175zM291 400q0 -58 16 -107.5t46 -86t75 -56.5t103 -20q73 0 134.5 27.5t106 76.5t69.5 115.5t25 145.5q0 61 -17.5 110.5t-49 84t-77 53.5t-101.5 19q-73 0 -133.5 -29.5t-104 -78.5t-68 -114.5t-24.5 -139.5z" />
<glyph unicode="7" d="M187 0l711 1218q32 50 68 86h-699q-17 0 -28 13t-9 31l15 118h920l-9 -78q-5 -33 -15.5 -55t-18.5 -38l-705 -1233q-15 -27 -40 -44.5t-64 -17.5h-126z" />
<glyph unicode="8" d="M105 349q0 165 80.5 270.5t233.5 147.5q-90 39 -135.5 112t-45.5 173q0 90 34.5 168.5t96 137t146 92t184.5 33.5q85 0 153 -26.5t115.5 -73t73 -108.5t25.5 -133q0 -136 -67 -234t-196 -144q110 -36 167.5 -114t57.5 -196q0 -109 -39 -196t-107 -148t-162 -93.5 t-205 -32.5q-93 0 -169 26t-129 73.5t-82.5 115t-29.5 150.5zM288 362q0 -52 16.5 -96t47 -75t76 -49t103.5 -18q73 0 131.5 24t99.5 67.5t63 104.5t22 135q0 63 -20 107.5t-54 73.5t-78.5 41.5t-93.5 12.5q-57 0 -113 -17t-100.5 -55t-72 -101t-27.5 -155zM410 1051 q0 -43 11 -82.5t36 -70t65 -49t96 -18.5q69 0 121 24.5t86 65.5t51 95.5t17 115.5q0 44 -13.5 83t-39.5 68.5t-65.5 46.5t-91.5 17q-67 0 -118 -24t-85.5 -63.5t-52 -93.5t-17.5 -115z" />
<glyph unicode="9" d="M187 966q0 109 39.5 203t108 163.5t162 110t200.5 40.5q89 0 160 -30.5t119.5 -84t74.5 -128t26 -163.5q0 -76 -14.5 -140.5t-42.5 -125t-68.5 -121.5t-91.5 -129l-390 -520q-14 -17 -40 -29t-56 -12h-162l484 587q24 29 44.5 55t39.5 51q-57 -43 -123.5 -66t-138.5 -23 q-69 0 -128.5 24.5t-104.5 71t-71.5 114t-26.5 152.5zM367 986q0 -116 59.5 -181.5t168.5 -65.5q72 0 130 28t99 74.5t63 108t22 127.5q0 58 -16 106t-47 82t-73.5 52.5t-94.5 18.5q-68 0 -125 -25.5t-98.5 -71.5t-64.5 -110.5t-23 -142.5z" />
<glyph unicode=":" horiz-adv-x="508" d="M68 113q0 27 10 50t26.5 40.5t40.5 28t50 10.5q27 0 50.5 -10.5t41 -28t27.5 -41t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-41 -27t-50.5 -9.5t-50.5 9.5t-40 27t-26.5 40.5t-10 51zM154 881q0 27 10 50t26.5 40.5t40.5 28t50 10.5q27 0 50.5 -10.5t41 -28t27.5 -41 t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-41 -27t-50.5 -9.5t-50.5 9.5t-40 27t-26.5 40.5t-10 51z" />
<glyph unicode=";" horiz-adv-x="531" d="M83 126q0 48 33.5 82t88.5 34q31 0 54.5 -11.5t39 -31.5t24 -46t8.5 -57q0 -46 -13 -95.5t-38 -98t-61 -95t-82 -85.5l-31 30q-13 12 -14 29q0 12 16.5 31.5t38 46.5t42 62.5t27.5 79.5q-4 -1 -13 -1q-53 0 -86.5 35.5t-33.5 90.5zM165 881q0 27 10 50t26.5 40.5t40.5 28 t50 10.5q27 0 50.5 -10.5t41 -28t27.5 -41t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-41 -27t-50.5 -9.5t-50.5 9.5t-40 27t-26.5 40.5t-10 51z" />
<glyph unicode="&#x3c;" d="M171 651l6 57h1l2 19l842 411l-15 -130q-2 -17 -12.5 -30t-30.5 -24l-490 -233q-22 -10 -46 -18.5t-50 -13.5q26 -6 47.5 -13.5t41.5 -19.5l431 -232q18 -10 25.5 -23.5t5.5 -29.5l-18 -131z" />
<glyph unicode="=" d="M131 453l16 134h878l-16 -134h-878zM174 794l16 134h877l-15 -134h-878z" />
<glyph unicode="&#x3e;" d="M180 240l17 131q2 16 11.5 29.5t31.5 23.5l488 232q22 11 45.5 19t49.5 14q-51 11 -88 32l-431 233q-18 10 -25 23t-5 31l16 130l742 -411l-7 -57l-2 -19z" />
<glyph unicode="?" horiz-adv-x="857" d="M162 1334q34 30 72 56.5t82.5 47t96 32.5t108.5 12q72 0 130 -21.5t98.5 -59.5t62 -89.5t21.5 -112.5q0 -86 -24.5 -146.5t-61 -105t-80.5 -76.5t-84.5 -62.5t-72 -63t-42.5 -76.5l-38 -158h-122l11 170q2 49 29.5 86.5t66 70.5t83 65t82.5 69.5t63.5 84t25.5 108.5 q0 80 -49 126t-132 46q-57 0 -99 -15t-72 -33.5t-50 -34t-34 -15.5q-13 0 -20 5t-14 18zM218 113q0 27 10 50t26.5 40.5t40 28t50.5 10.5t50.5 -10.5t41 -28t27.5 -41t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-41 -27t-50.5 -9.5t-50.5 9.5t-40 27t-26.5 40.5t-10 51z" />
<glyph unicode="@" horiz-adv-x="1573" d="M90 461q0 130 31 249t85.5 222t131.5 187.5t169 144.5t196.5 93t216.5 33q120 0 225.5 -40.5t185 -117.5t125.5 -187.5t46 -251.5q0 -130 -35 -240.5t-95.5 -190.5t-140.5 -126t-169 -46q-72 0 -114 38.5t-46 116.5q-65 -81 -133.5 -116.5t-141.5 -35.5q-49 0 -85.5 17.5 t-60.5 48.5t-35.5 73t-11.5 91q0 61 16.5 125.5t48.5 124.5t79 113.5t107 93.5t134 63t160 23q65 0 112 -10.5t88 -30.5l-135 -369q-28 -79 -33 -128.5t5 -77t32.5 -37.5t51.5 -10q53 0 102.5 36t88 101t62 155t23.5 198q0 119 -34.5 209t-97 150t-149 91t-190.5 31 q-138 0 -262.5 -60t-218 -167.5t-149 -255t-55.5 -322.5q0 -144 41 -256t114.5 -187t174.5 -114t222 -39q144 0 255 32.5t186 80.5q16 9 29 10.5t22 -2t14 -12t8 -17.5l13 -52q-110 -74 -243 -116t-297 -42q-150 0 -272.5 49t-211 140.5t-137 222t-48.5 294.5zM573 442 q0 -58 26 -95t78 -37q28 0 58.5 10.5t60 36.5t58.5 70.5t52 113.5l111 303q-36 9 -78 9q-75 0 -141.5 -36.5t-116 -94.5t-79 -131.5t-29.5 -148.5z" />
<glyph unicode="A" horiz-adv-x="1292" d="M-67 0l719 1467h195l359 -1467h-147q-26 0 -40 13t-21 33l-79 358h-597l-168 -358q-9 -18 -28.5 -32t-42.5 -14h-150zM387 544h501l-134 607q-5 28 -12.5 64t-12.5 78q-15 -43 -30.5 -78.5t-27.5 -64.5z" />
<glyph unicode="B" horiz-adv-x="1232" d="M100 0l181 1467h435q114 0 196 -23t135.5 -65.5t79 -103.5t25.5 -135q0 -65 -19 -125t-56.5 -110t-94 -89t-132.5 -62q132 -30 198 -103.5t66 -189.5q0 -101 -36.5 -186t-105 -146t-167 -95t-222.5 -34h-483zM310 152h288q82 0 143.5 21.5t102 61t61 96t20.5 127.5 q0 97 -65 155t-198 58h-288zM391 806h237q83 0 145 22t103.5 61.5t61.5 94.5t20 122q0 105 -63.5 158t-196.5 53h-244z" />
<glyph unicode="C" horiz-adv-x="1278" d="M100 621q0 191 59.5 349.5t163.5 273t244.5 177.5t301.5 63q78 0 141.5 -13.5t116 -39t95 -61.5t78.5 -80l-65 -80q-8 -10 -18.5 -16.5t-25.5 -6.5q-18 0 -38.5 20.5t-56 44.5t-91.5 44.5t-145 20.5q-120 0 -222.5 -47.5t-178.5 -136.5t-119.5 -214.5t-43.5 -282.5 q0 -114 29.5 -204t82.5 -153t125.5 -97t159.5 -34q68 0 120 11.5t92 28t68 36.5t49 36t35.5 27.5t25.5 11.5q9 0 15.5 -3.5t10.5 -8.5l67 -83q-96 -104 -222 -162t-292 -58q-132 0 -236 47t-176.5 131.5t-111 201t-38.5 257.5z" />
<glyph unicode="D" horiz-adv-x="1448" d="M100 0l181 1467h506q135 0 244 -46t185 -129.5t117 -199.5t41 -255q0 -187 -58 -341.5t-160 -264.5t-243 -170.5t-307 -60.5h-506zM311 155h315q122 0 223 46.5t174 134t113.5 213t40.5 284.5q0 111 -28 199.5t-80.5 150.5t-128.5 95.5t-171 33.5h-314z" />
<glyph unicode="E" horiz-adv-x="1101" d="M100 0l181 1467h835l-19 -156h-643l-61 -495h519l-19 -150h-518l-63 -510h644l-19 -156h-837z" />
<glyph unicode="F" horiz-adv-x="1085" d="M100 0l181 1467h835l-19 -156h-643l-64 -523h549l-20 -155h-548l-78 -633h-193z" />
<glyph unicode="G" horiz-adv-x="1393" d="M100 629q0 189 59 346.5t163 270.5t247 175.5t312 62.5q86 0 155 -14.5t126 -40t102.5 -60.5t83.5 -76l-62 -80q-15 -22 -36 -26.5t-44 9.5q-23 13 -50 34t-65.5 40t-93 33t-131.5 14q-126 0 -231 -48t-180 -137.5t-116.5 -215t-41.5 -280.5q0 -116 30 -208t85.5 -156.5 t133.5 -99t173 -34.5q53 0 99 6.5t87 17t78 26.5t74 36l41 332h-205q-18 0 -28.5 11t-8.5 26l12 109h421l-69 -560q-55 -40 -114 -69.5t-124.5 -49.5t-138 -29.5t-156.5 -9.5q-133 0 -241.5 47.5t-185.5 133t-118.5 204t-41.5 260.5z" />
<glyph unicode="H" horiz-adv-x="1454" d="M100 0l181 1467h192l-81 -658h716l81 658h193l-181 -1467h-192l82 670h-716l-82 -670h-193z" />
<glyph unicode="I" horiz-adv-x="545" d="M100 0l180 1467h192l-179 -1467h-193z" />
<glyph unicode="J" horiz-adv-x="820" d="M-37 12l13 112q3 15 12.5 26.5t29.5 11.5q11 0 24 -2.5t30 -6t39 -6.5t52 -3q53 0 100 17.5t84.5 57.5t63.5 104.5t38 158.5l120 985h192l-121 -981q-15 -122 -53.5 -215.5t-99.5 -157.5t-142.5 -96.5t-182.5 -32.5q-55 0 -103 7t-96 21z" />
<glyph unicode="K" horiz-adv-x="1265" d="M100 0l180 1467h191l-80 -646h68q36 0 59 10t44 33l508 557q22 28 45.5 37t53.5 9h163l-586 -634q-41 -47 -79 -65q25 -10 43.5 -27t33.5 -46l443 -695h-165q-38 0 -53.5 11t-26.5 30l-385 586q-15 26 -35.5 36.5t-64.5 10.5h-83l-83 -674h-191z" />
<glyph unicode="L" horiz-adv-x="973" d="M100 0l181 1467h191l-161 -1305h580l-20 -162h-771z" />
<glyph unicode="M" horiz-adv-x="1766" d="M100 0l181 1467h141q26 0 37 -6.5t25 -25.5l356 -923q7 -20 12 -40.5t10 -42.5q10 22 20.5 42.5t22.5 40.5l578 923q17 20 30.5 26t38.5 6h143l-182 -1467h-166l133 1084q3 20 7.5 45t10.5 52l-572 -923q-27 -44 -74 -44h-28q-23 0 -39.5 11.5t-23.5 32.5l-354 923 q-1 -25 -2.5 -49t-3.5 -45l-132 -1087h-169z" />
<glyph unicode="N" horiz-adv-x="1454" d="M100 0l181 1467h99q26 0 37 -6t23 -26l634 -1122q2 24 3.5 45.5t3.5 39.5l132 1069h169l-181 -1467h-95q-24 0 -38 8.5t-28 27.5l-632 1122q-2 -20 -3.5 -38t-3.5 -35l-132 -1085h-169z" />
<glyph unicode="O" horiz-adv-x="1526" d="M100 628q0 187 58.5 344t162 271t244.5 177.5t305 63.5q135 0 243.5 -49t184 -135t116 -204t40.5 -258q0 -187 -58 -344t-160.5 -270t-243.5 -176t-307 -63q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5zM297 633q0 -112 28 -202t81.5 -153.5t129 -98t171.5 -34.5 q122 0 223 48.5t174 138t113 217t40 285.5q0 112 -28.5 202t-81 153.5t-129 98.5t-171.5 35q-121 0 -222 -48.5t-174 -138.5t-113.5 -217.5t-40.5 -285.5z" />
<glyph unicode="P" horiz-adv-x="1150" d="M100 0l180 1467h404q117 0 203 -27.5t141.5 -78.5t83.5 -123t28 -160q0 -116 -38 -212.5t-110 -166t-178 -109t-243 -39.5h-213l-67 -551h-191zM377 702h213q87 0 153.5 27t111.5 74.5t68.5 113.5t23.5 144q0 59 -17.5 106t-52 80.5t-88 51.5t-123.5 18h-213z" />
<glyph unicode="Q" horiz-adv-x="1526" d="M100 628q0 187 58.5 344t162 271t244.5 177.5t305 63.5q135 0 243.5 -49t184 -135t116 -204t40.5 -258q0 -124 -26 -234.5t-73.5 -205.5t-116.5 -170.5t-153 -129.5l297 -402h-154q-36 0 -62 10t-47 35l-200 279q-108 -35 -227 -35h-7q-135 0 -243 48t-184.5 133.5 t-117 203t-40.5 258.5zM297 633q0 -112 28 -202t81.5 -153.5t129 -98t171.5 -34.5q122 0 223 48.5t174 138t113 217t40 285.5q0 112 -28.5 202t-81 153.5t-129 98.5t-171.5 35q-121 0 -222 -48.5t-174 -138.5t-113.5 -217.5t-40.5 -285.5z" />
<glyph unicode="R" horiz-adv-x="1195" d="M100 0l180 1467h386q117 0 202 -24.5t141 -70t83 -110.5t27 -145q0 -89 -26.5 -166t-76.5 -137t-121 -102.5t-160 -63.5q33 -23 55 -62l316 -586h-170q-52 0 -72 41l-276 528q-13 26 -30.5 36t-55.5 10h-135l-76 -615h-191zM384 755h187q87 0 154 24.5t112.5 69 t68.5 105.5t23 133q0 112 -70.5 171t-210.5 59h-194z" />
<glyph unicode="S" horiz-adv-x="1040" d="M13 175l68 91q8 11 20.5 18.5t26.5 7.5q19 0 42 -23t57.5 -51.5t86 -51.5t127.5 -23q71 0 128 22.5t96.5 62.5t61 96t21.5 123q0 56 -25 94t-65 63.5t-91 44.5t-104 38.5t-104.5 44t-91.5 62t-64.5 93t-24.5 136.5q0 90 33 173t94.5 146.5t150 101.5t198.5 38 q113 0 203 -43t149 -118l-57 -81q-11 -14 -21 -22t-25 -8q-17 0 -36.5 18t-50 39t-73.5 39t-107 18q-66 0 -117 -20.5t-86.5 -56t-54 -82.5t-18.5 -101q0 -53 25 -89.5t64.5 -62.5t91.5 -46t105 -41t105 -46.5t92 -63.5t64.5 -91.5t24.5 -128.5q0 -104 -36 -197t-103 -162.5 t-162 -110.5t-214 -41q-131 0 -234 51.5t-170 139.5z" />
<glyph unicode="T" horiz-adv-x="1118" d="M113 1307l18 160h1060l-20 -160h-432l-161 -1307h-191l160 1307h-434z" />
<glyph unicode="U" horiz-adv-x="1402" d="M155 573l108 894h192l-110 -893q-5 -40 -4 -78q0 -48 7 -92q14 -78 52 -134t99 -87.5t144 -31.5t151.5 31.5t121 87.5t85.5 133.5t45 169.5l109 894h192l-110 -894q-15 -126 -66.5 -234t-131.5 -187t-185 -124t-231 -45q-125 0 -220 45t-155.5 124t-84.5 187 q-14 65 -14 136q0 48 6 98z" />
<glyph unicode="V" horiz-adv-x="1289" d="M112 1467h153q26 0 41 -13t18 -33l245 -1048q8 -33 15.5 -72.5t12.5 -82.5q14 43 29.5 82.5t32.5 72.5l501 1048q8 17 27 31.5t44 14.5h153l-729 -1467h-173z" />
<glyph unicode="W" horiz-adv-x="1955" d="M128 1467h151q26 0 41 -12.5t17 -33.5l159 -1038q2 -27 4 -58t4 -64q11 34 22 64.5t22 57.5l438 1038q8 17 26.5 31.5t43.5 14.5h44q26 0 40.5 -12.5t19.5 -33.5l183 -1038q5 -27 8.5 -56.5t6.5 -62.5q9 33 18 62.5t19 56.5l412 1038q5 17 26 31.5t46 14.5h151 l-603 -1467h-173l-198 1129l-4.5 36t-4.5 40q-7 -20 -14.5 -39.5t-13.5 -36.5l-477 -1129h-172z" />
<glyph unicode="X" horiz-adv-x="1247" d="M-70 0l583 783l-344 684h173q20 0 29.5 -7t17.5 -21l262 -553q5 9 10.5 18t11.5 20l373 514q11 15 21.5 22t26.5 7h197l-513 -675l387 -792h-172q-20 0 -31.5 12t-17.5 25l-299 641q-7 -17 -18 -32l-443 -609q-13 -20 -29 -28.5t-34 -8.5h-191z" />
<glyph unicode="Y" horiz-adv-x="1191" d="M110 1467h169q26 0 40 -12t21 -33l243 -587q19 -58 30 -111q12 27 27 54t33 57l389 587q11 17 28 31t42 14h158l-602 -880l-73 -587h-191l73 585z" />
<glyph unicode="Z" horiz-adv-x="1137" d="M-27 0l9 60q2 14 8 25.5t14 24.5l898 1201h-731l19 156h991l-9 -61q-2 -14 -8.5 -25.5t-14.5 -24.5l-896 -1200h755l-21 -156h-1014z" />
<glyph unicode="[" horiz-adv-x="593" d="M70 -294l228 1863h350l-7 -69q-2 -20 -18 -33.5t-36 -13.5h-155l-202 -1630h157q20 0 32 -14t10 -34l-8 -69h-351z" />
<glyph unicode="\" horiz-adv-x="848" d="M169 1486h75q33 0 55 -17.5t30 -47.5l368 -1514h-72q-29 0 -54.5 17.5t-33.5 49.5z" />
<glyph unicode="]" horiz-adv-x="594" d="M-51 -294l8 69q2 20 17.5 34t36.5 14h156l201 1630h-156q-23 0 -34 13.5t-9 33.5l8 69h352l-229 -1863h-351z" />
<glyph unicode="^" d="M220 806l368 661h117l367 -661h-133q-17 0 -29.5 10t-20.5 25l-201 360q-13 24 -22.5 46t-17.5 43q-12 -42 -38 -89l-197 -360q-8 -14 -20.5 -24.5t-33.5 -10.5h-139z" />
<glyph unicode="_" horiz-adv-x="909" d="M-28 -291l15 119h743l-15 -119h-743z" />
<glyph unicode="`" horiz-adv-x="753" d="M291 1484h159q31 0 45 -11t24 -34l112 -248h-95q-19 0 -31 6.5t-24 22.5z" />
<glyph unicode="a" horiz-adv-x="1046" d="M51 351q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t124.5 -37l-124 -1004h-95q-36 0 -48.5 18.5t-12.5 44.5l19 210q-37 -66 -79 -119t-90 -90t-100.5 -57.5t-108.5 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156zM233 384 q0 -247 158 -247q50 0 98.5 31.5t92 87t80 132t65.5 167.5l44 355q-23 5 -44.5 7t-43.5 2q-61 0 -118.5 -20t-107.5 -56t-91 -85.5t-70.5 -109t-46 -126.5t-16.5 -138z" />
<glyph unicode="b" horiz-adv-x="1082" d="M60 0l183 1508h178l-89 -734q39 63 83.5 114.5t93.5 88t102.5 56.5t108.5 20q133 0 206.5 -96.5t73.5 -281.5q0 -84 -17 -168t-49.5 -160.5t-78 -142.5t-101.5 -114.5t-121.5 -76t-136.5 -27.5q-83 0 -149.5 34.5t-110.5 99.5l-15 -67q-8 -27 -20.5 -40t-43.5 -13h-97z M265 227q39 -54 94 -77t110 -23q53 0 100 22.5t86 61t69.5 90t52 110.5t32 122t10.5 124q0 122 -43.5 185.5t-123.5 63.5q-49 0 -99 -30.5t-97 -85.5t-87.5 -130t-70.5 -164z" />
<glyph unicode="c" horiz-adv-x="930" d="M59 410q0 126 39.5 241.5t110.5 204.5t168.5 142.5t213.5 53.5q103 0 173 -36.5t122 -106.5l-58 -68q-7 -8 -15.5 -13.5t-19.5 -5.5q-14 0 -28 13t-36.5 29t-56 28.5t-85.5 12.5q-71 0 -133.5 -38.5t-109.5 -105t-74 -157.5t-27 -195q0 -63 14 -115t43 -88.5t70.5 -56.5 t96.5 -20q69 0 115.5 20.5t78 44.5t53.5 44.5t44 20.5q19 0 35 -17l45 -56q-53 -58 -101.5 -97t-96.5 -62t-100 -32t-111 -9q-90 0 -159 30.5t-115.5 86.5t-71 134t-24.5 173z" />
<glyph unicode="d" horiz-adv-x="1083" d="M56 364q0 84 17.5 167.5t50 160.5t78 143t101.5 114t121 76t137 28q77 0 140 -30.5t107 -85.5l71 571h178l-186 -1508h-95q-36 0 -48 18.5t-12 44.5l19 219q-39 -68 -85 -122.5t-96.5 -93t-105.5 -59.5t-111 -21q-133 0 -207 96.5t-74 281.5zM238 382q0 -122 43.5 -186 t124.5 -64q49 0 99 31t96.5 85.5t87 129.5t70.5 164l34 269q-39 55 -94 77t-111 22q-81 0 -145.5 -48t-110 -124t-70 -170.5t-24.5 -185.5z" />
<glyph unicode="e" horiz-adv-x="979" d="M59 407q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77t18 -80q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q0 -11 -0.5 -22t-0.5 -22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42 33 t35 25t33 10q19 0 34 -17l46 -57q-52 -54 -102.5 -92.5t-102 -64t-108.5 -37t-123 -11.5q-89 0 -159.5 29.5t-119.5 84.5t-76 132.5t-27 174.5zM255 575q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38t-26 36.5t-46.5 27.5t-69.5 11q-65 0 -119 -27t-96 -73.5 t-72 -109.5t-46 -136z" />
<glyph unicode="f" horiz-adv-x="656" d="M85 943l9 74h148l12 100q11 89 44.5 158.5t84 117t115.5 72t141 24.5q62 0 116 -21l-18 -94q-4 -20 -27.5 -20h-59.5q-44 0 -81.5 -13t-66 -41.5t-48.5 -74.5t-28 -113l-11 -95h259l-18 -131h-252l-103 -852l-48 -212q-11 -37 -33 -57t-61 -20h-71l137 1138l-102 13 q-18 5 -29 16q-9 8 -9 25v6z" />
<glyph unicode="g" horiz-adv-x="991" d="M-57 -116q0 66 27.5 116t74.5 86.5t109.5 60t132.5 35.5q64 10 132 10h12q74 -1 144 -11q-9 19 -14.5 40.5t-5.5 45.5q0 32 9.5 66t32.5 77q-39 -27 -87 -42t-110 -15q-59 0 -110.5 19.5t-90.5 57.5t-61.5 95t-22.5 132q0 74 27 145t79 127t129 90t177 34 q55 0 105.5 -13.5t90.5 -40.5h281l-8 -60q-5 -37 -41 -49l-130 -23q19 -53 20 -117q0 -51 -12 -92.5t-29.5 -76t-38 -65.5t-37.5 -61t-29 -62.5t-12 -69.5q0 -28 10 -53t24.5 -49t32.5 -49t33 -52.5t24.5 -59.5t9.5 -71q-1 -73 -34.5 -138t-96 -115t-151.5 -79.5t-201 -29.5 q-89 0 -162.5 18.5t-125 52.5t-79.5 81.5t-28 104.5zM116 -93q0 -74 62 -113t175 -39q68 0 123.5 18t95 47.5t61.5 69.5t22 84q0 28 -5 51t-13 44q-106 17 -190 17q-22 0 -43 -1q-99 -6 -163 -30.5t-94.5 -63.5t-30.5 -84zM281 656q0 -92 44.5 -140t124.5 -48q55 0 97.5 24 t71.5 63.5t44.5 88.5t15.5 99q0 90 -45.5 136.5t-125.5 46.5q-55 0 -97 -22.5t-71.5 -60.5t-44 -86t-14.5 -101z" />
<glyph unicode="h" horiz-adv-x="1084" d="M60 0l183 1508h182l-85 -700q81 121 179.5 183t201.5 62q67 0 117 -26.5t81 -76.5t43 -123q6 -37 5 -80q0 -41 -5 -87l-78 -660h-183l78 660q5 41 5 76q0 66 -20 106q-30 60 -110 60q-46 0 -95 -25t-94.5 -71t-86 -111.5t-70.5 -146.5l-66 -548h-182z" />
<glyph unicode="i" horiz-adv-x="471" d="M71 0l125 1037h179l-125 -1037h-179zM194 1363q0 27 10 50.5t27.5 41.5t40 28.5t45.5 10.5q25 0 48 -10t40.5 -28t28 -42t10.5 -51t-10.5 -50t-28.5 -41t-41 -28t-48 -10q-24 0 -46 10t-39 28t-27 41t-10 50z" />
<glyph unicode="j" horiz-adv-x="468" d="M-181 -352l18 98q3 9 7.5 13.5t12.5 6t19.5 1t29.5 -0.5q73 0 107.5 37t45.5 120l137 1114h179l-137 -1114q-7 -62 -31 -116t-62.5 -94t-92.5 -63t-123 -23q-36 0 -61 5.5t-49 15.5zM189 1363q0 27 10.5 50.5t28 41.5t39.5 28.5t46 10.5q25 0 47.5 -10t40.5 -28t28 -42 t10 -51t-10.5 -50t-28.5 -41t-40.5 -28t-47.5 -10t-46.5 10t-38.5 28t-27.5 41t-10.5 50z" />
<glyph unicode="k" horiz-adv-x="970" d="M58 0l186 1508h179l-109 -887h31q22 0 35 5.5t29 22.5l344 351q14 17 30.5 27t39.5 10h162l-400 -406q-31 -36 -63 -54q16 -12 28 -28.5t22 -36.5l312 -512h-157q-23 0 -37.5 7.5t-23.5 28.5l-265 425q-12 22 -25.5 28.5t-41.5 6.5h-35l-60 -496h-181z" />
<glyph unicode="l" horiz-adv-x="460" d="M66 0l183 1508h179l-183 -1508h-179z" />
<glyph unicode="m" horiz-adv-x="1611" d="M60 0l125 1038h90q62 0 63 -61l-14 -184q77 128 168.5 194t191.5 66q105 0 155.5 -72.5t50.5 -206.5q77 142 173.5 210.5t204.5 68.5q133 -1 188 -103q39 -73 39 -190q0 -47 -6 -100l-78 -660h-180l79 660q5 47 5 85q0 61 -13 97q-22 60 -99 59q-44 0 -87.5 -22 t-84.5 -64.5t-76 -105.5t-63 -145v-1l-67 -563h-180l78 660q7 53 7 94q0 54 -12 88q-20 60 -95 59q-49 0 -95 -24.5t-86.5 -71t-75 -113.5t-63.5 -151l-64 -541h-179z" />
<glyph unicode="n" horiz-adv-x="1085" d="M60 0l125 1038h90q61 0 62 -61l-15 -202q83 137 187 207.5t215 70.5q66 0 115.5 -26.5t80.5 -76.5q30 -50 42 -123q6 -37 7 -80q0 -41 -6 -87l-78 -660h-182l78 660q5 40 5 73q0 69 -20 109q-30 59 -110 59q-49 0 -99.5 -26.5t-97.5 -75.5t-87.5 -118.5t-69.5 -154.5 l-59 -526h-183z" />
<glyph unicode="o" horiz-adv-x="1062" d="M60 417q0 131 41 246.5t113 202t168.5 137t208.5 50.5q85 0 155.5 -29.5t122 -84.5t80 -135t28.5 -181q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5zM244 418q0 -143 56 -216.5t164 -73.5q75 0 135.5 41 t104.5 109t67.5 157t23.5 187q0 143 -57 215t-164 72q-76 0 -136.5 -40.5t-103.5 -108t-66.5 -156.5t-23.5 -186z" />
<glyph unicode="p" horiz-adv-x="1082" d="M17 -351l169 1389h90q61 0 62 -61l-17 -221q39 68 85 122.5t97 93.5t106 60t112 21q133 0 206 -96.5t73 -281.5q0 -84 -17 -168t-49.5 -160.5t-78 -142.5t-101.5 -114.5t-121.5 -76t-136.5 -27.5q-77 0 -139.5 30t-106.5 85l-55 -452h-178zM265 227q39 -54 93.5 -77 t110.5 -23q53 0 100 22.5t86 61t70 90t52 110.5t32 122t11 124q0 122 -44 185.5t-124 63.5q-49 0 -99 -30t-96.5 -84.5t-87 -129t-70.5 -163.5z" />
<glyph unicode="q" horiz-adv-x="1047" d="M51 351q0 92 23 179.5t65.5 165.5t102.5 143t133.5 112.5t157 74t176.5 26.5q67 0 129 -11t124 -37l-167 -1355h-115q-18 0 -30.5 6.5t-19 17.5t-8.5 25t0 29l66 523q-36 -60 -77.5 -109t-87 -83.5t-95.5 -53t-104 -18.5q-61 0 -111.5 24t-86.5 70.5t-55.5 114.5 t-19.5 156zM233 384q0 -123 40.5 -185t118.5 -62q49 0 97.5 31t91.5 85.5t79.5 130t65.5 164.5l46 362q-23 5 -44.5 7t-43.5 2q-62 0 -119.5 -20t-107.5 -56t-91 -85.5t-70.5 -109t-46 -126.5t-16.5 -138z" />
<glyph unicode="r" horiz-adv-x="700" d="M60 0l125 1038h90q62 0 63 -61l-17 -228q41 87 88.5 151t99.5 100q51 36 107 44q13 2 27 2q42 0 86 -19l-33 -175q-49 19 -93 19q-77 0 -142 -58q-102 -92 -165 -333l-57 -480h-179z" />
<glyph unicode="s" horiz-adv-x="819" d="M-1 110l45 69q9 13 20 20.5t28 7.5q16 0 32.5 -14.5t41 -32.5t62 -32t95.5 -14q50 0 90.5 15t68.5 41t43 60t15 73q0 52 -32 83t-80 51.5t-103.5 38.5t-103.5 46t-79.5 73t-31.5 120q0 66 26 126.5t75 107.5t119 75.5t158 28.5q93 0 164.5 -31.5t122.5 -84.5l-46 -66 q-8 -12 -16 -17.5t-21 -5.5q-14 0 -31 11t-41 25t-59 26t-84 12q-46 0 -84 -13t-65 -35.5t-42 -52.5t-15 -64q0 -48 32 -77t80 -50t103.5 -40.5t103.5 -49t79.5 -75.5t31.5 -120q0 -72 -28 -137.5t-80.5 -115t-127 -79t-166.5 -29.5q-99 0 -175.5 35t-124.5 91z" />
<glyph unicode="t" horiz-adv-x="726" d="M77 925l8 72l160 22l78 320q5 15 15.5 25t27.5 10h95l-44 -357h272l-15 -132h-274l-62 -510q-5 -42 -8 -67.5t-4.5 -41t-2 -21.5t-0.5 -9q0 -52 24.5 -77.5t65.5 -25.5q28 0 47.5 7.5t35 17t25.5 17t18 7.5q9 0 13.5 -4t8.5 -13l39 -87q-50 -44 -114 -69t-129 -25 q-99 0 -156 54.5t-59 157.5v15.5t1.5 28t5.5 48t10 78.5l63 519h-116q-14 -1 -22 9q-7 8 -7 23v8z" />
<glyph unicode="u" horiz-adv-x="1084" d="M96 211q-6 36 -5 76q0 43 6 91l78 659h181l-77 -659q-5 -40 -4 -73q0 -69 19 -109q29 -60 109 -59q47 0 97.5 25.5t96.5 73t86.5 114.5t69.5 149l64 538h181l-125 -1037h-90q-67 0 -66 66l15 189q-82 -133 -185 -201t-211 -68q-66 0 -116 26t-81 76t-43 123z" />
<glyph unicode="v" horiz-adv-x="980" d="M72 1037h147q20 0 32.5 -11t16.5 -27l154 -667q6 -39 10 -77t7 -77q11 38 24.5 76t31.5 78l320 667q8 16 22.5 27t32.5 11h142l-520 -1037h-152z" />
<glyph unicode="w" horiz-adv-x="1480" d="M80 1037h136q20 0 32.5 -10t16.5 -28l95 -667q2 -36 4.5 -70t3.5 -66q11 33 24.5 66.5t27.5 69.5l283 672q5 15 19.5 25t32.5 10h75q19 0 30.5 -10t13.5 -25l117 -672q5 -36 9.5 -69.5t7.5 -66.5q9 33 19.5 66.5t23.5 69.5l263 667q5 16 20 27t32 11h132l-435 -1037h-138 q-25 0 -31 35l-126 703q-2 17 -3.5 33t-3.5 34q-11 -34 -25 -68l-299 -702q-14 -35 -42 -35h-133z" />
<glyph unicode="x" horiz-adv-x="950" d="M-65 0l419 558l-249 479h163q20 0 29.5 -5.5t15.5 -19.5l174 -358q11 23 26 44l235 311q15 28 41 28h2h163l-369 -480l275 -557h-162q-20 0 -32 10t-19 26l-194 416q-9 -22 -22 -40l-283 -377q-11 -14 -23 -24.5t-31 -10.5h-159z" />
<glyph unicode="y" horiz-adv-x="982" d="M74 1037h150q23 0 33 -10.5t15 -27.5l167 -653q5 -22 8.5 -43t5.5 -43l18 44t19 43l328 654q8 16 23 26t30 10h145l-701 -1346q-11 -22 -26 -32t-37 -10h-134l225 412z" />
<glyph unicode="z" horiz-adv-x="868" d="M-18 0l9 76q1 13 9 31.5t22 33.5l601 753h-503l17 143h719l-10 -78q-2 -19 -11 -37t-20 -32l-598 -748h511l-16 -142h-730z" />
<glyph unicode="{" horiz-adv-x="581" d="M68 585l13 106q76 0 113 61t37 188q0 23 -1.5 50t-3 55t-3 55t-1.5 50q0 99 23.5 177t69 131.5t113 82t155.5 28.5h50l-9 -79q-2 -20 -18 -28.5t-26 -8.5h-18q-77 0 -126 -59t-54 -167q-4 -48 -4 -94t1 -90.5t1.5 -87t-2.5 -83.5q-3 -47 -17 -86.5t-37 -69t-53 -50 t-64 -29.5q55 -16 87.5 -63.5t32.5 -111.5q0 -61 -15.5 -119.5t-33.5 -117.5t-33.5 -119t-15.5 -124q0 -72 34.5 -115.5t97.5 -43.5h19q11 0 24 -9.5t11 -28.5l-11 -79h-51q-70 0 -122 20.5t-86.5 57t-52 86.5t-17.5 110q0 68 16.5 129t35.5 120t35.5 116.5t16.5 118.5 q0 53 -28.5 87t-82.5 34z" />
<glyph unicode="|" horiz-adv-x="529" d="M193 -351v1921h141v-1921h-141z" />
<glyph unicode="}" horiz-adv-x="582" d="M-47 -294l11 79q2 19 16.5 28.5t26.5 9.5h18q77 0 126 59t54 167q4 47 4 93t-1 90t-1 86.5t3 83.5q2 47 16.5 86.5t37.5 69.5t52.5 50.5t63.5 29.5q-55 17 -88 64.5t-33 111.5q0 60 15.5 119t34 118t33.5 119t15 124q0 71 -34 115t-98 44h-17q-12 0 -25 8t-10 29l9 79h51 q70 0 122.5 -20.5t86.5 -57t51.5 -86.5t17.5 -110q0 -68 -16 -129t-35.5 -119.5t-35 -116.5t-15.5 -118q0 -53 27.5 -87t82.5 -34l-13 -106q-75 0 -113 -61t-38 -189q0 -22 1 -49t2.5 -55t3.5 -55.5t2 -49.5q0 -99 -24 -177t-69 -132t-112.5 -82.5t-154.5 -28.5h-51z" />
<glyph unicode="~" d="M119 424q0 69 19 126t54 98t87 64t118 23q53 0 105.5 -17t101 -36.5t92.5 -36.5t82 -17q67 0 104 43.5t38 114.5h148q0 -69 -19 -126t-54.5 -98t-87.5 -63.5t-117 -22.5q-54 0 -106 16.5t-100.5 36.5t-93 37t-81.5 17q-67 0 -104.5 -43t-38.5 -116h-147z" />
<glyph unicode="&#xa1;" horiz-adv-x="481" d="M50 -352l69 555q5 46 11.5 89.5t15 87.5t17.5 91t19 101h107q-2 -54 -4.5 -101t-6 -91t-7 -87.5t-9.5 -89.5l-67 -555h-145zM147 925q0 27 10 50t27.5 40.5t40.5 28t50 10.5t50 -10.5t40.5 -28t27.5 -41t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-40.5 -27t-50 -9.5 q-28 0 -50.5 9.5t-40 27t-27.5 40.5t-10 51z" />
<glyph unicode="&#xa2;" d="M126 441q0 129 39.5 239.5t114 191.5t181 128.5t241.5 50.5l35 184q4 19 19 34t37 15h65l-45 -240q83 -11 147 -43t112 -82l-52 -62q-9 -11 -17.5 -15.5t-22.5 -4.5q-12 0 -27.5 9t-37.5 21.5t-53 24.5t-75 19l-150 -789q66 4 112.5 20t78.5 33.5t53.5 31t37.5 13.5 q23 0 31 -15l41 -62q-69 -72 -168 -110t-211 -45l-34 -182q-5 -19 -20 -34t-36 -15h-65l44 232q-84 9 -153 44.5t-118.5 93t-76.5 137t-27 177.5zM300 450q0 -135 59.5 -217.5t167.5 -104.5l150 787q-92 -6 -162.5 -41t-118 -95.5t-72 -144t-24.5 -184.5z" />
<glyph unicode="&#xa3;" d="M0 0l13 115q37 10 66.5 26t51 40t35.5 58t20 82l37 309h-167l9 71q2 18 17.5 33t39.5 15h116l33 271q12 96 53 180.5t107.5 147t156.5 98.5t202 36q85 0 147.5 -19.5t108 -53.5t75 -80.5t47.5 -100.5l-76 -45q-27 -12 -49 -10t-39 24q-17 29 -36 54t-44.5 42.5t-62 27.5 t-89.5 10q-67 0 -121.5 -22t-96 -62.5t-67.5 -97.5t-34 -128l-34 -272h484l-7 -72q-2 -17 -18 -32t-39 -15h-435l-31 -262q-9 -77 -37 -131.5t-77 -97.5q31 6 61 10t61 4h701l-8 -75q-2 -13 -8.5 -27t-18.5 -25.5t-26.5 -18.5t-32.5 -7h-988z" />
<glyph unicode="&#xa4;" d="M139 1052l92 92l156 -156q45 32 98 49.5t112 17.5q58 0 110.5 -17t97.5 -48l156 157l92 -93l-155 -155q31 -45 48.5 -98t17.5 -113q0 -58 -17 -110t-47 -97l156 -154l-93 -95l-156 156q-45 -31 -98 -48t-112 -17q-58 0 -110 16.5t-96 46.5l-158 -157l-91 94l155 155 q-31 45 -48.5 98.5t-17.5 111.5t17 110.5t47 96.5zM368 688q0 -47 17.5 -88.5t49 -72.5t73.5 -49.5t89 -18.5q48 0 90.5 18.5t74 49.5t49.5 72.5t18 88.5q0 48 -18 90t-49.5 74t-74 50t-90.5 18q-47 0 -89 -18t-73.5 -50t-49 -74t-17.5 -90z" />
<glyph unicode="&#xa5;" d="M115 329l12 101h350l13 108h-350l13 101h317l-317 826h147q25 0 39.5 -11.5t19.5 -32.5l218 -586q10 -35 16 -64t9 -57q10 29 23 58t30 63l360 586q11 17 28.5 30.5t41.5 13.5h150l-522 -826h317l-12 -101h-350l-14 -108h351l-13 -101h-350l-41 -329h-176l40 329h-350z " />
<glyph unicode="&#xa6;" horiz-adv-x="528" d="M191 -351v814h143v-814h-143zM191 757v813h143v-813h-143z" />
<glyph unicode="&#xa7;" horiz-adv-x="946" d="M39 -3l49 68q9 13 21 19t29 6q18 0 35 -14.5t42.5 -31.5t62.5 -31.5t96 -14.5q51 0 91.5 15t68.5 41t43 61.5t15 77.5t-21 72.5t-55 55t-77.5 45t-88.5 42t-88.5 46t-77 57t-54.5 74.5t-21 99q0 90 49.5 162.5t153.5 111.5q-38 35 -61.5 80t-23.5 109q0 67 27 127t76 106 t120 73.5t160 27.5q93 0 165 -32t120 -87l-46 -65q-15 -23 -39 -22q-15 0 -32 11.5t-41 25.5t-58 26t-83 12q-48 0 -86.5 -14.5t-66 -38t-42.5 -55.5t-15 -66q0 -47 35 -80.5t87 -62t113 -57.5t113 -67t87 -89t35 -125q0 -93 -45 -166t-143 -115q37 -34 60 -77.5t23 -102.5 q0 -76 -28 -141.5t-80 -115t-127.5 -77.5t-171.5 -28q-99 0 -177.5 35t-127.5 90zM269 728q0 -47 30 -81.5t76 -62.5t102 -54t108 -57q60 30 85.5 74t25.5 98q0 50 -27.5 85t-71 62.5t-96 52t-103.5 54.5q-72 -36 -100.5 -77t-28.5 -94z" />
<glyph unicode="&#xa8;" horiz-adv-x="753" d="M226 1320q0 24 9.5 45t25 37t36 25.5t44.5 9.5t45 -9.5t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5t-25.5 -35.5t-37 -24.5t-45 -9.5t-44.5 9.5t-36 24.5t-25 35.5t-9.5 44.5zM596 1320q0 24 9 45t25 37t37 25.5t45 9.5t44.5 -9.5t36.5 -25.5t25 -37t9 -45t-9 -44.5 t-25 -35.5t-37 -24.5t-44 -9.5q-24 0 -45 9.5t-37 24.5t-25 35.5t-9 44.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1701" d="M117 733q0 103 26.5 199t75 179.5t117 151.5t151.5 117.5t178.5 76t198.5 26.5t199 -26.5t179 -76t151.5 -117.5t117.5 -151.5t75.5 -179t26.5 -199.5q0 -102 -26.5 -198t-75.5 -179t-117.5 -151t-151.5 -117t-178.5 -75.5t-199.5 -26.5q-103 0 -198.5 26.5t-178.5 75.5 t-151.5 117t-117 151t-75 178.5t-26.5 198.5zM219 733q0 -91 22.5 -175t64.5 -157t100.5 -132.5t130 -102t154.5 -65.5t173 -23t173.5 23t156 65.5t131 102t100.5 132.5t65 157t23 175q0 137 -50.5 257.5t-138 210t-206.5 141.5t-254 52t-252.5 -52t-205 -141.5t-137.5 -210 t-50 -257.5zM421 735q0 103 35.5 190t97.5 149t147.5 96t186.5 34q112 0 189 -34t135 -92l-47 -66q-5 -6 -13 -12.5t-20 -6.5q-14 0 -30.5 11.5t-41.5 25t-64.5 25.5t-98.5 12q-73 0 -130.5 -23t-98 -66t-62.5 -104.5t-22 -138.5q0 -79 22 -141t61 -103.5t93 -64t118 -22.5 q53 0 88.5 8.5t63 20t49 26t47.5 26.5q15 1 27 -11l62 -65q-58 -68 -143 -105.5t-203 -37.5q-100 0 -183 35t-142 97.5t-91 148.5t-32 188z" />
<glyph unicode="&#xaa;" horiz-adv-x="701" d="M141 991q0 41 19 79.5t63.5 69t117.5 50t181 21.5l4 39q5 58 -14 93t-75 35q-36 0 -60.5 -9t-43 -20t-33 -20t-30.5 -9q-14 0 -21.5 7.5t-12.5 17.5l-18 40q55 51 116.5 75t131.5 24q50 0 87.5 -19t61 -51.5t33 -75t3.5 -88.5l-48 -390h-59q-17 0 -25 5.5t-15 23.5l-5 50 q-26 -22 -49.5 -38t-48 -27.5t-51.5 -17t-59 -5.5q-66 0 -108 35.5t-42 104.5zM266 1008q0 -35 21.5 -50.5t54.5 -15.5q47 0 83.5 19.5t73.5 55.5l13 112q-68 -2 -114.5 -11t-75.5 -25t-42.5 -37.5t-13.5 -47.5z" />
<glyph unicode="&#xab;" horiz-adv-x="807" d="M81 530l3 23l286 397l53 -27q22 -11 24 -31.5t-16 -43.5l-179 -269q-16 -24 -33 -37q11 -11 23 -37l114 -269q10 -24 3.5 -44.5t-29.5 -31.5l-62 -28zM359 530l3 23l286 397l53 -27q22 -11 24 -31.5t-16 -43.5l-179 -269q-16 -24 -33 -37q11 -11 23 -37l114 -269 q10 -24 3.5 -44.5t-29.5 -31.5l-62 -28z" />
<glyph unicode="&#xac;" d="M154 621l15 133h879l-17 -133l-37 -295h-149l37 295h-728z" />
<glyph unicode="&#xae;" horiz-adv-x="1701" d="M117 733q0 103 26.5 199t75 179.5t117 151.5t151.5 117.5t178.5 76t198.5 26.5t199 -26.5t179 -76t151.5 -117.5t117.5 -151.5t75.5 -179t26.5 -199.5q0 -102 -26.5 -198t-75.5 -179t-117.5 -151t-151.5 -117t-178.5 -75.5t-199.5 -26.5q-103 0 -198.5 26.5t-178.5 75.5 t-151.5 117t-117 151t-75 178.5t-26.5 198.5zM219 733q0 -91 22.5 -175t64.5 -157t100.5 -132.5t130 -102t154.5 -65.5t173 -23t173.5 23t156 65.5t131 102t100.5 132.5t65 157t23 175q0 137 -50.5 257.5t-138 210t-206.5 141.5t-254 52t-252.5 -52t-205 -141.5t-137.5 -210 t-50 -257.5zM558 279v913h294q177 0 261.5 -64t84.5 -189q0 -96 -54 -163t-163 -93q17 -11 29.5 -26.5t23.5 -35.5l235 -342h-153q-34 0 -49 25l-207 309q-8 14 -21.5 21t-39.5 7h-82v-362h-159zM717 758h119q56 0 95.5 10.5t63.5 31t34.5 50.5t10.5 69q0 38 -9 66.5t-31 47 t-58.5 27.5t-89.5 9h-135v-311z" />
<glyph unicode="&#xaf;" horiz-adv-x="753" d="M248 1261l14 119h541l-13 -119h-542z" />
<glyph unicode="&#xb0;" horiz-adv-x="839" d="M150 1155q0 69 26 129.5t70.5 105t105 70t129.5 25.5q71 0 132 -25.5t106 -70t71 -105t26 -129.5q0 -68 -26 -127.5t-71 -104t-106 -71t-132 -26.5q-70 0 -130 26.5t-104.5 71t-70.5 104t-26 127.5zM278 1154q0 -43 15 -80.5t43 -65t65 -43.5t80 -16t80.5 16t65 43.5 t43 65t15.5 80.5t-15.5 81t-43 66t-65 44t-80.5 16t-80 -16t-65 -44t-43 -66t-15 -81z" />
<glyph unicode="&#xb1;" d="M41 82l15 134h981l-16 -134h-980zM119 706l15 134h417l48 388h143l-48 -388h420l-16 -134h-420l-46 -379h-144l47 379h-416z" />
<glyph unicode="&#xb2;" horiz-adv-x="684" d="M115 922l6 43q2 12 8.5 26.5t19.5 25.5l256 226q32 28 59.5 56.5t48 57.5t32 58.5t11.5 60.5q0 46 -27.5 70.5t-70.5 24.5q-48 0 -81.5 -25.5t-51.5 -69.5q-12 -18 -25.5 -25t-40.5 -4l-68 13q29 108 105.5 162t181.5 54q98 0 153 -51.5t55 -136.5q0 -46 -15.5 -85 t-42 -73t-60 -66.5t-70.5 -65.5l-192 -174q52 14 95 15h207q20 0 30 -11t7 -29l-9 -77h-521z" />
<glyph unicode="&#xb3;" horiz-adv-x="684" d="M133 1110l59 25q23 8 41.5 4t24.5 -21q3 -10 9 -26.5t19 -32t35.5 -27t58.5 -11.5q35 0 62.5 11.5t46.5 30.5t29.5 43.5t10.5 51.5q0 50 -36 75t-124 25l10 91q93 2 135.5 33.5t42.5 94.5q0 45 -27 68t-72 23q-50 0 -83 -24.5t-50 -66.5q-12 -20 -25 -26.5t-38 -3.5 l-65 13q14 53 41.5 94t64 67.5t81 40.5t94.5 14q48 0 86 -13.5t65 -36.5t41 -55t14 -68q0 -143 -140 -190q57 -17 85.5 -52.5t28.5 -91.5q0 -61 -24.5 -109t-64.5 -80t-91 -49t-104 -17q-58 0 -99 12.5t-69.5 38t-45.5 61.5t-27 84z" />
<glyph unicode="&#xb4;" horiz-adv-x="753" d="M408 1191l166 248q15 24 32.5 34.5t47.5 10.5h164l-250 -264q-14 -15 -27.5 -22t-32.5 -7h-100z" />
<glyph unicode="&#xb5;" horiz-adv-x="1242" d="M73 -273l161 1310h183l-81 -665q-5 -100 35.5 -157.5t129.5 -57.5q84 0 151.5 49.5t119.5 139.5l85 691h183l-93 -756q-10 -80 21.5 -117t91.5 -37h58l-8 -74q-4 -24 -38 -43.5t-91 -19.5q-40 0 -75.5 12t-62 37.5t-43.5 64t-21 91.5q-66 -90 -142.5 -139.5t-165.5 -49.5 q-67 0 -114 21.5t-77 62.5q1 -40 -2 -80.5t-8 -75.5l-34 -285h-92q-39 0 -57 20.5t-14 57.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="1424" d="M117 1027q0 92 36.5 172.5t104.5 140t162 93.5t209 34h829l-18 -156h-226l-186 -1517h-160l186 1517h-284l-185 -1517h-161l107 882q-94 0 -170.5 25.5t-131 71.5t-83.5 110.5t-29 143.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="526" d="M108 607q0 32 11.5 60.5t32.5 49t48 33t58 12.5q32 0 60.5 -12.5t49 -33t33 -49t12.5 -60.5q0 -31 -12.5 -58.5t-33 -48.5t-49 -32.5t-60.5 -11.5q-31 0 -58 11.5t-48 32.5t-32.5 48.5t-11.5 58.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="753" d="M184 -334l25 56q7 18 25 19q6 0 13 -3.5l16 -8t22.5 -8.5t32.5 -4q41 0 62.5 19.5t21.5 49.5q0 35 -36 49.5t-103 24.5l73 166h102l-40 -104q78 -17 113.5 -49.5t35.5 -79.5q0 -37 -17 -66.5t-47.5 -50t-72.5 -31.5t-92 -11q-39 0 -72.5 9t-61.5 23z" />
<glyph unicode="&#xb9;" horiz-adv-x="684" d="M204 922l11 95h152l55 447l11 45l-124 -94q-19 -13 -35.5 -8.5t-22.5 14.5l-33 55l250 194h108l-80 -653h123l-10 -95h-405z" />
<glyph unicode="&#xba;" horiz-adv-x="769" d="M154 1118q0 83 24.5 150.5t69 115.5t106 73.5t135.5 25.5q59 0 107 -19t81 -54.5t50.5 -85t17.5 -111.5q0 -84 -24.5 -151.5t-68 -115t-105 -73t-134.5 -25.5q-60 0 -108.5 19t-82 54t-51 85.5t-17.5 111.5zM291 1116q0 -77 33 -120t102 -43q92 0 137 70.5t45 189.5 q0 77 -32.5 120.5t-98.5 43.5q-51 0 -86.5 -19.5t-57.5 -53.5t-32 -82t-10 -106z" />
<glyph unicode="&#xbb;" horiz-adv-x="806" d="M63.5 191.5q-1.5 20.5 14.5 44.5l181 269q15 25 31 37q-13 12 -23 37l-114 269q-10 24 -2.5 43.5t30.5 31.5l60 27l186 -397l-2 -23l-284 -398l-54 28q-22 11 -23.5 31.5zM342.5 191.5q-1.5 20.5 14.5 44.5l181 269q15 25 31 37q-13 12 -23 37l-114 269q-10 24 -2.5 43.5 t30.5 31.5l60 27l186 -397l-2 -23l-284 -398l-54 28q-22 11 -23.5 31.5z" />
<glyph unicode="&#xbc;" horiz-adv-x="1428" d="M149 720l11 95h152l55 447l11 45l-124 -94q-19 -13 -35.5 -8.5t-22.5 14.5l-33 55l250 194h108l-80 -653h123l-10 -95h-405zM156 0l947 1404q20 30 45.5 45.5t58.5 15.5h73l-951 -1411q-24 -32 -47 -43t-53 -11h-73zM725 273l412 472h122l-57 -462h112l-7 -75 q-2 -11 -10 -19t-21 -8h-86l-23 -181h-108l22 181h-314q-17 0 -26.5 8.5t-10.5 20.5zM856 283h238l30 243q2 19 6.5 42.5t11.5 47.5z" />
<glyph unicode="&#xbd;" horiz-adv-x="1408" d="M118 0l947 1404q20 30 45.5 45.5t58.5 15.5h73l-951 -1411q-24 -32 -47 -43t-53 -11h-73zM151 720l11 95h152l55 447l11 45l-124 -94q-19 -13 -35.5 -8.5t-22.5 14.5l-33 55l250 194h108l-80 -653h123l-10 -95h-405zM729 0l6 43q2 12 8.5 26.5t19.5 25.5l256 226 q32 28 59.5 56.5t48 57.5t32 58.5t11.5 60.5q0 46 -27.5 70.5t-70.5 24.5q-48 0 -81.5 -25.5t-51.5 -69.5q-12 -18 -25.5 -25t-40.5 -4l-68 13q29 108 105.5 162t181.5 54q98 0 153 -51.5t55 -136.5q0 -46 -15.5 -85t-42 -73t-60 -66.5t-70.5 -65.5l-192 -174q52 14 95 15 h207q20 0 30 -11t7 -29l-9 -77h-521z" />
<glyph unicode="&#xbe;" horiz-adv-x="1446" d="M119 908l59 25q23 8 41.5 4t24.5 -21q3 -10 9 -26.5t19 -32t35.5 -27t58.5 -11.5q35 0 62.5 11.5t46.5 30.5t29.5 43.5t10.5 51.5q0 50 -36 75t-124 25l10 91q93 2 135.5 33.5t42.5 94.5q0 45 -27 68t-72 23q-50 0 -83 -24.5t-50 -66.5q-12 -20 -25 -26.5t-38 -3.5 l-65 13q14 53 41.5 94t64 67.5t81 40.5t94.5 14q48 0 86 -13.5t65 -36.5t41 -55t14 -68q0 -143 -140 -190q57 -17 85.5 -52.5t28.5 -91.5q0 -61 -24.5 -109t-64.5 -80t-91 -49t-104 -17q-58 0 -99 12.5t-69.5 38t-45.5 61.5t-27 84zM182 0l947 1404q20 30 45.5 45.5 t58.5 15.5h73l-951 -1411q-24 -32 -47 -43t-53 -11h-73zM746 273l412 472h122l-57 -462h112l-7 -75q-2 -11 -10 -19t-21 -8h-86l-23 -181h-108l22 181h-314q-17 0 -26.5 8.5t-10.5 20.5zM877 283h238l30 243q2 19 6.5 42.5t11.5 47.5z" />
<glyph unicode="&#xbf;" horiz-adv-x="835" d="M-6 -80q0 83 24 142t61.5 101.5t82 73t84.5 58t69.5 55t38.5 64.5l38 158h119l-9 -172q-2 -46 -27.5 -79.5t-62.5 -63.5t-79.5 -58.5t-78.5 -63t-60 -78.5t-24 -104q0 -42 13.5 -74.5t36.5 -54.5t53.5 -33t64.5 -11q56 0 98.5 15.5t73.5 33.5t51 33.5t31 15.5 q25 0 34 -21l38 -75q-34 -30 -72.5 -56.5t-83 -46.5t-95.5 -32.5t-108 -12.5q-70 0 -126.5 20t-98 57t-64 90t-22.5 119zM382 925q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50.5 -10.5t40.5 -28t27.5 -41t10.5 -49.5q0 -28 -10.5 -51t-27.5 -40.5t-40.5 -27t-50.5 -9.5 t-50.5 9.5t-40.5 27t-26.5 40.5t-9.5 51z" />
<glyph unicode="&#xc0;" horiz-adv-x="1292" d="M-67 0l719 1467h195l359 -1467h-147q-26 0 -40 13t-21 33l-79 358h-597l-168 -358q-9 -18 -28.5 -32t-42.5 -14h-150zM387 544h501l-134 607q-5 28 -12.5 64t-12.5 78q-15 -43 -30.5 -78.5t-27.5 -64.5zM422 1896h179q31 0 46.5 -5t29.5 -27l182 -281h-121q-20 0 -32 2.5 t-23 17.5z" />
<glyph unicode="&#xc1;" horiz-adv-x="1292" d="M-67 0l719 1467h195l359 -1467h-147q-26 0 -40 13t-21 33l-79 358h-597l-168 -358q-9 -18 -28.5 -32t-42.5 -14h-150zM387 544h501l-134 607q-5 28 -12.5 64t-12.5 78q-15 -43 -30.5 -78.5t-27.5 -64.5zM636 1583l250 281q18 23 34.5 27.5t47.5 4.5h189l-339 -292 q-17 -14 -30 -17.5t-33 -3.5h-119z" />
<glyph unicode="&#xc2;" horiz-adv-x="1292" d="M-67 0l719 1467h195l359 -1467h-147q-26 0 -40 13t-21 33l-79 358h-597l-168 -358q-9 -18 -28.5 -32t-42.5 -14h-150zM387 544h501l-134 607q-5 28 -12.5 64t-12.5 78q-15 -43 -30.5 -78.5t-27.5 -64.5zM475 1583l257 278h151l193 -278h-123q-11 0 -26.5 4.5t-28.5 25.5 l-94 144q-2 5 -4.5 8.5t-4.5 9.5q-3 -7 -13 -18l-127 -144q-18 -20 -34 -25t-27 -5h-119z" />
<glyph unicode="&#xc3;" horiz-adv-x="1292" d="M-67 0l719 1467h195l359 -1467h-147q-26 0 -40 13t-21 33l-79 358h-597l-168 -358q-9 -18 -28.5 -32t-42.5 -14h-150zM387 544h501l-134 607q-5 28 -12.5 64t-12.5 78q-15 -43 -30.5 -78.5t-27.5 -64.5zM475 1585q5 44 21.5 85t42 72.5t59.5 49.5t75 18q43 0 75 -16.5 t58 -36.5t49.5 -37t49.5 -17q22 0 39 8.5t30 23.5t20.5 33.5t9.5 36.5h104q-5 -44 -21 -84.5t-41 -71.5t-59.5 -49.5t-75.5 -18.5q-42 0 -73.5 17t-58 37t-50.5 37t-49 17q-22 0 -39 -9.5t-30 -24.5t-20.5 -33t-10.5 -37h-105z" />
<glyph unicode="&#xc4;" horiz-adv-x="1292" d="M-67 0l719 1467h195l359 -1467h-147q-26 0 -40 13t-21 33l-79 358h-597l-168 -358q-9 -18 -28.5 -32t-42.5 -14h-150zM387 544h501l-134 607q-5 28 -12.5 64t-12.5 78q-15 -43 -30.5 -78.5t-27.5 -64.5zM463 1703q0 25 9 45.5t25 37t36.5 26t43.5 9.5q24 0 44.5 -9.5 t37 -26t25.5 -37t9 -45.5q0 -24 -9 -44.5t-25.5 -35.5t-37.5 -24t-44 -9t-43.5 9t-36.5 24t-25 35.5t-9 44.5zM852 1703q0 25 9.5 45.5t24.5 37t36 26t45 9.5t44.5 -9.5t36.5 -26t25.5 -37t9.5 -45.5q0 -24 -9.5 -44.5t-25.5 -35.5t-37 -24t-44 -9q-24 0 -45 9t-36 24 t-24.5 35.5t-9.5 44.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1292" d="M-67 0l719 1467h195l359 -1467h-147q-26 0 -40 13t-21 33l-79 358h-597l-168 -358q-9 -18 -28.5 -32t-42.5 -14h-150zM387 544h501l-134 607q-5 28 -12.5 64t-12.5 78q-15 -43 -30.5 -78.5t-27.5 -64.5zM605 1738q5 40 23.5 73.5t45.5 57t60.5 37t68.5 13.5 q37 0 68 -13.5t53 -37t32.5 -57t6.5 -73.5t-22 -73t-46 -56.5t-61.5 -36.5t-70.5 -13q-36 0 -66 13t-52 36.5t-33 56.5t-7 73zM690 1738q-5 -45 17.5 -74t66.5 -29q42 0 71.5 29t34.5 74q5 46 -17.5 74.5t-65.5 28.5q-44 0 -73 -28.5t-34 -74.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1776" d="M-68 0l881 1467h977l-18 -156h-709v-496h524l-18 -150h-506v-509h567l-20 -156h-727v404h-516l-210 -358q-11 -18 -31.5 -32t-44.5 -14h-149zM449 544h434v767q-14 -37 -29.5 -70t-33.5 -62z" />
<glyph unicode="&#xc7;" horiz-adv-x="1278" d="M100 621q0 191 59.5 349.5t163.5 273t244.5 177.5t301.5 63q78 0 141.5 -13.5t116 -39t95 -61.5t78.5 -80l-65 -80q-8 -10 -18.5 -16.5t-25.5 -6.5q-18 0 -38.5 20.5t-56 44.5t-91.5 44.5t-145 20.5q-120 0 -222.5 -47.5t-178.5 -136.5t-119.5 -214.5t-43.5 -282.5 q0 -114 29.5 -204t82.5 -153t125.5 -97t159.5 -34q68 0 120 11.5t92 28t68 36.5t49 36t35.5 27.5t25.5 11.5q9 0 15.5 -3.5t10.5 -8.5l67 -83q-88 -96 -201.5 -153t-260.5 -65l-25 -64q78 -17 113.5 -49.5t35.5 -79.5q0 -37 -17 -66.5t-48 -50t-73 -31.5t-91 -11 q-39 0 -72.5 9t-61.5 23l25 56q7 18 25 19q6 0 13 -3.5l16 -8t22.5 -8.5t32.5 -4q41 0 62.5 19.5t21.5 49.5q0 35 -36 49.5t-103 24.5l55 126q-120 9 -214.5 59.5t-159.5 134t-100 195.5t-35 246z" />
<glyph unicode="&#xc8;" horiz-adv-x="1101" d="M100 0l181 1467h835l-19 -156h-643l-61 -495h519l-19 -150h-518l-63 -510h644l-19 -156h-837zM418 1896h179q31 0 46.5 -5t29.5 -27l182 -281h-121q-20 0 -32 2.5t-23 17.5z" />
<glyph unicode="&#xc9;" horiz-adv-x="1101" d="M100 0l181 1467h835l-19 -156h-643l-61 -495h519l-19 -150h-518l-63 -510h644l-19 -156h-837zM607 1583l250 281q18 23 34.5 27.5t47.5 4.5h189l-339 -292q-17 -14 -30 -17.5t-33 -3.5h-119z" />
<glyph unicode="&#xca;" horiz-adv-x="1101" d="M100 0l181 1467h835l-19 -156h-643l-61 -495h519l-19 -150h-518l-63 -510h644l-19 -156h-837zM415 1583l257 278h150l194 -278h-123q-11 0 -26.5 4.5t-28.5 25.5l-95 144q-2 5 -4.5 8.5t-4.5 9.5q-3 -7 -12 -18l-127 -144q-18 -20 -34 -25t-27 -5h-119z" />
<glyph unicode="&#xcb;" horiz-adv-x="1101" d="M100 0l181 1467h835l-19 -156h-643l-61 -495h519l-19 -150h-518l-63 -510h644l-19 -156h-837zM419 1703q0 25 9 45.5t25 37t36.5 26t43.5 9.5q24 0 44.5 -9.5t37 -26t25.5 -37t9 -45.5q0 -24 -9 -44.5t-25.5 -35.5t-37.5 -24t-44 -9t-43.5 9t-36.5 24t-25 35.5t-9 44.5z M808 1703q0 25 9.5 45.5t24.5 37t36 26t45 9.5t44.5 -9.5t36.5 -26t25.5 -37t9.5 -45.5q0 -24 -9.5 -44.5t-25.5 -35.5t-37 -24t-44 -9q-24 0 -45 9t-36 24t-24.5 35.5t-9.5 44.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="545" d="M64 1896h179q31 0 46.5 -5t29.5 -27l182 -281h-121q-20 0 -32 2.5t-23 17.5zM100 0l180 1467h192l-179 -1467h-193z" />
<glyph unicode="&#xcd;" horiz-adv-x="545" d="M100 0l180 1467h192l-179 -1467h-193zM276 1583l250 281q18 23 34.5 27.5t47.5 4.5h189l-339 -292q-17 -14 -30 -17.5t-33 -3.5h-119z" />
<glyph unicode="&#xce;" horiz-adv-x="545" d="M93 1583l257 278h151l193 -278h-123q-11 0 -26.5 4.5t-28.5 25.5l-94 144q-2 5 -4.5 8.5t-4.5 9.5q-3 -7 -13 -18l-127 -144q-18 -20 -34 -25t-27 -5h-119zM100 0l180 1467h192l-179 -1467h-193z" />
<glyph unicode="&#xcf;" horiz-adv-x="545" d="M97 1703q0 25 9 45.5t25 37t36.5 26t43.5 9.5q24 0 44.5 -9.5t37 -26t25.5 -37t9 -45.5q0 -24 -9 -44.5t-25.5 -35.5t-37.5 -24t-44 -9t-43.5 9t-36.5 24t-25 35.5t-9 44.5zM100 0l180 1467h192l-179 -1467h-193zM486 1703q0 25 9.5 45.5t24.5 37t36 26t45 9.5t44.5 -9.5 t36.5 -26t25.5 -37t9.5 -45.5q0 -24 -9.5 -44.5t-25.5 -35.5t-37 -24t-44 -9q-24 0 -45 9t-36 24t-24.5 35.5t-9.5 44.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1460" d="M14 683l14 113h183l83 671h506q135 0 243.5 -46t185 -129.5t116.5 -199.5t40 -255q0 -187 -57.5 -341.5t-159.5 -264.5t-242.5 -170.5t-304.5 -60.5h-507l84 683h-184zM326 155h313q123 0 224 46.5t173.5 134t112.5 213t40 284.5q0 111 -28 199.5t-80.5 150.5t-128 95.5 t-171.5 33.5h-314l-64 -516h357l-14 -113h-356z" />
<glyph unicode="&#xd1;" horiz-adv-x="1454" d="M100 0l181 1467h99q26 0 37 -6t23 -26l634 -1122q2 24 3.5 45.5t3.5 39.5l132 1069h169l-181 -1467h-95q-24 0 -38 8.5t-28 27.5l-632 1122q-2 -20 -3.5 -38t-3.5 -35l-132 -1085h-169zM551 1584q5 44 21.5 85t42 72.5t59.5 49.5t75 18q43 0 75 -16.5t58 -36.5t49.5 -37 t49.5 -17q22 0 39 8.5t30 23.5t20.5 33.5t9.5 36.5h104q-5 -44 -21 -84.5t-41 -71.5t-59.5 -49.5t-75.5 -18.5q-42 0 -73.5 17t-58.5 37t-50.5 37t-48.5 17q-22 0 -39.5 -9.5t-30 -24.5t-20.5 -33t-10 -37h-105z" />
<glyph unicode="&#xd2;" horiz-adv-x="1526" d="M100 628q0 187 58.5 344t162 271t244.5 177.5t305 63.5q135 0 243.5 -49t184 -135t116 -204t40.5 -258q0 -187 -58 -344t-160.5 -270t-243.5 -176t-307 -63q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5zM297 633q0 -112 28 -202t81.5 -153.5t129 -98t171.5 -34.5 q122 0 223 48.5t174 138t113 217t40 285.5q0 112 -28.5 202t-81 153.5t-129 98.5t-171.5 35q-121 0 -222 -48.5t-174 -138.5t-113.5 -217.5t-40.5 -285.5zM576 1896h179q31 0 46.5 -5t29.5 -27l182 -281h-121q-20 0 -32 2.5t-23 17.5z" />
<glyph unicode="&#xd3;" horiz-adv-x="1526" d="M100 628q0 187 58.5 344t162 271t244.5 177.5t305 63.5q135 0 243.5 -49t184 -135t116 -204t40.5 -258q0 -187 -58 -344t-160.5 -270t-243.5 -176t-307 -63q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5zM297 633q0 -112 28 -202t81.5 -153.5t129 -98t171.5 -34.5 q122 0 223 48.5t174 138t113 217t40 285.5q0 112 -28.5 202t-81 153.5t-129 98.5t-171.5 35q-121 0 -222 -48.5t-174 -138.5t-113.5 -217.5t-40.5 -285.5zM747 1583l250 281q18 23 34.5 27.5t47.5 4.5h189l-339 -292q-17 -14 -30 -17.5t-33 -3.5h-119z" />
<glyph unicode="&#xd4;" horiz-adv-x="1526" d="M100 628q0 187 58.5 344t162 271t244.5 177.5t305 63.5q135 0 243.5 -49t184 -135t116 -204t40.5 -258q0 -187 -58 -344t-160.5 -270t-243.5 -176t-307 -63q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5zM297 633q0 -112 28 -202t81.5 -153.5t129 -98t171.5 -34.5 q122 0 223 48.5t174 138t113 217t40 285.5q0 112 -28.5 202t-81 153.5t-129 98.5t-171.5 35q-121 0 -222 -48.5t-174 -138.5t-113.5 -217.5t-40.5 -285.5zM579 1583l257 278h150l194 -278h-123q-11 0 -26.5 4.5t-29.5 25.5l-94 144q-2 5 -4.5 8.5t-4.5 9.5q-3 -7 -12 -18 l-127 -144q-18 -20 -34 -25t-28 -5h-118z" />
<glyph unicode="&#xd5;" horiz-adv-x="1526" d="M100 628q0 187 58.5 344t162 271t244.5 177.5t305 63.5q135 0 243.5 -49t184 -135t116 -204t40.5 -258q0 -187 -58 -344t-160.5 -270t-243.5 -176t-307 -63q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5zM297 633q0 -112 28 -202t81.5 -153.5t129 -98t171.5 -34.5 q122 0 223 48.5t174 138t113 217t40 285.5q0 112 -28.5 202t-81 153.5t-129 98.5t-171.5 35q-121 0 -222 -48.5t-174 -138.5t-113.5 -217.5t-40.5 -285.5zM579 1585q5 44 21.5 85t42 72.5t59.5 49.5t75 18q43 0 75 -16.5t58 -36.5t49.5 -37t49.5 -17q22 0 39 8.5t30 23.5 t20.5 33.5t9.5 36.5h104q-5 -44 -21 -84.5t-41 -71.5t-59.5 -49.5t-75.5 -18.5q-42 0 -73.5 17t-58 37t-50.5 37t-49 17q-22 0 -39 -9.5t-30 -24.5t-20.5 -33t-10.5 -37h-105z" />
<glyph unicode="&#xd6;" horiz-adv-x="1526" d="M100 628q0 187 58.5 344t162 271t244.5 177.5t305 63.5q135 0 243.5 -49t184 -135t116 -204t40.5 -258q0 -187 -58 -344t-160.5 -270t-243.5 -176t-307 -63q-135 0 -243 48t-184.5 133.5t-117 203t-40.5 258.5zM297 633q0 -112 28 -202t81.5 -153.5t129 -98t171.5 -34.5 q122 0 223 48.5t174 138t113 217t40 285.5q0 112 -28.5 202t-81 153.5t-129 98.5t-171.5 35q-121 0 -222 -48.5t-174 -138.5t-113.5 -217.5t-40.5 -285.5zM604 1703q0 25 9 45.5t25 37t36.5 26t43.5 9.5q24 0 44.5 -9.5t37 -26t25.5 -37t9 -45.5q0 -24 -9 -44.5t-25.5 -35.5 t-37.5 -24t-44 -9t-43.5 9t-36.5 24t-25 35.5t-9 44.5zM993 1703q0 25 9.5 45.5t24.5 37t36 26t45 9.5t44.5 -9.5t36.5 -26t25.5 -37t9.5 -45.5q0 -24 -9.5 -44.5t-25.5 -35.5t-37 -24t-44 -9q-24 0 -45 9t-36 24t-24.5 35.5t-9.5 44.5z" />
<glyph unicode="&#xd7;" d="M97 322l411 366l-311 354l105 96l312 -356l397 354l83 -96l-397 -353l319 -363l-105 -96l-320 365l-412 -367z" />
<glyph unicode="&#xd8;" horiz-adv-x="1526" d="M47 -112l216 272q-79 87 -121 205.5t-42 262.5q0 187 58.5 344t162 271t244.5 177.5t305 63.5q106 0 196 -30.5t161 -85.5l91 115q11 13 20.5 23t19 16t21 8.5t27.5 2.5h98l-195 -246q71 -86 108 -200t37 -249q0 -187 -58 -344t-160.5 -270t-243.5 -176t-307 -63 q-99 0 -183 25.5t-153 73.5l-111 -140q-26 -30 -56 -43t-58 -13h-77zM297 633q0 -99 22 -180t64 -144l730 917q-52 47 -118.5 72t-147.5 25q-121 0 -222 -48.5t-174 -138.5t-113.5 -217.5t-40.5 -285.5zM462 224q49 -39 110 -59t135 -20q122 0 223 48.5t174 138t113 217 t40 285.5q0 90 -17.5 165t-52.5 135z" />
<glyph unicode="&#xd9;" horiz-adv-x="1402" d="M155 573l108 894h192l-110 -893q-11 -92 3 -169.5t52 -134t99 -88t144 -31.5t151.5 31.5t121 87.5t85.5 133.5t45 169.5l109 894h192l-110 -894q-15 -126 -66.5 -234t-131.5 -187t-185 -124t-231 -45q-125 0 -220 45t-155.5 124t-84.5 187t-8 234zM496 1896h179 q31 0 46.5 -5t29.5 -27l182 -281h-121q-20 0 -32 2.5t-23 17.5z" />
<glyph unicode="&#xda;" horiz-adv-x="1402" d="M155 573l108 894h192l-110 -893q-11 -92 3 -169.5t52 -134t99 -88t144 -31.5t151.5 31.5t121 87.5t85.5 133.5t45 169.5l109 894h192l-110 -894q-15 -126 -66.5 -234t-131.5 -187t-185 -124t-231 -45q-125 0 -220 45t-155.5 124t-84.5 187t-8 234zM686 1583l250 281 q18 23 34.5 27.5t47.5 4.5h189l-339 -292q-17 -14 -30 -17.5t-33 -3.5h-119z" />
<glyph unicode="&#xdb;" horiz-adv-x="1402" d="M155 573l108 894h192l-110 -893q-11 -92 3 -169.5t52 -134t99 -88t144 -31.5t151.5 31.5t121 87.5t85.5 133.5t45 169.5l109 894h192l-110 -894q-15 -126 -66.5 -234t-131.5 -187t-185 -124t-231 -45q-125 0 -220 45t-155.5 124t-84.5 187t-8 234zM522 1583l257 278h151 l193 -278h-123q-11 0 -26.5 4.5t-28.5 25.5l-94 144q-2 5 -4.5 8.5t-4.5 9.5q-3 -7 -13 -18l-127 -144q-18 -20 -34 -25t-27 -5h-119z" />
<glyph unicode="&#xdc;" horiz-adv-x="1402" d="M155 573l108 894h192l-110 -893q-11 -92 3 -169.5t52 -134t99 -88t144 -31.5t151.5 31.5t121 87.5t85.5 133.5t45 169.5l109 894h192l-110 -894q-15 -126 -66.5 -234t-131.5 -187t-185 -124t-231 -45q-125 0 -220 45t-155.5 124t-84.5 187t-8 234zM532 1703q0 25 9 45.5 t25 37t36.5 26t43.5 9.5q24 0 44.5 -9.5t37 -26t25.5 -37t9 -45.5q0 -24 -9 -44.5t-25.5 -35.5t-37.5 -24t-44 -9t-43.5 9t-36.5 24t-25 35.5t-9 44.5zM921 1703q0 25 9.5 45.5t24.5 37t36 26t45 9.5t44.5 -9.5t36.5 -26t25.5 -37t9.5 -45.5q0 -24 -9.5 -44.5t-25.5 -35.5 t-37 -24t-44 -9q-24 0 -45 9t-36 24t-24.5 35.5t-9.5 44.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1191" d="M110 1467h169q26 0 40 -12t21 -33l243 -587q19 -58 30 -111q12 27 27 54t33 57l389 587q11 17 28 31t42 14h158l-602 -880l-73 -587h-191l73 585zM582 1583l250 281q18 23 34.5 27.5t47.5 4.5h189l-339 -292q-17 -14 -30 -17.5t-33 -3.5h-119z" />
<glyph unicode="&#xde;" horiz-adv-x="1147" d="M100 0l180 1467h191l-34 -274h214q117 0 202.5 -27.5t142 -78.5t84 -122t27.5 -158q0 -116 -38 -212.5t-110.5 -167t-178 -109t-241.5 -38.5h-213l-35 -280h-191zM344 431h213q87 0 153.5 26.5t111.5 74.5t68.5 114t23.5 144q0 119 -69.5 185.5t-212.5 66.5h-213z" />
<glyph unicode="&#xdf;" horiz-adv-x="1185" d="M81 942l7 73h167q24 94 72.5 179.5t116 151t151.5 104t179 38.5q90 0 155 -27.5t106 -70t60.5 -94.5t19.5 -100q0 -70 -24 -119t-59.5 -86t-77.5 -64.5t-77 -53.5t-59 -54.5t-24 -67.5q0 -36 22 -60t54.5 -45t71 -43t71.5 -53.5t55 -76.5t22 -112q0 -88 -32 -158 t-85.5 -118.5t-126 -74.5t-152.5 -26q-92 0 -162.5 35t-120.5 91l49 67q9 12 21 19.5t29 7.5t34.5 -15t41 -32.5t56.5 -32t82 -14.5q43 0 79.5 15.5t62.5 43.5t40.5 65t14.5 80q0 49 -24 81.5t-58.5 56t-75.5 44t-76 47t-58.5 63.5t-23.5 94q0 63 25 108.5t63 81t82 65 t82 62.5t63 74t25 98q0 29 -10 59t-33.5 54.5t-61 40t-90.5 15.5q-63 0 -119.5 -34.5t-100 -94t-73 -137.5t-39.5 -165l-110 -893l-48 -212q-11 -37 -33 -57t-61 -20h-72l139 1139l-111 13q-22 5 -33.5 15.5t-7.5 29.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1046" d="M51 351q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t124.5 -37l-124 -1004h-95q-36 0 -48.5 18.5t-12.5 44.5l19 210q-37 -66 -79 -119t-90 -90t-100.5 -57.5t-108.5 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156zM233 384 q0 -247 158 -247q50 0 98.5 31.5t92 87t80 132t65.5 167.5l44 355q-23 5 -44.5 7t-43.5 2q-61 0 -118.5 -20t-107.5 -56t-91 -85.5t-70.5 -109t-46 -126.5t-16.5 -138zM472 1484h159q31 0 45 -11t24 -34l112 -248h-95q-19 0 -31 6.5t-24 22.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="1046" d="M51 351q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t124.5 -37l-124 -1004h-95q-36 0 -48.5 18.5t-12.5 44.5l19 210q-37 -66 -79 -119t-90 -90t-100.5 -57.5t-108.5 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156zM233 384 q0 -247 158 -247q50 0 98.5 31.5t92 87t80 132t65.5 167.5l44 355q-23 5 -44.5 7t-43.5 2q-61 0 -118.5 -20t-107.5 -56t-91 -85.5t-70.5 -109t-46 -126.5t-16.5 -138zM625 1191l166 248q15 24 32.5 34.5t47.5 10.5h164l-250 -264q-14 -15 -27.5 -22t-32.5 -7h-100z" />
<glyph unicode="&#xe2;" horiz-adv-x="1046" d="M51 351q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t124.5 -37l-124 -1004h-95q-36 0 -48.5 18.5t-12.5 44.5l19 210q-37 -66 -79 -119t-90 -90t-100.5 -57.5t-108.5 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156zM233 384 q0 -247 158 -247q50 0 98.5 31.5t92 87t80 132t65.5 167.5l44 355q-23 5 -44.5 7t-43.5 2q-61 0 -118.5 -20t-107.5 -56t-91 -85.5t-70.5 -109t-46 -126.5t-16.5 -138zM389 1197l241 270h155l181 -270h-113q-10 0 -20 4t-19 16l-101 135q-3 5 -7 9.5t-7 9.5q-5 -5 -9 -10 t-8 -9l-133 -135q-19 -19 -43 -20h-117z" />
<glyph unicode="&#xe3;" horiz-adv-x="1046" d="M51 351q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t124.5 -37l-124 -1004h-95q-36 0 -48.5 18.5t-12.5 44.5l19 210q-37 -66 -79 -119t-90 -90t-100.5 -57.5t-108.5 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156zM233 384 q0 -247 158 -247q50 0 98.5 31.5t92 87t80 132t65.5 167.5l44 355q-23 5 -44.5 7t-43.5 2q-61 0 -118.5 -20t-107.5 -56t-91 -85.5t-70.5 -109t-46 -126.5t-16.5 -138zM421 1231q5 49 22 89.5t42.5 69.5t58 45t69.5 16q34 0 60.5 -15t50 -32.5t44.5 -32t43 -14.5q69 0 81 88 h102q-5 -48 -21.5 -88.5t-42 -69.5t-57.5 -44.5t-70 -15.5q-33 0 -60 14.5t-50.5 32t-44.5 32.5t-43 15q-69 0 -80 -90h-104z" />
<glyph unicode="&#xe4;" horiz-adv-x="1046" d="M51 351q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t124.5 -37l-124 -1004h-95q-36 0 -48.5 18.5t-12.5 44.5l19 210q-37 -66 -79 -119t-90 -90t-100.5 -57.5t-108.5 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156zM233 384 q0 -247 158 -247q50 0 98.5 31.5t92 87t80 132t65.5 167.5l44 355q-23 5 -44.5 7t-43.5 2q-61 0 -118.5 -20t-107.5 -56t-91 -85.5t-70.5 -109t-46 -126.5t-16.5 -138zM420 1320q0 24 9.5 45t25 37t36 25.5t44.5 9.5t45 -9.5t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5 t-25.5 -35.5t-37 -24.5t-45 -9.5t-44.5 9.5t-36 24.5t-25 35.5t-9.5 44.5zM790 1320q0 24 9 45t25 37t37 25.5t45 9.5t44.5 -9.5t36.5 -25.5t25 -37t9 -45t-9 -44.5t-25 -35.5t-37 -24.5t-44 -9.5q-24 0 -45 9.5t-37 24.5t-25 35.5t-9 44.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1046" d="M51 351q0 92 23 179.5t65.5 165.5t102 143t132.5 112.5t157 74t177 26.5q67 0 129.5 -11t124.5 -37l-124 -1004h-95q-36 0 -48.5 18.5t-12.5 44.5l19 210q-37 -66 -79 -119t-90 -90t-100.5 -57.5t-108.5 -20.5q-61 0 -111.5 24t-86 70.5t-55 114.5t-19.5 156zM233 384 q0 -247 158 -247q50 0 98.5 31.5t92 87t80 132t65.5 167.5l44 355q-23 5 -44.5 7t-43.5 2q-61 0 -118.5 -20t-107.5 -56t-91 -85.5t-70.5 -109t-46 -126.5t-16.5 -138zM544 1347q4 43 23.5 77.5t48 60t64 39.5t73.5 14t71 -14t56.5 -39.5t35 -60t6.5 -77.5q-5 -42 -24 -77 t-48 -59.5t-65 -38.5t-75 -14q-38 0 -70.5 14t-55.5 38.5t-34 59.5t-6 77zM638 1347q-5 -45 17 -74t67 -29q42 0 71 29t35 74q5 46 -18 74.5t-65 28.5q-44 0 -73 -28.5t-34 -74.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1520" d="M19 228q0 73 33 139t106.5 117t193.5 82t295 36l8 62q15 123 -23.5 184.5t-129.5 61.5q-61 0 -105 -16.5t-76 -37.5t-56.5 -38t-47.5 -17q-18 0 -29.5 9t-19.5 24l-24 56q89 83 180 124t197 41q112 0 174 -49.5t84 -136.5q61 86 150 134.5t203 48.5q61 0 113 -19 t90.5 -53t60 -79.5t21.5 -97.5q0 -58 -28 -113.5t-98 -99t-190 -72t-302 -31.5q-1 -10 -1 -19.5v-21.5q0 -159 62.5 -238t172.5 -79q49 0 88 10.5t69.5 25t53.5 32.5t42.5 33t35 25t32.5 10q10 0 18 -4.5t17 -12.5l45 -57q-52 -53 -102 -92t-102 -64t-109 -37t-123 -12 q-106 0 -188 58t-122 175q-33 -63 -78.5 -107.5t-96.5 -73.5t-105.5 -41.5t-104.5 -12.5q-136 0 -210 60.5t-74 183.5zM189 252q0 -74 40 -109t107 -35q53 0 100.5 18.5t86 56.5t65 95t36.5 133l10 79q-123 -5 -208 -23t-137.5 -49t-76 -73t-23.5 -93zM809 595 q133 7 219.5 25.5t137.5 44.5t71 59t20 70q0 58 -38 93t-110 35q-61 0 -111.5 -23t-88 -65.5t-63 -103t-37.5 -135.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="930" d="M59 410q0 126 39.5 241.5t110.5 204.5t168.5 142.5t213.5 53.5q103 0 173 -36.5t122 -106.5l-58 -68q-7 -8 -15.5 -13.5t-19.5 -5.5q-14 0 -28 13t-36.5 29t-56 28.5t-85.5 12.5q-71 0 -133.5 -38.5t-109.5 -105t-74 -157.5t-27 -195q0 -63 14 -115t43 -88.5t70.5 -56.5 t96.5 -20q69 0 115.5 20.5t78 44.5t53.5 44.5t44 20.5q19 0 35 -17l45 -56q-48 -53 -92.5 -90t-88 -60t-89 -34.5t-96.5 -14.5l-25 -65q78 -17 113.5 -49.5t35.5 -79.5q0 -37 -17 -66.5t-47.5 -50t-72.5 -31.5t-91 -11q-39 0 -73 9t-62 23l25 56q7 18 26 19q6 0 12.5 -3.5 t16 -8t22.5 -8.5t33 -4q41 0 62.5 19.5t21.5 49.5q0 35 -36.5 49.5t-103.5 24.5l58 130q-76 10 -134 44.5t-97.5 89t-59.5 127.5t-20 159z" />
<glyph unicode="&#xe8;" horiz-adv-x="979" d="M59 407q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77t18 -80q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q0 -11 -0.5 -22t-0.5 -22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42 33 t35 25t33 10q19 0 34 -17l46 -57q-52 -54 -102.5 -92.5t-102 -64t-108.5 -37t-123 -11.5q-89 0 -159.5 29.5t-119.5 84.5t-76 132.5t-27 174.5zM255 575q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38t-26 36.5t-46.5 27.5t-69.5 11q-65 0 -119 -27t-96 -73.5 t-72 -109.5t-46 -136zM350 1484h159q31 0 45 -11t24 -34l112 -248h-95q-19 0 -31 6.5t-24 22.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="979" d="M59 407q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77t18 -80q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q0 -11 -0.5 -22t-0.5 -22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42 33 t35 25t33 10q19 0 34 -17l46 -57q-52 -54 -102.5 -92.5t-102 -64t-108.5 -37t-123 -11.5q-89 0 -159.5 29.5t-119.5 84.5t-76 132.5t-27 174.5zM255 575q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38t-26 36.5t-46.5 27.5t-69.5 11q-65 0 -119 -27t-96 -73.5 t-72 -109.5t-46 -136zM511 1191l166 248q15 24 32.5 34.5t47.5 10.5h164l-250 -264q-14 -15 -27.5 -22t-32.5 -7h-100z" />
<glyph unicode="&#xea;" horiz-adv-x="979" d="M59 407q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77t18 -80q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q0 -11 -0.5 -22t-0.5 -22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42 33 t35 25t33 10q19 0 34 -17l46 -57q-52 -54 -102.5 -92.5t-102 -64t-108.5 -37t-123 -11.5q-89 0 -159.5 29.5t-119.5 84.5t-76 132.5t-27 174.5zM255 575q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38t-26 36.5t-46.5 27.5t-69.5 11q-65 0 -119 -27t-96 -73.5 t-72 -109.5t-46 -136zM310 1197l241 270h156l180 -270h-113q-10 0 -20 4t-19 16l-101 135q-3 5 -6.5 9.5t-6.5 9.5q-5 -5 -9.5 -10t-8.5 -9l-133 -135q-19 -19 -43 -20h-117z" />
<glyph unicode="&#xeb;" horiz-adv-x="979" d="M59 407q0 80 17 159t49.5 150.5t79 133t106.5 106t132 70.5t155 26q79 0 136.5 -22.5t94 -57.5t54.5 -77t18 -80q0 -68 -27.5 -124.5t-101.5 -102t-203 -79.5t-331 -54q0 -11 -0.5 -22t-0.5 -22q0 -137 59.5 -209.5t180.5 -72.5q49 0 87.5 10.5t69 25t53.5 32.5t42 33 t35 25t33 10q19 0 34 -17l46 -57q-52 -54 -102.5 -92.5t-102 -64t-108.5 -37t-123 -11.5q-89 0 -159.5 29.5t-119.5 84.5t-76 132.5t-27 174.5zM255 575q158 19 253 44.5t146.5 54.5t67.5 63t16 71q0 18 -8 38t-26 36.5t-46.5 27.5t-69.5 11q-65 0 -119 -27t-96 -73.5 t-72 -109.5t-46 -136zM324 1320q0 24 9.5 45t25 37t36 25.5t44.5 9.5t45 -9.5t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5t-25.5 -35.5t-37 -24.5t-45 -9.5t-44.5 9.5t-36 24.5t-25 35.5t-9.5 44.5zM694 1320q0 24 9 45t25 37t37 25.5t45 9.5t44.5 -9.5t36.5 -25.5t25 -37t9 -45 t-9 -44.5t-25 -35.5t-37 -24.5t-44 -9.5q-24 0 -45 9.5t-37 24.5t-25 35.5t-9 44.5z" />
<glyph unicode="&#xec;" horiz-adv-x="471" d="M71 0l125 1037h179l-125 -1037h-179zM73 1484h159q31 0 45 -11t24 -34l112 -248h-95q-19 0 -31 6.5t-24 22.5z" />
<glyph unicode="&#xed;" horiz-adv-x="471" d="M71 0l125 1037h179l-125 -1037h-179zM191 1191l166 248q15 24 32.5 34.5t47.5 10.5h164l-250 -264q-14 -15 -27.5 -22t-32.5 -7h-100z" />
<glyph unicode="&#xee;" horiz-adv-x="471" d="M17 1197l241 270h156l180 -270h-113q-10 0 -19.5 4t-19.5 16l-101 135q-3 5 -6.5 9.5t-6.5 9.5q-5 -5 -9.5 -10t-8.5 -9l-133 -135q-19 -19 -43 -20h-117zM71 0l125 1037h179l-125 -1037h-179z" />
<glyph unicode="&#xef;" horiz-adv-x="471" d="M57 1320q0 24 9.5 45t25 37t36 25.5t43.5 9.5q24 0 44.5 -9.5t37 -25.5t26 -37t9.5 -45t-9.5 -44.5t-26 -35.5t-37.5 -24.5t-44 -9.5t-43.5 9.5t-36 24.5t-25 35.5t-9.5 44.5zM71 0l125 1037h179l-125 -1037h-179zM356 1320q0 24 9.5 45t24.5 37t36 25.5t45 9.5 t44.5 -9.5t36.5 -25.5t25.5 -37t9.5 -45t-9.5 -44.5t-25.5 -35.5t-37 -24.5t-44 -9.5q-24 0 -45 9.5t-36 24.5t-24.5 35.5t-9.5 44.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1079" d="M57 407q0 111 35.5 209.5t99.5 172t153 117t197 43.5q99 0 179 -46.5t129 -140.5q-2 139 -41 240t-117 172l-195 -142l-33 59q-8 13 -4.5 26t14.5 22l132 97q-42 25 -90.5 44t-105.5 35q-31 10 -37 29t6 46l27 56q90 -16 173.5 -49t154.5 -85l178 127l30 -59 q7 -12 5.5 -27t-18.5 -25l-116 -85q83 -86 131 -209.5t48 -292.5t-35.5 -307.5t-105.5 -238t-172 -154t-236 -54.5q-85 0 -155.5 29t-121.5 84t-80 132t-29 175zM232 416q0 -69 17.5 -123t49 -91.5t74 -57.5t91.5 -20q67 0 123.5 26t101.5 82t77 143t48 209q-10 44 -30 85.5 t-51 73t-74 50.5t-100 19q-77 0 -138 -30.5t-103 -84.5t-64 -126t-22 -155z" />
<glyph unicode="&#xf1;" horiz-adv-x="1085" d="M60 0l125 1038h90q61 0 62 -61l-15 -202q83 137 187 207.5t215 70.5q66 0 115.5 -26.5t80.5 -76.5t42.5 -123t0.5 -167l-78 -660h-182l78 660q14 123 -15.5 182t-109.5 59q-49 0 -99.5 -26.5t-97.5 -75.5t-87.5 -118.5t-69.5 -154.5l-59 -526h-183zM334 1231 q5 49 22 89.5t42.5 69.5t58 45t69.5 16q34 0 60.5 -15t50 -32.5t44.5 -32t43 -14.5q69 0 81 88h102q-5 -48 -21.5 -88.5t-42 -69.5t-57.5 -44.5t-70 -15.5q-33 0 -60 14.5t-50.5 32t-44.5 32.5t-43 15q-69 0 -80 -90h-104z" />
<glyph unicode="&#xf2;" horiz-adv-x="1062" d="M60 417q0 131 41 246.5t113 202t168.5 137t208.5 50.5q85 0 155.5 -29.5t122 -84.5t80 -135t28.5 -181q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5zM244 418q0 -143 56 -216.5t164 -73.5q75 0 135.5 41 t104.5 109t67.5 157t23.5 187q0 143 -57 215t-164 72q-76 0 -136.5 -40.5t-103.5 -108t-66.5 -156.5t-23.5 -186zM381 1484h159q31 0 45 -11t24 -34l112 -248h-95q-19 0 -31 6.5t-24 22.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1062" d="M60 417q0 131 41 246.5t113 202t168.5 137t208.5 50.5q85 0 155.5 -29.5t122 -84.5t80 -135t28.5 -181q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5zM244 418q0 -143 56 -216.5t164 -73.5q75 0 135.5 41 t104.5 109t67.5 157t23.5 187q0 143 -57 215t-164 72q-76 0 -136.5 -40.5t-103.5 -108t-66.5 -156.5t-23.5 -186zM498 1191l166 248q15 24 32.5 34.5t47.5 10.5h164l-250 -264q-14 -15 -27.5 -22t-32.5 -7h-100z" />
<glyph unicode="&#xf4;" horiz-adv-x="1062" d="M60 417q0 131 41 246.5t113 202t168.5 137t208.5 50.5q85 0 155.5 -29.5t122 -84.5t80 -135t28.5 -181q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5zM244 418q0 -143 56 -216.5t164 -73.5q75 0 135.5 41 t104.5 109t67.5 157t23.5 187q0 143 -57 215t-164 72q-76 0 -136.5 -40.5t-103.5 -108t-66.5 -156.5t-23.5 -186zM324 1197l240 270h156l180 -270h-113q-10 0 -19.5 4t-18.5 16l-102 135q-3 5 -6.5 9.5t-6.5 9.5q-5 -5 -9 -10t-9 -9l-133 -135q-19 -19 -43 -20h-116z" />
<glyph unicode="&#xf5;" horiz-adv-x="1062" d="M60 417q0 131 41 246.5t113 202t168.5 137t208.5 50.5q85 0 155.5 -29.5t122 -84.5t80 -135t28.5 -181q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5zM244 418q0 -143 56 -216.5t164 -73.5q75 0 135.5 41 t104.5 109t67.5 157t23.5 187q0 143 -57 215t-164 72q-76 0 -136.5 -40.5t-103.5 -108t-66.5 -156.5t-23.5 -186zM349 1231q5 49 22 89.5t42.5 69.5t58 45t69.5 16q34 0 60.5 -15t50 -32.5t44.5 -32t43 -14.5q69 0 81 88h102q-5 -48 -21.5 -88.5t-42 -69.5t-57.5 -44.5 t-70 -15.5q-33 0 -60 14.5t-50.5 32t-44.5 32.5t-43 15q-69 0 -80 -90h-104z" />
<glyph unicode="&#xf6;" horiz-adv-x="1062" d="M60 417q0 131 41 246.5t113 202t168.5 137t208.5 50.5q85 0 155.5 -29.5t122 -84.5t80 -135t28.5 -181q0 -130 -41 -246t-112 -203t-168.5 -137.5t-209.5 -50.5q-85 0 -155.5 29t-121.5 84.5t-80 135t-29 182.5zM244 418q0 -143 56 -216.5t164 -73.5q75 0 135.5 41 t104.5 109t67.5 157t23.5 187q0 143 -57 215t-164 72q-76 0 -136.5 -40.5t-103.5 -108t-66.5 -156.5t-23.5 -186zM342 1320q0 24 9.5 45t25 37t36 25.5t44.5 9.5t45 -9.5t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5t-25.5 -35.5t-37 -24.5t-45 -9.5t-44.5 9.5t-36 24.5t-25 35.5 t-9.5 44.5zM712 1320q0 24 9 45t25 37t37 25.5t45 9.5t44.5 -9.5t36.5 -25.5t25 -37t9 -45t-9 -44.5t-25 -35.5t-37 -24.5t-44 -9.5q-24 0 -45 9.5t-37 24.5t-25 35.5t-9 44.5z" />
<glyph unicode="&#xf7;" d="M102 621l17 133h981l-17 -133h-981zM429 307q0 29 11.5 54.5t30 45t44.5 31t54 11.5q25 0 45.5 -9.5t35.5 -25t23 -36t8 -43.5q0 -30 -12 -55t-31.5 -43.5t-45 -29t-53.5 -10.5q-49 0 -79.5 31.5t-30.5 78.5zM519 1036q0 29 11.5 54.5t30.5 45t44.5 31t53.5 11.5 q25 0 45.5 -9.5t35.5 -25t23 -36t8 -43.5q0 -30 -11.5 -55t-31 -43.5t-46 -29t-53.5 -10.5q-49 0 -79.5 31.5t-30.5 78.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1060" d="M9 -58l136 185q-41 54 -63 127t-22 163q0 131 41 246.5t112.5 202.5t168 137.5t209.5 50.5q127 0 221 -65l34 46q20 29 36.5 40t49.5 11h92l-130 -177q39 -54 61 -125.5t22 -160.5q0 -130 -41 -246t-112.5 -203t-168.5 -137.5t-209 -50.5q-127 0 -219 62l-37 -50 q-23 -31 -52.5 -43.5t-59.5 -12.5h-69zM232 418q0 -91 27 -159l453 617q-57 46 -138 46q-76 0 -138 -39t-107.5 -107t-71 -160.5t-25.5 -197.5zM327 163q57 -46 136 -46q75 0 138 39.5t108.5 107.5t70.5 160t25 198q0 46 -6.5 84t-19.5 71z" />
<glyph unicode="&#xf9;" horiz-adv-x="1084" d="M96.5 211q-11.5 73 0.5 167l78 659h181l-77 -659q-14 -122 15 -181.5t109 -59.5q47 0 97.5 25.5t96.5 73t86.5 114.5t69.5 149l64 538h181l-125 -1037h-90q-67 0 -66 66l15 189q-82 -133 -185 -201t-211 -68q-66 0 -116 26t-81 76t-42.5 123zM373 1484h159q31 0 45 -11 t24 -34l112 -248h-95q-19 0 -31 6.5t-24 22.5z" />
<glyph unicode="&#xfa;" horiz-adv-x="1084" d="M96.5 211q-11.5 73 0.5 167l78 659h181l-77 -659q-14 -122 15 -181.5t109 -59.5q47 0 97.5 25.5t96.5 73t86.5 114.5t69.5 149l64 538h181l-125 -1037h-90q-67 0 -66 66l15 189q-82 -133 -185 -201t-211 -68q-66 0 -116 26t-81 76t-42.5 123zM533 1191l166 248 q15 24 32.5 34.5t47.5 10.5h164l-250 -264q-14 -15 -27.5 -22t-32.5 -7h-100z" />
<glyph unicode="&#xfb;" horiz-adv-x="1084" d="M96.5 211q-11.5 73 0.5 167l78 659h181l-77 -659q-14 -122 15 -181.5t109 -59.5q47 0 97.5 25.5t96.5 73t86.5 114.5t69.5 149l64 538h181l-125 -1037h-90q-67 0 -66 66l15 189q-82 -133 -185 -201t-211 -68q-66 0 -116 26t-81 76t-42.5 123zM327 1197l240 270h156 l180 -270h-112q-10 0 -20 4t-19 16l-102 135q-3 5 -6.5 9.5t-6.5 9.5q-5 -5 -9 -10t-8 -9l-134 -135q-19 -19 -43 -20h-116z" />
<glyph unicode="&#xfc;" horiz-adv-x="1084" d="M96.5 211q-11.5 73 0.5 167l78 659h181l-77 -659q-14 -122 15 -181.5t109 -59.5q47 0 97.5 25.5t96.5 73t86.5 114.5t69.5 149l64 538h181l-125 -1037h-90q-67 0 -66 66l15 189q-82 -133 -185 -201t-211 -68q-66 0 -116 26t-81 76t-42.5 123zM323 1320q0 24 9.5 45t25 37 t36 25.5t44.5 9.5t45 -9.5t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5t-25.5 -35.5t-37 -24.5t-45 -9.5t-44.5 9.5t-36 24.5t-25 35.5t-9.5 44.5zM693 1320q0 24 9 45t25 37t37 25.5t45 9.5t44.5 -9.5t36.5 -25.5t25 -37t9 -45t-9 -44.5t-25 -35.5t-37 -24.5t-44 -9.5 q-24 0 -45 9.5t-37 24.5t-25 35.5t-9 44.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="982" d="M74 1037h150q23 0 33 -10.5t15 -27.5l167 -653q5 -22 8.5 -43t5.5 -43l18 44t19 43l328 654q8 16 23 26t30 10h145l-701 -1346q-11 -22 -26 -32t-37 -10h-134l225 412zM474 1191l166 248q15 24 32.5 34.5t47.5 10.5h164l-250 -264q-14 -15 -27.5 -22t-32.5 -7h-100z" />
<glyph unicode="&#xfe;" horiz-adv-x="1082" d="M17 -351l227 1859h179l-90 -732q39 62 83.5 113.5t93.5 88t102.5 56t108.5 19.5q133 0 206 -96.5t73 -281.5q0 -84 -17 -168t-49.5 -160.5t-78 -142.5t-101.5 -114.5t-121.5 -76t-136.5 -27.5q-77 0 -140 30t-106 85l-48 -387q-3 -27 -23 -46t-51 -19h-111zM265 228 q39 -55 93.5 -78t110.5 -23q53 0 100 22.5t86 61t70 90t52 110.5t32 122t11 124q0 122 -44 185.5t-124 63.5q-49 0 -99 -30.5t-96.5 -85t-87.5 -129t-71 -162.5z" />
<glyph unicode="&#xff;" horiz-adv-x="982" d="M74 1037h150q23 0 33 -10.5t15 -27.5l167 -653q5 -22 8.5 -43t5.5 -43l18 44t19 43l328 654q8 16 23 26t30 10h145l-701 -1346q-11 -22 -26 -32t-37 -10h-134l225 412zM285 1320q0 24 9.5 45t25 37t36 25.5t44.5 9.5t45 -9.5t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5 t-25.5 -35.5t-37 -24.5t-45 -9.5t-44.5 9.5t-36 24.5t-25 35.5t-9.5 44.5zM655 1320q0 24 9 45t25 37t37 25.5t45 9.5t44.5 -9.5t36.5 -25.5t25 -37t9 -45t-9 -44.5t-25 -35.5t-37 -24.5t-44 -9.5q-24 0 -45 9.5t-37 24.5t-25 35.5t-9 44.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2067" d="M102 617q0 189 53.5 348t147.5 274.5t222.5 180t278.5 64.5q155 0 265 -76t170 -206l33 265h810l-19 -156h-641l-60 -496h516l-17 -150h-518l-61 -509h639l-19 -156h-810l28 232q-92 -118 -218.5 -182t-279.5 -64q-121 0 -217.5 47t-163.5 130.5t-103 199.5t-36 254z M299 623q0 -110 24.5 -198.5t70.5 -152t113 -98t152 -34.5q112 0 204.5 50.5t160 142.5t104 221.5t36.5 289.5q0 109 -24 198.5t-70 152.5t-112.5 98t-151.5 35q-112 0 -205 -50.5t-160 -143.5t-104.5 -223t-37.5 -288z" />
<glyph unicode="&#x153;" horiz-adv-x="1647" d="M58 358q0 168 43.5 297.5t118.5 218.5t172.5 134.5t207.5 45.5t183.5 -52.5t109.5 -145.5q65 93 159 145.5t217 52.5q67 0 120.5 -18t91 -50.5t58 -78t20.5 -99.5q0 -42 -12 -81t-40.5 -73t-76 -64t-119 -55t-168.5 -44t-227 -32v-13q0 -159 62 -238t174 -79 q49 0 87.5 10.5t68.5 25t53 32.5t42.5 33t35.5 25t32 10q19 0 35 -17l45 -57q-51 -53 -101 -92t-102.5 -64t-109 -37t-122.5 -12q-106 0 -188.5 59t-121.5 176q-71 -112 -176.5 -173.5t-238.5 -61.5q-83 0 -145.5 30t-104 82t-62.5 119.5t-21 140.5zM237 369q0 -48 10 -92.5 t33.5 -78t61.5 -54t93 -20.5q80 0 140.5 36t103 100.5t66.5 154.5t31 198q4 69 -5 124.5t-32.5 94.5t-62.5 60.5t-93 21.5q-80 0 -144 -38.5t-109 -110.5t-69 -172t-24 -224zM926 572q148 18 241.5 44t146.5 55.5t72.5 63t19.5 67.5q0 25 -10 46.5t-30 38.5t-51 27t-74 10 q-60 0 -112.5 -24t-93.5 -69.5t-69 -110.5t-40 -148z" />
<glyph unicode="&#x178;" horiz-adv-x="1191" d="M110 1467h169q26 0 40 -12t21 -33l243 -587q19 -58 30 -111q12 27 27 54t33 57l389 587q11 17 28 31t42 14h158l-602 -880l-73 -587h-191l73 585zM410 1703q0 25 9 45.5t25 37t36.5 26t43.5 9.5q24 0 44.5 -9.5t37 -26t25.5 -37t9 -45.5q0 -24 -9 -44.5t-25.5 -35.5 t-37.5 -24t-44 -9t-43.5 9t-36.5 24t-25 35.5t-9 44.5zM799 1703q0 25 9.5 45.5t24.5 37t36 26t45 9.5t44.5 -9.5t36.5 -26t25.5 -37t9.5 -45.5q0 -24 -9.5 -44.5t-25.5 -35.5t-37 -24t-44 -9q-24 0 -45 9t-36 24t-24.5 35.5t-9.5 44.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="753" d="M223 1197l241 270h156l180 -270h-113q-10 0 -20 4t-19 16l-101 135q-3 5 -6.5 9.5t-6.5 9.5q-5 -5 -9.5 -10t-8.5 -9l-133 -135q-19 -19 -43 -20h-117z" />
<glyph unicode="&#x2dc;" horiz-adv-x="753" d="M244 1231q5 49 22 89.5t42.5 69.5t58 45t69.5 16q34 0 60.5 -15t50 -32.5t44.5 -32t43 -14.5q69 0 81 88h102q-5 -48 -21.5 -88.5t-42 -69.5t-57.5 -44.5t-70 -15.5q-33 0 -60 14.5t-50.5 32t-44.5 32.5t-43 15q-69 0 -80 -90h-104z" />
<glyph unicode="&#x2000;" horiz-adv-x="959" />
<glyph unicode="&#x2001;" horiz-adv-x="1919" />
<glyph unicode="&#x2002;" horiz-adv-x="959" />
<glyph unicode="&#x2003;" horiz-adv-x="1919" />
<glyph unicode="&#x2004;" horiz-adv-x="639" />
<glyph unicode="&#x2005;" horiz-adv-x="479" />
<glyph unicode="&#x2006;" horiz-adv-x="319" />
<glyph unicode="&#x2007;" horiz-adv-x="319" />
<glyph unicode="&#x2008;" horiz-adv-x="239" />
<glyph unicode="&#x2009;" horiz-adv-x="383" />
<glyph unicode="&#x200a;" horiz-adv-x="106" />
<glyph unicode="&#x2010;" horiz-adv-x="718" d="M115 539l18 149h468l-18 -149h-468z" />
<glyph unicode="&#x2011;" horiz-adv-x="718" d="M115 539l18 149h468l-18 -149h-468z" />
<glyph unicode="&#x2012;" horiz-adv-x="718" d="M115 539l18 149h468l-18 -149h-468z" />
<glyph unicode="&#x2013;" d="M206 540l15 128h754l-16 -128h-753z" />
<glyph unicode="&#x2014;" horiz-adv-x="1506" d="M115 540l15 128h1255l-16 -128h-1254z" />
<glyph unicode="&#x2018;" horiz-adv-x="417" d="M172.5 1181q-1.5 70 22.5 137t70.5 129t112.5 116l48 -32q11 -8 11 -23q0 -8 -4 -15t-15 -19q-23 -27 -46 -63.5t-36 -80t-12.5 -93t23.5 -101.5q7 -16 -0.5 -30t-28.5 -23l-112 -43q-32 71 -33.5 141z" />
<glyph unicode="&#x2019;" horiz-adv-x="421" d="M153 1071q0 8 4.5 15t15.5 20q23 26 45.5 62t36 80t13 93.5t-23.5 101.5q-7 16 0.5 30t27.5 23l113 43q32 -71 33.5 -141t-22.5 -137t-71 -129t-112 -116l-48 32q-11 8 -12 23z" />
<glyph unicode="&#x201a;" horiz-adv-x="411" d="M-26 -226q0 8 4.5 15t15.5 20q23 26 45.5 62t36 80t13 93.5t-23.5 101.5q-7 16 0.5 30t27.5 23l113 43q32 -71 33.5 -141t-22.5 -137t-71 -129t-112 -116l-48 32q-11 8 -12 23z" />
<glyph unicode="&#x201c;" horiz-adv-x="704" d="M172.5 1181q-1.5 70 22.5 137t70.5 129t112.5 116l48 -32q11 -8 11 -23q0 -8 -4 -15t-15 -19q-23 -27 -46 -63.5t-36 -80t-12.5 -93t23.5 -101.5q7 -16 -0.5 -30t-28.5 -23l-112 -43q-32 71 -33.5 141zM458.5 1181q-1.5 70 22.5 137t70.5 129t112.5 116l48 -32 q11 -8 11 -23q0 -8 -4 -15t-15 -19q-23 -27 -46 -63.5t-36 -80t-12.5 -93t23.5 -101.5q7 -16 -0.5 -30t-28.5 -23l-112 -43q-32 71 -33.5 141z" />
<glyph unicode="&#x201d;" horiz-adv-x="707" d="M153 1071q0 8 4.5 15t15.5 20q23 26 45.5 62t36 80t13 93.5t-23.5 101.5q-7 16 0.5 30t27.5 23l113 43q32 -71 33.5 -141t-22.5 -137t-71 -129t-112 -116l-48 32q-11 8 -12 23zM439 1071q0 8 4.5 15t15.5 20q23 26 45.5 62t36 80t13 93.5t-23.5 101.5q-7 16 0.5 30 t27.5 23l113 43q32 -71 33.5 -141t-22.5 -137t-71 -129t-112 -116l-48 32q-11 8 -12 23z" />
<glyph unicode="&#x201e;" horiz-adv-x="697" d="M-26 -226q0 8 4.5 15t15.5 20q23 26 45.5 62t36 80t13 93.5t-23.5 101.5q-7 16 0.5 30t27.5 23l113 43q32 -71 33.5 -141t-22.5 -137t-71 -129t-112 -116l-48 32q-11 8 -12 23zM260 -226q0 8 4.5 15t15.5 20q23 26 45.5 62t36 80t13 93.5t-23.5 101.5q-7 16 0.5 30 t27.5 23l113 43q32 -71 33.5 -141t-22.5 -137t-71 -129t-112 -116l-48 32q-11 8 -12 23z" />
<glyph unicode="&#x2022;" d="M211 609q0 79 30 148.5t81.5 121.5t120.5 82t147 30q79 0 148.5 -30t121 -82t82 -121.5t30.5 -148.5t-30.5 -148t-82 -120t-121 -81.5t-148.5 -30.5q-78 0 -147 30.5t-120.5 81.5t-81.5 120t-30 148z" />
<glyph unicode="&#x2026;" horiz-adv-x="1532" d="M53 113q0 27 10 50t26.5 40.5t40.5 28t50 10.5q27 0 50.5 -10.5t41 -28t27.5 -41t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-41 -27t-50.5 -9.5t-50.5 9.5t-40 27t-26.5 40.5t-10 51zM580 113q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41t10.5 -49.5 q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5t-40.5 27t-26.5 40.5t-9.5 51zM1106 113q0 27 9.5 50t27 40.5t40.5 28t51 10.5q27 0 50 -10.5t40 -28t27.5 -41t10.5 -49.5q0 -28 -10.5 -51t-27.5 -40.5t-40 -27t-50 -9.5q-28 0 -51 9.5t-40.5 27t-27 40.5 t-9.5 51z" />
<glyph unicode="&#x202f;" horiz-adv-x="383" />
<glyph unicode="&#x2039;" horiz-adv-x="530" d="M81 530l3 23l286 397l53 -27q22 -11 24 -31.5t-16 -43.5l-179 -269q-16 -24 -33 -37q11 -11 23 -37l114 -269q10 -24 3.5 -44.5t-29.5 -31.5l-62 -28z" />
<glyph unicode="&#x203a;" horiz-adv-x="528" d="M63.5 191.5q-1.5 20.5 14.5 44.5l181 269q15 25 31 37q-13 12 -23 37l-114 269q-10 24 -2.5 43.5t30.5 31.5l60 27l186 -397l-2 -23l-284 -398l-54 28q-22 11 -23.5 31.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="479" />
<glyph unicode="&#x20ac;" d="M16 567l13 102h138q3 76 15 148h-134l11 102h145q34 130 94.5 234t143 177t183.5 112t216 39q136 0 227 -51t152 -140l-69 -67q-8 -9 -17 -15t-23 -6q-16 0 -33 19.5t-47 43t-77.5 43t-125.5 19.5q-79 0 -148 -26.5t-126 -79t-100 -128.5t-70 -174h573l-5 -57 q-3 -17 -18 -31t-40 -14h-532q-6 -36 -9.5 -72.5t-5.5 -75.5h486l-8 -56q-2 -17 -17.5 -31.5t-39.5 -14.5h-422q7 -209 92.5 -319t234.5 -110q84 0 140.5 23t94.5 51t61 51t42 23q9 0 14 -2t11 -11l67 -69q-85 -103 -198 -161t-259 -58q-116 0 -204.5 42t-149.5 118 t-92.5 183.5t-34.5 238.5h-149z" />
<glyph unicode="&#x2122;" horiz-adv-x="1418" d="M169 1362l14 103h459l-13 -103h-168l-62 -501h-123l61 501h-168zM655 861l74 604h105q16 0 23 -3.5t15 -16.5l117 -315q8 -24 13 -52q5 14 12 26.5t15 25.5l194 315q11 13 19 16.5t25 3.5h103l-73 -604h-108l46 379l17 75l-211 -347q-17 -28 -47 -28h-17q-14 0 -24.5 7 t-14.5 21l-126 343l-2 -71l-46 -379h-109z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1039" d="M0 0v1039h1039v-1039h-1039z" />
<hkern u1="&#x20;" u2="&#x178;" k="79" />
<hkern u1="&#x20;" u2="&#xff;" k="49" />
<hkern u1="&#x20;" u2="&#xfd;" k="49" />
<hkern u1="&#x20;" u2="&#xe6;" k="33" />
<hkern u1="&#x20;" u2="&#xdd;" k="79" />
<hkern u1="&#x20;" u2="&#xc6;" k="66" />
<hkern u1="&#x20;" u2="&#xc5;" k="57" />
<hkern u1="&#x20;" u2="&#xc4;" k="57" />
<hkern u1="&#x20;" u2="&#xc3;" k="57" />
<hkern u1="&#x20;" u2="&#xc2;" k="57" />
<hkern u1="&#x20;" u2="&#xc1;" k="57" />
<hkern u1="&#x20;" u2="&#xc0;" k="57" />
<hkern u1="&#x20;" u2="y" k="49" />
<hkern u1="&#x20;" u2="w" k="19" />
<hkern u1="&#x20;" u2="v" k="49" />
<hkern u1="&#x20;" u2="t" k="15" />
<hkern u1="&#x20;" u2="Y" k="79" />
<hkern u1="&#x20;" u2="W" k="28" />
<hkern u1="&#x20;" u2="V" k="72" />
<hkern u1="&#x20;" u2="T" k="60" />
<hkern u1="&#x20;" u2="J" k="49" />
<hkern u1="&#x20;" u2="A" k="57" />
<hkern u1="&#x22;" u2="&#xf0;" k="53" />
<hkern u1="&#x22;" u2="&#xef;" k="-26" />
<hkern u1="&#x22;" u2="&#xee;" k="-36" />
<hkern u1="&#x22;" u2="&#xec;" k="-30" />
<hkern u1="&#x22;" u2="&#x2f;" k="104" />
<hkern u1="&#x22;" u2="&#x2c;" k="199" />
<hkern u1="&#x22;" u2="&#x26;" k="46" />
<hkern u1="&#x26;" u2="&#x201d;" k="70" />
<hkern u1="&#x26;" u2="&#x2019;" k="70" />
<hkern u1="&#x26;" u2="&#x178;" k="104" />
<hkern u1="&#x26;" u2="&#xff;" k="18" />
<hkern u1="&#x26;" u2="&#xfd;" k="18" />
<hkern u1="&#x26;" u2="&#xdd;" k="104" />
<hkern u1="&#x26;" u2="&#xc6;" k="-42" />
<hkern u1="&#x26;" u2="&#xc5;" k="-44" />
<hkern u1="&#x26;" u2="&#xc4;" k="-44" />
<hkern u1="&#x26;" u2="&#xc3;" k="-44" />
<hkern u1="&#x26;" u2="&#xc2;" k="-44" />
<hkern u1="&#x26;" u2="&#xc1;" k="-44" />
<hkern u1="&#x26;" u2="&#xc0;" k="-44" />
<hkern u1="&#x26;" u2="y" k="18" />
<hkern u1="&#x26;" u2="x" k="-41" />
<hkern u1="&#x26;" u2="v" k="18" />
<hkern u1="&#x26;" u2="g" k="-22" />
<hkern u1="&#x26;" u2="Z" k="-19" />
<hkern u1="&#x26;" u2="Y" k="104" />
<hkern u1="&#x26;" u2="X" k="-48" />
<hkern u1="&#x26;" u2="W" k="54" />
<hkern u1="&#x26;" u2="V" k="82" />
<hkern u1="&#x26;" u2="T" k="89" />
<hkern u1="&#x26;" u2="J" k="-9" />
<hkern u1="&#x26;" u2="A" k="-44" />
<hkern u1="&#x26;" u2="&#x27;" k="66" />
<hkern u1="&#x26;" u2="&#x22;" k="66" />
<hkern u1="&#x27;" u2="&#xf0;" k="53" />
<hkern u1="&#x27;" u2="&#xef;" k="-26" />
<hkern u1="&#x27;" u2="&#xee;" k="-36" />
<hkern u1="&#x27;" u2="&#xec;" k="-30" />
<hkern u1="&#x27;" u2="&#x2f;" k="104" />
<hkern u1="&#x27;" u2="&#x2c;" k="199" />
<hkern u1="&#x27;" u2="&#x26;" k="46" />
<hkern u1="&#x28;" u2="&#x153;" k="56" />
<hkern u1="&#x28;" u2="&#x152;" k="40" />
<hkern u1="&#x28;" u2="&#xff;" k="19" />
<hkern u1="&#x28;" u2="&#xfd;" k="19" />
<hkern u1="&#x28;" u2="&#xfc;" k="41" />
<hkern u1="&#x28;" u2="&#xfb;" k="41" />
<hkern u1="&#x28;" u2="&#xfa;" k="41" />
<hkern u1="&#x28;" u2="&#xf9;" k="41" />
<hkern u1="&#x28;" u2="&#xf8;" k="56" />
<hkern u1="&#x28;" u2="&#xf6;" k="56" />
<hkern u1="&#x28;" u2="&#xf5;" k="56" />
<hkern u1="&#x28;" u2="&#xf4;" k="56" />
<hkern u1="&#x28;" u2="&#xf3;" k="56" />
<hkern u1="&#x28;" u2="&#xf2;" k="56" />
<hkern u1="&#x28;" u2="&#xf1;" k="15" />
<hkern u1="&#x28;" u2="&#xf0;" k="49" />
<hkern u1="&#x28;" u2="&#xef;" k="-27" />
<hkern u1="&#x28;" u2="&#xec;" k="-49" />
<hkern u1="&#x28;" u2="&#xeb;" k="56" />
<hkern u1="&#x28;" u2="&#xea;" k="56" />
<hkern u1="&#x28;" u2="&#xe9;" k="56" />
<hkern u1="&#x28;" u2="&#xe8;" k="56" />
<hkern u1="&#x28;" u2="&#xe7;" k="56" />
<hkern u1="&#x28;" u2="&#xe6;" k="52" />
<hkern u1="&#x28;" u2="&#xe5;" k="52" />
<hkern u1="&#x28;" u2="&#xe4;" k="52" />
<hkern u1="&#x28;" u2="&#xe3;" k="52" />
<hkern u1="&#x28;" u2="&#xe2;" k="52" />
<hkern u1="&#x28;" u2="&#xe1;" k="52" />
<hkern u1="&#x28;" u2="&#xe0;" k="52" />
<hkern u1="&#x28;" u2="&#xd8;" k="40" />
<hkern u1="&#x28;" u2="&#xd6;" k="40" />
<hkern u1="&#x28;" u2="&#xd5;" k="40" />
<hkern u1="&#x28;" u2="&#xd4;" k="40" />
<hkern u1="&#x28;" u2="&#xd3;" k="40" />
<hkern u1="&#x28;" u2="&#xd2;" k="40" />
<hkern u1="&#x28;" u2="&#xc7;" k="40" />
<hkern u1="&#x28;" u2="y" k="19" />
<hkern u1="&#x28;" u2="w" k="41" />
<hkern u1="&#x28;" u2="v" k="46" />
<hkern u1="&#x28;" u2="u" k="41" />
<hkern u1="&#x28;" u2="t" k="23" />
<hkern u1="&#x28;" u2="s" k="15" />
<hkern u1="&#x28;" u2="r" k="15" />
<hkern u1="&#x28;" u2="q" k="52" />
<hkern u1="&#x28;" u2="p" k="15" />
<hkern u1="&#x28;" u2="o" k="56" />
<hkern u1="&#x28;" u2="n" k="15" />
<hkern u1="&#x28;" u2="m" k="15" />
<hkern u1="&#x28;" u2="j" k="-27" />
<hkern u1="&#x28;" u2="e" k="56" />
<hkern u1="&#x28;" u2="d" k="53" />
<hkern u1="&#x28;" u2="c" k="56" />
<hkern u1="&#x28;" u2="a" k="52" />
<hkern u1="&#x28;" u2="Q" k="40" />
<hkern u1="&#x28;" u2="O" k="40" />
<hkern u1="&#x28;" u2="G" k="40" />
<hkern u1="&#x28;" u2="C" k="40" />
<hkern u1="&#x2a;" u2="&#x153;" k="65" />
<hkern u1="&#x2a;" u2="&#xf8;" k="65" />
<hkern u1="&#x2a;" u2="&#xf6;" k="65" />
<hkern u1="&#x2a;" u2="&#xf5;" k="65" />
<hkern u1="&#x2a;" u2="&#xf4;" k="65" />
<hkern u1="&#x2a;" u2="&#xf3;" k="65" />
<hkern u1="&#x2a;" u2="&#xf2;" k="65" />
<hkern u1="&#x2a;" u2="&#xf1;" k="11" />
<hkern u1="&#x2a;" u2="&#xf0;" k="63" />
<hkern u1="&#x2a;" u2="&#xef;" k="-39" />
<hkern u1="&#x2a;" u2="&#xee;" k="-9" />
<hkern u1="&#x2a;" u2="&#xeb;" k="65" />
<hkern u1="&#x2a;" u2="&#xea;" k="65" />
<hkern u1="&#x2a;" u2="&#xe9;" k="65" />
<hkern u1="&#x2a;" u2="&#xe8;" k="65" />
<hkern u1="&#x2a;" u2="&#xe7;" k="65" />
<hkern u1="&#x2a;" u2="&#xe6;" k="83" />
<hkern u1="&#x2a;" u2="&#xe5;" k="83" />
<hkern u1="&#x2a;" u2="&#xe4;" k="83" />
<hkern u1="&#x2a;" u2="&#xe3;" k="83" />
<hkern u1="&#x2a;" u2="&#xe2;" k="83" />
<hkern u1="&#x2a;" u2="&#xe1;" k="83" />
<hkern u1="&#x2a;" u2="&#xe0;" k="83" />
<hkern u1="&#x2a;" u2="&#xc6;" k="183" />
<hkern u1="&#x2a;" u2="&#xc5;" k="126" />
<hkern u1="&#x2a;" u2="&#xc4;" k="126" />
<hkern u1="&#x2a;" u2="&#xc3;" k="126" />
<hkern u1="&#x2a;" u2="&#xc2;" k="126" />
<hkern u1="&#x2a;" u2="&#xc1;" k="126" />
<hkern u1="&#x2a;" u2="&#xc0;" k="126" />
<hkern u1="&#x2a;" u2="s" k="46" />
<hkern u1="&#x2a;" u2="r" k="11" />
<hkern u1="&#x2a;" u2="q" k="83" />
<hkern u1="&#x2a;" u2="p" k="11" />
<hkern u1="&#x2a;" u2="o" k="65" />
<hkern u1="&#x2a;" u2="n" k="11" />
<hkern u1="&#x2a;" u2="m" k="11" />
<hkern u1="&#x2a;" u2="g" k="53" />
<hkern u1="&#x2a;" u2="e" k="65" />
<hkern u1="&#x2a;" u2="d" k="75" />
<hkern u1="&#x2a;" u2="c" k="65" />
<hkern u1="&#x2a;" u2="a" k="83" />
<hkern u1="&#x2a;" u2="J" k="150" />
<hkern u1="&#x2a;" u2="A" k="126" />
<hkern u1="&#x2c;" g2="uniFB04" k="16" />
<hkern u1="&#x2c;" g2="uniFB03" k="16" />
<hkern u1="&#x2c;" g2="uniFB02" k="16" />
<hkern u1="&#x2c;" g2="uniFB01" k="16" />
<hkern u1="&#x2c;" u2="&#x201d;" k="211" />
<hkern u1="&#x2c;" u2="&#x201c;" k="209" />
<hkern u1="&#x2c;" u2="&#x2019;" k="211" />
<hkern u1="&#x2c;" u2="&#x2018;" k="209" />
<hkern u1="&#x2c;" u2="&#x178;" k="162" />
<hkern u1="&#x2c;" u2="&#x152;" k="78" />
<hkern u1="&#x2c;" u2="&#xff;" k="81" />
<hkern u1="&#x2c;" u2="&#xfd;" k="81" />
<hkern u1="&#x2c;" u2="&#xdf;" k="16" />
<hkern u1="&#x2c;" u2="&#xdd;" k="162" />
<hkern u1="&#x2c;" u2="&#xdc;" k="68" />
<hkern u1="&#x2c;" u2="&#xdb;" k="68" />
<hkern u1="&#x2c;" u2="&#xda;" k="68" />
<hkern u1="&#x2c;" u2="&#xd9;" k="68" />
<hkern u1="&#x2c;" u2="&#xd8;" k="78" />
<hkern u1="&#x2c;" u2="&#xd6;" k="78" />
<hkern u1="&#x2c;" u2="&#xd5;" k="78" />
<hkern u1="&#x2c;" u2="&#xd4;" k="78" />
<hkern u1="&#x2c;" u2="&#xd3;" k="78" />
<hkern u1="&#x2c;" u2="&#xd2;" k="78" />
<hkern u1="&#x2c;" u2="&#xc7;" k="78" />
<hkern u1="&#x2c;" u2="y" k="81" />
<hkern u1="&#x2c;" u2="w" k="59" />
<hkern u1="&#x2c;" u2="v" k="86" />
<hkern u1="&#x2c;" u2="t" k="20" />
<hkern u1="&#x2c;" u2="f" k="16" />
<hkern u1="&#x2c;" u2="Y" k="162" />
<hkern u1="&#x2c;" u2="W" k="119" />
<hkern u1="&#x2c;" u2="V" k="159" />
<hkern u1="&#x2c;" u2="U" k="68" />
<hkern u1="&#x2c;" u2="T" k="141" />
<hkern u1="&#x2c;" u2="Q" k="78" />
<hkern u1="&#x2c;" u2="O" k="78" />
<hkern u1="&#x2c;" u2="G" k="78" />
<hkern u1="&#x2c;" u2="C" k="78" />
<hkern u1="&#x2c;" u2="&#x27;" k="199" />
<hkern u1="&#x2c;" u2="&#x22;" k="199" />
<hkern u1="&#x2e;" g2="uniFB04" k="16" />
<hkern u1="&#x2e;" g2="uniFB03" k="16" />
<hkern u1="&#x2e;" g2="uniFB02" k="16" />
<hkern u1="&#x2e;" g2="uniFB01" k="16" />
<hkern u1="&#x2e;" u2="&#x201d;" k="211" />
<hkern u1="&#x2e;" u2="&#x201c;" k="209" />
<hkern u1="&#x2e;" u2="&#x2019;" k="211" />
<hkern u1="&#x2e;" u2="&#x2018;" k="209" />
<hkern u1="&#x2e;" u2="&#x2014;" k="105" />
<hkern u1="&#x2e;" u2="&#x178;" k="166" />
<hkern u1="&#x2e;" u2="&#x152;" k="75" />
<hkern u1="&#x2e;" u2="&#xff;" k="88" />
<hkern u1="&#x2e;" u2="&#xfd;" k="88" />
<hkern u1="&#x2e;" u2="&#xdf;" k="16" />
<hkern u1="&#x2e;" u2="&#xdd;" k="166" />
<hkern u1="&#x2e;" u2="&#xdc;" k="63" />
<hkern u1="&#x2e;" u2="&#xdb;" k="63" />
<hkern u1="&#x2e;" u2="&#xda;" k="63" />
<hkern u1="&#x2e;" u2="&#xd9;" k="63" />
<hkern u1="&#x2e;" u2="&#xd8;" k="75" />
<hkern u1="&#x2e;" u2="&#xd6;" k="75" />
<hkern u1="&#x2e;" u2="&#xd5;" k="75" />
<hkern u1="&#x2e;" u2="&#xd4;" k="75" />
<hkern u1="&#x2e;" u2="&#xd3;" k="75" />
<hkern u1="&#x2e;" u2="&#xd2;" k="75" />
<hkern u1="&#x2e;" u2="&#xc7;" k="75" />
<hkern u1="&#x2e;" u2="y" k="88" />
<hkern u1="&#x2e;" u2="w" k="59" />
<hkern u1="&#x2e;" u2="v" k="86" />
<hkern u1="&#x2e;" u2="t" k="19" />
<hkern u1="&#x2e;" u2="f" k="16" />
<hkern u1="&#x2e;" u2="Y" k="166" />
<hkern u1="&#x2e;" u2="W" k="118" />
<hkern u1="&#x2e;" u2="V" k="158" />
<hkern u1="&#x2e;" u2="U" k="63" />
<hkern u1="&#x2e;" u2="T" k="143" />
<hkern u1="&#x2e;" u2="Q" k="75" />
<hkern u1="&#x2e;" u2="O" k="75" />
<hkern u1="&#x2e;" u2="G" k="75" />
<hkern u1="&#x2e;" u2="C" k="75" />
<hkern u1="&#x2e;" u2="&#x2d;" k="105" />
<hkern u1="&#x2e;" u2="&#x27;" k="199" />
<hkern u1="&#x2e;" u2="&#x22;" k="199" />
<hkern u1="&#x2f;" u2="&#x178;" k="-8" />
<hkern u1="&#x2f;" u2="&#x153;" k="79" />
<hkern u1="&#x2f;" u2="&#xfc;" k="43" />
<hkern u1="&#x2f;" u2="&#xfb;" k="43" />
<hkern u1="&#x2f;" u2="&#xfa;" k="43" />
<hkern u1="&#x2f;" u2="&#xf9;" k="43" />
<hkern u1="&#x2f;" u2="&#xf8;" k="79" />
<hkern u1="&#x2f;" u2="&#xf6;" k="79" />
<hkern u1="&#x2f;" u2="&#xf5;" k="79" />
<hkern u1="&#x2f;" u2="&#xf4;" k="79" />
<hkern u1="&#x2f;" u2="&#xf3;" k="79" />
<hkern u1="&#x2f;" u2="&#xf2;" k="79" />
<hkern u1="&#x2f;" u2="&#xf1;" k="47" />
<hkern u1="&#x2f;" u2="&#xf0;" k="58" />
<hkern u1="&#x2f;" u2="&#xef;" k="-23" />
<hkern u1="&#x2f;" u2="&#xec;" k="-47" />
<hkern u1="&#x2f;" u2="&#xeb;" k="79" />
<hkern u1="&#x2f;" u2="&#xea;" k="79" />
<hkern u1="&#x2f;" u2="&#xe9;" k="79" />
<hkern u1="&#x2f;" u2="&#xe8;" k="79" />
<hkern u1="&#x2f;" u2="&#xe7;" k="79" />
<hkern u1="&#x2f;" u2="&#xe6;" k="91" />
<hkern u1="&#x2f;" u2="&#xe5;" k="91" />
<hkern u1="&#x2f;" u2="&#xe4;" k="91" />
<hkern u1="&#x2f;" u2="&#xe3;" k="91" />
<hkern u1="&#x2f;" u2="&#xe2;" k="91" />
<hkern u1="&#x2f;" u2="&#xe1;" k="91" />
<hkern u1="&#x2f;" u2="&#xe0;" k="91" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-8" />
<hkern u1="&#x2f;" u2="&#xc6;" k="133" />
<hkern u1="&#x2f;" u2="&#xc5;" k="98" />
<hkern u1="&#x2f;" u2="&#xc4;" k="98" />
<hkern u1="&#x2f;" u2="&#xc3;" k="98" />
<hkern u1="&#x2f;" u2="&#xc2;" k="98" />
<hkern u1="&#x2f;" u2="&#xc1;" k="98" />
<hkern u1="&#x2f;" u2="&#xc0;" k="98" />
<hkern u1="&#x2f;" u2="z" k="24" />
<hkern u1="&#x2f;" u2="x" k="36" />
<hkern u1="&#x2f;" u2="u" k="43" />
<hkern u1="&#x2f;" u2="s" k="66" />
<hkern u1="&#x2f;" u2="r" k="47" />
<hkern u1="&#x2f;" u2="q" k="91" />
<hkern u1="&#x2f;" u2="p" k="47" />
<hkern u1="&#x2f;" u2="o" k="79" />
<hkern u1="&#x2f;" u2="n" k="47" />
<hkern u1="&#x2f;" u2="m" k="47" />
<hkern u1="&#x2f;" u2="g" k="74" />
<hkern u1="&#x2f;" u2="e" k="79" />
<hkern u1="&#x2f;" u2="d" k="85" />
<hkern u1="&#x2f;" u2="c" k="79" />
<hkern u1="&#x2f;" u2="a" k="91" />
<hkern u1="&#x2f;" u2="Y" k="-8" />
<hkern u1="&#x2f;" u2="V" k="-8" />
<hkern u1="&#x2f;" u2="J" k="101" />
<hkern u1="&#x2f;" u2="A" k="98" />
<hkern u1="&#x2f;" u2="&#x2f;" k="408" />
<hkern u1="&#x3a;" u2="&#x201d;" k="13" />
<hkern u1="&#x3a;" u2="&#x2019;" k="13" />
<hkern u1="&#x3a;" u2="&#x178;" k="87" />
<hkern u1="&#x3a;" u2="&#xdd;" k="87" />
<hkern u1="&#x3a;" u2="Y" k="87" />
<hkern u1="&#x3a;" u2="W" k="22" />
<hkern u1="&#x3a;" u2="V" k="53" />
<hkern u1="&#x3a;" u2="T" k="109" />
<hkern u1="&#x3a;" u2="&#x27;" k="18" />
<hkern u1="&#x3a;" u2="&#x22;" k="18" />
<hkern u1="&#x3b;" u2="&#x201d;" k="13" />
<hkern u1="&#x3b;" u2="&#x2019;" k="13" />
<hkern u1="&#x3b;" u2="&#x178;" k="86" />
<hkern u1="&#x3b;" u2="&#xdd;" k="86" />
<hkern u1="&#x3b;" u2="Y" k="86" />
<hkern u1="&#x3b;" u2="W" k="22" />
<hkern u1="&#x3b;" u2="V" k="51" />
<hkern u1="&#x3b;" u2="T" k="108" />
<hkern u1="&#x3b;" u2="&#x27;" k="18" />
<hkern u1="&#x3b;" u2="&#x22;" k="18" />
<hkern u1="&#x40;" u2="&#x201d;" k="16" />
<hkern u1="&#x40;" u2="&#x2019;" k="16" />
<hkern u1="&#x40;" u2="&#x178;" k="72" />
<hkern u1="&#x40;" u2="&#xdd;" k="72" />
<hkern u1="&#x40;" u2="Y" k="72" />
<hkern u1="&#x40;" u2="V" k="25" />
<hkern u1="&#x40;" u2="T" k="70" />
<hkern u1="A" u2="&#x2122;" k="143" />
<hkern u1="A" u2="&#xf0;" k="30" />
<hkern u1="A" u2="&#xd0;" k="8" />
<hkern u1="A" u2="&#xba;" k="127" />
<hkern u1="A" u2="&#xae;" k="48" />
<hkern u1="A" u2="&#xaa;" k="115" />
<hkern u1="A" u2="&#xa9;" k="47" />
<hkern u1="A" u2="&#x7d;" k="18" />
<hkern u1="A" u2="&#x7c;" k="20" />
<hkern u1="A" u2="]" k="36" />
<hkern u1="A" u2="\" k="109" />
<hkern u1="A" u2="&#x3f;" k="40" />
<hkern u1="A" u2="&#x2a;" k="134" />
<hkern u1="A" u2="&#x20;" k="65" />
<hkern u1="B" u2="&#x2122;" k="13" />
<hkern u1="B" u2="&#xf0;" k="15" />
<hkern u1="B" u2="&#xee;" k="2" />
<hkern u1="B" u2="&#x7d;" k="17" />
<hkern u1="B" u2="]" k="22" />
<hkern u1="C" u2="&#xf0;" k="29" />
<hkern u1="C" u2="&#xef;" k="-42" />
<hkern u1="C" u2="&#xee;" k="-24" />
<hkern u1="D" u2="&#x2122;" k="36" />
<hkern u1="D" u2="&#xf0;" k="13" />
<hkern u1="D" u2="&#xd0;" k="9" />
<hkern u1="D" u2="&#x7d;" k="39" />
<hkern u1="D" u2="]" k="47" />
<hkern u1="D" u2="&#x2c;" k="58" />
<hkern u1="D" u2="&#x29;" k="39" />
<hkern u1="E" u2="&#xf0;" k="53" />
<hkern u1="E" u2="&#xef;" k="-15" />
<hkern u1="E" u2="&#xee;" k="-16" />
<hkern u1="E" u2="&#xec;" k="-22" />
<hkern u1="F" u2="&#xf0;" k="103" />
<hkern u1="F" u2="&#xef;" k="-18" />
<hkern u1="F" u2="&#xee;" k="-18" />
<hkern u1="F" u2="&#xed;" k="43" />
<hkern u1="F" u2="&#xec;" k="-24" />
<hkern u1="F" u2="&#xdf;" k="60" />
<hkern u1="F" u2="&#x3b;" k="37" />
<hkern u1="F" u2="&#x3a;" k="37" />
<hkern u1="F" u2="&#x2f;" k="88" />
<hkern u1="F" u2="&#x2c;" k="180" />
<hkern u1="F" u2="&#x20;" k="41" />
<hkern u1="G" u2="&#xf0;" k="14" />
<hkern u1="G" u2="&#xef;" k="2" />
<hkern u1="G" u2="&#xaa;" k="13" />
<hkern u1="G" u2="]" k="16" />
<hkern u1="G" u2="&#x2a;" k="12" />
<hkern u1="H" u2="&#xf0;" k="33" />
<hkern u1="I" u2="&#xf0;" k="33" />
<hkern u1="J" u2="&#xf0;" k="37" />
<hkern u1="J" u2="&#xee;" k="7" />
<hkern u1="J" u2="&#x2c;" k="38" />
<hkern u1="K" u2="&#x2122;" k="-8" />
<hkern u1="K" u2="&#xf8;" k="52" />
<hkern u1="K" u2="&#xf0;" k="77" />
<hkern u1="K" u2="&#xef;" k="-18" />
<hkern u1="K" u2="&#xed;" k="8" />
<hkern u1="K" u2="&#xec;" k="-56" />
<hkern u1="K" u2="&#xae;" k="42" />
<hkern u1="K" u2="&#xa9;" k="42" />
<hkern u1="L" u2="&#x2122;" k="202" />
<hkern u1="L" u2="&#xf0;" k="73" />
<hkern u1="L" u2="&#xba;" k="199" />
<hkern u1="L" u2="&#xb7;" k="313" />
<hkern u1="L" u2="&#xae;" k="93" />
<hkern u1="L" u2="&#xaa;" k="199" />
<hkern u1="L" u2="&#xa9;" k="92" />
<hkern u1="L" u2="&#x7c;" k="19" />
<hkern u1="L" u2="\" k="128" />
<hkern u1="L" u2="&#x3f;" k="35" />
<hkern u1="L" u2="&#x2a;" k="205" />
<hkern u1="L" u2="&#x20;" k="61" />
<hkern u1="M" u2="&#xf0;" k="33" />
<hkern u1="N" u2="&#xf0;" k="33" />
<hkern u1="O" u2="&#x2122;" k="34" />
<hkern u1="O" u2="&#xf0;" k="13" />
<hkern u1="O" u2="&#xd0;" k="9" />
<hkern u1="O" u2="&#x7d;" k="39" />
<hkern u1="O" u2="]" k="45" />
<hkern u1="O" u2="&#x2c;" k="57" />
<hkern u1="O" u2="&#x29;" k="38" />
<hkern u1="P" u2="&#xf0;" k="71" />
<hkern u1="P" u2="&#xee;" k="-24" />
<hkern u1="P" u2="]" k="17" />
<hkern u1="P" u2="&#x2f;" k="81" />
<hkern u1="P" u2="&#x2c;" k="186" />
<hkern u1="P" u2="&#x20;" k="51" />
<hkern u1="Q" u2="&#x2122;" k="34" />
<hkern u1="Q" u2="&#x201e;" k="49" />
<hkern u1="Q" u2="&#x201a;" k="49" />
<hkern u1="Q" u2="&#xf0;" k="13" />
<hkern u1="Q" u2="&#xd0;" k="9" />
<hkern u1="Q" u2="&#x7d;" k="10" />
<hkern u1="Q" u2="]" k="11" />
<hkern u1="Q" u2="&#x2c;" k="57" />
<hkern u1="Q" u2="&#x29;" k="38" />
<hkern u1="R" u2="&#xf8;" k="48" />
<hkern u1="R" u2="&#xf0;" k="71" />
<hkern u1="R" u2="&#xee;" k="9" />
<hkern u1="R" u2="&#xd0;" k="8" />
<hkern u1="S" u2="&#xf0;" k="6" />
<hkern u1="S" u2="&#xef;" k="-4" />
<hkern u1="S" u2="&#xaa;" k="13" />
<hkern u1="S" u2="]" k="16" />
<hkern u1="T" g2="uniFB02" k="119" />
<hkern u1="T" g2="uniFB01" k="118" />
<hkern u1="T" u2="&#x2122;" k="-14" />
<hkern u1="T" u2="&#xf6;" k="203" />
<hkern u1="T" u2="&#xf0;" k="156" />
<hkern u1="T" u2="&#xef;" k="-40" />
<hkern u1="T" u2="&#xee;" k="-44" />
<hkern u1="T" u2="&#xed;" k="65" />
<hkern u1="T" u2="&#xec;" k="-46" />
<hkern u1="T" u2="&#xeb;" k="196" />
<hkern u1="T" u2="&#xea;" k="208" />
<hkern u1="T" u2="&#xe8;" k="200" />
<hkern u1="T" u2="&#xdf;" k="152" />
<hkern u1="T" u2="&#xae;" k="19" />
<hkern u1="T" u2="&#xa9;" k="19" />
<hkern u1="T" u2="&#x40;" k="79" />
<hkern u1="T" u2="&#x3b;" k="104" />
<hkern u1="T" u2="&#x3a;" k="106" />
<hkern u1="T" u2="&#x2f;" k="113" />
<hkern u1="T" u2="&#x2c;" k="141" />
<hkern u1="T" u2="&#x26;" k="45" />
<hkern u1="T" u2="&#x20;" k="52" />
<hkern u1="U" u2="&#xf0;" k="39" />
<hkern u1="U" u2="&#xee;" k="8" />
<hkern u1="U" u2="&#xed;" k="27" />
<hkern u1="U" u2="&#xec;" k="13" />
<hkern u1="U" u2="&#x2f;" k="20" />
<hkern u1="U" u2="&#x2c;" k="54" />
<hkern u1="V" u2="&#x2122;" k="-32" />
<hkern u1="V" u2="&#xff;" k="35" />
<hkern u1="V" u2="&#xf0;" k="122" />
<hkern u1="V" u2="&#xef;" k="-46" />
<hkern u1="V" u2="&#xee;" k="-22" />
<hkern u1="V" u2="&#xed;" k="30" />
<hkern u1="V" u2="&#xec;" k="-73" />
<hkern u1="V" u2="&#xe8;" k="121" />
<hkern u1="V" u2="&#x7d;" k="-6" />
<hkern u1="V" u2="]" k="-11" />
<hkern u1="V" u2="\" k="-9" />
<hkern u1="V" u2="&#x40;" k="42" />
<hkern u1="V" u2="&#x3b;" k="28" />
<hkern u1="V" u2="&#x3a;" k="41" />
<hkern u1="V" u2="&#x2f;" k="114" />
<hkern u1="V" u2="&#x2c;" k="152" />
<hkern u1="V" u2="&#x26;" k="46" />
<hkern u1="V" u2="&#x20;" k="65" />
<hkern u1="W" u2="&#x2122;" k="-13" />
<hkern u1="W" u2="&#xf0;" k="100" />
<hkern u1="W" u2="&#xef;" k="-40" />
<hkern u1="W" u2="&#xee;" k="-24" />
<hkern u1="W" u2="&#xed;" k="25" />
<hkern u1="W" u2="&#xec;" k="-61" />
<hkern u1="W" u2="&#x3b;" k="15" />
<hkern u1="W" u2="&#x3a;" k="16" />
<hkern u1="W" u2="&#x2f;" k="83" />
<hkern u1="W" u2="&#x2c;" k="115" />
<hkern u1="W" u2="&#x26;" k="22" />
<hkern u1="W" u2="&#x20;" k="54" />
<hkern u1="X" u2="&#xf8;" k="43" />
<hkern u1="X" u2="&#xf0;" k="67" />
<hkern u1="X" u2="&#xef;" k="-15" />
<hkern u1="X" u2="&#xec;" k="-50" />
<hkern u1="X" u2="&#xae;" k="38" />
<hkern u1="X" u2="&#xa9;" k="38" />
<hkern u1="Y" g2="uniFB02" k="92" />
<hkern u1="Y" u2="&#x2122;" k="-35" />
<hkern u1="Y" u2="&#xff;" k="95" />
<hkern u1="Y" u2="&#xf9;" k="146" />
<hkern u1="Y" u2="&#xf6;" k="181" />
<hkern u1="Y" u2="&#xf2;" k="171" />
<hkern u1="Y" u2="&#xf0;" k="151" />
<hkern u1="Y" u2="&#xef;" k="-40" />
<hkern u1="Y" u2="&#xed;" k="51" />
<hkern u1="Y" u2="&#xec;" k="-73" />
<hkern u1="Y" u2="&#xeb;" k="175" />
<hkern u1="Y" u2="&#xe8;" k="152" />
<hkern u1="Y" u2="&#xdf;" k="103" />
<hkern u1="Y" u2="&#xae;" k="25" />
<hkern u1="Y" u2="&#xa9;" k="25" />
<hkern u1="Y" u2="&#x7d;" k="-7" />
<hkern u1="Y" u2="]" k="-11" />
<hkern u1="Y" u2="\" k="-9" />
<hkern u1="Y" u2="&#x40;" k="79" />
<hkern u1="Y" u2="&#x3b;" k="73" />
<hkern u1="Y" u2="&#x3a;" k="73" />
<hkern u1="Y" u2="&#x2f;" k="133" />
<hkern u1="Y" u2="&#x2c;" k="163" />
<hkern u1="Y" u2="&#x26;" k="69" />
<hkern u1="Y" u2="&#x20;" k="69" />
<hkern u1="Z" u2="&#xf0;" k="50" />
<hkern u1="Z" u2="&#xef;" k="-33" />
<hkern u1="Z" u2="&#xed;" k="9" />
<hkern u1="Z" u2="&#xec;" k="-40" />
<hkern u1="[" u2="&#x178;" k="-10" />
<hkern u1="[" u2="&#x153;" k="62" />
<hkern u1="[" u2="&#x152;" k="45" />
<hkern u1="[" u2="&#xff;" k="22" />
<hkern u1="[" u2="&#xfd;" k="22" />
<hkern u1="[" u2="&#xfc;" k="51" />
<hkern u1="[" u2="&#xfb;" k="51" />
<hkern u1="[" u2="&#xfa;" k="51" />
<hkern u1="[" u2="&#xf9;" k="51" />
<hkern u1="[" u2="&#xf8;" k="62" />
<hkern u1="[" u2="&#xf6;" k="62" />
<hkern u1="[" u2="&#xf5;" k="62" />
<hkern u1="[" u2="&#xf4;" k="62" />
<hkern u1="[" u2="&#xf3;" k="62" />
<hkern u1="[" u2="&#xf2;" k="62" />
<hkern u1="[" u2="&#xf1;" k="47" />
<hkern u1="[" u2="&#xf0;" k="55" />
<hkern u1="[" u2="&#xef;" k="-31" />
<hkern u1="[" u2="&#xec;" k="-57" />
<hkern u1="[" u2="&#xeb;" k="62" />
<hkern u1="[" u2="&#xea;" k="62" />
<hkern u1="[" u2="&#xe9;" k="62" />
<hkern u1="[" u2="&#xe8;" k="62" />
<hkern u1="[" u2="&#xe7;" k="62" />
<hkern u1="[" u2="&#xe6;" k="62" />
<hkern u1="[" u2="&#xe5;" k="62" />
<hkern u1="[" u2="&#xe4;" k="62" />
<hkern u1="[" u2="&#xe3;" k="62" />
<hkern u1="[" u2="&#xe2;" k="62" />
<hkern u1="[" u2="&#xe1;" k="62" />
<hkern u1="[" u2="&#xe0;" k="62" />
<hkern u1="[" u2="&#xdd;" k="-10" />
<hkern u1="[" u2="&#xd8;" k="45" />
<hkern u1="[" u2="&#xd6;" k="45" />
<hkern u1="[" u2="&#xd5;" k="45" />
<hkern u1="[" u2="&#xd4;" k="45" />
<hkern u1="[" u2="&#xd3;" k="45" />
<hkern u1="[" u2="&#xd2;" k="45" />
<hkern u1="[" u2="&#xcf;" k="-29" />
<hkern u1="[" u2="&#xce;" k="-45" />
<hkern u1="[" u2="&#xc7;" k="45" />
<hkern u1="[" u2="&#xc6;" k="50" />
<hkern u1="[" u2="&#xc5;" k="18" />
<hkern u1="[" u2="&#xc4;" k="18" />
<hkern u1="[" u2="&#xc3;" k="18" />
<hkern u1="[" u2="&#xc2;" k="18" />
<hkern u1="[" u2="&#xc1;" k="18" />
<hkern u1="[" u2="&#xc0;" k="18" />
<hkern u1="[" u2="z" k="40" />
<hkern u1="[" u2="y" k="22" />
<hkern u1="[" u2="x" k="20" />
<hkern u1="[" u2="w" k="49" />
<hkern u1="[" u2="v" k="53" />
<hkern u1="[" u2="u" k="51" />
<hkern u1="[" u2="t" k="47" />
<hkern u1="[" u2="s" k="52" />
<hkern u1="[" u2="r" k="47" />
<hkern u1="[" u2="q" k="62" />
<hkern u1="[" u2="p" k="47" />
<hkern u1="[" u2="o" k="62" />
<hkern u1="[" u2="n" k="47" />
<hkern u1="[" u2="m" k="47" />
<hkern u1="[" u2="j" k="-34" />
<hkern u1="[" u2="e" k="62" />
<hkern u1="[" u2="d" k="62" />
<hkern u1="[" u2="c" k="62" />
<hkern u1="[" u2="a" k="62" />
<hkern u1="[" u2="Y" k="-10" />
<hkern u1="[" u2="V" k="-10" />
<hkern u1="[" u2="S" k="17" />
<hkern u1="[" u2="Q" k="45" />
<hkern u1="[" u2="O" k="45" />
<hkern u1="[" u2="G" k="45" />
<hkern u1="[" u2="C" k="45" />
<hkern u1="[" u2="A" k="18" />
<hkern u1="\" u2="&#x201d;" k="108" />
<hkern u1="\" u2="&#x2019;" k="108" />
<hkern u1="\" u2="&#x178;" k="142" />
<hkern u1="\" u2="&#x152;" k="40" />
<hkern u1="\" u2="&#xff;" k="63" />
<hkern u1="\" u2="&#xfd;" k="63" />
<hkern u1="\" u2="&#xdd;" k="142" />
<hkern u1="\" u2="&#xdc;" k="24" />
<hkern u1="\" u2="&#xdb;" k="24" />
<hkern u1="\" u2="&#xda;" k="24" />
<hkern u1="\" u2="&#xd9;" k="24" />
<hkern u1="\" u2="&#xd8;" k="40" />
<hkern u1="\" u2="&#xd6;" k="40" />
<hkern u1="\" u2="&#xd5;" k="40" />
<hkern u1="\" u2="&#xd4;" k="40" />
<hkern u1="\" u2="&#xd3;" k="40" />
<hkern u1="\" u2="&#xd2;" k="40" />
<hkern u1="\" u2="&#xc7;" k="40" />
<hkern u1="\" u2="y" k="63" />
<hkern u1="\" u2="w" k="54" />
<hkern u1="\" u2="v" k="73" />
<hkern u1="\" u2="t" k="29" />
<hkern u1="\" u2="Y" k="142" />
<hkern u1="\" u2="W" k="89" />
<hkern u1="\" u2="V" k="126" />
<hkern u1="\" u2="U" k="24" />
<hkern u1="\" u2="T" k="119" />
<hkern u1="\" u2="Q" k="40" />
<hkern u1="\" u2="O" k="40" />
<hkern u1="\" u2="G" k="40" />
<hkern u1="\" u2="C" k="40" />
<hkern u1="\" u2="&#x27;" k="106" />
<hkern u1="\" u2="&#x22;" k="106" />
<hkern u1="a" u2="&#x2122;" k="37" />
<hkern u1="a" u2="&#x7d;" k="42" />
<hkern u1="a" u2="]" k="49" />
<hkern u1="a" u2="\" k="51" />
<hkern u1="a" u2="&#x3f;" k="20" />
<hkern u1="a" u2="&#x2a;" k="17" />
<hkern u1="b" u2="&#x2122;" k="59" />
<hkern u1="b" u2="&#xba;" k="45" />
<hkern u1="b" u2="&#xaa;" k="33" />
<hkern u1="b" u2="&#x7d;" k="56" />
<hkern u1="b" u2="&#x7c;" k="18" />
<hkern u1="b" u2="]" k="62" />
<hkern u1="b" u2="\" k="74" />
<hkern u1="b" u2="&#x3f;" k="59" />
<hkern u1="b" u2="&#x2f;" k="18" />
<hkern u1="b" u2="&#x2a;" k="65" />
<hkern u1="b" u2="&#x29;" k="57" />
<hkern u1="c" u2="&#x2122;" k="47" />
<hkern u1="c" u2="&#xf0;" k="23" />
<hkern u1="c" u2="&#xba;" k="13" />
<hkern u1="c" u2="&#x7d;" k="48" />
<hkern u1="c" u2="]" k="53" />
<hkern u1="c" u2="\" k="58" />
<hkern u1="c" u2="&#x3f;" k="40" />
<hkern u1="c" u2="&#x2a;" k="44" />
<hkern u1="c" u2="&#x29;" k="16" />
<hkern u1="d" u2="&#xee;" k="-13" />
<hkern u1="d" u2="&#xec;" k="-15" />
<hkern u1="e" u2="&#x2122;" k="52" />
<hkern u1="e" u2="&#xf0;" k="5" />
<hkern u1="e" u2="&#xba;" k="36" />
<hkern u1="e" u2="&#xaa;" k="16" />
<hkern u1="e" u2="&#x7d;" k="50" />
<hkern u1="e" u2="]" k="55" />
<hkern u1="e" u2="\" k="68" />
<hkern u1="e" u2="&#x3f;" k="46" />
<hkern u1="e" u2="&#x2a;" k="54" />
<hkern u1="e" u2="&#x29;" k="39" />
<hkern u1="f" u2="&#x2122;" k="-13" />
<hkern u1="f" u2="&#xf0;" k="51" />
<hkern u1="f" u2="&#xef;" k="-61" />
<hkern u1="f" u2="&#xee;" k="-18" />
<hkern u1="f" u2="&#xec;" k="-73" />
<hkern u1="f" u2="&#x7d;" k="-11" />
<hkern u1="f" u2="&#x7c;" k="-9" />
<hkern u1="f" u2="]" k="-16" />
<hkern u1="f" u2="\" k="-14" />
<hkern u1="f" u2="&#x2f;" k="49" />
<hkern u1="f" u2="&#x2c;" k="52" />
<hkern u1="f" u2="&#x2a;" k="2" />
<hkern u1="f" u2="&#x29;" k="-10" />
<hkern u1="f" u2="&#x20;" k="20" />
<hkern u1="g" u2="&#x2122;" k="14" />
<hkern u1="g" u2="&#xf0;" k="34" />
<hkern u1="g" u2="\" k="19" />
<hkern u1="h" u2="&#x2122;" k="54" />
<hkern u1="h" u2="&#xba;" k="39" />
<hkern u1="h" u2="&#xaa;" k="17" />
<hkern u1="h" u2="&#x7d;" k="47" />
<hkern u1="h" u2="]" k="52" />
<hkern u1="h" u2="\" k="72" />
<hkern u1="h" u2="&#x3f;" k="48" />
<hkern u1="h" u2="&#x2a;" k="54" />
<hkern u1="h" u2="&#x29;" k="15" />
<hkern u1="i" u2="&#xef;" k="-11" />
<hkern u1="j" u2="&#xef;" k="-11" />
<hkern u1="k" u2="&#x2122;" k="31" />
<hkern u1="k" u2="&#xf0;" k="61" />
<hkern u1="k" u2="&#x7d;" k="19" />
<hkern u1="k" u2="]" k="40" />
<hkern u1="k" u2="&#x2a;" k="11" />
<hkern u1="l" u2="&#xee;" k="-13" />
<hkern u1="l" u2="&#xec;" k="-13" />
<hkern u1="l" u2="&#xb7;" k="66" />
<hkern u1="m" u2="&#x2122;" k="54" />
<hkern u1="m" u2="&#xba;" k="39" />
<hkern u1="m" u2="&#xaa;" k="17" />
<hkern u1="m" u2="&#x7d;" k="47" />
<hkern u1="m" u2="]" k="52" />
<hkern u1="m" u2="\" k="72" />
<hkern u1="m" u2="&#x3f;" k="48" />
<hkern u1="m" u2="&#x2a;" k="54" />
<hkern u1="m" u2="&#x29;" k="15" />
<hkern u1="n" u2="&#x2122;" k="54" />
<hkern u1="n" u2="&#xba;" k="39" />
<hkern u1="n" u2="&#xaa;" k="17" />
<hkern u1="n" u2="&#x7d;" k="47" />
<hkern u1="n" u2="]" k="52" />
<hkern u1="n" u2="\" k="72" />
<hkern u1="n" u2="&#x3f;" k="48" />
<hkern u1="n" u2="&#x2a;" k="54" />
<hkern u1="n" u2="&#x29;" k="15" />
<hkern u1="o" u2="&#x2122;" k="63" />
<hkern u1="o" u2="&#xba;" k="51" />
<hkern u1="o" u2="&#xaa;" k="37" />
<hkern u1="o" u2="&#x7d;" k="55" />
<hkern u1="o" u2="&#x7c;" k="19" />
<hkern u1="o" u2="]" k="62" />
<hkern u1="o" u2="\" k="80" />
<hkern u1="o" u2="&#x3f;" k="59" />
<hkern u1="o" u2="&#x2a;" k="72" />
<hkern u1="o" u2="&#x29;" k="56" />
<hkern u1="p" u2="&#x2122;" k="59" />
<hkern u1="p" u2="&#xba;" k="45" />
<hkern u1="p" u2="&#xaa;" k="33" />
<hkern u1="p" u2="&#x7d;" k="56" />
<hkern u1="p" u2="&#x7c;" k="18" />
<hkern u1="p" u2="]" k="62" />
<hkern u1="p" u2="\" k="74" />
<hkern u1="p" u2="&#x3f;" k="59" />
<hkern u1="p" u2="&#x2f;" k="18" />
<hkern u1="p" u2="&#x2a;" k="65" />
<hkern u1="p" u2="&#x29;" k="57" />
<hkern u1="q" u2="&#x2122;" k="35" />
<hkern u1="q" u2="&#x7d;" k="42" />
<hkern u1="q" u2="]" k="48" />
<hkern u1="q" u2="\" k="47" />
<hkern u1="q" u2="&#x3f;" k="18" />
<hkern u1="q" u2="&#x2a;" k="13" />
<hkern u1="r" u2="&#xf0;" k="53" />
<hkern u1="r" u2="&#x7d;" k="40" />
<hkern u1="r" u2="]" k="47" />
<hkern u1="r" u2="&#x2f;" k="74" />
<hkern u1="r" u2="&#x2c;" k="87" />
<hkern u1="r" u2="&#x29;" k="37" />
<hkern u1="r" u2="&#x20;" k="39" />
<hkern u1="s" u2="&#x2122;" k="40" />
<hkern u1="s" u2="&#x7d;" k="45" />
<hkern u1="s" u2="]" k="52" />
<hkern u1="s" u2="\" k="52" />
<hkern u1="s" u2="&#x3f;" k="23" />
<hkern u1="s" u2="&#x2a;" k="35" />
<hkern u1="s" u2="&#x29;" k="38" />
<hkern u1="t" u2="&#x2122;" k="35" />
<hkern u1="t" u2="&#xf0;" k="28" />
<hkern u1="t" u2="&#x7d;" k="41" />
<hkern u1="t" u2="]" k="45" />
<hkern u1="t" u2="\" k="39" />
<hkern u1="t" u2="&#x2a;" k="22" />
<hkern u1="u" u2="&#x2122;" k="35" />
<hkern u1="u" u2="&#x7d;" k="42" />
<hkern u1="u" u2="]" k="48" />
<hkern u1="u" u2="\" k="47" />
<hkern u1="u" u2="&#x3f;" k="18" />
<hkern u1="u" u2="&#x2a;" k="13" />
<hkern u1="v" u2="&#xf0;" k="33" />
<hkern u1="v" u2="&#x7d;" k="42" />
<hkern u1="v" u2="]" k="49" />
<hkern u1="v" u2="&#x2f;" k="66" />
<hkern u1="v" u2="&#x2c;" k="76" />
<hkern u1="v" u2="&#x29;" k="19" />
<hkern u1="v" u2="&#x20;" k="44" />
<hkern u1="w" u2="&#xf0;" k="20" />
<hkern u1="w" u2="&#x7d;" k="36" />
<hkern u1="w" u2="]" k="43" />
<hkern u1="w" u2="&#x2f;" k="49" />
<hkern u1="w" u2="&#x2c;" k="53" />
<hkern u1="w" u2="&#x29;" k="17" />
<hkern u1="w" u2="&#x20;" k="37" />
<hkern u1="x" u2="&#x2122;" k="12" />
<hkern u1="x" u2="&#xf0;" k="49" />
<hkern u1="x" u2="&#x7d;" k="18" />
<hkern u1="x" u2="]" k="20" />
<hkern u1="y" u2="&#xf0;" k="35" />
<hkern u1="y" u2="&#x7d;" k="39" />
<hkern u1="y" u2="]" k="47" />
<hkern u1="y" u2="&#x2f;" k="66" />
<hkern u1="y" u2="&#x2c;" k="81" />
<hkern u1="y" u2="&#x29;" k="18" />
<hkern u1="y" u2="&#x20;" k="45" />
<hkern u1="z" u2="&#x2122;" k="16" />
<hkern u1="z" u2="&#xf0;" k="27" />
<hkern u1="z" u2="&#x7d;" k="17" />
<hkern u1="z" u2="]" k="41" />
<hkern u1="z" u2="\" k="18" />
<hkern u1="&#x7b;" u2="&#x178;" k="-6" />
<hkern u1="&#x7b;" u2="&#x153;" k="55" />
<hkern u1="&#x7b;" u2="&#x152;" k="40" />
<hkern u1="&#x7b;" u2="&#xff;" k="18" />
<hkern u1="&#x7b;" u2="&#xfd;" k="18" />
<hkern u1="&#x7b;" u2="&#xfc;" k="44" />
<hkern u1="&#x7b;" u2="&#xfb;" k="44" />
<hkern u1="&#x7b;" u2="&#xfa;" k="44" />
<hkern u1="&#x7b;" u2="&#xf9;" k="44" />
<hkern u1="&#x7b;" u2="&#xf8;" k="55" />
<hkern u1="&#x7b;" u2="&#xf6;" k="55" />
<hkern u1="&#x7b;" u2="&#xf5;" k="55" />
<hkern u1="&#x7b;" u2="&#xf4;" k="55" />
<hkern u1="&#x7b;" u2="&#xf3;" k="55" />
<hkern u1="&#x7b;" u2="&#xf2;" k="55" />
<hkern u1="&#x7b;" u2="&#xf1;" k="41" />
<hkern u1="&#x7b;" u2="&#xf0;" k="49" />
<hkern u1="&#x7b;" u2="&#xef;" k="-31" />
<hkern u1="&#x7b;" u2="&#xec;" k="-54" />
<hkern u1="&#x7b;" u2="&#xeb;" k="55" />
<hkern u1="&#x7b;" u2="&#xea;" k="55" />
<hkern u1="&#x7b;" u2="&#xe9;" k="55" />
<hkern u1="&#x7b;" u2="&#xe8;" k="55" />
<hkern u1="&#x7b;" u2="&#xe7;" k="55" />
<hkern u1="&#x7b;" u2="&#xe6;" k="55" />
<hkern u1="&#x7b;" u2="&#xe5;" k="55" />
<hkern u1="&#x7b;" u2="&#xe4;" k="55" />
<hkern u1="&#x7b;" u2="&#xe3;" k="55" />
<hkern u1="&#x7b;" u2="&#xe2;" k="55" />
<hkern u1="&#x7b;" u2="&#xe1;" k="55" />
<hkern u1="&#x7b;" u2="&#xe0;" k="55" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-6" />
<hkern u1="&#x7b;" u2="&#xd8;" k="40" />
<hkern u1="&#x7b;" u2="&#xd6;" k="40" />
<hkern u1="&#x7b;" u2="&#xd5;" k="40" />
<hkern u1="&#x7b;" u2="&#xd4;" k="40" />
<hkern u1="&#x7b;" u2="&#xd3;" k="40" />
<hkern u1="&#x7b;" u2="&#xd2;" k="40" />
<hkern u1="&#x7b;" u2="&#xcf;" k="-26" />
<hkern u1="&#x7b;" u2="&#xce;" k="-42" />
<hkern u1="&#x7b;" u2="&#xc7;" k="40" />
<hkern u1="&#x7b;" u2="&#xc6;" k="49" />
<hkern u1="&#x7b;" u2="&#xc5;" k="18" />
<hkern u1="&#x7b;" u2="&#xc4;" k="18" />
<hkern u1="&#x7b;" u2="&#xc3;" k="18" />
<hkern u1="&#x7b;" u2="&#xc2;" k="18" />
<hkern u1="&#x7b;" u2="&#xc1;" k="18" />
<hkern u1="&#x7b;" u2="&#xc0;" k="18" />
<hkern u1="&#x7b;" u2="z" k="17" />
<hkern u1="&#x7b;" u2="y" k="18" />
<hkern u1="&#x7b;" u2="x" k="19" />
<hkern u1="&#x7b;" u2="w" k="42" />
<hkern u1="&#x7b;" u2="v" k="47" />
<hkern u1="&#x7b;" u2="u" k="44" />
<hkern u1="&#x7b;" u2="t" k="40" />
<hkern u1="&#x7b;" u2="s" k="48" />
<hkern u1="&#x7b;" u2="r" k="41" />
<hkern u1="&#x7b;" u2="q" k="55" />
<hkern u1="&#x7b;" u2="p" k="41" />
<hkern u1="&#x7b;" u2="o" k="55" />
<hkern u1="&#x7b;" u2="n" k="41" />
<hkern u1="&#x7b;" u2="m" k="41" />
<hkern u1="&#x7b;" u2="j" k="-32" />
<hkern u1="&#x7b;" u2="e" k="55" />
<hkern u1="&#x7b;" u2="d" k="55" />
<hkern u1="&#x7b;" u2="c" k="55" />
<hkern u1="&#x7b;" u2="a" k="55" />
<hkern u1="&#x7b;" u2="Y" k="-6" />
<hkern u1="&#x7b;" u2="V" k="-7" />
<hkern u1="&#x7b;" u2="S" k="15" />
<hkern u1="&#x7b;" u2="Q" k="40" />
<hkern u1="&#x7b;" u2="O" k="40" />
<hkern u1="&#x7b;" u2="G" k="40" />
<hkern u1="&#x7b;" u2="C" k="40" />
<hkern u1="&#x7b;" u2="A" k="18" />
<hkern u1="&#x7c;" u2="&#x201d;" k="50" />
<hkern u1="&#x7c;" u2="&#x2019;" k="50" />
<hkern u1="&#x7c;" u2="&#x178;" k="65" />
<hkern u1="&#x7c;" u2="&#xdd;" k="65" />
<hkern u1="&#x7c;" u2="w" k="18" />
<hkern u1="&#x7c;" u2="v" k="37" />
<hkern u1="&#x7c;" u2="t" k="19" />
<hkern u1="&#x7c;" u2="j" k="-35" />
<hkern u1="&#x7c;" u2="Y" k="65" />
<hkern u1="&#x7c;" u2="W" k="42" />
<hkern u1="&#x7c;" u2="V" k="56" />
<hkern u1="&#x7c;" u2="T" k="42" />
<hkern u1="&#x7c;" u2="&#x27;" k="48" />
<hkern u1="&#x7c;" u2="&#x22;" k="48" />
<hkern u1="&#xa1;" u2="&#x178;" k="90" />
<hkern u1="&#xa1;" u2="&#xdd;" k="90" />
<hkern u1="&#xa1;" u2="Y" k="90" />
<hkern u1="&#xa1;" u2="W" k="24" />
<hkern u1="&#xa1;" u2="V" k="57" />
<hkern u1="&#xa1;" u2="T" k="126" />
<hkern u1="&#xb7;" u2="l" k="66" />
<hkern u1="&#xbf;" u2="&#x178;" k="137" />
<hkern u1="&#xbf;" u2="&#x153;" k="57" />
<hkern u1="&#xbf;" u2="&#x152;" k="47" />
<hkern u1="&#xbf;" u2="&#xff;" k="15" />
<hkern u1="&#xbf;" u2="&#xfe;" k="20" />
<hkern u1="&#xbf;" u2="&#xfd;" k="15" />
<hkern u1="&#xbf;" u2="&#xfc;" k="42" />
<hkern u1="&#xbf;" u2="&#xfb;" k="42" />
<hkern u1="&#xbf;" u2="&#xfa;" k="42" />
<hkern u1="&#xbf;" u2="&#xf9;" k="42" />
<hkern u1="&#xbf;" u2="&#xf8;" k="57" />
<hkern u1="&#xbf;" u2="&#xf6;" k="57" />
<hkern u1="&#xbf;" u2="&#xf5;" k="57" />
<hkern u1="&#xbf;" u2="&#xf4;" k="57" />
<hkern u1="&#xbf;" u2="&#xf3;" k="57" />
<hkern u1="&#xbf;" u2="&#xf2;" k="57" />
<hkern u1="&#xbf;" u2="&#xf1;" k="20" />
<hkern u1="&#xbf;" u2="&#xf0;" k="62" />
<hkern u1="&#xbf;" u2="&#xef;" k="20" />
<hkern u1="&#xbf;" u2="&#xee;" k="20" />
<hkern u1="&#xbf;" u2="&#xed;" k="20" />
<hkern u1="&#xbf;" u2="&#xec;" k="20" />
<hkern u1="&#xbf;" u2="&#xeb;" k="57" />
<hkern u1="&#xbf;" u2="&#xea;" k="57" />
<hkern u1="&#xbf;" u2="&#xe9;" k="57" />
<hkern u1="&#xbf;" u2="&#xe8;" k="57" />
<hkern u1="&#xbf;" u2="&#xe7;" k="57" />
<hkern u1="&#xbf;" u2="&#xe6;" k="59" />
<hkern u1="&#xbf;" u2="&#xe5;" k="59" />
<hkern u1="&#xbf;" u2="&#xe4;" k="59" />
<hkern u1="&#xbf;" u2="&#xe3;" k="59" />
<hkern u1="&#xbf;" u2="&#xe2;" k="59" />
<hkern u1="&#xbf;" u2="&#xe1;" k="59" />
<hkern u1="&#xbf;" u2="&#xe0;" k="59" />
<hkern u1="&#xbf;" u2="&#xde;" k="18" />
<hkern u1="&#xbf;" u2="&#xdd;" k="137" />
<hkern u1="&#xbf;" u2="&#xdc;" k="50" />
<hkern u1="&#xbf;" u2="&#xdb;" k="50" />
<hkern u1="&#xbf;" u2="&#xda;" k="50" />
<hkern u1="&#xbf;" u2="&#xd9;" k="50" />
<hkern u1="&#xbf;" u2="&#xd8;" k="47" />
<hkern u1="&#xbf;" u2="&#xd6;" k="47" />
<hkern u1="&#xbf;" u2="&#xd5;" k="47" />
<hkern u1="&#xbf;" u2="&#xd4;" k="47" />
<hkern u1="&#xbf;" u2="&#xd3;" k="47" />
<hkern u1="&#xbf;" u2="&#xd2;" k="47" />
<hkern u1="&#xbf;" u2="&#xd1;" k="18" />
<hkern u1="&#xbf;" u2="&#xd0;" k="42" />
<hkern u1="&#xbf;" u2="&#xcf;" k="18" />
<hkern u1="&#xbf;" u2="&#xce;" k="18" />
<hkern u1="&#xbf;" u2="&#xcd;" k="18" />
<hkern u1="&#xbf;" u2="&#xcc;" k="18" />
<hkern u1="&#xbf;" u2="&#xcb;" k="18" />
<hkern u1="&#xbf;" u2="&#xca;" k="18" />
<hkern u1="&#xbf;" u2="&#xc9;" k="18" />
<hkern u1="&#xbf;" u2="&#xc8;" k="18" />
<hkern u1="&#xbf;" u2="&#xc7;" k="47" />
<hkern u1="&#xbf;" u2="&#xc6;" k="36" />
<hkern u1="&#xbf;" u2="&#xc5;" k="30" />
<hkern u1="&#xbf;" u2="&#xc4;" k="30" />
<hkern u1="&#xbf;" u2="&#xc3;" k="30" />
<hkern u1="&#xbf;" u2="&#xc2;" k="30" />
<hkern u1="&#xbf;" u2="&#xc1;" k="30" />
<hkern u1="&#xbf;" u2="&#xc0;" k="30" />
<hkern u1="&#xbf;" u2="z" k="24" />
<hkern u1="&#xbf;" u2="y" k="15" />
<hkern u1="&#xbf;" u2="x" k="23" />
<hkern u1="&#xbf;" u2="w" k="17" />
<hkern u1="&#xbf;" u2="v" k="41" />
<hkern u1="&#xbf;" u2="u" k="42" />
<hkern u1="&#xbf;" u2="t" k="17" />
<hkern u1="&#xbf;" u2="s" k="47" />
<hkern u1="&#xbf;" u2="r" k="20" />
<hkern u1="&#xbf;" u2="q" k="59" />
<hkern u1="&#xbf;" u2="p" k="20" />
<hkern u1="&#xbf;" u2="o" k="57" />
<hkern u1="&#xbf;" u2="n" k="20" />
<hkern u1="&#xbf;" u2="m" k="20" />
<hkern u1="&#xbf;" u2="l" k="20" />
<hkern u1="&#xbf;" u2="k" k="20" />
<hkern u1="&#xbf;" u2="j" k="-5" />
<hkern u1="&#xbf;" u2="i" k="20" />
<hkern u1="&#xbf;" u2="h" k="20" />
<hkern u1="&#xbf;" u2="e" k="57" />
<hkern u1="&#xbf;" u2="d" k="57" />
<hkern u1="&#xbf;" u2="c" k="57" />
<hkern u1="&#xbf;" u2="b" k="20" />
<hkern u1="&#xbf;" u2="a" k="59" />
<hkern u1="&#xbf;" u2="Z" k="25" />
<hkern u1="&#xbf;" u2="Y" k="137" />
<hkern u1="&#xbf;" u2="X" k="30" />
<hkern u1="&#xbf;" u2="W" k="81" />
<hkern u1="&#xbf;" u2="V" k="106" />
<hkern u1="&#xbf;" u2="U" k="50" />
<hkern u1="&#xbf;" u2="T" k="164" />
<hkern u1="&#xbf;" u2="S" k="20" />
<hkern u1="&#xbf;" u2="R" k="18" />
<hkern u1="&#xbf;" u2="Q" k="47" />
<hkern u1="&#xbf;" u2="P" k="18" />
<hkern u1="&#xbf;" u2="O" k="47" />
<hkern u1="&#xbf;" u2="N" k="18" />
<hkern u1="&#xbf;" u2="M" k="18" />
<hkern u1="&#xbf;" u2="L" k="18" />
<hkern u1="&#xbf;" u2="K" k="18" />
<hkern u1="&#xbf;" u2="J" k="43" />
<hkern u1="&#xbf;" u2="I" k="18" />
<hkern u1="&#xbf;" u2="H" k="18" />
<hkern u1="&#xbf;" u2="G" k="47" />
<hkern u1="&#xbf;" u2="F" k="18" />
<hkern u1="&#xbf;" u2="E" k="18" />
<hkern u1="&#xbf;" u2="D" k="18" />
<hkern u1="&#xbf;" u2="C" k="47" />
<hkern u1="&#xbf;" u2="B" k="18" />
<hkern u1="&#xbf;" u2="A" k="30" />
<hkern u1="&#xc0;" u2="&#x2122;" k="143" />
<hkern u1="&#xc0;" u2="&#xf0;" k="30" />
<hkern u1="&#xc0;" u2="&#xd0;" k="8" />
<hkern u1="&#xc0;" u2="&#xba;" k="127" />
<hkern u1="&#xc0;" u2="&#xae;" k="48" />
<hkern u1="&#xc0;" u2="&#xaa;" k="115" />
<hkern u1="&#xc0;" u2="&#xa9;" k="47" />
<hkern u1="&#xc0;" u2="&#x7d;" k="18" />
<hkern u1="&#xc0;" u2="&#x7c;" k="20" />
<hkern u1="&#xc0;" u2="]" k="36" />
<hkern u1="&#xc0;" u2="\" k="109" />
<hkern u1="&#xc0;" u2="&#x3f;" k="40" />
<hkern u1="&#xc0;" u2="&#x2a;" k="134" />
<hkern u1="&#xc0;" u2="&#x20;" k="65" />
<hkern u1="&#xc1;" u2="&#x2122;" k="143" />
<hkern u1="&#xc1;" u2="&#xf0;" k="30" />
<hkern u1="&#xc1;" u2="&#xd0;" k="8" />
<hkern u1="&#xc1;" u2="&#xba;" k="127" />
<hkern u1="&#xc1;" u2="&#xae;" k="48" />
<hkern u1="&#xc1;" u2="&#xaa;" k="115" />
<hkern u1="&#xc1;" u2="&#xa9;" k="47" />
<hkern u1="&#xc1;" u2="&#x7d;" k="18" />
<hkern u1="&#xc1;" u2="&#x7c;" k="20" />
<hkern u1="&#xc1;" u2="]" k="36" />
<hkern u1="&#xc1;" u2="\" k="109" />
<hkern u1="&#xc1;" u2="&#x3f;" k="40" />
<hkern u1="&#xc1;" u2="&#x2a;" k="134" />
<hkern u1="&#xc1;" u2="&#x20;" k="65" />
<hkern u1="&#xc2;" u2="&#x2122;" k="143" />
<hkern u1="&#xc2;" u2="&#xf0;" k="30" />
<hkern u1="&#xc2;" u2="&#xd0;" k="8" />
<hkern u1="&#xc2;" u2="&#xba;" k="127" />
<hkern u1="&#xc2;" u2="&#xae;" k="48" />
<hkern u1="&#xc2;" u2="&#xaa;" k="115" />
<hkern u1="&#xc2;" u2="&#xa9;" k="47" />
<hkern u1="&#xc2;" u2="&#x7d;" k="18" />
<hkern u1="&#xc2;" u2="&#x7c;" k="20" />
<hkern u1="&#xc2;" u2="]" k="36" />
<hkern u1="&#xc2;" u2="\" k="109" />
<hkern u1="&#xc2;" u2="&#x3f;" k="40" />
<hkern u1="&#xc2;" u2="&#x2a;" k="134" />
<hkern u1="&#xc2;" u2="&#x20;" k="65" />
<hkern u1="&#xc3;" u2="&#x2122;" k="143" />
<hkern u1="&#xc3;" u2="&#xf0;" k="30" />
<hkern u1="&#xc3;" u2="&#xd0;" k="8" />
<hkern u1="&#xc3;" u2="&#xba;" k="127" />
<hkern u1="&#xc3;" u2="&#xae;" k="48" />
<hkern u1="&#xc3;" u2="&#xaa;" k="115" />
<hkern u1="&#xc3;" u2="&#xa9;" k="47" />
<hkern u1="&#xc3;" u2="&#x7d;" k="18" />
<hkern u1="&#xc3;" u2="&#x7c;" k="20" />
<hkern u1="&#xc3;" u2="]" k="36" />
<hkern u1="&#xc3;" u2="\" k="109" />
<hkern u1="&#xc3;" u2="&#x3f;" k="40" />
<hkern u1="&#xc3;" u2="&#x2a;" k="134" />
<hkern u1="&#xc3;" u2="&#x20;" k="65" />
<hkern u1="&#xc4;" u2="&#x2122;" k="143" />
<hkern u1="&#xc4;" u2="&#xf0;" k="30" />
<hkern u1="&#xc4;" u2="&#xd0;" k="8" />
<hkern u1="&#xc4;" u2="&#xba;" k="127" />
<hkern u1="&#xc4;" u2="&#xae;" k="48" />
<hkern u1="&#xc4;" u2="&#xaa;" k="115" />
<hkern u1="&#xc4;" u2="&#xa9;" k="47" />
<hkern u1="&#xc4;" u2="&#x7d;" k="18" />
<hkern u1="&#xc4;" u2="&#x7c;" k="20" />
<hkern u1="&#xc4;" u2="]" k="36" />
<hkern u1="&#xc4;" u2="\" k="109" />
<hkern u1="&#xc4;" u2="&#x3f;" k="40" />
<hkern u1="&#xc4;" u2="&#x2a;" k="134" />
<hkern u1="&#xc4;" u2="&#x20;" k="65" />
<hkern u1="&#xc5;" u2="&#x2122;" k="143" />
<hkern u1="&#xc5;" u2="&#xf0;" k="30" />
<hkern u1="&#xc5;" u2="&#xd0;" k="8" />
<hkern u1="&#xc5;" u2="&#xba;" k="127" />
<hkern u1="&#xc5;" u2="&#xae;" k="48" />
<hkern u1="&#xc5;" u2="&#xaa;" k="115" />
<hkern u1="&#xc5;" u2="&#xa9;" k="47" />
<hkern u1="&#xc5;" u2="&#x7d;" k="18" />
<hkern u1="&#xc5;" u2="&#x7c;" k="20" />
<hkern u1="&#xc5;" u2="]" k="36" />
<hkern u1="&#xc5;" u2="\" k="109" />
<hkern u1="&#xc5;" u2="&#x3f;" k="40" />
<hkern u1="&#xc5;" u2="&#x2a;" k="134" />
<hkern u1="&#xc5;" u2="&#x20;" k="65" />
<hkern u1="&#xc6;" u2="&#xf0;" k="53" />
<hkern u1="&#xc6;" u2="&#xef;" k="-15" />
<hkern u1="&#xc6;" u2="&#xee;" k="-16" />
<hkern u1="&#xc6;" u2="&#xec;" k="-22" />
<hkern u1="&#xc7;" u2="&#xf0;" k="29" />
<hkern u1="&#xc7;" u2="&#xef;" k="-42" />
<hkern u1="&#xc7;" u2="&#xee;" k="-24" />
<hkern u1="&#xc8;" u2="&#xf0;" k="53" />
<hkern u1="&#xc8;" u2="&#xef;" k="-15" />
<hkern u1="&#xc8;" u2="&#xee;" k="-16" />
<hkern u1="&#xc8;" u2="&#xec;" k="-22" />
<hkern u1="&#xc9;" u2="&#xf0;" k="53" />
<hkern u1="&#xc9;" u2="&#xef;" k="-15" />
<hkern u1="&#xc9;" u2="&#xee;" k="-16" />
<hkern u1="&#xc9;" u2="&#xec;" k="-22" />
<hkern u1="&#xca;" u2="&#xf0;" k="53" />
<hkern u1="&#xca;" u2="&#xef;" k="-15" />
<hkern u1="&#xca;" u2="&#xee;" k="-16" />
<hkern u1="&#xca;" u2="&#xec;" k="-22" />
<hkern u1="&#xcb;" u2="&#xf0;" k="53" />
<hkern u1="&#xcb;" u2="&#xef;" k="-15" />
<hkern u1="&#xcb;" u2="&#xee;" k="-16" />
<hkern u1="&#xcb;" u2="&#xec;" k="-22" />
<hkern u1="&#xcc;" u2="&#xf0;" k="33" />
<hkern u1="&#xcd;" u2="&#xf0;" k="33" />
<hkern u1="&#xce;" u2="&#xf0;" k="33" />
<hkern u1="&#xce;" u2="&#x7d;" k="-43" />
<hkern u1="&#xce;" u2="&#x7c;" k="-46" />
<hkern u1="&#xce;" u2="]" k="-47" />
<hkern u1="&#xcf;" u2="&#xf0;" k="33" />
<hkern u1="&#xcf;" u2="&#x7d;" k="-36" />
<hkern u1="&#xcf;" u2="&#x7c;" k="-35" />
<hkern u1="&#xcf;" u2="]" k="-39" />
<hkern u1="&#xd0;" u2="&#x2122;" k="36" />
<hkern u1="&#xd0;" u2="&#xf0;" k="13" />
<hkern u1="&#xd0;" u2="&#xd0;" k="9" />
<hkern u1="&#xd0;" u2="&#x7d;" k="39" />
<hkern u1="&#xd0;" u2="]" k="47" />
<hkern u1="&#xd0;" u2="&#x2c;" k="58" />
<hkern u1="&#xd0;" u2="&#x29;" k="39" />
<hkern u1="&#xd1;" u2="&#xf0;" k="33" />
<hkern u1="&#xd2;" u2="&#x2122;" k="34" />
<hkern u1="&#xd2;" u2="&#xf0;" k="13" />
<hkern u1="&#xd2;" u2="&#xd0;" k="9" />
<hkern u1="&#xd2;" u2="&#x7d;" k="39" />
<hkern u1="&#xd2;" u2="]" k="45" />
<hkern u1="&#xd2;" u2="&#x2c;" k="57" />
<hkern u1="&#xd2;" u2="&#x29;" k="38" />
<hkern u1="&#xd3;" u2="&#x2122;" k="34" />
<hkern u1="&#xd3;" u2="&#xf0;" k="13" />
<hkern u1="&#xd3;" u2="&#xd0;" k="9" />
<hkern u1="&#xd3;" u2="&#x7d;" k="39" />
<hkern u1="&#xd3;" u2="]" k="45" />
<hkern u1="&#xd3;" u2="&#x2c;" k="57" />
<hkern u1="&#xd3;" u2="&#x29;" k="38" />
<hkern u1="&#xd4;" u2="&#x2122;" k="34" />
<hkern u1="&#xd4;" u2="&#xf0;" k="13" />
<hkern u1="&#xd4;" u2="&#xd0;" k="9" />
<hkern u1="&#xd4;" u2="&#x7d;" k="39" />
<hkern u1="&#xd4;" u2="]" k="45" />
<hkern u1="&#xd4;" u2="&#x2c;" k="57" />
<hkern u1="&#xd4;" u2="&#x29;" k="38" />
<hkern u1="&#xd5;" u2="&#x2122;" k="34" />
<hkern u1="&#xd5;" u2="&#xf0;" k="13" />
<hkern u1="&#xd5;" u2="&#xd0;" k="9" />
<hkern u1="&#xd5;" u2="&#x7d;" k="39" />
<hkern u1="&#xd5;" u2="]" k="45" />
<hkern u1="&#xd5;" u2="&#x2c;" k="57" />
<hkern u1="&#xd5;" u2="&#x29;" k="38" />
<hkern u1="&#xd6;" u2="&#x2122;" k="34" />
<hkern u1="&#xd6;" u2="&#xf0;" k="13" />
<hkern u1="&#xd6;" u2="&#xd0;" k="9" />
<hkern u1="&#xd6;" u2="&#x7d;" k="39" />
<hkern u1="&#xd6;" u2="]" k="45" />
<hkern u1="&#xd6;" u2="&#x2c;" k="57" />
<hkern u1="&#xd6;" u2="&#x29;" k="38" />
<hkern u1="&#xd8;" u2="&#x2122;" k="34" />
<hkern u1="&#xd8;" u2="&#x178;" k="81" />
<hkern u1="&#xd8;" u2="&#xf0;" k="13" />
<hkern u1="&#xd8;" u2="&#xdd;" k="81" />
<hkern u1="&#xd8;" u2="&#xd0;" k="9" />
<hkern u1="&#xd8;" u2="&#x7d;" k="39" />
<hkern u1="&#xd8;" u2="]" k="45" />
<hkern u1="&#xd8;" u2="Y" k="81" />
<hkern u1="&#xd8;" u2="&#x2c;" k="57" />
<hkern u1="&#xd8;" u2="&#x29;" k="38" />
<hkern u1="&#xd9;" u2="&#xf0;" k="39" />
<hkern u1="&#xd9;" u2="&#xee;" k="8" />
<hkern u1="&#xd9;" u2="&#xed;" k="27" />
<hkern u1="&#xd9;" u2="&#xec;" k="13" />
<hkern u1="&#xd9;" u2="&#x2f;" k="20" />
<hkern u1="&#xd9;" u2="&#x2c;" k="54" />
<hkern u1="&#xda;" u2="&#xf0;" k="39" />
<hkern u1="&#xda;" u2="&#xee;" k="8" />
<hkern u1="&#xda;" u2="&#xed;" k="27" />
<hkern u1="&#xda;" u2="&#xec;" k="13" />
<hkern u1="&#xda;" u2="&#x2f;" k="20" />
<hkern u1="&#xda;" u2="&#x2c;" k="54" />
<hkern u1="&#xdb;" u2="&#xf0;" k="39" />
<hkern u1="&#xdb;" u2="&#xee;" k="8" />
<hkern u1="&#xdb;" u2="&#xed;" k="27" />
<hkern u1="&#xdb;" u2="&#xec;" k="13" />
<hkern u1="&#xdb;" u2="&#x2f;" k="20" />
<hkern u1="&#xdb;" u2="&#x2c;" k="54" />
<hkern u1="&#xdc;" u2="&#xf0;" k="39" />
<hkern u1="&#xdc;" u2="&#xee;" k="8" />
<hkern u1="&#xdc;" u2="&#xed;" k="27" />
<hkern u1="&#xdc;" u2="&#xec;" k="13" />
<hkern u1="&#xdc;" u2="&#x2f;" k="20" />
<hkern u1="&#xdc;" u2="&#x2c;" k="54" />
<hkern u1="&#xdd;" g2="uniFB02" k="92" />
<hkern u1="&#xdd;" u2="&#x2122;" k="-35" />
<hkern u1="&#xdd;" u2="&#xff;" k="95" />
<hkern u1="&#xdd;" u2="&#xf9;" k="146" />
<hkern u1="&#xdd;" u2="&#xf6;" k="181" />
<hkern u1="&#xdd;" u2="&#xf2;" k="171" />
<hkern u1="&#xdd;" u2="&#xf0;" k="151" />
<hkern u1="&#xdd;" u2="&#xef;" k="-40" />
<hkern u1="&#xdd;" u2="&#xed;" k="51" />
<hkern u1="&#xdd;" u2="&#xec;" k="-73" />
<hkern u1="&#xdd;" u2="&#xeb;" k="175" />
<hkern u1="&#xdd;" u2="&#xe8;" k="152" />
<hkern u1="&#xdd;" u2="&#xdf;" k="103" />
<hkern u1="&#xdd;" u2="&#xae;" k="25" />
<hkern u1="&#xdd;" u2="&#xa9;" k="25" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-7" />
<hkern u1="&#xdd;" u2="]" k="-11" />
<hkern u1="&#xdd;" u2="\" k="-9" />
<hkern u1="&#xdd;" u2="&#x40;" k="79" />
<hkern u1="&#xdd;" u2="&#x3b;" k="73" />
<hkern u1="&#xdd;" u2="&#x3a;" k="73" />
<hkern u1="&#xdd;" u2="&#x2f;" k="133" />
<hkern u1="&#xdd;" u2="&#x2c;" k="163" />
<hkern u1="&#xdd;" u2="&#x26;" k="69" />
<hkern u1="&#xdd;" u2="&#x20;" k="69" />
<hkern u1="&#xde;" u2="&#x2122;" k="37" />
<hkern u1="&#xde;" u2="&#x2026;" k="75" />
<hkern u1="&#xde;" u2="&#x201e;" k="72" />
<hkern u1="&#xde;" u2="&#x201d;" k="24" />
<hkern u1="&#xde;" u2="&#x201c;" k="30" />
<hkern u1="&#xde;" u2="&#x201a;" k="72" />
<hkern u1="&#xde;" u2="&#x2019;" k="24" />
<hkern u1="&#xde;" u2="&#x2018;" k="30" />
<hkern u1="&#xde;" u2="&#x178;" k="101" />
<hkern u1="&#xde;" u2="&#xfe;" k="5" />
<hkern u1="&#xde;" u2="&#xf1;" k="5" />
<hkern u1="&#xde;" u2="&#xef;" k="5" />
<hkern u1="&#xde;" u2="&#xee;" k="5" />
<hkern u1="&#xde;" u2="&#xed;" k="5" />
<hkern u1="&#xde;" u2="&#xec;" k="5" />
<hkern u1="&#xde;" u2="&#xe6;" k="4" />
<hkern u1="&#xde;" u2="&#xe5;" k="4" />
<hkern u1="&#xde;" u2="&#xe4;" k="4" />
<hkern u1="&#xde;" u2="&#xe3;" k="4" />
<hkern u1="&#xde;" u2="&#xe2;" k="4" />
<hkern u1="&#xde;" u2="&#xe1;" k="4" />
<hkern u1="&#xde;" u2="&#xe0;" k="4" />
<hkern u1="&#xde;" u2="&#xdd;" k="101" />
<hkern u1="&#xde;" u2="&#xc5;" k="38" />
<hkern u1="&#xde;" u2="&#xc4;" k="38" />
<hkern u1="&#xde;" u2="&#xc3;" k="38" />
<hkern u1="&#xde;" u2="&#xc2;" k="38" />
<hkern u1="&#xde;" u2="&#xc1;" k="38" />
<hkern u1="&#xde;" u2="&#xc0;" k="38" />
<hkern u1="&#xde;" u2="&#x7d;" k="15" />
<hkern u1="&#xde;" u2="z" k="4" />
<hkern u1="&#xde;" u2="x" k="22" />
<hkern u1="&#xde;" u2="r" k="5" />
<hkern u1="&#xde;" u2="q" k="4" />
<hkern u1="&#xde;" u2="p" k="5" />
<hkern u1="&#xde;" u2="n" k="5" />
<hkern u1="&#xde;" u2="m" k="5" />
<hkern u1="&#xde;" u2="l" k="5" />
<hkern u1="&#xde;" u2="k" k="5" />
<hkern u1="&#xde;" u2="j" k="5" />
<hkern u1="&#xde;" u2="i" k="5" />
<hkern u1="&#xde;" u2="h" k="5" />
<hkern u1="&#xde;" u2="b" k="5" />
<hkern u1="&#xde;" u2="a" k="4" />
<hkern u1="&#xde;" u2="]" k="40" />
<hkern u1="&#xde;" u2="Z" k="45" />
<hkern u1="&#xde;" u2="Y" k="101" />
<hkern u1="&#xde;" u2="X" k="73" />
<hkern u1="&#xde;" u2="W" k="23" />
<hkern u1="&#xde;" u2="V" k="50" />
<hkern u1="&#xde;" u2="T" k="82" />
<hkern u1="&#xde;" u2="S" k="5" />
<hkern u1="&#xde;" u2="J" k="82" />
<hkern u1="&#xde;" u2="A" k="38" />
<hkern u1="&#xde;" u2="&#x3f;" k="17" />
<hkern u1="&#xde;" u2="&#x2e;" k="75" />
<hkern u1="&#xde;" u2="&#x2c;" k="63" />
<hkern u1="&#xde;" u2="&#x2a;" k="18" />
<hkern u1="&#xde;" u2="&#x29;" k="20" />
<hkern u1="&#xdf;" g2="uniFB04" k="22" />
<hkern u1="&#xdf;" g2="uniFB03" k="22" />
<hkern u1="&#xdf;" g2="uniFB02" k="22" />
<hkern u1="&#xdf;" g2="uniFB01" k="22" />
<hkern u1="&#xdf;" u2="&#x2122;" k="22" />
<hkern u1="&#xdf;" u2="&#x201d;" k="14" />
<hkern u1="&#xdf;" u2="&#x2019;" k="14" />
<hkern u1="&#xdf;" u2="&#xff;" k="33" />
<hkern u1="&#xdf;" u2="&#xfd;" k="33" />
<hkern u1="&#xdf;" u2="&#xdf;" k="22" />
<hkern u1="&#xdf;" u2="&#x7d;" k="38" />
<hkern u1="&#xdf;" u2="y" k="33" />
<hkern u1="&#xdf;" u2="x" k="8" />
<hkern u1="&#xdf;" u2="w" k="18" />
<hkern u1="&#xdf;" u2="v" k="33" />
<hkern u1="&#xdf;" u2="t" k="20" />
<hkern u1="&#xdf;" u2="g" k="9" />
<hkern u1="&#xdf;" u2="f" k="22" />
<hkern u1="&#xdf;" u2="]" k="42" />
<hkern u1="&#xdf;" u2="\" k="19" />
<hkern u1="&#xdf;" u2="&#x2a;" k="16" />
<hkern u1="&#xe0;" u2="&#x2122;" k="37" />
<hkern u1="&#xe0;" u2="&#x7d;" k="42" />
<hkern u1="&#xe0;" u2="]" k="49" />
<hkern u1="&#xe0;" u2="\" k="51" />
<hkern u1="&#xe0;" u2="&#x3f;" k="20" />
<hkern u1="&#xe0;" u2="&#x2a;" k="17" />
<hkern u1="&#xe1;" u2="&#x2122;" k="37" />
<hkern u1="&#xe1;" u2="&#x7d;" k="42" />
<hkern u1="&#xe1;" u2="]" k="49" />
<hkern u1="&#xe1;" u2="\" k="44" />
<hkern u1="&#xe1;" u2="&#x3f;" k="20" />
<hkern u1="&#xe1;" u2="&#x2a;" k="17" />
<hkern u1="&#xe2;" u2="&#x2122;" k="37" />
<hkern u1="&#xe2;" u2="&#x7d;" k="42" />
<hkern u1="&#xe2;" u2="]" k="49" />
<hkern u1="&#xe2;" u2="\" k="51" />
<hkern u1="&#xe2;" u2="&#x3f;" k="20" />
<hkern u1="&#xe2;" u2="&#x2a;" k="17" />
<hkern u1="&#xe3;" u2="&#x2122;" k="37" />
<hkern u1="&#xe3;" u2="&#x7d;" k="42" />
<hkern u1="&#xe3;" u2="]" k="49" />
<hkern u1="&#xe3;" u2="\" k="51" />
<hkern u1="&#xe3;" u2="&#x3f;" k="20" />
<hkern u1="&#xe3;" u2="&#x2a;" k="17" />
<hkern u1="&#xe4;" u2="&#x2122;" k="37" />
<hkern u1="&#xe4;" u2="&#x7d;" k="42" />
<hkern u1="&#xe4;" u2="]" k="49" />
<hkern u1="&#xe4;" u2="\" k="51" />
<hkern u1="&#xe4;" u2="&#x3f;" k="20" />
<hkern u1="&#xe4;" u2="&#x2a;" k="17" />
<hkern u1="&#xe5;" u2="&#x2122;" k="37" />
<hkern u1="&#xe5;" u2="&#x7d;" k="42" />
<hkern u1="&#xe5;" u2="]" k="49" />
<hkern u1="&#xe5;" u2="\" k="51" />
<hkern u1="&#xe5;" u2="&#x3f;" k="20" />
<hkern u1="&#xe5;" u2="&#x2a;" k="17" />
<hkern u1="&#xe6;" u2="&#x2122;" k="52" />
<hkern u1="&#xe6;" u2="&#xf0;" k="5" />
<hkern u1="&#xe6;" u2="&#xba;" k="36" />
<hkern u1="&#xe6;" u2="&#xaa;" k="16" />
<hkern u1="&#xe6;" u2="&#x7d;" k="50" />
<hkern u1="&#xe6;" u2="]" k="55" />
<hkern u1="&#xe6;" u2="\" k="68" />
<hkern u1="&#xe6;" u2="&#x3f;" k="46" />
<hkern u1="&#xe6;" u2="&#x2a;" k="54" />
<hkern u1="&#xe6;" u2="&#x29;" k="39" />
<hkern u1="&#xe7;" u2="&#x2122;" k="47" />
<hkern u1="&#xe7;" u2="&#xf0;" k="23" />
<hkern u1="&#xe7;" u2="&#xba;" k="13" />
<hkern u1="&#xe7;" u2="&#x7d;" k="48" />
<hkern u1="&#xe7;" u2="]" k="53" />
<hkern u1="&#xe7;" u2="\" k="58" />
<hkern u1="&#xe7;" u2="&#x3f;" k="40" />
<hkern u1="&#xe7;" u2="&#x2a;" k="44" />
<hkern u1="&#xe7;" u2="&#x29;" k="16" />
<hkern u1="&#xe8;" u2="&#x2122;" k="52" />
<hkern u1="&#xe8;" u2="&#xf0;" k="5" />
<hkern u1="&#xe8;" u2="&#xba;" k="36" />
<hkern u1="&#xe8;" u2="&#xaa;" k="16" />
<hkern u1="&#xe8;" u2="&#x7d;" k="50" />
<hkern u1="&#xe8;" u2="]" k="55" />
<hkern u1="&#xe8;" u2="\" k="68" />
<hkern u1="&#xe8;" u2="&#x3f;" k="46" />
<hkern u1="&#xe8;" u2="&#x2a;" k="54" />
<hkern u1="&#xe8;" u2="&#x29;" k="39" />
<hkern u1="&#xe9;" u2="&#x2122;" k="52" />
<hkern u1="&#xe9;" u2="&#xf0;" k="5" />
<hkern u1="&#xe9;" u2="&#xba;" k="36" />
<hkern u1="&#xe9;" u2="&#xaa;" k="16" />
<hkern u1="&#xe9;" u2="&#x7d;" k="50" />
<hkern u1="&#xe9;" u2="]" k="55" />
<hkern u1="&#xe9;" u2="\" k="68" />
<hkern u1="&#xe9;" u2="&#x3f;" k="46" />
<hkern u1="&#xe9;" u2="&#x2a;" k="54" />
<hkern u1="&#xe9;" u2="&#x29;" k="39" />
<hkern u1="&#xea;" u2="&#x2122;" k="52" />
<hkern u1="&#xea;" u2="&#xf0;" k="5" />
<hkern u1="&#xea;" u2="&#xba;" k="36" />
<hkern u1="&#xea;" u2="&#xaa;" k="16" />
<hkern u1="&#xea;" u2="&#x7d;" k="50" />
<hkern u1="&#xea;" u2="]" k="55" />
<hkern u1="&#xea;" u2="\" k="68" />
<hkern u1="&#xea;" u2="&#x3f;" k="46" />
<hkern u1="&#xea;" u2="&#x2a;" k="54" />
<hkern u1="&#xea;" u2="&#x29;" k="39" />
<hkern u1="&#xeb;" u2="&#x2122;" k="52" />
<hkern u1="&#xeb;" u2="&#xf0;" k="5" />
<hkern u1="&#xeb;" u2="&#xba;" k="36" />
<hkern u1="&#xeb;" u2="&#xaa;" k="16" />
<hkern u1="&#xeb;" u2="&#x7d;" k="50" />
<hkern u1="&#xeb;" u2="]" k="55" />
<hkern u1="&#xeb;" u2="\" k="68" />
<hkern u1="&#xeb;" u2="&#x3f;" k="46" />
<hkern u1="&#xeb;" u2="&#x2a;" k="54" />
<hkern u1="&#xeb;" u2="&#x29;" k="39" />
<hkern u1="&#xec;" u2="&#xef;" k="-11" />
<hkern u1="&#xed;" u2="&#x2122;" k="-43" />
<hkern u1="&#xed;" u2="&#x201d;" k="-24" />
<hkern u1="&#xed;" u2="&#x2019;" k="-24" />
<hkern u1="&#xed;" u2="&#xfe;" k="-9" />
<hkern u1="&#xed;" u2="&#xef;" k="-11" />
<hkern u1="&#xed;" u2="&#x7d;" k="-48" />
<hkern u1="&#xed;" u2="&#x7c;" k="-46" />
<hkern u1="&#xed;" u2="l" k="-9" />
<hkern u1="&#xed;" u2="k" k="-9" />
<hkern u1="&#xed;" u2="h" k="-9" />
<hkern u1="&#xed;" u2="b" k="-9" />
<hkern u1="&#xed;" u2="]" k="-52" />
<hkern u1="&#xed;" u2="\" k="-44" />
<hkern u1="&#xed;" u2="&#x29;" k="-44" />
<hkern u1="&#xed;" u2="&#x27;" k="-26" />
<hkern u1="&#xed;" u2="&#x22;" k="-26" />
<hkern u1="&#xee;" u2="&#x2122;" k="-13" />
<hkern u1="&#xee;" u2="&#x201d;" k="-8" />
<hkern u1="&#xee;" u2="&#x201c;" k="-49" />
<hkern u1="&#xee;" u2="&#x2019;" k="-8" />
<hkern u1="&#xee;" u2="&#x2018;" k="-49" />
<hkern u1="&#xee;" u2="&#xfe;" k="-15" />
<hkern u1="&#xee;" u2="&#xef;" k="-11" />
<hkern u1="&#xee;" u2="&#xba;" k="-54" />
<hkern u1="&#xee;" u2="&#x7c;" k="-32" />
<hkern u1="&#xee;" u2="l" k="-15" />
<hkern u1="&#xee;" u2="k" k="-15" />
<hkern u1="&#xee;" u2="h" k="-15" />
<hkern u1="&#xee;" u2="b" k="-15" />
<hkern u1="&#xee;" u2="&#x2a;" k="-10" />
<hkern u1="&#xee;" u2="&#x27;" k="-40" />
<hkern u1="&#xee;" u2="&#x22;" k="-40" />
<hkern u1="&#xee;" u2="&#x21;" k="-10" />
<hkern u1="&#xef;" u2="&#x2122;" k="-52" />
<hkern u1="&#xef;" u2="&#x201c;" k="-42" />
<hkern u1="&#xef;" u2="&#x2018;" k="-42" />
<hkern u1="&#xef;" u2="&#xfe;" k="-10" />
<hkern u1="&#xef;" u2="&#xef;" k="-11" />
<hkern u1="&#xef;" u2="&#xee;" k="-23" />
<hkern u1="&#xef;" u2="&#xed;" k="-23" />
<hkern u1="&#xef;" u2="&#xec;" k="-23" />
<hkern u1="&#xef;" u2="&#xba;" k="-43" />
<hkern u1="&#xef;" u2="&#xaa;" k="-13" />
<hkern u1="&#xef;" u2="&#x7d;" k="-25" />
<hkern u1="&#xef;" u2="&#x7c;" k="-34" />
<hkern u1="&#xef;" u2="l" k="-10" />
<hkern u1="&#xef;" u2="k" k="-10" />
<hkern u1="&#xef;" u2="j" k="-30" />
<hkern u1="&#xef;" u2="i" k="-23" />
<hkern u1="&#xef;" u2="h" k="-10" />
<hkern u1="&#xef;" u2="b" k="-10" />
<hkern u1="&#xef;" u2="]" k="-24" />
<hkern u1="&#xef;" u2="\" k="-24" />
<hkern u1="&#xef;" u2="&#x3f;" k="-44" />
<hkern u1="&#xef;" u2="&#x2a;" k="-49" />
<hkern u1="&#xef;" u2="&#x29;" k="-23" />
<hkern u1="&#xef;" u2="&#x27;" k="-34" />
<hkern u1="&#xef;" u2="&#x22;" k="-34" />
<hkern u1="&#xf0;" g2="uniFB04" k="8" />
<hkern u1="&#xf0;" g2="uniFB03" k="8" />
<hkern u1="&#xf0;" g2="uniFB02" k="8" />
<hkern u1="&#xf0;" g2="uniFB01" k="8" />
<hkern u1="&#xf0;" u2="&#x2122;" k="32" />
<hkern u1="&#xf0;" u2="&#x201d;" k="16" />
<hkern u1="&#xf0;" u2="&#x201c;" k="16" />
<hkern u1="&#xf0;" u2="&#x2019;" k="16" />
<hkern u1="&#xf0;" u2="&#x2018;" k="16" />
<hkern u1="&#xf0;" u2="&#xff;" k="10" />
<hkern u1="&#xf0;" u2="&#xfd;" k="10" />
<hkern u1="&#xf0;" u2="&#xdf;" k="8" />
<hkern u1="&#xf0;" u2="&#x7d;" k="41" />
<hkern u1="&#xf0;" u2="z" k="12" />
<hkern u1="&#xf0;" u2="y" k="10" />
<hkern u1="&#xf0;" u2="x" k="34" />
<hkern u1="&#xf0;" u2="v" k="10" />
<hkern u1="&#xf0;" u2="f" k="8" />
<hkern u1="&#xf0;" u2="]" k="47" />
<hkern u1="&#xf0;" u2="\" k="16" />
<hkern u1="&#xf0;" u2="&#x3f;" k="15" />
<hkern u1="&#xf0;" u2="&#x2a;" k="31" />
<hkern u1="&#xf0;" u2="&#x29;" k="40" />
<hkern u1="&#xf1;" u2="&#x2122;" k="54" />
<hkern u1="&#xf1;" u2="&#xba;" k="39" />
<hkern u1="&#xf1;" u2="&#xaa;" k="17" />
<hkern u1="&#xf1;" u2="&#x7d;" k="47" />
<hkern u1="&#xf1;" u2="]" k="52" />
<hkern u1="&#xf1;" u2="\" k="72" />
<hkern u1="&#xf1;" u2="&#x3f;" k="48" />
<hkern u1="&#xf1;" u2="&#x2a;" k="54" />
<hkern u1="&#xf1;" u2="&#x29;" k="15" />
<hkern u1="&#xf2;" u2="&#x2122;" k="63" />
<hkern u1="&#xf2;" u2="&#xba;" k="51" />
<hkern u1="&#xf2;" u2="&#xaa;" k="37" />
<hkern u1="&#xf2;" u2="&#x7d;" k="55" />
<hkern u1="&#xf2;" u2="&#x7c;" k="19" />
<hkern u1="&#xf2;" u2="]" k="62" />
<hkern u1="&#xf2;" u2="\" k="80" />
<hkern u1="&#xf2;" u2="&#x3f;" k="59" />
<hkern u1="&#xf2;" u2="&#x2a;" k="72" />
<hkern u1="&#xf2;" u2="&#x29;" k="56" />
<hkern u1="&#xf3;" u2="&#x2122;" k="63" />
<hkern u1="&#xf3;" u2="&#xba;" k="51" />
<hkern u1="&#xf3;" u2="&#xaa;" k="37" />
<hkern u1="&#xf3;" u2="&#x7d;" k="55" />
<hkern u1="&#xf3;" u2="&#x7c;" k="19" />
<hkern u1="&#xf3;" u2="]" k="62" />
<hkern u1="&#xf3;" u2="\" k="80" />
<hkern u1="&#xf3;" u2="&#x3f;" k="59" />
<hkern u1="&#xf3;" u2="&#x2a;" k="72" />
<hkern u1="&#xf3;" u2="&#x29;" k="56" />
<hkern u1="&#xf4;" u2="&#x2122;" k="63" />
<hkern u1="&#xf4;" u2="&#xba;" k="51" />
<hkern u1="&#xf4;" u2="&#xaa;" k="37" />
<hkern u1="&#xf4;" u2="&#x7d;" k="55" />
<hkern u1="&#xf4;" u2="&#x7c;" k="19" />
<hkern u1="&#xf4;" u2="]" k="62" />
<hkern u1="&#xf4;" u2="\" k="80" />
<hkern u1="&#xf4;" u2="&#x3f;" k="59" />
<hkern u1="&#xf4;" u2="&#x2a;" k="72" />
<hkern u1="&#xf4;" u2="&#x29;" k="56" />
<hkern u1="&#xf5;" u2="&#x2122;" k="63" />
<hkern u1="&#xf5;" u2="&#xba;" k="51" />
<hkern u1="&#xf5;" u2="&#xaa;" k="37" />
<hkern u1="&#xf5;" u2="&#x7d;" k="55" />
<hkern u1="&#xf5;" u2="&#x7c;" k="19" />
<hkern u1="&#xf5;" u2="]" k="62" />
<hkern u1="&#xf5;" u2="\" k="80" />
<hkern u1="&#xf5;" u2="&#x3f;" k="59" />
<hkern u1="&#xf5;" u2="&#x2a;" k="72" />
<hkern u1="&#xf5;" u2="&#x29;" k="56" />
<hkern u1="&#xf6;" u2="&#x2122;" k="63" />
<hkern u1="&#xf6;" u2="&#xba;" k="51" />
<hkern u1="&#xf6;" u2="&#xaa;" k="37" />
<hkern u1="&#xf6;" u2="&#x7d;" k="55" />
<hkern u1="&#xf6;" u2="&#x7c;" k="19" />
<hkern u1="&#xf6;" u2="]" k="62" />
<hkern u1="&#xf6;" u2="\" k="80" />
<hkern u1="&#xf6;" u2="&#x3f;" k="59" />
<hkern u1="&#xf6;" u2="&#x2a;" k="72" />
<hkern u1="&#xf6;" u2="&#x29;" k="56" />
<hkern u1="&#xf8;" u2="&#x2122;" k="63" />
<hkern u1="&#xf8;" u2="&#xba;" k="44" />
<hkern u1="&#xf8;" u2="&#xaa;" k="37" />
<hkern u1="&#xf8;" u2="&#x7d;" k="55" />
<hkern u1="&#xf8;" u2="&#x7c;" k="19" />
<hkern u1="&#xf8;" u2="]" k="62" />
<hkern u1="&#xf8;" u2="\" k="80" />
<hkern u1="&#xf8;" u2="&#x3f;" k="59" />
<hkern u1="&#xf8;" u2="&#x2a;" k="60" />
<hkern u1="&#xf8;" u2="&#x29;" k="56" />
<hkern u1="&#xf9;" u2="&#x2122;" k="35" />
<hkern u1="&#xf9;" u2="&#x7d;" k="42" />
<hkern u1="&#xf9;" u2="]" k="48" />
<hkern u1="&#xf9;" u2="\" k="47" />
<hkern u1="&#xf9;" u2="&#x3f;" k="18" />
<hkern u1="&#xf9;" u2="&#x2a;" k="13" />
<hkern u1="&#xfa;" u2="&#x2122;" k="35" />
<hkern u1="&#xfa;" u2="&#x7d;" k="42" />
<hkern u1="&#xfa;" u2="]" k="48" />
<hkern u1="&#xfa;" u2="\" k="47" />
<hkern u1="&#xfa;" u2="&#x3f;" k="18" />
<hkern u1="&#xfa;" u2="&#x2a;" k="13" />
<hkern u1="&#xfb;" u2="&#x2122;" k="35" />
<hkern u1="&#xfb;" u2="&#x7d;" k="42" />
<hkern u1="&#xfb;" u2="]" k="48" />
<hkern u1="&#xfb;" u2="\" k="47" />
<hkern u1="&#xfb;" u2="&#x3f;" k="18" />
<hkern u1="&#xfb;" u2="&#x2a;" k="13" />
<hkern u1="&#xfc;" u2="&#x2122;" k="35" />
<hkern u1="&#xfc;" u2="&#x7d;" k="42" />
<hkern u1="&#xfc;" u2="]" k="48" />
<hkern u1="&#xfc;" u2="\" k="47" />
<hkern u1="&#xfc;" u2="&#x3f;" k="18" />
<hkern u1="&#xfc;" u2="&#x2a;" k="13" />
<hkern u1="&#xfd;" u2="&#xf0;" k="35" />
<hkern u1="&#xfd;" u2="&#x7d;" k="39" />
<hkern u1="&#xfd;" u2="]" k="47" />
<hkern u1="&#xfd;" u2="&#x2f;" k="66" />
<hkern u1="&#xfd;" u2="&#x2c;" k="81" />
<hkern u1="&#xfd;" u2="&#x29;" k="18" />
<hkern u1="&#xfd;" u2="&#x20;" k="45" />
<hkern u1="&#xfe;" u2="&#x2122;" k="59" />
<hkern u1="&#xfe;" u2="&#xba;" k="45" />
<hkern u1="&#xfe;" u2="&#xaa;" k="33" />
<hkern u1="&#xfe;" u2="&#x7d;" k="56" />
<hkern u1="&#xfe;" u2="&#x7c;" k="18" />
<hkern u1="&#xfe;" u2="]" k="62" />
<hkern u1="&#xfe;" u2="\" k="74" />
<hkern u1="&#xfe;" u2="&#x3f;" k="59" />
<hkern u1="&#xfe;" u2="&#x2f;" k="18" />
<hkern u1="&#xfe;" u2="&#x2a;" k="65" />
<hkern u1="&#xfe;" u2="&#x29;" k="57" />
<hkern u1="&#xff;" u2="&#xf0;" k="35" />
<hkern u1="&#xff;" u2="&#x7d;" k="39" />
<hkern u1="&#xff;" u2="]" k="47" />
<hkern u1="&#xff;" u2="&#x2f;" k="66" />
<hkern u1="&#xff;" u2="&#x2c;" k="81" />
<hkern u1="&#xff;" u2="&#x29;" k="18" />
<hkern u1="&#xff;" u2="&#x20;" k="45" />
<hkern u1="&#x152;" u2="&#xf0;" k="53" />
<hkern u1="&#x152;" u2="&#xef;" k="-15" />
<hkern u1="&#x152;" u2="&#xee;" k="-16" />
<hkern u1="&#x152;" u2="&#xec;" k="-22" />
<hkern u1="&#x153;" u2="&#x2122;" k="52" />
<hkern u1="&#x153;" u2="&#xf0;" k="5" />
<hkern u1="&#x153;" u2="&#xba;" k="36" />
<hkern u1="&#x153;" u2="&#xaa;" k="16" />
<hkern u1="&#x153;" u2="&#x7d;" k="50" />
<hkern u1="&#x153;" u2="]" k="55" />
<hkern u1="&#x153;" u2="\" k="68" />
<hkern u1="&#x153;" u2="&#x3f;" k="46" />
<hkern u1="&#x153;" u2="&#x2a;" k="54" />
<hkern u1="&#x153;" u2="&#x29;" k="39" />
<hkern u1="&#x178;" g2="uniFB02" k="92" />
<hkern u1="&#x178;" u2="&#x2122;" k="-35" />
<hkern u1="&#x178;" u2="&#xff;" k="95" />
<hkern u1="&#x178;" u2="&#xf9;" k="146" />
<hkern u1="&#x178;" u2="&#xf6;" k="181" />
<hkern u1="&#x178;" u2="&#xf2;" k="171" />
<hkern u1="&#x178;" u2="&#xf0;" k="151" />
<hkern u1="&#x178;" u2="&#xef;" k="-40" />
<hkern u1="&#x178;" u2="&#xed;" k="51" />
<hkern u1="&#x178;" u2="&#xec;" k="-73" />
<hkern u1="&#x178;" u2="&#xeb;" k="175" />
<hkern u1="&#x178;" u2="&#xe8;" k="152" />
<hkern u1="&#x178;" u2="&#xdf;" k="103" />
<hkern u1="&#x178;" u2="&#xae;" k="25" />
<hkern u1="&#x178;" u2="&#xa9;" k="25" />
<hkern u1="&#x178;" u2="&#x7d;" k="-7" />
<hkern u1="&#x178;" u2="]" k="-11" />
<hkern u1="&#x178;" u2="\" k="-9" />
<hkern u1="&#x178;" u2="&#x40;" k="79" />
<hkern u1="&#x178;" u2="&#x3b;" k="73" />
<hkern u1="&#x178;" u2="&#x3a;" k="73" />
<hkern u1="&#x178;" u2="&#x2f;" k="133" />
<hkern u1="&#x178;" u2="&#x2c;" k="163" />
<hkern u1="&#x178;" u2="&#x26;" k="69" />
<hkern u1="&#x178;" u2="&#x20;" k="69" />
<hkern u1="&#x2018;" u2="&#xf0;" k="61" />
<hkern u1="&#x2018;" u2="&#xee;" k="-15" />
<hkern u1="&#x2018;" u2="&#xec;" k="-50" />
<hkern u1="&#x2018;" u2="&#xce;" k="-16" />
<hkern u1="&#x2018;" u2="&#x2c;" k="209" />
<hkern u1="&#x2019;" u2="&#xf0;" k="54" />
<hkern u1="&#x2019;" u2="&#xef;" k="-41" />
<hkern u1="&#x2019;" u2="&#xee;" k="-44" />
<hkern u1="&#x2019;" u2="&#xec;" k="-34" />
<hkern u1="&#x2019;" u2="&#x40;" k="49" />
<hkern u1="&#x2019;" u2="&#x3b;" k="17" />
<hkern u1="&#x2019;" u2="&#x3a;" k="18" />
<hkern u1="&#x2019;" u2="&#x2f;" k="121" />
<hkern u1="&#x2019;" u2="&#x2c;" k="211" />
<hkern u1="&#x2019;" u2="&#x26;" k="49" />
<hkern u1="&#x201c;" u2="&#xf0;" k="61" />
<hkern u1="&#x201c;" u2="&#xee;" k="-15" />
<hkern u1="&#x201c;" u2="&#xec;" k="-50" />
<hkern u1="&#x201c;" u2="&#xce;" k="-16" />
<hkern u1="&#x201c;" u2="&#x2c;" k="209" />
<hkern u1="&#x201d;" u2="&#xf0;" k="54" />
<hkern u1="&#x201d;" u2="&#xef;" k="-41" />
<hkern u1="&#x201d;" u2="&#xee;" k="-44" />
<hkern u1="&#x201d;" u2="&#xec;" k="-34" />
<hkern u1="&#x201d;" u2="&#x40;" k="49" />
<hkern u1="&#x201d;" u2="&#x3b;" k="17" />
<hkern u1="&#x201d;" u2="&#x3a;" k="18" />
<hkern u1="&#x201d;" u2="&#x2f;" k="121" />
<hkern u1="&#x201d;" u2="&#x2c;" k="211" />
<hkern u1="&#x201d;" u2="&#x26;" k="49" />
<hkern g1="uniFB01" u2="&#xef;" k="-1" />
<hkern g1="uniFB02" u2="&#xee;" k="-13" />
<hkern g1="uniFB02" u2="&#xec;" k="-13" />
<hkern g1="uniFB02" u2="&#xb7;" k="66" />
<hkern g1="uniFB03" u2="&#xef;" k="-1" />
<hkern g1="uniFB04" u2="&#xee;" k="-13" />
<hkern g1="uniFB04" u2="&#xec;" k="-13" />
<hkern g1="uniFB04" u2="&#xb7;" k="66" />
<hkern g1="B" 	g2="V" 	k="32" />
<hkern g1="B" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="13" />
<hkern g1="B" 	g2="t" 	k="25" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="24" />
<hkern g1="B" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="27" />
<hkern g1="B" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="19" />
<hkern g1="B" 	g2="T" 	k="39" />
<hkern g1="B" 	g2="W" 	k="9" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="52" />
<hkern g1="B" 	g2="d" 	k="13" />
<hkern g1="B" 	g2="v" 	k="24" />
<hkern g1="B" 	g2="w" 	k="11" />
<hkern g1="B" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="B" 	g2="X" 	k="23" />
<hkern g1="B" 	g2="b,h,k,thorn" 	k="19" />
<hkern g1="B" 	g2="J" 	k="10" />
<hkern g1="B" 	g2="l" 	k="19" />
<hkern g1="B" 	g2="g" 	k="44" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="B" 	g2="s" 	k="36" />
<hkern g1="B" 	g2="AE" 	k="38" />
<hkern g1="B" 	g2="x" 	k="35" />
<hkern g1="B" 	g2="z" 	k="28" />
<hkern g1="B" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="19" />
<hkern g1="B" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="hyphen,emdash" 	k="33" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="22" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="32" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="32" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="7" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,ntilde" 	k="12" />
<hkern g1="D,Eth" 	g2="V" 	k="49" />
<hkern g1="D,Eth" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="D,Eth" 	g2="t" 	k="6" />
<hkern g1="D,Eth" 	g2="y,yacute,ydieresis" 	k="11" />
<hkern g1="D,Eth" 	g2="S" 	k="6" />
<hkern g1="D,Eth" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="13" />
<hkern g1="D,Eth" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="8" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="D,Eth" 	g2="T" 	k="75" />
<hkern g1="D,Eth" 	g2="W" 	k="24" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="91" />
<hkern g1="D,Eth" 	g2="d" 	k="13" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="D,Eth" 	g2="v" 	k="11" />
<hkern g1="D,Eth" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="6" />
<hkern g1="D,Eth" 	g2="X" 	k="58" />
<hkern g1="D,Eth" 	g2="b,h,k,thorn" 	k="17" />
<hkern g1="D,Eth" 	g2="J" 	k="65" />
<hkern g1="D,Eth" 	g2="l" 	k="17" />
<hkern g1="D,Eth" 	g2="g" 	k="20" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="34" />
<hkern g1="D,Eth" 	g2="s" 	k="17" />
<hkern g1="D,Eth" 	g2="AE" 	k="60" />
<hkern g1="D,Eth" 	g2="x" 	k="44" />
<hkern g1="D,Eth" 	g2="z" 	k="24" />
<hkern g1="D,Eth" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="18" />
<hkern g1="D,Eth" 	g2="m,n,p,r,ntilde" 	k="17" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="67" />
<hkern g1="D,Eth" 	g2="period,ellipsis" 	k="55" />
<hkern g1="D,Eth" 	g2="Z" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="47" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="hyphen,emdash" 	k="22" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d" 	k="43" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="42" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="53" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="b,h,k,thorn" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="l" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="13" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="5" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="m,n,p,r,ntilde" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="F" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="116" />
<hkern g1="F" 	g2="t" 	k="48" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="43" />
<hkern g1="F" 	g2="S" 	k="30" />
<hkern g1="F" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="53" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="82" />
<hkern g1="F" 	g2="hyphen,emdash" 	k="39" />
<hkern g1="F" 	g2="d" 	k="101" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="15" />
<hkern g1="F" 	g2="v" 	k="43" />
<hkern g1="F" 	g2="w" 	k="35" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="92" />
<hkern g1="F" 	g2="b,h,k,thorn" 	k="24" />
<hkern g1="F" 	g2="J" 	k="178" />
<hkern g1="F" 	g2="l" 	k="24" />
<hkern g1="F" 	g2="g" 	k="101" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="109" />
<hkern g1="F" 	g2="s" 	k="101" />
<hkern g1="F" 	g2="AE" 	k="166" />
<hkern g1="F" 	g2="x" 	k="92" />
<hkern g1="F" 	g2="z" 	k="95" />
<hkern g1="F" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="13" />
<hkern g1="F" 	g2="m,n,p,r,ntilde" 	k="93" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="175" />
<hkern g1="F" 	g2="period,ellipsis" 	k="191" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="43" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="45" />
<hkern g1="G" 	g2="V" 	k="29" />
<hkern g1="G" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="13" />
<hkern g1="G" 	g2="t" 	k="33" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="40" />
<hkern g1="G" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="34" />
<hkern g1="G" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="19" />
<hkern g1="G" 	g2="T" 	k="7" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="G" 	g2="d" 	k="12" />
<hkern g1="G" 	g2="v" 	k="38" />
<hkern g1="G" 	g2="w" 	k="28" />
<hkern g1="G" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="13" />
<hkern g1="G" 	g2="b,h,k,thorn" 	k="16" />
<hkern g1="G" 	g2="l" 	k="16" />
<hkern g1="G" 	g2="g" 	k="18" />
<hkern g1="G" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="12" />
<hkern g1="G" 	g2="s" 	k="13" />
<hkern g1="G" 	g2="AE" 	k="26" />
<hkern g1="G" 	g2="x" 	k="14" />
<hkern g1="G" 	g2="z" 	k="17" />
<hkern g1="G" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="9" />
<hkern g1="G" 	g2="m,n,p,r,ntilde" 	k="18" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="33" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="t" 	k="27" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="y,yacute,ydieresis" 	k="23" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="29" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="d" 	k="29" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="v" 	k="23" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="w" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="b,h,k,thorn" 	k="13" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="l" 	k="13" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="g" 	k="34" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="s" 	k="29" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="x" 	k="7" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="z" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="13" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="m,n,p,r,ntilde" 	k="13" />
<hkern g1="J" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="J" 	g2="t" 	k="22" />
<hkern g1="J" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="J" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="25" />
<hkern g1="J" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="J" 	g2="d" 	k="34" />
<hkern g1="J" 	g2="v" 	k="17" />
<hkern g1="J" 	g2="w" 	k="8" />
<hkern g1="J" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="34" />
<hkern g1="J" 	g2="b,h,k,thorn" 	k="15" />
<hkern g1="J" 	g2="J" 	k="18" />
<hkern g1="J" 	g2="l" 	k="15" />
<hkern g1="J" 	g2="g" 	k="51" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="9" />
<hkern g1="J" 	g2="s" 	k="40" />
<hkern g1="J" 	g2="AE" 	k="27" />
<hkern g1="J" 	g2="x" 	k="27" />
<hkern g1="J" 	g2="z" 	k="31" />
<hkern g1="J" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="16" />
<hkern g1="J" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="43" />
<hkern g1="J" 	g2="period,ellipsis" 	k="36" />
<hkern g1="K" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="53" />
<hkern g1="K" 	g2="t" 	k="70" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="97" />
<hkern g1="K" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="48" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="54" />
<hkern g1="K" 	g2="hyphen,emdash" 	k="70" />
<hkern g1="K" 	g2="d" 	k="58" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="83" />
<hkern g1="K" 	g2="v" 	k="98" />
<hkern g1="K" 	g2="w" 	k="81" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="79" />
<hkern g1="K" 	g2="s" 	k="6" />
<hkern g1="K" 	g2="m,n,p,r,ntilde" 	k="8" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="58" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="54" />
<hkern g1="L" 	g2="V" 	k="168" />
<hkern g1="L" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="36" />
<hkern g1="L" 	g2="t" 	k="80" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="175" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="191" />
<hkern g1="L" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="60" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="52" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="198" />
<hkern g1="L" 	g2="T" 	k="175" />
<hkern g1="L" 	g2="hyphen,emdash" 	k="145" />
<hkern g1="L" 	g2="W" 	k="122" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="178" />
<hkern g1="L" 	g2="d" 	k="42" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="73" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="195" />
<hkern g1="L" 	g2="v" 	k="174" />
<hkern g1="L" 	g2="w" 	k="137" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="77" />
<hkern g1="L" 	g2="b,h,k,thorn" 	k="7" />
<hkern g1="L" 	g2="l" 	k="7" />
<hkern g1="L" 	g2="s" 	k="9" />
<hkern g1="L" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="6" />
<hkern g1="L" 	g2="m,n,p,r,ntilde" 	k="7" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="96" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="47" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="13" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="y,yacute,ydieresis" 	k="5" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="S" 	k="6" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="13" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="8" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="11" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="75" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="17" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="88" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="d" 	k="7" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="15" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="v" 	k="11" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="6" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="55" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="b,h,k,thorn" 	k="17" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="58" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="l" 	k="17" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="g" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="s" 	k="17" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="59" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="42" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="17" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="m,n,p,r,ntilde" 	k="17" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="66" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="period,ellipsis" 	k="54" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="31" />
<hkern g1="P" 	g2="V" 	k="22" />
<hkern g1="P" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="79" />
<hkern g1="P" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="7" />
<hkern g1="P" 	g2="hyphen,emdash" 	k="45" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="34" />
<hkern g1="P" 	g2="d" 	k="59" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="50" />
<hkern g1="P" 	g2="X" 	k="56" />
<hkern g1="P" 	g2="b,h,k,thorn" 	k="12" />
<hkern g1="P" 	g2="J" 	k="162" />
<hkern g1="P" 	g2="l" 	k="12" />
<hkern g1="P" 	g2="g" 	k="37" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="94" />
<hkern g1="P" 	g2="s" 	k="25" />
<hkern g1="P" 	g2="AE" 	k="157" />
<hkern g1="P" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="5" />
<hkern g1="P" 	g2="m,n,p,r,ntilde" 	k="16" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="180" />
<hkern g1="P" 	g2="period,ellipsis" 	k="198" />
<hkern g1="P" 	g2="Z" 	k="8" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="9" />
<hkern g1="R" 	g2="V" 	k="41" />
<hkern g1="R" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="48" />
<hkern g1="R" 	g2="t" 	k="30" />
<hkern g1="R" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="R" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="25" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="38" />
<hkern g1="R" 	g2="T" 	k="45" />
<hkern g1="R" 	g2="W" 	k="17" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="60" />
<hkern g1="R" 	g2="d" 	k="48" />
<hkern g1="R" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="13" />
<hkern g1="R" 	g2="v" 	k="30" />
<hkern g1="R" 	g2="w" 	k="22" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="63" />
<hkern g1="R" 	g2="b,h,k,thorn" 	k="14" />
<hkern g1="R" 	g2="l" 	k="14" />
<hkern g1="R" 	g2="s" 	k="15" />
<hkern g1="R" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="15" />
<hkern g1="R" 	g2="m,n,p,r,ntilde" 	k="15" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="38" />
<hkern g1="S" 	g2="V" 	k="28" />
<hkern g1="S" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="5" />
<hkern g1="S" 	g2="t" 	k="42" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="52" />
<hkern g1="S" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="43" />
<hkern g1="S" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="19" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="S" 	g2="d" 	k="5" />
<hkern g1="S" 	g2="v" 	k="51" />
<hkern g1="S" 	g2="w" 	k="37" />
<hkern g1="S" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="6" />
<hkern g1="S" 	g2="b,h,k,thorn" 	k="16" />
<hkern g1="S" 	g2="l" 	k="16" />
<hkern g1="S" 	g2="g" 	k="33" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="22" />
<hkern g1="S" 	g2="s" 	k="26" />
<hkern g1="S" 	g2="AE" 	k="41" />
<hkern g1="S" 	g2="x" 	k="41" />
<hkern g1="S" 	g2="z" 	k="28" />
<hkern g1="S" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="9" />
<hkern g1="S" 	g2="m,n,p,r,ntilde" 	k="22" />
<hkern g1="S" 	g2="quotesinglbase,quotedblbase" 	k="16" />
<hkern g1="T" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="226" />
<hkern g1="T" 	g2="t" 	k="112" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="159" />
<hkern g1="T" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="102" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="175" />
<hkern g1="T" 	g2="hyphen,emdash" 	k="120" />
<hkern g1="T" 	g2="d" 	k="221" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="T" 	g2="v" 	k="160" />
<hkern g1="T" 	g2="w" 	k="154" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="222" />
<hkern g1="T" 	g2="b,h,k,thorn" 	k="15" />
<hkern g1="T" 	g2="J" 	k="119" />
<hkern g1="T" 	g2="l" 	k="15" />
<hkern g1="T" 	g2="g" 	k="214" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="128" />
<hkern g1="T" 	g2="s" 	k="207" />
<hkern g1="T" 	g2="AE" 	k="176" />
<hkern g1="T" 	g2="x" 	k="157" />
<hkern g1="T" 	g2="z" 	k="177" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="186" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="144" />
<hkern g1="T" 	g2="period,ellipsis" 	k="142" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="150" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="138" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="40" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="t" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="30" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="d" 	k="38" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="v" 	k="15" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="w" 	k="7" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="36" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="b,h,k,thorn" 	k="22" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="53" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="l" 	k="22" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="54" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="46" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="51" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="33" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="34" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="m,n,p,r,ntilde" 	k="28" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="60" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="period,ellipsis" 	k="51" />
<hkern g1="V" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="142" />
<hkern g1="V" 	g2="t" 	k="46" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="V" 	g2="S" 	k="30" />
<hkern g1="V" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="47" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="97" />
<hkern g1="V" 	g2="hyphen,emdash" 	k="69" />
<hkern g1="V" 	g2="d" 	k="135" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="34" />
<hkern g1="V" 	g2="v" 	k="41" />
<hkern g1="V" 	g2="w" 	k="44" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="140" />
<hkern g1="V" 	g2="J" 	k="134" />
<hkern g1="V" 	g2="g" 	k="144" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="106" />
<hkern g1="V" 	g2="s" 	k="129" />
<hkern g1="V" 	g2="AE" 	k="158" />
<hkern g1="V" 	g2="x" 	k="71" />
<hkern g1="V" 	g2="z" 	k="81" />
<hkern g1="V" 	g2="m,n,p,r,ntilde" 	k="100" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="157" />
<hkern g1="V" 	g2="period,ellipsis" 	k="151" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="88" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="60" />
<hkern g1="W" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="106" />
<hkern g1="W" 	g2="t" 	k="35" />
<hkern g1="W" 	g2="y,yacute,ydieresis" 	k="24" />
<hkern g1="W" 	g2="S" 	k="16" />
<hkern g1="W" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="34" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="71" />
<hkern g1="W" 	g2="hyphen,emdash" 	k="43" />
<hkern g1="W" 	g2="d" 	k="99" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="15" />
<hkern g1="W" 	g2="v" 	k="24" />
<hkern g1="W" 	g2="w" 	k="25" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="105" />
<hkern g1="W" 	g2="J" 	k="106" />
<hkern g1="W" 	g2="g" 	k="104" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="80" />
<hkern g1="W" 	g2="s" 	k="91" />
<hkern g1="W" 	g2="AE" 	k="133" />
<hkern g1="W" 	g2="x" 	k="46" />
<hkern g1="W" 	g2="z" 	k="56" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="79" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="119" />
<hkern g1="W" 	g2="period,ellipsis" 	k="114" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="60" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="24" />
<hkern g1="X" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="44" />
<hkern g1="X" 	g2="t" 	k="75" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="95" />
<hkern g1="X" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="51" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="54" />
<hkern g1="X" 	g2="hyphen,emdash" 	k="56" />
<hkern g1="X" 	g2="d" 	k="47" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="56" />
<hkern g1="X" 	g2="v" 	k="96" />
<hkern g1="X" 	g2="w" 	k="83" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="X" 	g2="b,h,k,thorn" 	k="5" />
<hkern g1="X" 	g2="l" 	k="5" />
<hkern g1="X" 	g2="m,n,p,r,ntilde" 	k="14" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="225" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="88" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="40" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="85" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="156" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,emdash" 	k="131" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="207" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="60" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="103" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="98" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="203" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="138" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="203" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="135" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="200" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="187" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="111" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="137" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="157" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="165" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="period,ellipsis" 	k="166" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="142" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="103" />
<hkern g1="Z" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="Z" 	g2="t" 	k="49" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="66" />
<hkern g1="Z" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="43" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="45" />
<hkern g1="Z" 	g2="hyphen,emdash" 	k="38" />
<hkern g1="Z" 	g2="d" 	k="39" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="32" />
<hkern g1="Z" 	g2="v" 	k="67" />
<hkern g1="Z" 	g2="w" 	k="56" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="50" />
<hkern g1="Z" 	g2="b,h,k,thorn" 	k="6" />
<hkern g1="Z" 	g2="l" 	k="6" />
<hkern g1="Z" 	g2="g" 	k="8" />
<hkern g1="Z" 	g2="s" 	k="14" />
<hkern g1="Z" 	g2="z" 	k="6" />
<hkern g1="Z" 	g2="m,n,p,r,ntilde" 	k="29" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="15" />
<hkern g1="b,p,thorn" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="13" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="71" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ydieresis" 	k="194" />
<hkern g1="b,p,thorn" 	g2="J" 	k="70" />
<hkern g1="b,p,thorn" 	g2="W" 	k="96" />
<hkern g1="b,p,thorn" 	g2="S" 	k="32" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="80" />
<hkern g1="b,p,thorn" 	g2="X" 	k="69" />
<hkern g1="b,p,thorn" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="29" />
<hkern g1="b,p,thorn" 	g2="Z" 	k="58" />
<hkern g1="b,p,thorn" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="36" />
<hkern g1="b,p,thorn" 	g2="V" 	k="140" />
<hkern g1="b,p,thorn" 	g2="T" 	k="221" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="b,p,thorn" 	g2="g" 	k="5" />
<hkern g1="b,p,thorn" 	g2="z" 	k="18" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="50" />
<hkern g1="b,p,thorn" 	g2="x" 	k="41" />
<hkern g1="b,p,thorn" 	g2="v" 	k="24" />
<hkern g1="b,p,thorn" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="15" />
<hkern g1="b,p,thorn" 	g2="w" 	k="9" />
<hkern g1="b,p,thorn" 	g2="t" 	k="8" />
<hkern g1="b,p,thorn" 	g2="quotesinglbase,quotedblbase" 	k="16" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="28" />
<hkern g1="c,ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="32" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="26" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="187" />
<hkern g1="c,ccedilla" 	g2="J" 	k="12" />
<hkern g1="c,ccedilla" 	g2="W" 	k="91" />
<hkern g1="c,ccedilla" 	g2="S" 	k="12" />
<hkern g1="c,ccedilla" 	g2="quoteleft,quotedblleft" 	k="47" />
<hkern g1="c,ccedilla" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="24" />
<hkern g1="c,ccedilla" 	g2="Z" 	k="5" />
<hkern g1="c,ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="50" />
<hkern g1="c,ccedilla" 	g2="V" 	k="131" />
<hkern g1="c,ccedilla" 	g2="T" 	k="208" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="11" />
<hkern g1="c,ccedilla" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="c,ccedilla" 	g2="v" 	k="11" />
<hkern g1="c,ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="19" />
<hkern g1="c,ccedilla" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="19" />
<hkern g1="c,ccedilla" 	g2="d" 	k="14" />
<hkern g1="c,ccedilla" 	g2="hyphen,emdash" 	k="33" />
<hkern g1="d" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="19" />
<hkern g1="d" 	g2="Y,Yacute,Ydieresis" 	k="7" />
<hkern g1="d" 	g2="J" 	k="8" />
<hkern g1="d" 	g2="W" 	k="4" />
<hkern g1="d" 	g2="S" 	k="9" />
<hkern g1="d" 	g2="X" 	k="6" />
<hkern g1="d" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="14" />
<hkern g1="d" 	g2="Z" 	k="17" />
<hkern g1="d" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="23" />
<hkern g1="d" 	g2="V" 	k="6" />
<hkern g1="d" 	g2="T" 	k="18" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="24" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="44" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="212" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="15" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="99" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="52" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="13" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Z" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="46" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="141" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="208" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="40" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="f" 	g2="Y,Yacute,Ydieresis" 	k="-36" />
<hkern g1="f" 	g2="J" 	k="109" />
<hkern g1="f" 	g2="W" 	k="-30" />
<hkern g1="f" 	g2="S" 	k="5" />
<hkern g1="f" 	g2="X" 	k="-9" />
<hkern g1="f" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="4" />
<hkern g1="f" 	g2="V" 	k="-37" />
<hkern g1="f" 	g2="T" 	k="-9" />
<hkern g1="f" 	g2="g" 	k="5" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="51" />
<hkern g1="f" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="75" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="32" />
<hkern g1="f" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="48" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="17" />
<hkern g1="f" 	g2="d" 	k="37" />
<hkern g1="f" 	g2="hyphen,emdash" 	k="29" />
<hkern g1="f" 	g2="period,ellipsis" 	k="53" />
<hkern g1="g" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="g" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="122" />
<hkern g1="g" 	g2="J" 	k="16" />
<hkern g1="g" 	g2="W" 	k="36" />
<hkern g1="g" 	g2="S" 	k="14" />
<hkern g1="g" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="g" 	g2="X" 	k="8" />
<hkern g1="g" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="18" />
<hkern g1="g" 	g2="Z" 	k="12" />
<hkern g1="g" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="19" />
<hkern g1="g" 	g2="V" 	k="66" />
<hkern g1="g" 	g2="T" 	k="185" />
<hkern g1="g" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="g" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="32" />
<hkern g1="g" 	g2="d" 	k="25" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="Y,Yacute,Ydieresis" 	k="9" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="J" 	k="7" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="S" 	k="10" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="13" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="Z" 	k="14" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="23" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="T" 	k="10" />
<hkern g1="k" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="24" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="110" />
<hkern g1="k" 	g2="W" 	k="34" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="26" />
<hkern g1="k" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="27" />
<hkern g1="k" 	g2="V" 	k="63" />
<hkern g1="k" 	g2="T" 	k="173" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="56" />
<hkern g1="k" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="47" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="k" 	g2="d" 	k="47" />
<hkern g1="k" 	g2="hyphen,emdash" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="26" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="204" />
<hkern g1="h,m,n,ntilde" 	g2="W" 	k="82" />
<hkern g1="h,m,n,ntilde" 	g2="S" 	k="19" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="53" />
<hkern g1="h,m,n,ntilde" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="17" />
<hkern g1="h,m,n,ntilde" 	g2="Z" 	k="19" />
<hkern g1="h,m,n,ntilde" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="35" />
<hkern g1="h,m,n,ntilde" 	g2="V" 	k="126" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="228" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="26" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="42" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="25" />
<hkern g1="h,m,n,ntilde" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="11" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="15" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="63" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Y,Yacute,Ydieresis" 	k="214" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="J" 	k="63" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="W" 	k="102" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="S" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="73" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="X" 	k="66" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Z" 	k="56" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="38" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="V" 	k="144" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="T" 	k="223" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="34" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="g" 	k="6" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="56" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="44" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="26" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="r" 	g2="J" 	k="146" />
<hkern g1="r" 	g2="W" 	k="17" />
<hkern g1="r" 	g2="S" 	k="13" />
<hkern g1="r" 	g2="X" 	k="103" />
<hkern g1="r" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="18" />
<hkern g1="r" 	g2="Z" 	k="62" />
<hkern g1="r" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="5" />
<hkern g1="r" 	g2="V" 	k="42" />
<hkern g1="r" 	g2="T" 	k="162" />
<hkern g1="r" 	g2="g" 	k="13" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="89" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="99" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="38" />
<hkern g1="r" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="55" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="26" />
<hkern g1="r" 	g2="d" 	k="39" />
<hkern g1="r" 	g2="hyphen,emdash" 	k="38" />
<hkern g1="r" 	g2="period,ellipsis" 	k="88" />
<hkern g1="r" 	g2="s" 	k="6" />
<hkern g1="s" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="17" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="180" />
<hkern g1="s" 	g2="J" 	k="10" />
<hkern g1="s" 	g2="W" 	k="88" />
<hkern g1="s" 	g2="S" 	k="5" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="38" />
<hkern g1="s" 	g2="X" 	k="23" />
<hkern g1="s" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="29" />
<hkern g1="s" 	g2="Z" 	k="18" />
<hkern g1="s" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="39" />
<hkern g1="s" 	g2="V" 	k="111" />
<hkern g1="s" 	g2="T" 	k="206" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="s" 	g2="g" 	k="6" />
<hkern g1="s" 	g2="v" 	k="13" />
<hkern g1="s" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="12" />
<hkern g1="t" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="19" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="136" />
<hkern g1="t" 	g2="W" 	k="62" />
<hkern g1="t" 	g2="S" 	k="10" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="34" />
<hkern g1="t" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="27" />
<hkern g1="t" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="t" 	g2="V" 	k="96" />
<hkern g1="t" 	g2="T" 	k="180" />
<hkern g1="t" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="t" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="19" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="t" 	g2="d" 	k="17" />
<hkern g1="t" 	g2="hyphen,emdash" 	k="20" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="22" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="162" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="J" 	k="8" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="76" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="S" 	k="18" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="quoteleft,quotedblleft" 	k="12" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="6" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="14" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Z" 	k="20" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="28" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="111" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="211" />
<hkern g1="v" 	g2="Y,Yacute,Ydieresis" 	k="105" />
<hkern g1="v" 	g2="J" 	k="140" />
<hkern g1="v" 	g2="W" 	k="19" />
<hkern g1="v" 	g2="S" 	k="8" />
<hkern g1="v" 	g2="X" 	k="91" />
<hkern g1="v" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="18" />
<hkern g1="v" 	g2="Z" 	k="51" />
<hkern g1="v" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="7" />
<hkern g1="v" 	g2="V" 	k="44" />
<hkern g1="v" 	g2="T" 	k="190" />
<hkern g1="v" 	g2="g" 	k="20" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="80" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="74" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="24" />
<hkern g1="v" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="36" />
<hkern g1="v" 	g2="d" 	k="27" />
<hkern g1="v" 	g2="period,ellipsis" 	k="77" />
<hkern g1="v" 	g2="s" 	k="17" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="110" />
<hkern g1="w" 	g2="J" 	k="115" />
<hkern g1="w" 	g2="W" 	k="22" />
<hkern g1="w" 	g2="S" 	k="7" />
<hkern g1="w" 	g2="X" 	k="82" />
<hkern g1="w" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="10" />
<hkern g1="w" 	g2="Z" 	k="45" />
<hkern g1="w" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="6" />
<hkern g1="w" 	g2="V" 	k="50" />
<hkern g1="w" 	g2="T" 	k="182" />
<hkern g1="w" 	g2="g" 	k="10" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="57" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="54" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="w" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="22" />
<hkern g1="w" 	g2="d" 	k="16" />
<hkern g1="w" 	g2="period,ellipsis" 	k="54" />
<hkern g1="w" 	g2="s" 	k="8" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="113" />
<hkern g1="x" 	g2="W" 	k="37" />
<hkern g1="x" 	g2="S" 	k="4" />
<hkern g1="x" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="8" />
<hkern g1="x" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="30" />
<hkern g1="x" 	g2="V" 	k="67" />
<hkern g1="x" 	g2="T" 	k="179" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="43" />
<hkern g1="x" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="39" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="46" />
<hkern g1="x" 	g2="d" 	k="36" />
<hkern g1="x" 	g2="hyphen,emdash" 	k="39" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="143" />
<hkern g1="y,yacute,ydieresis" 	g2="W" 	k="17" />
<hkern g1="y,yacute,ydieresis" 	g2="S" 	k="8" />
<hkern g1="y,yacute,ydieresis" 	g2="X" 	k="91" />
<hkern g1="y,yacute,ydieresis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="19" />
<hkern g1="y,yacute,ydieresis" 	g2="Z" 	k="50" />
<hkern g1="y,yacute,ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="6" />
<hkern g1="y,yacute,ydieresis" 	g2="V" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="189" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="22" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="85" />
<hkern g1="y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="77" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="26" />
<hkern g1="y,yacute,ydieresis" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="y,yacute,ydieresis" 	g2="d" 	k="29" />
<hkern g1="y,yacute,ydieresis" 	g2="period,ellipsis" 	k="81" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="17" />
<hkern g1="z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="134" />
<hkern g1="z" 	g2="W" 	k="46" />
<hkern g1="z" 	g2="S" 	k="6" />
<hkern g1="z" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="25" />
<hkern g1="z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="33" />
<hkern g1="z" 	g2="V" 	k="77" />
<hkern g1="z" 	g2="T" 	k="188" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="19" />
<hkern g1="z" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="22" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="z" 	g2="d" 	k="18" />
<hkern g1="z" 	g2="hyphen,emdash" 	k="17" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="67" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="11" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="59" />
<hkern g1="quoteright,quotedblright" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="12" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="145" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="83" />
<hkern g1="quoteright,quotedblright" 	g2="d" 	k="106" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="196" />
<hkern g1="quoteright,quotedblright" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="103" />
<hkern g1="quoteright,quotedblright" 	g2="period,ellipsis" 	k="211" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="15" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="27" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="146" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="12" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,emdash" 	k="138" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="77" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="121" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="12" />
<hkern g1="quoteright,quotedblright" 	g2="quotesinglbase,quotedblbase" 	k="206" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="f,germandbls,uniFB01,uniFB02,uniFB03,uniFB04" 	k="16" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="19" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="74" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="117" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="84" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="162" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="58" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="199" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="206" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="141" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="65" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="80" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="157" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="19" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="113" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quoteright,quotedblright" 	k="55" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="139" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="68" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="19" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="74" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="47" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="68" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="19" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="150" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="75" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="111" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="150" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="100" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="22" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="45" />
<hkern g1="hyphen,emdash" 	g2="z" 	k="24" />
<hkern g1="hyphen,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="30" />
<hkern g1="hyphen,emdash" 	g2="AE" 	k="45" />
<hkern g1="hyphen,emdash" 	g2="J" 	k="113" />
<hkern g1="hyphen,emdash" 	g2="x" 	k="45" />
<hkern g1="hyphen,emdash" 	g2="W" 	k="44" />
<hkern g1="hyphen,emdash" 	g2="Y,Yacute,Ydieresis" 	k="135" />
<hkern g1="hyphen,emdash" 	g2="quotedbl,quotesingle" 	k="114" />
<hkern g1="hyphen,emdash" 	g2="quoteright,quotedblright" 	k="142" />
<hkern g1="hyphen,emdash" 	g2="T" 	k="121" />
<hkern g1="hyphen,emdash" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="hyphen,emdash" 	g2="V" 	k="74" />
<hkern g1="hyphen,emdash" 	g2="Z" 	k="38" />
<hkern g1="hyphen,emdash" 	g2="X" 	k="54" />
<hkern g1="hyphen,emdash" 	g2="S" 	k="15" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="39" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="124" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="d" 	k="62" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="178" />
<hkern g1="quotedbl,quotesingle" 	g2="a,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="70" />
<hkern g1="quotedbl,quotesingle" 	g2="period,ellipsis" 	k="199" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="147" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,emdash" 	k="90" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="quotesinglbase,quotedblbase" 	k="199" />
<hkern g1="l,uniFB02,uniFB04" 	g2="V" 	k="6" />
<hkern g1="l,uniFB02,uniFB04" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="24" />
<hkern g1="l,uniFB02,uniFB04" 	g2="X" 	k="6" />
<hkern g1="l,uniFB02,uniFB04" 	g2="W" 	k="4" />
<hkern g1="l,uniFB02,uniFB04" 	g2="S" 	k="9" />
<hkern g1="l,uniFB02,uniFB04" 	g2="T" 	k="18" />
<hkern g1="l,uniFB02,uniFB04" 	g2="J" 	k="8" />
<hkern g1="l,uniFB02,uniFB04" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="13" />
<hkern g1="l,uniFB02,uniFB04" 	g2="Y,Yacute,Ydieresis" 	k="13" />
<hkern g1="l,uniFB02,uniFB04" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="19" />
<hkern g1="l,uniFB02,uniFB04" 	g2="Z" 	k="17" />
</font>
</defs></svg> 