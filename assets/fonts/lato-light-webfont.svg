<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="latolight" horiz-adv-x="1187" >
<font-face units-per-em="2048" ascent="1649" descent="-399" />
<missing-glyph horiz-adv-x="556" />
<glyph unicode="&#xfb01;" horiz-adv-x="1102" d="M58 947v40h172v87q0 94 28 169.5t81 128t129 81t173 28.5q33 0 69 -6t62 -16l-4 -49q-2 -8 -8 -9t-18.5 0t-31.5 3.5t-45 2.5q-162 0 -250.5 -83t-88.5 -253v-84h597v-987h-98v914h-496v-914h-99v912l-143 10q-29 3 -29 25z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1152" d="M58 947v40h172v67q0 90 27 167t78.5 133.5t127.5 88.5t175 32q70 0 137 -6.5t126 -8.5h73v-1460h-97v1392q-54 2 -115.5 6t-111.5 4q-77 0 -137 -24t-101.5 -69.5t-63.5 -110t-22 -144.5v-67h296v-73h-293v-914h-99v912l-143 10q-29 3 -29 25z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1725" d="M58 947v40h172v116q0 87 22.5 153t63.5 110t99 66t128 22q32 0 63.5 -5t57.5 -16l-4 -48q-2 -9 -8 -11t-18.5 -1.5t-31.5 3t-46 2.5q-50 0 -92.5 -14.5t-73.5 -47.5t-47.5 -86t-16.5 -130v-113h528v87q0 94 27.5 169.5t80.5 128t128.5 81t173.5 28.5q34 0 70 -6t61 -16 l-4 -49q-1 -8 -7.5 -9t-18.5 0t-31.5 3.5t-46.5 2.5q-162 0 -250 -83t-88 -253v-84h597v-987h-98v914h-497v-914h-97v914h-525v-914h-99v912l-143 10q-29 3 -29 25z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1772" d="M56 947v40h172v116q0 87 22.5 153t63.5 110t98.5 66t127.5 22q32 0 64 -5t58 -16l-4 -48q-2 -9 -8.5 -11t-19 -1.5t-31.5 3t-45 2.5q-50 0 -92.5 -14.5t-73.5 -47.5t-47.5 -86t-16.5 -130v-113h528v67q0 90 26.5 167t78 133.5t127.5 88.5t174 32q71 0 137.5 -6.5 t126.5 -8.5h73v-1460h-97v1392q-55 2 -116 6t-110 4q-77 0 -137.5 -24t-102 -69.5t-63.5 -110t-22 -144.5v-67h330v-73h-328v-914h-97v914h-525v-914h-99v912l-143 10q-29 3 -29 25z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="556" />
<glyph unicode="&#x09;" horiz-adv-x="556" />
<glyph unicode="&#xa0;" horiz-adv-x="556" />
<glyph unicode="!" horiz-adv-x="521" d="M164 79q0 19 7 36.5t20 30.5t30 20.5t37 7.5q19 0 36.5 -7.5t30.5 -20.5t20.5 -30.5t7.5 -36.5q0 -20 -7.5 -37t-20.5 -30t-30 -20t-37 -7q-40 0 -67 27t-27 67zM214 863v586h95v-586q0 -46 -1 -87t-2.5 -83.5t-4.5 -89.5t-6 -104h-67q-3 57 -6 104t-4.5 89.5t-2.5 83.5 t-1 87z" />
<glyph unicode="&#x22;" horiz-adv-x="697" d="M158 1150v299h85v-299l-10 -157q-2 -17 -9 -27t-24 -10q-14 0 -22.5 10t-10.5 27zM455 1150v299h85v-299l-10 -157q-2 -17 -9 -27t-24 -10q-14 0 -22.5 10t-10.5 27z" />
<glyph unicode="#" d="M84 496l4 29h224l84 399h-260l6 39q4 34 46 33h220l88 414q3 19 15.5 29t31.5 10h42l-96 -453h306l95 453h43q17 0 26.5 -11.5t5.5 -31.5l-88 -410h228l-6 -39q-2 -17 -13 -25t-32 -8h-189l-84 -399h231q16 0 24 -9t5 -32l-4 -30h-267l-96 -454h-41q-17 0 -27 12.5 t-5 32.5l87 409h-305l-87 -413q-4 -23 -17.5 -32t-30.5 -9h-43l96 454h-186q-17 0 -25 9t-6 33zM394 525h305l84 399h-305z" />
<glyph unicode="$" d="M138 173l28 40q12 15 29 15q9 0 22 -11t32.5 -28t46.5 -37t62 -38t80 -31t103 -16l33 652q-70 22 -136 48t-118 67.5t-83.5 102t-31.5 153.5q0 72 27 137.5t78.5 117.5t127.5 83.5t174 34.5l9 171q0 14 8 23.5t21 9.5h37l-10 -206q103 -7 181 -45.5t142 -102.5l-22 -35 q-10 -17 -28 -17q-12 0 -32 16.5t-52 37.5t-79.5 40t-113.5 24l-30 -585q73 -24 144.5 -50t128 -67t91 -101.5t34.5 -152.5q0 -88 -29 -166t-85.5 -137t-137.5 -94.5t-186 -40.5l-11 -216q-1 -13 -9 -22.5t-22 -9.5h-37l13 248q-131 6 -228 56t-171 132zM297 1098 q0 -63 22.5 -108.5t61.5 -77.5t89.5 -55t108.5 -43l28 565q-78 -4 -135.5 -28t-96.5 -62.5t-58.5 -87.5t-19.5 -103zM607 67q82 5 145.5 33t107.5 73.5t66.5 105.5t22.5 129q0 68 -25.5 114.5t-68.5 79.5t-99 55t-117 41z" />
<glyph unicode="%" horiz-adv-x="1596" d="M102 1101q0 90 24 158.5t64.5 114t96.5 68.5t120 23q63 0 118.5 -23t97 -68.5t65.5 -114t24 -158.5q0 -89 -24.5 -157t-66.5 -113.5t-97.5 -68.5t-116.5 -23q-63 0 -119.5 23t-97 68.5t-64.5 113.5t-24 157zM181 1101q0 -79 18 -135t49 -91.5t71.5 -52.5t87.5 -17 q46 0 87.5 17t72.5 52.5t49 91.5t18 135t-18 135t-49 92.5t-72.5 53.5t-87.5 17t-87 -17t-72 -53.5t-49 -92.5t-18 -135zM222 0l1040 1431q6 8 14.5 13t22.5 5h69l-1040 -1431q-13 -18 -36 -18h-70zM886 344q0 90 23.5 158.5t64.5 114.5t96.5 69t119.5 23q63 0 118.5 -23 t97 -69t65 -114.5t23.5 -158.5q0 -89 -24.5 -157t-66 -113t-97 -68t-116.5 -23q-63 0 -119 23t-97 68t-64.5 113t-23.5 157zM964 344q0 -79 17.5 -134.5t48.5 -91.5t72.5 -52.5t87.5 -16.5t87.5 16.5t72 52.5t48.5 91.5t18 134.5t-18 136t-48.5 93t-72 53t-87.5 17 t-87.5 -17t-72.5 -53t-48.5 -93t-17.5 -136z" />
<glyph unicode="&#x26;" horiz-adv-x="1430" d="M110 371q0 74 26.5 141t72.5 122.5t109.5 99.5t137.5 72q-74 83 -110 159.5t-36 164.5q0 72 26 133t74.5 106t115.5 70.5t150 25.5q69 0 130 -23.5t106.5 -63.5t73 -92.5t29.5 -109.5l-56 -12q-10 -2 -18.5 4t-13.5 20q-5 28 -22 62.5t-48 65.5t-76 52t-105 21 q-61 0 -111.5 -19t-86.5 -53.5t-56 -81.5t-20 -103q0 -84 43 -161t134 -165l457 -450q46 74 73 153.5t35 154.5q2 13 8 20.5t18 7.5h57q-2 -96 -36 -198t-97 -195l304 -299h-91q-18 0 -30 4t-28 19l-210 205q-45 -53 -100 -97.5t-119.5 -77.5t-138.5 -51t-155 -18 q-77 0 -151.5 25.5t-133.5 75t-95 122t-36 164.5zM209 377q0 -79 30 -137.5t78.5 -98t107 -59t116.5 -19.5q73 0 136.5 16.5t119 46t102.5 70t85 87.5l-470 461l-11 12q-68 -28 -122.5 -68t-92.5 -89t-58.5 -105.5t-20.5 -116.5z" />
<glyph unicode="'" horiz-adv-x="400" d="M158 1150v299h85v-299l-10 -157q-2 -17 -9 -27t-24 -10q-14 0 -22.5 10t-10.5 27z" />
<glyph unicode="(" horiz-adv-x="528" d="M113 645q0 120 14.5 233t48 225t87 225.5t132.5 234.5l43 -27q8 -5 13 -16t-6 -28q-123 -196 -184.5 -406.5t-61.5 -440.5q0 -229 62 -439.5t184 -406.5q10 -17 5.5 -28t-12.5 -16l-43 -26q-79 121 -132.5 234t-87 225t-48 224.5t-14.5 232.5z" />
<glyph unicode=")" horiz-adv-x="528" d="M77.5 -229q-4.5 11 5.5 28q123 196 185 406.5t62 439.5q0 230 -62 440.5t-185 406.5q-10 17 -5.5 28t12.5 16l43 27q79 -121 132.5 -234.5t87 -225.5t48.5 -225t15 -233t-15 -232.5t-48.5 -224.5t-87 -225.5t-132.5 -233.5l-43 26q-8 5 -12.5 16z" />
<glyph unicode="*" horiz-adv-x="870" d="M153 1357l26 46l194 -113q17 -10 27 -20t24 -28q-8 20 -12 35t-4 33v219h53v-218q0 -18 -2.5 -32.5t-11.5 -34.5q12 17 23 27t27 19l192 112l27 -46l-193 -112q-15 -9 -28.5 -14t-30.5 -7q18 -3 31 -7t28 -13l194 -112l-26 -47l-194 113q-15 9 -25.5 18t-22.5 26l-2 -1 q7 -18 10.5 -31.5t3.5 -30.5v-219h-53v218q0 17 2.5 30.5t10.5 30.5v1q-11 -15 -22 -24t-26 -18l-193 -111l-25 45l192 112q14 8 28 12.5t34 7.5q-20 4 -34 8t-28 13z" />
<glyph unicode="+" d="M114 649v75h440v462h80v-462h441v-75h-441v-464h-80v464h-440z" />
<glyph unicode="," horiz-adv-x="448" d="M136 87q0 36 24.5 61.5t64.5 25.5q44 0 68 -30t24 -79q0 -42 -10.5 -86t-31 -85.5t-49 -80.5t-63.5 -72l-16 15q-5 4 -6.5 8.5t-1.5 9.5q0 6 8 16q9 10 25 30t33 47.5t31 61t18 72.5q-15 -5 -32 -5q-38 0 -62 25.5t-24 65.5z" />
<glyph unicode="-" horiz-adv-x="757" d="M141 566v83h476v-83h-476z" />
<glyph unicode="&#xad;" horiz-adv-x="757" d="M141 566v83h476v-83h-476z" />
<glyph unicode="." horiz-adv-x="478" d="M144 79q0 19 7.5 36.5t20 30.5t29.5 20.5t38 7.5q19 0 36.5 -7.5t30 -20.5t20.5 -30.5t8 -36.5q0 -20 -8 -37t-20.5 -30t-30 -20t-36.5 -7q-40 0 -67.5 27t-27.5 67z" />
<glyph unicode="/" horiz-adv-x="889" d="M78 -86l637 1538q15 38 55 38h42l-636 -1536q-8 -20 -24.5 -30t-33.5 -10h-40z" />
<glyph unicode="0" d="M75 725q0 190 40.5 329.5t111 230.5t164.5 135.5t202 44.5t202.5 -44.5t165 -135.5t111.5 -230.5t41 -329.5t-41 -329.5t-111.5 -230.5t-165.5 -135.5t-202 -44.5q-108 0 -202 44.5t-164.5 135.5t-111 230.5t-40.5 329.5zM175 725q0 -177 34 -301.5t91 -204t133 -115.5 t160 -36t160 36t133 115.5t91 204t34 301.5q0 176 -34 301t-91 204.5t-133 116t-160 36.5t-160 -36.5t-133 -116t-91 -204.5t-34 -301z" />
<glyph unicode="1" d="M209 1085l419 370h77v-1379h316v-76h-748v76h333v1175q0 35 3 74l-319 -284q-14 -12 -28.5 -9t-22.5 12z" />
<glyph unicode="2" d="M118 0v35q0 11 5.5 23t14.5 20l495 501q61 62 113.5 120.5t91 118t60 121.5t21.5 133q0 80 -27 139t-71 97t-102.5 56t-120.5 18q-69 0 -126.5 -21t-102 -58t-74 -89t-40.5 -113q-7 -23 -19 -30t-32 -5l-50 8q13 93 51 166t96 123t135 76t166 26q83 0 158.5 -24 t133 -72.5t91.5 -121.5t34 -172q0 -82 -25 -152t-68 -134.5t-100 -125.5t-119 -124l-454 -463q33 8 68 12t71 4h626q16 0 25.5 -9.5t9.5 -25.5v-57h-935z" />
<glyph unicode="3" d="M113 354l39 17q16 7 34 4t26 -24q5 -12 13.5 -38t25 -58t42.5 -65t65.5 -61t95 -45t131.5 -17q92 0 161 30.5t115.5 78t69.5 106.5t23 114q0 67 -21 124.5t-70 98.5t-129.5 65.5t-197.5 24.5v70q94 2 165.5 25t119.5 63t73 95.5t25 121.5q0 77 -26 133.5t-69 93.5 t-100 54t-120 17q-69 0 -127 -21t-102.5 -58t-74.5 -89t-41 -113q-6 -23 -17.5 -30t-31.5 -5l-50 8q13 93 51 166t96.5 123t135 76t165.5 26q83 0 157 -23t129.5 -69t88 -114.5t32.5 -158.5q0 -71 -21.5 -128t-60 -100t-90.5 -72t-114 -44q160 -30 241.5 -123.5t81.5 -234.5 q0 -89 -36 -165t-98.5 -131.5t-147.5 -86.5t-184 -31q-124 0 -206.5 33t-136 86.5t-83 119.5t-47.5 131z" />
<glyph unicode="4" d="M42 488l728 963h87v-957h265v-54q0 -11 -6.5 -17.5t-20.5 -6.5h-238v-416h-86v416h-685q-15 0 -23.5 6.5t-11.5 17.5zM150 494h621v758q0 17 1.5 38t3.5 42z" />
<glyph unicode="5" d="M146 113l30 40q9 15 29 15q13 0 38 -15.5t64.5 -34t94 -33.5t125.5 -15q84 0 157 27t126 78t83.5 126.5t30.5 170.5q0 78 -23 143.5t-70 112t-118 72.5t-167 26q-58 0 -124 -9t-139 -30l-63 20l113 642h652v-42q0 -20 -14 -34t-43 -14h-525l-86 -483q69 17 130 24.5 t116 7.5q114 0 200 -32t144 -90t87 -138t29 -174q0 -115 -39.5 -205.5t-108 -154t-159.5 -97t-194 -33.5q-59 0 -114 10.5t-102.5 28.5t-88 41.5t-71.5 48.5z" />
<glyph unicode="6" d="M139 461q0 85 43 192t142 240l395 532q17 24 52 24h86l-432 -564q-32 -42 -58.5 -79t-48.5 -73q60 62 143.5 97.5t181.5 35.5q93 0 172 -30.5t136 -86.5t89 -135t32 -178q0 -98 -35.5 -181t-98 -143.5t-151 -94t-193.5 -33.5q-101 0 -185 32.5t-144 94t-93 150t-33 200.5 zM234 437q0 -81 25.5 -149.5t71.5 -118t112.5 -77t149.5 -27.5q88 0 158.5 27.5t120.5 76t76.5 115.5t26.5 146q0 83 -26 150t-74 113.5t-113.5 72t-145.5 25.5q-90 0 -161 -31.5t-120 -81t-75 -113t-26 -128.5z" />
<glyph unicode="7" d="M128 1386v63h965v-44q0 -17 -4.5 -30t-9.5 -22l-663 -1316q-8 -16 -22 -26.5t-37 -10.5h-67l669 1311q13 27 32 48h-835q-11 0 -19.5 8t-8.5 19z" />
<glyph unicode="8" d="M123 383q0 80 23.5 142.5t65 108.5t99.5 76.5t127 46.5q-63 18 -112.5 50t-82.5 76t-50.5 97.5t-17.5 115.5q0 77 29 144.5t83.5 117.5t132 78.5t174.5 28.5q96 0 173.5 -28.5t132 -78.5t84 -118t29.5 -144q0 -61 -17.5 -115t-51.5 -97.5t-83 -76t-112 -50.5 q70 -15 127.5 -46t99.5 -77t65.5 -108.5t23.5 -142.5q0 -92 -35 -166t-97 -126t-148.5 -79.5t-190.5 -27.5t-191 27.5t-148.5 79.5t-96.5 126t-35 166zM223 384q0 -73 26.5 -132t75.5 -101.5t117.5 -66t151.5 -23.5t151 23.5t117 66t76 101.5t27 132q0 95 -35 158.5 t-89.5 101.5t-120 54t-126.5 16t-127 -16t-120 -54t-89 -101.5t-35 -158.5zM270 1095q0 -58 19 -112.5t59 -97t101 -67.5t145 -25q83 0 144 25t101 67.5t58.5 96.5t18.5 113q0 62 -21.5 116t-62.5 94t-101 62.5t-137 22.5t-137.5 -22.5t-102 -62.5t-63 -94t-21.5 -116z" />
<glyph unicode="9" d="M134 1030q0 93 35 172.5t96.5 138t146 91.5t184.5 33q96 0 176.5 -32.5t138.5 -92.5t90 -143.5t32 -185.5q0 -56 -11.5 -107.5t-34.5 -102.5t-56 -105t-75 -115l-380 -557q-15 -24 -50 -24h-89l429 598q31 44 57.5 82.5t47.5 75.5q-59 -67 -144 -103.5t-181 -36.5 q-89 0 -164.5 29.5t-130.5 83.5t-86 130.5t-31 170.5zM232 1036q0 -80 24.5 -144t69 -108.5t107 -68t139.5 -23.5q87 0 156 30t115.5 78t71 108t24.5 121q0 79 -25.5 144.5t-71.5 112t-109 72.5t-138 26q-81 0 -147.5 -25.5t-114 -71.5t-74.5 -110t-27 -141z" />
<glyph unicode=":" horiz-adv-x="491" d="M150 79q0 19 7.5 36.5t20 30.5t29.5 20.5t38 7.5q19 0 36.5 -7.5t30 -20.5t20.5 -30.5t8 -36.5q0 -20 -8 -37t-20.5 -30t-30 -20t-36.5 -7q-40 0 -67.5 27t-27.5 67zM150 895q0 19 7.5 36.5t20 30.5t29.5 20.5t38 7.5q19 0 36.5 -7.5t30 -20.5t20.5 -30.5t8 -36.5 q0 -20 -8 -37t-20.5 -30t-30 -20t-36.5 -7q-40 0 -67.5 27t-27.5 67z" />
<glyph unicode=";" horiz-adv-x="514" d="M161 895q0 19 7.5 36.5t20 30.5t29.5 20.5t38 7.5q19 0 36.5 -7.5t30 -20.5t20.5 -30.5t8 -36.5q0 -20 -8 -37t-20.5 -30t-30 -20t-36.5 -7q-40 0 -67.5 27t-27.5 67zM167 87q0 36 24.5 61.5t64.5 25.5q44 0 68 -30t24 -79q0 -42 -10.5 -86t-31 -85.5t-49 -80.5 t-63.5 -72l-16 15q-5 4 -6.5 8.5t-1.5 9.5q0 6 8 16q9 10 25 30t33 47.5t31 61t18 72.5q-15 -5 -32 -5q-38 0 -62 25.5t-24 65.5z" />
<glyph unicode="&#x3c;" d="M178 667v42l768 394v-67q0 -11 -6 -19t-21 -17l-567 -286q-16 -8 -34 -14.5t-38 -11.5q39 -9 72 -26l567 -288q15 -7 21 -16t6 -20v-67z" />
<glyph unicode="=" d="M170 494v75h848v-75h-848zM170 814v76h848v-76h-848z" />
<glyph unicode="&#x3e;" d="M242 271v67q0 11 6 20t20 16l568 288q16 8 34 14t38 11q-39 9 -72 27l-568 286q-27 14 -26 36v67l768 -394v-42z" />
<glyph unicode="?" horiz-adv-x="884" d="M96 1323q28 28 63 53.5t76 45.5t90.5 31.5t106.5 11.5q71 0 135 -21.5t111.5 -61.5t75.5 -97.5t28 -129.5q0 -78 -24 -134t-61 -98.5t-80 -75t-80.5 -60.5t-64 -57t-28.5 -63l-11 -168h-66l-7 175q-2 43 22 75.5t60.5 62.5t79.5 60.5t80.5 69.5t62.5 89t25 120 q0 55 -21.5 98.5t-58.5 74t-85 46t-100 15.5q-68 0 -117.5 -17.5t-83.5 -39.5t-53 -40t-27 -18q-15 0 -22 12zM303 79q0 19 7 36.5t20 30.5t30 20.5t37 7.5q19 0 37 -7.5t30.5 -20.5t20.5 -30.5t8 -36.5q0 -20 -8 -37t-20.5 -30t-30 -20t-37.5 -7q-40 0 -67 27t-27 67z" />
<glyph unicode="@" horiz-adv-x="1696" d="M123 562q0 171 60.5 317.5t165 254t243.5 168.5t298 61q137 0 263 -42.5t223 -127t154.5 -209.5t57.5 -290q0 -112 -29.5 -204t-81.5 -158t-121.5 -102.5t-148.5 -36.5q-90 0 -135.5 47.5t-47.5 137.5q-57 -96 -129.5 -138.5t-156.5 -42.5q-57 0 -99 20t-69.5 54.5 t-41.5 81.5t-14 102q0 87 33.5 174.5t97.5 157.5t157 114t212 44q54 0 101.5 -9t85.5 -27l-93 -355q-25 -94 -24 -152.5t19.5 -90.5t50 -43t67.5 -11q58 0 111 30.5t94 87.5t65.5 138t24.5 181q0 152 -50 265t-135.5 188t-199 111.5t-241.5 36.5q-140 0 -265.5 -54 t-220.5 -151.5t-150.5 -232t-55.5 -295.5q0 -181 57.5 -319t157 -230.5t232 -140t282.5 -47.5q152 0 277 35t228 101q12 8 22 5t15 -14l16 -38q-115 -76 -252.5 -117t-305.5 -41q-169 0 -315.5 54t-255 157t-171 253t-62.5 342zM594 457q0 -41 10 -76.5t30.5 -61t51 -40.5 t71.5 -15q37 0 76 12.5t75.5 44.5t68.5 87t53 140l82 317q-25 7 -51 11t-61 4q-92 0 -167 -37.5t-128 -98t-82 -135.5t-29 -152z" />
<glyph unicode="A" horiz-adv-x="1342" d="M27 0l593 1449h103l593 -1449h-81q-14 0 -23.5 7.5t-14.5 20.5l-170 421h-712l-170 -421q-4 -11 -14 -19.5t-25 -8.5h-79zM347 526h648l-295 732q-15 36 -29 87q-6 -25 -13.5 -47t-15.5 -41z" />
<glyph unicode="B" horiz-adv-x="1294" d="M214 0v1449h417q124 0 215 -24.5t150.5 -72t88 -116.5t28.5 -156q0 -57 -20 -110.5t-57.5 -98.5t-92 -78t-125.5 -51q171 -25 262.5 -113t91.5 -233q0 -92 -32.5 -165.5t-95 -125t-153.5 -78.5t-207 -27h-470zM319 84h364q190 0 288 82.5t98 231.5q0 69 -26 124t-75 93 t-121 58.5t-163 20.5h-365v-610zM319 771h314q100 0 171.5 25.5t117 67.5t67 95.5t21.5 109.5q0 146 -93 221.5t-286 75.5h-312v-595z" />
<glyph unicode="C" horiz-adv-x="1384" d="M120 725q0 165 51 301t143.5 234t221.5 151.5t284 53.5q148 0 263 -46t209 -130l-31 -46q-8 -12 -26 -12q-12 0 -40 22.5t-77 49.5t-122 49.5t-176 22.5q-131 0 -240 -45t-187.5 -129t-122.5 -204.5t-44 -271.5q0 -154 44.5 -274.5t122.5 -204t184 -128t229 -44.5 q78 0 139 10t112 30.5t95 50.5t87 69q5 4 10.5 7t10.5 3q10 0 17 -7l41 -44q-45 -48 -98 -86.5t-116 -66t-138 -42t-165 -14.5q-151 0 -276.5 52.5t-215.5 149.5t-139.5 234t-49.5 305z" />
<glyph unicode="D" horiz-adv-x="1556" d="M214 0v1449h529q154 0 281.5 -50t218.5 -144.5t141.5 -228.5t50.5 -301q0 -168 -50.5 -302t-141.5 -228.5t-218.5 -144.5t-281.5 -50h-529zM319 85h424q130 0 238 43t184.5 125t118.5 200.5t42 271.5q0 152 -42 270.5t-118.5 200.5t-184.5 125t-238 43h-424v-1279z" />
<glyph unicode="E" d="M214 0v1449h868v-87h-763v-586h635v-85h-635v-604h765l-2 -87h-868z" />
<glyph unicode="F" horiz-adv-x="1156" d="M214 0v1449h868v-87h-763v-609h661v-87h-661v-666h-105z" />
<glyph unicode="G" horiz-adv-x="1505" d="M120 725q0 168 50.5 304.5t145.5 233.5t229 149.5t302 52.5q83 0 153 -11t130.5 -33t112.5 -54.5t100 -74.5l-28 -45q-14 -25 -43 -11q-15 6 -44 28.5t-79 47t-124.5 44t-180.5 19.5q-141 0 -255.5 -45t-195 -129t-124 -204.5t-43.5 -271.5q0 -153 43.5 -274.5 t126 -206.5t200.5 -130t265 -45q65 0 119.5 8t104.5 23t96.5 37t94.5 49v391h-278q-11 0 -18.5 7t-7.5 16v57h399v-515q-109 -77 -231.5 -117.5t-276.5 -40.5q-172 0 -309 52.5t-234 149.5t-148.5 234t-51.5 305z" />
<glyph unicode="H" horiz-adv-x="1546" d="M214 0v1449h105v-676h908v676h105v-1449h-105v694h-908v-694h-105z" />
<glyph unicode="I" horiz-adv-x="533" d="M214 0v1449h105v-1449h-105z" />
<glyph unicode="J" horiz-adv-x="848" d="M27 13q1 14 2.5 28.5t3.5 29.5q2 9 7.5 16t19.5 7q9 0 22 -3.5t30.5 -7.5t41 -7.5t54.5 -3.5q78 0 140.5 24.5t106.5 75t67 128t23 181.5v968h104v-966q0 -122 -29.5 -215t-86 -156.5t-136.5 -95.5t-180 -32q-46 0 -93 7t-97 22z" />
<glyph unicode="K" horiz-adv-x="1300" d="M214 0v1449h105v-669h87q19 0 33.5 1.5t26 6t21.5 12t21 18.5l585 601q15 17 30 23.5t35 6.5h84l-633 -650q-19 -20 -34 -31.5t-33 -18.5q22 -6 39 -19t34 -34l667 -696h-85q-27 0 -38.5 6.5t-22.5 21.5l-613 626q-11 11 -20.5 19.5t-21.5 14t-28 8t-40 2.5h-94v-698 h-105z" />
<glyph unicode="L" horiz-adv-x="1036" d="M214 0v1449h105v-1360h687v-89h-792z" />
<glyph unicode="M" horiz-adv-x="1859" d="M214 0v1449h73q14 0 22 -3t17 -16l584 -1008q15 -29 27 -62q6 16 13 32.5t16 30.5l568 1007q8 13 17 16t23 3h72v-1449h-92v1235q0 27 4 56l-571 -1012q-14 -27 -41 -27h-16q-27 0 -41 27l-586 1014q3 -31 3 -58v-1235h-92z" />
<glyph unicode="N" horiz-adv-x="1546" d="M214 0v1449h52q14 0 22 -3.5t17 -15.5l939 -1255q-3 31 -3 58v1216h91v-1449h-49q-13 0 -22 4.5t-17 15.5l-941 1258q3 -31 3 -58v-1220h-92z" />
<glyph unicode="O" horiz-adv-x="1620" d="M120 725q0 167 50 303.5t141 233.5t218 150t281 53t281.5 -52.5t218.5 -150t141 -234t50 -303.5q0 -168 -50 -304.5t-141 -233.5t-218.5 -149.5t-281.5 -52.5t-281 52.5t-218 149.5t-141 233.5t-50 304.5zM226 725q0 -153 42.5 -273t120 -204t185 -128t236.5 -44 q130 0 237.5 44t184.5 128t119 204t42 273q0 152 -42 272.5t-119 204.5t-184.5 128.5t-237.5 44.5q-129 0 -236.5 -44.5t-185 -128.5t-120 -204.5t-42.5 -272.5z" />
<glyph unicode="P" horiz-adv-x="1183" d="M214 0v1449h375q255 0 384.5 -111.5t129.5 -318.5q0 -95 -36 -175t-102.5 -138t-161.5 -90t-214 -32h-270v-584h-105zM319 668h270q95 0 171 26.5t128.5 73.5t80.5 111t28 140q0 167 -102 257t-306 90h-270v-698z" />
<glyph unicode="Q" horiz-adv-x="1620" d="M120 725q0 167 50 303.5t141 233.5t218 150t281 53t281.5 -52.5t218.5 -150t141 -234t50 -303.5q0 -110 -22 -206.5t-63 -178t-100 -146t-132 -110.5l377 -403h-88q-40 0 -66 25l-309 334q-65 -27 -136.5 -41t-151.5 -14q-154 0 -281 52.5t-218 149.5t-141 233.5 t-50 304.5zM226 725q0 -153 42.5 -273t120 -204t185 -128t236.5 -44q130 0 237.5 44t184.5 128t119 204t42 273q0 152 -42 272.5t-119 204.5t-184.5 128.5t-237.5 44.5q-129 0 -236.5 -44.5t-185 -128.5t-120 -204.5t-42.5 -272.5z" />
<glyph unicode="R" horiz-adv-x="1243" d="M214 0v1449h371q250 0 374 -97t124 -284q0 -83 -28.5 -151.5t-82 -120.5t-130 -84.5t-171.5 -43.5q25 -15 44 -41l498 -627h-90q-17 0 -29 5.5t-22 21.5l-465 587q-8 11 -16.5 19.5t-19.5 13t-26 6.5t-36 2h-190v-655h-105zM319 733h251q96 0 172.5 23t129.5 66 t80.5 103.5t27.5 136.5q0 155 -101 229.5t-294 74.5h-266v-633z" />
<glyph unicode="S" horiz-adv-x="1095" d="M91 173l29 45q12 15 28 15q9 0 23.5 -12t35 -30t49.5 -39t66.5 -39t87 -30t111.5 -12q86 0 153.5 26t114.5 71t71.5 107t24.5 133q0 77 -31.5 126.5t-82.5 82.5t-116 55.5t-133.5 44t-133.5 48.5t-116.5 68t-82.5 103t-31 156q0 73 27.5 140t80.5 119t131.5 83t179.5 31 q113 0 202 -35.5t163 -109.5l-24 -47q-9 -17 -27 -18q-13 0 -34 19t-57 42t-90 42.5t-132 19.5q-79 0 -139 -22.5t-100.5 -61.5t-61.5 -90t-21 -107q0 -74 31 -122t82.5 -82t116.5 -57t133.5 -45.5t133.5 -49.5t116.5 -68t82.5 -101t31 -149q0 -91 -31 -171.5t-90.5 -140 t-145.5 -93.5t-197 -34q-143 0 -246.5 50.5t-181.5 138.5z" />
<glyph unicode="T" horiz-adv-x="1185" d="M30 1360v89h1126v-89h-509v-1360h-105v1360h-512z" />
<glyph unicode="U" horiz-adv-x="1488" d="M196 553v896h105v-895q0 -101 30 -188.5t87 -152.5t139.5 -101.5t186.5 -36.5t187 36t140 100.5t87.5 152.5t30.5 189v896h104v-896q0 -120 -37.5 -224t-109 -181t-173.5 -121t-229 -44t-228.5 44t-173 121t-109 181t-37.5 224z" />
<glyph unicode="V" horiz-adv-x="1342" d="M27 1449h83q14 0 23 -7.5t14 -20.5l494 -1202q8 -23 16.5 -48.5t15.5 -51.5q6 27 13 52.5t16 47.5l493 1202q4 11 14.5 19.5t24.5 8.5h82l-598 -1449h-93z" />
<glyph unicode="W" horiz-adv-x="2082" d="M48 1449h87q30 0 38 -28l366 -1191q11 -41 20 -93q5 27 10 50t13 43l409 1191q4 11 14.5 19.5t24.5 8.5h29q14 0 23.5 -7.5t14.5 -20.5l409 -1191q7 -19 13 -42.5t11 -49.5q5 26 9.5 49t9.5 43l366 1191q3 11 13.5 19.5t25.5 8.5h80l-456 -1449h-94l-428 1257 q-8 26 -15 54q-6 -28 -14 -54l-429 -1257h-93z" />
<glyph unicode="X" horiz-adv-x="1275" d="M41 0l524 743l-500 706h103q14 0 21 -6t12 -15l440 -633q6 15 15 30l425 602q5 9 13 15.5t18 6.5h100l-500 -699l521 -750h-104q-14 0 -22 8.5t-13 17.5l-457 668q-4 -15 -14 -30l-449 -638q-7 -9 -14.5 -17.5t-20.5 -8.5h-98z" />
<glyph unicode="Y" horiz-adv-x="1218" d="M20 1449h93q14 0 23 -6.5t16 -19.5l414 -665q13 -24 24.5 -46t19.5 -43q8 22 18.5 43.5t23.5 45.5l415 665q5 11 15 18.5t24 7.5h92l-536 -852v-597h-105v597z" />
<glyph unicode="Z" horiz-adv-x="1223" d="M71 0v38q0 11 3.5 20t8.5 17l922 1287h-908v87h1048v-36q0 -19 -15 -42l-919 -1284h927v-87h-1067z" />
<glyph unicode="[" horiz-adv-x="608" d="M207 -270v1823h325v-36q0 -13 -8.5 -21.5t-22.5 -8.5h-211v-1691h211q14 0 22.5 -8t8.5 -21v-37h-325z" />
<glyph unicode="\" horiz-adv-x="889" d="M78 1490h42q40 0 55 -38l637 -1538h-40q-17 0 -33.5 9.5t-24.5 30.5z" />
<glyph unicode="]" horiz-adv-x="608" d="M76 -233q0 13 8 21t22 8h211v1691h-211q-14 0 -22 8t-8 22v36h324v-1823h-324v37z" />
<glyph unicode="^" d="M177 732l385 717h61l388 -717h-70q-11 0 -20 6.5t-14 17.5l-285 533q-9 18 -16.5 34.5t-12.5 34.5q-5 -17 -12 -33.5t-15 -35.5l-281 -533q-4 -8 -13 -16t-23 -8h-72z" />
<glyph unicode="_" horiz-adv-x="922" d="M57 -210h807v-70h-807v70z" />
<glyph unicode="`" horiz-adv-x="819" d="M199 1465h94q25 0 37 -7t23 -25l161 -244h-56q-12 0 -21 3.5t-17 13.5z" />
<glyph unicode="a" horiz-adv-x="991" d="M87 247q0 65 37 120t115.5 96t202 65.5t296.5 28.5v107q0 141 -61 217t-180 76q-74 0 -126 -20.5t-87.5 -45t-58 -45t-38.5 -20.5q-11 0 -18 5.5t-12 13.5l-18 29q80 80 168 121t200 41q82 0 143.5 -26t102 -74.5t61 -117.5t20.5 -154v-664h-39q-31 0 -38 29l-15 131 q-42 -41 -83.5 -74t-86.5 -55.5t-97 -34.5t-115 -12q-52 0 -101 15t-87.5 47t-61.5 81.5t-23 119.5zM181 251q0 -51 17 -88.5t45 -61.5t65 -35.5t77 -11.5q59 0 109 13t92.5 36t79 54.5t72.5 68.5v267q-145 -5 -250 -23t-173 -49t-101 -73.5t-33 -96.5z" />
<glyph unicode="b" horiz-adv-x="1128" d="M168 0v1490h98v-648q67 90 156 141.5t207 51.5q92 0 164.5 -33.5t123.5 -97t77.5 -156.5t26.5 -210q0 -124 -29.5 -226t-86.5 -174t-139 -111.5t-187 -39.5q-109 0 -185.5 41.5t-131.5 123.5l-7 -126q-3 -26 -28 -26h-59zM266 222q59 -88 130.5 -123.5t160.5 -35.5 q91 0 159 33t114 94t68.5 148t22.5 196q0 211 -84 317t-236 106q-109 0 -189.5 -52t-145.5 -145v-538z" />
<glyph unicode="c" horiz-adv-x="965" d="M106 510q0 117 31 213t90.5 165.5t145 108t195.5 38.5q99 0 176 -31.5t135 -85.5l-26 -35q-5 -5 -9.5 -9t-13.5 -4q-10 0 -27.5 14t-47 30.5t-74 30t-108.5 13.5q-88 0 -156 -31t-115 -89.5t-71 -141.5t-24 -186q0 -108 25 -190.5t71 -139.5t111.5 -87t145.5 -30 q74 0 123.5 17.5t82 38t51 38t30.5 17.5t20 -10l26 -33q-24 -31 -59.5 -57.5t-80.5 -46t-98 -30t-110 -10.5q-97 0 -177.5 35t-138.5 102t-90.5 164.5t-32.5 221.5z" />
<glyph unicode="d" horiz-adv-x="1128" d="M109 507q0 112 29 208.5t85.5 167.5t138.5 112t190 41q103 0 179 -37.5t132 -112.5v604h98v-1490h-54q-27 0 -30 27l-11 160q-68 -92 -158 -146t-202 -54q-188 0 -292.5 130t-104.5 390zM209 507q0 -227 83.5 -334t241.5 -107q100 0 183 52t146 146v536 q-58 88 -129 123.5t-163 35.5q-90 0 -157.5 -32.5t-113 -92.5t-68.5 -143.5t-23 -183.5z" />
<glyph unicode="e" horiz-adv-x="1068" d="M106 530q0 110 31.5 202.5t90.5 159.5t144.5 105t193.5 38q86 0 160 -29.5t128 -86.5t85.5 -140t31.5 -190q0 -23 -6.5 -31t-20.5 -8h-741v-20q0 -115 26.5 -201.5t75.5 -145.5t119 -88.5t156 -29.5q77 0 133 16.5t94.5 37.5t61 38t32.5 17q13 0 21 -10l26 -33 q-25 -31 -65 -57.5t-90 -45.5t-106.5 -30t-114.5 -11q-104 0 -190 36t-147.5 106t-95 170.5t-33.5 230.5zM208 612h675q0 82 -23 147t-65 110t-100 69.5t-128 24.5q-79 0 -141 -25t-108 -71t-73.5 -110.5t-36.5 -144.5z" />
<glyph unicode="f" horiz-adv-x="697" d="M56 947v40h172v129q0 87 22.5 153t63.5 110t98.5 66t127.5 22q32 0 64 -5.5t58 -15.5l-4 -47q-2 -9 -8.5 -12t-19 -2.5t-31.5 3.5t-45 3q-50 0 -92.5 -15t-73.5 -48t-47.5 -85.5t-16.5 -129.5v-126h330v-73h-327v-914h-99v912l-143 10q-29 3 -29 25z" />
<glyph unicode="g" horiz-adv-x="1045" d="M79 -108q0 81 53 139t146 91q-51 17 -82.5 50.5t-31.5 90.5q0 22 8 45.5t24.5 46.5t40 43.5t53.5 36.5q-73 42 -113.5 111.5t-40.5 163.5q0 74 26 133.5t74.5 102.5t115.5 66.5t149 23.5q68 0 124.5 -16t101.5 -46h261v-34q0 -23 -29 -28l-155 -11q29 -39 44 -87t15 -104 q0 -74 -26 -134t-73.5 -102.5t-114.5 -66t-148 -23.5q-87 0 -159 27q-43 -25 -68 -58.5t-25 -65.5q0 -44 31 -67.5t82 -34.5t116 -14t132.5 -6.5t132.5 -13t115.5 -32.5t82 -64.5t31.5 -109.5q0 -63 -32 -122t-92 -104t-145 -72t-190 -27q-108 0 -188.5 22t-135.5 59.5 t-82.5 86.5t-27.5 104zM166 -96q0 -45 23 -83.5t67.5 -66.5t109 -44.5t148.5 -16.5q78 0 145 17.5t116.5 49t78 75.5t28.5 96q0 48 -25.5 77.5t-67 46t-96.5 23t-115 9.5t-120.5 6t-113.5 12q-39 -15 -71.5 -35t-56 -45.5t-37 -55.5t-13.5 -65zM227 708q0 -57 18.5 -104.5 t54 -81.5t86 -52.5t115.5 -18.5q66 0 117 18.5t85.5 52.5t53 81t18.5 105q0 57 -19 104.5t-54 81.5t-86 52.5t-115 18.5q-65 0 -115.5 -18.5t-86 -52.5t-54 -81.5t-18.5 -104.5z" />
<glyph unicode="h" horiz-adv-x="1126" d="M168 0v1490h98v-643q71 86 161.5 137t203.5 51q84 0 148 -26.5t106 -76.5t63.5 -121t21.5 -160v-651h-99v651q0 143 -65.5 224.5t-199.5 81.5q-99 0 -185.5 -50.5t-154.5 -140.5v-766h-98z" />
<glyph unicode="i" horiz-adv-x="455" d="M137 1370q0 18 7.5 35t19.5 29.5t28.5 19.5t34.5 7t35 -7t29 -19.5t19.5 -29.5t7.5 -35t-7.5 -34t-19.5 -28.5t-28.5 -19.5t-35.5 -7q-18 0 -34.5 7t-28.5 19.5t-19.5 28.5t-7.5 34zM179 0v1019h97v-1019h-97z" />
<glyph unicode="j" horiz-adv-x="455" d="M-67 -356l6 49q3 9 8 10.5t13.5 0t22 -4t33.5 -2.5q83 0 123 45t40 128v1149h97v-1149q0 -53 -15 -98t-45.5 -78.5t-76 -52.5t-106.5 -19q-30 0 -53.5 5t-46.5 17zM136 1370q0 18 7.5 35t19.5 29.5t28.5 19.5t34.5 7t35 -7t29.5 -19.5t20 -29.5t7.5 -35t-7.5 -34 t-20 -28.5t-29 -19.5t-35.5 -7q-18 0 -34.5 7t-28.5 19.5t-19.5 28.5t-7.5 34z" />
<glyph unicode="k" horiz-adv-x="974" d="M168 0v1490h98v-908h47q14 0 27 4t29 19l408 388q10 12 22.5 19t30.5 7h86l-449 -427q-18 -22 -40 -34q16 -8 28 -19t23 -25l472 -514h-85q-14 0 -26 4.5t-22 19.5l-428 458q-8 9 -15.5 15.5t-16 10t-19 5t-26.5 1.5h-46v-514h-98z" />
<glyph unicode="l" horiz-adv-x="452" d="M177 0v1490h98v-1490h-98z" />
<glyph unicode="m" horiz-adv-x="1648" d="M168 0v1019h54q26 0 30 -26l9 -147q59 85 136 137t173 52q112 0 179 -64.5t91 -178.5q18 62 51.5 108t76.5 76.5t93.5 44.5t103.5 14q76 0 136.5 -25t102.5 -74t64.5 -121t22.5 -164v-651h-97v651q0 150 -64.5 228t-184.5 78q-53 0 -102 -19t-86 -57.5t-59 -96 t-22 -133.5v-651h-99v651q0 148 -59 227t-172 79q-83 0 -153.5 -49t-126.5 -136v-772h-98z" />
<glyph unicode="n" horiz-adv-x="1126" d="M168 0v1019h54q26 0 30 -26l9 -152q71 88 163.5 141t206.5 53q84 0 148 -26.5t106 -76.5t63.5 -121t21.5 -160v-651h-99v651q0 143 -65.5 224.5t-199.5 81.5q-99 0 -185.5 -50.5t-154.5 -140.5v-766h-98z" />
<glyph unicode="o" horiz-adv-x="1142" d="M106 510q0 120 31.5 216.5t91.5 165.5t146 106t196 37t196.5 -37t146 -106t90.5 -165.5t31 -216.5t-31 -216t-90.5 -165t-146 -105.5t-196.5 -36.5t-196 36.5t-146 105.5t-91.5 165t-31.5 216zM207 510q0 -102 23 -184.5t68.5 -141t113.5 -90t159 -31.5t159.5 31.5 t114 90t68 141t22.5 184.5q0 101 -22.5 184t-68 142t-113.5 90.5t-160 31.5q-91 0 -159 -31.5t-113.5 -90.5t-68.5 -142t-23 -184z" />
<glyph unicode="p" horiz-adv-x="1129" d="M168 -360v1379h54q26 0 30 -26l10 -157q68 92 158.5 146t203.5 54q187 0 292 -130t105 -390q0 -112 -29 -208.5t-86 -167.5t-139.5 -112t-189.5 -41q-103 0 -179 37.5t-132 111.5v-496h-98zM266 222q30 -44 62 -74t68 -49t76.5 -27.5t85.5 -8.5q90 0 157.5 33t113 93 t69 143.5t23.5 183.5q0 227 -84 334t-241 107q-101 0 -183 -52t-147 -145v-538z" />
<glyph unicode="q" horiz-adv-x="1127" d="M109 507q0 112 29 208.5t85.5 167.5t138.5 112t190 41q105 0 182 -39.5t133 -116.5l10 113q4 26 30 26h54v-1379h-98v543q-68 -90 -157 -143t-200 -53q-188 0 -292.5 130t-104.5 390zM209 507q0 -227 83.5 -334t241.5 -107q100 0 183 52t146 146v536q-55 85 -127.5 122 t-164.5 37q-90 0 -157.5 -32.5t-113 -92.5t-68.5 -143.5t-23 -183.5z" />
<glyph unicode="r" horiz-adv-x="723" d="M168 0v1019h52q17 0 24.5 -7t9.5 -25l7 -214q24 62 54 111t69.5 82.5t88 51.5t108.5 18q32 0 62.5 -5.5t53.5 -19.5l-8 -68q-5 -16 -19 -16q-12 0 -37.5 7t-66.5 7q-59 0 -104.5 -18t-82 -53.5t-63.5 -88.5t-50 -122v-659h-98z" />
<glyph unicode="s" horiz-adv-x="870" d="M91 108l23 34q5 8 11.5 12.5t17.5 4.5q13 0 32.5 -16.5t51 -35.5t78 -35.5t115.5 -16.5q65 0 114 18t81.5 49t49.5 72.5t17 87.5q0 51 -24.5 84.5t-64.5 57t-90.5 40t-104 33t-104 36t-90.5 49t-64.5 72t-24.5 106.5q0 54 23.5 104t67 88t106 60.5t140.5 22.5 q93 0 165 -26.5t132 -81.5l-21 -34q-8 -14 -23 -14q-11 0 -29 12.5t-47 28.5t-72 29t-104 13q-55 0 -101 -16t-78 -42.5t-49.5 -62t-17.5 -74.5q0 -48 24.5 -80t64 -54.5t90.5 -38.5t104 -32.5t104 -36.5t90.5 -50t64 -73t24.5 -104q0 -67 -23.5 -124t-69 -99t-112 -66.5 t-152.5 -24.5q-108 0 -185.5 34t-139.5 90z" />
<glyph unicode="t" horiz-adv-x="701" d="M55 938v38l174 12l25 365q2 9 8 16t18 7h46v-389h321v-73h-321v-691q0 -42 10.5 -72t29.5 -49.5t44.5 -29t55.5 -9.5q37 0 63.5 11t46 24t32 23.5t19.5 10.5q8 0 16 -10l27 -43q-39 -43 -99 -69t-125 -26q-103 0 -161 57t-58 177v696h-146q-11 0 -18.5 6.5t-7.5 17.5z " />
<glyph unicode="u" horiz-adv-x="1125" d="M157 368v651h98v-651q0 -143 65.5 -225t199.5 -82q99 0 186 51t154 141v766h98v-1019h-54q-26 0 -30 27l-9 151q-71 -88 -163.5 -141t-205.5 -53q-85 0 -148.5 26.5t-105.5 76.5t-63.5 121t-21.5 160z" />
<glyph unicode="v" horiz-adv-x="1027" d="M40 1019h77q14 0 23.5 -7.5t12.5 -17.5l336 -804q15 -45 25 -89q9 44 26 89l338 804q5 11 13.5 18t20.5 7h74l-430 -1019h-86z" />
<glyph unicode="w" horiz-adv-x="1565" d="M51 1019h74q14 0 23.5 -7.5t12.5 -17.5l257 -804q6 -24 11 -45.5t8 -43.5q6 22 12.5 44t14.5 45l272 811q7 23 29 23h40q23 0 30 -23l270 -811q7 -24 13 -45t11 -43q4 22 9 43.5t12 44.5l259 804q4 11 13.5 18t21.5 7h70l-339 -1019h-73q-17 0 -24 22l-278 827 q-5 15 -9.5 30.5t-7.5 30.5q-5 -31 -15 -61l-283 -827q-6 -22 -24 -22h-70z" />
<glyph unicode="x" horiz-adv-x="960" d="M44 0l371 521l-357 498h94q14 0 21 -6t12 -15l299 -431q2 8 7 17.5t10 18.5l279 394q6 9 13.5 15.5t18.5 6.5h89l-355 -492l369 -527h-93q-14 0 -22 8.5t-14 17.5l-309 451q-4 -18 -15 -36l-295 -415q-7 -9 -15 -17.5t-20 -8.5h-88z" />
<glyph unicode="y" horiz-adv-x="1027" d="M40 1019h81q15 0 24.5 -7.5t12.5 -17.5l343 -792q6 -15 10.5 -31t8.5 -33q5 16 11 32l12 32l339 792q5 11 14 18t20 7h75l-585 -1347q-6 -14 -16 -23t-29 -9h-69l179 398z" />
<glyph unicode="z" horiz-adv-x="908" d="M67 0v39q0 17 15 38l640 865h-622v77h737v-41q0 -22 -15 -39l-638 -862h627v-77h-744z" />
<glyph unicode="{" horiz-adv-x="604" d="M84 610v63q36 0 65 11t50 32t32.5 49.5t11.5 61.5q0 56 -10 109.5t-21 107t-21 107t-10 111.5q0 62 19.5 115.5t56.5 92.5t90.5 61t121.5 22h56v-43q0 -11 -8 -17t-17 -6h-34q-44 0 -80.5 -15.5t-62.5 -44t-41 -69.5t-15 -91q0 -58 9.5 -113.5t20.5 -109.5t20.5 -107.5 t9.5 -108.5q0 -38 -13 -69.5t-34 -55t-48 -39.5t-57 -22q30 -7 57 -23t48 -39.5t34 -55t13 -69.5q0 -54 -9.5 -108t-20.5 -108.5t-20.5 -109.5t-9.5 -113q0 -50 15 -91t41 -69.5t62.5 -44t80.5 -15.5h34q9 0 17 -6t8 -17v-43h-56q-68 0 -121.5 22t-90.5 61.5t-56.5 92.5 t-19.5 116q0 57 10 110.5t21 107t21 106.5t10 110q0 34 -11.5 62t-32.5 49t-50 32t-65 11z" />
<glyph unicode="|" horiz-adv-x="488" d="M206 -364v1917h77v-1917h-77z" />
<glyph unicode="}" horiz-adv-x="604" d="M79 -227q0 11 8 17t17 6h34q44 0 80.5 15.5t62.5 44t41 69.5t15 91q0 57 -9.5 112.5t-20.5 110t-20 108t-9 108.5q0 38 12.5 69.5t33.5 55t48 39.5t57 23q-30 6 -57 22t-48 39.5t-33.5 55.5t-12.5 69q0 54 9 108t20 108t20.5 109t9.5 114q0 50 -15 91t-41 69.5t-62.5 44 t-80.5 15.5h-34q-9 0 -17 6t-8 17v43h56q68 0 121.5 -22t90.5 -61t56.5 -92.5t19.5 -115.5q0 -57 -10 -111t-21 -107.5t-21 -106.5t-10 -110q0 -34 11.5 -62t32.5 -49t50 -32t65 -11v-63q-36 0 -65 -11t-50 -32t-32.5 -49.5t-11.5 -61.5q0 -56 10 -109.5t21 -107t21 -107 t10 -110.5q0 -62 -19.5 -115.5t-56.5 -93t-90.5 -61.5t-121.5 -22h-56v43z" />
<glyph unicode="~" d="M146 444q0 56 16.5 104.5t47.5 84.5t75.5 56.5t101.5 20.5q53 0 108 -20t108 -44.5t103.5 -44.5t94.5 -20q38 0 69 13.5t52.5 38t33.5 58.5t12 74h80q0 -56 -16.5 -104.5t-47.5 -84t-76 -56t-102 -20.5q-52 0 -107 20t-108.5 44.5t-104 44.5t-94.5 20q-39 0 -69 -14 t-51.5 -38.5t-33.5 -58.5t-13 -74h-79z" />
<glyph unicode="&#xa1;" horiz-adv-x="471" d="M138 941q0 40 27.5 67t66.5 27q40 0 68.5 -27t28.5 -67q0 -20 -8 -37.5t-20.5 -30.5t-30.5 -20.5t-38 -7.5q-40 0 -67 28t-27 68zM188 203q0 46 1 87t3.5 83.5t5.5 89.5t6 104h65q3 -57 6.5 -104t5.5 -89.5t3 -83.5t1 -87v-563h-97v563z" />
<glyph unicode="&#xa2;" d="M169 510q0 115 32.5 211t95.5 165t155 108t210 41l10 210q1 13 8.5 23t21.5 10h37l-12 -245q89 -8 158.5 -38t123.5 -77l-25 -34q-5 -5 -9.5 -9t-12.5 -4q-9 0 -26 12t-45.5 27t-69.5 28.5t-98 18.5l-44 -895q77 2 129 19.5t86.5 37.5t54.5 36.5t31 16.5t20 -9l25 -32 q-24 -30 -59 -55t-80 -44t-98.5 -30.5t-112.5 -13.5l-10 -215q-1 -13 -9 -22.5t-22 -9.5h-37l12 247q-98 6 -179 44.5t-139.5 105t-90 160.5t-31.5 212zM265 510q0 -100 24.5 -179.5t69.5 -136.5t110 -89.5t144 -39.5l44 894q-94 -3 -167 -35.5t-123 -90.5t-76 -140 t-26 -183z" />
<glyph unicode="&#xa3;" d="M62 647v33q0 15 10 25.5t27 10.5h169v301q0 95 27 177t79.5 142t132 94t183.5 34q78 0 137.5 -19t104 -51.5t76 -76.5t54.5 -93l-39 -23q-15 -7 -28.5 -5t-24.5 17q-20 32 -43.5 62.5t-55 54t-75.5 37t-106 13.5q-80 0 -140 -26.5t-100.5 -74t-61 -114.5t-20.5 -148v-301 h490v-40q0 -11 -8.5 -20t-22.5 -9h-459v-311q0 -91 -37 -153t-102 -106q23 4 45 6t45 2h806v-41q0 -15 -12 -29.5t-33 -14.5h-993v67q35 13 67.5 33.5t58.5 50t40.5 69.5t14.5 93v334h-206z" />
<glyph unicode="&#xa4;" d="M163 1066l50 51l158 -158q46 38 102 59t121 21q63 0 119.5 -21t102.5 -56l157 156l50 -51l-157 -157q38 -46 59 -102t21 -121q0 -63 -21 -119t-58 -102l158 -158l-51 -51l-157 158q-46 -37 -102.5 -58t-120.5 -21q-63 0 -119 20.5t-102 57.5l-159 -158l-50 51l158 158 q-37 46 -58 102t-21 120q0 63 20.5 119t57.5 102zM316 687q0 -57 22 -107.5t59.5 -87.5t88.5 -59t108 -22t108 22t88.5 59t59.5 87.5t22 107.5t-22 108t-59.5 89t-88.5 60t-108 22t-108 -22t-88.5 -60t-59.5 -89t-22 -108z" />
<glyph unicode="&#xa5;" d="M92 1449h82q28 0 39 -26l354 -681q9 -20 16 -38.5t12 -35.5q5 18 11 36t15 38l354 681q5 11 14.5 18.5t24.5 7.5h82l-432 -818h354v-60h-375v-120h375v-60h-375v-391h-98v391h-375v60h375v120h-375v60h354z" />
<glyph unicode="&#xa6;" horiz-adv-x="494" d="M209 -360v795h77v-795h-77zM209 757v796h77v-796h-77z" />
<glyph unicode="&#xa7;" horiz-adv-x="993" d="M119 731q0 79 46.5 142t152.5 101q-28 18 -51 40t-40 48.5t-27 57.5t-10 69q0 55 24 104.5t67.5 88t106 61t139.5 22.5q93 0 165.5 -27t132.5 -81l-21 -35q-7 -13 -23 -13q-11 0 -29 12.5t-47 28t-71.5 28.5t-104.5 13q-57 0 -103.5 -16t-79.5 -43t-50.5 -63t-17.5 -76 q0 -47 25.5 -82t67.5 -63t95 -51.5t109 -48t109 -53t95 -64t67.5 -83.5t25.5 -111q0 -84 -42.5 -147t-127.5 -99q55 -39 89.5 -89.5t34.5 -121.5q0 -67 -24 -124t-69.5 -99t-112 -66.5t-151.5 -24.5q-108 0 -185.5 34t-139.5 90l24 35q5 8 11 12t18 4q13 0 32.5 -16.5 t51 -35.5t79 -35t116.5 -16q63 0 112.5 17t83.5 48t51 72.5t17 89.5q0 52 -26.5 91t-70.5 69t-100 54.5t-113.5 49.5t-113.5 51.5t-100 62t-70.5 81t-26.5 107.5zM206 744q0 -42 20 -75t54 -60t79 -49.5t94.5 -44t100 -43.5t96.5 -48q73 33 104.5 81.5t31.5 110.5 q0 46 -18 82t-49.5 64.5t-73 51.5t-87 44t-93 41t-91.5 43q-92 -39 -130 -88t-38 -110z" />
<glyph unicode="&#xa8;" horiz-adv-x="819" d="M144 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 31.5 -7t27.5 -18.5t19 -27t7 -32.5q0 -16 -7 -31t-19 -26.5t-27 -18t-32 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5zM506 1315q0 35 24.5 60t59.5 25q16 0 31.5 -7t26.5 -18.5t18.5 -27t7.5 -32.5q0 -16 -7.5 -31 t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-17 0 -32.5 6.5t-27 18t-18 26t-6.5 31.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1722" d="M122 725q0 101 26.5 196t74.5 177t116 149.5t149.5 115.5t176 74.5t196.5 26.5q101 0 196 -26.5t177 -74.5t149.5 -115.5t115.5 -149.5t75 -177t27 -196q0 -102 -27 -196.5t-75 -176.5t-115.5 -149.5t-149.5 -115.5t-176.5 -74.5t-196.5 -26.5t-196.5 26.5t-176 74.5 t-149.5 115.5t-116 149.5t-74.5 176.5t-26.5 196.5zM183 725q0 -142 53 -266.5t145 -217.5t215.5 -146t264.5 -53q94 0 181 24t163 68.5t138 106.5t106.5 138t68.5 163.5t24 182.5q0 142 -53 266.5t-146 218t-217 147t-265 53.5t-264.5 -53.5t-215.5 -147t-145 -218 t-53 -266.5zM423 726q0 102 34.5 187t95 146t145.5 95t187 34q53 0 97.5 -8t82.5 -22.5t71 -35.5t64 -49l-23 -34q-7 -11 -22 -11q-9 0 -26 13.5t-47 29t-77 29t-116 13.5q-85 0 -154.5 -27t-119.5 -77.5t-77 -122t-27 -160.5q0 -92 27 -164t76 -121.5t115 -75.5t143 -26 q56 0 97.5 7.5t74 20.5t59.5 31t56 38q10 1 18 -4l34 -36q-60 -60 -142 -96t-201 -36q-98 0 -180 33.5t-141 94t-91.5 145.5t-32.5 189z" />
<glyph unicode="&#xaa;" horiz-adv-x="752" d="M126 1005q0 37 21.5 70t69 58.5t123.5 41t183 17.5v52q0 78 -34.5 118.5t-109.5 40.5q-44 0 -74.5 -11.5t-51 -24t-34 -24t-24.5 -11.5q-8 0 -13.5 4t-9.5 10l-12 23q49 49 103 72t123 23q103 0 156.5 -59.5t53.5 -160.5v-385h-33q-11 0 -16 4t-10 16l-11 69 q-26 -24 -50.5 -42t-52 -30.5t-58.5 -19t-68 -6.5q-34 0 -64.5 9t-54 28t-38 48.5t-14.5 69.5zM197 1009q0 -28 9.5 -48t26.5 -33t38.5 -18.5t44.5 -5.5q34 0 63 7.5t54 20t47 30t43 37.5v143q-172 -5 -249 -40.5t-77 -92.5z" />
<glyph unicode="&#xab;" horiz-adv-x="813" d="M115 525v14l246 387l31 -16q30 -15 10 -49l-187 -300q-13 -22 -24 -30q9 -7 24 -28l187 -301q19 -34 -10 -49l-31 -16zM376 525v14l246 387l31 -16q30 -15 10 -49l-187 -300q-13 -22 -24 -30q9 -7 24 -28l187 -301q19 -34 -10 -49l-31 -16z" />
<glyph unicode="&#xac;" d="M171 649v75h849v-377h-84v302h-765z" />
<glyph unicode="&#xae;" horiz-adv-x="1722" d="M122 725q0 101 26.5 196t74.5 177t116 149.5t149.5 115.5t176 74.5t196.5 26.5q101 0 196 -26.5t177 -74.5t149.5 -115.5t115.5 -149.5t75 -177t27 -196q0 -102 -27 -196.5t-75 -176.5t-115.5 -149.5t-149.5 -115.5t-176.5 -74.5t-196.5 -26.5t-196.5 26.5t-176 74.5 t-149.5 115.5t-116 149.5t-74.5 176.5t-26.5 196.5zM183 725q0 -142 53 -266.5t145 -217.5t215.5 -146t264.5 -53q94 0 181 24t163 68.5t138 106.5t106.5 138t68.5 163.5t24 182.5q0 142 -53 266.5t-146 218t-217 147t-265 53.5t-264.5 -53.5t-215.5 -147t-145 -218 t-53 -266.5zM587 272v907h253q158 0 237.5 -59t79.5 -180q0 -102 -65.5 -167.5t-185.5 -82.5q22 -12 40 -41l292 -377h-81q-11 0 -18 3.5t-14 13.5l-277 359q-8 11 -20.5 18t-41.5 7h-112v-401h-87zM674 738h151q124 0 185 50t61 143q0 94 -54.5 137t-176.5 43h-166v-373z " />
<glyph unicode="&#xaf;" horiz-adv-x="819" d="M130 1282v67h558v-67h-558z" />
<glyph unicode="&#xb0;" horiz-adv-x="846" d="M115 1161q0 63 24 119t65.5 97.5t97.5 65t121 23.5t121.5 -23.5t98 -65t65.5 -97t24 -119.5q0 -63 -24 -118.5t-65.5 -97t-98.5 -65.5t-121 -24q-65 0 -121 24t-97.5 65.5t-65.5 97t-24 118.5zM188 1161q0 -49 18.5 -92t50 -74.5t74.5 -50t92 -18.5t92 18.5t75 50 t50 74.5t18 92t-18 92t-50 75.5t-75 51t-92 18.5t-92 -18.5t-74.5 -51t-50 -75.5t-18.5 -92z" />
<glyph unicode="&#xb1;" d="M113 82v75h961v-75h-961zM113 690v75h440v416h80v-416h441v-75h-441v-411h-80v411h-440z" />
<glyph unicode="&#xb2;" horiz-adv-x="684" d="M94 922v25q0 8 3 16t12 15l240 243q29 29 54 57.5t44 57.5t29.5 60t10.5 64q0 36 -11.5 62.5t-31.5 44t-46.5 26t-55.5 8.5q-63 0 -103 -35.5t-55 -93.5q-5 -11 -12.5 -16t-24.5 -3l-38 7q14 98 77.5 150t158.5 52q45 0 85 -13t69 -38t46 -62.5t17 -86.5 q0 -41 -12.5 -76.5t-34 -67.5t-50.5 -62.5t-60 -61.5l-211 -215q19 5 39 8t40 3h282q25 0 25 -25v-43h-486z" />
<glyph unicode="&#xb3;" horiz-adv-x="684" d="M90 1102l31 14q12 5 25 3t18 -16q3 -8 10.5 -28.5t26 -43t50.5 -39.5t83 -17q42 0 73 14t52 35.5t32 48t11 52.5q0 34 -10.5 60.5t-34 45.5t-61.5 29t-92 10v50q93 2 138 40.5t45 103.5q0 35 -11 60.5t-31 42.5t-46 25t-55 8q-65 0 -104 -35.5t-54 -91.5 q-5 -13 -12.5 -17.5t-21.5 -2.5l-38 7q14 98 77 150t156 52q44 0 83 -12.5t67.5 -36.5t45 -58.5t16.5 -78.5q0 -68 -36.5 -112.5t-97.5 -62.5q75 -17 114 -61t39 -111q0 -49 -19 -88.5t-52.5 -67.5t-77 -43.5t-92.5 -15.5q-61 0 -103 15.5t-70 41.5t-45.5 60t-28.5 71z" />
<glyph unicode="&#xb4;" horiz-adv-x="819" d="M322 1189l160 244q11 18 23.5 25t37.5 7h97l-222 -259q-8 -10 -17 -13.5t-21 -3.5h-58z" />
<glyph unicode="&#xb5;" horiz-adv-x="1302" d="M204 -317v1336h98v-636q0 -72 16.5 -129t49 -97t82.5 -61t117 -21q108 0 194 56.5t147 159.5v728h98v-775q0 -46 13 -80t36.5 -56t57.5 -32t74 -10h43v-40q0 -13 -21 -24t-61 -11q-43 0 -82 12t-69.5 38.5t-51 66.5t-25.5 96q-68 -98 -158.5 -154t-199.5 -56 q-102 0 -169 39.5t-103 112.5q4 -44 5.5 -89t1.5 -82v-335h-49q-20 0 -32 11t-12 32z" />
<glyph unicode="&#xb6;" horiz-adv-x="1344" d="M78 1089q0 82 31 148.5t90 113.5t143 72.5t189 25.5h742v-84h-231v-1560h-88v1560h-336v-1560h-87v911q-105 0 -189 28.5t-143 78.5t-90 118.5t-31 147.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="495" d="M144 606q0 22 8.5 41.5t22 33.5t32 22.5t39.5 8.5q22 0 41 -8.5t33.5 -22.5t22.5 -33.5t8 -41.5q0 -20 -8 -39t-22.5 -33t-34 -22t-40.5 -8q-20 0 -39 8t-32.5 22t-22 33t-8.5 39z" />
<glyph unicode="&#xb8;" horiz-adv-x="819" d="M264 -323l10 29q6 11 17 11q5 0 13.5 -5.5t22.5 -11.5t34.5 -11.5t50.5 -5.5q53 0 81.5 22.5t28.5 60.5q0 26 -13 43t-37.5 27.5t-57.5 17.5t-73 12l47 146h57l-32 -108q93 -15 140.5 -46.5t47.5 -91.5q0 -31 -14 -55.5t-39 -41.5t-59.5 -26t-75.5 -9q-42 0 -82 12 t-67 31z" />
<glyph unicode="&#xb9;" horiz-adv-x="684" d="M137 1469l216 188h60v-685h150v-50h-387v50h162v553l4 38l-147 -129q-10 -8 -20 -5.5t-15 8.5z" />
<glyph unicode="&#xba;" horiz-adv-x="827" d="M121 1155q0 71 20 127.5t58 97t92.5 62t123.5 21.5t122.5 -21.5t91.5 -62t58 -97t20 -127.5t-20 -127.5t-58 -97t-92 -62t-122 -21.5q-69 0 -123.5 21.5t-92.5 62t-58 97t-20 127.5zM198 1155q0 -117 54 -183t163 -66q108 0 161.5 66t53.5 183q0 116 -54 182.5t-161 66.5 q-109 0 -163 -66.5t-54 -182.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="814" d="M151 202l187 301q14 22 23 28q-10 8 -23 30l-187 300q-19 34 10 49l30 16l247 -387v-14l-247 -388l-30 16q-30 15 -10 49zM412 202l187 301q14 22 23 28q-10 8 -23 30l-187 300q-19 34 10 49l30 16l247 -387v-14l-247 -388l-30 16q-30 15 -10 49z" />
<glyph unicode="&#xbc;" horiz-adv-x="1479" d="M105 1265l216 188h60v-685h150v-50h-387v50h162v553l4 38l-147 -129q-10 -8 -20 -5.5t-15 8.5zM289 0l819 1413q10 17 23 26.5t33 9.5h43l-821 -1417q-11 -18 -25 -25t-31 -7h-41zM836 249l361 483h68v-479h130v-37q0 -16 -18 -16h-112v-200h-62v200h-334q-23 0 -27 16z M909 253h294v343q0 13 1 27.5t3 28.5z" />
<glyph unicode="&#xbd;" horiz-adv-x="1461" d="M108 1265l216 188h60v-685h150v-50h-387v50h162v553l4 38l-147 -129q-10 -8 -20 -5.5t-15 8.5zM245 0l819 1413q10 17 23 26.5t33 9.5h43l-821 -1417q-11 -18 -25 -25t-31 -7h-41zM873 0v25q0 8 3 16t12 15l240 243q29 29 54 57.5t44 57.5t29.5 60t10.5 64 q0 36 -11.5 62.5t-31.5 44t-46.5 26t-55.5 8.5q-63 0 -103 -35.5t-55 -93.5q-5 -11 -12.5 -16t-24.5 -3l-38 7q14 98 77.5 150t158.5 52q45 0 85 -13t69 -38t46 -62.5t17 -86.5q0 -41 -12.5 -76.5t-34 -67.5t-50.5 -62.5t-60 -61.5l-211 -215q19 5 39 8t40 3h282 q25 0 25 -25v-43h-486z" />
<glyph unicode="&#xbe;" horiz-adv-x="1500" d="M103 898l31 14q12 5 25 3t18 -16q3 -8 10.5 -28.5t26 -43t50.5 -39.5t83 -17q42 0 73 14t52 35.5t32 48t11 52.5q0 34 -10.5 60.5t-34 45.5t-61.5 29t-92 10v50q93 2 138 40.5t45 103.5q0 35 -11 60.5t-31 42.5t-46 25t-55 8q-65 0 -104 -35.5t-54 -91.5 q-5 -13 -12.5 -17.5t-21.5 -2.5l-38 7q14 98 77 150t156 52q44 0 83 -12.5t67.5 -36.5t45 -58.5t16.5 -78.5q0 -68 -36.5 -112.5t-97.5 -62.5q75 -17 114 -61t39 -111q0 -49 -19 -88.5t-52.5 -67.5t-77 -43.5t-92.5 -15.5q-61 0 -103 15.5t-70 41.5t-45.5 60t-28.5 71z M317 0l819 1413q10 17 23 26.5t33 9.5h43l-821 -1417q-11 -18 -25 -25t-31 -7h-41zM856 249l361 483h68v-479h130v-37q0 -16 -18 -16h-112v-200h-62v200h-334q-23 0 -27 16zM929 253h294v343q0 13 1 27.5t3 28.5z" />
<glyph unicode="&#xbf;" horiz-adv-x="864" d="M93 -65q0 78 24.5 133t61 94.5t79.5 68.5t81 54.5t63.5 52.5t28.5 62l11 167h67l6 -174q2 -44 -22 -75t-60.5 -58.5t-80 -54.5t-80 -62t-62 -83.5t-25.5 -116.5q0 -55 21.5 -99t58.5 -74t85 -46t101 -16q68 0 116.5 18t82.5 40t53.5 40t27.5 18q14 0 23 -13l25 -40 q-28 -28 -62.5 -53.5t-76 -45.5t-90.5 -32t-107 -12q-71 0 -134.5 21t-111 60.5t-76 96.5t-28.5 129zM383 940q0 19 7 36.5t20 30.5t30 20.5t37 7.5q19 0 36.5 -7.5t30.5 -20.5t20.5 -30.5t7.5 -36.5q0 -20 -7.5 -37t-20.5 -30t-30 -20t-37 -7q-40 0 -67 27t-27 67z" />
<glyph unicode="&#xc0;" horiz-adv-x="1342" d="M27 0l593 1449h103l593 -1449h-81q-14 0 -23.5 7.5t-14.5 20.5l-170 421h-712l-170 -421q-4 -11 -14 -19.5t-25 -8.5h-79zM347 526h648l-295 732q-15 36 -29 87q-6 -25 -13.5 -47t-15.5 -41zM362 1885h111q25 0 37.5 -3.5t25.5 -18.5l244 -284h-66q-13 0 -22 2t-18 10z " />
<glyph unicode="&#xc1;" horiz-adv-x="1342" d="M27 0l593 1449h103l593 -1449h-81q-14 0 -23.5 7.5t-14.5 20.5l-170 421h-712l-170 -421q-4 -11 -14 -19.5t-25 -8.5h-79zM347 526h648l-295 732q-15 36 -29 87q-6 -25 -13.5 -47t-15.5 -41zM563 1579l264 284q12 14 25 18t38 4h116l-336 -294q-9 -8 -18.5 -10t-21.5 -2 h-67z" />
<glyph unicode="&#xc2;" horiz-adv-x="1342" d="M27 0l593 1449h103l593 -1449h-81q-14 0 -23.5 7.5t-14.5 20.5l-170 421h-712l-170 -421q-4 -11 -14 -19.5t-25 -8.5h-79zM347 526h648l-295 732q-15 36 -29 87q-6 -25 -13.5 -47t-15.5 -41zM389 1579l241 273h84l240 -273h-72q-8 0 -18.5 3t-21.5 14l-164 191 q-5 6 -7 11l-8 -11l-163 -191q-11 -11 -22 -14t-19 -3h-70z" />
<glyph unicode="&#xc3;" horiz-adv-x="1342" d="M27 0l593 1449h103l593 -1449h-81q-14 0 -23.5 7.5t-14.5 20.5l-170 421h-712l-170 -421q-4 -11 -14 -19.5t-25 -8.5h-79zM347 526h648l-295 732q-15 36 -29 87q-6 -25 -13.5 -47t-15.5 -41zM386 1590q0 35 10.5 67.5t31 57t50 39t66.5 14.5q40 0 73.5 -17.5t65 -38.5 t60 -39t58.5 -18q25 0 44 9t32.5 24.5t20.5 35.5t7 41h61q0 -36 -10.5 -68t-30.5 -56.5t-48.5 -39t-66.5 -14.5q-39 0 -73 18t-65 39t-60.5 39t-58.5 18q-24 0 -43 -9.5t-32.5 -24.5t-21 -35t-8.5 -42h-62z" />
<glyph unicode="&#xc4;" horiz-adv-x="1342" d="M27 0l593 1449h103l593 -1449h-81q-14 0 -23.5 7.5t-14.5 20.5l-170 421h-712l-170 -421q-4 -11 -14 -19.5t-25 -8.5h-79zM347 526h648l-295 732q-15 36 -29 87q-6 -25 -13.5 -47t-15.5 -41zM387 1705q0 16 6.5 31.5t18 27.5t26 18.5t31.5 6.5q16 0 31.5 -6.5t27 -18.5 t18.5 -27.5t7 -31.5t-7 -31t-18.5 -26t-27 -17.5t-31.5 -6.5t-31 6.5t-26.5 17.5t-18 26t-6.5 31zM787 1705q0 34 24.5 59t58.5 25q16 0 31 -6.5t27 -18.5t18.5 -27.5t6.5 -31.5t-6.5 -31t-18.5 -26t-26.5 -17.5t-31.5 -6.5t-32 6.5t-26 17.5t-18 26t-7 31z" />
<glyph unicode="&#xc5;" horiz-adv-x="1342" d="M27 0l593 1449h103l593 -1449h-81q-14 0 -23.5 7.5t-14.5 20.5l-170 421h-712l-170 -421q-4 -11 -14 -19.5t-25 -8.5h-79zM347 526h648l-295 732q-15 36 -29 87q-6 -25 -13.5 -47t-15.5 -41zM501 1725q0 35 13.5 65.5t36.5 52t54 33.5t66 12t66.5 -12t54.5 -33.5t37 -52 t14 -65.5q0 -36 -14 -65.5t-37 -51t-55 -33.5t-66 -12q-35 0 -66 12t-54 33.5t-36.5 51t-13.5 65.5zM555 1725q0 -51 32 -83.5t85 -32.5q51 0 83.5 32.5t32.5 83.5t-32.5 84t-83.5 33q-52 0 -84.5 -33t-32.5 -84z" />
<glyph unicode="&#xc6;" horiz-adv-x="1899" d="M10 0l801 1449h983v-87h-835l75 -586h632v-85h-622l77 -604h674l-1 -87h-760l-57 449h-610l-233 -423q-13 -26 -43 -26h-81zM410 526h557l-108 843q-8 -27 -18.5 -50.5t-21.5 -46.5z" />
<glyph unicode="&#xc7;" horiz-adv-x="1384" d="M120 725q0 165 51 301t143.5 234t221.5 151.5t284 53.5q148 0 263 -46t209 -130l-31 -46q-8 -12 -26 -12q-12 0 -40 22.5t-77 49.5t-122 49.5t-176 22.5q-131 0 -240 -45t-187.5 -129t-122.5 -204.5t-44 -271.5q0 -154 44.5 -274.5t122.5 -204t184 -128t229 -44.5 q78 0 139 10t112 30.5t95 50.5t87 69q5 4 10 7t11 3q10 0 17 -7l41 -44q-43 -46 -93 -83.5t-110 -64.5t-130.5 -42.5t-154.5 -18.5l-24 -80q93 -15 140.5 -46.5t47.5 -91.5q0 -31 -13.5 -55.5t-39 -41.5t-60 -26t-75.5 -9q-42 0 -82 12t-67 31l11 29q6 11 16 11q5 0 14 -5.5 t22.5 -11.5t34 -11.5t50.5 -5.5q53 0 81.5 22.5t28.5 60.5q0 26 -13 43t-37 27.5t-57.5 17.5t-73.5 12l38 118q-145 5 -265 59.5t-206 151.5t-133.5 231t-47.5 299z" />
<glyph unicode="&#xc8;" d="M214 0v1449h868v-87h-763v-586h635v-85h-635v-604h765l-2 -87h-868zM350 1885h111q25 0 37.5 -3.5t25.5 -18.5l244 -284h-66q-13 0 -22 2t-18 10z" />
<glyph unicode="&#xc9;" d="M214 0v1449h868v-87h-763v-586h635v-85h-635v-604h765l-2 -87h-868zM551 1579l264 284q12 14 25 18t38 4h116l-336 -294q-9 -8 -18.5 -10t-21.5 -2h-67z" />
<glyph unicode="&#xca;" d="M214 0v1449h868v-87h-763v-586h635v-85h-635v-604h765l-2 -87h-868zM377 1579l240 273h84l241 -273h-73q-8 0 -18 3t-22 14l-163 191q-5 6 -8 11l-8 -11l-163 -191q-11 -11 -21.5 -14t-19.5 -3h-69z" />
<glyph unicode="&#xcb;" d="M214 0v1449h868v-87h-763v-586h635v-85h-635v-604h765l-2 -87h-868zM375 1705q0 16 6.5 31.5t18 27.5t26 18.5t31.5 6.5q16 0 31.5 -6.5t27 -18.5t18.5 -27.5t7 -31.5t-7 -31t-18.5 -26t-27 -17.5t-31.5 -6.5t-31 6.5t-26.5 17.5t-18 26t-6.5 31zM775 1705q0 34 24.5 59 t58.5 25q16 0 31 -6.5t27 -18.5t18.5 -27.5t6.5 -31.5t-6.5 -31t-18.5 -26t-26.5 -17.5t-31.5 -6.5t-32 6.5t-26 17.5t-18 26t-7 31z" />
<glyph unicode="&#xcc;" horiz-adv-x="533" d="M-39 1885h111q25 0 37.5 -3.5t25.5 -18.5l244 -284h-66q-13 0 -22 2t-18 10zM214 0v1449h105v-1449h-105z" />
<glyph unicode="&#xcd;" horiz-adv-x="533" d="M162 1579l264 284q12 14 25 18t38 4h116l-336 -294q-9 -8 -18.5 -10t-21.5 -2h-67zM214 0v1449h105v-1449h-105z" />
<glyph unicode="&#xce;" horiz-adv-x="533" d="M-12 1579l240 273h84l241 -273h-73q-8 0 -18 3t-22 14l-164 191q-5 6 -7 11l-8 -11l-163 -191q-11 -11 -21.5 -14t-19.5 -3h-69zM214 0v1449h105v-1449h-105z" />
<glyph unicode="&#xcf;" horiz-adv-x="533" d="M-14 1705q0 16 6.5 31.5t18 27.5t26 18.5t31.5 6.5q16 0 31.5 -6.5t27 -18.5t18.5 -27.5t7 -31.5t-7 -31t-18.5 -26t-27 -17.5t-31.5 -6.5t-31 6.5t-26.5 17.5t-18 26t-6.5 31zM214 0v1449h105v-1449h-105zM386 1705q0 34 24.5 59t58.5 25q16 0 31 -6.5t27 -18.5 t18.5 -27.5t6.5 -31.5t-6.5 -31t-18.5 -26t-26.5 -17.5t-31.5 -6.5t-32 6.5t-26 17.5t-18 26t-7 31z" />
<glyph unicode="&#xd0;" horiz-adv-x="1569" d="M7 701v66h221v682h529q155 0 281.5 -50t218 -144.5t141.5 -228.5t50 -301q0 -168 -50 -302t-141.5 -228.5t-218.5 -144.5t-281 -50h-529v701h-221zM334 85h423q131 0 238.5 43t183.5 125t118 200.5t42 271.5q0 152 -42 270.5t-118 200.5t-183.5 125t-238.5 43h-423v-597 h326v-66h-326v-616z" />
<glyph unicode="&#xd1;" horiz-adv-x="1546" d="M214 0v1449h52q14 0 22 -3.5t17 -15.5l939 -1255q-3 31 -3 58v1216h91v-1449h-49q-13 0 -22 4.5t-17 15.5l-941 1258q3 -31 3 -58v-1220h-92zM511 1589q0 35 10.5 67.5t31 57t50 39t66.5 14.5q40 0 73.5 -17.5t65 -38.5t60 -39t58.5 -18q25 0 44 9t32.5 24.5t20.5 35.5 t7 41h61q0 -36 -10.5 -68t-30.5 -56.5t-48.5 -39t-66.5 -14.5q-39 0 -73 18t-65 39t-60.5 39t-58.5 18q-24 0 -43 -9.5t-32.5 -24.5t-21 -35t-8.5 -42h-62z" />
<glyph unicode="&#xd2;" horiz-adv-x="1620" d="M120 725q0 167 50 303.5t141 233.5t218 150t281 53t281.5 -52.5t218.5 -150t141 -234t50 -303.5q0 -168 -50 -304.5t-141 -233.5t-218.5 -149.5t-281.5 -52.5t-281 52.5t-218 149.5t-141 233.5t-50 304.5zM226 725q0 -153 42.5 -273t120 -204t185 -128t236.5 -44 q130 0 237.5 44t184.5 128t119 204t42 273q0 152 -42 272.5t-119 204.5t-184.5 128.5t-237.5 44.5q-129 0 -236.5 -44.5t-185 -128.5t-120 -204.5t-42.5 -272.5zM503 1885h111q25 0 37.5 -3.5t25.5 -18.5l244 -284h-66q-13 0 -22 2t-18 10z" />
<glyph unicode="&#xd3;" horiz-adv-x="1620" d="M120 725q0 167 50 303.5t141 233.5t218 150t281 53t281.5 -52.5t218.5 -150t141 -234t50 -303.5q0 -168 -50 -304.5t-141 -233.5t-218.5 -149.5t-281.5 -52.5t-281 52.5t-218 149.5t-141 233.5t-50 304.5zM226 725q0 -153 42.5 -273t120 -204t185 -128t236.5 -44 q130 0 237.5 44t184.5 128t119 204t42 273q0 152 -42 272.5t-119 204.5t-184.5 128.5t-237.5 44.5q-129 0 -236.5 -44.5t-185 -128.5t-120 -204.5t-42.5 -272.5zM704 1579l264 284q12 14 25 18t38 4h116l-336 -294q-9 -8 -18.5 -10t-21.5 -2h-67z" />
<glyph unicode="&#xd4;" horiz-adv-x="1620" d="M120 725q0 167 50 303.5t141 233.5t218 150t281 53t281.5 -52.5t218.5 -150t141 -234t50 -303.5q0 -168 -50 -304.5t-141 -233.5t-218.5 -149.5t-281.5 -52.5t-281 52.5t-218 149.5t-141 233.5t-50 304.5zM226 725q0 -153 42.5 -273t120 -204t185 -128t236.5 -44 q130 0 237.5 44t184.5 128t119 204t42 273q0 152 -42 272.5t-119 204.5t-184.5 128.5t-237.5 44.5q-129 0 -236.5 -44.5t-185 -128.5t-120 -204.5t-42.5 -272.5zM530 1579l241 273h84l241 -273h-73q-8 0 -18.5 3t-21.5 14l-164 191q-5 6 -7 11l-8 -11l-163 -191 q-11 -11 -22 -14t-19 -3h-70z" />
<glyph unicode="&#xd5;" horiz-adv-x="1620" d="M120 725q0 167 50 303.5t141 233.5t218 150t281 53t281.5 -52.5t218.5 -150t141 -234t50 -303.5q0 -168 -50 -304.5t-141 -233.5t-218.5 -149.5t-281.5 -52.5t-281 52.5t-218 149.5t-141 233.5t-50 304.5zM226 725q0 -153 42.5 -273t120 -204t185 -128t236.5 -44 q130 0 237.5 44t184.5 128t119 204t42 273q0 152 -42 272.5t-119 204.5t-184.5 128.5t-237.5 44.5q-129 0 -236.5 -44.5t-185 -128.5t-120 -204.5t-42.5 -272.5zM527 1590q0 35 10.5 67.5t31 57t50 39t66.5 14.5q40 0 73.5 -17.5t65 -38.5t60 -39t58.5 -18q25 0 44 9 t32.5 24.5t20.5 35.5t7 41h61q0 -36 -10.5 -68t-30.5 -56.5t-48.5 -39t-66.5 -14.5q-39 0 -73 18t-65 39t-60.5 39t-58.5 18q-24 0 -43 -9.5t-32.5 -24.5t-21 -35t-8.5 -42h-62z" />
<glyph unicode="&#xd6;" horiz-adv-x="1620" d="M120 725q0 167 50 303.5t141 233.5t218 150t281 53t281.5 -52.5t218.5 -150t141 -234t50 -303.5q0 -168 -50 -304.5t-141 -233.5t-218.5 -149.5t-281.5 -52.5t-281 52.5t-218 149.5t-141 233.5t-50 304.5zM226 725q0 -153 42.5 -273t120 -204t185 -128t236.5 -44 q130 0 237.5 44t184.5 128t119 204t42 273q0 152 -42 272.5t-119 204.5t-184.5 128.5t-237.5 44.5q-129 0 -236.5 -44.5t-185 -128.5t-120 -204.5t-42.5 -272.5zM528 1705q0 16 6.5 31.5t18 27.5t26 18.5t31.5 6.5q16 0 31.5 -6.5t27 -18.5t18.5 -27.5t7 -31.5t-7 -31 t-18.5 -26t-27 -17.5t-31.5 -6.5t-31 6.5t-26.5 17.5t-18 26t-6.5 31zM928 1705q0 34 24.5 59t58.5 25q16 0 31 -6.5t27 -18.5t18.5 -27.5t6.5 -31.5t-6.5 -31t-18.5 -26t-26.5 -17.5t-31.5 -6.5t-32 6.5t-26 17.5t-18 26t-7 31z" />
<glyph unicode="&#xd7;" d="M159 303l382 383l-377 377l54 55l377 -378l375 376l54 -55l-375 -375l380 -381l-52 -53l-382 381l-383 -383z" />
<glyph unicode="&#xd8;" horiz-adv-x="1620" d="M120 725q0 167 50 303.5t141 233.5t218 150t281 53q124 0 230.5 -34t191.5 -99l114 153q11 16 20.5 23.5t29.5 7.5h56l-168 -228q103 -97 160 -240t57 -323q0 -168 -50 -304.5t-141 -233.5t-218.5 -149.5t-281.5 -52.5q-118 0 -220 31t-184 89l-135 -180q-13 -17 -30 -25 t-34 -8h-44l189 255q-111 98 -171.5 244.5t-60.5 333.5zM226 725q0 -164 48.5 -289.5t136.5 -209.5l765 1029q-74 58 -165.5 89t-200.5 31q-129 0 -236.5 -44.5t-185 -128.5t-120 -204.5t-42.5 -272.5zM462 182q72 -52 159 -79t189 -27q130 0 237.5 44t184.5 128t119 204 t42 273q0 156 -44 278t-124 206z" />
<glyph unicode="&#xd9;" horiz-adv-x="1488" d="M196 553v896h105v-895q0 -101 30 -188.5t87 -152.5t139.5 -101.5t186.5 -36.5t187 36t140 100.5t87.5 152.5t30.5 189v896h104v-896q0 -120 -37.5 -224t-109 -181t-173.5 -121t-229 -44t-228.5 44t-173 121t-109 181t-37.5 224zM436 1885h111q25 0 37.5 -3.5t25.5 -18.5 l244 -284h-66q-13 0 -22 2t-18 10z" />
<glyph unicode="&#xda;" horiz-adv-x="1488" d="M196 553v896h105v-895q0 -101 30 -188.5t87 -152.5t139.5 -101.5t186.5 -36.5t187 36t140 100.5t87.5 152.5t30.5 189v896h104v-896q0 -120 -37.5 -224t-109 -181t-173.5 -121t-229 -44t-228.5 44t-173 121t-109 181t-37.5 224zM637 1579l264 284q12 14 25 18t38 4h116 l-336 -294q-9 -8 -18.5 -10t-21.5 -2h-67z" />
<glyph unicode="&#xdb;" horiz-adv-x="1488" d="M196 553v896h105v-895q0 -101 30 -188.5t87 -152.5t139.5 -101.5t186.5 -36.5t187 36t140 100.5t87.5 152.5t30.5 189v896h104v-896q0 -120 -37.5 -224t-109 -181t-173.5 -121t-229 -44t-228.5 44t-173 121t-109 181t-37.5 224zM463 1579l240 273h84l241 -273h-73 q-8 0 -18 3t-22 14l-163 191q-5 6 -8 11l-8 -11l-163 -191q-11 -11 -21.5 -14t-19.5 -3h-69z" />
<glyph unicode="&#xdc;" horiz-adv-x="1488" d="M196 553v896h105v-895q0 -101 30 -188.5t87 -152.5t139.5 -101.5t186.5 -36.5t187 36t140 100.5t87.5 152.5t30.5 189v896h104v-896q0 -120 -37.5 -224t-109 -181t-173.5 -121t-229 -44t-228.5 44t-173 121t-109 181t-37.5 224zM461 1705q0 16 6.5 31.5t18 27.5t26 18.5 t31.5 6.5q16 0 31.5 -6.5t27 -18.5t18.5 -27.5t7 -31.5t-7 -31t-18.5 -26t-27 -17.5t-31.5 -6.5t-31 6.5t-26.5 17.5t-18 26t-6.5 31zM861 1705q0 34 24.5 59t58.5 25q16 0 31 -6.5t27 -18.5t18.5 -27.5t6.5 -31.5t-6.5 -31t-18.5 -26t-26.5 -17.5t-31.5 -6.5t-32 6.5 t-26 17.5t-18 26t-7 31z" />
<glyph unicode="&#xdd;" horiz-adv-x="1218" d="M20 1449h93q14 0 23 -6.5t16 -19.5l414 -665q13 -24 24.5 -46t19.5 -43q8 22 18.5 43.5t23.5 45.5l415 665q5 11 15 18.5t24 7.5h92l-536 -852v-597h-105v597zM503 1579l264 284q12 14 25 18t38 4h116l-336 -294q-9 -8 -18.5 -10t-21.5 -2h-67z" />
<glyph unicode="&#xde;" horiz-adv-x="1182" d="M214 0v1449h105v-291h270q255 0 384.5 -111.5t129.5 -318.5q0 -95 -36 -175t-102.5 -138t-161.5 -90t-214 -32h-270v-293h-105zM319 377h270q95 0 171 26.5t128.5 73.5t80.5 111t28 140q0 167 -102 256.5t-306 89.5h-270v-697z" />
<glyph unicode="&#xdf;" horiz-adv-x="1132" d="M168 0v1025q0 103 34 185.5t93.5 139.5t143.5 87t182 30q87 0 154.5 -26t113.5 -67.5t69.5 -92.5t23.5 -102q0 -63 -24 -110.5t-60.5 -84t-78.5 -65t-78 -56.5t-60.5 -58t-24.5 -68q0 -37 17 -62t45 -43.5t63.5 -33.5t73 -32t73 -38t63.5 -52t45 -73.5t17 -102.5 q0 -70 -26 -128t-72 -100t-110.5 -65t-143.5 -23q-103 0 -177 34t-136 90l23 34q5 8 11.5 12.5t17.5 4.5q13 0 32.5 -16.5t50 -35.5t76 -35.5t110.5 -16.5q60 0 107 18t79.5 49.5t49.5 73.5t17 90q0 69 -30.5 110t-75.5 67.5t-97 46t-97.5 45t-75.5 63.5t-30 103 q0 50 24.5 87t62 68.5t80.5 61.5t80.5 64.5t62 77t24.5 100.5q0 32 -14.5 69t-46.5 69.5t-84.5 54t-127.5 21.5q-73 0 -136.5 -22t-111.5 -67t-76 -115.5t-28 -166.5v-1023h-97z" />
<glyph unicode="&#xe0;" horiz-adv-x="991" d="M87 247q0 65 37 120t115.5 96t202 65.5t296.5 28.5v107q0 141 -61 217t-180 76q-74 0 -126 -20.5t-87.5 -45t-58 -45t-38.5 -20.5q-11 0 -18 5.5t-12 13.5l-18 29q80 80 168 121t200 41q82 0 143.5 -26t102 -74.5t61 -117.5t20.5 -154v-664h-39q-31 0 -38 29l-15 131 q-42 -41 -83.5 -74t-86.5 -55.5t-97 -34.5t-115 -12q-52 0 -101 15t-87.5 47t-61.5 81.5t-23 119.5zM181 251q0 -51 17 -88.5t45 -61.5t65 -35.5t77 -11.5q59 0 109 13t92.5 36t79 54.5t72.5 68.5v267q-145 -5 -250 -23t-173 -49t-101 -73.5t-33 -96.5zM277 1465h94 q25 0 37 -7t23 -25l161 -244h-56q-12 0 -21 3.5t-17 13.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="991" d="M87 247q0 65 37 120t115.5 96t202 65.5t296.5 28.5v107q0 141 -61 217t-180 76q-74 0 -126 -20.5t-87.5 -45t-58 -45t-38.5 -20.5q-11 0 -18 5.5t-12 13.5l-18 29q80 80 168 121t200 41q82 0 143.5 -26t102 -74.5t61 -117.5t20.5 -154v-664h-39q-31 0 -38 29l-15 131 q-42 -41 -83.5 -74t-86.5 -55.5t-97 -34.5t-115 -12q-52 0 -101 15t-87.5 47t-61.5 81.5t-23 119.5zM181 251q0 -51 17 -88.5t45 -61.5t65 -35.5t77 -11.5q59 0 109 13t92.5 36t79 54.5t72.5 68.5v267q-145 -5 -250 -23t-173 -49t-101 -73.5t-33 -96.5zM400 1189l160 244 q11 18 23.5 25t37.5 7h97l-222 -259q-8 -10 -17 -13.5t-21 -3.5h-58z" />
<glyph unicode="&#xe2;" horiz-adv-x="991" d="M87 247q0 65 37 120t115.5 96t202 65.5t296.5 28.5v107q0 141 -61 217t-180 76q-74 0 -126 -20.5t-87.5 -45t-58 -45t-38.5 -20.5q-11 0 -18 5.5t-12 13.5l-18 29q80 80 168 121t200 41q82 0 143.5 -26t102 -74.5t61 -117.5t20.5 -154v-664h-39q-31 0 -38 29l-15 131 q-42 -41 -83.5 -74t-86.5 -55.5t-97 -34.5t-115 -12q-52 0 -101 15t-87.5 47t-61.5 81.5t-23 119.5zM181 251q0 -51 17 -88.5t45 -61.5t65 -35.5t77 -11.5q59 0 109 13t92.5 36t79 54.5t72.5 68.5v267q-145 -5 -250 -23t-173 -49t-101 -73.5t-33 -96.5zM221 1197l222 252h87 l224 -252h-67q-15 0 -30 14l-157 170q-3 3 -5.5 6.5t-6.5 7.5q-3 -4 -5.5 -7.5t-5.5 -6.5l-158 -170q-6 -6 -13.5 -10t-15.5 -4h-69z" />
<glyph unicode="&#xe3;" horiz-adv-x="991" d="M87 247q0 65 37 120t115.5 96t202 65.5t296.5 28.5v107q0 141 -61 217t-180 76q-74 0 -126 -20.5t-87.5 -45t-58 -45t-38.5 -20.5q-11 0 -18 5.5t-12 13.5l-18 29q80 80 168 121t200 41q82 0 143.5 -26t102 -74.5t61 -117.5t20.5 -154v-664h-39q-31 0 -38 29l-15 131 q-42 -41 -83.5 -74t-86.5 -55.5t-97 -34.5t-115 -12q-52 0 -101 15t-87.5 47t-61.5 81.5t-23 119.5zM181 251q0 -51 17 -88.5t45 -61.5t65 -35.5t77 -11.5q59 0 109 13t92.5 36t79 54.5t72.5 68.5v267q-145 -5 -250 -23t-173 -49t-101 -73.5t-33 -96.5zM228 1243 q0 37 10 69.5t29.5 56t47 36.5t62.5 13t64.5 -17t56.5 -36.5t53 -36.5t53 -17q45 0 67 28t24 76h61q0 -37 -10 -69t-28.5 -55.5t-46.5 -37t-64 -13.5q-34 0 -63.5 17t-57 37t-53.5 37t-53 17q-43 0 -66 -29.5t-24 -75.5h-62z" />
<glyph unicode="&#xe4;" horiz-adv-x="991" d="M87 247q0 65 37 120t115.5 96t202 65.5t296.5 28.5v107q0 141 -61 217t-180 76q-74 0 -126 -20.5t-87.5 -45t-58 -45t-38.5 -20.5q-11 0 -18 5.5t-12 13.5l-18 29q80 80 168 121t200 41q82 0 143.5 -26t102 -74.5t61 -117.5t20.5 -154v-664h-39q-31 0 -38 29l-15 131 q-42 -41 -83.5 -74t-86.5 -55.5t-97 -34.5t-115 -12q-52 0 -101 15t-87.5 47t-61.5 81.5t-23 119.5zM181 251q0 -51 17 -88.5t45 -61.5t65 -35.5t77 -11.5q59 0 109 13t92.5 36t79 54.5t72.5 68.5v267q-145 -5 -250 -23t-173 -49t-101 -73.5t-33 -96.5zM222 1315 q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 31.5 -7t27.5 -18.5t19 -27t7 -32.5q0 -16 -7 -31t-19 -26.5t-27 -18t-32 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5zM584 1315q0 35 24.5 60t59.5 25q16 0 31.5 -7t26.5 -18.5t18.5 -27t7.5 -32.5q0 -16 -7.5 -31t-18.5 -26.5 t-26.5 -18t-31.5 -6.5q-17 0 -32.5 6.5t-27 18t-18 26t-6.5 31.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="991" d="M87 247q0 65 37 120t115.5 96t202 65.5t296.5 28.5v107q0 141 -61 217t-180 76q-74 0 -126 -20.5t-87.5 -45t-58 -45t-38.5 -20.5q-11 0 -18 5.5t-12 13.5l-18 29q80 80 168 121t200 41q82 0 143.5 -26t102 -74.5t61 -117.5t20.5 -154v-664h-39q-31 0 -38 29l-15 131 q-42 -41 -83.5 -74t-86.5 -55.5t-97 -34.5t-115 -12q-52 0 -101 15t-87.5 47t-61.5 81.5t-23 119.5zM181 251q0 -51 17 -88.5t45 -61.5t65 -35.5t77 -11.5q59 0 109 13t92.5 36t79 54.5t72.5 68.5v267q-145 -5 -250 -23t-173 -49t-101 -73.5t-33 -96.5zM311 1333q0 37 14 68 t38 53t56 34.5t68 12.5q37 0 69 -12.5t56 -34.5t38 -53t14 -68t-14 -67t-38 -52.5t-56.5 -35t-68.5 -12.5t-68 12.5t-56 35t-38 52.5t-14 67zM371 1333q0 -51 32 -83.5t85 -32.5q51 0 83.5 32.5t32.5 83.5t-32.5 84t-83.5 33q-52 0 -84.5 -33t-32.5 -84z" />
<glyph unicode="&#xe6;" horiz-adv-x="1637" d="M90 264q0 66 37 123.5t116 102t201.5 72t295.5 31.5v71q0 141 -61 218t-180 77q-74 0 -125.5 -20.5t-87.5 -45.5t-58.5 -46t-38.5 -21q-11 0 -18 5.5t-12 13.5l-18 29q79 80 164.5 121t195.5 41q129 0 202.5 -66t97.5 -180q24 56 59 101.5t81 77t103 49t123 17.5 q77 0 144.5 -30.5t118 -89.5t79 -146t28.5 -201q0 -23 -5.5 -31t-20.5 -8h-680q0 -115 25 -201.5t70 -145t108.5 -88t141.5 -29.5q73 0 124 16.5t85.5 36.5t54 37t29.5 17q14 0 23 -10l25 -31q-24 -31 -62 -57.5t-84 -45.5t-97.5 -30t-105.5 -11q-133 0 -230 70.5 t-145 210.5q-20 -79 -64 -133t-99.5 -88t-117 -48.5t-118.5 -14.5q-65 0 -120 15.5t-96 49.5t-64.5 87t-23.5 128zM184 268q0 -57 17 -97.5t48 -66t73 -38t92 -12.5q68 0 127 22.5t103.5 66t70 107t25.5 144.5v134q-145 -5 -250 -26t-173 -54.5t-100.5 -79t-32.5 -100.5z M834 592h617q0 87 -21.5 156t-59.5 116.5t-90 73t-115 25.5q-74 0 -132.5 -27t-100.5 -75t-67 -116.5t-31 -152.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="965" d="M106 510q0 117 31 213t90.5 165.5t145 108t195.5 38.5q99 0 176 -31.5t135 -85.5l-26 -35q-5 -5 -9.5 -9t-13.5 -4q-10 0 -27.5 14t-47 30.5t-74 30t-108.5 13.5q-88 0 -156 -31t-115 -89.5t-71 -141.5t-24 -186q0 -108 25 -190.5t71 -139.5t111.5 -87t145.5 -30 q74 0 123.5 17.5t82 38t51 38t30.5 17.5t20 -10l26 -33q-44 -57 -125 -96t-183 -47l-27 -84q93 -15 140.5 -46.5t47.5 -91.5q0 -31 -13.5 -55.5t-38.5 -41.5t-60 -26t-76 -9q-42 0 -82 12t-66 31l10 29q6 11 16 11q5 0 14 -5.5t22.5 -11.5t34 -11.5t50.5 -5.5q53 0 82 22.5 t29 60.5q0 26 -13.5 43t-37.5 27.5t-57.5 17.5t-73.5 12l39 121q-93 4 -169.5 41t-132 104t-86.5 162t-31 216z" />
<glyph unicode="&#xe8;" horiz-adv-x="1068" d="M106 530q0 110 31.5 202.5t90.5 159.5t144.5 105t193.5 38q86 0 160 -29.5t128 -86.5t85.5 -140t31.5 -190q0 -23 -6.5 -31t-20.5 -8h-741v-20q0 -115 26.5 -201.5t75.5 -145.5t119 -88.5t156 -29.5q77 0 133 16.5t94.5 37.5t61 38t32.5 17q13 0 21 -10l26 -33 q-25 -31 -65 -57.5t-90 -45.5t-106.5 -30t-114.5 -11q-104 0 -190 36t-147.5 106t-95 170.5t-33.5 230.5zM208 612h675q0 82 -23 147t-65 110t-100 69.5t-128 24.5q-79 0 -141 -25t-108 -71t-73.5 -110.5t-36.5 -144.5zM356 1465h94q25 0 37 -7t23 -25l161 -244h-56 q-12 0 -21 3.5t-17 13.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="1068" d="M106 530q0 110 31.5 202.5t90.5 159.5t144.5 105t193.5 38q86 0 160 -29.5t128 -86.5t85.5 -140t31.5 -190q0 -23 -6.5 -31t-20.5 -8h-741v-20q0 -115 26.5 -201.5t75.5 -145.5t119 -88.5t156 -29.5q77 0 133 16.5t94.5 37.5t61 38t32.5 17q13 0 21 -10l26 -33 q-25 -31 -65 -57.5t-90 -45.5t-106.5 -30t-114.5 -11q-104 0 -190 36t-147.5 106t-95 170.5t-33.5 230.5zM208 612h675q0 82 -23 147t-65 110t-100 69.5t-128 24.5q-79 0 -141 -25t-108 -71t-73.5 -110.5t-36.5 -144.5zM479 1189l160 244q11 18 23.5 25t37.5 7h97l-222 -259 q-8 -10 -17 -13.5t-21 -3.5h-58z" />
<glyph unicode="&#xea;" horiz-adv-x="1068" d="M106 530q0 110 31.5 202.5t90.5 159.5t144.5 105t193.5 38q86 0 160 -29.5t128 -86.5t85.5 -140t31.5 -190q0 -23 -6.5 -31t-20.5 -8h-741v-20q0 -115 26.5 -201.5t75.5 -145.5t119 -88.5t156 -29.5q77 0 133 16.5t94.5 37.5t61 38t32.5 17q13 0 21 -10l26 -33 q-25 -31 -65 -57.5t-90 -45.5t-106.5 -30t-114.5 -11q-104 0 -190 36t-147.5 106t-95 170.5t-33.5 230.5zM208 612h675q0 82 -23 147t-65 110t-100 69.5t-128 24.5q-79 0 -141 -25t-108 -71t-73.5 -110.5t-36.5 -144.5zM300 1197l222 252h87l224 -252h-67q-15 0 -30 14 l-157 170q-3 3 -6 6.5t-6 7.5q-3 -4 -5.5 -7.5t-5.5 -6.5l-158 -170q-6 -6 -13.5 -10t-15.5 -4h-69z" />
<glyph unicode="&#xeb;" horiz-adv-x="1068" d="M106 530q0 110 31.5 202.5t90.5 159.5t144.5 105t193.5 38q86 0 160 -29.5t128 -86.5t85.5 -140t31.5 -190q0 -23 -6.5 -31t-20.5 -8h-741v-20q0 -115 26.5 -201.5t75.5 -145.5t119 -88.5t156 -29.5q77 0 133 16.5t94.5 37.5t61 38t32.5 17q13 0 21 -10l26 -33 q-25 -31 -65 -57.5t-90 -45.5t-106.5 -30t-114.5 -11q-104 0 -190 36t-147.5 106t-95 170.5t-33.5 230.5zM208 612h675q0 82 -23 147t-65 110t-100 69.5t-128 24.5q-79 0 -141 -25t-108 -71t-73.5 -110.5t-36.5 -144.5zM301 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7 q16 0 31.5 -7t27.5 -18.5t19 -27t7 -32.5q0 -16 -7 -31t-19 -26.5t-27 -18t-32 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5zM663 1315q0 35 24.5 60t59.5 25q16 0 31.5 -7t26.5 -18.5t18.5 -27t7.5 -32.5q0 -16 -7.5 -31t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-17 0 -32.5 6.5 t-27 18t-18 26t-6.5 31.5z" />
<glyph unicode="&#xec;" horiz-adv-x="455" d="M20 1465h94q25 0 37 -7t23 -25l161 -244h-56q-12 0 -21 3.5t-17 13.5zM179 0v1019h99v-1019h-99z" />
<glyph unicode="&#xed;" horiz-adv-x="456" d="M143 1189l160 244q11 18 23.5 25t37.5 7h97l-222 -259q-8 -10 -17 -13.5t-21 -3.5h-58zM179 0v1019h99v-1019h-99z" />
<glyph unicode="&#xee;" horiz-adv-x="455" d="M-36 1197l222 252h87l224 -252h-67q-15 0 -30 14l-157 170q-3 3 -6 6.5t-6 7.5q-3 -4 -5.5 -7.5t-5.5 -6.5l-158 -170q-6 -6 -13.5 -10t-15.5 -4h-69zM179 0v1019h99v-1019h-99z" />
<glyph unicode="&#xef;" horiz-adv-x="455" d="M4 1315q0 17 7 32.5t18.5 27t26.5 18.5t31 7t32 -7t27.5 -18.5t18.5 -27t7 -32.5q0 -16 -7 -31t-18.5 -26.5t-27 -18t-32.5 -6.5q-16 0 -31 6.5t-26.5 18t-18.5 26t-7 31.5zM179 0v1019h99v-1019h-99zM288 1315q0 17 6.5 32.5t18.5 27t27 18.5t32 7q16 0 32 -7 t27.5 -18.5t18.5 -27t7 -32.5q0 -16 -7 -31t-18.5 -26.5t-27.5 -18t-32 -6.5q-17 0 -32 6.5t-27 18t-18.5 26t-6.5 31.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1147" d="M108 466q0 98 29.5 185t86.5 152t139.5 102.5t188.5 37.5q54 0 109 -14t105.5 -43t94.5 -74t77 -106q-8 92 -30 169t-57 140t-81 113t-102 89l-169 -150l-18 29q-8 10 -7 20.5t12 19.5l130 112q-61 34 -129.5 57.5t-144.5 39.5q-12 3 -22 14t-3 32l12 34q93 -14 182 -44 t170 -81l156 139l18 -30q14 -23 -8 -40l-117 -102q65 -47 118.5 -110t93 -144t61 -180.5t21.5 -219.5q0 -141 -29 -256t-87.5 -197.5t-146.5 -127t-206 -44.5q-93 0 -174.5 33t-142.5 95t-95.5 150.5t-34.5 199.5zM205 466q0 -95 28 -170t76.5 -126t112.5 -78t135 -27 q90 0 158.5 35t116.5 101.5t73.5 162t28.5 214.5q-16 52 -46 103.5t-75.5 92.5t-106 66.5t-138.5 25.5q-90 0 -158 -30.5t-113.5 -85t-68.5 -127.5t-23 -157z" />
<glyph unicode="&#xf1;" horiz-adv-x="1126" d="M168 0v1019h54q26 0 30 -26l9 -152q71 88 163.5 141t206.5 53q84 0 148 -26.5t106 -76.5t63.5 -121t21.5 -160v-651h-99v651q0 143 -65.5 224.5t-199.5 81.5q-99 0 -185.5 -50.5t-154.5 -140.5v-766h-98zM306 1243q0 37 10 69.5t29.5 56t47 36.5t62.5 13t64.5 -17 t56.5 -36.5t53 -36.5t53 -17q45 0 67 28t24 76h61q0 -37 -10 -69t-28.5 -55.5t-46.5 -37t-64 -13.5q-34 0 -63.5 17t-57 37t-53.5 37t-53 17q-43 0 -66 -29.5t-24 -75.5h-62z" />
<glyph unicode="&#xf2;" horiz-adv-x="1142" d="M106 510q0 120 31.5 216.5t91.5 165.5t146 106t196 37t196.5 -37t146 -106t90.5 -165.5t31 -216.5t-31 -216t-90.5 -165t-146 -105.5t-196.5 -36.5t-196 36.5t-146 105.5t-91.5 165t-31.5 216zM207 510q0 -102 23 -184.5t68.5 -141t113.5 -90t159 -31.5t159.5 31.5 t114 90t68 141t22.5 184.5q0 101 -22.5 184t-68 142t-113.5 90.5t-160 31.5q-91 0 -159 -31.5t-113.5 -90.5t-68.5 -142t-23 -184zM363 1465h94q25 0 37 -7t23 -25l161 -244h-56q-12 0 -21 3.5t-17 13.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1142" d="M106 510q0 120 31.5 216.5t91.5 165.5t146 106t196 37t196.5 -37t146 -106t90.5 -165.5t31 -216.5t-31 -216t-90.5 -165t-146 -105.5t-196.5 -36.5t-196 36.5t-146 105.5t-91.5 165t-31.5 216zM207 510q0 -102 23 -184.5t68.5 -141t113.5 -90t159 -31.5t159.5 31.5 t114 90t68 141t22.5 184.5q0 101 -22.5 184t-68 142t-113.5 90.5t-160 31.5q-91 0 -159 -31.5t-113.5 -90.5t-68.5 -142t-23 -184zM486 1189l160 244q11 18 23.5 25t37.5 7h97l-222 -259q-8 -10 -17 -13.5t-21 -3.5h-58z" />
<glyph unicode="&#xf4;" horiz-adv-x="1142" d="M106 510q0 120 31.5 216.5t91.5 165.5t146 106t196 37t196.5 -37t146 -106t90.5 -165.5t31 -216.5t-31 -216t-90.5 -165t-146 -105.5t-196.5 -36.5t-196 36.5t-146 105.5t-91.5 165t-31.5 216zM207 510q0 -102 23 -184.5t68.5 -141t113.5 -90t159 -31.5t159.5 31.5 t114 90t68 141t22.5 184.5q0 101 -22.5 184t-68 142t-113.5 90.5t-160 31.5q-91 0 -159 -31.5t-113.5 -90.5t-68.5 -142t-23 -184zM307 1197l222 252h87l224 -252h-67q-15 0 -30 14l-157 170q-3 3 -5.5 6.5t-6.5 7.5q-3 -4 -5.5 -7.5t-5.5 -6.5l-157 -170q-6 -6 -14 -10 t-16 -4h-69z" />
<glyph unicode="&#xf5;" horiz-adv-x="1142" d="M106 510q0 120 31.5 216.5t91.5 165.5t146 106t196 37t196.5 -37t146 -106t90.5 -165.5t31 -216.5t-31 -216t-90.5 -165t-146 -105.5t-196.5 -36.5t-196 36.5t-146 105.5t-91.5 165t-31.5 216zM207 510q0 -102 23 -184.5t68.5 -141t113.5 -90t159 -31.5t159.5 31.5 t114 90t68 141t22.5 184.5q0 101 -22.5 184t-68 142t-113.5 90.5t-160 31.5q-91 0 -159 -31.5t-113.5 -90.5t-68.5 -142t-23 -184zM314 1243q0 37 10 69.5t29.5 56t47 36.5t62.5 13t64.5 -17t56.5 -36.5t53 -36.5t53 -17q45 0 67 28t24 76h61q0 -37 -10 -69t-28.5 -55.5 t-46.5 -37t-64 -13.5q-34 0 -63.5 17t-57 37t-53.5 37t-53 17q-43 0 -66 -29.5t-24 -75.5h-62z" />
<glyph unicode="&#xf6;" horiz-adv-x="1142" d="M106 510q0 120 31.5 216.5t91.5 165.5t146 106t196 37t196.5 -37t146 -106t90.5 -165.5t31 -216.5t-31 -216t-90.5 -165t-146 -105.5t-196.5 -36.5t-196 36.5t-146 105.5t-91.5 165t-31.5 216zM207 510q0 -102 23 -184.5t68.5 -141t113.5 -90t159 -31.5t159.5 31.5 t114 90t68 141t22.5 184.5q0 101 -22.5 184t-68 142t-113.5 90.5t-160 31.5q-91 0 -159 -31.5t-113.5 -90.5t-68.5 -142t-23 -184zM308 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 31.5 -7t27.5 -18.5t19 -27t7 -32.5q0 -16 -7 -31t-19 -26.5t-27 -18t-32 -6.5t-32 6.5 t-26.5 18t-18.5 26t-7 31.5zM670 1315q0 35 24.5 60t59.5 25q16 0 31.5 -7t26.5 -18.5t18.5 -27t7.5 -32.5q0 -16 -7.5 -31t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-17 0 -32.5 6.5t-27 18t-18 26t-6.5 31.5z" />
<glyph unicode="&#xf7;" d="M113 649v75h961v-75h-961zM498 321q0 40 27 68t67 28q19 0 37 -8t31 -20.5t20.5 -30.5t7.5 -37q0 -40 -28 -67.5t-68 -27.5t-67 27.5t-27 67.5zM498 1050q0 40 27 68t67 28q19 0 37 -8t31 -20.5t20.5 -30.5t7.5 -37q0 -40 -28 -67.5t-68 -27.5t-67 27.5t-27 67.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1140" d="M106 510q0 120 31.5 216.5t91.5 165.5t146.5 106t195.5 37q90 0 163.5 -24.5t130.5 -71.5l88 119q11 16 21 22.5t30 6.5h51l-144 -193q61 -69 92.5 -166t31.5 -218q0 -120 -31 -216t-90.5 -165t-146 -105.5t-196.5 -36.5q-83 0 -151.5 21t-123.5 60l-83 -112 q-12 -17 -29.5 -25t-35.5 -8h-38l138 186q-69 70 -105.5 171t-36.5 230zM202 510q0 -106 24 -190t73 -143l517 695q-46 43 -107 66t-138 23q-91 0 -160 -32.5t-115.5 -91.5t-70 -142.5t-23.5 -184.5zM344 133q89 -72 227 -72q91 0 160.5 32t115.5 90.5t69.5 141.5t23.5 185 q0 97 -20.5 176t-61.5 137z" />
<glyph unicode="&#xf9;" horiz-adv-x="1125" d="M157 368v651h98v-651q0 -143 65.5 -225t199.5 -82q99 0 186 51t154 141v766h98v-1019h-54q-26 0 -30 27l-9 151q-71 -88 -163.5 -141t-205.5 -53q-85 0 -148.5 26.5t-105.5 76.5t-63.5 121t-21.5 160zM354 1465h94q25 0 37 -7t23 -25l161 -244h-56q-12 0 -21 3.5 t-17 13.5z" />
<glyph unicode="&#xfa;" horiz-adv-x="1125" d="M157 368v651h98v-651q0 -143 65.5 -225t199.5 -82q99 0 186 51t154 141v766h98v-1019h-54q-26 0 -30 27l-9 151q-71 -88 -163.5 -141t-205.5 -53q-85 0 -148.5 26.5t-105.5 76.5t-63.5 121t-21.5 160zM477 1189l160 244q11 18 23.5 25t37.5 7h97l-222 -259 q-8 -10 -17 -13.5t-21 -3.5h-58z" />
<glyph unicode="&#xfb;" horiz-adv-x="1125" d="M157 368v651h98v-651q0 -143 65.5 -225t199.5 -82q99 0 186 51t154 141v766h98v-1019h-54q-26 0 -30 27l-9 151q-71 -88 -163.5 -141t-205.5 -53q-85 0 -148.5 26.5t-105.5 76.5t-63.5 121t-21.5 160zM298 1197l222 252h87l223 -252h-66q-15 0 -30 14l-157 170 q-3 3 -6 6.5t-6 7.5q-3 -4 -5.5 -7.5t-5.5 -6.5l-158 -170q-6 -6 -13.5 -10t-15.5 -4h-69z" />
<glyph unicode="&#xfc;" horiz-adv-x="1125" d="M157 368v651h98v-651q0 -143 65.5 -225t199.5 -82q99 0 186 51t154 141v766h98v-1019h-54q-26 0 -30 27l-9 151q-71 -88 -163.5 -141t-205.5 -53q-85 0 -148.5 26.5t-105.5 76.5t-63.5 121t-21.5 160zM299 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 31.5 -7 t27.5 -18.5t19 -27t7 -32.5q0 -16 -7 -31t-19 -26.5t-27 -18t-32 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5zM661 1315q0 35 24.5 60t59.5 25q16 0 31.5 -7t26.5 -18.5t18.5 -27t7.5 -32.5q0 -16 -7.5 -31t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-17 0 -32.5 6.5t-27 18t-18 26 t-6.5 31.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1027" d="M40 1019h81q15 0 24.5 -7.5t12.5 -17.5l343 -792q6 -15 10.5 -31t8.5 -33q5 16 11 32l12 32l339 792q5 11 14 18t20 7h75l-585 -1347q-6 -14 -16 -23t-29 -9h-69l179 398zM455 1189l160 244q11 18 23.5 25t37.5 7h97l-222 -259q-8 -10 -17 -13.5t-21 -3.5h-58z" />
<glyph unicode="&#xfe;" horiz-adv-x="1129" d="M168 -360v1850h98v-648q68 90 157 141.5t201 51.5q187 0 292 -129.5t105 -389.5q0 -112 -29 -208.5t-86 -167.5t-139.5 -112t-189.5 -41q-104 0 -179.5 40t-131.5 117v-504h-98zM266 222q30 -44 62 -74t68 -49t76.5 -27.5t85.5 -8.5q90 0 157.5 33t113 93t69 143.5 t23.5 183.5q0 227 -84 334t-241 107q-101 0 -183 -52t-147 -145v-538z" />
<glyph unicode="&#xff;" horiz-adv-x="1027" d="M40 1019h81q15 0 24.5 -7.5t12.5 -17.5l343 -792q6 -15 10.5 -31t8.5 -33q5 16 11 32l12 32l339 792q5 11 14 18t20 7h75l-585 -1347q-6 -14 -16 -23t-29 -9h-69l179 398zM277 1315q0 17 7 32.5t18.5 27t26.5 18.5t32 7q16 0 31.5 -7t27.5 -18.5t19 -27t7 -32.5 q0 -16 -7 -31t-19 -26.5t-27 -18t-32 -6.5t-32 6.5t-26.5 18t-18.5 26t-7 31.5zM639 1315q0 35 24.5 60t59.5 25q16 0 31.5 -7t26.5 -18.5t18.5 -27t7.5 -32.5q0 -16 -7.5 -31t-18.5 -26.5t-26.5 -18t-31.5 -6.5q-17 0 -32.5 6.5t-27 18t-18 26t-6.5 31.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2254" d="M122 725q0 167 45.5 303.5t127.5 233.5t196 150t252 53q95 0 179 -24t153.5 -70t124 -112.5t92.5 -150.5v341h857v-87h-763v-586h635v-85h-635v-604h764l-1 -87h-857v342q-38 -84 -92.5 -150t-124 -111.5t-153 -69.5t-179.5 -24q-138 0 -252 52t-196 149t-127.5 233 t-45.5 304zM229 725q0 -153 38.5 -273.5t107.5 -204t166.5 -128t214.5 -44.5q118 0 214.5 44.5t165.5 128t107 204t38 273.5q0 152 -38 272.5t-107 205t-166 129.5t-214 45t-214.5 -45t-166.5 -129.5t-107.5 -205t-38.5 -272.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1781" d="M108 510q0 120 29.5 216.5t85.5 165.5t136.5 106t182.5 37q147 0 246 -78t143 -218q20 68 55 122t83.5 93t110 60t135.5 21q77 0 143.5 -30.5t115.5 -89.5t77 -146t28 -201q0 -23 -5 -31t-19 -8h-676q0 -115 24.5 -201.5t69.5 -145t108.5 -88t141.5 -29.5 q69 0 119.5 16.5t86 37.5t56.5 38t32 17q12 0 20 -10l27 -33q-24 -31 -61.5 -57.5t-83.5 -45.5t-98 -30t-105 -11q-138 0 -237.5 76.5t-145.5 226.5q-42 -142 -142.5 -222.5t-251.5 -80.5q-100 0 -180 36.5t-136 105.5t-85.5 165t-29.5 216zM208 510q0 -102 21 -184.5 t63 -141t105.5 -90t148.5 -31.5q84 0 147 31.5t105.5 90t63.5 141t21 184.5q0 101 -21 184t-63.5 142t-105.5 90.5t-147 31.5q-85 0 -148.5 -31.5t-105.5 -90.5t-63 -142t-21 -184zM981 592h612q0 87 -20.5 156t-57 116.5t-88.5 73t-114 25.5q-74 0 -133 -27t-101 -75 t-67 -116.5t-31 -152.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1218" d="M20 1449h93q14 0 23 -6.5t16 -19.5l414 -665q13 -24 24.5 -46t19.5 -43q8 22 18.5 43.5t23.5 45.5l415 665q5 11 15 18.5t24 7.5h92l-536 -852v-597h-105v597zM327 1705q0 16 6.5 31.5t18 27.5t26 18.5t31.5 6.5q16 0 31.5 -6.5t27 -18.5t18.5 -27.5t7 -31.5t-7 -31 t-18.5 -26t-27 -17.5t-31.5 -6.5t-31 6.5t-26.5 17.5t-18 26t-6.5 31zM727 1705q0 34 24.5 59t58.5 25q16 0 31 -6.5t27 -18.5t18.5 -27.5t6.5 -31.5t-6.5 -31t-18.5 -26t-26.5 -17.5t-31.5 -6.5t-32 6.5t-26 17.5t-18 26t-7 31z" />
<glyph unicode="&#x2c6;" horiz-adv-x="819" d="M143 1197l223 252h87l223 -252h-67q-15 0 -29 14l-158 170q-3 3 -5.5 6.5t-5.5 7.5q-3 -4 -6 -7.5t-6 -6.5l-157 -170q-6 -6 -14 -10t-16 -4h-69z" />
<glyph unicode="&#x2dc;" horiz-adv-x="819" d="M150 1243q0 37 10 69.5t29.5 56t47 36.5t62.5 13t64.5 -17t56.5 -36.5t53 -36.5t53 -17q45 0 67 28t24 76h61q0 -37 -10 -69t-28.5 -55.5t-46.5 -37t-64 -13.5q-34 0 -63.5 17t-57 37t-53.5 37t-53 17q-43 0 -66 -29.5t-24 -75.5h-62z" />
<glyph unicode="&#x2000;" horiz-adv-x="944" />
<glyph unicode="&#x2001;" horiz-adv-x="1888" />
<glyph unicode="&#x2002;" horiz-adv-x="944" />
<glyph unicode="&#x2003;" horiz-adv-x="1888" />
<glyph unicode="&#x2004;" horiz-adv-x="629" />
<glyph unicode="&#x2005;" horiz-adv-x="472" />
<glyph unicode="&#x2006;" horiz-adv-x="314" />
<glyph unicode="&#x2007;" horiz-adv-x="314" />
<glyph unicode="&#x2008;" horiz-adv-x="236" />
<glyph unicode="&#x2009;" horiz-adv-x="377" />
<glyph unicode="&#x200a;" horiz-adv-x="104" />
<glyph unicode="&#x2010;" horiz-adv-x="757" d="M141 566v83h476v-83h-476z" />
<glyph unicode="&#x2011;" horiz-adv-x="757" d="M141 566v83h476v-83h-476z" />
<glyph unicode="&#x2012;" horiz-adv-x="757" d="M141 566v83h476v-83h-476z" />
<glyph unicode="&#x2013;" d="M190 556v73h806v-73h-806z" />
<glyph unicode="&#x2014;" horiz-adv-x="1612" d="M142 556v73h1328v-73h-1328z" />
<glyph unicode="&#x2018;" horiz-adv-x="420" d="M137.5 1275q8.5 71 39 136t81.5 114l29 -19q8 -6 7 -14t-6 -13q-33 -43 -50.5 -96.5t-21 -108.5t8 -109.5t37.5 -99.5q16 -29 -9 -40l-63 -26q-36 62 -48.5 133.5t-4 142.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="419" d="M133.5 1054.5q0.5 8.5 5.5 13.5q33 43 51 96t21 108.5t-9 109.5t-37 100q-16 29 9 39l63 26q35 -62 48 -133.5t4.5 -142t-39.5 -135.5t-81 -114l-28 18q-8 6 -7.5 14.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="435" d="M130.5 -277.5q0.5 8.5 5.5 13.5q33 43 51 96t21 108.5t-9 109.5t-37 100q-16 29 9 39l63 26q35 -62 48 -133.5t4.5 -142t-39.5 -135.5t-81 -114l-28 18q-8 6 -7.5 14.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="697" d="M137.5 1275q8.5 71 39 136t81.5 114l29 -19q8 -6 7 -14t-6 -13q-33 -43 -50.5 -96.5t-21 -108.5t8 -109.5t37.5 -99.5q16 -29 -9 -40l-63 -26q-36 62 -48.5 133.5t-4 142.5zM412.5 1275q8.5 71 39 136t81.5 114l29 -19q8 -6 7 -14t-6 -13q-33 -43 -50.5 -96.5t-21 -108.5 t8 -109.5t37.5 -99.5q16 -29 -9 -40l-63 -26q-36 62 -48.5 133.5t-4 142.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="695" d="M133.5 1054.5q0.5 8.5 5.5 13.5q33 43 51 96t21 108.5t-9 109.5t-37 100q-16 29 9 39l63 26q35 -62 48 -133.5t4.5 -142t-39.5 -135.5t-81 -114l-28 18q-8 6 -7.5 14.5zM410.5 1054.5q0.5 8.5 5.5 13.5q33 43 51 96t21 108.5t-9 109.5t-37 100q-16 29 9 39l63 26 q35 -62 48 -133.5t4.5 -142t-39.5 -135.5t-81 -114l-28 18q-8 6 -7.5 14.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="710" d="M130.5 -277.5q0.5 8.5 5.5 13.5q33 43 51 96t21 108.5t-9 109.5t-37 100q-16 29 9 39l63 26q35 -62 48 -133.5t4.5 -142t-39.5 -135.5t-81 -114l-28 18q-8 6 -7.5 14.5zM405.5 -277.5q0.5 8.5 5.5 13.5q33 43 51 96t21 108.5t-9 109.5t-37 100q-16 29 9 39l63 26 q35 -62 48 -133.5t4.5 -142t-39.5 -135.5t-81 -114l-28 18q-8 6 -7.5 14.5z" />
<glyph unicode="&#x2022;" d="M294 610q0 62 23.5 117.5t64.5 97t94.5 65t115.5 23.5t117.5 -23.5t96.5 -65t64.5 -96.5t23.5 -118q0 -61 -23.5 -115t-64.5 -95t-96.5 -64.5t-117.5 -23.5q-61 0 -115 23.5t-95 64.5t-64.5 95t-23.5 115z" />
<glyph unicode="&#x2026;" horiz-adv-x="1482" d="M144 79q0 19 7.5 36.5t20 30.5t29.5 20.5t38 7.5q19 0 36.5 -7.5t30 -20.5t20.5 -30.5t8 -36.5q0 -20 -8 -37t-20.5 -30t-30 -20t-36.5 -7q-40 0 -67.5 27t-27.5 67zM646 79q0 19 7.5 36.5t20 30.5t29.5 20.5t38 7.5q19 0 36.5 -7.5t30 -20.5t20.5 -30.5t8 -36.5 q0 -20 -8 -37t-20.5 -30t-30 -20t-36.5 -7q-40 0 -67.5 27t-27.5 67zM1149 79q0 19 7.5 36.5t20 30.5t29.5 20.5t38 7.5q19 0 36.5 -7.5t30 -20.5t20.5 -30.5t8 -36.5q0 -20 -8 -37t-20.5 -30t-30 -20t-36.5 -7q-40 0 -67.5 27t-27.5 67z" />
<glyph unicode="&#x202f;" horiz-adv-x="377" />
<glyph unicode="&#x2039;" horiz-adv-x="551" d="M115 525v14l246 387l31 -16q30 -15 10 -49l-187 -300q-13 -22 -24 -30q9 -7 24 -28l187 -301q19 -34 -10 -49l-31 -16z" />
<glyph unicode="&#x203a;" horiz-adv-x="552" d="M151 202l187 301q14 22 23 28q-10 8 -23 30l-187 300q-19 34 10 49l30 16l247 -387v-14l-247 -388l-30 16q-30 15 -10 49z" />
<glyph unicode="&#x205f;" horiz-adv-x="472" />
<glyph unicode="&#x20ac;" d="M19 583v60h161q-1 20 -1.5 40t-0.5 42q0 50 3 99h-162v60h168q16 135 62.5 242.5t117 182t164.5 114.5t206 40q66 0 121 -11.5t103 -34t89.5 -56t78.5 -77.5l-30 -36q-5 -5 -10 -8.5t-13 -3.5t-19 10.5t-28.5 26t-42.5 34.5t-60 34.5t-80.5 26t-105.5 10.5 q-92 0 -168 -32t-134.5 -94.5t-96.5 -154.5t-53 -213h616v-31q0 -11 -7.5 -20t-24.5 -9h-590q-3 -48 -3 -99q0 -22 0.5 -41.5t1.5 -40.5h530v-31q0 -12 -8.5 -20.5t-24.5 -8.5h-492q13 -127 50 -223.5t94.5 -161t134.5 -97t169 -32.5q63 0 112 12t86 31t63 41t45 41 l31.5 31.5t22.5 12.5t18 -10l38 -35q-37 -47 -80 -85.5t-94.5 -66t-113 -42t-134.5 -14.5q-116 0 -210 40t-163 116.5t-112 187.5t-58 254h-166z" />
<glyph unicode="&#x2122;" horiz-adv-x="1521" d="M98 1389v60h483v-60h-204v-529h-73v529h-206zM709 860v589h58q10 0 14.5 -2t12.5 -10l222 -373q4 -8 6.5 -15.5t4.5 -14.5q3 7 6 14.5t7 15.5l218 373q5 8 10 10t15 2h58v-589h-63v454l5 45l-224 -389q-8 -16 -26 -17h-11q-17 0 -26 17l-228 388l5 -44v-454h-64z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1018" d="M0 0v1019h1019v-1019h-1019z" />
<hkern u1="&#x20;" u2="&#x178;" k="77" />
<hkern u1="&#x20;" u2="&#xff;" k="51" />
<hkern u1="&#x20;" u2="&#xfd;" k="51" />
<hkern u1="&#x20;" u2="&#xdd;" k="77" />
<hkern u1="&#x20;" u2="&#xc6;" k="78" />
<hkern u1="&#x20;" u2="&#xc5;" k="68" />
<hkern u1="&#x20;" u2="&#xc4;" k="68" />
<hkern u1="&#x20;" u2="&#xc3;" k="68" />
<hkern u1="&#x20;" u2="&#xc2;" k="68" />
<hkern u1="&#x20;" u2="&#xc1;" k="68" />
<hkern u1="&#x20;" u2="&#xc0;" k="68" />
<hkern u1="&#x20;" u2="y" k="51" />
<hkern u1="&#x20;" u2="w" k="43" />
<hkern u1="&#x20;" u2="v" k="49" />
<hkern u1="&#x20;" u2="Y" k="77" />
<hkern u1="&#x20;" u2="W" k="45" />
<hkern u1="&#x20;" u2="V" k="72" />
<hkern u1="&#x20;" u2="T" k="61" />
<hkern u1="&#x20;" u2="J" k="61" />
<hkern u1="&#x20;" u2="A" k="68" />
<hkern u1="&#x22;" u2="&#xf0;" k="41" />
<hkern u1="&#x22;" u2="&#xef;" k="-11" />
<hkern u1="&#x22;" u2="&#xee;" k="-20" />
<hkern u1="&#x22;" u2="&#xec;" k="-15" />
<hkern u1="&#x22;" u2="&#x2f;" k="100" />
<hkern u1="&#x22;" u2="&#x2c;" k="201" />
<hkern u1="&#x22;" u2="&#x26;" k="38" />
<hkern u1="&#x26;" u2="&#x201d;" k="63" />
<hkern u1="&#x26;" u2="&#x2019;" k="63" />
<hkern u1="&#x26;" u2="&#x178;" k="100" />
<hkern u1="&#x26;" u2="&#xff;" k="34" />
<hkern u1="&#x26;" u2="&#xfd;" k="34" />
<hkern u1="&#x26;" u2="&#xdd;" k="100" />
<hkern u1="&#x26;" u2="&#xc6;" k="-24" />
<hkern u1="&#x26;" u2="&#xc5;" k="-22" />
<hkern u1="&#x26;" u2="&#xc4;" k="-22" />
<hkern u1="&#x26;" u2="&#xc3;" k="-22" />
<hkern u1="&#x26;" u2="&#xc2;" k="-22" />
<hkern u1="&#x26;" u2="&#xc1;" k="-22" />
<hkern u1="&#x26;" u2="&#xc0;" k="-22" />
<hkern u1="&#x26;" u2="y" k="34" />
<hkern u1="&#x26;" u2="x" k="-15" />
<hkern u1="&#x26;" u2="v" k="33" />
<hkern u1="&#x26;" u2="Z" k="-8" />
<hkern u1="&#x26;" u2="Y" k="100" />
<hkern u1="&#x26;" u2="X" k="-18" />
<hkern u1="&#x26;" u2="W" k="57" />
<hkern u1="&#x26;" u2="V" k="83" />
<hkern u1="&#x26;" u2="T" k="97" />
<hkern u1="&#x26;" u2="J" k="-9" />
<hkern u1="&#x26;" u2="A" k="-22" />
<hkern u1="&#x26;" u2="&#x27;" k="67" />
<hkern u1="&#x26;" u2="&#x22;" k="67" />
<hkern u1="&#x27;" u2="&#xf0;" k="41" />
<hkern u1="&#x27;" u2="&#xef;" k="-11" />
<hkern u1="&#x27;" u2="&#xee;" k="-20" />
<hkern u1="&#x27;" u2="&#xec;" k="-15" />
<hkern u1="&#x27;" u2="&#x2f;" k="100" />
<hkern u1="&#x27;" u2="&#x2c;" k="201" />
<hkern u1="&#x27;" u2="&#x26;" k="38" />
<hkern u1="&#x28;" g2="uniFB04" k="35" />
<hkern u1="&#x28;" g2="uniFB03" k="35" />
<hkern u1="&#x28;" g2="uniFB02" k="35" />
<hkern u1="&#x28;" g2="uniFB01" k="35" />
<hkern u1="&#x28;" u2="&#x178;" k="-6" />
<hkern u1="&#x28;" u2="&#x153;" k="62" />
<hkern u1="&#x28;" u2="&#x152;" k="45" />
<hkern u1="&#x28;" u2="&#xff;" k="34" />
<hkern u1="&#x28;" u2="&#xfd;" k="34" />
<hkern u1="&#x28;" u2="&#xfc;" k="47" />
<hkern u1="&#x28;" u2="&#xfb;" k="47" />
<hkern u1="&#x28;" u2="&#xfa;" k="47" />
<hkern u1="&#x28;" u2="&#xf9;" k="47" />
<hkern u1="&#x28;" u2="&#xf8;" k="62" />
<hkern u1="&#x28;" u2="&#xf6;" k="62" />
<hkern u1="&#x28;" u2="&#xf5;" k="62" />
<hkern u1="&#x28;" u2="&#xf4;" k="62" />
<hkern u1="&#x28;" u2="&#xf3;" k="62" />
<hkern u1="&#x28;" u2="&#xf2;" k="62" />
<hkern u1="&#x28;" u2="&#xf1;" k="27" />
<hkern u1="&#x28;" u2="&#xf0;" k="52" />
<hkern u1="&#x28;" u2="&#xef;" k="-12" />
<hkern u1="&#x28;" u2="&#xee;" k="-5" />
<hkern u1="&#x28;" u2="&#xec;" k="-24" />
<hkern u1="&#x28;" u2="&#xeb;" k="62" />
<hkern u1="&#x28;" u2="&#xea;" k="62" />
<hkern u1="&#x28;" u2="&#xe9;" k="62" />
<hkern u1="&#x28;" u2="&#xe8;" k="62" />
<hkern u1="&#x28;" u2="&#xe7;" k="62" />
<hkern u1="&#x28;" u2="&#xe6;" k="40" />
<hkern u1="&#x28;" u2="&#xe5;" k="40" />
<hkern u1="&#x28;" u2="&#xe4;" k="40" />
<hkern u1="&#x28;" u2="&#xe3;" k="40" />
<hkern u1="&#x28;" u2="&#xe2;" k="40" />
<hkern u1="&#x28;" u2="&#xe1;" k="40" />
<hkern u1="&#x28;" u2="&#xe0;" k="40" />
<hkern u1="&#x28;" u2="&#xdd;" k="-6" />
<hkern u1="&#x28;" u2="&#xd8;" k="45" />
<hkern u1="&#x28;" u2="&#xd6;" k="45" />
<hkern u1="&#x28;" u2="&#xd5;" k="45" />
<hkern u1="&#x28;" u2="&#xd4;" k="45" />
<hkern u1="&#x28;" u2="&#xd3;" k="45" />
<hkern u1="&#x28;" u2="&#xd2;" k="45" />
<hkern u1="&#x28;" u2="&#xc7;" k="45" />
<hkern u1="&#x28;" u2="y" k="34" />
<hkern u1="&#x28;" u2="w" k="43" />
<hkern u1="&#x28;" u2="v" k="50" />
<hkern u1="&#x28;" u2="u" k="47" />
<hkern u1="&#x28;" u2="t" k="38" />
<hkern u1="&#x28;" u2="s" k="39" />
<hkern u1="&#x28;" u2="r" k="27" />
<hkern u1="&#x28;" u2="q" k="60" />
<hkern u1="&#x28;" u2="p" k="27" />
<hkern u1="&#x28;" u2="o" k="62" />
<hkern u1="&#x28;" u2="n" k="27" />
<hkern u1="&#x28;" u2="m" k="27" />
<hkern u1="&#x28;" u2="j" k="-16" />
<hkern u1="&#x28;" u2="f" k="35" />
<hkern u1="&#x28;" u2="e" k="62" />
<hkern u1="&#x28;" u2="d" k="60" />
<hkern u1="&#x28;" u2="c" k="62" />
<hkern u1="&#x28;" u2="a" k="40" />
<hkern u1="&#x28;" u2="Y" k="-6" />
<hkern u1="&#x28;" u2="V" k="-6" />
<hkern u1="&#x28;" u2="Q" k="45" />
<hkern u1="&#x28;" u2="O" k="45" />
<hkern u1="&#x28;" u2="G" k="45" />
<hkern u1="&#x28;" u2="C" k="45" />
<hkern u1="&#x2a;" u2="&#x153;" k="57" />
<hkern u1="&#x2a;" u2="&#xf8;" k="57" />
<hkern u1="&#x2a;" u2="&#xf6;" k="57" />
<hkern u1="&#x2a;" u2="&#xf5;" k="57" />
<hkern u1="&#x2a;" u2="&#xf4;" k="57" />
<hkern u1="&#x2a;" u2="&#xf3;" k="57" />
<hkern u1="&#x2a;" u2="&#xf2;" k="57" />
<hkern u1="&#x2a;" u2="&#xf0;" k="55" />
<hkern u1="&#x2a;" u2="&#xef;" k="-17" />
<hkern u1="&#x2a;" u2="&#xee;" k="-6" />
<hkern u1="&#x2a;" u2="&#xeb;" k="57" />
<hkern u1="&#x2a;" u2="&#xea;" k="57" />
<hkern u1="&#x2a;" u2="&#xe9;" k="57" />
<hkern u1="&#x2a;" u2="&#xe8;" k="57" />
<hkern u1="&#x2a;" u2="&#xe7;" k="57" />
<hkern u1="&#x2a;" u2="&#xe6;" k="46" />
<hkern u1="&#x2a;" u2="&#xe5;" k="46" />
<hkern u1="&#x2a;" u2="&#xe4;" k="46" />
<hkern u1="&#x2a;" u2="&#xe3;" k="46" />
<hkern u1="&#x2a;" u2="&#xe2;" k="46" />
<hkern u1="&#x2a;" u2="&#xe1;" k="46" />
<hkern u1="&#x2a;" u2="&#xe0;" k="46" />
<hkern u1="&#x2a;" u2="&#xc6;" k="178" />
<hkern u1="&#x2a;" u2="&#xc5;" k="114" />
<hkern u1="&#x2a;" u2="&#xc4;" k="114" />
<hkern u1="&#x2a;" u2="&#xc3;" k="114" />
<hkern u1="&#x2a;" u2="&#xc2;" k="114" />
<hkern u1="&#x2a;" u2="&#xc1;" k="114" />
<hkern u1="&#x2a;" u2="&#xc0;" k="114" />
<hkern u1="&#x2a;" u2="s" k="40" />
<hkern u1="&#x2a;" u2="q" k="67" />
<hkern u1="&#x2a;" u2="o" k="57" />
<hkern u1="&#x2a;" u2="g" k="47" />
<hkern u1="&#x2a;" u2="e" k="57" />
<hkern u1="&#x2a;" u2="d" k="67" />
<hkern u1="&#x2a;" u2="c" k="57" />
<hkern u1="&#x2a;" u2="a" k="46" />
<hkern u1="&#x2a;" u2="J" k="151" />
<hkern u1="&#x2a;" u2="A" k="114" />
<hkern u1="&#x2c;" u2="&#x201d;" k="210" />
<hkern u1="&#x2c;" u2="&#x201c;" k="210" />
<hkern u1="&#x2c;" u2="&#x2019;" k="210" />
<hkern u1="&#x2c;" u2="&#x2018;" k="210" />
<hkern u1="&#x2c;" u2="&#x178;" k="168" />
<hkern u1="&#x2c;" u2="&#x152;" k="66" />
<hkern u1="&#x2c;" u2="&#xff;" k="74" />
<hkern u1="&#x2c;" u2="&#xfd;" k="74" />
<hkern u1="&#x2c;" u2="&#xdd;" k="168" />
<hkern u1="&#x2c;" u2="&#xdc;" k="55" />
<hkern u1="&#x2c;" u2="&#xdb;" k="55" />
<hkern u1="&#x2c;" u2="&#xda;" k="55" />
<hkern u1="&#x2c;" u2="&#xd9;" k="55" />
<hkern u1="&#x2c;" u2="&#xd8;" k="66" />
<hkern u1="&#x2c;" u2="&#xd6;" k="66" />
<hkern u1="&#x2c;" u2="&#xd5;" k="66" />
<hkern u1="&#x2c;" u2="&#xd4;" k="66" />
<hkern u1="&#x2c;" u2="&#xd3;" k="66" />
<hkern u1="&#x2c;" u2="&#xd2;" k="66" />
<hkern u1="&#x2c;" u2="&#xc7;" k="66" />
<hkern u1="&#x2c;" u2="y" k="74" />
<hkern u1="&#x2c;" u2="w" k="49" />
<hkern u1="&#x2c;" u2="v" k="74" />
<hkern u1="&#x2c;" u2="Y" k="168" />
<hkern u1="&#x2c;" u2="W" k="119" />
<hkern u1="&#x2c;" u2="V" k="165" />
<hkern u1="&#x2c;" u2="U" k="55" />
<hkern u1="&#x2c;" u2="T" k="154" />
<hkern u1="&#x2c;" u2="Q" k="66" />
<hkern u1="&#x2c;" u2="O" k="66" />
<hkern u1="&#x2c;" u2="G" k="66" />
<hkern u1="&#x2c;" u2="C" k="66" />
<hkern u1="&#x2c;" u2="&#x27;" k="201" />
<hkern u1="&#x2c;" u2="&#x22;" k="201" />
<hkern u1="&#x2e;" u2="&#x201d;" k="210" />
<hkern u1="&#x2e;" u2="&#x201c;" k="210" />
<hkern u1="&#x2e;" u2="&#x2019;" k="210" />
<hkern u1="&#x2e;" u2="&#x2018;" k="210" />
<hkern u1="&#x2e;" u2="&#x178;" k="174" />
<hkern u1="&#x2e;" u2="&#x152;" k="56" />
<hkern u1="&#x2e;" u2="&#xff;" k="75" />
<hkern u1="&#x2e;" u2="&#xfd;" k="75" />
<hkern u1="&#x2e;" u2="&#xdd;" k="174" />
<hkern u1="&#x2e;" u2="&#xdc;" k="48" />
<hkern u1="&#x2e;" u2="&#xdb;" k="48" />
<hkern u1="&#x2e;" u2="&#xda;" k="48" />
<hkern u1="&#x2e;" u2="&#xd9;" k="48" />
<hkern u1="&#x2e;" u2="&#xd8;" k="56" />
<hkern u1="&#x2e;" u2="&#xd6;" k="56" />
<hkern u1="&#x2e;" u2="&#xd5;" k="56" />
<hkern u1="&#x2e;" u2="&#xd4;" k="56" />
<hkern u1="&#x2e;" u2="&#xd3;" k="56" />
<hkern u1="&#x2e;" u2="&#xd2;" k="56" />
<hkern u1="&#x2e;" u2="&#xc7;" k="56" />
<hkern u1="&#x2e;" u2="y" k="75" />
<hkern u1="&#x2e;" u2="w" k="44" />
<hkern u1="&#x2e;" u2="v" k="69" />
<hkern u1="&#x2e;" u2="Y" k="174" />
<hkern u1="&#x2e;" u2="W" k="116" />
<hkern u1="&#x2e;" u2="V" k="160" />
<hkern u1="&#x2e;" u2="U" k="48" />
<hkern u1="&#x2e;" u2="T" k="155" />
<hkern u1="&#x2e;" u2="Q" k="56" />
<hkern u1="&#x2e;" u2="O" k="56" />
<hkern u1="&#x2e;" u2="G" k="56" />
<hkern u1="&#x2e;" u2="C" k="56" />
<hkern u1="&#x2e;" u2="&#x27;" k="201" />
<hkern u1="&#x2e;" u2="&#x22;" k="201" />
<hkern u1="&#x2f;" g2="uniFB04" k="9" />
<hkern u1="&#x2f;" g2="uniFB03" k="9" />
<hkern u1="&#x2f;" g2="uniFB02" k="9" />
<hkern u1="&#x2f;" g2="uniFB01" k="9" />
<hkern u1="&#x2f;" u2="&#x178;" k="-4" />
<hkern u1="&#x2f;" u2="&#x153;" k="87" />
<hkern u1="&#x2f;" u2="&#x152;" k="8" />
<hkern u1="&#x2f;" u2="&#xfc;" k="50" />
<hkern u1="&#x2f;" u2="&#xfb;" k="50" />
<hkern u1="&#x2f;" u2="&#xfa;" k="50" />
<hkern u1="&#x2f;" u2="&#xf9;" k="50" />
<hkern u1="&#x2f;" u2="&#xf8;" k="87" />
<hkern u1="&#x2f;" u2="&#xf6;" k="87" />
<hkern u1="&#x2f;" u2="&#xf5;" k="87" />
<hkern u1="&#x2f;" u2="&#xf4;" k="87" />
<hkern u1="&#x2f;" u2="&#xf3;" k="87" />
<hkern u1="&#x2f;" u2="&#xf2;" k="87" />
<hkern u1="&#x2f;" u2="&#xf1;" k="53" />
<hkern u1="&#x2f;" u2="&#xf0;" k="60" />
<hkern u1="&#x2f;" u2="&#xef;" k="-5" />
<hkern u1="&#x2f;" u2="&#xec;" k="-24" />
<hkern u1="&#x2f;" u2="&#xeb;" k="87" />
<hkern u1="&#x2f;" u2="&#xea;" k="87" />
<hkern u1="&#x2f;" u2="&#xe9;" k="87" />
<hkern u1="&#x2f;" u2="&#xe8;" k="87" />
<hkern u1="&#x2f;" u2="&#xe7;" k="87" />
<hkern u1="&#x2f;" u2="&#xe6;" k="78" />
<hkern u1="&#x2f;" u2="&#xe5;" k="78" />
<hkern u1="&#x2f;" u2="&#xe4;" k="78" />
<hkern u1="&#x2f;" u2="&#xe3;" k="78" />
<hkern u1="&#x2f;" u2="&#xe2;" k="78" />
<hkern u1="&#x2f;" u2="&#xe1;" k="78" />
<hkern u1="&#x2f;" u2="&#xe0;" k="78" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-4" />
<hkern u1="&#x2f;" u2="&#xd8;" k="8" />
<hkern u1="&#x2f;" u2="&#xd6;" k="8" />
<hkern u1="&#x2f;" u2="&#xd5;" k="8" />
<hkern u1="&#x2f;" u2="&#xd4;" k="8" />
<hkern u1="&#x2f;" u2="&#xd3;" k="8" />
<hkern u1="&#x2f;" u2="&#xd2;" k="8" />
<hkern u1="&#x2f;" u2="&#xc7;" k="8" />
<hkern u1="&#x2f;" u2="&#xc6;" k="147" />
<hkern u1="&#x2f;" u2="&#xc5;" k="110" />
<hkern u1="&#x2f;" u2="&#xc4;" k="110" />
<hkern u1="&#x2f;" u2="&#xc3;" k="110" />
<hkern u1="&#x2f;" u2="&#xc2;" k="110" />
<hkern u1="&#x2f;" u2="&#xc1;" k="110" />
<hkern u1="&#x2f;" u2="&#xc0;" k="110" />
<hkern u1="&#x2f;" u2="z" k="14" />
<hkern u1="&#x2f;" u2="x" k="9" />
<hkern u1="&#x2f;" u2="u" k="50" />
<hkern u1="&#x2f;" u2="t" k="10" />
<hkern u1="&#x2f;" u2="s" k="71" />
<hkern u1="&#x2f;" u2="r" k="53" />
<hkern u1="&#x2f;" u2="q" k="90" />
<hkern u1="&#x2f;" u2="p" k="53" />
<hkern u1="&#x2f;" u2="o" k="87" />
<hkern u1="&#x2f;" u2="n" k="53" />
<hkern u1="&#x2f;" u2="m" k="53" />
<hkern u1="&#x2f;" u2="g" k="83" />
<hkern u1="&#x2f;" u2="f" k="9" />
<hkern u1="&#x2f;" u2="e" k="87" />
<hkern u1="&#x2f;" u2="d" k="90" />
<hkern u1="&#x2f;" u2="c" k="87" />
<hkern u1="&#x2f;" u2="a" k="78" />
<hkern u1="&#x2f;" u2="Y" k="-4" />
<hkern u1="&#x2f;" u2="V" k="-4" />
<hkern u1="&#x2f;" u2="Q" k="8" />
<hkern u1="&#x2f;" u2="O" k="8" />
<hkern u1="&#x2f;" u2="J" k="122" />
<hkern u1="&#x2f;" u2="G" k="8" />
<hkern u1="&#x2f;" u2="C" k="8" />
<hkern u1="&#x2f;" u2="A" k="110" />
<hkern u1="&#x2f;" u2="&#x2f;" k="434" />
<hkern u1="&#x3a;" u2="&#x178;" k="69" />
<hkern u1="&#x3a;" u2="&#xdd;" k="69" />
<hkern u1="&#x3a;" u2="Y" k="69" />
<hkern u1="&#x3a;" u2="W" k="9" />
<hkern u1="&#x3a;" u2="V" k="14" />
<hkern u1="&#x3a;" u2="T" k="114" />
<hkern u1="&#x3b;" u2="&#x178;" k="67" />
<hkern u1="&#x3b;" u2="&#xdd;" k="67" />
<hkern u1="&#x3b;" u2="Y" k="67" />
<hkern u1="&#x3b;" u2="W" k="9" />
<hkern u1="&#x3b;" u2="V" k="14" />
<hkern u1="&#x3b;" u2="T" k="110" />
<hkern u1="&#x40;" u2="&#x178;" k="63" />
<hkern u1="&#x40;" u2="&#xdd;" k="63" />
<hkern u1="&#x40;" u2="Y" k="63" />
<hkern u1="&#x40;" u2="V" k="9" />
<hkern u1="&#x40;" u2="T" k="87" />
<hkern u1="A" u2="&#x2122;" k="126" />
<hkern u1="A" u2="&#xf0;" k="26" />
<hkern u1="A" u2="&#xba;" k="108" />
<hkern u1="A" u2="&#xae;" k="8" />
<hkern u1="A" u2="&#xaa;" k="96" />
<hkern u1="A" u2="&#xa9;" k="8" />
<hkern u1="A" u2="&#x7d;" k="30" />
<hkern u1="A" u2="]" k="40" />
<hkern u1="A" u2="\" k="110" />
<hkern u1="A" u2="&#x2a;" k="115" />
<hkern u1="A" u2="&#x20;" k="68" />
<hkern u1="B" u2="&#xf0;" k="16" />
<hkern u1="B" u2="&#xee;" k="14" />
<hkern u1="B" u2="&#x7d;" k="33" />
<hkern u1="B" u2="]" k="39" />
<hkern u1="C" u2="&#x2122;" k="-5" />
<hkern u1="C" u2="&#xf0;" k="17" />
<hkern u1="C" u2="&#xef;" k="-20" />
<hkern u1="C" u2="&#xee;" k="-10" />
<hkern u1="C" u2="&#x2a;" k="-3" />
<hkern u1="D" u2="&#xf0;" k="20" />
<hkern u1="D" u2="&#xd0;" k="5" />
<hkern u1="D" u2="&#x7d;" k="45" />
<hkern u1="D" u2="]" k="54" />
<hkern u1="D" u2="&#x2f;" k="11" />
<hkern u1="D" u2="&#x2c;" k="69" />
<hkern u1="D" u2="&#x29;" k="44" />
<hkern u1="E" u2="&#xf0;" k="58" />
<hkern u1="E" u2="&#xef;" k="-1" />
<hkern u1="E" u2="&#xee;" k="-6" />
<hkern u1="E" u2="&#xed;" k="11" />
<hkern u1="E" u2="&#xec;" k="-4" />
<hkern u1="E" u2="&#xdf;" k="35" />
<hkern u1="F" u2="&#xf0;" k="109" />
<hkern u1="F" u2="&#xef;" k="-6" />
<hkern u1="F" u2="&#xee;" k="-11" />
<hkern u1="F" u2="&#xed;" k="33" />
<hkern u1="F" u2="&#xec;" k="-8" />
<hkern u1="F" u2="&#xdf;" k="62" />
<hkern u1="F" u2="&#x3b;" k="7" />
<hkern u1="F" u2="&#x3a;" k="8" />
<hkern u1="F" u2="&#x2f;" k="87" />
<hkern u1="F" u2="&#x2c;" k="203" />
<hkern u1="F" u2="&#x20;" k="44" />
<hkern u1="G" u2="&#xf0;" k="13" />
<hkern u1="G" u2="&#xef;" k="7" />
<hkern u1="G" u2="]" k="29" />
<hkern u1="H" u2="&#xf0;" k="35" />
<hkern u1="H" u2="&#xee;" k="12" />
<hkern u1="I" u2="&#xf0;" k="35" />
<hkern u1="I" u2="&#xee;" k="12" />
<hkern u1="J" u2="&#xf0;" k="44" />
<hkern u1="J" u2="&#xee;" k="12" />
<hkern u1="J" u2="&#xed;" k="23" />
<hkern u1="J" u2="&#x2f;" k="8" />
<hkern u1="J" u2="&#x2c;" k="38" />
<hkern u1="K" u2="&#x2122;" k="-6" />
<hkern u1="K" u2="&#xf8;" k="71" />
<hkern u1="K" u2="&#xf0;" k="80" />
<hkern u1="K" u2="&#xef;" k="-6" />
<hkern u1="K" u2="&#xec;" k="-28" />
<hkern u1="K" u2="&#xae;" k="47" />
<hkern u1="K" u2="&#xa9;" k="48" />
<hkern u1="L" u2="&#x2122;" k="214" />
<hkern u1="L" u2="&#xf0;" k="98" />
<hkern u1="L" u2="&#xba;" k="211" />
<hkern u1="L" u2="&#xb7;" k="305" />
<hkern u1="L" u2="&#xae;" k="60" />
<hkern u1="L" u2="&#xaa;" k="211" />
<hkern u1="L" u2="&#xa9;" k="60" />
<hkern u1="L" u2="\" k="142" />
<hkern u1="L" u2="&#x2a;" k="214" />
<hkern u1="L" u2="&#x20;" k="60" />
<hkern u1="M" u2="&#xf0;" k="35" />
<hkern u1="M" u2="&#xee;" k="12" />
<hkern u1="N" u2="&#xf0;" k="35" />
<hkern u1="N" u2="&#xee;" k="12" />
<hkern u1="O" u2="&#xf0;" k="19" />
<hkern u1="O" u2="&#xd0;" k="5" />
<hkern u1="O" u2="&#x7d;" k="43" />
<hkern u1="O" u2="]" k="53" />
<hkern u1="O" u2="&#x2f;" k="10" />
<hkern u1="O" u2="&#x2c;" k="69" />
<hkern u1="O" u2="&#x29;" k="43" />
<hkern u1="P" u2="&#xf0;" k="99" />
<hkern u1="P" u2="&#xee;" k="-10" />
<hkern u1="P" u2="]" k="30" />
<hkern u1="P" u2="&#x2f;" k="93" />
<hkern u1="P" u2="&#x2c;" k="207" />
<hkern u1="P" u2="&#x26;" k="27" />
<hkern u1="P" u2="&#x20;" k="56" />
<hkern u1="Q" u2="&#x201e;" k="29" />
<hkern u1="Q" u2="&#x201a;" k="29" />
<hkern u1="Q" u2="&#xf0;" k="19" />
<hkern u1="Q" u2="&#xd0;" k="5" />
<hkern u1="Q" u2="&#x7d;" k="28" />
<hkern u1="Q" u2="]" k="35" />
<hkern u1="Q" u2="&#x2f;" k="10" />
<hkern u1="Q" u2="&#x2c;" k="69" />
<hkern u1="Q" u2="&#x29;" k="39" />
<hkern u1="R" u2="&#xf8;" k="58" />
<hkern u1="R" u2="&#xf0;" k="71" />
<hkern u1="R" u2="&#xee;" k="10" />
<hkern u1="R" u2="&#xd0;" k="3" />
<hkern u1="S" u2="&#xf0;" k="9" />
<hkern u1="S" u2="&#xef;" k="9" />
<hkern u1="S" u2="]" k="28" />
<hkern u1="T" g2="uniFB02" k="146" />
<hkern u1="T" g2="uniFB01" k="138" />
<hkern u1="T" u2="&#x2122;" k="-13" />
<hkern u1="T" u2="&#xf6;" k="247" />
<hkern u1="T" u2="&#xf4;" k="247" />
<hkern u1="T" u2="&#xf2;" k="251" />
<hkern u1="T" u2="&#xf0;" k="163" />
<hkern u1="T" u2="&#xef;" k="-6" />
<hkern u1="T" u2="&#xee;" k="-27" />
<hkern u1="T" u2="&#xed;" k="74" />
<hkern u1="T" u2="&#xec;" k="-22" />
<hkern u1="T" u2="&#xeb;" k="243" />
<hkern u1="T" u2="&#xea;" k="244" />
<hkern u1="T" u2="&#xe8;" k="248" />
<hkern u1="T" u2="&#xe4;" k="221" />
<hkern u1="T" u2="&#xe3;" k="228" />
<hkern u1="T" u2="&#xe2;" k="221" />
<hkern u1="T" u2="&#xe0;" k="226" />
<hkern u1="T" u2="&#xdf;" k="119" />
<hkern u1="T" u2="&#xae;" k="53" />
<hkern u1="T" u2="&#xa9;" k="53" />
<hkern u1="T" u2="&#x40;" k="110" />
<hkern u1="T" u2="&#x3b;" k="109" />
<hkern u1="T" u2="&#x3a;" k="113" />
<hkern u1="T" u2="&#x2f;" k="126" />
<hkern u1="T" u2="&#x2c;" k="156" />
<hkern u1="T" u2="&#x26;" k="53" />
<hkern u1="T" u2="&#x20;" k="61" />
<hkern u1="U" u2="&#xf0;" k="51" />
<hkern u1="U" u2="&#xee;" k="16" />
<hkern u1="U" u2="&#xed;" k="30" />
<hkern u1="U" u2="&#xdf;" k="30" />
<hkern u1="U" u2="&#x2f;" k="11" />
<hkern u1="U" u2="&#x2c;" k="58" />
<hkern u1="V" u2="&#x2122;" k="-14" />
<hkern u1="V" u2="&#xf0;" k="126" />
<hkern u1="V" u2="&#xef;" k="-20" />
<hkern u1="V" u2="&#xee;" k="-13" />
<hkern u1="V" u2="&#xed;" k="35" />
<hkern u1="V" u2="&#xec;" k="-36" />
<hkern u1="V" u2="&#xe8;" k="139" />
<hkern u1="V" u2="&#xe4;" k="136" />
<hkern u1="V" u2="&#xe0;" k="129" />
<hkern u1="V" u2="&#xdf;" k="72" />
<hkern u1="V" u2="&#xae;" k="9" />
<hkern u1="V" u2="&#xa9;" k="9" />
<hkern u1="V" u2="&#x7d;" k="-6" />
<hkern u1="V" u2="]" k="-8" />
<hkern u1="V" u2="\" k="-4" />
<hkern u1="V" u2="&#x40;" k="51" />
<hkern u1="V" u2="&#x3b;" k="13" />
<hkern u1="V" u2="&#x3a;" k="14" />
<hkern u1="V" u2="&#x2f;" k="121" />
<hkern u1="V" u2="&#x2c;" k="167" />
<hkern u1="V" u2="&#x29;" k="-6" />
<hkern u1="V" u2="&#x26;" k="54" />
<hkern u1="V" u2="&#x20;" k="71" />
<hkern u1="W" u2="&#x2122;" k="-6" />
<hkern u1="W" u2="&#xf0;" k="103" />
<hkern u1="W" u2="&#xef;" k="-15" />
<hkern u1="W" u2="&#xee;" k="-12" />
<hkern u1="W" u2="&#xed;" k="26" />
<hkern u1="W" u2="&#xec;" k="-28" />
<hkern u1="W" u2="&#xdf;" k="56" />
<hkern u1="W" u2="&#xd0;" k="6" />
<hkern u1="W" u2="&#x40;" k="9" />
<hkern u1="W" u2="&#x3b;" k="8" />
<hkern u1="W" u2="&#x3a;" k="9" />
<hkern u1="W" u2="&#x2f;" k="86" />
<hkern u1="W" u2="&#x2c;" k="122" />
<hkern u1="W" u2="&#x26;" k="13" />
<hkern u1="W" u2="&#x20;" k="45" />
<hkern u1="X" u2="&#x2122;" k="-7" />
<hkern u1="X" u2="&#xf8;" k="72" />
<hkern u1="X" u2="&#xf0;" k="78" />
<hkern u1="X" u2="&#xef;" k="-8" />
<hkern u1="X" u2="&#xed;" k="18" />
<hkern u1="X" u2="&#xec;" k="-29" />
<hkern u1="X" u2="&#xdf;" k="17" />
<hkern u1="X" u2="&#xae;" k="35" />
<hkern u1="X" u2="&#xa9;" k="35" />
<hkern u1="Y" g2="uniFB02" k="103" />
<hkern u1="Y" u2="&#x2122;" k="-14" />
<hkern u1="Y" u2="&#xf9;" k="159" />
<hkern u1="Y" u2="&#xf6;" k="210" />
<hkern u1="Y" u2="&#xf2;" k="199" />
<hkern u1="Y" u2="&#xf0;" k="145" />
<hkern u1="Y" u2="&#xef;" k="-16" />
<hkern u1="Y" u2="&#xed;" k="56" />
<hkern u1="Y" u2="&#xec;" k="-36" />
<hkern u1="Y" u2="&#xeb;" k="207" />
<hkern u1="Y" u2="&#xe8;" k="196" />
<hkern u1="Y" u2="&#xe4;" k="198" />
<hkern u1="Y" u2="&#xe3;" k="214" />
<hkern u1="Y" u2="&#xe0;" k="186" />
<hkern u1="Y" u2="&#xdf;" k="88" />
<hkern u1="Y" u2="&#xae;" k="57" />
<hkern u1="Y" u2="&#xa9;" k="58" />
<hkern u1="Y" u2="&#x7d;" k="-6" />
<hkern u1="Y" u2="]" k="-8" />
<hkern u1="Y" u2="\" k="-4" />
<hkern u1="Y" u2="&#x40;" k="90" />
<hkern u1="Y" u2="&#x3b;" k="65" />
<hkern u1="Y" u2="&#x3a;" k="69" />
<hkern u1="Y" u2="&#x2f;" k="139" />
<hkern u1="Y" u2="&#x2c;" k="171" />
<hkern u1="Y" u2="&#x29;" k="-6" />
<hkern u1="Y" u2="&#x26;" k="74" />
<hkern u1="Y" u2="&#x20;" k="77" />
<hkern u1="Z" u2="&#xf0;" k="53" />
<hkern u1="Z" u2="&#xef;" k="-8" />
<hkern u1="Z" u2="&#xee;" k="1" />
<hkern u1="Z" u2="&#xed;" k="11" />
<hkern u1="Z" u2="&#xec;" k="-11" />
<hkern u1="Z" u2="&#xdf;" k="28" />
<hkern u1="Z" u2="&#xae;" k="30" />
<hkern u1="Z" u2="&#xa9;" k="30" />
<hkern u1="[" g2="uniFB04" k="51" />
<hkern u1="[" g2="uniFB03" k="51" />
<hkern u1="[" g2="uniFB02" k="51" />
<hkern u1="[" g2="uniFB01" k="51" />
<hkern u1="[" u2="&#x178;" k="-8" />
<hkern u1="[" u2="&#x153;" k="73" />
<hkern u1="[" u2="&#x152;" k="53" />
<hkern u1="[" u2="&#xff;" k="50" />
<hkern u1="[" u2="&#xfd;" k="50" />
<hkern u1="[" u2="&#xfc;" k="62" />
<hkern u1="[" u2="&#xfb;" k="62" />
<hkern u1="[" u2="&#xfa;" k="62" />
<hkern u1="[" u2="&#xf9;" k="62" />
<hkern u1="[" u2="&#xf8;" k="73" />
<hkern u1="[" u2="&#xf6;" k="73" />
<hkern u1="[" u2="&#xf5;" k="73" />
<hkern u1="[" u2="&#xf4;" k="73" />
<hkern u1="[" u2="&#xf3;" k="73" />
<hkern u1="[" u2="&#xf2;" k="73" />
<hkern u1="[" u2="&#xf1;" k="59" />
<hkern u1="[" u2="&#xf0;" k="66" />
<hkern u1="[" u2="&#xef;" k="-12" />
<hkern u1="[" u2="&#xec;" k="-27" />
<hkern u1="[" u2="&#xeb;" k="73" />
<hkern u1="[" u2="&#xea;" k="73" />
<hkern u1="[" u2="&#xe9;" k="73" />
<hkern u1="[" u2="&#xe8;" k="73" />
<hkern u1="[" u2="&#xe7;" k="73" />
<hkern u1="[" u2="&#xe6;" k="63" />
<hkern u1="[" u2="&#xe5;" k="63" />
<hkern u1="[" u2="&#xe4;" k="63" />
<hkern u1="[" u2="&#xe3;" k="63" />
<hkern u1="[" u2="&#xe2;" k="63" />
<hkern u1="[" u2="&#xe1;" k="63" />
<hkern u1="[" u2="&#xe0;" k="63" />
<hkern u1="[" u2="&#xdd;" k="-8" />
<hkern u1="[" u2="&#xd8;" k="53" />
<hkern u1="[" u2="&#xd6;" k="53" />
<hkern u1="[" u2="&#xd5;" k="53" />
<hkern u1="[" u2="&#xd4;" k="53" />
<hkern u1="[" u2="&#xd3;" k="53" />
<hkern u1="[" u2="&#xd2;" k="53" />
<hkern u1="[" u2="&#xcf;" k="-15" />
<hkern u1="[" u2="&#xce;" k="-25" />
<hkern u1="[" u2="&#xc7;" k="53" />
<hkern u1="[" u2="&#xc6;" k="51" />
<hkern u1="[" u2="&#xc5;" k="43" />
<hkern u1="[" u2="&#xc4;" k="43" />
<hkern u1="[" u2="&#xc3;" k="43" />
<hkern u1="[" u2="&#xc2;" k="43" />
<hkern u1="[" u2="&#xc1;" k="43" />
<hkern u1="[" u2="&#xc0;" k="43" />
<hkern u1="[" u2="z" k="47" />
<hkern u1="[" u2="y" k="50" />
<hkern u1="[" u2="x" k="48" />
<hkern u1="[" u2="w" k="54" />
<hkern u1="[" u2="v" k="61" />
<hkern u1="[" u2="u" k="62" />
<hkern u1="[" u2="t" k="55" />
<hkern u1="[" u2="s" k="63" />
<hkern u1="[" u2="r" k="59" />
<hkern u1="[" u2="q" k="72" />
<hkern u1="[" u2="p" k="59" />
<hkern u1="[" u2="o" k="73" />
<hkern u1="[" u2="n" k="59" />
<hkern u1="[" u2="m" k="59" />
<hkern u1="[" u2="j" k="-19" />
<hkern u1="[" u2="f" k="51" />
<hkern u1="[" u2="e" k="73" />
<hkern u1="[" u2="d" k="72" />
<hkern u1="[" u2="c" k="73" />
<hkern u1="[" u2="a" k="63" />
<hkern u1="[" u2="Y" k="-8" />
<hkern u1="[" u2="V" k="-8" />
<hkern u1="[" u2="S" k="33" />
<hkern u1="[" u2="Q" k="53" />
<hkern u1="[" u2="O" k="53" />
<hkern u1="[" u2="G" k="53" />
<hkern u1="[" u2="C" k="53" />
<hkern u1="[" u2="A" k="43" />
<hkern u1="\" g2="uniFB04" k="11" />
<hkern u1="\" g2="uniFB03" k="11" />
<hkern u1="\" g2="uniFB02" k="11" />
<hkern u1="\" g2="uniFB01" k="11" />
<hkern u1="\" u2="&#x201d;" k="104" />
<hkern u1="\" u2="&#x2019;" k="104" />
<hkern u1="\" u2="&#x178;" k="139" />
<hkern u1="\" u2="&#x152;" k="11" />
<hkern u1="\" u2="&#xff;" k="68" />
<hkern u1="\" u2="&#xfd;" k="68" />
<hkern u1="\" u2="&#xdd;" k="139" />
<hkern u1="\" u2="&#xdc;" k="11" />
<hkern u1="\" u2="&#xdb;" k="11" />
<hkern u1="\" u2="&#xda;" k="11" />
<hkern u1="\" u2="&#xd9;" k="11" />
<hkern u1="\" u2="&#xd8;" k="11" />
<hkern u1="\" u2="&#xd6;" k="11" />
<hkern u1="\" u2="&#xd5;" k="11" />
<hkern u1="\" u2="&#xd4;" k="11" />
<hkern u1="\" u2="&#xd3;" k="11" />
<hkern u1="\" u2="&#xd2;" k="11" />
<hkern u1="\" u2="&#xc7;" k="11" />
<hkern u1="\" u2="y" k="68" />
<hkern u1="\" u2="w" k="50" />
<hkern u1="\" u2="v" k="72" />
<hkern u1="\" u2="t" k="14" />
<hkern u1="\" u2="f" k="11" />
<hkern u1="\" u2="Y" k="139" />
<hkern u1="\" u2="W" k="86" />
<hkern u1="\" u2="V" k="122" />
<hkern u1="\" u2="U" k="11" />
<hkern u1="\" u2="T" k="130" />
<hkern u1="\" u2="Q" k="11" />
<hkern u1="\" u2="O" k="11" />
<hkern u1="\" u2="G" k="11" />
<hkern u1="\" u2="C" k="11" />
<hkern u1="\" u2="&#x27;" k="100" />
<hkern u1="\" u2="&#x22;" k="100" />
<hkern u1="a" u2="&#x2122;" k="45" />
<hkern u1="a" u2="&#xba;" k="9" />
<hkern u1="a" u2="&#xaa;" k="6" />
<hkern u1="a" u2="&#x7d;" k="55" />
<hkern u1="a" u2="]" k="63" />
<hkern u1="a" u2="\" k="78" />
<hkern u1="a" u2="&#x3f;" k="44" />
<hkern u1="a" u2="&#x2a;" k="43" />
<hkern u1="a" u2="&#x29;" k="37" />
<hkern u1="b" u2="&#x2122;" k="54" />
<hkern u1="b" u2="&#xba;" k="35" />
<hkern u1="b" u2="&#xaa;" k="27" />
<hkern u1="b" u2="&#x7d;" k="60" />
<hkern u1="b" u2="]" k="72" />
<hkern u1="b" u2="\" k="84" />
<hkern u1="b" u2="&#x3f;" k="61" />
<hkern u1="b" u2="&#x2f;" k="8" />
<hkern u1="b" u2="&#x2a;" k="60" />
<hkern u1="b" u2="&#x29;" k="61" />
<hkern u1="c" u2="&#x2122;" k="34" />
<hkern u1="c" u2="&#xf0;" k="25" />
<hkern u1="c" u2="&#x7d;" k="54" />
<hkern u1="c" u2="]" k="60" />
<hkern u1="c" u2="\" k="56" />
<hkern u1="c" u2="&#x3f;" k="9" />
<hkern u1="c" u2="&#x2a;" k="23" />
<hkern u1="c" u2="&#x29;" k="35" />
<hkern u1="d" u2="&#xee;" k="-9" />
<hkern u1="e" u2="&#x2122;" k="49" />
<hkern u1="e" u2="&#xba;" k="29" />
<hkern u1="e" u2="&#x7d;" k="59" />
<hkern u1="e" u2="]" k="67" />
<hkern u1="e" u2="\" k="77" />
<hkern u1="e" u2="&#x3f;" k="49" />
<hkern u1="e" u2="&#x2a;" k="51" />
<hkern u1="e" u2="&#x29;" k="48" />
<hkern u1="f" u2="&#x2122;" k="-3" />
<hkern u1="f" u2="&#xf0;" k="76" />
<hkern u1="f" u2="&#xef;" k="-26" />
<hkern u1="f" u2="&#xee;" k="-8" />
<hkern u1="f" u2="&#xec;" k="-26" />
<hkern u1="f" u2="&#x7d;" k="-4" />
<hkern u1="f" u2="]" k="-5" />
<hkern u1="f" u2="&#x2f;" k="70" />
<hkern u1="f" u2="&#x2c;" k="56" />
<hkern u1="f" u2="&#x29;" k="-3" />
<hkern u1="f" u2="&#x20;" k="36" />
<hkern u1="g" u2="&#xf0;" k="20" />
<hkern u1="g" u2="\" k="12" />
<hkern u1="h" u2="&#x2122;" k="46" />
<hkern u1="h" u2="&#xba;" k="9" />
<hkern u1="h" u2="&#x7d;" k="54" />
<hkern u1="h" u2="]" k="63" />
<hkern u1="h" u2="\" k="80" />
<hkern u1="h" u2="&#x3f;" k="44" />
<hkern u1="h" u2="&#x2a;" k="45" />
<hkern u1="h" u2="&#x29;" k="38" />
<hkern u1="i" u2="&#xef;" k="-5" />
<hkern u1="j" u2="&#xef;" k="-5" />
<hkern u1="k" u2="&#x2122;" k="27" />
<hkern u1="k" u2="&#xf0;" k="74" />
<hkern u1="k" u2="&#x7d;" k="36" />
<hkern u1="k" u2="]" k="47" />
<hkern u1="k" u2="\" k="39" />
<hkern u1="l" u2="&#xee;" k="-9" />
<hkern u1="l" u2="&#xb7;" k="62" />
<hkern u1="m" u2="&#x2122;" k="46" />
<hkern u1="m" u2="&#xba;" k="9" />
<hkern u1="m" u2="&#x7d;" k="54" />
<hkern u1="m" u2="]" k="63" />
<hkern u1="m" u2="\" k="80" />
<hkern u1="m" u2="&#x3f;" k="44" />
<hkern u1="m" u2="&#x2a;" k="45" />
<hkern u1="m" u2="&#x29;" k="38" />
<hkern u1="n" u2="&#x2122;" k="46" />
<hkern u1="n" u2="&#xba;" k="9" />
<hkern u1="n" u2="&#x7d;" k="54" />
<hkern u1="n" u2="]" k="63" />
<hkern u1="n" u2="\" k="80" />
<hkern u1="n" u2="&#x3f;" k="44" />
<hkern u1="n" u2="&#x2a;" k="45" />
<hkern u1="n" u2="&#x29;" k="38" />
<hkern u1="o" u2="&#x2122;" k="53" />
<hkern u1="o" u2="&#xba;" k="35" />
<hkern u1="o" u2="&#xaa;" k="8" />
<hkern u1="o" u2="&#x7d;" k="62" />
<hkern u1="o" u2="]" k="73" />
<hkern u1="o" u2="\" k="87" />
<hkern u1="o" u2="&#x3f;" k="58" />
<hkern u1="o" u2="&#x2a;" k="57" />
<hkern u1="o" u2="&#x29;" k="62" />
<hkern u1="p" u2="&#x2122;" k="54" />
<hkern u1="p" u2="&#xba;" k="35" />
<hkern u1="p" u2="&#xaa;" k="27" />
<hkern u1="p" u2="&#x7d;" k="60" />
<hkern u1="p" u2="]" k="72" />
<hkern u1="p" u2="\" k="84" />
<hkern u1="p" u2="&#x3f;" k="61" />
<hkern u1="p" u2="&#x2f;" k="8" />
<hkern u1="p" u2="&#x2a;" k="60" />
<hkern u1="p" u2="&#x29;" k="61" />
<hkern u1="q" u2="&#x2122;" k="29" />
<hkern u1="q" u2="&#x7d;" k="49" />
<hkern u1="q" u2="]" k="59" />
<hkern u1="q" u2="\" k="53" />
<hkern u1="q" u2="&#x3f;" k="8" />
<hkern u1="q" u2="&#x29;" k="27" />
<hkern u1="r" u2="&#xf0;" k="87" />
<hkern u1="r" u2="&#x7d;" k="47" />
<hkern u1="r" u2="]" k="54" />
<hkern u1="r" u2="&#x2f;" k="85" />
<hkern u1="r" u2="&#x2c;" k="84" />
<hkern u1="r" u2="&#x29;" k="43" />
<hkern u1="r" u2="&#x20;" k="44" />
<hkern u1="s" u2="&#x2122;" k="38" />
<hkern u1="s" u2="&#x7d;" k="53" />
<hkern u1="s" u2="]" k="63" />
<hkern u1="s" u2="\" k="62" />
<hkern u1="s" u2="&#x3f;" k="11" />
<hkern u1="s" u2="&#x2a;" k="27" />
<hkern u1="s" u2="&#x29;" k="44" />
<hkern u1="t" u2="&#xf0;" k="4" />
<hkern u1="t" u2="\" k="9" />
<hkern u1="u" u2="&#x2122;" k="29" />
<hkern u1="u" u2="&#x7d;" k="49" />
<hkern u1="u" u2="]" k="59" />
<hkern u1="u" u2="\" k="53" />
<hkern u1="u" u2="&#x3f;" k="8" />
<hkern u1="u" u2="&#x29;" k="27" />
<hkern u1="v" u2="&#xf0;" k="40" />
<hkern u1="v" u2="&#x7d;" k="52" />
<hkern u1="v" u2="]" k="61" />
<hkern u1="v" u2="&#x2f;" k="72" />
<hkern u1="v" u2="&#x2c;" k="76" />
<hkern u1="v" u2="&#x29;" k="50" />
<hkern u1="v" u2="&#x20;" k="49" />
<hkern u1="w" u2="&#xf0;" k="22" />
<hkern u1="w" u2="&#x7d;" k="44" />
<hkern u1="w" u2="]" k="54" />
<hkern u1="w" u2="&#x2f;" k="50" />
<hkern u1="w" u2="&#x2c;" k="51" />
<hkern u1="w" u2="&#x29;" k="43" />
<hkern u1="w" u2="&#x20;" k="43" />
<hkern u1="x" u2="&#xf0;" k="51" />
<hkern u1="x" u2="&#x7d;" k="36" />
<hkern u1="x" u2="]" k="48" />
<hkern u1="x" u2="\" k="9" />
<hkern u1="y" u2="&#xf0;" k="40" />
<hkern u1="y" u2="&#x7d;" k="49" />
<hkern u1="y" u2="]" k="57" />
<hkern u1="y" u2="&#x2f;" k="70" />
<hkern u1="y" u2="&#x2c;" k="78" />
<hkern u1="y" u2="&#x29;" k="47" />
<hkern u1="y" u2="&#x20;" k="50" />
<hkern u1="z" u2="&#xf0;" k="34" />
<hkern u1="z" u2="&#x7d;" k="43" />
<hkern u1="z" u2="]" k="48" />
<hkern u1="z" u2="\" k="12" />
<hkern u1="&#x7b;" g2="uniFB04" k="42" />
<hkern u1="&#x7b;" g2="uniFB03" k="42" />
<hkern u1="&#x7b;" g2="uniFB02" k="42" />
<hkern u1="&#x7b;" g2="uniFB01" k="42" />
<hkern u1="&#x7b;" u2="&#x178;" k="-6" />
<hkern u1="&#x7b;" u2="&#x153;" k="61" />
<hkern u1="&#x7b;" u2="&#x152;" k="45" />
<hkern u1="&#x7b;" u2="&#xff;" k="43" />
<hkern u1="&#x7b;" u2="&#xfd;" k="43" />
<hkern u1="&#x7b;" u2="&#xfc;" k="52" />
<hkern u1="&#x7b;" u2="&#xfb;" k="52" />
<hkern u1="&#x7b;" u2="&#xfa;" k="52" />
<hkern u1="&#x7b;" u2="&#xf9;" k="52" />
<hkern u1="&#x7b;" u2="&#xf8;" k="61" />
<hkern u1="&#x7b;" u2="&#xf6;" k="61" />
<hkern u1="&#x7b;" u2="&#xf5;" k="61" />
<hkern u1="&#x7b;" u2="&#xf4;" k="61" />
<hkern u1="&#x7b;" u2="&#xf3;" k="61" />
<hkern u1="&#x7b;" u2="&#xf2;" k="61" />
<hkern u1="&#x7b;" u2="&#xf1;" k="49" />
<hkern u1="&#x7b;" u2="&#xf0;" k="56" />
<hkern u1="&#x7b;" u2="&#xef;" k="-12" />
<hkern u1="&#x7b;" u2="&#xec;" k="-26" />
<hkern u1="&#x7b;" u2="&#xeb;" k="61" />
<hkern u1="&#x7b;" u2="&#xea;" k="61" />
<hkern u1="&#x7b;" u2="&#xe9;" k="61" />
<hkern u1="&#x7b;" u2="&#xe8;" k="61" />
<hkern u1="&#x7b;" u2="&#xe7;" k="61" />
<hkern u1="&#x7b;" u2="&#xe6;" k="56" />
<hkern u1="&#x7b;" u2="&#xe5;" k="56" />
<hkern u1="&#x7b;" u2="&#xe4;" k="56" />
<hkern u1="&#x7b;" u2="&#xe3;" k="56" />
<hkern u1="&#x7b;" u2="&#xe2;" k="56" />
<hkern u1="&#x7b;" u2="&#xe1;" k="56" />
<hkern u1="&#x7b;" u2="&#xe0;" k="56" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-6" />
<hkern u1="&#x7b;" u2="&#xd8;" k="45" />
<hkern u1="&#x7b;" u2="&#xd6;" k="45" />
<hkern u1="&#x7b;" u2="&#xd5;" k="45" />
<hkern u1="&#x7b;" u2="&#xd4;" k="45" />
<hkern u1="&#x7b;" u2="&#xd3;" k="45" />
<hkern u1="&#x7b;" u2="&#xd2;" k="45" />
<hkern u1="&#x7b;" u2="&#xcf;" k="-13" />
<hkern u1="&#x7b;" u2="&#xce;" k="-24" />
<hkern u1="&#x7b;" u2="&#xc7;" k="45" />
<hkern u1="&#x7b;" u2="&#xc6;" k="50" />
<hkern u1="&#x7b;" u2="&#xc5;" k="35" />
<hkern u1="&#x7b;" u2="&#xc4;" k="35" />
<hkern u1="&#x7b;" u2="&#xc3;" k="35" />
<hkern u1="&#x7b;" u2="&#xc2;" k="35" />
<hkern u1="&#x7b;" u2="&#xc1;" k="35" />
<hkern u1="&#x7b;" u2="&#xc0;" k="35" />
<hkern u1="&#x7b;" u2="z" k="44" />
<hkern u1="&#x7b;" u2="y" k="43" />
<hkern u1="&#x7b;" u2="x" k="36" />
<hkern u1="&#x7b;" u2="w" k="44" />
<hkern u1="&#x7b;" u2="v" k="52" />
<hkern u1="&#x7b;" u2="u" k="52" />
<hkern u1="&#x7b;" u2="t" k="45" />
<hkern u1="&#x7b;" u2="s" k="57" />
<hkern u1="&#x7b;" u2="r" k="49" />
<hkern u1="&#x7b;" u2="q" k="61" />
<hkern u1="&#x7b;" u2="p" k="49" />
<hkern u1="&#x7b;" u2="o" k="61" />
<hkern u1="&#x7b;" u2="n" k="49" />
<hkern u1="&#x7b;" u2="m" k="49" />
<hkern u1="&#x7b;" u2="j" k="-17" />
<hkern u1="&#x7b;" u2="f" k="42" />
<hkern u1="&#x7b;" u2="e" k="61" />
<hkern u1="&#x7b;" u2="d" k="61" />
<hkern u1="&#x7b;" u2="c" k="61" />
<hkern u1="&#x7b;" u2="a" k="56" />
<hkern u1="&#x7b;" u2="Y" k="-6" />
<hkern u1="&#x7b;" u2="V" k="-6" />
<hkern u1="&#x7b;" u2="S" k="33" />
<hkern u1="&#x7b;" u2="Q" k="45" />
<hkern u1="&#x7b;" u2="O" k="45" />
<hkern u1="&#x7b;" u2="J" k="25" />
<hkern u1="&#x7b;" u2="G" k="45" />
<hkern u1="&#x7b;" u2="C" k="45" />
<hkern u1="&#x7b;" u2="A" k="35" />
<hkern u1="&#x7c;" u2="&#xee;" k="-3" />
<hkern u1="&#xa1;" u2="&#x178;" k="80" />
<hkern u1="&#xa1;" u2="&#xdd;" k="80" />
<hkern u1="&#xa1;" u2="Y" k="80" />
<hkern u1="&#xa1;" u2="W" k="12" />
<hkern u1="&#xa1;" u2="V" k="46" />
<hkern u1="&#xa1;" u2="T" k="140" />
<hkern u1="&#xb7;" u2="l" k="62" />
<hkern u1="&#xbf;" g2="uniFB04" k="34" />
<hkern u1="&#xbf;" g2="uniFB03" k="34" />
<hkern u1="&#xbf;" g2="uniFB02" k="34" />
<hkern u1="&#xbf;" g2="uniFB01" k="34" />
<hkern u1="&#xbf;" u2="&#x178;" k="138" />
<hkern u1="&#xbf;" u2="&#x153;" k="70" />
<hkern u1="&#xbf;" u2="&#x152;" k="56" />
<hkern u1="&#xbf;" u2="&#xff;" k="33" />
<hkern u1="&#xbf;" u2="&#xfe;" k="42" />
<hkern u1="&#xbf;" u2="&#xfd;" k="33" />
<hkern u1="&#xbf;" u2="&#xfc;" k="55" />
<hkern u1="&#xbf;" u2="&#xfb;" k="55" />
<hkern u1="&#xbf;" u2="&#xfa;" k="55" />
<hkern u1="&#xbf;" u2="&#xf9;" k="55" />
<hkern u1="&#xbf;" u2="&#xf8;" k="70" />
<hkern u1="&#xbf;" u2="&#xf6;" k="70" />
<hkern u1="&#xbf;" u2="&#xf5;" k="70" />
<hkern u1="&#xbf;" u2="&#xf4;" k="70" />
<hkern u1="&#xbf;" u2="&#xf3;" k="70" />
<hkern u1="&#xbf;" u2="&#xf2;" k="70" />
<hkern u1="&#xbf;" u2="&#xf1;" k="43" />
<hkern u1="&#xbf;" u2="&#xf0;" k="73" />
<hkern u1="&#xbf;" u2="&#xef;" k="43" />
<hkern u1="&#xbf;" u2="&#xee;" k="43" />
<hkern u1="&#xbf;" u2="&#xed;" k="43" />
<hkern u1="&#xbf;" u2="&#xec;" k="43" />
<hkern u1="&#xbf;" u2="&#xeb;" k="70" />
<hkern u1="&#xbf;" u2="&#xea;" k="70" />
<hkern u1="&#xbf;" u2="&#xe9;" k="70" />
<hkern u1="&#xbf;" u2="&#xe8;" k="70" />
<hkern u1="&#xbf;" u2="&#xe7;" k="70" />
<hkern u1="&#xbf;" u2="&#xe6;" k="61" />
<hkern u1="&#xbf;" u2="&#xe5;" k="61" />
<hkern u1="&#xbf;" u2="&#xe4;" k="61" />
<hkern u1="&#xbf;" u2="&#xe3;" k="61" />
<hkern u1="&#xbf;" u2="&#xe2;" k="61" />
<hkern u1="&#xbf;" u2="&#xe1;" k="61" />
<hkern u1="&#xbf;" u2="&#xe0;" k="61" />
<hkern u1="&#xbf;" u2="&#xdf;" k="42" />
<hkern u1="&#xbf;" u2="&#xde;" k="37" />
<hkern u1="&#xbf;" u2="&#xdd;" k="138" />
<hkern u1="&#xbf;" u2="&#xdc;" k="56" />
<hkern u1="&#xbf;" u2="&#xdb;" k="56" />
<hkern u1="&#xbf;" u2="&#xda;" k="56" />
<hkern u1="&#xbf;" u2="&#xd9;" k="56" />
<hkern u1="&#xbf;" u2="&#xd8;" k="56" />
<hkern u1="&#xbf;" u2="&#xd6;" k="56" />
<hkern u1="&#xbf;" u2="&#xd5;" k="56" />
<hkern u1="&#xbf;" u2="&#xd4;" k="56" />
<hkern u1="&#xbf;" u2="&#xd3;" k="56" />
<hkern u1="&#xbf;" u2="&#xd2;" k="56" />
<hkern u1="&#xbf;" u2="&#xd1;" k="37" />
<hkern u1="&#xbf;" u2="&#xd0;" k="47" />
<hkern u1="&#xbf;" u2="&#xcf;" k="37" />
<hkern u1="&#xbf;" u2="&#xce;" k="37" />
<hkern u1="&#xbf;" u2="&#xcd;" k="37" />
<hkern u1="&#xbf;" u2="&#xcc;" k="37" />
<hkern u1="&#xbf;" u2="&#xcb;" k="37" />
<hkern u1="&#xbf;" u2="&#xca;" k="37" />
<hkern u1="&#xbf;" u2="&#xc9;" k="37" />
<hkern u1="&#xbf;" u2="&#xc8;" k="37" />
<hkern u1="&#xbf;" u2="&#xc7;" k="56" />
<hkern u1="&#xbf;" u2="&#xc6;" k="61" />
<hkern u1="&#xbf;" u2="&#xc5;" k="57" />
<hkern u1="&#xbf;" u2="&#xc4;" k="57" />
<hkern u1="&#xbf;" u2="&#xc3;" k="57" />
<hkern u1="&#xbf;" u2="&#xc2;" k="57" />
<hkern u1="&#xbf;" u2="&#xc1;" k="57" />
<hkern u1="&#xbf;" u2="&#xc0;" k="57" />
<hkern u1="&#xbf;" u2="z" k="45" />
<hkern u1="&#xbf;" u2="y" k="33" />
<hkern u1="&#xbf;" u2="x" k="40" />
<hkern u1="&#xbf;" u2="w" k="44" />
<hkern u1="&#xbf;" u2="v" k="50" />
<hkern u1="&#xbf;" u2="u" k="55" />
<hkern u1="&#xbf;" u2="t" k="44" />
<hkern u1="&#xbf;" u2="s" k="65" />
<hkern u1="&#xbf;" u2="r" k="43" />
<hkern u1="&#xbf;" u2="q" k="68" />
<hkern u1="&#xbf;" u2="p" k="43" />
<hkern u1="&#xbf;" u2="o" k="70" />
<hkern u1="&#xbf;" u2="n" k="43" />
<hkern u1="&#xbf;" u2="m" k="43" />
<hkern u1="&#xbf;" u2="l" k="42" />
<hkern u1="&#xbf;" u2="k" k="42" />
<hkern u1="&#xbf;" u2="j" k="29" />
<hkern u1="&#xbf;" u2="i" k="43" />
<hkern u1="&#xbf;" u2="h" k="42" />
<hkern u1="&#xbf;" u2="f" k="34" />
<hkern u1="&#xbf;" u2="e" k="70" />
<hkern u1="&#xbf;" u2="d" k="68" />
<hkern u1="&#xbf;" u2="c" k="70" />
<hkern u1="&#xbf;" u2="b" k="42" />
<hkern u1="&#xbf;" u2="a" k="61" />
<hkern u1="&#xbf;" u2="Z" k="50" />
<hkern u1="&#xbf;" u2="Y" k="138" />
<hkern u1="&#xbf;" u2="X" k="55" />
<hkern u1="&#xbf;" u2="W" k="84" />
<hkern u1="&#xbf;" u2="V" k="109" />
<hkern u1="&#xbf;" u2="U" k="56" />
<hkern u1="&#xbf;" u2="T" k="187" />
<hkern u1="&#xbf;" u2="S" k="41" />
<hkern u1="&#xbf;" u2="R" k="37" />
<hkern u1="&#xbf;" u2="Q" k="56" />
<hkern u1="&#xbf;" u2="P" k="37" />
<hkern u1="&#xbf;" u2="O" k="56" />
<hkern u1="&#xbf;" u2="N" k="37" />
<hkern u1="&#xbf;" u2="M" k="37" />
<hkern u1="&#xbf;" u2="L" k="37" />
<hkern u1="&#xbf;" u2="K" k="37" />
<hkern u1="&#xbf;" u2="J" k="72" />
<hkern u1="&#xbf;" u2="I" k="37" />
<hkern u1="&#xbf;" u2="H" k="37" />
<hkern u1="&#xbf;" u2="G" k="56" />
<hkern u1="&#xbf;" u2="F" k="37" />
<hkern u1="&#xbf;" u2="E" k="37" />
<hkern u1="&#xbf;" u2="D" k="37" />
<hkern u1="&#xbf;" u2="C" k="56" />
<hkern u1="&#xbf;" u2="B" k="37" />
<hkern u1="&#xbf;" u2="A" k="57" />
<hkern u1="&#xc0;" u2="&#x2122;" k="126" />
<hkern u1="&#xc0;" u2="&#xf0;" k="26" />
<hkern u1="&#xc0;" u2="&#xba;" k="108" />
<hkern u1="&#xc0;" u2="&#xae;" k="8" />
<hkern u1="&#xc0;" u2="&#xaa;" k="96" />
<hkern u1="&#xc0;" u2="&#xa9;" k="8" />
<hkern u1="&#xc0;" u2="&#x7d;" k="30" />
<hkern u1="&#xc0;" u2="]" k="40" />
<hkern u1="&#xc0;" u2="\" k="110" />
<hkern u1="&#xc0;" u2="&#x2a;" k="115" />
<hkern u1="&#xc0;" u2="&#x20;" k="68" />
<hkern u1="&#xc1;" u2="&#x2122;" k="126" />
<hkern u1="&#xc1;" u2="&#xf0;" k="26" />
<hkern u1="&#xc1;" u2="&#xba;" k="108" />
<hkern u1="&#xc1;" u2="&#xae;" k="8" />
<hkern u1="&#xc1;" u2="&#xaa;" k="96" />
<hkern u1="&#xc1;" u2="&#xa9;" k="8" />
<hkern u1="&#xc1;" u2="&#x7d;" k="30" />
<hkern u1="&#xc1;" u2="]" k="40" />
<hkern u1="&#xc1;" u2="\" k="110" />
<hkern u1="&#xc1;" u2="&#x2a;" k="115" />
<hkern u1="&#xc1;" u2="&#x20;" k="68" />
<hkern u1="&#xc2;" u2="&#x2122;" k="126" />
<hkern u1="&#xc2;" u2="&#xf0;" k="26" />
<hkern u1="&#xc2;" u2="&#xba;" k="108" />
<hkern u1="&#xc2;" u2="&#xae;" k="8" />
<hkern u1="&#xc2;" u2="&#xaa;" k="96" />
<hkern u1="&#xc2;" u2="&#xa9;" k="8" />
<hkern u1="&#xc2;" u2="&#x7d;" k="30" />
<hkern u1="&#xc2;" u2="]" k="40" />
<hkern u1="&#xc2;" u2="\" k="110" />
<hkern u1="&#xc2;" u2="&#x2a;" k="115" />
<hkern u1="&#xc2;" u2="&#x20;" k="68" />
<hkern u1="&#xc3;" u2="&#x2122;" k="126" />
<hkern u1="&#xc3;" u2="&#xf0;" k="26" />
<hkern u1="&#xc3;" u2="&#xba;" k="108" />
<hkern u1="&#xc3;" u2="&#xae;" k="8" />
<hkern u1="&#xc3;" u2="&#xaa;" k="96" />
<hkern u1="&#xc3;" u2="&#xa9;" k="8" />
<hkern u1="&#xc3;" u2="&#x7d;" k="30" />
<hkern u1="&#xc3;" u2="]" k="40" />
<hkern u1="&#xc3;" u2="\" k="110" />
<hkern u1="&#xc3;" u2="&#x2a;" k="115" />
<hkern u1="&#xc3;" u2="&#x20;" k="68" />
<hkern u1="&#xc4;" u2="&#x2122;" k="126" />
<hkern u1="&#xc4;" u2="&#xf0;" k="26" />
<hkern u1="&#xc4;" u2="&#xba;" k="108" />
<hkern u1="&#xc4;" u2="&#xae;" k="8" />
<hkern u1="&#xc4;" u2="&#xaa;" k="96" />
<hkern u1="&#xc4;" u2="&#xa9;" k="8" />
<hkern u1="&#xc4;" u2="&#x7d;" k="30" />
<hkern u1="&#xc4;" u2="]" k="40" />
<hkern u1="&#xc4;" u2="\" k="110" />
<hkern u1="&#xc4;" u2="&#x2a;" k="115" />
<hkern u1="&#xc4;" u2="&#x20;" k="68" />
<hkern u1="&#xc5;" u2="&#x2122;" k="126" />
<hkern u1="&#xc5;" u2="&#xf0;" k="26" />
<hkern u1="&#xc5;" u2="&#xba;" k="108" />
<hkern u1="&#xc5;" u2="&#xae;" k="8" />
<hkern u1="&#xc5;" u2="&#xaa;" k="96" />
<hkern u1="&#xc5;" u2="&#xa9;" k="8" />
<hkern u1="&#xc5;" u2="&#x7d;" k="30" />
<hkern u1="&#xc5;" u2="]" k="40" />
<hkern u1="&#xc5;" u2="\" k="110" />
<hkern u1="&#xc5;" u2="&#x2a;" k="115" />
<hkern u1="&#xc5;" u2="&#x20;" k="68" />
<hkern u1="&#xc6;" u2="&#xf0;" k="58" />
<hkern u1="&#xc6;" u2="&#xef;" k="-1" />
<hkern u1="&#xc6;" u2="&#xee;" k="-6" />
<hkern u1="&#xc6;" u2="&#xed;" k="11" />
<hkern u1="&#xc6;" u2="&#xec;" k="-4" />
<hkern u1="&#xc6;" u2="&#xdf;" k="35" />
<hkern u1="&#xc7;" u2="&#x2122;" k="-5" />
<hkern u1="&#xc7;" u2="&#xf0;" k="17" />
<hkern u1="&#xc7;" u2="&#xef;" k="-20" />
<hkern u1="&#xc7;" u2="&#xee;" k="-10" />
<hkern u1="&#xc7;" u2="&#x2a;" k="-3" />
<hkern u1="&#xc8;" u2="&#xf0;" k="58" />
<hkern u1="&#xc8;" u2="&#xef;" k="-1" />
<hkern u1="&#xc8;" u2="&#xee;" k="-6" />
<hkern u1="&#xc8;" u2="&#xed;" k="11" />
<hkern u1="&#xc8;" u2="&#xec;" k="-4" />
<hkern u1="&#xc8;" u2="&#xdf;" k="35" />
<hkern u1="&#xc9;" u2="&#xf0;" k="58" />
<hkern u1="&#xc9;" u2="&#xef;" k="-1" />
<hkern u1="&#xc9;" u2="&#xee;" k="-6" />
<hkern u1="&#xc9;" u2="&#xed;" k="11" />
<hkern u1="&#xc9;" u2="&#xec;" k="-4" />
<hkern u1="&#xc9;" u2="&#xdf;" k="35" />
<hkern u1="&#xca;" u2="&#xf0;" k="58" />
<hkern u1="&#xca;" u2="&#xef;" k="-1" />
<hkern u1="&#xca;" u2="&#xee;" k="-6" />
<hkern u1="&#xca;" u2="&#xed;" k="11" />
<hkern u1="&#xca;" u2="&#xec;" k="-4" />
<hkern u1="&#xca;" u2="&#xdf;" k="35" />
<hkern u1="&#xcb;" u2="&#xf0;" k="58" />
<hkern u1="&#xcb;" u2="&#xef;" k="-1" />
<hkern u1="&#xcb;" u2="&#xee;" k="-6" />
<hkern u1="&#xcb;" u2="&#xed;" k="11" />
<hkern u1="&#xcb;" u2="&#xec;" k="-4" />
<hkern u1="&#xcb;" u2="&#xdf;" k="35" />
<hkern u1="&#xcc;" u2="&#xf0;" k="35" />
<hkern u1="&#xcc;" u2="&#xee;" k="12" />
<hkern u1="&#xcd;" u2="&#xf0;" k="35" />
<hkern u1="&#xcd;" u2="&#xee;" k="12" />
<hkern u1="&#xce;" u2="&#xf0;" k="35" />
<hkern u1="&#xce;" u2="&#xee;" k="12" />
<hkern u1="&#xce;" u2="&#x7d;" k="-28" />
<hkern u1="&#xce;" u2="]" k="-30" />
<hkern u1="&#xce;" u2="&#x29;" k="-7" />
<hkern u1="&#xcf;" u2="&#xf0;" k="35" />
<hkern u1="&#xcf;" u2="&#xee;" k="12" />
<hkern u1="&#xcf;" u2="&#x7d;" k="-15" />
<hkern u1="&#xcf;" u2="]" k="-17" />
<hkern u1="&#xd0;" u2="&#xf0;" k="20" />
<hkern u1="&#xd0;" u2="&#xd0;" k="5" />
<hkern u1="&#xd0;" u2="&#x7d;" k="45" />
<hkern u1="&#xd0;" u2="]" k="54" />
<hkern u1="&#xd0;" u2="&#x2f;" k="11" />
<hkern u1="&#xd0;" u2="&#x2c;" k="69" />
<hkern u1="&#xd0;" u2="&#x29;" k="44" />
<hkern u1="&#xd1;" u2="&#xf0;" k="35" />
<hkern u1="&#xd1;" u2="&#xee;" k="12" />
<hkern u1="&#xd2;" u2="&#xf0;" k="19" />
<hkern u1="&#xd2;" u2="&#xd0;" k="5" />
<hkern u1="&#xd2;" u2="&#x7d;" k="43" />
<hkern u1="&#xd2;" u2="]" k="53" />
<hkern u1="&#xd2;" u2="&#x2f;" k="10" />
<hkern u1="&#xd2;" u2="&#x2c;" k="69" />
<hkern u1="&#xd2;" u2="&#x29;" k="43" />
<hkern u1="&#xd3;" u2="&#xf0;" k="19" />
<hkern u1="&#xd3;" u2="&#xd0;" k="5" />
<hkern u1="&#xd3;" u2="&#x7d;" k="43" />
<hkern u1="&#xd3;" u2="]" k="53" />
<hkern u1="&#xd3;" u2="&#x2f;" k="10" />
<hkern u1="&#xd3;" u2="&#x2c;" k="69" />
<hkern u1="&#xd3;" u2="&#x29;" k="43" />
<hkern u1="&#xd4;" u2="&#xf0;" k="19" />
<hkern u1="&#xd4;" u2="&#xd0;" k="5" />
<hkern u1="&#xd4;" u2="&#x7d;" k="43" />
<hkern u1="&#xd4;" u2="]" k="53" />
<hkern u1="&#xd4;" u2="&#x2f;" k="10" />
<hkern u1="&#xd4;" u2="&#x2c;" k="69" />
<hkern u1="&#xd4;" u2="&#x29;" k="43" />
<hkern u1="&#xd5;" u2="&#xf0;" k="19" />
<hkern u1="&#xd5;" u2="&#xd0;" k="5" />
<hkern u1="&#xd5;" u2="&#x7d;" k="43" />
<hkern u1="&#xd5;" u2="]" k="53" />
<hkern u1="&#xd5;" u2="&#x2f;" k="10" />
<hkern u1="&#xd5;" u2="&#x2c;" k="69" />
<hkern u1="&#xd5;" u2="&#x29;" k="43" />
<hkern u1="&#xd6;" u2="&#xf0;" k="19" />
<hkern u1="&#xd6;" u2="&#xd0;" k="5" />
<hkern u1="&#xd6;" u2="&#x7d;" k="43" />
<hkern u1="&#xd6;" u2="]" k="53" />
<hkern u1="&#xd6;" u2="&#x2f;" k="10" />
<hkern u1="&#xd6;" u2="&#x2c;" k="69" />
<hkern u1="&#xd6;" u2="&#x29;" k="43" />
<hkern u1="&#xd8;" u2="&#xf0;" k="19" />
<hkern u1="&#xd8;" u2="&#xd0;" k="5" />
<hkern u1="&#xd8;" u2="&#x7d;" k="43" />
<hkern u1="&#xd8;" u2="]" k="53" />
<hkern u1="&#xd8;" u2="&#x2f;" k="10" />
<hkern u1="&#xd8;" u2="&#x2c;" k="69" />
<hkern u1="&#xd8;" u2="&#x29;" k="43" />
<hkern u1="&#xd9;" u2="&#xf0;" k="51" />
<hkern u1="&#xd9;" u2="&#xee;" k="16" />
<hkern u1="&#xd9;" u2="&#xed;" k="30" />
<hkern u1="&#xd9;" u2="&#xdf;" k="30" />
<hkern u1="&#xd9;" u2="&#x2f;" k="11" />
<hkern u1="&#xd9;" u2="&#x2c;" k="58" />
<hkern u1="&#xda;" u2="&#xf0;" k="51" />
<hkern u1="&#xda;" u2="&#xee;" k="16" />
<hkern u1="&#xda;" u2="&#xed;" k="30" />
<hkern u1="&#xda;" u2="&#xdf;" k="30" />
<hkern u1="&#xda;" u2="&#x2f;" k="11" />
<hkern u1="&#xda;" u2="&#x2c;" k="58" />
<hkern u1="&#xdb;" u2="&#xf0;" k="51" />
<hkern u1="&#xdb;" u2="&#xee;" k="16" />
<hkern u1="&#xdb;" u2="&#xed;" k="30" />
<hkern u1="&#xdb;" u2="&#xdf;" k="30" />
<hkern u1="&#xdb;" u2="&#x2f;" k="11" />
<hkern u1="&#xdb;" u2="&#x2c;" k="58" />
<hkern u1="&#xdc;" u2="&#xf0;" k="51" />
<hkern u1="&#xdc;" u2="&#xee;" k="16" />
<hkern u1="&#xdc;" u2="&#xed;" k="30" />
<hkern u1="&#xdc;" u2="&#xdf;" k="30" />
<hkern u1="&#xdc;" u2="&#x2f;" k="11" />
<hkern u1="&#xdc;" u2="&#x2c;" k="58" />
<hkern u1="&#xdd;" g2="uniFB02" k="103" />
<hkern u1="&#xdd;" u2="&#x2122;" k="-14" />
<hkern u1="&#xdd;" u2="&#xf9;" k="159" />
<hkern u1="&#xdd;" u2="&#xf6;" k="210" />
<hkern u1="&#xdd;" u2="&#xf2;" k="199" />
<hkern u1="&#xdd;" u2="&#xf0;" k="145" />
<hkern u1="&#xdd;" u2="&#xef;" k="-16" />
<hkern u1="&#xdd;" u2="&#xed;" k="56" />
<hkern u1="&#xdd;" u2="&#xec;" k="-36" />
<hkern u1="&#xdd;" u2="&#xeb;" k="207" />
<hkern u1="&#xdd;" u2="&#xe8;" k="196" />
<hkern u1="&#xdd;" u2="&#xe4;" k="198" />
<hkern u1="&#xdd;" u2="&#xe3;" k="214" />
<hkern u1="&#xdd;" u2="&#xe0;" k="186" />
<hkern u1="&#xdd;" u2="&#xdf;" k="88" />
<hkern u1="&#xdd;" u2="&#xae;" k="57" />
<hkern u1="&#xdd;" u2="&#xa9;" k="58" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-6" />
<hkern u1="&#xdd;" u2="]" k="-8" />
<hkern u1="&#xdd;" u2="\" k="-4" />
<hkern u1="&#xdd;" u2="&#x40;" k="90" />
<hkern u1="&#xdd;" u2="&#x3b;" k="65" />
<hkern u1="&#xdd;" u2="&#x3a;" k="69" />
<hkern u1="&#xdd;" u2="&#x2f;" k="139" />
<hkern u1="&#xdd;" u2="&#x2c;" k="171" />
<hkern u1="&#xdd;" u2="&#x29;" k="-6" />
<hkern u1="&#xdd;" u2="&#x26;" k="74" />
<hkern u1="&#xdd;" u2="&#x20;" k="77" />
<hkern u1="&#xde;" u2="&#x2122;" k="25" />
<hkern u1="&#xde;" u2="&#x2026;" k="90" />
<hkern u1="&#xde;" u2="&#x201e;" k="70" />
<hkern u1="&#xde;" u2="&#x201a;" k="70" />
<hkern u1="&#xde;" u2="&#x178;" k="83" />
<hkern u1="&#xde;" u2="&#xfe;" k="9" />
<hkern u1="&#xde;" u2="&#xfc;" k="8" />
<hkern u1="&#xde;" u2="&#xfb;" k="8" />
<hkern u1="&#xde;" u2="&#xfa;" k="8" />
<hkern u1="&#xde;" u2="&#xf9;" k="8" />
<hkern u1="&#xde;" u2="&#xf1;" k="10" />
<hkern u1="&#xde;" u2="&#xef;" k="9" />
<hkern u1="&#xde;" u2="&#xee;" k="9" />
<hkern u1="&#xde;" u2="&#xed;" k="9" />
<hkern u1="&#xde;" u2="&#xec;" k="9" />
<hkern u1="&#xde;" u2="&#xe6;" k="10" />
<hkern u1="&#xde;" u2="&#xe5;" k="10" />
<hkern u1="&#xde;" u2="&#xe4;" k="10" />
<hkern u1="&#xde;" u2="&#xe3;" k="10" />
<hkern u1="&#xde;" u2="&#xe2;" k="10" />
<hkern u1="&#xde;" u2="&#xe1;" k="10" />
<hkern u1="&#xde;" u2="&#xe0;" k="10" />
<hkern u1="&#xde;" u2="&#xdf;" k="9" />
<hkern u1="&#xde;" u2="&#xdd;" k="83" />
<hkern u1="&#xde;" u2="&#xc5;" k="40" />
<hkern u1="&#xde;" u2="&#xc4;" k="40" />
<hkern u1="&#xde;" u2="&#xc3;" k="40" />
<hkern u1="&#xde;" u2="&#xc2;" k="40" />
<hkern u1="&#xde;" u2="&#xc1;" k="40" />
<hkern u1="&#xde;" u2="&#xc0;" k="40" />
<hkern u1="&#xde;" u2="&#x7d;" k="28" />
<hkern u1="&#xde;" u2="z" k="7" />
<hkern u1="&#xde;" u2="x" k="11" />
<hkern u1="&#xde;" u2="u" k="8" />
<hkern u1="&#xde;" u2="r" k="10" />
<hkern u1="&#xde;" u2="p" k="10" />
<hkern u1="&#xde;" u2="n" k="10" />
<hkern u1="&#xde;" u2="m" k="10" />
<hkern u1="&#xde;" u2="l" k="9" />
<hkern u1="&#xde;" u2="k" k="9" />
<hkern u1="&#xde;" u2="j" k="9" />
<hkern u1="&#xde;" u2="i" k="9" />
<hkern u1="&#xde;" u2="h" k="9" />
<hkern u1="&#xde;" u2="g" k="10" />
<hkern u1="&#xde;" u2="b" k="9" />
<hkern u1="&#xde;" u2="a" k="10" />
<hkern u1="&#xde;" u2="]" k="47" />
<hkern u1="&#xde;" u2="Z" k="56" />
<hkern u1="&#xde;" u2="Y" k="83" />
<hkern u1="&#xde;" u2="X" k="83" />
<hkern u1="&#xde;" u2="W" k="17" />
<hkern u1="&#xde;" u2="V" k="37" />
<hkern u1="&#xde;" u2="T" k="93" />
<hkern u1="&#xde;" u2="S" k="8" />
<hkern u1="&#xde;" u2="J" k="100" />
<hkern u1="&#xde;" u2="A" k="40" />
<hkern u1="&#xde;" u2="&#x3f;" k="29" />
<hkern u1="&#xde;" u2="&#x2f;" k="37" />
<hkern u1="&#xde;" u2="&#x2e;" k="90" />
<hkern u1="&#xde;" u2="&#x2c;" k="75" />
<hkern u1="&#xde;" u2="&#x29;" k="37" />
<hkern u1="&#xdf;" g2="uniFB04" k="17" />
<hkern u1="&#xdf;" g2="uniFB03" k="17" />
<hkern u1="&#xdf;" g2="uniFB02" k="17" />
<hkern u1="&#xdf;" g2="uniFB01" k="17" />
<hkern u1="&#xdf;" u2="&#xff;" k="36" />
<hkern u1="&#xdf;" u2="&#xfd;" k="36" />
<hkern u1="&#xdf;" u2="&#x7d;" k="31" />
<hkern u1="&#xdf;" u2="y" k="36" />
<hkern u1="&#xdf;" u2="x" k="13" />
<hkern u1="&#xdf;" u2="w" k="22" />
<hkern u1="&#xdf;" u2="v" k="33" />
<hkern u1="&#xdf;" u2="t" k="18" />
<hkern u1="&#xdf;" u2="s" k="3" />
<hkern u1="&#xdf;" u2="g" k="4" />
<hkern u1="&#xdf;" u2="f" k="17" />
<hkern u1="&#xdf;" u2="]" k="44" />
<hkern u1="&#xdf;" u2="\" k="9" />
<hkern u1="&#xe0;" u2="&#x2122;" k="45" />
<hkern u1="&#xe0;" u2="&#xba;" k="9" />
<hkern u1="&#xe0;" u2="&#xaa;" k="6" />
<hkern u1="&#xe0;" u2="&#x7d;" k="55" />
<hkern u1="&#xe0;" u2="]" k="63" />
<hkern u1="&#xe0;" u2="\" k="78" />
<hkern u1="&#xe0;" u2="&#x3f;" k="44" />
<hkern u1="&#xe0;" u2="&#x2a;" k="43" />
<hkern u1="&#xe0;" u2="&#x29;" k="37" />
<hkern u1="&#xe1;" u2="&#x2122;" k="45" />
<hkern u1="&#xe1;" u2="&#xba;" k="9" />
<hkern u1="&#xe1;" u2="&#xaa;" k="6" />
<hkern u1="&#xe1;" u2="&#x7d;" k="55" />
<hkern u1="&#xe1;" u2="]" k="63" />
<hkern u1="&#xe1;" u2="\" k="78" />
<hkern u1="&#xe1;" u2="&#x3f;" k="44" />
<hkern u1="&#xe1;" u2="&#x2a;" k="43" />
<hkern u1="&#xe1;" u2="&#x29;" k="37" />
<hkern u1="&#xe2;" u2="&#x2122;" k="45" />
<hkern u1="&#xe2;" u2="&#xba;" k="9" />
<hkern u1="&#xe2;" u2="&#xaa;" k="6" />
<hkern u1="&#xe2;" u2="&#x7d;" k="55" />
<hkern u1="&#xe2;" u2="]" k="63" />
<hkern u1="&#xe2;" u2="\" k="78" />
<hkern u1="&#xe2;" u2="&#x3f;" k="44" />
<hkern u1="&#xe2;" u2="&#x2a;" k="43" />
<hkern u1="&#xe2;" u2="&#x29;" k="37" />
<hkern u1="&#xe3;" u2="&#x2122;" k="45" />
<hkern u1="&#xe3;" u2="&#xba;" k="9" />
<hkern u1="&#xe3;" u2="&#xaa;" k="6" />
<hkern u1="&#xe3;" u2="&#x7d;" k="55" />
<hkern u1="&#xe3;" u2="]" k="63" />
<hkern u1="&#xe3;" u2="\" k="78" />
<hkern u1="&#xe3;" u2="&#x3f;" k="44" />
<hkern u1="&#xe3;" u2="&#x2a;" k="43" />
<hkern u1="&#xe3;" u2="&#x29;" k="37" />
<hkern u1="&#xe4;" u2="&#x2122;" k="45" />
<hkern u1="&#xe4;" u2="&#xba;" k="9" />
<hkern u1="&#xe4;" u2="&#xaa;" k="6" />
<hkern u1="&#xe4;" u2="&#x7d;" k="55" />
<hkern u1="&#xe4;" u2="]" k="63" />
<hkern u1="&#xe4;" u2="\" k="78" />
<hkern u1="&#xe4;" u2="&#x3f;" k="44" />
<hkern u1="&#xe4;" u2="&#x2a;" k="43" />
<hkern u1="&#xe4;" u2="&#x29;" k="37" />
<hkern u1="&#xe5;" u2="&#x2122;" k="45" />
<hkern u1="&#xe5;" u2="&#xba;" k="9" />
<hkern u1="&#xe5;" u2="&#xaa;" k="6" />
<hkern u1="&#xe5;" u2="&#x7d;" k="55" />
<hkern u1="&#xe5;" u2="]" k="63" />
<hkern u1="&#xe5;" u2="\" k="78" />
<hkern u1="&#xe5;" u2="&#x3f;" k="44" />
<hkern u1="&#xe5;" u2="&#x2a;" k="43" />
<hkern u1="&#xe5;" u2="&#x29;" k="37" />
<hkern u1="&#xe6;" u2="&#x2122;" k="49" />
<hkern u1="&#xe6;" u2="&#xba;" k="29" />
<hkern u1="&#xe6;" u2="&#x7d;" k="59" />
<hkern u1="&#xe6;" u2="]" k="67" />
<hkern u1="&#xe6;" u2="\" k="77" />
<hkern u1="&#xe6;" u2="&#x3f;" k="49" />
<hkern u1="&#xe6;" u2="&#x2a;" k="51" />
<hkern u1="&#xe6;" u2="&#x29;" k="48" />
<hkern u1="&#xe7;" u2="&#x2122;" k="34" />
<hkern u1="&#xe7;" u2="&#xf0;" k="25" />
<hkern u1="&#xe7;" u2="&#x7d;" k="54" />
<hkern u1="&#xe7;" u2="]" k="60" />
<hkern u1="&#xe7;" u2="\" k="56" />
<hkern u1="&#xe7;" u2="&#x3f;" k="9" />
<hkern u1="&#xe7;" u2="&#x2a;" k="23" />
<hkern u1="&#xe7;" u2="&#x29;" k="35" />
<hkern u1="&#xe8;" u2="&#x2122;" k="49" />
<hkern u1="&#xe8;" u2="&#xba;" k="29" />
<hkern u1="&#xe8;" u2="&#x7d;" k="59" />
<hkern u1="&#xe8;" u2="]" k="67" />
<hkern u1="&#xe8;" u2="\" k="77" />
<hkern u1="&#xe8;" u2="&#x3f;" k="49" />
<hkern u1="&#xe8;" u2="&#x2a;" k="51" />
<hkern u1="&#xe8;" u2="&#x29;" k="48" />
<hkern u1="&#xe9;" u2="&#x2122;" k="49" />
<hkern u1="&#xe9;" u2="&#xba;" k="29" />
<hkern u1="&#xe9;" u2="&#x7d;" k="59" />
<hkern u1="&#xe9;" u2="]" k="67" />
<hkern u1="&#xe9;" u2="\" k="77" />
<hkern u1="&#xe9;" u2="&#x3f;" k="49" />
<hkern u1="&#xe9;" u2="&#x2a;" k="51" />
<hkern u1="&#xe9;" u2="&#x29;" k="48" />
<hkern u1="&#xea;" u2="&#x2122;" k="49" />
<hkern u1="&#xea;" u2="&#xba;" k="29" />
<hkern u1="&#xea;" u2="&#x7d;" k="59" />
<hkern u1="&#xea;" u2="]" k="67" />
<hkern u1="&#xea;" u2="\" k="77" />
<hkern u1="&#xea;" u2="&#x3f;" k="49" />
<hkern u1="&#xea;" u2="&#x2a;" k="51" />
<hkern u1="&#xea;" u2="&#x29;" k="48" />
<hkern u1="&#xeb;" u2="&#x2122;" k="49" />
<hkern u1="&#xeb;" u2="&#xba;" k="29" />
<hkern u1="&#xeb;" u2="&#x7d;" k="59" />
<hkern u1="&#xeb;" u2="]" k="67" />
<hkern u1="&#xeb;" u2="\" k="77" />
<hkern u1="&#xeb;" u2="&#x3f;" k="49" />
<hkern u1="&#xeb;" u2="&#x2a;" k="51" />
<hkern u1="&#xeb;" u2="&#x29;" k="48" />
<hkern u1="&#xec;" u2="&#xef;" k="-5" />
<hkern u1="&#xed;" u2="&#x2122;" k="-33" />
<hkern u1="&#xed;" u2="&#x201d;" k="-23" />
<hkern u1="&#xed;" u2="&#x201c;" k="-11" />
<hkern u1="&#xed;" u2="&#x2019;" k="-23" />
<hkern u1="&#xed;" u2="&#x2018;" k="-11" />
<hkern u1="&#xed;" u2="&#xfe;" k="-16" />
<hkern u1="&#xed;" u2="&#xef;" k="-5" />
<hkern u1="&#xed;" u2="&#xdf;" k="-16" />
<hkern u1="&#xed;" u2="&#xaa;" k="-6" />
<hkern u1="&#xed;" u2="&#x7d;" k="-37" />
<hkern u1="&#xed;" u2="&#x7c;" k="-11" />
<hkern u1="&#xed;" u2="l" k="-16" />
<hkern u1="&#xed;" u2="k" k="-16" />
<hkern u1="&#xed;" u2="h" k="-16" />
<hkern u1="&#xed;" u2="b" k="-16" />
<hkern u1="&#xed;" u2="]" k="-39" />
<hkern u1="&#xed;" u2="\" k="-35" />
<hkern u1="&#xed;" u2="&#x2a;" k="-3" />
<hkern u1="&#xed;" u2="&#x29;" k="-35" />
<hkern u1="&#xed;" u2="&#x27;" k="-25" />
<hkern u1="&#xed;" u2="&#x22;" k="-25" />
<hkern u1="&#xed;" u2="&#x21;" k="-10" />
<hkern u1="&#xee;" u2="&#x2122;" k="-12" />
<hkern u1="&#xee;" u2="&#x201d;" k="-8" />
<hkern u1="&#xee;" u2="&#x201c;" k="-30" />
<hkern u1="&#xee;" u2="&#x2019;" k="-8" />
<hkern u1="&#xee;" u2="&#x2018;" k="-30" />
<hkern u1="&#xee;" u2="&#xfe;" k="-13" />
<hkern u1="&#xee;" u2="&#xef;" k="-5" />
<hkern u1="&#xee;" u2="&#xdf;" k="-13" />
<hkern u1="&#xee;" u2="&#xba;" k="-33" />
<hkern u1="&#xee;" u2="&#xaa;" k="-7" />
<hkern u1="&#xee;" u2="&#x7c;" k="-8" />
<hkern u1="&#xee;" u2="l" k="-13" />
<hkern u1="&#xee;" u2="k" k="-13" />
<hkern u1="&#xee;" u2="h" k="-13" />
<hkern u1="&#xee;" u2="b" k="-13" />
<hkern u1="&#xee;" u2="\" k="-4" />
<hkern u1="&#xee;" u2="&#x3f;" k="-9" />
<hkern u1="&#xee;" u2="&#x2a;" k="-10" />
<hkern u1="&#xee;" u2="&#x29;" k="-9" />
<hkern u1="&#xee;" u2="&#x27;" k="-26" />
<hkern u1="&#xee;" u2="&#x22;" k="-26" />
<hkern u1="&#xee;" u2="&#x21;" k="-9" />
<hkern u1="&#xef;" u2="&#x2122;" k="-25" />
<hkern u1="&#xef;" u2="&#x201d;" k="-7" />
<hkern u1="&#xef;" u2="&#x201c;" k="-18" />
<hkern u1="&#xef;" u2="&#x2019;" k="-7" />
<hkern u1="&#xef;" u2="&#x2018;" k="-18" />
<hkern u1="&#xef;" u2="&#xfe;" k="-4" />
<hkern u1="&#xef;" u2="&#xef;" k="-5" />
<hkern u1="&#xef;" u2="&#xee;" k="-10" />
<hkern u1="&#xef;" u2="&#xed;" k="-10" />
<hkern u1="&#xef;" u2="&#xec;" k="-10" />
<hkern u1="&#xef;" u2="&#xdf;" k="-4" />
<hkern u1="&#xef;" u2="&#xba;" k="-18" />
<hkern u1="&#xef;" u2="&#xaa;" k="-9" />
<hkern u1="&#xef;" u2="&#x7d;" k="-17" />
<hkern u1="&#xef;" u2="l" k="-4" />
<hkern u1="&#xef;" u2="k" k="-4" />
<hkern u1="&#xef;" u2="j" k="-10" />
<hkern u1="&#xef;" u2="i" k="-10" />
<hkern u1="&#xef;" u2="h" k="-4" />
<hkern u1="&#xef;" u2="b" k="-4" />
<hkern u1="&#xef;" u2="]" k="-17" />
<hkern u1="&#xef;" u2="\" k="-9" />
<hkern u1="&#xef;" u2="&#x3f;" k="-22" />
<hkern u1="&#xef;" u2="&#x2a;" k="-23" />
<hkern u1="&#xef;" u2="&#x29;" k="-16" />
<hkern u1="&#xef;" u2="&#x27;" k="-16" />
<hkern u1="&#xef;" u2="&#x22;" k="-16" />
<hkern u1="&#xf0;" g2="uniFB04" k="3" />
<hkern u1="&#xf0;" g2="uniFB03" k="3" />
<hkern u1="&#xf0;" g2="uniFB02" k="3" />
<hkern u1="&#xf0;" g2="uniFB01" k="3" />
<hkern u1="&#xf0;" u2="&#x2122;" k="19" />
<hkern u1="&#xf0;" u2="&#xff;" k="5" />
<hkern u1="&#xf0;" u2="&#xfd;" k="5" />
<hkern u1="&#xf0;" u2="&#x7d;" k="47" />
<hkern u1="&#xf0;" u2="z" k="14" />
<hkern u1="&#xf0;" u2="y" k="5" />
<hkern u1="&#xf0;" u2="x" k="28" />
<hkern u1="&#xf0;" u2="v" k="4" />
<hkern u1="&#xf0;" u2="f" k="3" />
<hkern u1="&#xf0;" u2="]" k="55" />
<hkern u1="&#xf0;" u2="\" k="39" />
<hkern u1="&#xf0;" u2="&#x3f;" k="25" />
<hkern u1="&#xf0;" u2="&#x2f;" k="11" />
<hkern u1="&#xf0;" u2="&#x29;" k="45" />
<hkern u1="&#xf1;" u2="&#x2122;" k="46" />
<hkern u1="&#xf1;" u2="&#xba;" k="9" />
<hkern u1="&#xf1;" u2="&#x7d;" k="54" />
<hkern u1="&#xf1;" u2="]" k="63" />
<hkern u1="&#xf1;" u2="\" k="80" />
<hkern u1="&#xf1;" u2="&#x3f;" k="44" />
<hkern u1="&#xf1;" u2="&#x2a;" k="45" />
<hkern u1="&#xf1;" u2="&#x29;" k="38" />
<hkern u1="&#xf2;" u2="&#x2122;" k="53" />
<hkern u1="&#xf2;" u2="&#xba;" k="35" />
<hkern u1="&#xf2;" u2="&#xaa;" k="8" />
<hkern u1="&#xf2;" u2="&#x7d;" k="62" />
<hkern u1="&#xf2;" u2="]" k="73" />
<hkern u1="&#xf2;" u2="\" k="87" />
<hkern u1="&#xf2;" u2="&#x3f;" k="58" />
<hkern u1="&#xf2;" u2="&#x2a;" k="57" />
<hkern u1="&#xf2;" u2="&#x29;" k="62" />
<hkern u1="&#xf3;" u2="&#x2122;" k="53" />
<hkern u1="&#xf3;" u2="&#xba;" k="35" />
<hkern u1="&#xf3;" u2="&#xaa;" k="8" />
<hkern u1="&#xf3;" u2="&#x7d;" k="62" />
<hkern u1="&#xf3;" u2="]" k="73" />
<hkern u1="&#xf3;" u2="\" k="87" />
<hkern u1="&#xf3;" u2="&#x3f;" k="58" />
<hkern u1="&#xf3;" u2="&#x2a;" k="57" />
<hkern u1="&#xf3;" u2="&#x29;" k="62" />
<hkern u1="&#xf4;" u2="&#x2122;" k="53" />
<hkern u1="&#xf4;" u2="&#xba;" k="35" />
<hkern u1="&#xf4;" u2="&#xaa;" k="8" />
<hkern u1="&#xf4;" u2="&#x7d;" k="62" />
<hkern u1="&#xf4;" u2="]" k="73" />
<hkern u1="&#xf4;" u2="\" k="87" />
<hkern u1="&#xf4;" u2="&#x3f;" k="58" />
<hkern u1="&#xf4;" u2="&#x2a;" k="57" />
<hkern u1="&#xf4;" u2="&#x29;" k="62" />
<hkern u1="&#xf5;" u2="&#x2122;" k="53" />
<hkern u1="&#xf5;" u2="&#xba;" k="35" />
<hkern u1="&#xf5;" u2="&#xaa;" k="8" />
<hkern u1="&#xf5;" u2="&#x7d;" k="62" />
<hkern u1="&#xf5;" u2="]" k="73" />
<hkern u1="&#xf5;" u2="\" k="87" />
<hkern u1="&#xf5;" u2="&#x3f;" k="58" />
<hkern u1="&#xf5;" u2="&#x2a;" k="57" />
<hkern u1="&#xf5;" u2="&#x29;" k="62" />
<hkern u1="&#xf6;" u2="&#x2122;" k="53" />
<hkern u1="&#xf6;" u2="&#xba;" k="35" />
<hkern u1="&#xf6;" u2="&#xaa;" k="8" />
<hkern u1="&#xf6;" u2="&#x7d;" k="62" />
<hkern u1="&#xf6;" u2="]" k="73" />
<hkern u1="&#xf6;" u2="\" k="87" />
<hkern u1="&#xf6;" u2="&#x3f;" k="58" />
<hkern u1="&#xf6;" u2="&#x2a;" k="57" />
<hkern u1="&#xf6;" u2="&#x29;" k="62" />
<hkern u1="&#xf8;" u2="&#x2122;" k="53" />
<hkern u1="&#xf8;" u2="&#x201c;" k="38" />
<hkern u1="&#xf8;" u2="&#x2018;" k="38" />
<hkern u1="&#xf8;" u2="&#xba;" k="23" />
<hkern u1="&#xf8;" u2="&#xaa;" k="8" />
<hkern u1="&#xf8;" u2="&#x7d;" k="62" />
<hkern u1="&#xf8;" u2="]" k="73" />
<hkern u1="&#xf8;" u2="\" k="87" />
<hkern u1="&#xf8;" u2="&#x3f;" k="58" />
<hkern u1="&#xf8;" u2="&#x2a;" k="57" />
<hkern u1="&#xf8;" u2="&#x29;" k="62" />
<hkern u1="&#xf8;" u2="&#x27;" k="32" />
<hkern u1="&#xf8;" u2="&#x22;" k="32" />
<hkern u1="&#xf9;" u2="&#x2122;" k="29" />
<hkern u1="&#xf9;" u2="&#x7d;" k="49" />
<hkern u1="&#xf9;" u2="]" k="59" />
<hkern u1="&#xf9;" u2="\" k="53" />
<hkern u1="&#xf9;" u2="&#x3f;" k="8" />
<hkern u1="&#xf9;" u2="&#x29;" k="27" />
<hkern u1="&#xfa;" u2="&#x2122;" k="29" />
<hkern u1="&#xfa;" u2="&#x7d;" k="49" />
<hkern u1="&#xfa;" u2="]" k="59" />
<hkern u1="&#xfa;" u2="\" k="53" />
<hkern u1="&#xfa;" u2="&#x3f;" k="8" />
<hkern u1="&#xfa;" u2="&#x29;" k="27" />
<hkern u1="&#xfb;" u2="&#x2122;" k="29" />
<hkern u1="&#xfb;" u2="&#x7d;" k="49" />
<hkern u1="&#xfb;" u2="]" k="59" />
<hkern u1="&#xfb;" u2="\" k="53" />
<hkern u1="&#xfb;" u2="&#x3f;" k="8" />
<hkern u1="&#xfb;" u2="&#x29;" k="27" />
<hkern u1="&#xfc;" u2="&#x2122;" k="29" />
<hkern u1="&#xfc;" u2="&#x7d;" k="49" />
<hkern u1="&#xfc;" u2="]" k="59" />
<hkern u1="&#xfc;" u2="\" k="53" />
<hkern u1="&#xfc;" u2="&#x3f;" k="8" />
<hkern u1="&#xfc;" u2="&#x29;" k="27" />
<hkern u1="&#xfd;" u2="&#xf0;" k="40" />
<hkern u1="&#xfd;" u2="&#x7d;" k="49" />
<hkern u1="&#xfd;" u2="]" k="57" />
<hkern u1="&#xfd;" u2="&#x2f;" k="70" />
<hkern u1="&#xfd;" u2="&#x2c;" k="78" />
<hkern u1="&#xfd;" u2="&#x29;" k="47" />
<hkern u1="&#xfd;" u2="&#x20;" k="50" />
<hkern u1="&#xfe;" u2="&#x2122;" k="54" />
<hkern u1="&#xfe;" u2="&#xba;" k="35" />
<hkern u1="&#xfe;" u2="&#xaa;" k="27" />
<hkern u1="&#xfe;" u2="&#x7d;" k="60" />
<hkern u1="&#xfe;" u2="]" k="72" />
<hkern u1="&#xfe;" u2="\" k="84" />
<hkern u1="&#xfe;" u2="&#x3f;" k="61" />
<hkern u1="&#xfe;" u2="&#x2f;" k="8" />
<hkern u1="&#xfe;" u2="&#x2a;" k="60" />
<hkern u1="&#xfe;" u2="&#x29;" k="61" />
<hkern u1="&#xff;" u2="&#xf0;" k="40" />
<hkern u1="&#xff;" u2="&#x7d;" k="49" />
<hkern u1="&#xff;" u2="]" k="57" />
<hkern u1="&#xff;" u2="&#x2f;" k="70" />
<hkern u1="&#xff;" u2="&#x2c;" k="78" />
<hkern u1="&#xff;" u2="&#x29;" k="47" />
<hkern u1="&#xff;" u2="&#x20;" k="50" />
<hkern u1="&#x152;" u2="&#xf0;" k="58" />
<hkern u1="&#x152;" u2="&#xef;" k="-1" />
<hkern u1="&#x152;" u2="&#xee;" k="-6" />
<hkern u1="&#x152;" u2="&#xed;" k="11" />
<hkern u1="&#x152;" u2="&#xec;" k="-4" />
<hkern u1="&#x152;" u2="&#xdf;" k="35" />
<hkern u1="&#x153;" u2="&#x2122;" k="49" />
<hkern u1="&#x153;" u2="&#xba;" k="29" />
<hkern u1="&#x153;" u2="&#x7d;" k="59" />
<hkern u1="&#x153;" u2="]" k="67" />
<hkern u1="&#x153;" u2="\" k="77" />
<hkern u1="&#x153;" u2="&#x3f;" k="49" />
<hkern u1="&#x153;" u2="&#x2a;" k="51" />
<hkern u1="&#x153;" u2="&#x29;" k="48" />
<hkern u1="&#x178;" g2="uniFB02" k="103" />
<hkern u1="&#x178;" u2="&#x2122;" k="-14" />
<hkern u1="&#x178;" u2="&#xf9;" k="159" />
<hkern u1="&#x178;" u2="&#xf6;" k="210" />
<hkern u1="&#x178;" u2="&#xf2;" k="199" />
<hkern u1="&#x178;" u2="&#xf0;" k="145" />
<hkern u1="&#x178;" u2="&#xef;" k="-16" />
<hkern u1="&#x178;" u2="&#xed;" k="56" />
<hkern u1="&#x178;" u2="&#xec;" k="-36" />
<hkern u1="&#x178;" u2="&#xeb;" k="207" />
<hkern u1="&#x178;" u2="&#xe8;" k="196" />
<hkern u1="&#x178;" u2="&#xe4;" k="198" />
<hkern u1="&#x178;" u2="&#xe3;" k="214" />
<hkern u1="&#x178;" u2="&#xe0;" k="186" />
<hkern u1="&#x178;" u2="&#xdf;" k="88" />
<hkern u1="&#x178;" u2="&#xae;" k="57" />
<hkern u1="&#x178;" u2="&#xa9;" k="58" />
<hkern u1="&#x178;" u2="&#x7d;" k="-6" />
<hkern u1="&#x178;" u2="]" k="-8" />
<hkern u1="&#x178;" u2="\" k="-4" />
<hkern u1="&#x178;" u2="&#x40;" k="90" />
<hkern u1="&#x178;" u2="&#x3b;" k="65" />
<hkern u1="&#x178;" u2="&#x3a;" k="69" />
<hkern u1="&#x178;" u2="&#x2f;" k="139" />
<hkern u1="&#x178;" u2="&#x2c;" k="171" />
<hkern u1="&#x178;" u2="&#x29;" k="-6" />
<hkern u1="&#x178;" u2="&#x26;" k="74" />
<hkern u1="&#x178;" u2="&#x20;" k="77" />
<hkern u1="&#x2018;" u2="&#xf0;" k="47" />
<hkern u1="&#x2018;" u2="&#xee;" k="-8" />
<hkern u1="&#x2018;" u2="&#xec;" k="-22" />
<hkern u1="&#x2018;" u2="&#xce;" k="-10" />
<hkern u1="&#x2018;" u2="&#x2c;" k="210" />
<hkern u1="&#x2019;" u2="&#xf0;" k="42" />
<hkern u1="&#x2019;" u2="&#xef;" k="-18" />
<hkern u1="&#x2019;" u2="&#xee;" k="-25" />
<hkern u1="&#x2019;" u2="&#xec;" k="-14" />
<hkern u1="&#x2019;" u2="&#x40;" k="36" />
<hkern u1="&#x2019;" u2="&#x2f;" k="119" />
<hkern u1="&#x2019;" u2="&#x2c;" k="210" />
<hkern u1="&#x2019;" u2="&#x26;" k="41" />
<hkern u1="&#x2019;" u2="&#x20;" k="30" />
<hkern u1="&#x201c;" u2="&#xf0;" k="47" />
<hkern u1="&#x201c;" u2="&#xee;" k="-8" />
<hkern u1="&#x201c;" u2="&#xec;" k="-22" />
<hkern u1="&#x201c;" u2="&#xce;" k="-10" />
<hkern u1="&#x201c;" u2="&#x2c;" k="210" />
<hkern u1="&#x201d;" u2="&#xf0;" k="42" />
<hkern u1="&#x201d;" u2="&#xef;" k="-18" />
<hkern u1="&#x201d;" u2="&#xee;" k="-25" />
<hkern u1="&#x201d;" u2="&#xec;" k="-14" />
<hkern u1="&#x201d;" u2="&#x40;" k="36" />
<hkern u1="&#x201d;" u2="&#x2f;" k="119" />
<hkern u1="&#x201d;" u2="&#x2c;" k="210" />
<hkern u1="&#x201d;" u2="&#x26;" k="41" />
<hkern u1="&#x201d;" u2="&#x20;" k="30" />
<hkern g1="uniFB01" u2="&#xef;" k="-5" />
<hkern g1="uniFB02" u2="&#xee;" k="-9" />
<hkern g1="uniFB02" u2="&#xb7;" k="62" />
<hkern g1="uniFB03" u2="&#xef;" k="-5" />
<hkern g1="uniFB04" u2="&#xee;" k="-9" />
<hkern g1="uniFB04" u2="&#xb7;" k="62" />
<hkern g1="B" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="25" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="43" />
<hkern g1="B" 	g2="V" 	k="25" />
<hkern g1="B" 	g2="t" 	k="34" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="30" />
<hkern g1="B" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="31" />
<hkern g1="B" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="B" 	g2="m,n,p,r,ntilde" 	k="26" />
<hkern g1="B" 	g2="l" 	k="24" />
<hkern g1="B" 	g2="T" 	k="51" />
<hkern g1="B" 	g2="W" 	k="6" />
<hkern g1="B" 	g2="b,h,k,germandbls,thorn" 	k="24" />
<hkern g1="B" 	g2="d,q" 	k="16" />
<hkern g1="B" 	g2="v" 	k="30" />
<hkern g1="B" 	g2="w" 	k="18" />
<hkern g1="B" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="B" 	g2="J" 	k="27" />
<hkern g1="B" 	g2="X" 	k="23" />
<hkern g1="B" 	g2="g" 	k="40" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="B" 	g2="s" 	k="48" />
<hkern g1="B" 	g2="AE" 	k="35" />
<hkern g1="B" 	g2="x" 	k="43" />
<hkern g1="B" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="17" />
<hkern g1="B" 	g2="z" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="24" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="19" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,ntilde" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="d,q" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="36" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="26" />
<hkern g1="C,Ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="19" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="23" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="6" />
<hkern g1="D,Eth" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="24" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="D,Eth" 	g2="V" 	k="34" />
<hkern g1="D,Eth" 	g2="t" 	k="8" />
<hkern g1="D,Eth" 	g2="y,yacute,ydieresis" 	k="3" />
<hkern g1="D,Eth" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="4" />
<hkern g1="D,Eth" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="19" />
<hkern g1="D,Eth" 	g2="m,n,p,r,ntilde" 	k="25" />
<hkern g1="D,Eth" 	g2="l" 	k="23" />
<hkern g1="D,Eth" 	g2="T" 	k="86" />
<hkern g1="D,Eth" 	g2="W" 	k="10" />
<hkern g1="D,Eth" 	g2="b,h,k,germandbls,thorn" 	k="23" />
<hkern g1="D,Eth" 	g2="d,q" 	k="16" />
<hkern g1="D,Eth" 	g2="v" 	k="7" />
<hkern g1="D,Eth" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="15" />
<hkern g1="D,Eth" 	g2="J" 	k="89" />
<hkern g1="D,Eth" 	g2="X" 	k="60" />
<hkern g1="D,Eth" 	g2="g" 	k="28" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="D,Eth" 	g2="s" 	k="19" />
<hkern g1="D,Eth" 	g2="AE" 	k="61" />
<hkern g1="D,Eth" 	g2="x" 	k="35" />
<hkern g1="D,Eth" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="D,Eth" 	g2="z" 	k="25" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="44" />
<hkern g1="D,Eth" 	g2="period,ellipsis" 	k="55" />
<hkern g1="D,Eth" 	g2="Z" 	k="38" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="50" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="57" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="57" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="m,n,p,r,ntilde" 	k="36" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="l" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="b,h,k,germandbls,thorn" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d,q" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="60" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="48" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="57" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="32" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="19" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="19" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="28" />
<hkern g1="F" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="7" />
<hkern g1="F" 	g2="t" 	k="46" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="39" />
<hkern g1="F" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="48" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="82" />
<hkern g1="F" 	g2="m,n,p,r,ntilde" 	k="93" />
<hkern g1="F" 	g2="l" 	k="20" />
<hkern g1="F" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="F" 	g2="d,q" 	k="89" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="F" 	g2="v" 	k="39" />
<hkern g1="F" 	g2="w" 	k="34" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="86" />
<hkern g1="F" 	g2="J" 	k="215" />
<hkern g1="F" 	g2="g" 	k="97" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="115" />
<hkern g1="F" 	g2="s" 	k="90" />
<hkern g1="F" 	g2="AE" 	k="180" />
<hkern g1="F" 	g2="x" 	k="78" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="155" />
<hkern g1="F" 	g2="z" 	k="90" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="195" />
<hkern g1="F" 	g2="period,ellipsis" 	k="218" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="44" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="39" />
<hkern g1="F" 	g2="S" 	k="54" />
<hkern g1="F" 	g2="hyphen,emdash" 	k="30" />
<hkern g1="G" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="14" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="28" />
<hkern g1="G" 	g2="V" 	k="22" />
<hkern g1="G" 	g2="t" 	k="36" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="42" />
<hkern g1="G" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="34" />
<hkern g1="G" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="19" />
<hkern g1="G" 	g2="m,n,p,r,ntilde" 	k="18" />
<hkern g1="G" 	g2="l" 	k="15" />
<hkern g1="G" 	g2="T" 	k="9" />
<hkern g1="G" 	g2="W" 	k="4" />
<hkern g1="G" 	g2="b,h,k,germandbls,thorn" 	k="15" />
<hkern g1="G" 	g2="d,q" 	k="13" />
<hkern g1="G" 	g2="v" 	k="41" />
<hkern g1="G" 	g2="w" 	k="26" />
<hkern g1="G" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="13" />
<hkern g1="G" 	g2="g" 	k="25" />
<hkern g1="G" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="G" 	g2="s" 	k="14" />
<hkern g1="G" 	g2="AE" 	k="14" />
<hkern g1="G" 	g2="x" 	k="18" />
<hkern g1="G" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="G" 	g2="z" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="16" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="t" 	k="31" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="y,yacute,ydieresis" 	k="26" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="30" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="17" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="m,n,p,r,ntilde" 	k="16" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="l" 	k="16" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="b,h,k,germandbls,thorn" 	k="16" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="d,q" 	k="32" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="v" 	k="28" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="w" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="g" 	k="35" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="s" 	k="36" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="x" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="36" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="z" 	k="25" />
<hkern g1="J" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="19" />
<hkern g1="J" 	g2="t" 	k="25" />
<hkern g1="J" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="J" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="24" />
<hkern g1="J" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="28" />
<hkern g1="J" 	g2="m,n,p,r,ntilde" 	k="22" />
<hkern g1="J" 	g2="l" 	k="19" />
<hkern g1="J" 	g2="b,h,k,germandbls,thorn" 	k="19" />
<hkern g1="J" 	g2="d,q" 	k="41" />
<hkern g1="J" 	g2="v" 	k="18" />
<hkern g1="J" 	g2="w" 	k="11" />
<hkern g1="J" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="J" 	g2="J" 	k="42" />
<hkern g1="J" 	g2="g" 	k="44" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="17" />
<hkern g1="J" 	g2="s" 	k="48" />
<hkern g1="J" 	g2="AE" 	k="33" />
<hkern g1="J" 	g2="x" 	k="25" />
<hkern g1="J" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="45" />
<hkern g1="J" 	g2="z" 	k="36" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="J" 	g2="period,ellipsis" 	k="29" />
<hkern g1="K" 	g2="t" 	k="61" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="105" />
<hkern g1="K" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="44" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="51" />
<hkern g1="K" 	g2="d,q" 	k="69" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="101" />
<hkern g1="K" 	g2="v" 	k="104" />
<hkern g1="K" 	g2="w" 	k="78" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="81" />
<hkern g1="K" 	g2="s" 	k="11" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="11" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="58" />
<hkern g1="K" 	g2="hyphen,emdash" 	k="68" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="193" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="67" />
<hkern g1="L" 	g2="V" 	k="185" />
<hkern g1="L" 	g2="t" 	k="90" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="201" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="197" />
<hkern g1="L" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="57" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="76" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="203" />
<hkern g1="L" 	g2="T" 	k="204" />
<hkern g1="L" 	g2="W" 	k="129" />
<hkern g1="L" 	g2="d,q" 	k="80" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="88" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="204" />
<hkern g1="L" 	g2="v" 	k="198" />
<hkern g1="L" 	g2="w" 	k="146" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="100" />
<hkern g1="L" 	g2="s" 	k="19" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="115" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="48" />
<hkern g1="L" 	g2="hyphen,emdash" 	k="159" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="68" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="33" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t" 	k="7" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="y,yacute,ydieresis" 	k="3" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="4" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="18" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="m,n,p,r,ntilde" 	k="24" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="l" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="85" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="10" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="b,h,k,germandbls,thorn" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="d,q" 	k="15" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="v" 	k="7" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="90" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="58" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="g" 	k="28" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="32" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="s" 	k="18" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="59" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="34" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="25" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="42" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="period,ellipsis" 	k="57" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="36" />
<hkern g1="P" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="9" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="22" />
<hkern g1="P" 	g2="V" 	k="8" />
<hkern g1="P" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="P" 	g2="m,n,p,r,ntilde" 	k="23" />
<hkern g1="P" 	g2="l" 	k="11" />
<hkern g1="P" 	g2="b,h,k,germandbls,thorn" 	k="11" />
<hkern g1="P" 	g2="d,q" 	k="65" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="62" />
<hkern g1="P" 	g2="J" 	k="173" />
<hkern g1="P" 	g2="X" 	k="28" />
<hkern g1="P" 	g2="g" 	k="51" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="100" />
<hkern g1="P" 	g2="s" 	k="28" />
<hkern g1="P" 	g2="AE" 	k="167" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="207" />
<hkern g1="P" 	g2="period,ellipsis" 	k="223" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="P" 	g2="hyphen,emdash" 	k="77" />
<hkern g1="R" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="14" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="42" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="4" />
<hkern g1="R" 	g2="V" 	k="28" />
<hkern g1="R" 	g2="t" 	k="32" />
<hkern g1="R" 	g2="y,yacute,ydieresis" 	k="30" />
<hkern g1="R" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="27" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="43" />
<hkern g1="R" 	g2="m,n,p,r,ntilde" 	k="14" />
<hkern g1="R" 	g2="l" 	k="12" />
<hkern g1="R" 	g2="T" 	k="60" />
<hkern g1="R" 	g2="W" 	k="10" />
<hkern g1="R" 	g2="b,h,k,germandbls,thorn" 	k="12" />
<hkern g1="R" 	g2="d,q" 	k="54" />
<hkern g1="R" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="7" />
<hkern g1="R" 	g2="v" 	k="30" />
<hkern g1="R" 	g2="w" 	k="23" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="65" />
<hkern g1="R" 	g2="g" 	k="3" />
<hkern g1="R" 	g2="s" 	k="17" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="S" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="15" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="25" />
<hkern g1="S" 	g2="V" 	k="22" />
<hkern g1="S" 	g2="t" 	k="47" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="55" />
<hkern g1="S" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="45" />
<hkern g1="S" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="24" />
<hkern g1="S" 	g2="m,n,p,r,ntilde" 	k="26" />
<hkern g1="S" 	g2="l" 	k="23" />
<hkern g1="S" 	g2="W" 	k="3" />
<hkern g1="S" 	g2="b,h,k,germandbls,thorn" 	k="23" />
<hkern g1="S" 	g2="d,q" 	k="9" />
<hkern g1="S" 	g2="v" 	k="54" />
<hkern g1="S" 	g2="w" 	k="38" />
<hkern g1="S" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="S" 	g2="J" 	k="8" />
<hkern g1="S" 	g2="X" 	k="11" />
<hkern g1="S" 	g2="g" 	k="35" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="22" />
<hkern g1="S" 	g2="s" 	k="33" />
<hkern g1="S" 	g2="AE" 	k="36" />
<hkern g1="S" 	g2="x" 	k="50" />
<hkern g1="S" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="15" />
<hkern g1="S" 	g2="z" 	k="37" />
<hkern g1="T" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="3" />
<hkern g1="T" 	g2="t" 	k="117" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="183" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="118" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="242" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="248" />
<hkern g1="T" 	g2="l" 	k="17" />
<hkern g1="T" 	g2="b,h,k,germandbls,thorn" 	k="17" />
<hkern g1="T" 	g2="d,q" 	k="254" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="85" />
<hkern g1="T" 	g2="v" 	k="181" />
<hkern g1="T" 	g2="w" 	k="182" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="255" />
<hkern g1="T" 	g2="J" 	k="155" />
<hkern g1="T" 	g2="g" 	k="260" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="142" />
<hkern g1="T" 	g2="s" 	k="244" />
<hkern g1="T" 	g2="AE" 	k="186" />
<hkern g1="T" 	g2="x" 	k="191" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="243" />
<hkern g1="T" 	g2="z" 	k="209" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="137" />
<hkern g1="T" 	g2="period,ellipsis" 	k="155" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="163" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="152" />
<hkern g1="T" 	g2="S" 	k="9" />
<hkern g1="T" 	g2="hyphen,emdash" 	k="124" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="t" 	k="24" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="24" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="34" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="m,n,p,r,ntilde" 	k="28" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="l" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="b,h,k,germandbls,thorn" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="d,q" 	k="44" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="v" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="w" 	k="11" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="44" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="66" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="50" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="54" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="54" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="28" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="53" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="38" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="36" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="period,ellipsis" 	k="47" />
<hkern g1="V" 	g2="t" 	k="47" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="46" />
<hkern g1="V" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="47" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="102" />
<hkern g1="V" 	g2="m,n,p,r,ntilde" 	k="113" />
<hkern g1="V" 	g2="l" 	k="11" />
<hkern g1="V" 	g2="b,h,k,germandbls,thorn" 	k="11" />
<hkern g1="V" 	g2="d,q" 	k="139" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="33" />
<hkern g1="V" 	g2="v" 	k="48" />
<hkern g1="V" 	g2="w" 	k="49" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="142" />
<hkern g1="V" 	g2="J" 	k="142" />
<hkern g1="V" 	g2="g" 	k="145" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="V" 	g2="s" 	k="131" />
<hkern g1="V" 	g2="AE" 	k="155" />
<hkern g1="V" 	g2="x" 	k="61" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="144" />
<hkern g1="V" 	g2="z" 	k="81" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="140" />
<hkern g1="V" 	g2="period,ellipsis" 	k="160" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="97" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="V" 	g2="S" 	k="26" />
<hkern g1="V" 	g2="hyphen,emdash" 	k="61" />
<hkern g1="W" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="3" />
<hkern g1="W" 	g2="t" 	k="32" />
<hkern g1="W" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="W" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="30" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="78" />
<hkern g1="W" 	g2="l" 	k="5" />
<hkern g1="W" 	g2="b,h,k,germandbls,thorn" 	k="5" />
<hkern g1="W" 	g2="d,q" 	k="99" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="9" />
<hkern g1="W" 	g2="v" 	k="24" />
<hkern g1="W" 	g2="w" 	k="24" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="103" />
<hkern g1="W" 	g2="J" 	k="113" />
<hkern g1="W" 	g2="g" 	k="105" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="77" />
<hkern g1="W" 	g2="s" 	k="94" />
<hkern g1="W" 	g2="AE" 	k="132" />
<hkern g1="W" 	g2="x" 	k="34" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="W" 	g2="z" 	k="51" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="98" />
<hkern g1="W" 	g2="period,ellipsis" 	k="116" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="65" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="W" 	g2="S" 	k="5" />
<hkern g1="W" 	g2="hyphen,emdash" 	k="30" />
<hkern g1="X" 	g2="t" 	k="67" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="91" />
<hkern g1="X" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="56" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="65" />
<hkern g1="X" 	g2="m,n,p,r,ntilde" 	k="24" />
<hkern g1="X" 	g2="l" 	k="11" />
<hkern g1="X" 	g2="b,h,k,germandbls,thorn" 	k="11" />
<hkern g1="X" 	g2="d,q" 	k="68" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="62" />
<hkern g1="X" 	g2="v" 	k="90" />
<hkern g1="X" 	g2="w" 	k="80" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="80" />
<hkern g1="X" 	g2="g" 	k="13" />
<hkern g1="X" 	g2="s" 	k="16" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="54" />
<hkern g1="X" 	g2="hyphen,emdash" 	k="50" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="83" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="116" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="91" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="165" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="l" 	k="11" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="b,h,k,germandbls,thorn" 	k="11" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d,q" 	k="216" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="115" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="103" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="216" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="157" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="199" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="131" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="211" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="188" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="103" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="221" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="139" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="156" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="period,ellipsis" 	k="174" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="151" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="106" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="36" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,emdash" 	k="136" />
<hkern g1="Z" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="7" />
<hkern g1="Z" 	g2="t" 	k="49" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="69" />
<hkern g1="Z" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="48" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="55" />
<hkern g1="Z" 	g2="m,n,p,r,ntilde" 	k="32" />
<hkern g1="Z" 	g2="l" 	k="12" />
<hkern g1="Z" 	g2="b,h,k,germandbls,thorn" 	k="12" />
<hkern g1="Z" 	g2="d,q" 	k="48" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="Z" 	g2="v" 	k="70" />
<hkern g1="Z" 	g2="w" 	k="59" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="56" />
<hkern g1="Z" 	g2="g" 	k="24" />
<hkern g1="Z" 	g2="s" 	k="16" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="12" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="Z" 	g2="hyphen,emdash" 	k="22" />
<hkern g1="b,p,thorn" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="13" />
<hkern g1="b,p,thorn" 	g2="w" 	k="10" />
<hkern g1="b,p,thorn" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="11" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="56" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="39" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ydieresis" 	k="202" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="24" />
<hkern g1="b,p,thorn" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="b,p,thorn" 	g2="W" 	k="101" />
<hkern g1="b,p,thorn" 	g2="S" 	k="31" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="52" />
<hkern g1="b,p,thorn" 	g2="V" 	k="138" />
<hkern g1="b,p,thorn" 	g2="v" 	k="20" />
<hkern g1="b,p,thorn" 	g2="X" 	k="79" />
<hkern g1="b,p,thorn" 	g2="Z" 	k="59" />
<hkern g1="b,p,thorn" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="42" />
<hkern g1="b,p,thorn" 	g2="T" 	k="256" />
<hkern g1="b,p,thorn" 	g2="t" 	k="12" />
<hkern g1="b,p,thorn" 	g2="z" 	k="20" />
<hkern g1="b,p,thorn" 	g2="x" 	k="41" />
<hkern g1="b,p,thorn" 	g2="J" 	k="78" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="30" />
<hkern g1="c,ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="c,ccedilla" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="18" />
<hkern g1="c,ccedilla" 	g2="W" 	k="75" />
<hkern g1="c,ccedilla" 	g2="S" 	k="15" />
<hkern g1="c,ccedilla" 	g2="V" 	k="115" />
<hkern g1="c,ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="42" />
<hkern g1="c,ccedilla" 	g2="T" 	k="230" />
<hkern g1="c,ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="19" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="40" />
<hkern g1="c,ccedilla" 	g2="d,q" 	k="15" />
<hkern g1="c,ccedilla" 	g2="hyphen,emdash" 	k="27" />
<hkern g1="d" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="22" />
<hkern g1="d" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="d" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="16" />
<hkern g1="d" 	g2="W" 	k="5" />
<hkern g1="d" 	g2="S" 	k="16" />
<hkern g1="d" 	g2="V" 	k="11" />
<hkern g1="d" 	g2="X" 	k="11" />
<hkern g1="d" 	g2="Z" 	k="17" />
<hkern g1="d" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="d" 	g2="T" 	k="17" />
<hkern g1="d" 	g2="J" 	k="3" />
<hkern g1="d" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="11" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="39" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="227" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="22" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="34" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="113" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="40" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="42" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="143" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="17" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Z" 	k="29" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="42" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="247" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="3" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="11" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="36" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="13" />
<hkern g1="f" 	g2="Y,Yacute,Ydieresis" 	k="-16" />
<hkern g1="f" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="8" />
<hkern g1="f" 	g2="W" 	k="-8" />
<hkern g1="f" 	g2="S" 	k="11" />
<hkern g1="f" 	g2="V" 	k="-16" />
<hkern g1="f" 	g2="X" 	k="-7" />
<hkern g1="f" 	g2="J" 	k="154" />
<hkern g1="f" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="98" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="36" />
<hkern g1="f" 	g2="d,q" 	k="44" />
<hkern g1="f" 	g2="hyphen,emdash" 	k="48" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="9" />
<hkern g1="f" 	g2="g" 	k="17" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="72" />
<hkern g1="f" 	g2="period,ellipsis" 	k="51" />
<hkern g1="g" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="15" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="137" />
<hkern g1="g" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="24" />
<hkern g1="g" 	g2="W" 	k="42" />
<hkern g1="g" 	g2="S" 	k="14" />
<hkern g1="g" 	g2="V" 	k="74" />
<hkern g1="g" 	g2="X" 	k="8" />
<hkern g1="g" 	g2="Z" 	k="11" />
<hkern g1="g" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="28" />
<hkern g1="g" 	g2="T" 	k="216" />
<hkern g1="g" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="9" />
<hkern g1="g" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="13" />
<hkern g1="g" 	g2="d,q" 	k="12" />
<hkern g1="g" 	g2="g" 	k="6" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="16" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="W" 	k="3" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="S" 	k="18" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="Z" 	k="15" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="T" 	k="3" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="J" 	k="3" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="7" />
<hkern g1="k" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="48" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="k" 	g2="W" 	k="32" />
<hkern g1="k" 	g2="V" 	k="63" />
<hkern g1="k" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="28" />
<hkern g1="k" 	g2="T" 	k="191" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="58" />
<hkern g1="k" 	g2="d,q" 	k="58" />
<hkern g1="k" 	g2="hyphen,emdash" 	k="56" />
<hkern g1="h,m,n,ntilde" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="13" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="3" />
<hkern g1="h,m,n,ntilde" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="26" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="34" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="9" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="208" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="22" />
<hkern g1="h,m,n,ntilde" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="18" />
<hkern g1="h,m,n,ntilde" 	g2="W" 	k="83" />
<hkern g1="h,m,n,ntilde" 	g2="S" 	k="23" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="36" />
<hkern g1="h,m,n,ntilde" 	g2="V" 	k="126" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="19" />
<hkern g1="h,m,n,ntilde" 	g2="X" 	k="17" />
<hkern g1="h,m,n,ntilde" 	g2="Z" 	k="25" />
<hkern g1="h,m,n,ntilde" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="34" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="263" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="13" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="17" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="15" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="46" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Y,Yacute,Ydieresis" 	k="216" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="28" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="32" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="W" 	k="103" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="S" 	k="32" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="48" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="V" 	k="142" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="25" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="X" 	k="78" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Z" 	k="56" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="44" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="T" 	k="259" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="17" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="44" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="J" 	k="77" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="28" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="g" 	k="3" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="r" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="19" />
<hkern g1="r" 	g2="W" 	k="10" />
<hkern g1="r" 	g2="S" 	k="11" />
<hkern g1="r" 	g2="V" 	k="32" />
<hkern g1="r" 	g2="X" 	k="105" />
<hkern g1="r" 	g2="Z" 	k="67" />
<hkern g1="r" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="8" />
<hkern g1="r" 	g2="T" 	k="183" />
<hkern g1="r" 	g2="J" 	k="171" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="116" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="57" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="r" 	g2="d,q" 	k="55" />
<hkern g1="r" 	g2="hyphen,emdash" 	k="70" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="22" />
<hkern g1="r" 	g2="g" 	k="39" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="105" />
<hkern g1="r" 	g2="period,ellipsis" 	k="80" />
<hkern g1="r" 	g2="s" 	k="11" />
<hkern g1="s" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="22" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="189" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="17" />
<hkern g1="s" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="35" />
<hkern g1="s" 	g2="W" 	k="90" />
<hkern g1="s" 	g2="S" 	k="8" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="s" 	g2="V" 	k="115" />
<hkern g1="s" 	g2="v" 	k="13" />
<hkern g1="s" 	g2="X" 	k="31" />
<hkern g1="s" 	g2="Z" 	k="22" />
<hkern g1="s" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="44" />
<hkern g1="s" 	g2="T" 	k="244" />
<hkern g1="s" 	g2="J" 	k="19" />
<hkern g1="s" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="11" />
<hkern g1="s" 	g2="g" 	k="3" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="109" />
<hkern g1="t" 	g2="W" 	k="14" />
<hkern g1="t" 	g2="V" 	k="49" />
<hkern g1="t" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="11" />
<hkern g1="t" 	g2="T" 	k="187" />
<hkern g1="t" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="3" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="22" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="162" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="14" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="75" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="S" 	k="17" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="110" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="22" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Z" 	k="26" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="26" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="247" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="J" 	k="3" />
<hkern g1="v" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="7" />
<hkern g1="v" 	g2="Y,Yacute,Ydieresis" 	k="115" />
<hkern g1="v" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="28" />
<hkern g1="v" 	g2="W" 	k="24" />
<hkern g1="v" 	g2="S" 	k="16" />
<hkern g1="v" 	g2="V" 	k="48" />
<hkern g1="v" 	g2="X" 	k="89" />
<hkern g1="v" 	g2="Z" 	k="69" />
<hkern g1="v" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="16" />
<hkern g1="v" 	g2="T" 	k="181" />
<hkern g1="v" 	g2="J" 	k="157" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="78" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="v" 	g2="guillemotleft,guilsinglleft" 	k="9" />
<hkern g1="v" 	g2="d,q" 	k="25" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="19" />
<hkern g1="v" 	g2="g" 	k="23" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="82" />
<hkern g1="v" 	g2="period,ellipsis" 	k="68" />
<hkern g1="v" 	g2="s" 	k="16" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="103" />
<hkern g1="w" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="20" />
<hkern g1="w" 	g2="W" 	k="24" />
<hkern g1="w" 	g2="S" 	k="12" />
<hkern g1="w" 	g2="V" 	k="49" />
<hkern g1="w" 	g2="X" 	k="81" />
<hkern g1="w" 	g2="Z" 	k="56" />
<hkern g1="w" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="11" />
<hkern g1="w" 	g2="T" 	k="182" />
<hkern g1="w" 	g2="J" 	k="134" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="58" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="w" 	g2="d,q" 	k="12" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="w" 	g2="g" 	k="11" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="54" />
<hkern g1="w" 	g2="period,ellipsis" 	k="44" />
<hkern g1="w" 	g2="s" 	k="4" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="34" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="103" />
<hkern g1="x" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="11" />
<hkern g1="x" 	g2="W" 	k="34" />
<hkern g1="x" 	g2="S" 	k="8" />
<hkern g1="x" 	g2="V" 	k="61" />
<hkern g1="x" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="28" />
<hkern g1="x" 	g2="T" 	k="191" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="44" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="50" />
<hkern g1="x" 	g2="d,q" 	k="40" />
<hkern g1="x" 	g2="hyphen,emdash" 	k="34" />
<hkern g1="y,yacute,ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="7" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="112" />
<hkern g1="y,yacute,ydieresis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="26" />
<hkern g1="y,yacute,ydieresis" 	g2="W" 	k="22" />
<hkern g1="y,yacute,ydieresis" 	g2="S" 	k="16" />
<hkern g1="y,yacute,ydieresis" 	g2="V" 	k="45" />
<hkern g1="y,yacute,ydieresis" 	g2="X" 	k="88" />
<hkern g1="y,yacute,ydieresis" 	g2="Z" 	k="67" />
<hkern g1="y,yacute,ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="13" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="177" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="159" />
<hkern g1="y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="79" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="24" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="9" />
<hkern g1="y,yacute,ydieresis" 	g2="d,q" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="17" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="84" />
<hkern g1="y,yacute,ydieresis" 	g2="period,ellipsis" 	k="70" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="14" />
<hkern g1="z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="17" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="132" />
<hkern g1="z" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="25" />
<hkern g1="z" 	g2="W" 	k="42" />
<hkern g1="z" 	g2="S" 	k="8" />
<hkern g1="z" 	g2="V" 	k="69" />
<hkern g1="z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="33" />
<hkern g1="z" 	g2="T" 	k="197" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="36" />
<hkern g1="z" 	g2="d,q" 	k="18" />
<hkern g1="z" 	g2="hyphen,emdash" 	k="26" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="50" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="44" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="134" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="65" />
<hkern g1="quoteright,quotedblright" 	g2="d,q" 	k="84" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="196" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="55" />
<hkern g1="quoteright,quotedblright" 	g2="period,ellipsis" 	k="210" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="148" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,emdash" 	k="122" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="30" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="90" />
<hkern g1="quoteright,quotedblright" 	g2="quotesinglbase,quotedblbase" 	k="208" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="100" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="85" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="155" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="56" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="201" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="208" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="137" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="37" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="91" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="139" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="105" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="152" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="60" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="99" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="65" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="9" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="151" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="71" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="164" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="36" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="97" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="27" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="53" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="45" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="49" />
<hkern g1="hyphen,emdash" 	g2="AE" 	k="31" />
<hkern g1="hyphen,emdash" 	g2="J" 	k="120" />
<hkern g1="hyphen,emdash" 	g2="W" 	k="30" />
<hkern g1="hyphen,emdash" 	g2="Y,Yacute,Ydieresis" 	k="136" />
<hkern g1="hyphen,emdash" 	g2="quoteright,quotedblright" 	k="108" />
<hkern g1="hyphen,emdash" 	g2="T" 	k="125" />
<hkern g1="hyphen,emdash" 	g2="V" 	k="61" />
<hkern g1="hyphen,emdash" 	g2="Z" 	k="20" />
<hkern g1="hyphen,emdash" 	g2="X" 	k="50" />
<hkern g1="hyphen,emdash" 	g2="z" 	k="34" />
<hkern g1="hyphen,emdash" 	g2="x" 	k="34" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="28" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="8" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="111" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q" 	k="47" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="172" />
<hkern g1="quotedbl,quotesingle" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="period,ellipsis" 	k="201" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="150" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="48" />
<hkern g1="quotedbl,quotesingle" 	g2="quotesinglbase,quotedblbase" 	k="201" />
<hkern g1="l,uniFB02,uniFB04" 	g2="V" 	k="11" />
<hkern g1="l,uniFB02,uniFB04" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="X" 	k="11" />
<hkern g1="l,uniFB02,uniFB04" 	g2="W" 	k="5" />
<hkern g1="l,uniFB02,uniFB04" 	g2="S" 	k="16" />
<hkern g1="l,uniFB02,uniFB04" 	g2="T" 	k="17" />
<hkern g1="l,uniFB02,uniFB04" 	g2="J" 	k="3" />
<hkern g1="l,uniFB02,uniFB04" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="16" />
<hkern g1="l,uniFB02,uniFB04" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="7" />
<hkern g1="l,uniFB02,uniFB04" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="l,uniFB02,uniFB04" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="22" />
<hkern g1="l,uniFB02,uniFB04" 	g2="Z" 	k="17" />
</font>
</defs></svg> 