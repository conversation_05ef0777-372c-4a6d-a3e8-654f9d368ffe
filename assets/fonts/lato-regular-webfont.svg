<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="latoregular" horiz-adv-x="1187" >
<font-face units-per-em="2048" ascent="1649" descent="-399" />
<missing-glyph horiz-adv-x="524" />
<glyph unicode="&#xfb01;" horiz-adv-x="1172" d="M50 942v75h164v57q0 95 29.5 174.5t89 137t148 89t205.5 31.5q39 0 80 -5t71 -15l-8 -95q-1 -10 -7.5 -14.5t-17.5 -5.5t-28 -0.5t-39 0.5q-182 0 -264 -75t-82 -227v-52h627v-1017h-183v885h-439v-885h-182v882l-115 13q-23 5 -36 16t-13 31z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1207" d="M50 942v75h164v55q0 86 26 163.5t79.5 134.5t133 91t186.5 34q79 0 152 -6t142 -7h124v-1482h-181v1354q-55 2 -111 5t-98 3q-133 0 -204.5 -76.5t-71.5 -213.5v-55h269v-132h-264v-885h-182v882l-115 13q-23 5 -36 16t-13 31z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1817" d="M50 942v75h164v85q0 89 25 158t71 116.5t111.5 72.5t147.5 25q70 0 129 -21l-5 -92q-2 -25 -26.5 -27t-67.5 -2q-47 0 -85.5 -11.5t-65.5 -39.5t-42 -73.5t-15 -111.5v-79h468v57q0 95 30 174.5t89 137t147.5 89t205.5 31.5q39 0 80 -5t71 -15l-7 -95q-1 -10 -8 -14.5 t-18 -5.5t-28 -0.5t-39 0.5q-182 0 -264 -75t-82 -227v-52h627v-1017h-183v885h-439v-885h-182v885h-463v-885h-182v882l-115 13q-23 5 -36 16t-13 31z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1851" d="M48 942v75h164v85q0 89 25 158t71.5 116.5t112 72.5t147.5 25q70 0 128 -21l-4 -92q-2 -25 -27 -27t-67 -2q-47 0 -85.5 -11.5t-66 -39.5t-42.5 -73.5t-15 -111.5v-79h468v55q0 86 26.5 163.5t80 134.5t133.5 91t186 34q78 0 151 -6t142 -7h125v-1482h-182v1354 q-55 2 -111 5t-97 3q-133 0 -205 -76.5t-72 -213.5v-55h299v-132h-293v-885h-183v885h-462v-885h-183v882l-115 13q-22 5 -35.5 16t-13.5 31z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="524" />
<glyph unicode="&#x09;" horiz-adv-x="524" />
<glyph unicode="&#xa0;" horiz-adv-x="524" />
<glyph unicode="!" horiz-adv-x="550" d="M145 113q0 27 10 50t27.5 40.5t40.5 28t50 10.5t50 -10.5t40.5 -28t27.5 -41t10 -49.5q0 -28 -10 -51t-27.5 -40.5t-40.5 -27t-50 -9.5q-28 0 -50.5 9.5t-40 27t-27.5 40.5t-10 51zM189 882v585h175v-585q0 -46 -2.5 -90t-5.5 -89t-7.5 -91.5t-10.5 -99.5h-123 q-6 53 -10.5 99.5t-7.5 91.5t-5.5 89t-2.5 90z" />
<glyph unicode="&#x22;" horiz-adv-x="758" d="M130 1170v297h159v-297l-17 -158q-5 -33 -18.5 -51t-44.5 -18q-28 0 -42.5 18t-20.5 51zM471 1170v297h159v-297l-17 -158q-5 -33 -18.5 -51t-44.5 -18q-28 0 -42.5 18t-20.5 51z" />
<glyph unicode="#" d="M60 509l9 58h209l66 334h-238l14 76q5 30 24 44t58 14h162l73 367q6 31 28 48t53 17h82l-86 -432h253l86 432h81q30 0 46.5 -19.5t11.5 -48.5l-74 -364h217l-14 -77q-5 -30 -24 -43.5t-58 -13.5h-141l-67 -334h184q30 0 42.5 -16.5t6.5 -58.5l-9 -59h-243l-86 -433h-83 q-13 0 -24.5 6t-19 16.5t-10.5 24.5t0 32l73 354h-253l-73 -363q-6 -38 -29.5 -54t-53.5 -16h-82l87 433h-149q-29 0 -42 17t-7 59zM428 567h253l67 334h-253z" />
<glyph unicode="$" d="M100 175l56 84q8 11 20 18.5t27 7.5q19 0 43.5 -19.5t61 -44t86 -48t119.5 -32.5l37 544q-72 22 -141 48.5t-124 70.5t-88 109.5t-33 164.5q0 75 29 146t85.5 126.5t138.5 91t187 39.5l10 147q1 19 15 34.5t37 15.5h67l-13 -201q106 -13 184.5 -55.5t138.5 -102.5 l-44 -68q-20 -31 -47 -30q-14 0 -35 12.5t-50 30t-68.5 34t-89.5 24.5l-34 -496q74 -24 145.5 -50t129 -68t92.5 -105t35 -158q0 -92 -30.5 -173t-89.5 -142t-144.5 -99.5t-195.5 -45.5l-12 -180q-1 -19 -15.5 -34t-35.5 -15h-68l16 231q-124 12 -226 61.5t-176 126.5z M336 1097q0 -49 17.5 -85.5t48 -64t71.5 -48t89 -37.5l32 462q-67 -6 -115.5 -26.5t-80 -51.5t-47 -69t-15.5 -80zM628 140q68 6 119 29.5t86 59t53 83t18 105.5q0 51 -19 88t-51.5 64t-76.5 46t-94 35z" />
<glyph unicode="%" horiz-adv-x="1641" d="M90 1113q0 88 24.5 156.5t68 116t103 72t128.5 24.5q70 0 129 -24.5t103 -72t68.5 -116t24.5 -156.5q0 -86 -26.5 -154t-71 -114.5t-103.5 -71t-124 -24.5q-69 0 -128.5 24.5t-103 71t-68 114.5t-24.5 154zM186 0l1072 1437q10 12 24 21t38 9h132l-1073 -1437 q-9 -13 -23.5 -21.5t-34.5 -8.5h-135zM232 1113q0 -67 14.5 -113.5t39 -75.5t57.5 -42t71 -13t71.5 13t58.5 42t39.5 75.5t14.5 113.5q0 68 -14.5 115t-39.5 77t-58.5 43.5t-71.5 13.5t-71 -13.5t-57.5 -43.5t-39 -77t-14.5 -115zM902 346q0 88 24.5 157t68 116.5t102.5 72 t129 24.5q69 0 128.5 -24.5t103 -72t68.5 -116.5t25 -157q0 -86 -26.5 -153.5t-71 -114t-103.5 -71t-124 -24.5q-70 0 -129 24.5t-102.5 71t-68 114t-24.5 153.5zM1044 346q0 -67 14 -113t38.5 -75t58 -42t71.5 -13t71.5 13t58.5 42t39.5 75t14.5 113q0 68 -14.5 115.5 t-39.5 77t-58.5 43t-71.5 13.5t-71.5 -13.5t-58 -43t-38.5 -77t-14 -115.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1458" d="M90 391q0 72 24 136t67 118t101.5 97t128.5 74q-62 80 -92 154t-30 154q0 75 27 140.5t78 114t124.5 77t164.5 28.5q81 0 147.5 -26t114.5 -69t76.5 -100t32.5 -119l-114 -22q-34 -8 -50 32q-7 27 -23 55.5t-41 52t-60.5 38.5t-82.5 15q-51 0 -92.5 -16t-71 -45.5 t-44.5 -69t-15 -85.5q0 -36 8 -68.5t26 -66.5t45.5 -68.5t66.5 -73.5l422 -429q39 70 62.5 145t30.5 150q2 19 12.5 30.5t28.5 11.5h112q-1 -118 -37 -231t-103 -213l307 -312h-177q-30 0 -48.5 7t-39.5 30l-149 149q-96 -93 -221.5 -147.5t-276.5 -54.5q-82 0 -160 27.5 t-140.5 80t-100.5 128.5t-38 171zM276 406q0 -67 25 -117.5t65 -85.5t91 -53t104 -18q115 0 206.5 42t160.5 113l-433 436q-109 -57 -164 -139t-55 -178z" />
<glyph unicode="'" horiz-adv-x="417" d="M130 1170v297h159v-297l-17 -158q-5 -33 -18.5 -51t-44.5 -18q-28 0 -42.5 18t-20.5 51z" />
<glyph unicode="(" horiz-adv-x="546" d="M92 644q0 124 15.5 243t48.5 235t87 231.5t131 234.5l81 -50q15 -10 23 -27t-8 -48q-108 -186 -163.5 -393t-55.5 -426t55.5 -426t163.5 -393q16 -32 8 -49t-23 -27l-81 -49q-77 118 -131 233.5t-87 232.5t-48.5 235t-15.5 243z" />
<glyph unicode=")" horiz-adv-x="546" d="M67 -202.5q2 12.5 10 27.5q108 186 163.5 393t55.5 426t-56 426t-163 393q-8 15 -10 27t1 21t9.5 15.5t14.5 11.5l81 50q77 -119 130.5 -234.5t87 -231.5t49 -235t15.5 -243q0 -125 -15.5 -243t-49 -235t-87 -232.5t-130.5 -233.5l-81 49q-8 5 -14.5 12t-9.5 15.5t-1 21z " />
<glyph unicode="*" horiz-adv-x="870" d="M126 1341l45 77l177 -102q18 -11 32.5 -23.5t26.5 -29.5q-16 36 -17 76v203h90v-202q0 -20 -2.5 -38t-10.5 -34q11 14 24.5 25.5t30.5 22.5l176 101l45 -77l-176 -102q-17 -11 -34.5 -17.5t-36.5 -8.5q19 -2 36.5 -7.5t34.5 -16.5l177 -104l-45 -76l-177 102 q-18 11 -32.5 22.5t-25.5 28.5q16 -34 16 -75v-202h-90v201q0 20 2.5 38t10.5 34q-11 -14 -24 -25.5t-31 -21.5l-176 -101l-45 76l176 103q39 24 78 24q-20 2 -39 8t-39 18z" />
<glyph unicode="+" d="M103 618v139h416v437h149v-437h417v-139h-417v-440h-149v440h-416z" />
<glyph unicode="," horiz-adv-x="464" d="M110 126q0 48 33 82t88 34q31 0 54.5 -11.5t39.5 -31.5t24 -46t8 -57q0 -46 -12.5 -95.5t-37.5 -98t-61 -95t-82 -85.5l-31 30q-8 7 -11 13.5t-3 15.5q0 7 4.5 14t10.5 14q10 11 26 29.5t32.5 43.5t30.5 55t20 64q-3 -1 -7 -1h-7q-53 0 -86 35.5t-33 90.5z" />
<glyph unicode="-" horiz-adv-x="760" d="M128 537v154h506v-154h-506z" />
<glyph unicode="&#xad;" horiz-adv-x="760" d="M128 537v154h506v-154h-506z" />
<glyph unicode="." horiz-adv-x="483" d="M114 113q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5t-40.5 27t-26.5 40.5t-9.5 51z" />
<glyph unicode="/" horiz-adv-x="925" d="M69 -92l615 1533q12 33 37 50t58 17h78l-612 -1530q-13 -36 -41.5 -53t-58.5 -17h-76z" />
<glyph unicode="0" d="M61 733q0 193 41 334t113 234t168.5 138t209.5 45q112 0 209.5 -45t169 -138t113 -234t41.5 -334t-41.5 -333.5t-113 -232.5t-169 -137t-209.5 -45q-113 0 -209.5 45t-168.5 137t-113 233t-41 333zM250 733q0 -168 28 -281.5t75 -183t109.5 -100t130.5 -30.5t130 30.5 t110 100t76 183t28 281.5t-28 282t-76 184t-110.5 100t-129.5 30q-68 0 -130.5 -30t-109.5 -100t-75 -184t-28 -282z" />
<glyph unicode="1" d="M168 1093l437 377h149v-1331h288v-139h-786v139h314v1001q0 45 3 91l-261 -224q-13 -11 -26.5 -14t-24.5 -1t-21 8.5t-15 13.5z" />
<glyph unicode="2" d="M96 0v62q0 19 8.5 40t25.5 38l470 472q58 59 106.5 114t82 110.5t52 112.5t18.5 121q0 65 -20.5 113.5t-55.5 80t-83.5 47.5t-103.5 16q-56 0 -103.5 -16.5t-83.5 -45.5t-60.5 -69t-35.5 -88q-12 -36 -33 -47.5t-59 -6.5l-95 16q14 100 55.5 177.5t104.5 130t144 79.5 t175 27q93 0 174 -28t140.5 -80.5t93.5 -128.5t34 -172q0 -82 -25 -152t-67 -133.5t-96.5 -124t-116.5 -121.5l-387 -396q41 11 83 18t81 7h492q30 0 47 -17.5t17 -44.5v-111h-979z" />
<glyph unicode="3" d="M88 367l79 32q31 13 60 7t42 -33q13 -29 33.5 -69t55 -77t88.5 -62.5t137 -25.5q77 0 134.5 25t96 65t58 89.5t19.5 97.5q0 59 -15.5 108t-57.5 85t-115 56.5t-188 20.5v132q94 1 160 20.5t107.5 53.5t60.5 81t19 104q0 63 -20 110.5t-54.5 78t-82.5 46t-103 15.5 t-102.5 -16.5t-84 -45.5t-61 -69t-35.5 -88q-13 -36 -33 -47.5t-58 -6.5l-95 16q14 100 55 177.5t104 130t144.5 79.5t175.5 27q93 0 172 -26.5t136 -76t88.5 -119t31.5 -154.5q0 -70 -17.5 -124.5t-51 -95.5t-80.5 -69.5t-106 -47.5q143 -37 215.5 -126.5t72.5 -223.5 q0 -101 -38.5 -182t-105 -138t-155 -87t-189.5 -30q-117 0 -200 29t-140.5 80t-94 121.5t-62.5 152.5z" />
<glyph unicode="4" d="M24 520l713 947h170v-938h222v-104q0 -16 -9.5 -27.5t-29.5 -11.5h-183v-386h-159v386h-652q-20 0 -35 12t-19 29zM221 529h527v606q0 27 2 58.5t6 63.5z" />
<glyph unicode="5" d="M115 138l56 78q18 27 48 27q20 0 46 -16t63 -35.5t86.5 -35.5t117.5 -16q77 0 138.5 24.5t105.5 70.5t67.5 109.5t23.5 141.5q0 69 -20 124.5t-60.5 94t-100.5 60t-141 21.5q-55 0 -115 -9t-123 -29l-114 33l117 686h696v-79q0 -39 -24.5 -64.5t-82.5 -25.5h-461 l-67 -385q115 25 211 25q115 0 202.5 -34t146.5 -93t89.5 -140t30.5 -176q0 -117 -40.5 -211.5t-112 -161.5t-167.5 -102.5t-208 -35.5q-65 0 -124 12.5t-110.5 34t-96 49.5t-77.5 58z" />
<glyph unicode="6" d="M111 484q0 96 42.5 205.5t135.5 233.5l373 500q14 18 39 31t58 13h162l-436 -549q-19 -25 -37 -48t-34 -46q52 36 114.5 55.5t136.5 19.5q89 0 167.5 -29t137.5 -85t94 -137.5t35 -185.5q0 -101 -37 -188.5t-103 -152t-158.5 -101t-204.5 -36.5q-111 0 -201 35t-153 100 t-97 158t-34 207zM286 453q0 -71 20.5 -129.5t60 -100.5t96 -65t129.5 -23q74 0 133.5 23.5t102 65.5t65.5 99.5t23 125.5q0 72 -22.5 129.5t-64 98t-98.5 62t-126 21.5q-74 0 -133 -25t-100.5 -67.5t-63.5 -98.5t-22 -116z" />
<glyph unicode="7" d="M111 1343v124h996v-82q0 -35 -7.5 -57t-15.5 -38l-608 -1225q-13 -27 -36.5 -46t-62.5 -19h-131l616 1210q13 26 27.5 47.5t31.5 40.5h-766q-17 0 -30.5 14t-13.5 31z" />
<glyph unicode="8" d="M99 401q0 142 74 233.5t211 130.5q-116 43 -174.5 128.5t-58.5 204.5q0 81 31.5 151.5t90 122.5t140 82t181.5 30q99 0 181 -30t140.5 -82t90 -122.5t31.5 -151.5q0 -119 -58.5 -204.5t-174.5 -128.5q137 -39 211 -130t74 -234q0 -96 -36.5 -173t-101.5 -131.5 t-156.5 -83.5t-200.5 -29q-110 0 -201 29t-156.5 83.5t-101.5 131.5t-36 173zM288 404q0 -61 21 -111.5t60.5 -87t96 -56.5t128.5 -20t128.5 20t96 56.5t60.5 87t21 111.5q0 76 -25 130t-67.5 87.5t-98 49.5t-115.5 16t-115.5 -16t-98 -49.5t-67.5 -87.5t-25 -130zM329 1095 q0 -50 14 -97.5t46 -83.5t82.5 -58t122.5 -22t122.5 22t82 58t46 83t14.5 98t-17 96t-50 79t-82.5 53t-115.5 19t-115.5 -19t-83 -53t-50 -79t-16.5 -96z" />
<glyph unicode="9" d="M108 1023q0 96 35.5 180t100 146.5t153.5 98.5t196 36q105 0 191 -35t147.5 -97.5t95 -149.5t33.5 -192q0 -63 -12 -120t-34 -112t-53.5 -109t-71.5 -110l-358 -516q-13 -19 -37.5 -31t-56.5 -12h-168l447 585q23 30 42 56.5t37 52.5q-56 -45 -127 -68.5t-150 -23.5 q-83 0 -157 27.5t-130.5 81.5t-89.5 132.5t-33 179.5zM290 1036q0 -70 20 -124.5t57 -92.5t91 -57.5t121 -19.5q74 0 130.5 24.5t95.5 64t59.5 92t20.5 108.5q0 69 -22 124.5t-61 94.5t-93 60t-118 21q-68 0 -123 -22t-95 -61t-61.5 -93t-21.5 -119z" />
<glyph unicode=":" horiz-adv-x="512" d="M128 113q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5t-40.5 27t-26.5 40.5t-9.5 51zM128 881q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41 t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5t-40.5 27t-26.5 40.5t-9.5 51z" />
<glyph unicode=";" horiz-adv-x="535" d="M140 881q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5t-40.5 27t-26.5 40.5t-9.5 51zM145 126q0 48 33 82t88 34q31 0 54.5 -11.5t39.5 -31.5t24 -46t8 -57 q0 -46 -12.5 -95.5t-37.5 -98t-61 -95t-82 -85.5l-31 30q-8 7 -11 13.5t-3 15.5q0 7 4.5 14t10.5 14q10 11 26 29.5t32.5 43.5t30.5 55t20 64q-3 -1 -7 -1h-7q-53 0 -86 35.5t-33 90.5z" />
<glyph unicode="&#x3c;" d="M166 651v76l791 411v-130q0 -17 -8 -30t-28 -24l-460 -233q-20 -10 -43.5 -18.5t-49.5 -13.5q27 -6 49.5 -13.5t43.5 -19.5l460 -232q20 -10 28 -23.5t8 -29.5v-131z" />
<glyph unicode="=" d="M154 450v138h880v-138h-880zM154 793v138h880v-138h-880z" />
<glyph unicode="&#x3e;" d="M230 240v131q0 16 8.5 29.5t28.5 23.5l460 232q20 11 43.5 19t48.5 14q-52 11 -92 32l-460 233q-20 10 -28.5 23t-8.5 31v130l792 -411v-76z" />
<glyph unicode="?" horiz-adv-x="916" d="M83 1336q32 30 70 56.5t84 47t98.5 32.5t114.5 12q81 0 150 -23.5t119 -66.5t78.5 -104.5t28.5 -138.5q0 -78 -23 -134t-58 -98.5t-76.5 -74t-78.5 -59t-63 -55.5t-30 -61l-19 -157h-125l-12 170q-4 46 17.5 80.5t56.5 64.5t77 59t78 65t60.5 81.5t24.5 109.5 q0 44 -17 79.5t-47 61t-70.5 38.5t-86.5 13q-62 0 -106.5 -15t-75.5 -33.5t-50 -34t-31 -15.5q-26 0 -40 24zM283 113q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5 t-40.5 27t-26.5 40.5t-9.5 51z" />
<glyph unicode="@" horiz-adv-x="1713" d="M106 582q0 112 28.5 215t79.5 192.5t123 164t160 127t189.5 82t212.5 29.5q94 0 184.5 -20.5t170.5 -60.5t147 -99t116.5 -135.5t76.5 -170t27 -203.5q0 -111 -31 -204t-86 -162t-130 -108t-161 -39q-78 0 -128.5 38.5t-64.5 117.5q-58 -82 -128 -117.5t-151 -35.5 q-60 0 -105.5 20.5t-75.5 58t-44.5 89t-14.5 110.5q0 87 33 175.5t98.5 160t163 116.5t225.5 45q69 0 121 -11t98 -31l-96 -370q-20 -78 -19.5 -126.5t15.5 -76t41.5 -37.5t58.5 -10q50 0 95 29t79.5 81t54.5 125t20 162q0 141 -45 248t-124.5 178t-187.5 106.5t-233 35.5 q-137 0 -257 -52.5t-209 -144.5t-140.5 -218t-51.5 -274q0 -174 54.5 -306t149.5 -221t223 -134t274 -45q156 0 275 34t206 86q20 12 36 7.5t23 -22.5l26 -68q-111 -74 -250.5 -116t-315.5 -42q-178 0 -330.5 56.5t-265 162.5t-176.5 259.5t-64 348.5zM643 476 q0 -74 34 -118.5t100 -44.5q32 0 64 10.5t62.5 36t56 69.5t42.5 111l78 302q-40 9 -88 9q-77 0 -141 -32.5t-110 -84.5t-72 -119.5t-26 -138.5z" />
<glyph unicode="A" horiz-adv-x="1386" d="M6 0l587 1467h201l586 -1467h-153q-27 0 -43.5 13.5t-24.5 33.5l-137 354h-658l-138 -354q-6 -18 -23.5 -32.5t-42.5 -14.5h-154zM419 545h548l-231 597q-10 27 -21.5 62t-21.5 75q-22 -83 -44 -138z" />
<glyph unicode="B" horiz-adv-x="1324" d="M187 0v1467h468q135 0 233 -26.5t161 -75.5t92.5 -120t29.5 -161q0 -54 -16.5 -103.5t-50.5 -92.5t-85.5 -77.5t-121.5 -55.5q162 -31 243.5 -115.5t81.5 -221.5q0 -93 -34.5 -170t-100.5 -132.5t-162.5 -85.5t-218.5 -30h-519zM385 158h317q85 0 146.5 19.5t101 54.5 t58 84t18.5 107q0 114 -81 180t-243 66h-317v-511zM385 810h262q84 0 146 18.5t102.5 51t60 79.5t19.5 102q0 130 -78 190t-242 60h-270v-501z" />
<glyph unicode="C" horiz-adv-x="1368" d="M97 733q0 166 52.5 304.5t147 237.5t226.5 154t292 55q157 0 276.5 -50.5t211.5 -136.5l-66 -91q-6 -10 -16 -16.5t-26 -6.5q-18 0 -45 20t-70 44.5t-107.5 44.5t-158.5 20q-114 0 -208 -39.5t-162 -114.5t-106 -182.5t-38 -242.5q0 -137 39.5 -244.5t107.5 -182 t160.5 -113t200.5 -38.5q66 0 118.5 7.5t97 24t83 41.5t76.5 60q18 15 35 15q16 0 28 -13l79 -85q-90 -104 -218.5 -162.5t-310.5 -58.5q-158 0 -287 54.5t-220 153.5t-141.5 237t-50.5 304z" />
<glyph unicode="D" horiz-adv-x="1557" d="M187 0v1467h548q162 0 295.5 -52.5t229 -149.5t147.5 -232t52 -300t-52 -300t-147.5 -231.5t-229 -149t-295.5 -52.5h-548zM386 161h349q119 0 215.5 39t164 112t104 179.5t36.5 241.5t-36.5 241.5t-104 180.5t-164 113t-215.5 39h-349v-1146z" />
<glyph unicode="E" horiz-adv-x="1182" d="M187 0v1467h905v-161h-706v-488h571v-155h-571v-501h707l-1 -162h-905z" />
<glyph unicode="F" horiz-adv-x="1158" d="M187 0v1467h905v-161h-706v-513h603v-162h-603v-631h-199z" />
<glyph unicode="G" horiz-adv-x="1496" d="M96 733q0 168 53 306.5t150 237t236.5 153t311.5 54.5q87 0 161.5 -13t138 -37t118 -58.5t101.5 -77.5l-56 -90q-13 -20 -34.5 -25.5t-46.5 9.5q-25 14 -56.5 34.5t-77 39.5t-107.5 32.5t-147 13.5q-124 0 -224.5 -40.5t-171 -116t-108.5 -182.5t-38 -240 q0 -138 39.5 -247t112.5 -185.5t177.5 -116.5t234.5 -40q102 0 181.5 23t155.5 64v323h-228q-19 0 -31 11.5t-12 27.5v113h450v-564q-111 -80 -236 -119t-275 -39q-177 0 -319 54.5t-243 153.5t-155.5 237t-54.5 304z" />
<glyph unicode="H" horiz-adv-x="1563" d="M187 0v1467h199v-654h792v654h198v-1467h-198v668h-792v-668h-199z" />
<glyph unicode="I" horiz-adv-x="573" d="M187 0v1467h199v-1467h-199z" />
<glyph unicode="J" horiz-adv-x="865" d="M25 12q2 30 5 59t5 58q2 17 12.5 28t33.5 11q18 0 48.5 -9t81.5 -9q68 0 121 20t89 63t55 110.5t19 159.5v964h198v-960q0 -123 -30 -220.5t-89 -164.5t-145 -102.5t-198 -35.5q-101 0 -206 28z" />
<glyph unicode="K" horiz-adv-x="1356" d="M187 0v1467h198v-643h75q39 0 62.5 10t42.5 33l489 553q20 27 42.5 37t55.5 10h168l-559 -631q-20 -25 -39.5 -41t-40.5 -26q28 -9 50.5 -27t43.5 -47l584 -695h-172q-19 0 -33 2.5t-24.5 8t-18 13.5t-14.5 18l-506 583q-20 26 -44 36.5t-72 10.5h-90v-672h-198z" />
<glyph unicode="L" horiz-adv-x="1051" d="M187 0v1467h198v-1300h635v-167h-833z" />
<glyph unicode="M" horiz-adv-x="1901" d="M187 0v1467h147q27 0 41 -5t27 -28l507 -904q12 -25 24 -51t22 -54q10 28 21.5 55t24.5 51l498 903q12 24 27 28.5t42 4.5h146v-1467h-174v1078q0 22 1.5 46.5t3.5 50.5l-504 -918q-24 -46 -71 -46h-29q-48 0 -72 46l-514 922q6 -54 6 -101v-1078h-174z" />
<glyph unicode="N" horiz-adv-x="1563" d="M187 0v1467h103q13 0 22.5 -1.5t16.5 -5t14 -10t15 -16.5l849 -1105q-2 27 -3.5 52t-1.5 47v1039h174v-1467h-99q-24 0 -39.5 8t-31.5 28l-849 1106q2 -26 3 -50.5t1 -45.5v-1046h-174z" />
<glyph unicode="O" horiz-adv-x="1639" d="M96 733q0 165 52.5 303t147.5 237.5t228 155t295 55.5t295.5 -55.5t229 -155t147.5 -237.5t52 -303t-52 -302.5t-147.5 -237t-229 -154t-295.5 -54.5t-295 54.5t-228 154t-147.5 237t-52.5 302.5zM299 733q0 -135 37 -242t105 -181t164 -113.5t214 -39.5q119 0 215 39.5 t164 113.5t104.5 181t36.5 242t-36.5 242.5t-104.5 182t-164 114.5t-215 40q-118 0 -214 -40t-164 -114.5t-105 -182t-37 -242.5z" />
<glyph unicode="P" horiz-adv-x="1229" d="M187 0v1467h434q139 0 242 -32t170.5 -91.5t100.5 -143.5t33 -187q0 -102 -35.5 -187.5t-105 -147t-171 -95.5t-234.5 -34h-236v-549h-198zM385 707h236q85 0 150 22.5t109.5 63t67 96.5t22.5 124q0 140 -86.5 219t-262.5 79h-236v-604z" />
<glyph unicode="Q" horiz-adv-x="1639" d="M97 733q0 165 52.5 303t147.5 237.5t228 155t295 55.5t295.5 -55.5t229 -155t147.5 -237.5t52 -303q0 -103 -21 -196.5t-61 -173.5t-97 -145.5t-129 -114.5l376 -406h-164q-37 0 -65.5 10t-52.5 36l-257 281q-117 -39 -253 -39q-162 0 -295 54.5t-228 154t-147.5 237 t-52.5 302.5zM300 733q0 -135 37 -242t105 -181t164 -113.5t214 -39.5q119 0 215.5 39.5t164 113.5t104 181t36.5 242t-36.5 242.5t-104 182t-164 114.5t-215.5 40q-118 0 -214 -40t-164 -114.5t-105 -182t-37 -242.5z" />
<glyph unicode="R" horiz-adv-x="1283" d="M187 0v1467h415q139 0 240.5 -28t167.5 -81t98 -128.5t32 -168.5q0 -78 -24.5 -145.5t-71.5 -121.5t-114 -91.5t-152 -57.5q38 -22 66 -62l428 -583h-176q-54 0 -80 42l-381 524q-17 25 -37 35.5t-62 10.5h-151v-612h-198zM385 757h209q86 0 152 21t110.5 59.5t67 91.5 t22.5 118q0 131 -86 197.5t-258 66.5h-217v-554z" />
<glyph unicode="S" horiz-adv-x="1111" d="M68 175l57 94q8 11 20 19t26 8q13 0 30 -11t38.5 -27t49 -35.5t63 -36t80.5 -27.5t102 -11q71 0 126 19.5t93.5 55t59 84.5t20.5 110q0 67 -30.5 109t-79.5 71t-112.5 49t-129 41t-129 48.5t-113 71.5t-79.5 110t-30 163q0 78 30 151t88 129t142.5 90t194.5 34 q123 0 223.5 -39t177.5 -113l-48 -94q-9 -15 -20 -23t-26 -8q-17 0 -41 17.5t-59 38.5t-85 38.5t-120 17.5q-67 0 -117.5 -18t-85 -48.5t-52 -72t-17.5 -89.5q0 -61 30.5 -102t80 -69.5t112.5 -49.5t129 -43.5t129 -50.5t112.5 -71t80 -105.5t30.5 -154.5q0 -96 -33 -180.5 t-96 -147t-154.5 -98t-208.5 -35.5q-143 0 -259.5 51.5t-199.5 139.5z" />
<glyph unicode="T" horiz-adv-x="1209" d="M31 1300v167h1149v-167h-475v-1300h-198v1300h-476z" />
<glyph unicode="U" horiz-adv-x="1506" d="M169 575v892h198v-890q0 -92 26 -169t75.5 -132.5t122 -86.5t163.5 -31t162.5 30.5t121 86t76 132t26.5 168.5v892h197v-892q0 -127 -40.5 -235.5t-115.5 -187.5t-183.5 -124t-243.5 -45t-244 45t-184.5 124t-116 188t-40.5 235z" />
<glyph unicode="V" horiz-adv-x="1386" d="M6 1467h159q27 0 43 -13t24 -34l414 -1035q13 -35 26 -76t23 -86q10 45 21.5 86t24.5 76l413 1035q6 17 24 32t44 15h158l-598 -1467h-178z" />
<glyph unicode="W" horiz-adv-x="2120" d="M32 1467h165q27 0 44.5 -13t22.5 -34l303 -1020q7 -28 14 -59.5t13 -67.5q7 36 14.5 68.5t17.5 58.5l345 1020q6 17 24 32t43 15h58q27 0 43 -13t24 -34l344 -1020q9 -27 17 -57.5t15 -63.5q5 34 11 64.5t14 56.5l304 1020q5 18 23 32.5t43 14.5h155l-458 -1467h-178 l-372 1119q-5 16 -9.5 34.5t-9.5 39.5q-5 -20 -10 -38.5t-11 -35.5l-373 -1119h-179z" />
<glyph unicode="X" horiz-adv-x="1329" d="M20 0l505 754l-484 713h198q22 0 32 -7t18 -20l383 -588q7 22 21 47l362 537q8 14 19 22.5t27 8.5h189l-486 -704l503 -763h-197q-23 0 -35.5 12t-20.5 26l-394 616q-8 -25 -18 -42l-383 -574q-10 -14 -22 -26t-32 -12h-185z" />
<glyph unicode="Y" horiz-adv-x="1277" d="M2 1467h174q27 0 42 -12.5t27 -33.5l336 -570l33.5 -67t26.5 -63q11 32 25.5 63t31.5 67l335 570q9 17 25.5 31.5t42.5 14.5h175l-538 -882v-585h-198v585z" />
<glyph unicode="Z" horiz-adv-x="1232" d="M54 0v78q0 16 6 30.5t14 27.5l832 1170h-813v161h1073v-73q0 -34 -20 -66l-831 -1166h839v-162h-1100z" />
<glyph unicode="[" horiz-adv-x="626" d="M186 -296v1866h377v-72q0 -20 -13.5 -34t-36.5 -14h-173v-1625h173q23 0 36.5 -14.5t13.5 -34.5v-72h-377z" />
<glyph unicode="\" horiz-adv-x="925" d="M69 1508h77q33 0 58 -17t38 -50l615 -1533h-77q-30 0 -58.5 17.5t-40.5 52.5z" />
<glyph unicode="]" horiz-adv-x="626" d="M63 -224q0 20 14 34.5t37 14.5h173v1625h-173q-23 0 -37 14t-14 34v72h377v-1866h-377v72z" />
<glyph unicode="^" d="M135 713l397 754h118l403 -754h-133q-17 0 -30 9.5t-21 25.5l-235 453q-27 54 -41 99q-7 -24 -16.5 -48t-20.5 -51l-228 -453q-6 -14 -19.5 -24.5t-34.5 -10.5h-139z" />
<glyph unicode="_" horiz-adv-x="940" d="M67 -169h806v-123h-806v123z" />
<glyph unicode="`" horiz-adv-x="819" d="M134 1484h173q34 0 50.5 -11t30.5 -34l153 -248h-105q-22 0 -35 6.5t-27 22.5z" />
<glyph unicode="a" horiz-adv-x="1017" d="M67 259q0 61 33.5 118t109.5 101t197.5 72.5t299.5 32.5v81q0 121 -52.5 182.5t-152.5 61.5q-68 0 -113.5 -17t-79 -37.5t-57.5 -37.5t-47 -17q-18 0 -31.5 9.5t-22.5 24.5l-33 58q86 83 185 124t220 41q87 0 154.5 -29t114 -80t70 -123.5t23.5 -159.5v-664h-81 q-27 0 -41 8.5t-23 34.5l-20 98q-41 -38 -80 -67t-82 -49t-91.5 -30.5t-108.5 -10.5t-113.5 16.5t-92.5 50.5t-62 85.5t-23 122.5zM242 267q0 -41 13 -70.5t36 -48.5t54.5 -27.5t67.5 -8.5q48 0 88 9.5t75 28t67.5 44t63.5 58.5v215q-126 -4 -215 -20t-144.5 -41.5 t-80.5 -60.5t-25 -78z" />
<glyph unicode="b" horiz-adv-x="1146" d="M144 0v1508h184v-620q66 76 148 121t190 45q91 0 164 -35t124.5 -101t78.5 -160t27 -212q0 -126 -31 -229.5t-88.5 -176.5t-140.5 -113.5t-187 -40.5q-102 0 -172 38t-123 109l-9 -94q-8 -39 -47 -39h-118zM328 250q50 -68 110 -95.5t131 -27.5q146 0 224 103.5t78 308.5 q0 190 -69 279.5t-197 89.5q-89 0 -155 -41t-122 -115v-502z" />
<glyph unicode="c" horiz-adv-x="977" d="M86 519q0 116 32.5 214t94 169.5t152.5 111.5t208 40q110 0 193.5 -35.5t147.5 -99.5l-48 -66q-8 -11 -16 -17.5t-23 -6.5t-33.5 13t-45.5 28.5t-66.5 28t-96.5 12.5q-77 0 -135.5 -27t-98 -78t-59.5 -124t-20 -163q0 -94 21.5 -167t61 -123t95.5 -76t125 -26 q67 0 110 16t71 35.5t47 35t38 15.5q23 0 35 -17l51 -67q-34 -42 -77 -71.5t-92.5 -49.5t-104 -29t-110.5 -9q-97 0 -180.5 35.5t-145 104t-96.5 167.5t-35 226z" />
<glyph unicode="d" horiz-adv-x="1146" d="M88 515q0 115 31 213.5t88 171.5t140.5 114.5t188.5 41.5q95 0 163 -32.5t121 -89.5v574h182v-1508h-108q-39 0 -49 38l-17 126q-67 -80 -151.5 -129t-196.5 -49q-89 0 -161.5 34t-124 100.5t-79 166t-27.5 228.5zM276 515q0 -101 17.5 -173.5t51.5 -119t83 -68 t112 -21.5q90 0 157 41t123 116v501q-51 69 -110.5 95.5t-132.5 26.5q-144 0 -222.5 -103t-78.5 -295z" />
<glyph unicode="e" horiz-adv-x="1081" d="M85 540q0 109 33.5 203t95.5 163t152.5 108.5t202.5 39.5q93 0 172.5 -31.5t137.5 -90.5t90.5 -145.5t32.5 -196.5q0 -43 -9 -57.5t-35 -14.5h-693q3 -98 27 -171t67 -121.5t102.5 -72.5t133.5 -24q69 0 118.5 16t85.5 34.5t60 34t41 15.5q23 0 35 -17l51 -67 q-34 -41 -81 -71t-100.5 -49.5t-111 -29t-113.5 -9.5q-108 0 -198.5 36t-156.5 106.5t-103 174t-37 237.5zM271 632h568q0 63 -18 116t-52.5 91.5t-83.5 59.5t-112 21q-131 0 -207 -76.5t-95 -211.5z" />
<glyph unicode="f" horiz-adv-x="717" d="M48 942v75h164v100q0 89 25 158.5t71.5 117t112 72t147.5 24.5q70 0 128 -21l-4 -91q-2 -25 -27 -26.5t-67 -1.5q-47 0 -85.5 -12.5t-66 -40t-42.5 -72.5t-15 -112v-95h299v-132h-293v-885h-183v882l-115 13q-22 5 -35.5 16t-13.5 31z" />
<glyph unicode="g" horiz-adv-x="1064" d="M63 -99q0 77 48.5 130t132.5 85q-46 20 -73.5 54.5t-27.5 92.5q0 23 8.5 47t25.5 48t41.5 45.5t56.5 37.5q-77 43 -119.5 114.5t-42.5 165.5q0 76 29 138t81 105.5t124.5 67t158.5 23.5q68 0 126 -15t106 -44h282v-67q0 -34 -43 -43l-118 -16q35 -68 35 -149 q0 -76 -29 -138t-80.5 -106t-123 -67.5t-155.5 -23.5q-73 0 -137 17q-33 -20 -50.5 -43t-17.5 -47q0 -37 30 -55.5t79.5 -27t112.5 -10.5t129 -6.5t129 -16t112 -37t79 -70.5t30 -117q0 -67 -33 -129t-95.5 -110.5t-153 -78t-204.5 -29.5t-199 22.5t-141 60.5t-84.5 87.5 t-28.5 104.5zM223 -76q0 -35 18 -65t55 -52t92.5 -35t131.5 -13q74 0 132.5 13.5t99 38t62 58.5t21.5 75q0 38 -21.5 61.5t-58 36.5t-84.5 19t-102 9t-109.5 6t-106.5 10q-57 -27 -93.5 -67.5t-36.5 -94.5zM280 716q0 -46 14.5 -84.5t43.5 -66t71 -43t97 -15.5t97 15.5 t70.5 43t43 66t14.5 84.5q0 95 -58 151.5t-167 56.5q-111 0 -168.5 -56.5t-57.5 -151.5z" />
<glyph unicode="h" horiz-adv-x="1142" d="M144 0v1508h184v-609q67 71 147.5 113t186.5 42q85 0 150 -28.5t108.5 -80t66 -124t22.5 -161.5v-660h-184v660q0 118 -53.5 183t-163.5 65q-81 0 -151 -39t-129 -105v-764h-184z" />
<glyph unicode="i" horiz-adv-x="491" d="M116 1363q0 27 10 51t27.5 42t40.5 28t50 10t50.5 -10t41.5 -28t29 -42t11 -51t-11 -50t-29 -41t-42 -28t-50 -10q-27 0 -50 10t-40.5 28t-27.5 41t-10 50zM155 0v1037h182v-1037h-182z" />
<glyph unicode="j" horiz-adv-x="491" d="M-73 -352l7 98q2 9 7 13.5t13 6t21 1t32 -0.5q80 0 114 37t34 120v1114h182v-1114q0 -62 -16.5 -116t-52.5 -94t-91.5 -63t-133.5 -23q-33 0 -60.5 5.5t-55.5 15.5zM116 1363q0 27 10 51t27.5 42t40.5 28t50 10t50 -10t41 -28t29 -42t11 -51t-11 -50t-29 -41t-41.5 -28 t-49.5 -10q-27 0 -50 10t-40.5 28t-27.5 41t-10 50z" />
<glyph unicode="k" horiz-adv-x="1040" d="M144 0v1508h184v-887h47q20 0 34 5.5t29 22.5l328 351q14 17 30 27t43 10h165l-382 -406q-28 -35 -60 -54q18 -12 33 -28.5t29 -36.5l405 -512h-163q-24 0 -40.5 7.5t-28.5 28.5l-341 425q-15 22 -30 28.5t-46 6.5h-52v-496h-184z" />
<glyph unicode="l" horiz-adv-x="483" d="M151 0v1508h182v-1508h-182z" />
<glyph unicode="m" horiz-adv-x="1684" d="M144 0v1037h110q39 0 48 -38l13 -106q57 71 129 116t166 45q104 0 169.5 -58.5t93.5 -157.5q23 56 58.5 97t79.5 67.5t93.5 39t100.5 12.5q82 0 146 -26.5t108.5 -76.5t68 -123t23.5 -168v-660h-183v660q0 122 -53 185t-154 63q-45 0 -85.5 -15.5t-71 -46.5t-48.5 -77.5 t-18 -108.5v-660h-183v660q0 125 -50.5 186.5t-146.5 61.5q-68 0 -125 -36t-105 -99v-773h-184z" />
<glyph unicode="n" horiz-adv-x="1142" d="M144 0v1037h110q39 0 48 -38l14 -112q34 38 72 68.5t80.5 52.5t90 34t103.5 12q85 0 150 -28.5t108.5 -80t66 -124t22.5 -161.5v-660h-184v660q0 118 -53.5 183t-163.5 65q-81 0 -151 -39t-129 -105v-764h-184z" />
<glyph unicode="o" horiz-adv-x="1161" d="M86 519q0 122 34.5 221t99 168.5t156 107.5t206.5 38q114 0 205 -38t155.5 -107.5t98.5 -168.5t34 -221q0 -123 -34 -221t-98.5 -168t-156 -107t-204.5 -37q-115 0 -206.5 37t-156 107t-99 168t-34.5 221zM273 518q0 -184 76.5 -287t232.5 -103q154 0 229.5 103t75.5 287 q0 185 -76 288.5t-229 103.5q-78 0 -136 -26.5t-96.5 -76.5t-57.5 -123.5t-19 -165.5z" />
<glyph unicode="p" horiz-adv-x="1147" d="M144 -351v1388h110q39 0 48 -38l16 -122q66 81 152 130t197 49q90 0 162.5 -34.5t124 -101.5t79 -166.5t27.5 -228.5q0 -115 -31 -213.5t-88.5 -170.5t-141 -113.5t-188.5 -41.5q-95 0 -163 32t-120 90v-459h-184zM328 250q50 -68 109.5 -95.5t132.5 -27.5 q145 0 223 103.5t78 294.5q0 101 -17.5 174t-51.5 119.5t-83 68t-112 21.5q-89 0 -156 -41t-123 -115v-502z" />
<glyph unicode="q" horiz-adv-x="1146" d="M88 515q0 115 31 213.5t88 171.5t140.5 114.5t188.5 41.5q101 0 171.5 -36t125.5 -100l12 79q10 38 49 38h108v-1388h-182v506q-65 -76 -148.5 -122.5t-191.5 -46.5q-89 0 -161.5 34t-124 100.5t-79 166t-27.5 228.5zM276 515q0 -101 17.5 -173.5t51.5 -119t83 -68 t112 -21.5q90 0 157 41t123 116v502q-49 66 -109.5 93.5t-133.5 27.5q-144 0 -222.5 -103t-78.5 -295z" />
<glyph unicode="r" horiz-adv-x="745" d="M144 0v1037h105q30 0 41 -11t15 -39l12 -158q49 106 121 166.5t175 60.5q33 0 63.5 -7.5t53.5 -22.5l-13 -136q-6 -26 -31 -26q-14 0 -42 6.5t-62 6.5q-49 0 -87.5 -14.5t-69 -42.5t-54.5 -69.5t-43 -95.5v-655h-184z" />
<glyph unicode="s" horiz-adv-x="886" d="M69 110l43 69q8 13 19 20.5t29 7.5t38.5 -14.5t50 -32t71 -31.5t104.5 -14q53 0 93 13.5t66.5 37t39.5 54.5t13 66q0 43 -23 71t-61 48t-86.5 35t-99.5 31t-99.5 37t-86.5 53t-61 78.5t-23 113.5q0 59 24.5 114t72 96.5t116 66t156.5 24.5q102 0 183.5 -32.5t141.5 -88.5 l-41 -67q-12 -23 -38 -22q-15 0 -35 11t-48 25t-67 26t-92 12q-46 0 -83 -12t-63 -32.5t-40 -47.5t-14 -59q0 -40 23 -66.5t61 -46t86 -34.5t99 -32t99 -37.5t86 -51t61 -75t23 -107.5q0 -72 -25.5 -132.5t-76 -105t-123 -70t-167.5 -25.5q-109 0 -197 35t-149 91z" />
<glyph unicode="t" horiz-adv-x="734" d="M44 925v72l170 22l42 320q3 15 14 25t28 10h92v-357h300v-132h-300v-623q0 -66 32 -97.5t82 -31.5q29 0 49.5 7.5t36 17t26.5 17t19 7.5q14 0 25 -17l54 -87q-47 -44 -113.5 -69t-137.5 -25q-123 0 -189 68.5t-66 197.5v635h-125q-16 0 -27.5 9.5t-11.5 30.5z" />
<glyph unicode="u" horiz-adv-x="1141" d="M134 376v661h182v-661q0 -118 54 -182.5t165 -64.5q81 0 151 38.5t129 105.5v764h182v-1037h-108q-39 0 -49 38l-15 112q-68 -75 -151.5 -120.5t-192.5 -45.5q-85 0 -150 28t-109 79t-66 124t-22 161z" />
<glyph unicode="v" horiz-adv-x="1056" d="M24 1037h148q23 0 37 -11t19 -27l264 -667q12 -38 21 -74t17 -72q8 36 17.5 72t22.5 74l267 667q6 16 20 27t34 11h142l-423 -1037h-164z" />
<glyph unicode="w" horiz-adv-x="1608" d="M34 1037h143q22 0 36.5 -11t19.5 -27l199 -667q17 -74 28 -139q9 34 18.5 68.5t21.5 70.5l219 672q5 15 18 25t31 10h80q20 0 33.5 -10t18.5 -25l214 -672q11 -36 20.5 -71t17.5 -68q5 34 12.5 68t17.5 71l202 667q5 16 19.5 27t34.5 11h137l-336 -1037h-144 q-27 0 -37 35l-230 703q-8 24 -13 48l-10 48l-10 -48t-14 -49l-233 -702q-9 -35 -41 -35h-137z" />
<glyph unicode="x" horiz-adv-x="1019" d="M24 0l363 532l-349 505h175q23 0 33 -7t18 -20l255 -389q8 30 26 57l223 328q9 14 20 22.5t26 8.5h168l-349 -495l363 -542h-175q-23 0 -35.5 12t-20.5 26l-261 406q-4 -14 -9 -28t-13 -25l-241 -353q-10 -14 -22 -26t-33 -12h-162z" />
<glyph unicode="y" horiz-adv-x="1055" d="M22 1037h157q24 0 37.5 -11.5t19.5 -26.5l277 -653q16 -43 28 -90q14 48 30 91l270 652q6 16 21 27t33 11h144l-578 -1343q-9 -20 -23 -32.5t-43 -12.5h-135l190 411z" />
<glyph unicode="z" horiz-adv-x="925" d="M59 0v76q0 13 6.5 31t18.5 34l565 753h-558v143h769v-78q0 -19 -6.5 -37.5t-17.5 -31.5l-563 -748h568v-142h-782z" />
<glyph unicode="{" horiz-adv-x="616" d="M70 583v109q68 0 103.5 41t35.5 107q0 51 -8 101t-17.5 100.5t-17.5 101.5t-8 104q0 71 21 130.5t63.5 102.5t105.5 66.5t146 23.5h55v-81q0 -20 -14.5 -29.5t-26.5 -9.5h-21q-80 0 -125 -51.5t-45 -140.5q0 -57 7.5 -110t16.5 -104t16.5 -101t7.5 -101 q0 -38 -11.5 -71.5t-32.5 -60t-49 -45.5t-62 -28q34 -9 62 -27.5t49 -45t32.5 -60t11.5 -71.5q0 -51 -7.5 -101t-16.5 -101t-16.5 -104t-7.5 -111q0 -89 45.5 -140t124.5 -51h21q12 0 26.5 -9.5t14.5 -29.5v-82h-55q-83 0 -146 24t-105.5 67t-63.5 102.5t-21 130.5 q0 53 8 103.5t17.5 101.5t17.5 101t8 101q0 65 -36 106.5t-103 41.5z" />
<glyph unicode="|" horiz-adv-x="513" d="M185 -357v1927h143v-1927h-143z" />
<glyph unicode="}" horiz-adv-x="616" d="M68 -214q0 20 14.5 29.5t26.5 9.5h20q80 0 125 51t45 140q0 57 -7 110.5t-16.5 104.5t-16.5 101t-7 101q0 38 11 71.5t32 60t49.5 45t62.5 27.5q-34 9 -62.5 28t-49.5 45.5t-32 60t-11 71.5q0 51 7 101t16.5 101t16.5 104t7 110q0 88 -45 140t-125 52h-20q-11 0 -26 9 t-15 30v81h55q83 0 146 -23.5t105.5 -66.5t63.5 -102.5t21 -130.5q0 -53 -8.5 -104t-17.5 -101.5t-17 -100.5t-8 -101q0 -66 35.5 -107t103.5 -41v-109q-68 0 -103.5 -41.5t-35.5 -106.5q0 -51 8 -101t17 -101t17.5 -101.5t8.5 -103.5q0 -71 -21 -130.5t-63.5 -102.5 t-105.5 -67t-146 -24h-55v82z" />
<glyph unicode="~" d="M123 424q0 69 19 126t54 98t87 64t118 23q53 0 105.5 -17t101 -36.5t92.5 -36.5t81 -17q67 0 104.5 43.5t38.5 114.5h147q0 -69 -19 -126t-54 -98t-87.5 -63.5t-117.5 -22.5q-53 0 -105.5 16.5t-101 36.5t-92.5 37t-81 17q-67 0 -104.5 -43t-38.5 -116h-147z" />
<glyph unicode="&#xa1;" horiz-adv-x="516" d="M128 926q0 28 9.5 50.5t27 40t40.5 27.5t51 10q27 0 50 -10t40.5 -27.5t27.5 -40.5t10 -50t-10 -50.5t-27.5 -41t-40.5 -27.5t-50 -10q-28 0 -51 10t-40.5 27.5t-27 41t-9.5 50.5zM172 203q0 46 2 89.5t5 88t7.5 91.5t11.5 100h123q6 -53 10.5 -100t7.5 -91.5t5 -88 t2 -89.5v-554h-174v554z" />
<glyph unicode="&#xa2;" d="M142 518q0 114 34 209.5t98.5 166.5t159 112.5t216.5 45.5l13 182q2 20 15 35t35 15h68l-16 -238q84 -11 151.5 -44t121.5 -83l-47 -64q-8 -11 -15.5 -16.5t-22.5 -5.5q-12 0 -28.5 8.5t-39.5 20.5t-55 24.5t-75 19.5l-54 -780q65 4 108 20t73 34t49.5 31.5t36.5 13.5 q11 0 20 -4.5t14 -11.5l51 -65q-61 -73 -156 -111t-206 -45l-12 -179q-2 -19 -15.5 -34.5t-35.5 -15.5h-68l17 230q-94 10 -174 50.5t-138 108t-90.5 160.5t-32.5 210zM324 518q0 -165 69 -263.5t193 -122.5l54 779q-78 -6 -137.5 -35t-99 -80t-59.5 -121t-20 -157z" />
<glyph unicode="&#xa3;" d="M49 629v59q0 27 16.5 45.5t46.5 18.5h137v267q0 96 27.5 180.5t84 147.5t140 99.5t195.5 36.5q80 0 142.5 -20t110.5 -55t83.5 -81t60.5 -99l-74 -47q-27 -13 -50 -11.5t-43 25.5q-19 28 -40.5 51.5t-48 41.5t-61 28t-80.5 10q-65 0 -113 -21.5t-81 -61.5t-49.5 -96 t-16.5 -126v-269h451v-74q0 -18 -15 -33.5t-38 -15.5h-398v-249q0 -77 -29 -133t-80 -102q29 5 58.5 9t58.5 4h694v-78q0 -14 -6 -28t-16.5 -26t-25 -19t-32.5 -7h-987v118q35 10 67 27.5t56.5 43.5t39.5 61.5t15 82.5v296h-200z" />
<glyph unicode="&#xa4;" d="M135 1052l93 92l155 -156q45 32 98.5 49.5t112.5 17.5q58 0 110.5 -17t97.5 -47l155 156l92 -93l-155 -155q32 -45 49.5 -98t17.5 -113q0 -58 -17 -110t-47 -97l156 -154l-94 -95l-155 156q-45 -31 -98 -48t-112 -17q-58 0 -110 16.5t-96 46.5l-158 -157l-91 94l155 155 q-31 45 -48.5 98.5t-17.5 111.5t17 110.5t47 96.5zM364 688q0 -47 17.5 -88.5t49.5 -72.5t73.5 -49.5t89.5 -18.5t90.5 18.5t74 49.5t49.5 72.5t18 88.5q0 48 -18 90t-49.5 74t-74 50t-90.5 18t-89.5 -18t-73.5 -50t-49.5 -74t-17.5 -90z" />
<glyph unicode="&#xa5;" d="M50 1467h152q27 0 43.5 -12.5t24.5 -33.5l284 -579q14 -35 24 -64.5t18 -59.5q12 56 39 124l283 579q8 17 24.5 31.5t42.5 14.5h153l-419 -827h315v-104h-349v-108h349v-105h-349v-323h-182v323h-349v105h349v108h-349v104h315z" />
<glyph unicode="&#xa6;" horiz-adv-x="519" d="M188 -351v814h143v-814h-143zM188 757v813h143v-813h-143z" />
<glyph unicode="&#xa7;" horiz-adv-x="1013" d="M103 715q0 81 44 144.5t140 99.5q-51 39 -83.5 92.5t-32.5 130.5q0 59 24.5 114t71.5 96t116.5 65.5t157.5 24.5q101 0 182.5 -32t140.5 -88l-40 -67q-14 -23 -38 -22q-15 0 -35 11t-48 25t-67 26t-92 12q-49 0 -88 -13t-67 -34.5t-42 -50t-14 -60.5q0 -39 25 -68 t65.5 -53.5t92.5 -46.5t106 -45.5t106 -51.5t92.5 -64.5t65.5 -83.5t25 -109q0 -83 -40 -148t-126 -104q51 -38 82.5 -88.5t31.5 -120.5q0 -72 -25.5 -133t-75 -105.5t-123 -70t-167.5 -25.5q-109 0 -197 35.5t-149 90.5l43 70q8 13 19.5 20t28.5 7q18 0 38.5 -14t51 -32 t73.5 -32.5t107 -14.5q52 0 92.5 13t68.5 36t42 54.5t14 70.5q0 46 -25.5 80t-67.5 60t-95.5 47.5t-109.5 44t-109.5 48.5t-95.5 62t-68 83.5t-26 113.5zM265 743q0 -52 36 -88.5t92 -65.5t123.5 -56t130.5 -59q56 27 80.5 65.5t24.5 86.5q0 37 -15.5 65.5t-42 52t-62.5 43 t-76.5 37.5t-83.5 36t-84 38q-67 -31 -95 -68.5t-28 -86.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="819" d="M99 1320q0 24 9.5 45t25.5 37t36 25.5t43 9.5q24 0 45 -9.5t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5t-25.5 -35.5t-37 -24.5t-45 -9.5q-23 0 -43 9.5t-36 24.5t-25.5 35.5t-9.5 44.5zM489 1320q0 24 9.5 45t25 37t36.5 25.5t44 9.5q24 0 45 -9.5t36.5 -25.5t25 -37 t9.5 -45t-9.5 -44.5t-25 -35.5t-36.5 -24.5t-45 -9.5t-44.5 9.5t-36 24.5t-25 35.5t-9.5 44.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1702" d="M103 733q0 103 27 199t76 179.5t117 151.5t151 117.5t178.5 76t198.5 26.5t199 -26.5t179 -76t151.5 -117.5t117 -151.5t75 -179t26.5 -199.5q0 -102 -26.5 -198t-75 -179t-117 -151t-151.5 -117t-179 -75.5t-199 -26.5t-198.5 26.5t-178.5 75.5t-151 117t-117 151 t-76 178.5t-27 198.5zM207 733q0 -91 22.5 -175t64 -157t100.5 -132.5t130 -102t154 -65.5t173 -23t173.5 23t155.5 65.5t131 102t101 132.5t65 157t23 175q0 137 -50.5 257.5t-138 210t-206.5 141.5t-254 52q-90 0 -173 -23.5t-154 -66t-130 -103t-100.5 -134t-64 -158.5 t-22.5 -176zM409 735q0 103 34.5 190t97 149t148 96t185.5 34q112 0 189.5 -34t135.5 -92l-47 -66q-5 -6 -13.5 -12.5t-19.5 -6.5q-14 0 -30 11.5t-41.5 25t-65 25.5t-98.5 12q-73 0 -130.5 -23t-98.5 -66t-62.5 -104.5t-21.5 -138.5q0 -79 22 -141t61 -103.5t93 -64 t117 -22.5q53 0 89 8.5t63 20t49 26t48 26.5q15 1 28 -11l61 -65q-58 -68 -142.5 -105.5t-203.5 -37.5q-100 0 -182.5 35t-141.5 97.5t-91 148.5t-32 188z" />
<glyph unicode="&#xaa;" horiz-adv-x="752" d="M102 1017q0 35 19.5 69t65 62t119.5 45t182 19v38q0 65 -29 95.5t-87 30.5q-39 0 -65 -9t-45 -20t-34 -19.5t-31 -8.5q-14 0 -23.5 8t-15.5 18l-23 43q52 50 114.5 73.5t137.5 23.5q55 0 98 -17.5t73 -48t45 -73.5t15 -96v-390h-61q-18 0 -27.5 5.5t-17.5 24.5l-12 50 q-25 -22 -49 -38t-49 -28t-54.5 -17.5t-63.5 -5.5q-39 0 -72 10t-57.5 31t-38.5 52t-14 73zM229 1023q0 -43 27.5 -60.5t68.5 -17.5q52 0 90 19.5t73 55.5v108q-72 -3 -121 -11.5t-79.5 -22.5t-44.5 -32t-14 -39z" />
<glyph unicode="&#xab;" horiz-adv-x="878" d="M102 530v24l255 398l60 -28q24 -11 28.5 -32.5t-10.5 -44.5l-162 -267q-14 -26 -28 -37q14 -13 28 -38l162 -267q14 -24 9.5 -45t-27.5 -32l-60 -29zM405 530v24l255 398l60 -28q24 -11 28.5 -32.5t-10.5 -44.5l-162 -267q-14 -26 -28 -37q14 -13 28 -38l162 -267 q14 -24 9.5 -45t-27.5 -32l-60 -29z" />
<glyph unicode="&#xac;" d="M152 618v139h880v-434h-154v295h-726z" />
<glyph unicode="&#xae;" horiz-adv-x="1702" d="M103 733q0 103 27 199t76 179.5t117 151.5t151 117.5t178.5 76t198.5 26.5t199 -26.5t179 -76t151.5 -117.5t117 -151.5t75 -179t26.5 -199.5q0 -102 -26.5 -198t-75 -179t-117 -151t-151.5 -117t-179 -75.5t-199 -26.5t-198.5 26.5t-178.5 75.5t-151 117t-117 151 t-76 178.5t-27 198.5zM207 733q0 -91 22.5 -175t64 -157t100.5 -132.5t130 -102t154 -65.5t173 -23t173.5 23t155.5 65.5t131 102t101 132.5t65 157t23 175q0 137 -50.5 257.5t-138 210t-206.5 141.5t-254 52q-90 0 -173 -23.5t-154 -66t-130 -103t-100.5 -134t-64 -158.5 t-22.5 -176zM545 279v913h294q177 0 261.5 -64t84.5 -189q0 -96 -54.5 -163.5t-162.5 -92.5q17 -11 30 -26.5t24 -35.5l233 -342h-152q-34 0 -49 25l-207 309q-9 13 -22 20.5t-40 7.5h-80v-362h-160zM705 758h117q114 0 160 41.5t46 119.5q0 38 -9.5 66.5t-31.5 47 t-58.5 27.5t-89.5 9h-134v-311z" />
<glyph unicode="&#xaf;" horiz-adv-x="819" d="M116 1261v119h587v-119h-587z" />
<glyph unicode="&#xb0;" horiz-adv-x="848" d="M91 1155q0 69 25.5 129.5t70.5 105t105.5 70t131.5 25.5t131.5 -25.5t106 -70t71.5 -105t26 -129.5q0 -68 -26 -127.5t-71.5 -104t-106 -71t-131.5 -26.5t-131.5 26.5t-105.5 71t-70.5 104t-25.5 127.5zM220 1154q0 -43 15.5 -80.5t43 -65t65 -43.5t80.5 -16t80.5 16 t65 43.5t43 65t15.5 80.5t-15.5 81t-43 66t-65 44t-80.5 16t-80.5 -16t-65 -44t-43 -66t-15.5 -81z" />
<glyph unicode="&#xb1;" d="M103 82v138h981v-138h-981zM103 707v139h415v385h150v-385h416v-139h-416v-377h-150v377h-415z" />
<glyph unicode="&#xb2;" horiz-adv-x="684" d="M76 922v44q0 13 5.5 27.5t17.5 26.5l227 225q51 51 86 105t35 110q0 52 -30.5 80t-75.5 28q-48 0 -77 -24.5t-43 -67.5q-10 -18 -23.5 -25.5t-39.5 -3.5l-73 13q15 109 86 163t176 54q53 0 96.5 -15.5t74.5 -43t48 -67t17 -87.5q0 -41 -13 -76.5t-34.5 -67t-49 -61.5 t-58.5 -61l-167 -168q25 6 49 10t45 4h200q20 0 32 -11.5t12 -31.5v-79h-523z" />
<glyph unicode="&#xb3;" horiz-adv-x="684" d="M73 1111l56 25q23 9 42.5 4.5t28.5 -21.5l13 -26t23 -31.5t38 -26.5t58 -11q32 0 56 10.5t40 26.5t24.5 36.5t8.5 41.5q0 31 -9 53t-29 36.5t-53 21.5t-81 7v89q89 1 125.5 31.5t36.5 84.5q0 51 -30 77.5t-79 26.5t-78.5 -24t-41.5 -65q-10 -20 -21.5 -27t-37.5 -3 l-69 13q7 54 29.5 94.5t56 67.5t76.5 41t93 14q52 0 94 -15t72.5 -41t47 -61t16.5 -76q0 -131 -121 -177q137 -38 137 -163q0 -56 -21.5 -99t-57 -72t-82 -44t-95.5 -15q-58 0 -101 12.5t-74.5 38t-53.5 62t-37 84.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="819" d="M297 1191l152 248q14 24 31 34.5t49 10.5h179l-239 -264q-14 -15 -28 -22t-35 -7h-109z" />
<glyph unicode="&#xb5;" horiz-adv-x="1330" d="M184 -273v1310h184v-627q0 -117 53.5 -185t163.5 -68q94 0 164 50.5t117 142.5v687h183v-756q0 -80 40.5 -117t108.5 -37h60v-74q0 -24 -32 -43.5t-91 -19.5q-43 0 -82.5 12t-72 38.5t-56 65.5t-33.5 94q-61 -92 -139.5 -143t-177.5 -51q-77 0 -133.5 24t-95.5 70 q6 -42 8.5 -85.5t2.5 -80.5v-285h-91q-39 0 -60 20.5t-21 57.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="1434" d="M72 1074q0 88 32 160.5t92 124t145 80t191 28.5h830v-156h-224v-1517h-161v1517h-284v-1517h-161v882q-106 0 -191 31t-145 85t-92 126.5t-32 155.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="532" d="M114 607q0 32 11.5 60.5t32.5 49t48 33t58 12.5q32 0 60.5 -12.5t49 -33t33 -49t12.5 -60.5q0 -31 -12.5 -58.5t-33 -48.5t-49 -32.5t-60.5 -11.5q-31 0 -58 11.5t-48 32.5t-32.5 48.5t-11.5 58.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="819" d="M248 -334l17 56q6 18 24 19q6 0 14 -3.5t20 -8t28 -8.5t37 -4q43 0 65 17.5t22 44.5q0 39 -43 54.5t-122 26.5l56 166h103l-29 -103q95 -19 137.5 -55t42.5 -91q0 -33 -16.5 -59.5t-46 -45t-70.5 -28.5t-90 -10q-42 0 -79.5 9t-69.5 23z" />
<glyph unicode="&#xb9;" horiz-adv-x="684" d="M107 1476l228 195h110v-662h134v-87h-417v87h150v451l5 45l-110 -91q-9 -7 -18 -9t-17 -0.5t-15 6t-10 8.5z" />
<glyph unicode="&#xba;" horiz-adv-x="823" d="M96 1167q0 73 22.5 132t64 100t100 63t131.5 22q72 0 129.5 -22t98.5 -63t63.5 -100t22.5 -132q0 -74 -22.5 -133t-63.5 -100.5t-99 -63.5t-129 -22q-73 0 -131.5 22t-100 63.5t-64 101t-22.5 132.5zM240 1166q0 -102 42 -156t132 -54q86 0 128.5 54t42.5 156 t-42.5 155.5t-128.5 53.5q-90 0 -132 -53t-42 -156z" />
<glyph unicode="&#xbb;" horiz-adv-x="879" d="M131.5 193q-4.5 21 9.5 45l162 267q8 12 15 22t15 16q-8 5 -15 14.5t-15 22.5l-162 267q-14 24 -9.5 44.5t28.5 32.5l59 28l255 -398v-24l-255 -398l-59 29q-24 11 -28.5 32zM434.5 193q-4.5 21 9.5 45l162 267q8 12 15 22t15 16q-8 5 -15 14.5t-15 22.5l-162 267 q-14 24 -9.5 44.5t28.5 32.5l59 28l255 -398v-24l-255 -398l-59 29q-24 11 -28.5 32z" />
<glyph unicode="&#xbc;" horiz-adv-x="1503" d="M90 1274l228 195h110v-662h134v-87h-417v87h150v451l5 45l-110 -91q-9 -7 -18 -9t-17 -0.5t-15 6t-10 8.5zM256 0l838 1405q18 30 42 46t56 16h79l-842 -1413q-19 -32 -42 -43t-53 -11h-78zM834 267l351 479h126v-473h112v-66q0 -11 -7.5 -19t-19.5 -8h-85v-180h-111v180 h-313q-19 0 -29.5 8.5t-13.5 20.5zM959 273h241v247q0 41 7 90z" />
<glyph unicode="&#xbd;" horiz-adv-x="1485" d="M92 1274l228 195h110v-662h134v-87h-417v87h150v451l5 45l-110 -91q-9 -7 -18 -9t-17 -0.5t-15 6t-10 8.5zM214 0l838 1405q18 30 42 46t56 16h79l-842 -1413q-19 -32 -42 -43t-53 -11h-78zM870 0v44q0 13 5.5 27.5t17.5 26.5l227 225q51 51 86 105t35 110q0 52 -30.5 80 t-75.5 28q-48 0 -77 -24.5t-43 -67.5q-10 -18 -23.5 -25.5t-39.5 -3.5l-73 13q15 109 86 163t176 54q53 0 96.5 -15.5t74.5 -43t48 -67t17 -87.5q0 -41 -13 -76.5t-34.5 -67t-49 -61.5t-58.5 -61l-167 -168q25 6 49 10t45 4h200q20 0 32 -11.5t12 -31.5v-79h-523z" />
<glyph unicode="&#xbe;" horiz-adv-x="1519" d="M90 909l56 25q23 9 42.5 4.5t28.5 -21.5l13 -26t23 -31.5t38 -26.5t58 -11q32 0 56 10.5t40 26.5t24.5 36.5t8.5 41.5q0 31 -9 53t-29 36.5t-53 21.5t-81 7v89q89 1 125.5 31.5t36.5 84.5q0 51 -30 77.5t-79 26.5t-78.5 -24t-41.5 -65q-10 -20 -21.5 -27t-37.5 -3l-69 13 q7 54 29.5 94.5t56 67.5t76.5 41t93 14q52 0 94 -15t72.5 -41t47 -61t16.5 -76q0 -131 -121 -177q137 -38 137 -163q0 -56 -21.5 -99t-57 -72t-82 -44t-95.5 -15q-58 0 -101 12.5t-74.5 38t-53.5 62t-37 84.5zM279 0l838 1405q18 30 42 46t56 16h79l-842 -1413 q-19 -32 -42 -43t-53 -11h-78zM851 267l351 479h126v-473h112v-66q0 -11 -7.5 -19t-19.5 -8h-85v-180h-111v180h-313q-19 0 -29.5 8.5t-13.5 20.5zM976 273h241v247q0 41 7 90z" />
<glyph unicode="&#xbf;" horiz-adv-x="901" d="M85 -38q0 78 23 132t58.5 93t77 67t78 52.5t63 49.5t30.5 59l18 157h124l12 -171q4 -48 -17.5 -81t-56 -60t-76.5 -51.5t-78.5 -55t-61 -72.5t-24.5 -104q0 -45 17.5 -80t47.5 -60t70 -38.5t86 -13.5q62 0 107 15.5t76 34t50 33.5t31 15q27 0 39 -23l48 -77 q-32 -30 -70 -56.5t-83 -47t-98.5 -32.5t-114.5 -12q-81 0 -149.5 23t-119 64.5t-79 102t-28.5 137.5zM371 925q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5t-40.5 27 t-26.5 40.5t-9.5 51z" />
<glyph unicode="&#xc0;" horiz-adv-x="1386" d="M6 0l587 1467h201l586 -1467h-153q-27 0 -43.5 13.5t-24.5 33.5l-137 354h-658l-138 -354q-6 -18 -23.5 -32.5t-42.5 -14.5h-154zM317 1896h195q34 0 51 -5t35 -27l233 -281h-131q-23 0 -35.5 2.5t-27.5 17.5zM419 545h548l-231 597q-10 27 -21.5 62t-21.5 75 q-22 -83 -44 -138z" />
<glyph unicode="&#xc1;" horiz-adv-x="1386" d="M6 0l587 1467h201l586 -1467h-153q-27 0 -43.5 13.5t-24.5 33.5l-137 354h-658l-138 -354q-6 -18 -23.5 -32.5t-42.5 -14.5h-154zM419 545h548l-231 597q-10 27 -21.5 62t-21.5 75q-22 -83 -44 -138zM551 1583l236 281q17 23 35 27.5t52 4.5h206l-334 -292 q-15 -14 -29.5 -17.5t-34.5 -3.5h-131z" />
<glyph unicode="&#xc2;" horiz-adv-x="1386" d="M6 0l587 1467h201l586 -1467h-153q-27 0 -43.5 13.5t-24.5 33.5l-137 354h-658l-138 -354q-6 -18 -23.5 -32.5t-42.5 -14.5h-154zM369 1583l245 278h164l243 -278h-133q-12 0 -29 4.5t-34 25.5l-120 144l-12 19l-13 -19l-119 -144q-17 -20 -34.5 -25t-29.5 -5h-128z M419 545h548l-231 597q-10 27 -21.5 62t-21.5 75q-22 -83 -44 -138z" />
<glyph unicode="&#xc3;" horiz-adv-x="1386" d="M6 0l587 1467h201l586 -1467h-153q-27 0 -43.5 13.5t-24.5 33.5l-137 354h-658l-138 -354q-6 -18 -23.5 -32.5t-42.5 -14.5h-154zM369 1585q0 44 13 85t36.5 72.5t58 49.5t79.5 18q46 0 83 -16.5t68 -36.5t58.5 -37t56.5 -17q24 0 41.5 8.5t30 23.5t18.5 33.5t6 36.5h111 q0 -44 -12 -84.5t-36 -71.5t-59 -49.5t-80 -18.5q-46 0 -82.5 17t-67.5 37t-58.5 37t-56.5 17q-24 0 -41 -9.5t-29 -24.5t-18 -33t-6 -37h-114zM419 545h548l-231 597q-10 27 -21.5 62t-21.5 75q-22 -83 -44 -138z" />
<glyph unicode="&#xc4;" horiz-adv-x="1386" d="M6 0l587 1467h201l586 -1467h-153q-27 0 -43.5 13.5t-24.5 33.5l-137 354h-658l-138 -354q-6 -18 -23.5 -32.5t-42.5 -14.5h-154zM366 1706q0 24 9 45t24.5 36t35.5 24.5t42 9.5q23 0 43.5 -9.5t36.5 -24.5t25 -36t9 -45q0 -23 -9 -42.5t-25 -35t-37 -24.5t-43 -9t-42 9 t-35.5 24.5t-24.5 35t-9 42.5zM419 545h548l-231 597q-10 27 -21.5 62t-21.5 75q-22 -83 -44 -138zM794 1706q0 24 8.5 45t24 36t36 24.5t44.5 9.5q23 0 43 -9.5t35.5 -24.5t24.5 -36t9 -45q0 -23 -9 -42.5t-24.5 -35t-36 -24.5t-42.5 -9q-24 0 -44.5 9t-36 24.5t-24 35 t-8.5 42.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1386" d="M6 0l587 1467h201l586 -1467h-153q-27 0 -43.5 13.5t-24.5 33.5l-137 354h-658l-138 -354q-6 -18 -23.5 -32.5t-42.5 -14.5h-154zM419 545h548l-231 597q-10 27 -21.5 62t-21.5 75q-22 -83 -44 -138zM499 1738q0 40 16 73.5t42 57t61 37t74 13.5q40 0 75.5 -13.5t62 -37 t42.5 -57t16 -73.5t-16 -73t-42.5 -56.5t-62.5 -36.5t-75 -13t-74 13t-61 36.5t-42 56.5t-16 73zM590 1738q0 -45 27.5 -74t77.5 -29q46 0 74 29t28 74q0 46 -28 74.5t-74 28.5q-49 0 -77 -28.5t-28 -74.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1905" d="M-6 0l774 1467h1047v-161h-766l60 -488h571v-155h-552l62 -501h626l-1 -162h-791l-50 401h-570l-184 -355q-10 -20 -29.5 -33t-46.5 -13h-150zM478 545h478l-97 774q-13 -43 -27.5 -79t-28.5 -69z" />
<glyph unicode="&#xc7;" horiz-adv-x="1368" d="M97 733q0 166 52.5 304.5t147 237.5t226.5 154t292 55q157 0 276.5 -50.5t211.5 -136.5l-66 -91q-6 -10 -16 -16.5t-26 -6.5q-18 0 -45 20t-70 44.5t-107.5 44.5t-158.5 20q-114 0 -208 -39.5t-162 -114.5t-106 -182.5t-38 -242.5q0 -137 39.5 -244.5t107.5 -182 t160.5 -113t200.5 -38.5q66 0 118.5 7.5t97 24t83 41.5t76.5 60q17 15 35 15q16 0 28 -13l79 -85q-84 -97 -200 -154.5t-278 -65.5l-18 -62q95 -19 137.5 -55t42.5 -91q0 -33 -16.5 -59.5t-45.5 -45t-70 -28.5t-91 -10q-42 0 -79.5 9t-69.5 23l17 56q6 18 24 19q6 0 14 -3.5 t20 -8t28 -8.5t37 -4q43 0 65 17.5t22 44.5q0 39 -43 54.5t-122 26.5l42 126q-146 9 -265 67.5t-203.5 156.5t-130 231t-45.5 292z" />
<glyph unicode="&#xc8;" horiz-adv-x="1182" d="M187 0v1467h905v-161h-706v-488h571v-155h-571v-501h707l-1 -162h-905zM276 1896h195q34 0 51 -5t35 -27l233 -281h-131q-23 0 -35.5 2.5t-27.5 17.5z" />
<glyph unicode="&#xc9;" horiz-adv-x="1182" d="M187 0v1467h905v-161h-706v-488h571v-155h-571v-501h707l-1 -162h-905zM510 1583l236 281q17 23 35 27.5t52 4.5h206l-334 -292q-15 -14 -29.5 -17.5t-34.5 -3.5h-131z" />
<glyph unicode="&#xca;" horiz-adv-x="1182" d="M187 0v1467h905v-161h-706v-488h571v-155h-571v-501h707l-1 -162h-905zM329 1583l245 278h164l243 -278h-133q-12 0 -29 4.5t-34 25.5l-120 144l-12 19l-13 -19l-119 -144q-17 -20 -34.5 -25t-29.5 -5h-128z" />
<glyph unicode="&#xcb;" horiz-adv-x="1182" d="M187 0v1467h905v-161h-706v-488h571v-155h-571v-501h707l-1 -162h-905zM326 1706q0 24 9 45t24.5 36t35.5 24.5t42 9.5q23 0 43.5 -9.5t36.5 -24.5t25 -36t9 -45q0 -23 -9 -42.5t-25 -35t-37 -24.5t-43 -9t-42 9t-35.5 24.5t-24.5 35t-9 42.5zM754 1706q0 24 8.5 45 t24 36t36 24.5t44.5 9.5q23 0 43 -9.5t35.5 -24.5t24.5 -36t9 -45q0 -23 -9 -42.5t-24.5 -35t-36 -24.5t-42.5 -9q-24 0 -44.5 9t-36 24.5t-24 35t-8.5 42.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="573" d="M-86 1896h195q34 0 51 -5t35 -27l233 -281h-131q-23 0 -35.5 2.5t-27.5 17.5zM187 0v1467h199v-1467h-199z" />
<glyph unicode="&#xcd;" horiz-adv-x="573" d="M148 1583l236 281q17 23 35 27.5t52 4.5h206l-334 -292q-15 -14 -29.5 -17.5t-34.5 -3.5h-131zM187 0v1467h199v-1467h-199z" />
<glyph unicode="&#xce;" horiz-adv-x="573" d="M-34 1583l245 278h164l243 -278h-133q-12 0 -29 4.5t-34 25.5l-120 144l-12 19l-13 -19l-119 -144q-17 -20 -34.5 -25t-29.5 -5h-128zM187 0v1467h199v-1467h-199z" />
<glyph unicode="&#xcf;" horiz-adv-x="573" d="M-37 1706q0 24 9 45t24.5 36t35.5 24.5t42 9.5q23 0 43.5 -9.5t36.5 -24.5t25 -36t9 -45q0 -23 -9 -42.5t-25 -35t-37 -24.5t-43 -9t-42 9t-35.5 24.5t-24.5 35t-9 42.5zM187 0v1467h199v-1467h-199zM391 1706q0 24 8.5 45t24 36t36 24.5t44.5 9.5q23 0 43 -9.5 t35.5 -24.5t24.5 -36t9 -45q0 -23 -9 -42.5t-24.5 -35t-36 -24.5t-42.5 -9q-24 0 -44.5 9t-36 24.5t-24 35t-8.5 42.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1573" d="M3 682v117h201v668h549q162 0 295 -52.5t228 -149.5t147.5 -232t52.5 -300t-52.5 -300t-147.5 -231.5t-228.5 -149t-294.5 -52.5h-549v682h-201zM403 161h350q118 0 214 39t163.5 112t104.5 179.5t37 241.5t-37 241.5t-104.5 180.5t-164 113t-213.5 39h-350v-508h297 v-117h-297v-521z" />
<glyph unicode="&#xd1;" horiz-adv-x="1563" d="M187 0v1467h103q13 0 22.5 -1.5t16.5 -5t14 -10t15 -16.5l849 -1105q-2 27 -3.5 52t-1.5 47v1039h174v-1467h-99q-24 0 -39.5 8t-31.5 28l-849 1106q2 -26 3 -50.5t1 -45.5v-1046h-174zM473 1584q0 44 13 85t36.5 72.5t58 49.5t79.5 18q46 0 83 -16.5t68 -36.5t58.5 -37 t56.5 -17q24 0 41.5 8.5t30 23.5t18.5 33.5t6 36.5h111q0 -44 -12 -84.5t-36 -71.5t-59 -49.5t-80 -18.5q-46 0 -82.5 17t-67.5 37t-58.5 37t-56.5 17q-24 0 -41 -9.5t-29 -24.5t-18 -33t-6 -37h-114z" />
<glyph unicode="&#xd2;" horiz-adv-x="1639" d="M96 733q0 165 52.5 303t147.5 237.5t228 155t295 55.5t295.5 -55.5t229 -155t147.5 -237.5t52 -303t-52 -302.5t-147.5 -237t-229 -154t-295.5 -54.5t-295 54.5t-228 154t-147.5 237t-52.5 302.5zM299 733q0 -135 37 -242t105 -181t164 -113.5t214 -39.5q119 0 215 39.5 t164 113.5t104.5 181t36.5 242t-36.5 242.5t-104.5 182t-164 114.5t-215 40q-118 0 -214 -40t-164 -114.5t-105 -182t-37 -242.5zM445 1896h195q34 0 51 -5t35 -27l233 -281h-131q-23 0 -35.5 2.5t-27.5 17.5z" />
<glyph unicode="&#xd3;" horiz-adv-x="1639" d="M96 733q0 165 52.5 303t147.5 237.5t228 155t295 55.5t295.5 -55.5t229 -155t147.5 -237.5t52 -303t-52 -302.5t-147.5 -237t-229 -154t-295.5 -54.5t-295 54.5t-228 154t-147.5 237t-52.5 302.5zM299 733q0 -135 37 -242t105 -181t164 -113.5t214 -39.5q119 0 215 39.5 t164 113.5t104.5 181t36.5 242t-36.5 242.5t-104.5 182t-164 114.5t-215 40q-118 0 -214 -40t-164 -114.5t-105 -182t-37 -242.5zM679 1583l236 281q17 23 35 27.5t52 4.5h206l-334 -292q-15 -14 -29.5 -17.5t-34.5 -3.5h-131z" />
<glyph unicode="&#xd4;" horiz-adv-x="1639" d="M96 733q0 165 52.5 303t147.5 237.5t228 155t295 55.5t295.5 -55.5t229 -155t147.5 -237.5t52 -303t-52 -302.5t-147.5 -237t-229 -154t-295.5 -54.5t-295 54.5t-228 154t-147.5 237t-52.5 302.5zM299 733q0 -135 37 -242t105 -181t164 -113.5t214 -39.5q119 0 215 39.5 t164 113.5t104.5 181t36.5 242t-36.5 242.5t-104.5 182t-164 114.5t-215 40q-118 0 -214 -40t-164 -114.5t-105 -182t-37 -242.5zM498 1583l245 278h164l243 -278h-133q-12 0 -29 4.5t-34 25.5l-120 144l-12 19l-13 -19l-119 -144q-17 -20 -34.5 -25t-29.5 -5h-128z" />
<glyph unicode="&#xd5;" horiz-adv-x="1639" d="M96 733q0 165 52.5 303t147.5 237.5t228 155t295 55.5t295.5 -55.5t229 -155t147.5 -237.5t52 -303t-52 -302.5t-147.5 -237t-229 -154t-295.5 -54.5t-295 54.5t-228 154t-147.5 237t-52.5 302.5zM299 733q0 -135 37 -242t105 -181t164 -113.5t214 -39.5q119 0 215 39.5 t164 113.5t104.5 181t36.5 242t-36.5 242.5t-104.5 182t-164 114.5t-215 40q-118 0 -214 -40t-164 -114.5t-105 -182t-37 -242.5zM498 1585q0 44 13 85t36.5 72.5t58 49.5t79.5 18q46 0 83 -16.5t68 -36.5t58.5 -37t56.5 -17q24 0 41.5 8.5t30 23.5t18.5 33.5t6 36.5h111 q0 -44 -12 -84.5t-36 -71.5t-59 -49.5t-80 -18.5q-46 0 -82.5 17t-67.5 37t-58.5 37t-56.5 17q-24 0 -41 -9.5t-29 -24.5t-18 -33t-6 -37h-114z" />
<glyph unicode="&#xd6;" horiz-adv-x="1639" d="M96 733q0 165 52.5 303t147.5 237.5t228 155t295 55.5t295.5 -55.5t229 -155t147.5 -237.5t52 -303t-52 -302.5t-147.5 -237t-229 -154t-295.5 -54.5t-295 54.5t-228 154t-147.5 237t-52.5 302.5zM299 733q0 -135 37 -242t105 -181t164 -113.5t214 -39.5q119 0 215 39.5 t164 113.5t104.5 181t36.5 242t-36.5 242.5t-104.5 182t-164 114.5t-215 40q-118 0 -214 -40t-164 -114.5t-105 -182t-37 -242.5zM495 1706q0 24 9 45t24.5 36t35.5 24.5t42 9.5q23 0 43.5 -9.5t36.5 -24.5t25 -36t9 -45q0 -23 -9 -42.5t-25 -35t-37 -24.5t-43 -9t-42 9 t-35.5 24.5t-24.5 35t-9 42.5zM923 1706q0 24 8.5 45t24 36t36 24.5t44.5 9.5q23 0 43 -9.5t35.5 -24.5t24.5 -36t9 -45q0 -23 -9 -42.5t-24.5 -35t-36 -24.5t-42.5 -9q-24 0 -44.5 9t-36 24.5t-24 35t-8.5 42.5z" />
<glyph unicode="&#xd7;" d="M133 323l365 364l-354 353l98 99l353 -354l351 352l98 -99l-351 -351l363 -362l-98 -99l-362 364l-366 -366z" />
<glyph unicode="&#xd8;" horiz-adv-x="1639" d="M96 733q0 165 52.5 303t147.5 237.5t228 155t295 55.5q118 0 221 -30t188 -85l83 114q20 28 36.5 39.5t49.5 11.5h102l-176 -241q105 -100 162.5 -242.5t57.5 -317.5q0 -165 -52 -302.5t-147.5 -237t-229 -154t-295.5 -54.5q-110 0 -206 25.5t-178 73.5l-102 -139 q-22 -31 -52 -44t-60 -13h-80l196 267q-115 100 -178 247.5t-63 330.5zM299 733q0 -138 38.5 -246t108.5 -183l675 921q-61 43 -137 65t-165 22q-118 0 -214 -40t-164 -114.5t-105 -182t-37 -242.5zM541 228q119 -72 278 -71q119 0 215 39.5t164 113.5t104.5 181t36.5 242 q0 129 -33 232t-95 177z" />
<glyph unicode="&#xd9;" horiz-adv-x="1506" d="M169 575v892h198v-890q0 -92 26 -169t75.5 -132.5t122 -86.5t163.5 -31t162.5 30.5t121 86t76 132t26.5 168.5v892h197v-892q0 -127 -40.5 -235.5t-115.5 -187.5t-183.5 -124t-243.5 -45t-244 45t-184.5 124t-116 188t-40.5 235zM378 1896h195q34 0 51 -5t35 -27 l233 -281h-131q-23 0 -35.5 2.5t-27.5 17.5z" />
<glyph unicode="&#xda;" horiz-adv-x="1506" d="M169 575v892h198v-890q0 -92 26 -169t75.5 -132.5t122 -86.5t163.5 -31t162.5 30.5t121 86t76 132t26.5 168.5v892h197v-892q0 -127 -40.5 -235.5t-115.5 -187.5t-183.5 -124t-243.5 -45t-244 45t-184.5 124t-116 188t-40.5 235zM612 1583l236 281q17 23 35 27.5t52 4.5 h206l-334 -292q-15 -14 -29.5 -17.5t-34.5 -3.5h-131z" />
<glyph unicode="&#xdb;" horiz-adv-x="1506" d="M169 575v892h198v-890q0 -92 26 -169t75.5 -132.5t122 -86.5t163.5 -31t162.5 30.5t121 86t76 132t26.5 168.5v892h197v-892q0 -127 -40.5 -235.5t-115.5 -187.5t-183.5 -124t-243.5 -45t-244 45t-184.5 124t-116 188t-40.5 235zM430 1583l245 278h164l243 -278h-133 q-12 0 -29 4.5t-34 25.5l-120 144l-12 19l-13 -19l-119 -144q-17 -20 -34.5 -25t-29.5 -5h-128z" />
<glyph unicode="&#xdc;" horiz-adv-x="1506" d="M169 575v892h198v-890q0 -92 26 -169t75.5 -132.5t122 -86.5t163.5 -31t162.5 30.5t121 86t76 132t26.5 168.5v892h197v-892q0 -127 -40.5 -235.5t-115.5 -187.5t-183.5 -124t-243.5 -45t-244 45t-184.5 124t-116 188t-40.5 235zM427 1706q0 24 9 45t24.5 36t35.5 24.5 t42 9.5q23 0 43.5 -9.5t36.5 -24.5t25 -36t9 -45q0 -23 -9 -42.5t-25 -35t-37 -24.5t-43 -9t-42 9t-35.5 24.5t-24.5 35t-9 42.5zM855 1706q0 24 8.5 45t24 36t36 24.5t44.5 9.5q23 0 43 -9.5t35.5 -24.5t24.5 -36t9 -45q0 -23 -9 -42.5t-24.5 -35t-36 -24.5t-42.5 -9 q-24 0 -44.5 9t-36 24.5t-24 35t-8.5 42.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1277" d="M2 1467h174q27 0 42 -12.5t27 -33.5l336 -570l33.5 -67t26.5 -63q11 32 25.5 63t31.5 67l335 570q9 17 25.5 31.5t42.5 14.5h175l-538 -882v-585h-198v585zM499 1583l236 281q17 23 35 27.5t52 4.5h206l-334 -292q-15 -14 -29.5 -17.5t-34.5 -3.5h-131z" />
<glyph unicode="&#xde;" horiz-adv-x="1225" d="M187 0v1467h198v-270h236q139 0 242 -32t170.5 -91.5t100.5 -143.5t33 -188q0 -102 -35.5 -187t-105 -146.5t-171 -95.5t-234.5 -34h-236v-279h-198zM385 436h236q85 0 150 22.5t109.5 63t67 97t22.5 123.5q0 140 -86.5 219t-262.5 79h-236v-604z" />
<glyph unicode="&#xdf;" d="M144 0v1016q0 106 35.5 193t101 149.5t157.5 96.5t205 34q105 0 182 -31t127 -78.5t74.5 -103.5t24.5 -109q0 -61 -22 -105t-55 -78t-71.5 -59.5t-71 -50t-54.5 -50.5t-22 -59q0 -40 27 -66.5t68 -49.5t88.5 -47t88.5 -60t68 -88.5t27 -131.5q0 -80 -29 -142.5 t-79.5 -106t-120.5 -66.5t-150 -23q-99 0 -180.5 35t-142.5 91l43 69q8 13 19.5 20.5t29.5 7.5t38.5 -14.5t48.5 -32t66 -31.5t94 -14q44 0 79.5 13.5t61.5 37t39 56.5t13 71q0 57 -28.5 93t-71.5 62t-93.5 47.5t-93.5 51.5t-72 72.5t-29 109.5q0 54 23 94.5t57.5 73t75 60 t74.5 58.5t57 67t23 85q0 33 -12.5 66.5t-41 60t-74.5 43.5t-111 17q-70 0 -126 -21.5t-97 -64.5t-63 -107.5t-22 -150.5v-1010h-183z" />
<glyph unicode="&#xe0;" horiz-adv-x="1017" d="M67 259q0 61 33.5 118t109.5 101t197.5 72.5t299.5 32.5v81q0 121 -52.5 182.5t-152.5 61.5q-68 0 -113.5 -17t-79 -37.5t-57.5 -37.5t-47 -17q-18 0 -31.5 9.5t-22.5 24.5l-33 58q86 83 185 124t220 41q87 0 154.5 -29t114 -80t70 -123.5t23.5 -159.5v-664h-81 q-27 0 -41 8.5t-23 34.5l-20 98q-41 -38 -80 -67t-82 -49t-91.5 -30.5t-108.5 -10.5t-113.5 16.5t-92.5 50.5t-62 85.5t-23 122.5zM225 1484h173q34 0 50.5 -11t30.5 -34l153 -248h-105q-22 0 -35 6.5t-27 22.5zM242 267q0 -41 13 -70.5t36 -48.5t54.5 -27.5t67.5 -8.5 q48 0 88 9.5t75 28t67.5 44t63.5 58.5v215q-126 -4 -215 -20t-144.5 -41.5t-80.5 -60.5t-25 -78z" />
<glyph unicode="&#xe1;" horiz-adv-x="1017" d="M67 259q0 61 33.5 118t109.5 101t197.5 72.5t299.5 32.5v81q0 121 -52.5 182.5t-152.5 61.5q-68 0 -113.5 -17t-79 -37.5t-57.5 -37.5t-47 -17q-18 0 -31.5 9.5t-22.5 24.5l-33 58q86 83 185 124t220 41q87 0 154.5 -29t114 -80t70 -123.5t23.5 -159.5v-664h-81 q-27 0 -41 8.5t-23 34.5l-20 98q-41 -38 -80 -67t-82 -49t-91.5 -30.5t-108.5 -10.5t-113.5 16.5t-92.5 50.5t-62 85.5t-23 122.5zM242 267q0 -41 13 -70.5t36 -48.5t54.5 -27.5t67.5 -8.5q48 0 88 9.5t75 28t67.5 44t63.5 58.5v215q-126 -4 -215 -20t-144.5 -41.5 t-80.5 -60.5t-25 -78zM388 1191l152 248q14 24 31 34.5t49 10.5h179l-239 -264q-14 -15 -28 -22t-35 -7h-109z" />
<glyph unicode="&#xe2;" horiz-adv-x="1017" d="M67 259q0 61 33.5 118t109.5 101t197.5 72.5t299.5 32.5v81q0 121 -52.5 182.5t-152.5 61.5q-68 0 -113.5 -17t-79 -37.5t-57.5 -37.5t-47 -17q-18 0 -31.5 9.5t-22.5 24.5l-33 58q86 83 185 124t220 41q87 0 154.5 -29t114 -80t70 -123.5t23.5 -159.5v-664h-81 q-27 0 -41 8.5t-23 34.5l-20 98q-41 -38 -80 -67t-82 -49t-91.5 -30.5t-108.5 -10.5t-113.5 16.5t-92.5 50.5t-62 85.5t-23 122.5zM187 1197l229 270h170l228 -270h-122q-11 0 -23 4t-23 16l-126 135q-9 8 -16 19q-5 -5 -9.5 -10t-8.5 -9l-127 -135q-22 -19 -46 -20h-126z M242 267q0 -41 13 -70.5t36 -48.5t54.5 -27.5t67.5 -8.5q48 0 88 9.5t75 28t67.5 44t63.5 58.5v215q-126 -4 -215 -20t-144.5 -41.5t-80.5 -60.5t-25 -78z" />
<glyph unicode="&#xe3;" horiz-adv-x="1017" d="M67 259q0 61 33.5 118t109.5 101t197.5 72.5t299.5 32.5v81q0 121 -52.5 182.5t-152.5 61.5q-68 0 -113.5 -17t-79 -37.5t-57.5 -37.5t-47 -17q-18 0 -31.5 9.5t-22.5 24.5l-33 58q86 83 185 124t220 41q87 0 154.5 -29t114 -80t70 -123.5t23.5 -159.5v-664h-81 q-27 0 -41 8.5t-23 34.5l-20 98q-41 -38 -80 -67t-82 -49t-91.5 -30.5t-108.5 -10.5t-113.5 16.5t-92.5 50.5t-62 85.5t-23 122.5zM205 1231q0 49 13 89.5t37.5 69.5t58.5 45t74 16q36 0 67 -15t59 -32.5t51.5 -32t47.5 -14.5q37 0 57 21.5t21 66.5h111q0 -48 -13 -88.5 t-37 -69.5t-57.5 -44.5t-74.5 -15.5q-36 0 -67 14.5t-58.5 32t-52 32.5t-48.5 15q-74 0 -76 -90h-113zM242 267q0 -41 13 -70.5t36 -48.5t54.5 -27.5t67.5 -8.5q48 0 88 9.5t75 28t67.5 44t63.5 58.5v215q-126 -4 -215 -20t-144.5 -41.5t-80.5 -60.5t-25 -78z" />
<glyph unicode="&#xe4;" horiz-adv-x="1017" d="M67 259q0 61 33.5 118t109.5 101t197.5 72.5t299.5 32.5v81q0 121 -52.5 182.5t-152.5 61.5q-68 0 -113.5 -17t-79 -37.5t-57.5 -37.5t-47 -17q-18 0 -31.5 9.5t-22.5 24.5l-33 58q86 83 185 124t220 41q87 0 154.5 -29t114 -80t70 -123.5t23.5 -159.5v-664h-81 q-27 0 -41 8.5t-23 34.5l-20 98q-41 -38 -80 -67t-82 -49t-91.5 -30.5t-108.5 -10.5t-113.5 16.5t-92.5 50.5t-62 85.5t-23 122.5zM190 1320q0 24 9.5 45t25.5 37t36 25.5t43 9.5q24 0 45 -9.5t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5t-25.5 -35.5t-37 -24.5t-45 -9.5 q-23 0 -43 9.5t-36 24.5t-25.5 35.5t-9.5 44.5zM242 267q0 -41 13 -70.5t36 -48.5t54.5 -27.5t67.5 -8.5q48 0 88 9.5t75 28t67.5 44t63.5 58.5v215q-126 -4 -215 -20t-144.5 -41.5t-80.5 -60.5t-25 -78zM580 1320q0 24 9.5 45t25 37t36.5 25.5t44 9.5q24 0 45 -9.5 t36.5 -25.5t25 -37t9.5 -45t-9.5 -44.5t-25 -35.5t-36.5 -24.5t-45 -9.5t-44.5 9.5t-36 24.5t-25 35.5t-9.5 44.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1017" d="M67 259q0 61 33.5 118t109.5 101t197.5 72.5t299.5 32.5v81q0 121 -52.5 182.5t-152.5 61.5q-68 0 -113.5 -17t-79 -37.5t-57.5 -37.5t-47 -17q-18 0 -31.5 9.5t-22.5 24.5l-33 58q86 83 185 124t220 41q87 0 154.5 -29t114 -80t70 -123.5t23.5 -159.5v-664h-81 q-27 0 -41 8.5t-23 34.5l-20 98q-41 -38 -80 -67t-82 -49t-91.5 -30.5t-108.5 -10.5t-113.5 16.5t-92.5 50.5t-62 85.5t-23 122.5zM242 267q0 -41 13 -70.5t36 -48.5t54.5 -27.5t67.5 -8.5q48 0 88 9.5t75 28t67.5 44t63.5 58.5v215q-126 -4 -215 -20t-144.5 -41.5 t-80.5 -60.5t-25 -78zM297 1347q0 43 16.5 77.5t44.5 60t65 39.5t78 14q42 0 79 -14t66 -39.5t45 -60t16 -77.5q0 -42 -16 -77t-45 -59.5t-66 -38.5t-79 -14q-41 0 -78 14t-65 38.5t-44.5 59.5t-16.5 77zM399 1347q0 -45 27.5 -74t76.5 -29q46 0 74 29t28 74q0 46 -28 74.5 t-74 28.5q-48 0 -76 -28.5t-28 -74.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1648" d="M70 273q0 61 33.5 121t109 107t197 77t299.5 34v52q0 121 -52.5 185t-152.5 64q-68 0 -113.5 -17.5t-78.5 -39t-57.5 -39.5t-47.5 -18q-18 0 -31.5 9.5t-22.5 24.5l-33 58q86 83 178.5 124t208.5 41q123 0 197.5 -52.5t108.5 -144.5q55 90 144.5 142.5t211.5 52.5 q84 0 157 -33t126 -95.5t84 -152t31 -204.5q0 -42 -8.5 -56.5t-33.5 -14.5h-645q4 -92 27.5 -161.5t63 -115.5t93 -69t120.5 -23q71 0 118 15t78 33.5t51 33t36 14.5q14 0 23 -4.5t15 -12.5l48 -63q-34 -41 -78 -71t-95 -49.5t-105 -29t-108 -9.5q-121 0 -217.5 57.5 t-153.5 176.5q-28 -63 -72 -108t-99 -74t-115.5 -41.5t-121.5 -12.5q-71 0 -129 17.5t-100.5 53.5t-65.5 90.5t-23 127.5zM245 283q0 -89 51 -130t134 -41q58 0 109 18t89 56t59.5 94.5t21.5 132.5v85q-126 -5 -214.5 -23t-144 -46.5t-80.5 -65.5t-25 -80zM882 611h523 q0 67 -16.5 124t-47.5 97.5t-76 64t-102 23.5q-62 0 -111.5 -21t-84.5 -61t-56.5 -97.5t-28.5 -129.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="978" d="M86 519q0 116 32.5 214t94 169.5t152.5 111.5t208 40q110 0 193.5 -35.5t147.5 -99.5l-48 -66q-8 -11 -16 -17.5t-23 -6.5t-33.5 13t-45.5 28.5t-66.5 28t-96.5 12.5q-77 0 -135.5 -27t-98 -78t-59.5 -124t-20 -163q0 -94 21.5 -167t61 -123t95.5 -76t125 -26 q67 0 110 16t71 35.5t47 35t38 15.5q23 0 35 -17l51 -67q-58 -73 -143.5 -110.5t-181.5 -45.5l-18 -66q95 -19 137 -55t42 -91q0 -33 -16.5 -59.5t-45.5 -45t-69.5 -28.5t-89.5 -10q-42 0 -80.5 9t-69.5 23l17 56q6 18 23 19q6 0 14.5 -3.5t20 -8t27.5 -8.5t38 -4 q43 0 64.5 17.5t21.5 44.5q0 39 -43 54.5t-120 26.5l42 128q-89 8 -164.5 47.5t-130.5 107.5t-85.5 162.5t-30.5 213.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1081" d="M85 540q0 109 33.5 203t95.5 163t152.5 108.5t202.5 39.5q93 0 172.5 -31.5t137.5 -90.5t90.5 -145.5t32.5 -196.5q0 -43 -9 -57.5t-35 -14.5h-693q3 -98 27 -171t67 -121.5t102.5 -72.5t133.5 -24q69 0 118.5 16t85.5 34.5t60 34t41 15.5q23 0 35 -17l51 -67 q-34 -41 -81 -71t-100.5 -49.5t-111 -29t-113.5 -9.5q-108 0 -198.5 36t-156.5 106.5t-103 174t-37 237.5zM271 632h568q0 63 -18 116t-52.5 91.5t-83.5 59.5t-112 21q-131 0 -207 -76.5t-95 -211.5zM298 1484h173q34 0 50.5 -11t30.5 -34l153 -248h-105q-22 0 -35 6.5 t-27 22.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="1081" d="M85 540q0 109 33.5 203t95.5 163t152.5 108.5t202.5 39.5q93 0 172.5 -31.5t137.5 -90.5t90.5 -145.5t32.5 -196.5q0 -43 -9 -57.5t-35 -14.5h-693q3 -98 27 -171t67 -121.5t102.5 -72.5t133.5 -24q69 0 118.5 16t85.5 34.5t60 34t41 15.5q23 0 35 -17l51 -67 q-34 -41 -81 -71t-100.5 -49.5t-111 -29t-113.5 -9.5q-108 0 -198.5 36t-156.5 106.5t-103 174t-37 237.5zM271 632h568q0 63 -18 116t-52.5 91.5t-83.5 59.5t-112 21q-131 0 -207 -76.5t-95 -211.5zM461 1191l152 248q14 24 31 34.5t49 10.5h179l-239 -264q-14 -15 -28 -22 t-35 -7h-109z" />
<glyph unicode="&#xea;" horiz-adv-x="1081" d="M85 540q0 109 33.5 203t95.5 163t152.5 108.5t202.5 39.5q93 0 172.5 -31.5t137.5 -90.5t90.5 -145.5t32.5 -196.5q0 -43 -9 -57.5t-35 -14.5h-693q3 -98 27 -171t67 -121.5t102.5 -72.5t133.5 -24q69 0 118.5 16t85.5 34.5t60 34t41 15.5q23 0 35 -17l51 -67 q-34 -41 -81 -71t-100.5 -49.5t-111 -29t-113.5 -9.5q-108 0 -198.5 36t-156.5 106.5t-103 174t-37 237.5zM260 1197l228 270h170l229 -270h-122q-11 0 -23 4t-23 16l-126 135q-9 8 -16 19q-5 -5 -9.5 -10t-8.5 -9l-127 -135q-22 -19 -46 -20h-126zM271 632h568 q0 63 -18 116t-52.5 91.5t-83.5 59.5t-112 21q-131 0 -207 -76.5t-95 -211.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="1081" d="M85 540q0 109 33.5 203t95.5 163t152.5 108.5t202.5 39.5q93 0 172.5 -31.5t137.5 -90.5t90.5 -145.5t32.5 -196.5q0 -43 -9 -57.5t-35 -14.5h-693q3 -98 27 -171t67 -121.5t102.5 -72.5t133.5 -24q69 0 118.5 16t85.5 34.5t60 34t41 15.5q23 0 35 -17l51 -67 q-34 -41 -81 -71t-100.5 -49.5t-111 -29t-113.5 -9.5q-108 0 -198.5 36t-156.5 106.5t-103 174t-37 237.5zM263 1320q0 24 9.5 45t25.5 37t36 25.5t43 9.5q24 0 45 -9.5t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5t-25.5 -35.5t-37 -24.5t-45 -9.5q-23 0 -43 9.5t-36 24.5 t-25.5 35.5t-9.5 44.5zM271 632h568q0 63 -18 116t-52.5 91.5t-83.5 59.5t-112 21q-131 0 -207 -76.5t-95 -211.5zM653 1320q0 24 9.5 45t25 37t36.5 25.5t44 9.5q24 0 45 -9.5t36.5 -25.5t25 -37t9.5 -45t-9.5 -44.5t-25 -35.5t-36.5 -24.5t-45 -9.5t-44.5 9.5t-36 24.5 t-25 35.5t-9.5 44.5z" />
<glyph unicode="&#xec;" horiz-adv-x="491" d="M-25 1484h173q34 0 50.5 -11t30.5 -34l153 -248h-105q-22 0 -35 6.5t-27 22.5zM154 0v1037h183v-1037h-183z" />
<glyph unicode="&#xed;" horiz-adv-x="491" d="M138 1191l152 248q14 24 31 34.5t49 10.5h179l-239 -264q-14 -15 -28 -22t-35 -7h-109zM155 0v1037h183v-1037h-183z" />
<glyph unicode="&#xee;" horiz-adv-x="491" d="M-62 1197l228 270h170l228 -270h-122q-11 0 -22.5 4t-23.5 16l-126 135q-9 8 -16 19q-5 -5 -9 -10t-8 -9l-127 -135q-22 -19 -47 -20h-125zM154 0v1037h183v-1037h-183z" />
<glyph unicode="&#xef;" horiz-adv-x="491" d="M-25 1320q0 24 9.5 45t25 37t36 25.5t44.5 9.5t44.5 -9.5t37 -25.5t26 -37t9.5 -45t-9.5 -44.5t-26 -35.5t-37.5 -24.5t-44 -9.5q-24 0 -44.5 9.5t-36 24.5t-25 35.5t-9.5 44.5zM154 0v1037h183v-1037h-183zM295 1320q0 24 9 45t24.5 37t36.5 25.5t44 9.5q24 0 45.5 -9.5 t37.5 -25.5t25 -37t9 -45t-9 -44.5t-25 -35.5t-37.5 -24.5t-45.5 -9.5t-44.5 9.5t-36 24.5t-24.5 35.5t-9 44.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1159" d="M84 477q0 96 31.5 181.5t91 150t144 102.5t191.5 38q103 0 193.5 -43.5t156.5 -132.5q-20 139 -79.5 236t-157.5 163l-188 -139l-34 58q-12 18 -8 35.5t22 28.5l106 74q-91 41 -208 68q-24 7 -38 29t-1 58l21 64q98 -15 190.5 -46.5t175.5 -80.5l170 125l36 -58 q11 -17 7 -34.5t-21 -28.5l-100 -69q61 -50 112 -113.5t87.5 -142.5t56 -174t19.5 -208q0 -146 -32 -262.5t-95 -198.5t-156.5 -126t-216.5 -44q-100 0 -186.5 33.5t-151 96.5t-101.5 154t-37 206zM266 475q0 -83 23.5 -148t64.5 -109t95 -67t115 -23q73 0 132 27.5t101 84 t65.5 140.5t26.5 198q-16 44 -43.5 85.5t-66 73.5t-88.5 51t-113 19q-77 0 -135.5 -25.5t-97.5 -70t-59 -105.5t-20 -131z" />
<glyph unicode="&#xf1;" horiz-adv-x="1142" d="M144 0v1037h110q39 0 48 -38l14 -112q34 38 72 68.5t80.5 52.5t90 34t103.5 12q85 0 150 -28.5t108.5 -80t66 -124t22.5 -161.5v-660h-184v660q0 118 -53.5 183t-163.5 65q-81 0 -151 -39t-129 -105v-764h-184zM282 1231q0 49 13 89.5t37.5 69.5t58.5 45t74 16 q36 0 67 -15t59 -32.5t51.5 -32t47.5 -14.5q37 0 57 21.5t21 66.5h111q0 -48 -13 -88.5t-37 -69.5t-57.5 -44.5t-74.5 -15.5q-36 0 -67 14.5t-58.5 32t-52 32.5t-48.5 15q-74 0 -76 -90h-113z" />
<glyph unicode="&#xf2;" horiz-adv-x="1161" d="M86 519q0 122 34.5 221t99 168.5t156 107.5t206.5 38q114 0 205 -38t155.5 -107.5t98.5 -168.5t34 -221q0 -123 -34 -221t-98.5 -168t-156 -107t-204.5 -37q-115 0 -206.5 37t-156 107t-99 168t-34.5 221zM273 518q0 -184 76.5 -287t232.5 -103q154 0 229.5 103t75.5 287 q0 185 -76 288.5t-229 103.5q-78 0 -136 -26.5t-96.5 -76.5t-57.5 -123.5t-19 -165.5zM307 1484h173q34 0 50.5 -11t30.5 -34l153 -248h-105q-22 0 -35 6.5t-27 22.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1161" d="M86 519q0 122 34.5 221t99 168.5t156 107.5t206.5 38q114 0 205 -38t155.5 -107.5t98.5 -168.5t34 -221q0 -123 -34 -221t-98.5 -168t-156 -107t-204.5 -37q-115 0 -206.5 37t-156 107t-99 168t-34.5 221zM273 518q0 -184 76.5 -287t232.5 -103q154 0 229.5 103t75.5 287 q0 185 -76 288.5t-229 103.5q-78 0 -136 -26.5t-96.5 -76.5t-57.5 -123.5t-19 -165.5zM470 1191l152 248q14 24 31 34.5t49 10.5h179l-239 -264q-14 -15 -28 -22t-35 -7h-109z" />
<glyph unicode="&#xf4;" horiz-adv-x="1161" d="M86 519q0 122 34.5 221t99 168.5t156 107.5t206.5 38q114 0 205 -38t155.5 -107.5t98.5 -168.5t34 -221q0 -123 -34 -221t-98.5 -168t-156 -107t-204.5 -37q-115 0 -206.5 37t-156 107t-99 168t-34.5 221zM269 1197l229 270h170l228 -270h-122q-11 0 -23 4t-23 16 l-126 135q-9 8 -16 19q-5 -5 -9.5 -10t-8.5 -9l-127 -135q-22 -19 -46 -20h-126zM273 518q0 -184 76.5 -287t232.5 -103q154 0 229.5 103t75.5 287q0 185 -76 288.5t-229 103.5q-78 0 -136 -26.5t-96.5 -76.5t-57.5 -123.5t-19 -165.5z" />
<glyph unicode="&#xf5;" horiz-adv-x="1161" d="M86 519q0 122 34.5 221t99 168.5t156 107.5t206.5 38q114 0 205 -38t155.5 -107.5t98.5 -168.5t34 -221q0 -123 -34 -221t-98.5 -168t-156 -107t-204.5 -37q-115 0 -206.5 37t-156 107t-99 168t-34.5 221zM273 518q0 -184 76.5 -287t232.5 -103q154 0 229.5 103t75.5 287 q0 185 -76 288.5t-229 103.5q-78 0 -136 -26.5t-96.5 -76.5t-57.5 -123.5t-19 -165.5zM287 1231q0 49 13 89.5t37.5 69.5t58.5 45t74 16q36 0 67 -15t59 -32.5t51.5 -32t47.5 -14.5q37 0 57 21.5t21 66.5h111q0 -48 -13 -88.5t-37 -69.5t-57.5 -44.5t-74.5 -15.5 q-36 0 -67 14.5t-58.5 32t-52 32.5t-48.5 15q-74 0 -76 -90h-113z" />
<glyph unicode="&#xf6;" horiz-adv-x="1161" d="M86 519q0 122 34.5 221t99 168.5t156 107.5t206.5 38q114 0 205 -38t155.5 -107.5t98.5 -168.5t34 -221q0 -123 -34 -221t-98.5 -168t-156 -107t-204.5 -37q-115 0 -206.5 37t-156 107t-99 168t-34.5 221zM272 1320q0 24 9.5 45t25.5 37t36 25.5t43 9.5q24 0 45 -9.5 t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5t-25.5 -35.5t-37 -24.5t-45 -9.5q-23 0 -43 9.5t-36 24.5t-25.5 35.5t-9.5 44.5zM273 518q0 -184 76.5 -287t232.5 -103q154 0 229.5 103t75.5 287q0 185 -76 288.5t-229 103.5q-78 0 -136 -26.5t-96.5 -76.5t-57.5 -123.5t-19 -165.5 zM662 1320q0 24 9.5 45t25 37t36.5 25.5t44 9.5q24 0 45 -9.5t36.5 -25.5t25 -37t9.5 -45t-9.5 -44.5t-25 -35.5t-36.5 -24.5t-45 -9.5t-44.5 9.5t-36 24.5t-25 35.5t-9.5 44.5z" />
<glyph unicode="&#xf7;" d="M102 618v139h982v-139h-982zM465 322q0 27 9.5 50t27 40.5t40.5 28t51 10.5q27 0 50 -10.5t40 -28t27.5 -41t10.5 -49.5q0 -28 -10.5 -51t-27.5 -40.5t-40 -27t-50 -9.5q-28 0 -51 9.5t-40.5 27t-27 40.5t-9.5 51zM465 1051q0 27 9.5 50t27 40.5t40.5 28t51 10.5 q27 0 50 -10.5t40 -28t27.5 -41t10.5 -49.5q0 -28 -10.5 -51t-27.5 -40.5t-40 -27t-50 -9.5q-28 0 -51 9.5t-40.5 27t-27 40.5t-9.5 51z" />
<glyph unicode="&#xf8;" horiz-adv-x="1160" d="M77 -78l148 201q-68 71 -103.5 170.5t-35.5 225.5q0 122 34.5 221t99 168.5t156 107.5t206.5 38q81 0 150 -19.5t126 -55.5l70 93q18 29 35 40t51 11h91l-161 -217q63 -71 97 -168t34 -219q0 -123 -34 -221t-98.5 -168t-156 -107t-204.5 -37q-78 0 -145 17t-123 50 l-56 -76q-20 -31 -51.5 -43t-61.5 -12h-68zM263 518q0 -164 61 -263l446 604q-75 57 -188 57q-78 0 -137.5 -27.5t-100 -78.5t-61 -125.5t-20.5 -166.5zM401 172q72 -49 181 -49q77 0 135.5 27t99 78.5t61 124.5t20.5 165q0 155 -53 253z" />
<glyph unicode="&#xf9;" horiz-adv-x="1141" d="M134 376v661h182v-661q0 -118 54 -182.5t165 -64.5q81 0 151 38.5t129 105.5v764h182v-1037h-108q-39 0 -49 38l-15 112q-68 -75 -151.5 -120.5t-192.5 -45.5q-85 0 -150 28t-109 79t-66 124t-22 161zM296 1484h173q34 0 50.5 -11t30.5 -34l153 -248h-105q-22 0 -35 6.5 t-27 22.5z" />
<glyph unicode="&#xfa;" horiz-adv-x="1141" d="M134 376v661h182v-661q0 -118 54 -182.5t165 -64.5q81 0 151 38.5t129 105.5v764h182v-1037h-108q-39 0 -49 38l-15 112q-68 -75 -151.5 -120.5t-192.5 -45.5q-85 0 -150 28t-109 79t-66 124t-22 161zM459 1191l152 248q14 24 31 34.5t49 10.5h179l-239 -264 q-14 -15 -28 -22t-35 -7h-109z" />
<glyph unicode="&#xfb;" horiz-adv-x="1141" d="M134 376v661h182v-661q0 -118 54 -182.5t165 -64.5q81 0 151 38.5t129 105.5v764h182v-1037h-108q-39 0 -49 38l-15 112q-68 -75 -151.5 -120.5t-192.5 -45.5q-85 0 -150 28t-109 79t-66 124t-22 161zM258 1197l228 270h170l229 -270h-122q-11 0 -23 4t-23 16l-126 135 q-9 8 -17 19q-5 -5 -9 -10t-8 -9l-127 -135q-22 -19 -46 -20h-126z" />
<glyph unicode="&#xfc;" horiz-adv-x="1141" d="M134 376v661h182v-661q0 -118 54 -182.5t165 -64.5q81 0 151 38.5t129 105.5v764h182v-1037h-108q-39 0 -49 38l-15 112q-68 -75 -151.5 -120.5t-192.5 -45.5q-85 0 -150 28t-109 79t-66 124t-22 161zM261 1320q0 24 9.5 45t25.5 37t36 25.5t43 9.5q24 0 45 -9.5 t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5t-25.5 -35.5t-37 -24.5t-45 -9.5q-23 0 -43 9.5t-36 24.5t-25.5 35.5t-9.5 44.5zM651 1320q0 24 9.5 45t25 37t36.5 25.5t44 9.5q24 0 45 -9.5t36.5 -25.5t25 -37t9.5 -45t-9.5 -44.5t-25 -35.5t-36.5 -24.5t-45 -9.5t-44.5 9.5 t-36 24.5t-25 35.5t-9.5 44.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1055" d="M22 1037h157q24 0 37.5 -11.5t19.5 -26.5l277 -653q16 -43 28 -90q14 48 30 91l270 652q6 16 21 27t33 11h144l-578 -1343q-9 -20 -23 -32.5t-43 -12.5h-135l190 411zM442 1191l152 248q14 24 31 34.5t49 10.5h179l-239 -264q-14 -15 -28 -22t-35 -7h-109z" />
<glyph unicode="&#xfe;" horiz-adv-x="1147" d="M144 -351v1859h184v-620q66 76 148.5 121t190.5 45q90 0 162.5 -34t124 -101t79 -166t27.5 -228q0 -115 -31 -213.5t-88.5 -170.5t-141 -113.5t-188.5 -41.5q-96 0 -163.5 35t-119.5 100v-472h-184zM328 250q50 -68 109.5 -95.5t132.5 -27.5q145 0 223 103.5t78 294.5 q0 101 -17.5 174t-51.5 119.5t-83 68t-112 21.5q-89 0 -156 -41t-123 -115v-502z" />
<glyph unicode="&#xff;" horiz-adv-x="1055" d="M22 1037h157q24 0 37.5 -11.5t19.5 -26.5l277 -653q16 -43 28 -90q14 48 30 91l270 652q6 16 21 27t33 11h144l-578 -1343q-9 -20 -23 -32.5t-43 -12.5h-135l190 411zM244 1320q0 24 9.5 45t25.5 37t36 25.5t43 9.5q24 0 45 -9.5t37 -25.5t25.5 -37t9.5 -45t-9.5 -44.5 t-25.5 -35.5t-37 -24.5t-45 -9.5q-23 0 -43 9.5t-36 24.5t-25.5 35.5t-9.5 44.5zM634 1320q0 24 9.5 45t25 37t36.5 25.5t44 9.5q24 0 45 -9.5t36.5 -25.5t25 -37t9.5 -45t-9.5 -44.5t-25 -35.5t-36.5 -24.5t-45 -9.5t-44.5 9.5t-36 24.5t-25 35.5t-9.5 44.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2233" d="M98 733q0 165 47.5 303t133.5 237.5t205.5 155t265.5 55.5q165 0 295 -69t216 -192v244h880v-161h-704v-488h571v-155h-571v-501h705l-1 -162h-880v245q-86 -123 -216.5 -191t-294.5 -68q-145 0 -265 54.5t-206 153.5t-133.5 236.5t-47.5 302.5zM301 733 q0 -135 33.5 -243t94.5 -183.5t147.5 -115t194.5 -39.5q106 0 193 39.5t147.5 115t93.5 183.5t33 243t-33 243t-93.5 184t-147 116.5t-193.5 40.5q-108 0 -194.5 -40.5t-147.5 -116.5t-94.5 -184t-33.5 -243z" />
<glyph unicode="&#x153;" horiz-adv-x="1787" d="M86 519q0 122 33 221t93.5 168.5t146 107.5t190.5 38q134 0 231.5 -62t152.5 -174q26 53 62.5 96.5t84 74.5t106 48t126.5 17q84 0 155.5 -33t124.5 -95.5t83 -152t30 -204.5q0 -42 -8 -56.5t-34 -14.5h-641q4 -92 27 -161.5t62.5 -115.5t93 -69t120.5 -23q62 0 108 16 t78.5 34.5t55.5 34t42 15.5q22 0 33 -17l53 -67q-34 -41 -78.5 -71t-95.5 -49.5t-105 -29t-107 -9.5q-121 0 -219.5 59t-154.5 181q-54 -115 -153.5 -177.5t-239.5 -62.5q-102 0 -186.5 37t-144 107t-92.5 168t-33 221zM274 518q0 -184 70.5 -287t214.5 -103q142 0 212 103 t70 287q0 92 -17.5 165.5t-52.5 123.5t-88 76.5t-124 26.5q-73 0 -126 -26.5t-88.5 -76.5t-53 -123.5t-17.5 -165.5zM1024 611h519q0 67 -16 124t-46.5 97.5t-75 64t-100.5 23.5q-62 0 -111.5 -21t-85 -61t-56.5 -97.5t-28 -129.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1277" d="M2 1467h174q27 0 42 -12.5t27 -33.5l336 -570l33.5 -67t26.5 -63q11 32 25.5 63t31.5 67l335 570q9 17 25.5 31.5t42.5 14.5h175l-538 -882v-585h-198v585zM314 1706q0 24 9 45t24.5 36t35.5 24.5t42 9.5q23 0 43.5 -9.5t36.5 -24.5t25 -36t9 -45q0 -23 -9 -42.5t-25 -35 t-37 -24.5t-43 -9t-42 9t-35.5 24.5t-24.5 35t-9 42.5zM742 1706q0 24 8.5 45t24 36t36 24.5t44.5 9.5q23 0 43 -9.5t35.5 -24.5t24.5 -36t9 -45q0 -23 -9 -42.5t-24.5 -35t-36 -24.5t-42.5 -9q-24 0 -44.5 9t-36 24.5t-24 35t-8.5 42.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="819" d="M96 1197l229 270h170l228 -270h-122q-11 0 -23 4t-23 16l-126 135q-9 8 -16 19q-5 -5 -9.5 -10t-8.5 -9l-127 -135q-22 -19 -46 -20h-126z" />
<glyph unicode="&#x2dc;" horiz-adv-x="819" d="M114 1231q0 49 13 89.5t37.5 69.5t58.5 45t74 16q36 0 67 -15t59 -32.5t51.5 -32t47.5 -14.5q37 0 57 21.5t21 66.5h111q0 -48 -13 -88.5t-37 -69.5t-57.5 -44.5t-74.5 -15.5q-36 0 -67 14.5t-58.5 32t-52 32.5t-48.5 15q-74 0 -76 -90h-113z" />
<glyph unicode="&#x2000;" horiz-adv-x="959" />
<glyph unicode="&#x2001;" horiz-adv-x="1919" />
<glyph unicode="&#x2002;" horiz-adv-x="959" />
<glyph unicode="&#x2003;" horiz-adv-x="1919" />
<glyph unicode="&#x2004;" horiz-adv-x="639" />
<glyph unicode="&#x2005;" horiz-adv-x="479" />
<glyph unicode="&#x2006;" horiz-adv-x="319" />
<glyph unicode="&#x2007;" horiz-adv-x="319" />
<glyph unicode="&#x2008;" horiz-adv-x="239" />
<glyph unicode="&#x2009;" horiz-adv-x="383" />
<glyph unicode="&#x200a;" horiz-adv-x="106" />
<glyph unicode="&#x2010;" horiz-adv-x="760" d="M128 537v154h506v-154h-506z" />
<glyph unicode="&#x2011;" horiz-adv-x="760" d="M128 537v154h506v-154h-506z" />
<glyph unicode="&#x2012;" horiz-adv-x="760" d="M128 537v154h506v-154h-506z" />
<glyph unicode="&#x2013;" d="M184 538v133h820v-133h-820z" />
<glyph unicode="&#x2014;" horiz-adv-x="1625" d="M131 538v133h1362v-133h-1362z" />
<glyph unicode="&#x2018;" horiz-adv-x="438" d="M112.5 1285q12.5 79 51.5 151t103 130l56 -35q15 -10 13.5 -25t-9.5 -23q-27 -33 -46 -80t-24 -101t5 -111t42 -111q13 -22 7.5 -38.5t-24.5 -24.5l-113 -44q-43 73 -58.5 153t-3 159z" />
<glyph unicode="&#x2019;" horiz-adv-x="437" d="M108 1031q1 15 9 24q27 33 46 79.5t24.5 100.5t-5 111t-42.5 111q-13 22 -7.5 38.5t24.5 24.5l113 45q43 -73 58.5 -153.5t3 -159.5t-51.5 -151.5t-103 -129.5l-55 36q-15 9 -14 24z" />
<glyph unicode="&#x201a;" horiz-adv-x="441" d="M96 -266q1 15 9 24q27 33 46 79.5t24.5 100.5t-5 111t-42.5 111q-13 22 -7.5 38.5t24.5 24.5l113 45q43 -73 58.5 -153.5t3 -159.5t-51.5 -151.5t-103 -129.5l-55 36q-15 9 -14 24z" />
<glyph unicode="&#x201c;" horiz-adv-x="748" d="M112.5 1285q12.5 79 51.5 151t103 130l56 -35q15 -10 13.5 -25t-9.5 -23q-27 -33 -46 -80t-24 -101t5 -111t42 -111q13 -22 7.5 -38.5t-24.5 -24.5l-113 -44q-43 73 -58.5 153t-3 159zM423.5 1285q12.5 79 51.5 151t103 130l56 -35q15 -10 13.5 -25t-9.5 -23 q-27 -33 -46 -80t-24 -101t5 -111t42 -111q13 -22 7.5 -38.5t-24.5 -24.5l-113 -44q-43 73 -58.5 153t-3 159z" />
<glyph unicode="&#x201d;" horiz-adv-x="748" d="M108 1031q1 15 9 24q27 33 46 79.5t24.5 100.5t-5 111t-42.5 111q-13 22 -7.5 38.5t24.5 24.5l113 45q43 -73 58.5 -153.5t3 -159.5t-51.5 -151.5t-103 -129.5l-55 36q-15 9 -14 24zM420 1031q1 15 9 24q27 33 46 79.5t24.5 100.5t-5 111t-42.5 111q-13 22 -7.5 38.5 t24.5 24.5l113 45q43 -73 58.5 -153.5t3 -159.5t-51.5 -151.5t-103 -129.5l-55 36q-15 9 -14 24z" />
<glyph unicode="&#x201e;" horiz-adv-x="752" d="M96 -266q1 15 9 24q27 33 46 79.5t24.5 100.5t-5 111t-42.5 111q-13 22 -7.5 38.5t24.5 24.5l113 45q43 -73 58.5 -153.5t3 -159.5t-51.5 -151.5t-103 -129.5l-55 36q-15 9 -14 24zM407 -266q1 15 9 24q27 33 46 79.5t24.5 100.5t-5 111t-42.5 111q-13 22 -7.5 38.5 t24.5 24.5l113 45q43 -73 58.5 -153.5t3 -159.5t-51.5 -151.5t-103 -129.5l-55 36q-15 9 -14 24z" />
<glyph unicode="&#x2022;" d="M214 609q0 79 30 148.5t81.5 121.5t120.5 82t147 30q79 0 148 -30t120.5 -82t82 -121.5t30.5 -148.5t-30.5 -148t-82 -120t-120.5 -81.5t-148 -30.5q-78 0 -147 30.5t-120.5 81.5t-81.5 120t-30 148z" />
<glyph unicode="&#x2026;" horiz-adv-x="1536" d="M114 113q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5t-40.5 27t-26.5 40.5t-9.5 51zM640 113q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41 t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5t-40.5 27t-26.5 40.5t-9.5 51zM1168 113q0 27 9.5 50t26.5 40.5t40.5 28t50.5 10.5t50 -10.5t40.5 -28t28 -41t10.5 -49.5q0 -28 -10.5 -51t-28 -40.5t-41 -27t-49.5 -9.5q-27 0 -50.5 9.5 t-40.5 27t-26.5 40.5t-9.5 51z" />
<glyph unicode="&#x202f;" horiz-adv-x="383" />
<glyph unicode="&#x2039;" horiz-adv-x="575" d="M102 530v24l255 398l60 -28q24 -11 28.5 -32.5t-10.5 -44.5l-162 -267q-14 -26 -28 -37q14 -13 28 -38l162 -267q14 -24 9.5 -45t-27.5 -32l-60 -29z" />
<glyph unicode="&#x203a;" horiz-adv-x="576" d="M131.5 193q-4.5 21 9.5 45l162 267q8 12 15 22t15 16q-8 5 -15 14.5t-15 22.5l-162 267q-14 24 -9.5 44.5t28.5 32.5l59 28l255 -398v-24l-255 -398l-59 29q-24 11 -28.5 32z" />
<glyph unicode="&#x205f;" horiz-adv-x="479" />
<glyph unicode="&#x20ac;" d="M24 566v105h137q-1 15 -1 30.5v31.5q0 20 0.5 41.5t1.5 41.5h-138v105h150q20 129 69 232.5t121 177t166 112.5t204 39q69 0 127.5 -13.5t108 -38t91.5 -60.5t78 -81l-64 -69q-7 -9 -15.5 -16t-23.5 -7q-17 0 -36 19.5t-50.5 43t-81 43t-126.5 19.5q-150 0 -248.5 -102.5 t-131.5 -298.5h561v-57q0 -18 -14 -33t-40 -15h-519q-1 -20 -1.5 -40.5t-0.5 -42.5v-31.5t1 -30.5h478v-58q0 -17 -14.5 -32t-38.5 -15h-416q29 -211 127 -316.5t247 -105.5q55 0 97 11t73 27.5t53 35.5t38.5 35t29 27t25.5 11q9 0 15 -3t14 -12l77 -71 q-73 -103 -179.5 -161.5t-251.5 -58.5q-119 0 -214 40.5t-165.5 115.5t-114.5 183t-60 242h-145z" />
<glyph unicode="&#x2122;" horiz-adv-x="1537" d="M90 1360v107h498v-107h-185v-499h-128v499h-185zM686 861v606h109q17 0 24.5 -4t19.5 -17l173 -310q13 -28 20 -54q8 29 22 54l173 310q10 13 17.5 17t25.5 4h107v-606h-111v374l8 76l-188 -343q-12 -29 -44 -29h-18q-32 0 -45 29l-188 343l9 -76v-374h-114z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1039" d="M0 0v1039h1039v-1039h-1039z" />
<hkern u1="&#x20;" u2="&#x178;" k="80" />
<hkern u1="&#x20;" u2="&#xff;" k="51" />
<hkern u1="&#x20;" u2="&#xfd;" k="51" />
<hkern u1="&#x20;" u2="&#xdd;" k="80" />
<hkern u1="&#x20;" u2="&#xc6;" k="78" />
<hkern u1="&#x20;" u2="&#xc5;" k="69" />
<hkern u1="&#x20;" u2="&#xc4;" k="69" />
<hkern u1="&#x20;" u2="&#xc3;" k="69" />
<hkern u1="&#x20;" u2="&#xc2;" k="69" />
<hkern u1="&#x20;" u2="&#xc1;" k="69" />
<hkern u1="&#x20;" u2="&#xc0;" k="69" />
<hkern u1="&#x20;" u2="y" k="51" />
<hkern u1="&#x20;" u2="w" k="42" />
<hkern u1="&#x20;" u2="v" k="49" />
<hkern u1="&#x20;" u2="Y" k="80" />
<hkern u1="&#x20;" u2="W" k="28" />
<hkern u1="&#x20;" u2="V" k="74" />
<hkern u1="&#x20;" u2="T" k="61" />
<hkern u1="&#x20;" u2="J" k="59" />
<hkern u1="&#x20;" u2="A" k="69" />
<hkern u1="&#x22;" u2="&#xf0;" k="46" />
<hkern u1="&#x22;" u2="&#xef;" k="-26" />
<hkern u1="&#x22;" u2="&#xee;" k="-45" />
<hkern u1="&#x22;" u2="&#xec;" k="-34" />
<hkern u1="&#x22;" u2="&#x2f;" k="100" />
<hkern u1="&#x22;" u2="&#x2c;" k="209" />
<hkern u1="&#x22;" u2="&#x26;" k="37" />
<hkern u1="&#x26;" u2="&#x201d;" k="39" />
<hkern u1="&#x26;" u2="&#x2019;" k="39" />
<hkern u1="&#x26;" u2="&#x178;" k="111" />
<hkern u1="&#x26;" u2="&#xff;" k="20" />
<hkern u1="&#x26;" u2="&#xfd;" k="20" />
<hkern u1="&#x26;" u2="&#xdd;" k="111" />
<hkern u1="&#x26;" u2="&#xc6;" k="-51" />
<hkern u1="&#x26;" u2="&#xc5;" k="-47" />
<hkern u1="&#x26;" u2="&#xc4;" k="-47" />
<hkern u1="&#x26;" u2="&#xc3;" k="-47" />
<hkern u1="&#x26;" u2="&#xc2;" k="-47" />
<hkern u1="&#x26;" u2="&#xc1;" k="-47" />
<hkern u1="&#x26;" u2="&#xc0;" k="-47" />
<hkern u1="&#x26;" u2="y" k="20" />
<hkern u1="&#x26;" u2="x" k="-33" />
<hkern u1="&#x26;" u2="v" k="19" />
<hkern u1="&#x26;" u2="Z" k="-17" />
<hkern u1="&#x26;" u2="Y" k="111" />
<hkern u1="&#x26;" u2="X" k="-40" />
<hkern u1="&#x26;" u2="W" k="62" />
<hkern u1="&#x26;" u2="V" k="87" />
<hkern u1="&#x26;" u2="T" k="101" />
<hkern u1="&#x26;" u2="J" k="-19" />
<hkern u1="&#x26;" u2="A" k="-47" />
<hkern u1="&#x26;" u2="&#x27;" k="57" />
<hkern u1="&#x26;" u2="&#x22;" k="57" />
<hkern u1="&#x27;" u2="&#xf0;" k="46" />
<hkern u1="&#x27;" u2="&#xef;" k="-26" />
<hkern u1="&#x27;" u2="&#xee;" k="-45" />
<hkern u1="&#x27;" u2="&#xec;" k="-34" />
<hkern u1="&#x27;" u2="&#x2f;" k="100" />
<hkern u1="&#x27;" u2="&#x2c;" k="209" />
<hkern u1="&#x27;" u2="&#x26;" k="37" />
<hkern u1="&#x28;" g2="uniFB04" k="34" />
<hkern u1="&#x28;" g2="uniFB03" k="34" />
<hkern u1="&#x28;" g2="uniFB02" k="34" />
<hkern u1="&#x28;" g2="uniFB01" k="34" />
<hkern u1="&#x28;" u2="&#x178;" k="-13" />
<hkern u1="&#x28;" u2="&#x153;" k="61" />
<hkern u1="&#x28;" u2="&#x152;" k="43" />
<hkern u1="&#x28;" u2="&#xff;" k="20" />
<hkern u1="&#x28;" u2="&#xfd;" k="20" />
<hkern u1="&#x28;" u2="&#xfc;" k="45" />
<hkern u1="&#x28;" u2="&#xfb;" k="45" />
<hkern u1="&#x28;" u2="&#xfa;" k="45" />
<hkern u1="&#x28;" u2="&#xf9;" k="45" />
<hkern u1="&#x28;" u2="&#xf8;" k="61" />
<hkern u1="&#x28;" u2="&#xf6;" k="61" />
<hkern u1="&#x28;" u2="&#xf5;" k="61" />
<hkern u1="&#x28;" u2="&#xf4;" k="61" />
<hkern u1="&#x28;" u2="&#xf3;" k="61" />
<hkern u1="&#x28;" u2="&#xf2;" k="61" />
<hkern u1="&#x28;" u2="&#xf1;" k="16" />
<hkern u1="&#x28;" u2="&#xf0;" k="51" />
<hkern u1="&#x28;" u2="&#xef;" k="-26" />
<hkern u1="&#x28;" u2="&#xee;" k="-11" />
<hkern u1="&#x28;" u2="&#xec;" k="-51" />
<hkern u1="&#x28;" u2="&#xeb;" k="61" />
<hkern u1="&#x28;" u2="&#xea;" k="61" />
<hkern u1="&#x28;" u2="&#xe9;" k="61" />
<hkern u1="&#x28;" u2="&#xe8;" k="61" />
<hkern u1="&#x28;" u2="&#xe7;" k="61" />
<hkern u1="&#x28;" u2="&#xe6;" k="39" />
<hkern u1="&#x28;" u2="&#xe5;" k="39" />
<hkern u1="&#x28;" u2="&#xe4;" k="39" />
<hkern u1="&#x28;" u2="&#xe3;" k="39" />
<hkern u1="&#x28;" u2="&#xe2;" k="39" />
<hkern u1="&#x28;" u2="&#xe1;" k="39" />
<hkern u1="&#x28;" u2="&#xe0;" k="39" />
<hkern u1="&#x28;" u2="&#xdd;" k="-13" />
<hkern u1="&#x28;" u2="&#xd8;" k="43" />
<hkern u1="&#x28;" u2="&#xd6;" k="43" />
<hkern u1="&#x28;" u2="&#xd5;" k="43" />
<hkern u1="&#x28;" u2="&#xd4;" k="43" />
<hkern u1="&#x28;" u2="&#xd3;" k="43" />
<hkern u1="&#x28;" u2="&#xd2;" k="43" />
<hkern u1="&#x28;" u2="&#xc7;" k="43" />
<hkern u1="&#x28;" u2="y" k="20" />
<hkern u1="&#x28;" u2="w" k="40" />
<hkern u1="&#x28;" u2="v" k="47" />
<hkern u1="&#x28;" u2="u" k="45" />
<hkern u1="&#x28;" u2="t" k="39" />
<hkern u1="&#x28;" u2="s" k="38" />
<hkern u1="&#x28;" u2="r" k="16" />
<hkern u1="&#x28;" u2="q" k="58" />
<hkern u1="&#x28;" u2="p" k="16" />
<hkern u1="&#x28;" u2="o" k="61" />
<hkern u1="&#x28;" u2="n" k="16" />
<hkern u1="&#x28;" u2="m" k="16" />
<hkern u1="&#x28;" u2="j" k="-35" />
<hkern u1="&#x28;" u2="f" k="34" />
<hkern u1="&#x28;" u2="e" k="61" />
<hkern u1="&#x28;" u2="d" k="58" />
<hkern u1="&#x28;" u2="c" k="61" />
<hkern u1="&#x28;" u2="a" k="39" />
<hkern u1="&#x28;" u2="Y" k="-13" />
<hkern u1="&#x28;" u2="V" k="-13" />
<hkern u1="&#x28;" u2="Q" k="43" />
<hkern u1="&#x28;" u2="O" k="43" />
<hkern u1="&#x28;" u2="G" k="43" />
<hkern u1="&#x28;" u2="C" k="43" />
<hkern u1="&#x2a;" u2="&#x153;" k="55" />
<hkern u1="&#x2a;" u2="&#xf8;" k="55" />
<hkern u1="&#x2a;" u2="&#xf6;" k="55" />
<hkern u1="&#x2a;" u2="&#xf5;" k="55" />
<hkern u1="&#x2a;" u2="&#xf4;" k="55" />
<hkern u1="&#x2a;" u2="&#xf3;" k="55" />
<hkern u1="&#x2a;" u2="&#xf2;" k="55" />
<hkern u1="&#x2a;" u2="&#xf0;" k="56" />
<hkern u1="&#x2a;" u2="&#xef;" k="-39" />
<hkern u1="&#x2a;" u2="&#xee;" k="-13" />
<hkern u1="&#x2a;" u2="&#xeb;" k="55" />
<hkern u1="&#x2a;" u2="&#xea;" k="55" />
<hkern u1="&#x2a;" u2="&#xe9;" k="55" />
<hkern u1="&#x2a;" u2="&#xe8;" k="55" />
<hkern u1="&#x2a;" u2="&#xe7;" k="55" />
<hkern u1="&#x2a;" u2="&#xe6;" k="41" />
<hkern u1="&#x2a;" u2="&#xe5;" k="41" />
<hkern u1="&#x2a;" u2="&#xe4;" k="41" />
<hkern u1="&#x2a;" u2="&#xe3;" k="41" />
<hkern u1="&#x2a;" u2="&#xe2;" k="41" />
<hkern u1="&#x2a;" u2="&#xe1;" k="41" />
<hkern u1="&#x2a;" u2="&#xe0;" k="41" />
<hkern u1="&#x2a;" u2="&#xc6;" k="176" />
<hkern u1="&#x2a;" u2="&#xc5;" k="115" />
<hkern u1="&#x2a;" u2="&#xc4;" k="115" />
<hkern u1="&#x2a;" u2="&#xc3;" k="115" />
<hkern u1="&#x2a;" u2="&#xc2;" k="115" />
<hkern u1="&#x2a;" u2="&#xc1;" k="115" />
<hkern u1="&#x2a;" u2="&#xc0;" k="115" />
<hkern u1="&#x2a;" u2="s" k="38" />
<hkern u1="&#x2a;" u2="q" k="63" />
<hkern u1="&#x2a;" u2="o" k="55" />
<hkern u1="&#x2a;" u2="g" k="43" />
<hkern u1="&#x2a;" u2="e" k="55" />
<hkern u1="&#x2a;" u2="d" k="63" />
<hkern u1="&#x2a;" u2="c" k="55" />
<hkern u1="&#x2a;" u2="a" k="41" />
<hkern u1="&#x2a;" u2="J" k="143" />
<hkern u1="&#x2a;" u2="A" k="115" />
<hkern u1="&#x2c;" u2="&#x201d;" k="218" />
<hkern u1="&#x2c;" u2="&#x201c;" k="219" />
<hkern u1="&#x2c;" u2="&#x2019;" k="218" />
<hkern u1="&#x2c;" u2="&#x2018;" k="219" />
<hkern u1="&#x2c;" u2="&#x178;" k="165" />
<hkern u1="&#x2c;" u2="&#x152;" k="52" />
<hkern u1="&#x2c;" u2="&#xff;" k="77" />
<hkern u1="&#x2c;" u2="&#xfd;" k="77" />
<hkern u1="&#x2c;" u2="&#xdd;" k="165" />
<hkern u1="&#x2c;" u2="&#xdc;" k="46" />
<hkern u1="&#x2c;" u2="&#xdb;" k="46" />
<hkern u1="&#x2c;" u2="&#xda;" k="46" />
<hkern u1="&#x2c;" u2="&#xd9;" k="46" />
<hkern u1="&#x2c;" u2="&#xd8;" k="52" />
<hkern u1="&#x2c;" u2="&#xd6;" k="52" />
<hkern u1="&#x2c;" u2="&#xd5;" k="52" />
<hkern u1="&#x2c;" u2="&#xd4;" k="52" />
<hkern u1="&#x2c;" u2="&#xd3;" k="52" />
<hkern u1="&#x2c;" u2="&#xd2;" k="52" />
<hkern u1="&#x2c;" u2="&#xc7;" k="52" />
<hkern u1="&#x2c;" u2="y" k="77" />
<hkern u1="&#x2c;" u2="w" k="47" />
<hkern u1="&#x2c;" u2="v" k="73" />
<hkern u1="&#x2c;" u2="Y" k="165" />
<hkern u1="&#x2c;" u2="W" k="111" />
<hkern u1="&#x2c;" u2="V" k="155" />
<hkern u1="&#x2c;" u2="U" k="46" />
<hkern u1="&#x2c;" u2="T" k="144" />
<hkern u1="&#x2c;" u2="Q" k="52" />
<hkern u1="&#x2c;" u2="O" k="52" />
<hkern u1="&#x2c;" u2="G" k="52" />
<hkern u1="&#x2c;" u2="C" k="52" />
<hkern u1="&#x2c;" u2="&#x27;" k="209" />
<hkern u1="&#x2c;" u2="&#x22;" k="209" />
<hkern u1="&#x2e;" u2="&#x201d;" k="218" />
<hkern u1="&#x2e;" u2="&#x201c;" k="219" />
<hkern u1="&#x2e;" u2="&#x2019;" k="218" />
<hkern u1="&#x2e;" u2="&#x2018;" k="219" />
<hkern u1="&#x2e;" u2="&#x178;" k="169" />
<hkern u1="&#x2e;" u2="&#x152;" k="47" />
<hkern u1="&#x2e;" u2="&#xff;" k="77" />
<hkern u1="&#x2e;" u2="&#xfd;" k="77" />
<hkern u1="&#x2e;" u2="&#xdd;" k="169" />
<hkern u1="&#x2e;" u2="&#xdc;" k="41" />
<hkern u1="&#x2e;" u2="&#xdb;" k="41" />
<hkern u1="&#x2e;" u2="&#xda;" k="41" />
<hkern u1="&#x2e;" u2="&#xd9;" k="41" />
<hkern u1="&#x2e;" u2="&#xd8;" k="47" />
<hkern u1="&#x2e;" u2="&#xd6;" k="47" />
<hkern u1="&#x2e;" u2="&#xd5;" k="47" />
<hkern u1="&#x2e;" u2="&#xd4;" k="47" />
<hkern u1="&#x2e;" u2="&#xd3;" k="47" />
<hkern u1="&#x2e;" u2="&#xd2;" k="47" />
<hkern u1="&#x2e;" u2="&#xc7;" k="47" />
<hkern u1="&#x2e;" u2="y" k="77" />
<hkern u1="&#x2e;" u2="w" k="44" />
<hkern u1="&#x2e;" u2="v" k="70" />
<hkern u1="&#x2e;" u2="Y" k="169" />
<hkern u1="&#x2e;" u2="W" k="110" />
<hkern u1="&#x2e;" u2="V" k="151" />
<hkern u1="&#x2e;" u2="U" k="41" />
<hkern u1="&#x2e;" u2="T" k="144" />
<hkern u1="&#x2e;" u2="Q" k="47" />
<hkern u1="&#x2e;" u2="O" k="47" />
<hkern u1="&#x2e;" u2="G" k="47" />
<hkern u1="&#x2e;" u2="C" k="47" />
<hkern u1="&#x2e;" u2="&#x27;" k="209" />
<hkern u1="&#x2e;" u2="&#x22;" k="209" />
<hkern u1="&#x2f;" g2="uniFB04" k="19" />
<hkern u1="&#x2f;" g2="uniFB03" k="19" />
<hkern u1="&#x2f;" g2="uniFB02" k="19" />
<hkern u1="&#x2f;" g2="uniFB01" k="19" />
<hkern u1="&#x2f;" u2="&#x178;" k="-9" />
<hkern u1="&#x2f;" u2="&#x153;" k="93" />
<hkern u1="&#x2f;" u2="&#x152;" k="18" />
<hkern u1="&#x2f;" u2="&#xfc;" k="52" />
<hkern u1="&#x2f;" u2="&#xfb;" k="52" />
<hkern u1="&#x2f;" u2="&#xfa;" k="52" />
<hkern u1="&#x2f;" u2="&#xf9;" k="52" />
<hkern u1="&#x2f;" u2="&#xf8;" k="93" />
<hkern u1="&#x2f;" u2="&#xf6;" k="93" />
<hkern u1="&#x2f;" u2="&#xf5;" k="93" />
<hkern u1="&#x2f;" u2="&#xf4;" k="93" />
<hkern u1="&#x2f;" u2="&#xf3;" k="93" />
<hkern u1="&#x2f;" u2="&#xf2;" k="93" />
<hkern u1="&#x2f;" u2="&#xf1;" k="57" />
<hkern u1="&#x2f;" u2="&#xf0;" k="68" />
<hkern u1="&#x2f;" u2="&#xef;" k="-11" />
<hkern u1="&#x2f;" u2="&#xec;" k="-50" />
<hkern u1="&#x2f;" u2="&#xeb;" k="93" />
<hkern u1="&#x2f;" u2="&#xea;" k="93" />
<hkern u1="&#x2f;" u2="&#xe9;" k="93" />
<hkern u1="&#x2f;" u2="&#xe8;" k="93" />
<hkern u1="&#x2f;" u2="&#xe7;" k="93" />
<hkern u1="&#x2f;" u2="&#xe6;" k="82" />
<hkern u1="&#x2f;" u2="&#xe5;" k="82" />
<hkern u1="&#x2f;" u2="&#xe4;" k="82" />
<hkern u1="&#x2f;" u2="&#xe3;" k="82" />
<hkern u1="&#x2f;" u2="&#xe2;" k="82" />
<hkern u1="&#x2f;" u2="&#xe1;" k="82" />
<hkern u1="&#x2f;" u2="&#xe0;" k="82" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-9" />
<hkern u1="&#x2f;" u2="&#xd8;" k="18" />
<hkern u1="&#x2f;" u2="&#xd6;" k="18" />
<hkern u1="&#x2f;" u2="&#xd5;" k="18" />
<hkern u1="&#x2f;" u2="&#xd4;" k="18" />
<hkern u1="&#x2f;" u2="&#xd3;" k="18" />
<hkern u1="&#x2f;" u2="&#xd2;" k="18" />
<hkern u1="&#x2f;" u2="&#xc7;" k="18" />
<hkern u1="&#x2f;" u2="&#xc6;" k="153" />
<hkern u1="&#x2f;" u2="&#xc5;" k="116" />
<hkern u1="&#x2f;" u2="&#xc4;" k="116" />
<hkern u1="&#x2f;" u2="&#xc3;" k="116" />
<hkern u1="&#x2f;" u2="&#xc2;" k="116" />
<hkern u1="&#x2f;" u2="&#xc1;" k="116" />
<hkern u1="&#x2f;" u2="&#xc0;" k="116" />
<hkern u1="&#x2f;" u2="z" k="31" />
<hkern u1="&#x2f;" u2="x" k="19" />
<hkern u1="&#x2f;" u2="u" k="52" />
<hkern u1="&#x2f;" u2="t" k="23" />
<hkern u1="&#x2f;" u2="s" k="77" />
<hkern u1="&#x2f;" u2="r" k="57" />
<hkern u1="&#x2f;" u2="q" k="94" />
<hkern u1="&#x2f;" u2="p" k="57" />
<hkern u1="&#x2f;" u2="o" k="93" />
<hkern u1="&#x2f;" u2="n" k="57" />
<hkern u1="&#x2f;" u2="m" k="57" />
<hkern u1="&#x2f;" u2="g" k="86" />
<hkern u1="&#x2f;" u2="f" k="19" />
<hkern u1="&#x2f;" u2="e" k="93" />
<hkern u1="&#x2f;" u2="d" k="94" />
<hkern u1="&#x2f;" u2="c" k="93" />
<hkern u1="&#x2f;" u2="a" k="82" />
<hkern u1="&#x2f;" u2="Y" k="-9" />
<hkern u1="&#x2f;" u2="V" k="-8" />
<hkern u1="&#x2f;" u2="Q" k="18" />
<hkern u1="&#x2f;" u2="O" k="18" />
<hkern u1="&#x2f;" u2="J" k="117" />
<hkern u1="&#x2f;" u2="G" k="18" />
<hkern u1="&#x2f;" u2="C" k="18" />
<hkern u1="&#x2f;" u2="A" k="116" />
<hkern u1="&#x2f;" u2="&#x2f;" k="458" />
<hkern u1="&#x3a;" u2="&#x178;" k="77" />
<hkern u1="&#x3a;" u2="&#xdd;" k="77" />
<hkern u1="&#x3a;" u2="Y" k="77" />
<hkern u1="&#x3a;" u2="W" k="18" />
<hkern u1="&#x3a;" u2="V" k="31" />
<hkern u1="&#x3a;" u2="T" k="109" />
<hkern u1="&#x3b;" u2="&#x178;" k="76" />
<hkern u1="&#x3b;" u2="&#xdd;" k="76" />
<hkern u1="&#x3b;" u2="Y" k="76" />
<hkern u1="&#x3b;" u2="W" k="18" />
<hkern u1="&#x3b;" u2="V" k="31" />
<hkern u1="&#x3b;" u2="T" k="106" />
<hkern u1="&#x40;" u2="&#x178;" k="69" />
<hkern u1="&#x40;" u2="&#xdd;" k="69" />
<hkern u1="&#x40;" u2="Y" k="69" />
<hkern u1="&#x40;" u2="V" k="20" />
<hkern u1="&#x40;" u2="T" k="67" />
<hkern u1="A" u2="&#x2122;" k="128" />
<hkern u1="A" u2="&#xf0;" k="25" />
<hkern u1="A" u2="&#xba;" k="113" />
<hkern u1="A" u2="&#xae;" k="18" />
<hkern u1="A" u2="&#xaa;" k="100" />
<hkern u1="A" u2="&#xa9;" k="18" />
<hkern u1="A" u2="&#x7d;" k="18" />
<hkern u1="A" u2="]" k="37" />
<hkern u1="A" u2="\" k="116" />
<hkern u1="A" u2="&#x2a;" k="116" />
<hkern u1="A" u2="&#x20;" k="69" />
<hkern u1="B" u2="&#xf0;" k="15" />
<hkern u1="B" u2="&#xee;" k="-1" />
<hkern u1="B" u2="&#x7d;" k="20" />
<hkern u1="B" u2="]" k="24" />
<hkern u1="C" u2="&#x2122;" k="-11" />
<hkern u1="C" u2="&#xf0;" k="18" />
<hkern u1="C" u2="&#xef;" k="-44" />
<hkern u1="C" u2="&#xee;" k="-23" />
<hkern u1="C" u2="&#x2a;" k="-8" />
<hkern u1="D" u2="&#xf0;" k="18" />
<hkern u1="D" u2="&#xd0;" k="10" />
<hkern u1="D" u2="&#x7d;" k="44" />
<hkern u1="D" u2="]" k="52" />
<hkern u1="D" u2="&#x2f;" k="24" />
<hkern u1="D" u2="&#x2c;" k="54" />
<hkern u1="D" u2="&#x29;" k="41" />
<hkern u1="E" u2="&#xf0;" k="48" />
<hkern u1="E" u2="&#xef;" k="-15" />
<hkern u1="E" u2="&#xee;" k="-28" />
<hkern u1="E" u2="&#xed;" k="11" />
<hkern u1="E" u2="&#xec;" k="-22" />
<hkern u1="E" u2="&#xdf;" k="27" />
<hkern u1="F" u2="&#xf0;" k="108" />
<hkern u1="F" u2="&#xef;" k="-24" />
<hkern u1="F" u2="&#xee;" k="-36" />
<hkern u1="F" u2="&#xed;" k="38" />
<hkern u1="F" u2="&#xec;" k="-30" />
<hkern u1="F" u2="&#xdf;" k="58" />
<hkern u1="F" u2="&#x3b;" k="16" />
<hkern u1="F" u2="&#x3a;" k="16" />
<hkern u1="F" u2="&#x2f;" k="93" />
<hkern u1="F" u2="&#x2c;" k="185" />
<hkern u1="F" u2="&#x20;" k="47" />
<hkern u1="G" u2="&#xf0;" k="12" />
<hkern u1="G" u2="&#xef;" k="-5" />
<hkern u1="G" u2="]" k="17" />
<hkern u1="H" u2="&#xf0;" k="35" />
<hkern u1="H" u2="&#xee;" k="8" />
<hkern u1="I" u2="&#xf0;" k="35" />
<hkern u1="I" u2="&#xee;" k="8" />
<hkern u1="J" u2="&#xf0;" k="41" />
<hkern u1="J" u2="&#xee;" k="3" />
<hkern u1="J" u2="&#xed;" k="25" />
<hkern u1="J" u2="&#x2f;" k="17" />
<hkern u1="J" u2="&#x2c;" k="24" />
<hkern u1="K" u2="&#x2122;" k="-13" />
<hkern u1="K" u2="&#xf8;" k="55" />
<hkern u1="K" u2="&#xf0;" k="75" />
<hkern u1="K" u2="&#xef;" k="-12" />
<hkern u1="K" u2="&#xec;" k="-59" />
<hkern u1="K" u2="&#xae;" k="43" />
<hkern u1="K" u2="&#xa9;" k="43" />
<hkern u1="L" u2="&#x2122;" k="212" />
<hkern u1="L" u2="&#xf0;" k="70" />
<hkern u1="L" u2="&#xba;" k="210" />
<hkern u1="L" u2="&#xb7;" k="294" />
<hkern u1="L" u2="&#xae;" k="37" />
<hkern u1="L" u2="&#xaa;" k="209" />
<hkern u1="L" u2="&#xa9;" k="37" />
<hkern u1="L" u2="\" k="144" />
<hkern u1="L" u2="&#x2a;" k="212" />
<hkern u1="L" u2="&#x20;" k="61" />
<hkern u1="M" u2="&#xf0;" k="35" />
<hkern u1="M" u2="&#xee;" k="8" />
<hkern u1="N" u2="&#xf0;" k="35" />
<hkern u1="N" u2="&#xee;" k="8" />
<hkern u1="O" u2="&#xf0;" k="17" />
<hkern u1="O" u2="&#xd0;" k="10" />
<hkern u1="O" u2="&#x7d;" k="42" />
<hkern u1="O" u2="]" k="51" />
<hkern u1="O" u2="&#x2f;" k="23" />
<hkern u1="O" u2="&#x2c;" k="54" />
<hkern u1="O" u2="&#x29;" k="40" />
<hkern u1="P" u2="&#xf0;" k="90" />
<hkern u1="P" u2="&#xee;" k="-36" />
<hkern u1="P" u2="]" k="17" />
<hkern u1="P" u2="&#x2f;" k="92" />
<hkern u1="P" u2="&#x2c;" k="196" />
<hkern u1="P" u2="&#x26;" k="16" />
<hkern u1="P" u2="&#x20;" k="56" />
<hkern u1="Q" u2="&#x201e;" k="10" />
<hkern u1="Q" u2="&#x201a;" k="10" />
<hkern u1="Q" u2="&#xf0;" k="17" />
<hkern u1="Q" u2="&#xd0;" k="10" />
<hkern u1="Q" u2="&#x7d;" k="9" />
<hkern u1="Q" u2="]" k="11" />
<hkern u1="Q" u2="&#x2f;" k="23" />
<hkern u1="Q" u2="&#x2c;" k="54" />
<hkern u1="Q" u2="&#x29;" k="31" />
<hkern u1="R" u2="&#xf8;" k="53" />
<hkern u1="R" u2="&#xf0;" k="72" />
<hkern u1="R" u2="&#xee;" k="4" />
<hkern u1="R" u2="&#xd0;" k="7" />
<hkern u1="S" u2="&#xf0;" k="6" />
<hkern u1="S" u2="&#xef;" k="-4" />
<hkern u1="S" u2="]" k="17" />
<hkern u1="T" g2="uniFB02" k="133" />
<hkern u1="T" g2="uniFB01" k="132" />
<hkern u1="T" u2="&#x2122;" k="-8" />
<hkern u1="T" u2="&#xf6;" k="231" />
<hkern u1="T" u2="&#xf4;" k="232" />
<hkern u1="T" u2="&#xf2;" k="242" />
<hkern u1="T" u2="&#xf0;" k="168" />
<hkern u1="T" u2="&#xef;" k="-33" />
<hkern u1="T" u2="&#xee;" k="-57" />
<hkern u1="T" u2="&#xed;" k="75" />
<hkern u1="T" u2="&#xec;" k="-47" />
<hkern u1="T" u2="&#xeb;" k="223" />
<hkern u1="T" u2="&#xea;" k="225" />
<hkern u1="T" u2="&#xe8;" k="234" />
<hkern u1="T" u2="&#xe4;" k="187" />
<hkern u1="T" u2="&#xe3;" k="204" />
<hkern u1="T" u2="&#xe2;" k="188" />
<hkern u1="T" u2="&#xe0;" k="198" />
<hkern u1="T" u2="&#xdf;" k="102" />
<hkern u1="T" u2="&#xae;" k="33" />
<hkern u1="T" u2="&#xa9;" k="33" />
<hkern u1="T" u2="&#x40;" k="93" />
<hkern u1="T" u2="&#x3b;" k="105" />
<hkern u1="T" u2="&#x3a;" k="109" />
<hkern u1="T" u2="&#x2f;" k="128" />
<hkern u1="T" u2="&#x2c;" k="145" />
<hkern u1="T" u2="&#x26;" k="55" />
<hkern u1="T" u2="&#x20;" k="61" />
<hkern u1="U" u2="&#xf0;" k="46" />
<hkern u1="U" u2="&#xee;" k="3" />
<hkern u1="U" u2="&#xed;" k="31" />
<hkern u1="U" u2="&#xdf;" k="31" />
<hkern u1="U" u2="&#x2f;" k="25" />
<hkern u1="U" u2="&#x2c;" k="47" />
<hkern u1="V" u2="&#x2122;" k="-31" />
<hkern u1="V" u2="&#xf0;" k="134" />
<hkern u1="V" u2="&#xef;" k="-45" />
<hkern u1="V" u2="&#xee;" k="-29" />
<hkern u1="V" u2="&#xed;" k="46" />
<hkern u1="V" u2="&#xec;" k="-78" />
<hkern u1="V" u2="&#xe8;" k="146" />
<hkern u1="V" u2="&#xe4;" k="133" />
<hkern u1="V" u2="&#xe0;" k="117" />
<hkern u1="V" u2="&#xdf;" k="74" />
<hkern u1="V" u2="&#xae;" k="19" />
<hkern u1="V" u2="&#xa9;" k="19" />
<hkern u1="V" u2="&#x7d;" k="-14" />
<hkern u1="V" u2="]" k="-17" />
<hkern u1="V" u2="\" k="-8" />
<hkern u1="V" u2="&#x40;" k="54" />
<hkern u1="V" u2="&#x3b;" k="29" />
<hkern u1="V" u2="&#x3a;" k="30" />
<hkern u1="V" u2="&#x2f;" k="131" />
<hkern u1="V" u2="&#x2c;" k="155" />
<hkern u1="V" u2="&#x29;" k="-13" />
<hkern u1="V" u2="&#x26;" k="59" />
<hkern u1="V" u2="&#x20;" k="74" />
<hkern u1="W" u2="&#x2122;" k="-14" />
<hkern u1="W" u2="&#xf0;" k="120" />
<hkern u1="W" u2="&#xef;" k="-33" />
<hkern u1="W" u2="&#xee;" k="-26" />
<hkern u1="W" u2="&#xed;" k="42" />
<hkern u1="W" u2="&#xec;" k="-60" />
<hkern u1="W" u2="&#xdf;" k="66" />
<hkern u1="W" u2="&#xd0;" k="13" />
<hkern u1="W" u2="&#x40;" k="19" />
<hkern u1="W" u2="&#x3b;" k="17" />
<hkern u1="W" u2="&#x3a;" k="18" />
<hkern u1="W" u2="&#x2f;" k="96" />
<hkern u1="W" u2="&#x2c;" k="113" />
<hkern u1="W" u2="&#x26;" k="30" />
<hkern u1="W" u2="&#x20;" k="28" />
<hkern u1="X" u2="&#x2122;" k="-16" />
<hkern u1="X" u2="&#xf8;" k="65" />
<hkern u1="X" u2="&#xf0;" k="77" />
<hkern u1="X" u2="&#xef;" k="-18" />
<hkern u1="X" u2="&#xed;" k="19" />
<hkern u1="X" u2="&#xec;" k="-62" />
<hkern u1="X" u2="&#xdf;" k="18" />
<hkern u1="X" u2="&#xae;" k="35" />
<hkern u1="X" u2="&#xa9;" k="35" />
<hkern u1="Y" g2="uniFB02" k="109" />
<hkern u1="Y" u2="&#x2122;" k="-31" />
<hkern u1="Y" u2="&#xf9;" k="158" />
<hkern u1="Y" u2="&#xf6;" k="213" />
<hkern u1="Y" u2="&#xf2;" k="188" />
<hkern u1="Y" u2="&#xf0;" k="161" />
<hkern u1="Y" u2="&#xef;" k="-36" />
<hkern u1="Y" u2="&#xed;" k="72" />
<hkern u1="Y" u2="&#xec;" k="-78" />
<hkern u1="Y" u2="&#xeb;" k="206" />
<hkern u1="Y" u2="&#xe8;" k="181" />
<hkern u1="Y" u2="&#xe4;" k="176" />
<hkern u1="Y" u2="&#xe3;" k="212" />
<hkern u1="Y" u2="&#xe0;" k="152" />
<hkern u1="Y" u2="&#xdf;" k="99" />
<hkern u1="Y" u2="&#xae;" k="61" />
<hkern u1="Y" u2="&#xa9;" k="62" />
<hkern u1="Y" u2="&#x7d;" k="-14" />
<hkern u1="Y" u2="]" k="-17" />
<hkern u1="Y" u2="\" k="-9" />
<hkern u1="Y" u2="&#x40;" k="95" />
<hkern u1="Y" u2="&#x3b;" k="75" />
<hkern u1="Y" u2="&#x3a;" k="77" />
<hkern u1="Y" u2="&#x2f;" k="151" />
<hkern u1="Y" u2="&#x2c;" k="166" />
<hkern u1="Y" u2="&#x29;" k="-13" />
<hkern u1="Y" u2="&#x26;" k="82" />
<hkern u1="Y" u2="&#x20;" k="81" />
<hkern u1="Z" u2="&#xf0;" k="46" />
<hkern u1="Z" u2="&#xef;" k="-28" />
<hkern u1="Z" u2="&#xee;" k="-9" />
<hkern u1="Z" u2="&#xed;" k="13" />
<hkern u1="Z" u2="&#xec;" k="-35" />
<hkern u1="Z" u2="&#xdf;" k="25" />
<hkern u1="Z" u2="&#xae;" k="18" />
<hkern u1="Z" u2="&#xa9;" k="18" />
<hkern u1="[" g2="uniFB04" k="49" />
<hkern u1="[" g2="uniFB03" k="49" />
<hkern u1="[" g2="uniFB02" k="49" />
<hkern u1="[" g2="uniFB01" k="49" />
<hkern u1="[" u2="&#x178;" k="-17" />
<hkern u1="[" u2="&#x153;" k="71" />
<hkern u1="[" u2="&#x152;" k="51" />
<hkern u1="[" u2="&#xff;" k="46" />
<hkern u1="[" u2="&#xfd;" k="46" />
<hkern u1="[" u2="&#xfc;" k="59" />
<hkern u1="[" u2="&#xfb;" k="59" />
<hkern u1="[" u2="&#xfa;" k="59" />
<hkern u1="[" u2="&#xf9;" k="59" />
<hkern u1="[" u2="&#xf8;" k="71" />
<hkern u1="[" u2="&#xf6;" k="71" />
<hkern u1="[" u2="&#xf5;" k="71" />
<hkern u1="[" u2="&#xf4;" k="71" />
<hkern u1="[" u2="&#xf3;" k="71" />
<hkern u1="[" u2="&#xf2;" k="71" />
<hkern u1="[" u2="&#xf1;" k="55" />
<hkern u1="[" u2="&#xf0;" k="62" />
<hkern u1="[" u2="&#xef;" k="-27" />
<hkern u1="[" u2="&#xec;" k="-58" />
<hkern u1="[" u2="&#xeb;" k="71" />
<hkern u1="[" u2="&#xea;" k="71" />
<hkern u1="[" u2="&#xe9;" k="71" />
<hkern u1="[" u2="&#xe8;" k="71" />
<hkern u1="[" u2="&#xe7;" k="71" />
<hkern u1="[" u2="&#xe6;" k="61" />
<hkern u1="[" u2="&#xe5;" k="61" />
<hkern u1="[" u2="&#xe4;" k="61" />
<hkern u1="[" u2="&#xe3;" k="61" />
<hkern u1="[" u2="&#xe2;" k="61" />
<hkern u1="[" u2="&#xe1;" k="61" />
<hkern u1="[" u2="&#xe0;" k="61" />
<hkern u1="[" u2="&#xdd;" k="-17" />
<hkern u1="[" u2="&#xd8;" k="51" />
<hkern u1="[" u2="&#xd6;" k="51" />
<hkern u1="[" u2="&#xd5;" k="51" />
<hkern u1="[" u2="&#xd4;" k="51" />
<hkern u1="[" u2="&#xd3;" k="51" />
<hkern u1="[" u2="&#xd2;" k="51" />
<hkern u1="[" u2="&#xcf;" k="-33" />
<hkern u1="[" u2="&#xce;" k="-54" />
<hkern u1="[" u2="&#xc7;" k="51" />
<hkern u1="[" u2="&#xc6;" k="52" />
<hkern u1="[" u2="&#xc5;" k="40" />
<hkern u1="[" u2="&#xc4;" k="40" />
<hkern u1="[" u2="&#xc3;" k="40" />
<hkern u1="[" u2="&#xc2;" k="40" />
<hkern u1="[" u2="&#xc1;" k="40" />
<hkern u1="[" u2="&#xc0;" k="40" />
<hkern u1="[" u2="z" k="46" />
<hkern u1="[" u2="y" k="46" />
<hkern u1="[" u2="x" k="44" />
<hkern u1="[" u2="w" k="51" />
<hkern u1="[" u2="v" k="59" />
<hkern u1="[" u2="u" k="59" />
<hkern u1="[" u2="t" k="54" />
<hkern u1="[" u2="s" k="61" />
<hkern u1="[" u2="r" k="55" />
<hkern u1="[" u2="q" k="70" />
<hkern u1="[" u2="p" k="55" />
<hkern u1="[" u2="o" k="71" />
<hkern u1="[" u2="n" k="55" />
<hkern u1="[" u2="m" k="55" />
<hkern u1="[" u2="j" k="-42" />
<hkern u1="[" u2="f" k="49" />
<hkern u1="[" u2="e" k="71" />
<hkern u1="[" u2="d" k="70" />
<hkern u1="[" u2="c" k="71" />
<hkern u1="[" u2="a" k="61" />
<hkern u1="[" u2="Y" k="-17" />
<hkern u1="[" u2="V" k="-17" />
<hkern u1="[" u2="S" k="20" />
<hkern u1="[" u2="Q" k="51" />
<hkern u1="[" u2="O" k="51" />
<hkern u1="[" u2="G" k="51" />
<hkern u1="[" u2="C" k="51" />
<hkern u1="[" u2="A" k="40" />
<hkern u1="\" g2="uniFB04" k="24" />
<hkern u1="\" g2="uniFB03" k="24" />
<hkern u1="\" g2="uniFB02" k="24" />
<hkern u1="\" g2="uniFB01" k="24" />
<hkern u1="\" u2="&#x201d;" k="99" />
<hkern u1="\" u2="&#x2019;" k="99" />
<hkern u1="\" u2="&#x178;" k="151" />
<hkern u1="\" u2="&#x152;" k="25" />
<hkern u1="\" u2="&#xff;" k="73" />
<hkern u1="\" u2="&#xfd;" k="73" />
<hkern u1="\" u2="&#xdd;" k="151" />
<hkern u1="\" u2="&#xdc;" k="24" />
<hkern u1="\" u2="&#xdb;" k="24" />
<hkern u1="\" u2="&#xda;" k="24" />
<hkern u1="\" u2="&#xd9;" k="24" />
<hkern u1="\" u2="&#xd8;" k="25" />
<hkern u1="\" u2="&#xd6;" k="25" />
<hkern u1="\" u2="&#xd5;" k="25" />
<hkern u1="\" u2="&#xd4;" k="25" />
<hkern u1="\" u2="&#xd3;" k="25" />
<hkern u1="\" u2="&#xd2;" k="25" />
<hkern u1="\" u2="&#xc7;" k="25" />
<hkern u1="\" u2="y" k="73" />
<hkern u1="\" u2="w" k="55" />
<hkern u1="\" u2="v" k="78" />
<hkern u1="\" u2="t" k="31" />
<hkern u1="\" u2="f" k="24" />
<hkern u1="\" u2="Y" k="151" />
<hkern u1="\" u2="W" k="96" />
<hkern u1="\" u2="V" k="131" />
<hkern u1="\" u2="U" k="24" />
<hkern u1="\" u2="T" k="131" />
<hkern u1="\" u2="Q" k="25" />
<hkern u1="\" u2="O" k="25" />
<hkern u1="\" u2="G" k="25" />
<hkern u1="\" u2="C" k="25" />
<hkern u1="\" u2="&#x27;" k="101" />
<hkern u1="\" u2="&#x22;" k="101" />
<hkern u1="a" u2="&#x2122;" k="48" />
<hkern u1="a" u2="&#xba;" k="20" />
<hkern u1="a" u2="&#xaa;" k="14" />
<hkern u1="a" u2="&#x7d;" k="53" />
<hkern u1="a" u2="]" k="60" />
<hkern u1="a" u2="\" k="85" />
<hkern u1="a" u2="&#x3f;" k="47" />
<hkern u1="a" u2="&#x2a;" k="43" />
<hkern u1="a" u2="&#x29;" k="36" />
<hkern u1="b" u2="&#x2122;" k="57" />
<hkern u1="b" u2="&#xba;" k="38" />
<hkern u1="b" u2="&#xaa;" k="28" />
<hkern u1="b" u2="&#x7d;" k="60" />
<hkern u1="b" u2="]" k="70" />
<hkern u1="b" u2="\" k="88" />
<hkern u1="b" u2="&#x3f;" k="62" />
<hkern u1="b" u2="&#x2f;" k="18" />
<hkern u1="b" u2="&#x2a;" k="56" />
<hkern u1="b" u2="&#x29;" k="59" />
<hkern u1="c" u2="&#x2122;" k="35" />
<hkern u1="c" u2="&#xf0;" k="24" />
<hkern u1="c" u2="&#x7d;" k="52" />
<hkern u1="c" u2="]" k="59" />
<hkern u1="c" u2="\" k="61" />
<hkern u1="c" u2="&#x3f;" k="19" />
<hkern u1="c" u2="&#x2a;" k="13" />
<hkern u1="c" u2="&#x29;" k="35" />
<hkern u1="d" u2="&#xee;" k="-19" />
<hkern u1="e" u2="&#x2122;" k="51" />
<hkern u1="e" u2="&#xba;" k="33" />
<hkern u1="e" u2="&#x7d;" k="58" />
<hkern u1="e" u2="]" k="65" />
<hkern u1="e" u2="\" k="83" />
<hkern u1="e" u2="&#x3f;" k="51" />
<hkern u1="e" u2="&#x2a;" k="49" />
<hkern u1="e" u2="&#x29;" k="47" />
<hkern u1="f" u2="&#x2122;" k="-7" />
<hkern u1="f" u2="&#xf0;" k="63" />
<hkern u1="f" u2="&#xef;" k="-54" />
<hkern u1="f" u2="&#xee;" k="-17" />
<hkern u1="f" u2="&#xec;" k="-56" />
<hkern u1="f" u2="&#x7d;" k="-8" />
<hkern u1="f" u2="]" k="-11" />
<hkern u1="f" u2="&#x2f;" k="66" />
<hkern u1="f" u2="&#x2c;" k="50" />
<hkern u1="f" u2="&#x29;" k="-8" />
<hkern u1="f" u2="&#x20;" k="38" />
<hkern u1="g" u2="&#xf0;" k="23" />
<hkern u1="g" u2="\" k="26" />
<hkern u1="h" u2="&#x2122;" k="48" />
<hkern u1="h" u2="&#xba;" k="18" />
<hkern u1="h" u2="&#x7d;" k="52" />
<hkern u1="h" u2="]" k="60" />
<hkern u1="h" u2="\" k="85" />
<hkern u1="h" u2="&#x3f;" k="47" />
<hkern u1="h" u2="&#x2a;" k="43" />
<hkern u1="h" u2="&#x29;" k="36" />
<hkern u1="i" u2="&#xef;" k="-12" />
<hkern u1="j" u2="&#xef;" k="-12" />
<hkern u1="k" u2="&#x2122;" k="26" />
<hkern u1="k" u2="&#xf0;" k="66" />
<hkern u1="k" u2="&#x7d;" k="23" />
<hkern u1="k" u2="]" k="44" />
<hkern u1="k" u2="\" k="41" />
<hkern u1="l" u2="&#xee;" k="-19" />
<hkern u1="l" u2="&#xb7;" k="67" />
<hkern u1="m" u2="&#x2122;" k="48" />
<hkern u1="m" u2="&#xba;" k="18" />
<hkern u1="m" u2="&#x7d;" k="52" />
<hkern u1="m" u2="]" k="60" />
<hkern u1="m" u2="\" k="85" />
<hkern u1="m" u2="&#x3f;" k="47" />
<hkern u1="m" u2="&#x2a;" k="43" />
<hkern u1="m" u2="&#x29;" k="36" />
<hkern u1="n" u2="&#x2122;" k="48" />
<hkern u1="n" u2="&#xba;" k="18" />
<hkern u1="n" u2="&#x7d;" k="52" />
<hkern u1="n" u2="]" k="60" />
<hkern u1="n" u2="\" k="85" />
<hkern u1="n" u2="&#x3f;" k="47" />
<hkern u1="n" u2="&#x2a;" k="43" />
<hkern u1="n" u2="&#x29;" k="36" />
<hkern u1="o" u2="&#x2122;" k="55" />
<hkern u1="o" u2="&#xba;" k="40" />
<hkern u1="o" u2="&#xaa;" k="17" />
<hkern u1="o" u2="&#x7d;" k="62" />
<hkern u1="o" u2="]" k="71" />
<hkern u1="o" u2="\" k="93" />
<hkern u1="o" u2="&#x3f;" k="61" />
<hkern u1="o" u2="&#x2a;" k="55" />
<hkern u1="o" u2="&#x29;" k="61" />
<hkern u1="p" u2="&#x2122;" k="57" />
<hkern u1="p" u2="&#xba;" k="38" />
<hkern u1="p" u2="&#xaa;" k="28" />
<hkern u1="p" u2="&#x7d;" k="60" />
<hkern u1="p" u2="]" k="70" />
<hkern u1="p" u2="\" k="88" />
<hkern u1="p" u2="&#x3f;" k="62" />
<hkern u1="p" u2="&#x2f;" k="18" />
<hkern u1="p" u2="&#x2a;" k="56" />
<hkern u1="p" u2="&#x29;" k="59" />
<hkern u1="q" u2="&#x2122;" k="30" />
<hkern u1="q" u2="&#x7d;" k="47" />
<hkern u1="q" u2="]" k="55" />
<hkern u1="q" u2="\" k="56" />
<hkern u1="q" u2="&#x3f;" k="18" />
<hkern u1="q" u2="&#x29;" k="16" />
<hkern u1="r" u2="&#xf0;" k="70" />
<hkern u1="r" u2="&#x7d;" k="46" />
<hkern u1="r" u2="]" k="53" />
<hkern u1="r" u2="&#x2f;" k="85" />
<hkern u1="r" u2="&#x2c;" k="83" />
<hkern u1="r" u2="&#x29;" k="42" />
<hkern u1="r" u2="&#x20;" k="43" />
<hkern u1="s" u2="&#x2122;" k="39" />
<hkern u1="s" u2="&#x7d;" k="52" />
<hkern u1="s" u2="]" k="61" />
<hkern u1="s" u2="\" k="68" />
<hkern u1="s" u2="&#x3f;" k="24" />
<hkern u1="s" u2="&#x2a;" k="16" />
<hkern u1="s" u2="&#x29;" k="45" />
<hkern u1="t" u2="&#xf0;" k="9" />
<hkern u1="t" u2="\" k="18" />
<hkern u1="u" u2="&#x2122;" k="30" />
<hkern u1="u" u2="&#x7d;" k="47" />
<hkern u1="u" u2="]" k="55" />
<hkern u1="u" u2="\" k="56" />
<hkern u1="u" u2="&#x3f;" k="18" />
<hkern u1="u" u2="&#x29;" k="16" />
<hkern u1="v" u2="&#xf0;" k="45" />
<hkern u1="v" u2="&#x7d;" k="52" />
<hkern u1="v" u2="]" k="59" />
<hkern u1="v" u2="&#x2f;" k="78" />
<hkern u1="v" u2="&#x2c;" k="74" />
<hkern u1="v" u2="&#x29;" k="47" />
<hkern u1="v" u2="&#x20;" k="49" />
<hkern u1="w" u2="&#xf0;" k="26" />
<hkern u1="w" u2="&#x7d;" k="42" />
<hkern u1="w" u2="]" k="51" />
<hkern u1="w" u2="&#x2f;" k="55" />
<hkern u1="w" u2="&#x2c;" k="48" />
<hkern u1="w" u2="&#x29;" k="40" />
<hkern u1="w" u2="&#x20;" k="42" />
<hkern u1="x" u2="&#xf0;" k="53" />
<hkern u1="x" u2="&#x7d;" k="22" />
<hkern u1="x" u2="]" k="44" />
<hkern u1="x" u2="\" k="19" />
<hkern u1="y" u2="&#xf0;" k="45" />
<hkern u1="y" u2="&#x7d;" k="49" />
<hkern u1="y" u2="]" k="56" />
<hkern u1="y" u2="&#x2f;" k="77" />
<hkern u1="y" u2="&#x2c;" k="77" />
<hkern u1="y" u2="&#x29;" k="44" />
<hkern u1="y" u2="&#x20;" k="50" />
<hkern u1="z" u2="&#xf0;" k="29" />
<hkern u1="z" u2="&#x7d;" k="41" />
<hkern u1="z" u2="]" k="46" />
<hkern u1="z" u2="\" k="27" />
<hkern u1="&#x7b;" g2="uniFB04" k="42" />
<hkern u1="&#x7b;" g2="uniFB03" k="42" />
<hkern u1="&#x7b;" g2="uniFB02" k="42" />
<hkern u1="&#x7b;" g2="uniFB01" k="42" />
<hkern u1="&#x7b;" u2="&#x178;" k="-14" />
<hkern u1="&#x7b;" u2="&#x153;" k="62" />
<hkern u1="&#x7b;" u2="&#x152;" k="44" />
<hkern u1="&#x7b;" u2="&#xff;" k="40" />
<hkern u1="&#x7b;" u2="&#xfd;" k="40" />
<hkern u1="&#x7b;" u2="&#xfc;" k="51" />
<hkern u1="&#x7b;" u2="&#xfb;" k="51" />
<hkern u1="&#x7b;" u2="&#xfa;" k="51" />
<hkern u1="&#x7b;" u2="&#xf9;" k="51" />
<hkern u1="&#x7b;" u2="&#xf8;" k="62" />
<hkern u1="&#x7b;" u2="&#xf6;" k="62" />
<hkern u1="&#x7b;" u2="&#xf5;" k="62" />
<hkern u1="&#x7b;" u2="&#xf4;" k="62" />
<hkern u1="&#x7b;" u2="&#xf3;" k="62" />
<hkern u1="&#x7b;" u2="&#xf2;" k="62" />
<hkern u1="&#x7b;" u2="&#xf1;" k="46" />
<hkern u1="&#x7b;" u2="&#xf0;" k="55" />
<hkern u1="&#x7b;" u2="&#xef;" k="-27" />
<hkern u1="&#x7b;" u2="&#xec;" k="-55" />
<hkern u1="&#x7b;" u2="&#xeb;" k="62" />
<hkern u1="&#x7b;" u2="&#xea;" k="62" />
<hkern u1="&#x7b;" u2="&#xe9;" k="62" />
<hkern u1="&#x7b;" u2="&#xe8;" k="62" />
<hkern u1="&#x7b;" u2="&#xe7;" k="62" />
<hkern u1="&#x7b;" u2="&#xe6;" k="54" />
<hkern u1="&#x7b;" u2="&#xe5;" k="54" />
<hkern u1="&#x7b;" u2="&#xe4;" k="54" />
<hkern u1="&#x7b;" u2="&#xe3;" k="54" />
<hkern u1="&#x7b;" u2="&#xe2;" k="54" />
<hkern u1="&#x7b;" u2="&#xe1;" k="54" />
<hkern u1="&#x7b;" u2="&#xe0;" k="54" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-14" />
<hkern u1="&#x7b;" u2="&#xd8;" k="44" />
<hkern u1="&#x7b;" u2="&#xd6;" k="44" />
<hkern u1="&#x7b;" u2="&#xd5;" k="44" />
<hkern u1="&#x7b;" u2="&#xd4;" k="44" />
<hkern u1="&#x7b;" u2="&#xd3;" k="44" />
<hkern u1="&#x7b;" u2="&#xd2;" k="44" />
<hkern u1="&#x7b;" u2="&#xcf;" k="-30" />
<hkern u1="&#x7b;" u2="&#xce;" k="-51" />
<hkern u1="&#x7b;" u2="&#xc7;" k="44" />
<hkern u1="&#x7b;" u2="&#xc6;" k="49" />
<hkern u1="&#x7b;" u2="&#xc5;" k="22" />
<hkern u1="&#x7b;" u2="&#xc4;" k="22" />
<hkern u1="&#x7b;" u2="&#xc3;" k="22" />
<hkern u1="&#x7b;" u2="&#xc2;" k="22" />
<hkern u1="&#x7b;" u2="&#xc1;" k="22" />
<hkern u1="&#x7b;" u2="&#xc0;" k="22" />
<hkern u1="&#x7b;" u2="z" k="42" />
<hkern u1="&#x7b;" u2="y" k="40" />
<hkern u1="&#x7b;" u2="x" k="22" />
<hkern u1="&#x7b;" u2="w" k="42" />
<hkern u1="&#x7b;" u2="v" k="52" />
<hkern u1="&#x7b;" u2="u" k="51" />
<hkern u1="&#x7b;" u2="t" k="45" />
<hkern u1="&#x7b;" u2="s" k="56" />
<hkern u1="&#x7b;" u2="r" k="46" />
<hkern u1="&#x7b;" u2="q" k="60" />
<hkern u1="&#x7b;" u2="p" k="46" />
<hkern u1="&#x7b;" u2="o" k="62" />
<hkern u1="&#x7b;" u2="n" k="46" />
<hkern u1="&#x7b;" u2="m" k="46" />
<hkern u1="&#x7b;" u2="j" k="-39" />
<hkern u1="&#x7b;" u2="f" k="42" />
<hkern u1="&#x7b;" u2="e" k="62" />
<hkern u1="&#x7b;" u2="d" k="60" />
<hkern u1="&#x7b;" u2="c" k="62" />
<hkern u1="&#x7b;" u2="a" k="54" />
<hkern u1="&#x7b;" u2="Y" k="-14" />
<hkern u1="&#x7b;" u2="V" k="-14" />
<hkern u1="&#x7b;" u2="S" k="19" />
<hkern u1="&#x7b;" u2="Q" k="44" />
<hkern u1="&#x7b;" u2="O" k="44" />
<hkern u1="&#x7b;" u2="J" k="15" />
<hkern u1="&#x7b;" u2="G" k="44" />
<hkern u1="&#x7b;" u2="C" k="44" />
<hkern u1="&#x7b;" u2="A" k="22" />
<hkern u1="&#x7c;" u2="&#xee;" k="-8" />
<hkern u1="&#xa1;" u2="&#x178;" k="90" />
<hkern u1="&#xa1;" u2="&#xdd;" k="90" />
<hkern u1="&#xa1;" u2="Y" k="90" />
<hkern u1="&#xa1;" u2="W" k="26" />
<hkern u1="&#xa1;" u2="V" k="54" />
<hkern u1="&#xa1;" u2="T" k="135" />
<hkern u1="&#xb7;" u2="l" k="67" />
<hkern u1="&#xbf;" g2="uniFB04" k="20" />
<hkern u1="&#xbf;" g2="uniFB03" k="20" />
<hkern u1="&#xbf;" g2="uniFB02" k="20" />
<hkern u1="&#xbf;" g2="uniFB01" k="20" />
<hkern u1="&#xbf;" u2="&#x178;" k="147" />
<hkern u1="&#xbf;" u2="&#x153;" k="67" />
<hkern u1="&#xbf;" u2="&#x152;" k="55" />
<hkern u1="&#xbf;" u2="&#xff;" k="20" />
<hkern u1="&#xbf;" u2="&#xfe;" k="26" />
<hkern u1="&#xbf;" u2="&#xfd;" k="20" />
<hkern u1="&#xbf;" u2="&#xfc;" k="52" />
<hkern u1="&#xbf;" u2="&#xfb;" k="52" />
<hkern u1="&#xbf;" u2="&#xfa;" k="52" />
<hkern u1="&#xbf;" u2="&#xf9;" k="52" />
<hkern u1="&#xbf;" u2="&#xf8;" k="67" />
<hkern u1="&#xbf;" u2="&#xf6;" k="67" />
<hkern u1="&#xbf;" u2="&#xf5;" k="67" />
<hkern u1="&#xbf;" u2="&#xf4;" k="67" />
<hkern u1="&#xbf;" u2="&#xf3;" k="67" />
<hkern u1="&#xbf;" u2="&#xf2;" k="67" />
<hkern u1="&#xbf;" u2="&#xf1;" k="27" />
<hkern u1="&#xbf;" u2="&#xf0;" k="70" />
<hkern u1="&#xbf;" u2="&#xef;" k="27" />
<hkern u1="&#xbf;" u2="&#xee;" k="27" />
<hkern u1="&#xbf;" u2="&#xed;" k="27" />
<hkern u1="&#xbf;" u2="&#xec;" k="27" />
<hkern u1="&#xbf;" u2="&#xeb;" k="67" />
<hkern u1="&#xbf;" u2="&#xea;" k="67" />
<hkern u1="&#xbf;" u2="&#xe9;" k="67" />
<hkern u1="&#xbf;" u2="&#xe8;" k="67" />
<hkern u1="&#xbf;" u2="&#xe7;" k="67" />
<hkern u1="&#xbf;" u2="&#xe6;" k="52" />
<hkern u1="&#xbf;" u2="&#xe5;" k="52" />
<hkern u1="&#xbf;" u2="&#xe4;" k="52" />
<hkern u1="&#xbf;" u2="&#xe3;" k="52" />
<hkern u1="&#xbf;" u2="&#xe2;" k="52" />
<hkern u1="&#xbf;" u2="&#xe1;" k="52" />
<hkern u1="&#xbf;" u2="&#xe0;" k="52" />
<hkern u1="&#xbf;" u2="&#xdf;" k="26" />
<hkern u1="&#xbf;" u2="&#xde;" k="23" />
<hkern u1="&#xbf;" u2="&#xdd;" k="147" />
<hkern u1="&#xbf;" u2="&#xdc;" k="57" />
<hkern u1="&#xbf;" u2="&#xdb;" k="57" />
<hkern u1="&#xbf;" u2="&#xda;" k="57" />
<hkern u1="&#xbf;" u2="&#xd9;" k="57" />
<hkern u1="&#xbf;" u2="&#xd8;" k="55" />
<hkern u1="&#xbf;" u2="&#xd6;" k="55" />
<hkern u1="&#xbf;" u2="&#xd5;" k="55" />
<hkern u1="&#xbf;" u2="&#xd4;" k="55" />
<hkern u1="&#xbf;" u2="&#xd3;" k="55" />
<hkern u1="&#xbf;" u2="&#xd2;" k="55" />
<hkern u1="&#xbf;" u2="&#xd1;" k="23" />
<hkern u1="&#xbf;" u2="&#xd0;" k="45" />
<hkern u1="&#xbf;" u2="&#xcf;" k="23" />
<hkern u1="&#xbf;" u2="&#xce;" k="23" />
<hkern u1="&#xbf;" u2="&#xcd;" k="23" />
<hkern u1="&#xbf;" u2="&#xcc;" k="23" />
<hkern u1="&#xbf;" u2="&#xcb;" k="23" />
<hkern u1="&#xbf;" u2="&#xca;" k="23" />
<hkern u1="&#xbf;" u2="&#xc9;" k="23" />
<hkern u1="&#xbf;" u2="&#xc8;" k="23" />
<hkern u1="&#xbf;" u2="&#xc7;" k="55" />
<hkern u1="&#xbf;" u2="&#xc6;" k="38" />
<hkern u1="&#xbf;" u2="&#xc5;" k="35" />
<hkern u1="&#xbf;" u2="&#xc4;" k="35" />
<hkern u1="&#xbf;" u2="&#xc3;" k="35" />
<hkern u1="&#xbf;" u2="&#xc2;" k="35" />
<hkern u1="&#xbf;" u2="&#xc1;" k="35" />
<hkern u1="&#xbf;" u2="&#xc0;" k="35" />
<hkern u1="&#xbf;" u2="z" k="28" />
<hkern u1="&#xbf;" u2="y" k="20" />
<hkern u1="&#xbf;" u2="x" k="25" />
<hkern u1="&#xbf;" u2="w" k="41" />
<hkern u1="&#xbf;" u2="v" k="48" />
<hkern u1="&#xbf;" u2="u" k="52" />
<hkern u1="&#xbf;" u2="t" k="41" />
<hkern u1="&#xbf;" u2="s" k="54" />
<hkern u1="&#xbf;" u2="r" k="27" />
<hkern u1="&#xbf;" u2="q" k="63" />
<hkern u1="&#xbf;" u2="p" k="27" />
<hkern u1="&#xbf;" u2="o" k="67" />
<hkern u1="&#xbf;" u2="n" k="27" />
<hkern u1="&#xbf;" u2="m" k="27" />
<hkern u1="&#xbf;" u2="l" k="26" />
<hkern u1="&#xbf;" u2="k" k="26" />
<hkern u1="&#xbf;" u2="j" k="-6" />
<hkern u1="&#xbf;" u2="i" k="27" />
<hkern u1="&#xbf;" u2="h" k="26" />
<hkern u1="&#xbf;" u2="f" k="20" />
<hkern u1="&#xbf;" u2="e" k="67" />
<hkern u1="&#xbf;" u2="d" k="63" />
<hkern u1="&#xbf;" u2="c" k="67" />
<hkern u1="&#xbf;" u2="b" k="26" />
<hkern u1="&#xbf;" u2="a" k="52" />
<hkern u1="&#xbf;" u2="Z" k="31" />
<hkern u1="&#xbf;" u2="Y" k="147" />
<hkern u1="&#xbf;" u2="X" k="34" />
<hkern u1="&#xbf;" u2="W" k="93" />
<hkern u1="&#xbf;" u2="V" k="116" />
<hkern u1="&#xbf;" u2="U" k="57" />
<hkern u1="&#xbf;" u2="T" k="177" />
<hkern u1="&#xbf;" u2="S" k="25" />
<hkern u1="&#xbf;" u2="R" k="23" />
<hkern u1="&#xbf;" u2="Q" k="55" />
<hkern u1="&#xbf;" u2="P" k="23" />
<hkern u1="&#xbf;" u2="O" k="55" />
<hkern u1="&#xbf;" u2="N" k="23" />
<hkern u1="&#xbf;" u2="M" k="23" />
<hkern u1="&#xbf;" u2="L" k="23" />
<hkern u1="&#xbf;" u2="K" k="23" />
<hkern u1="&#xbf;" u2="J" k="44" />
<hkern u1="&#xbf;" u2="I" k="23" />
<hkern u1="&#xbf;" u2="H" k="23" />
<hkern u1="&#xbf;" u2="G" k="55" />
<hkern u1="&#xbf;" u2="F" k="23" />
<hkern u1="&#xbf;" u2="E" k="23" />
<hkern u1="&#xbf;" u2="D" k="23" />
<hkern u1="&#xbf;" u2="C" k="55" />
<hkern u1="&#xbf;" u2="B" k="23" />
<hkern u1="&#xbf;" u2="A" k="35" />
<hkern u1="&#xc0;" u2="&#x2122;" k="128" />
<hkern u1="&#xc0;" u2="&#xf0;" k="25" />
<hkern u1="&#xc0;" u2="&#xba;" k="113" />
<hkern u1="&#xc0;" u2="&#xae;" k="18" />
<hkern u1="&#xc0;" u2="&#xaa;" k="100" />
<hkern u1="&#xc0;" u2="&#xa9;" k="18" />
<hkern u1="&#xc0;" u2="&#x7d;" k="18" />
<hkern u1="&#xc0;" u2="]" k="37" />
<hkern u1="&#xc0;" u2="\" k="116" />
<hkern u1="&#xc0;" u2="&#x2a;" k="116" />
<hkern u1="&#xc0;" u2="&#x20;" k="69" />
<hkern u1="&#xc1;" u2="&#x2122;" k="128" />
<hkern u1="&#xc1;" u2="&#xf0;" k="25" />
<hkern u1="&#xc1;" u2="&#xba;" k="113" />
<hkern u1="&#xc1;" u2="&#xae;" k="18" />
<hkern u1="&#xc1;" u2="&#xaa;" k="100" />
<hkern u1="&#xc1;" u2="&#xa9;" k="18" />
<hkern u1="&#xc1;" u2="&#x7d;" k="18" />
<hkern u1="&#xc1;" u2="]" k="37" />
<hkern u1="&#xc1;" u2="\" k="116" />
<hkern u1="&#xc1;" u2="&#x2a;" k="116" />
<hkern u1="&#xc1;" u2="&#x20;" k="69" />
<hkern u1="&#xc2;" u2="&#x2122;" k="128" />
<hkern u1="&#xc2;" u2="&#xf0;" k="25" />
<hkern u1="&#xc2;" u2="&#xba;" k="113" />
<hkern u1="&#xc2;" u2="&#xae;" k="18" />
<hkern u1="&#xc2;" u2="&#xaa;" k="100" />
<hkern u1="&#xc2;" u2="&#xa9;" k="18" />
<hkern u1="&#xc2;" u2="&#x7d;" k="18" />
<hkern u1="&#xc2;" u2="]" k="37" />
<hkern u1="&#xc2;" u2="\" k="116" />
<hkern u1="&#xc2;" u2="&#x2a;" k="116" />
<hkern u1="&#xc2;" u2="&#x20;" k="69" />
<hkern u1="&#xc3;" u2="&#x2122;" k="128" />
<hkern u1="&#xc3;" u2="&#xf0;" k="25" />
<hkern u1="&#xc3;" u2="&#xba;" k="113" />
<hkern u1="&#xc3;" u2="&#xae;" k="18" />
<hkern u1="&#xc3;" u2="&#xaa;" k="100" />
<hkern u1="&#xc3;" u2="&#xa9;" k="18" />
<hkern u1="&#xc3;" u2="&#x7d;" k="18" />
<hkern u1="&#xc3;" u2="]" k="37" />
<hkern u1="&#xc3;" u2="\" k="116" />
<hkern u1="&#xc3;" u2="&#x2a;" k="116" />
<hkern u1="&#xc3;" u2="&#x20;" k="69" />
<hkern u1="&#xc4;" u2="&#x2122;" k="128" />
<hkern u1="&#xc4;" u2="&#xf0;" k="25" />
<hkern u1="&#xc4;" u2="&#xba;" k="113" />
<hkern u1="&#xc4;" u2="&#xae;" k="18" />
<hkern u1="&#xc4;" u2="&#xaa;" k="100" />
<hkern u1="&#xc4;" u2="&#xa9;" k="18" />
<hkern u1="&#xc4;" u2="&#x7d;" k="18" />
<hkern u1="&#xc4;" u2="]" k="37" />
<hkern u1="&#xc4;" u2="\" k="116" />
<hkern u1="&#xc4;" u2="&#x2a;" k="116" />
<hkern u1="&#xc4;" u2="&#x20;" k="69" />
<hkern u1="&#xc5;" u2="&#x2122;" k="128" />
<hkern u1="&#xc5;" u2="&#xf0;" k="25" />
<hkern u1="&#xc5;" u2="&#xba;" k="113" />
<hkern u1="&#xc5;" u2="&#xae;" k="18" />
<hkern u1="&#xc5;" u2="&#xaa;" k="100" />
<hkern u1="&#xc5;" u2="&#xa9;" k="18" />
<hkern u1="&#xc5;" u2="&#x7d;" k="18" />
<hkern u1="&#xc5;" u2="]" k="37" />
<hkern u1="&#xc5;" u2="\" k="116" />
<hkern u1="&#xc5;" u2="&#x2a;" k="116" />
<hkern u1="&#xc5;" u2="&#x20;" k="69" />
<hkern u1="&#xc6;" u2="&#xf0;" k="48" />
<hkern u1="&#xc6;" u2="&#xef;" k="-15" />
<hkern u1="&#xc6;" u2="&#xee;" k="-28" />
<hkern u1="&#xc6;" u2="&#xed;" k="11" />
<hkern u1="&#xc6;" u2="&#xec;" k="-22" />
<hkern u1="&#xc6;" u2="&#xdf;" k="27" />
<hkern u1="&#xc7;" u2="&#x2122;" k="-11" />
<hkern u1="&#xc7;" u2="&#xf0;" k="18" />
<hkern u1="&#xc7;" u2="&#xef;" k="-44" />
<hkern u1="&#xc7;" u2="&#xee;" k="-23" />
<hkern u1="&#xc7;" u2="&#x2a;" k="-8" />
<hkern u1="&#xc8;" u2="&#xf0;" k="48" />
<hkern u1="&#xc8;" u2="&#xef;" k="-15" />
<hkern u1="&#xc8;" u2="&#xee;" k="-28" />
<hkern u1="&#xc8;" u2="&#xed;" k="11" />
<hkern u1="&#xc8;" u2="&#xec;" k="-22" />
<hkern u1="&#xc8;" u2="&#xdf;" k="27" />
<hkern u1="&#xc9;" u2="&#xf0;" k="48" />
<hkern u1="&#xc9;" u2="&#xef;" k="-15" />
<hkern u1="&#xc9;" u2="&#xee;" k="-28" />
<hkern u1="&#xc9;" u2="&#xed;" k="11" />
<hkern u1="&#xc9;" u2="&#xec;" k="-22" />
<hkern u1="&#xc9;" u2="&#xdf;" k="27" />
<hkern u1="&#xca;" u2="&#xf0;" k="48" />
<hkern u1="&#xca;" u2="&#xef;" k="-15" />
<hkern u1="&#xca;" u2="&#xee;" k="-28" />
<hkern u1="&#xca;" u2="&#xed;" k="11" />
<hkern u1="&#xca;" u2="&#xec;" k="-22" />
<hkern u1="&#xca;" u2="&#xdf;" k="27" />
<hkern u1="&#xcb;" u2="&#xf0;" k="48" />
<hkern u1="&#xcb;" u2="&#xef;" k="-15" />
<hkern u1="&#xcb;" u2="&#xee;" k="-28" />
<hkern u1="&#xcb;" u2="&#xed;" k="11" />
<hkern u1="&#xcb;" u2="&#xec;" k="-22" />
<hkern u1="&#xcb;" u2="&#xdf;" k="27" />
<hkern u1="&#xcc;" u2="&#xf0;" k="35" />
<hkern u1="&#xcc;" u2="&#xee;" k="8" />
<hkern u1="&#xcd;" u2="&#xf0;" k="35" />
<hkern u1="&#xcd;" u2="&#xee;" k="8" />
<hkern u1="&#xce;" u2="&#xf0;" k="35" />
<hkern u1="&#xce;" u2="&#xee;" k="8" />
<hkern u1="&#xce;" u2="&#x7d;" k="-60" />
<hkern u1="&#xce;" u2="]" k="-65" />
<hkern u1="&#xce;" u2="&#x29;" k="-15" />
<hkern u1="&#xcf;" u2="&#xf0;" k="35" />
<hkern u1="&#xcf;" u2="&#xee;" k="8" />
<hkern u1="&#xcf;" u2="&#x7d;" k="-34" />
<hkern u1="&#xcf;" u2="]" k="-37" />
<hkern u1="&#xd0;" u2="&#xf0;" k="18" />
<hkern u1="&#xd0;" u2="&#xd0;" k="10" />
<hkern u1="&#xd0;" u2="&#x7d;" k="44" />
<hkern u1="&#xd0;" u2="]" k="52" />
<hkern u1="&#xd0;" u2="&#x2f;" k="24" />
<hkern u1="&#xd0;" u2="&#x2c;" k="54" />
<hkern u1="&#xd0;" u2="&#x29;" k="41" />
<hkern u1="&#xd1;" u2="&#xf0;" k="35" />
<hkern u1="&#xd1;" u2="&#xee;" k="8" />
<hkern u1="&#xd2;" u2="&#xf0;" k="17" />
<hkern u1="&#xd2;" u2="&#xd0;" k="10" />
<hkern u1="&#xd2;" u2="&#x7d;" k="42" />
<hkern u1="&#xd2;" u2="]" k="51" />
<hkern u1="&#xd2;" u2="&#x2f;" k="23" />
<hkern u1="&#xd2;" u2="&#x2c;" k="54" />
<hkern u1="&#xd2;" u2="&#x29;" k="40" />
<hkern u1="&#xd3;" u2="&#xf0;" k="17" />
<hkern u1="&#xd3;" u2="&#xd0;" k="10" />
<hkern u1="&#xd3;" u2="&#x7d;" k="42" />
<hkern u1="&#xd3;" u2="]" k="51" />
<hkern u1="&#xd3;" u2="&#x2f;" k="23" />
<hkern u1="&#xd3;" u2="&#x2c;" k="54" />
<hkern u1="&#xd3;" u2="&#x29;" k="40" />
<hkern u1="&#xd4;" u2="&#xf0;" k="17" />
<hkern u1="&#xd4;" u2="&#xd0;" k="10" />
<hkern u1="&#xd4;" u2="&#x7d;" k="42" />
<hkern u1="&#xd4;" u2="]" k="51" />
<hkern u1="&#xd4;" u2="&#x2f;" k="23" />
<hkern u1="&#xd4;" u2="&#x2c;" k="54" />
<hkern u1="&#xd4;" u2="&#x29;" k="40" />
<hkern u1="&#xd5;" u2="&#xf0;" k="17" />
<hkern u1="&#xd5;" u2="&#xd0;" k="10" />
<hkern u1="&#xd5;" u2="&#x7d;" k="42" />
<hkern u1="&#xd5;" u2="]" k="51" />
<hkern u1="&#xd5;" u2="&#x2f;" k="23" />
<hkern u1="&#xd5;" u2="&#x2c;" k="54" />
<hkern u1="&#xd5;" u2="&#x29;" k="40" />
<hkern u1="&#xd6;" u2="&#xf0;" k="17" />
<hkern u1="&#xd6;" u2="&#xd0;" k="10" />
<hkern u1="&#xd6;" u2="&#x7d;" k="42" />
<hkern u1="&#xd6;" u2="]" k="51" />
<hkern u1="&#xd6;" u2="&#x2f;" k="23" />
<hkern u1="&#xd6;" u2="&#x2c;" k="54" />
<hkern u1="&#xd6;" u2="&#x29;" k="40" />
<hkern u1="&#xd8;" u2="&#xf0;" k="17" />
<hkern u1="&#xd8;" u2="&#xd0;" k="10" />
<hkern u1="&#xd8;" u2="&#x7d;" k="42" />
<hkern u1="&#xd8;" u2="]" k="51" />
<hkern u1="&#xd8;" u2="&#x2f;" k="23" />
<hkern u1="&#xd8;" u2="&#x2c;" k="54" />
<hkern u1="&#xd8;" u2="&#x29;" k="40" />
<hkern u1="&#xd9;" u2="&#xf0;" k="46" />
<hkern u1="&#xd9;" u2="&#xee;" k="3" />
<hkern u1="&#xd9;" u2="&#xed;" k="31" />
<hkern u1="&#xd9;" u2="&#xdf;" k="31" />
<hkern u1="&#xd9;" u2="&#x2f;" k="25" />
<hkern u1="&#xd9;" u2="&#x2c;" k="47" />
<hkern u1="&#xda;" u2="&#xf0;" k="46" />
<hkern u1="&#xda;" u2="&#xee;" k="3" />
<hkern u1="&#xda;" u2="&#xed;" k="31" />
<hkern u1="&#xda;" u2="&#xdf;" k="31" />
<hkern u1="&#xda;" u2="&#x2f;" k="25" />
<hkern u1="&#xda;" u2="&#x2c;" k="47" />
<hkern u1="&#xdb;" u2="&#xf0;" k="46" />
<hkern u1="&#xdb;" u2="&#xee;" k="3" />
<hkern u1="&#xdb;" u2="&#xed;" k="31" />
<hkern u1="&#xdb;" u2="&#xdf;" k="31" />
<hkern u1="&#xdb;" u2="&#x2f;" k="25" />
<hkern u1="&#xdb;" u2="&#x2c;" k="47" />
<hkern u1="&#xdc;" u2="&#xf0;" k="46" />
<hkern u1="&#xdc;" u2="&#xee;" k="3" />
<hkern u1="&#xdc;" u2="&#xed;" k="31" />
<hkern u1="&#xdc;" u2="&#xdf;" k="31" />
<hkern u1="&#xdc;" u2="&#x2f;" k="25" />
<hkern u1="&#xdc;" u2="&#x2c;" k="47" />
<hkern u1="&#xdd;" g2="uniFB02" k="109" />
<hkern u1="&#xdd;" u2="&#x2122;" k="-31" />
<hkern u1="&#xdd;" u2="&#xf9;" k="158" />
<hkern u1="&#xdd;" u2="&#xf6;" k="213" />
<hkern u1="&#xdd;" u2="&#xf2;" k="188" />
<hkern u1="&#xdd;" u2="&#xf0;" k="161" />
<hkern u1="&#xdd;" u2="&#xef;" k="-36" />
<hkern u1="&#xdd;" u2="&#xed;" k="72" />
<hkern u1="&#xdd;" u2="&#xec;" k="-78" />
<hkern u1="&#xdd;" u2="&#xeb;" k="206" />
<hkern u1="&#xdd;" u2="&#xe8;" k="181" />
<hkern u1="&#xdd;" u2="&#xe4;" k="176" />
<hkern u1="&#xdd;" u2="&#xe3;" k="212" />
<hkern u1="&#xdd;" u2="&#xe0;" k="152" />
<hkern u1="&#xdd;" u2="&#xdf;" k="99" />
<hkern u1="&#xdd;" u2="&#xae;" k="61" />
<hkern u1="&#xdd;" u2="&#xa9;" k="62" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-14" />
<hkern u1="&#xdd;" u2="]" k="-17" />
<hkern u1="&#xdd;" u2="\" k="-9" />
<hkern u1="&#xdd;" u2="&#x40;" k="95" />
<hkern u1="&#xdd;" u2="&#x3b;" k="75" />
<hkern u1="&#xdd;" u2="&#x3a;" k="77" />
<hkern u1="&#xdd;" u2="&#x2f;" k="151" />
<hkern u1="&#xdd;" u2="&#x2c;" k="166" />
<hkern u1="&#xdd;" u2="&#x29;" k="-13" />
<hkern u1="&#xdd;" u2="&#x26;" k="82" />
<hkern u1="&#xdd;" u2="&#x20;" k="81" />
<hkern u1="&#xde;" u2="&#x2122;" k="15" />
<hkern u1="&#xde;" u2="&#x2026;" k="73" />
<hkern u1="&#xde;" u2="&#x201e;" k="60" />
<hkern u1="&#xde;" u2="&#x201a;" k="60" />
<hkern u1="&#xde;" u2="&#x178;" k="88" />
<hkern u1="&#xde;" u2="&#xfe;" k="6" />
<hkern u1="&#xde;" u2="&#xfc;" k="5" />
<hkern u1="&#xde;" u2="&#xfb;" k="5" />
<hkern u1="&#xde;" u2="&#xfa;" k="5" />
<hkern u1="&#xde;" u2="&#xf9;" k="5" />
<hkern u1="&#xde;" u2="&#xf1;" k="6" />
<hkern u1="&#xde;" u2="&#xef;" k="6" />
<hkern u1="&#xde;" u2="&#xee;" k="6" />
<hkern u1="&#xde;" u2="&#xed;" k="6" />
<hkern u1="&#xde;" u2="&#xec;" k="6" />
<hkern u1="&#xde;" u2="&#xe6;" k="6" />
<hkern u1="&#xde;" u2="&#xe5;" k="6" />
<hkern u1="&#xde;" u2="&#xe4;" k="6" />
<hkern u1="&#xde;" u2="&#xe3;" k="6" />
<hkern u1="&#xde;" u2="&#xe2;" k="6" />
<hkern u1="&#xde;" u2="&#xe1;" k="6" />
<hkern u1="&#xde;" u2="&#xe0;" k="6" />
<hkern u1="&#xde;" u2="&#xdf;" k="6" />
<hkern u1="&#xde;" u2="&#xdd;" k="88" />
<hkern u1="&#xde;" u2="&#xc5;" k="45" />
<hkern u1="&#xde;" u2="&#xc4;" k="45" />
<hkern u1="&#xde;" u2="&#xc3;" k="45" />
<hkern u1="&#xde;" u2="&#xc2;" k="45" />
<hkern u1="&#xde;" u2="&#xc1;" k="45" />
<hkern u1="&#xde;" u2="&#xc0;" k="45" />
<hkern u1="&#xde;" u2="&#x7d;" k="17" />
<hkern u1="&#xde;" u2="z" k="4" />
<hkern u1="&#xde;" u2="x" k="7" />
<hkern u1="&#xde;" u2="u" k="5" />
<hkern u1="&#xde;" u2="r" k="6" />
<hkern u1="&#xde;" u2="p" k="6" />
<hkern u1="&#xde;" u2="n" k="6" />
<hkern u1="&#xde;" u2="m" k="6" />
<hkern u1="&#xde;" u2="l" k="6" />
<hkern u1="&#xde;" u2="k" k="6" />
<hkern u1="&#xde;" u2="j" k="6" />
<hkern u1="&#xde;" u2="i" k="6" />
<hkern u1="&#xde;" u2="h" k="6" />
<hkern u1="&#xde;" u2="g" k="6" />
<hkern u1="&#xde;" u2="b" k="6" />
<hkern u1="&#xde;" u2="a" k="6" />
<hkern u1="&#xde;" u2="]" k="42" />
<hkern u1="&#xde;" u2="Z" k="47" />
<hkern u1="&#xde;" u2="Y" k="88" />
<hkern u1="&#xde;" u2="X" k="74" />
<hkern u1="&#xde;" u2="W" k="23" />
<hkern u1="&#xde;" u2="V" k="43" />
<hkern u1="&#xde;" u2="T" k="69" />
<hkern u1="&#xde;" u2="S" k="5" />
<hkern u1="&#xde;" u2="J" k="87" />
<hkern u1="&#xde;" u2="A" k="45" />
<hkern u1="&#xde;" u2="&#x3f;" k="17" />
<hkern u1="&#xde;" u2="&#x2f;" k="37" />
<hkern u1="&#xde;" u2="&#x2e;" k="73" />
<hkern u1="&#xde;" u2="&#x2c;" k="62" />
<hkern u1="&#xde;" u2="&#x29;" k="23" />
<hkern u1="&#xdf;" g2="uniFB04" k="22" />
<hkern u1="&#xdf;" g2="uniFB03" k="22" />
<hkern u1="&#xdf;" g2="uniFB02" k="22" />
<hkern u1="&#xdf;" g2="uniFB01" k="22" />
<hkern u1="&#xdf;" u2="&#xff;" k="35" />
<hkern u1="&#xdf;" u2="&#xfd;" k="35" />
<hkern u1="&#xdf;" u2="&#x7d;" k="18" />
<hkern u1="&#xdf;" u2="y" k="35" />
<hkern u1="&#xdf;" u2="x" k="16" />
<hkern u1="&#xdf;" u2="w" k="20" />
<hkern u1="&#xdf;" u2="v" k="32" />
<hkern u1="&#xdf;" u2="t" k="22" />
<hkern u1="&#xdf;" u2="s" k="6" />
<hkern u1="&#xdf;" u2="g" k="8" />
<hkern u1="&#xdf;" u2="f" k="22" />
<hkern u1="&#xdf;" u2="]" k="41" />
<hkern u1="&#xdf;" u2="\" k="20" />
<hkern u1="&#xe0;" u2="&#x2122;" k="48" />
<hkern u1="&#xe0;" u2="&#xba;" k="20" />
<hkern u1="&#xe0;" u2="&#xaa;" k="14" />
<hkern u1="&#xe0;" u2="&#x7d;" k="53" />
<hkern u1="&#xe0;" u2="]" k="60" />
<hkern u1="&#xe0;" u2="\" k="85" />
<hkern u1="&#xe0;" u2="&#x3f;" k="47" />
<hkern u1="&#xe0;" u2="&#x2a;" k="43" />
<hkern u1="&#xe0;" u2="&#x29;" k="36" />
<hkern u1="&#xe1;" u2="&#x2122;" k="48" />
<hkern u1="&#xe1;" u2="&#xba;" k="20" />
<hkern u1="&#xe1;" u2="&#xaa;" k="14" />
<hkern u1="&#xe1;" u2="&#x7d;" k="53" />
<hkern u1="&#xe1;" u2="]" k="60" />
<hkern u1="&#xe1;" u2="\" k="85" />
<hkern u1="&#xe1;" u2="&#x3f;" k="47" />
<hkern u1="&#xe1;" u2="&#x2a;" k="43" />
<hkern u1="&#xe1;" u2="&#x29;" k="36" />
<hkern u1="&#xe2;" u2="&#x2122;" k="48" />
<hkern u1="&#xe2;" u2="&#xba;" k="20" />
<hkern u1="&#xe2;" u2="&#xaa;" k="14" />
<hkern u1="&#xe2;" u2="&#x7d;" k="53" />
<hkern u1="&#xe2;" u2="]" k="60" />
<hkern u1="&#xe2;" u2="\" k="85" />
<hkern u1="&#xe2;" u2="&#x3f;" k="47" />
<hkern u1="&#xe2;" u2="&#x2a;" k="43" />
<hkern u1="&#xe2;" u2="&#x29;" k="36" />
<hkern u1="&#xe3;" u2="&#x2122;" k="48" />
<hkern u1="&#xe3;" u2="&#xba;" k="20" />
<hkern u1="&#xe3;" u2="&#xaa;" k="14" />
<hkern u1="&#xe3;" u2="&#x7d;" k="53" />
<hkern u1="&#xe3;" u2="]" k="60" />
<hkern u1="&#xe3;" u2="\" k="85" />
<hkern u1="&#xe3;" u2="&#x3f;" k="47" />
<hkern u1="&#xe3;" u2="&#x2a;" k="43" />
<hkern u1="&#xe3;" u2="&#x29;" k="36" />
<hkern u1="&#xe4;" u2="&#x2122;" k="48" />
<hkern u1="&#xe4;" u2="&#xba;" k="20" />
<hkern u1="&#xe4;" u2="&#xaa;" k="14" />
<hkern u1="&#xe4;" u2="&#x7d;" k="53" />
<hkern u1="&#xe4;" u2="]" k="60" />
<hkern u1="&#xe4;" u2="\" k="85" />
<hkern u1="&#xe4;" u2="&#x3f;" k="47" />
<hkern u1="&#xe4;" u2="&#x2a;" k="43" />
<hkern u1="&#xe4;" u2="&#x29;" k="36" />
<hkern u1="&#xe5;" u2="&#x2122;" k="48" />
<hkern u1="&#xe5;" u2="&#xba;" k="20" />
<hkern u1="&#xe5;" u2="&#xaa;" k="14" />
<hkern u1="&#xe5;" u2="&#x7d;" k="53" />
<hkern u1="&#xe5;" u2="]" k="60" />
<hkern u1="&#xe5;" u2="\" k="85" />
<hkern u1="&#xe5;" u2="&#x3f;" k="47" />
<hkern u1="&#xe5;" u2="&#x2a;" k="43" />
<hkern u1="&#xe5;" u2="&#x29;" k="36" />
<hkern u1="&#xe6;" u2="&#x2122;" k="51" />
<hkern u1="&#xe6;" u2="&#xba;" k="33" />
<hkern u1="&#xe6;" u2="&#x7d;" k="58" />
<hkern u1="&#xe6;" u2="]" k="65" />
<hkern u1="&#xe6;" u2="\" k="83" />
<hkern u1="&#xe6;" u2="&#x3f;" k="51" />
<hkern u1="&#xe6;" u2="&#x2a;" k="49" />
<hkern u1="&#xe6;" u2="&#x29;" k="47" />
<hkern u1="&#xe7;" u2="&#x2122;" k="35" />
<hkern u1="&#xe7;" u2="&#xf0;" k="24" />
<hkern u1="&#xe7;" u2="&#x7d;" k="52" />
<hkern u1="&#xe7;" u2="]" k="59" />
<hkern u1="&#xe7;" u2="\" k="61" />
<hkern u1="&#xe7;" u2="&#x3f;" k="19" />
<hkern u1="&#xe7;" u2="&#x2a;" k="13" />
<hkern u1="&#xe7;" u2="&#x29;" k="35" />
<hkern u1="&#xe8;" u2="&#x2122;" k="51" />
<hkern u1="&#xe8;" u2="&#xba;" k="33" />
<hkern u1="&#xe8;" u2="&#x7d;" k="58" />
<hkern u1="&#xe8;" u2="]" k="65" />
<hkern u1="&#xe8;" u2="\" k="83" />
<hkern u1="&#xe8;" u2="&#x3f;" k="51" />
<hkern u1="&#xe8;" u2="&#x2a;" k="49" />
<hkern u1="&#xe8;" u2="&#x29;" k="47" />
<hkern u1="&#xe9;" u2="&#x2122;" k="51" />
<hkern u1="&#xe9;" u2="&#xba;" k="33" />
<hkern u1="&#xe9;" u2="&#x7d;" k="58" />
<hkern u1="&#xe9;" u2="]" k="65" />
<hkern u1="&#xe9;" u2="\" k="83" />
<hkern u1="&#xe9;" u2="&#x3f;" k="51" />
<hkern u1="&#xe9;" u2="&#x2a;" k="49" />
<hkern u1="&#xe9;" u2="&#x29;" k="47" />
<hkern u1="&#xea;" u2="&#x2122;" k="51" />
<hkern u1="&#xea;" u2="&#xba;" k="33" />
<hkern u1="&#xea;" u2="&#x7d;" k="58" />
<hkern u1="&#xea;" u2="]" k="65" />
<hkern u1="&#xea;" u2="\" k="83" />
<hkern u1="&#xea;" u2="&#x3f;" k="51" />
<hkern u1="&#xea;" u2="&#x2a;" k="49" />
<hkern u1="&#xea;" u2="&#x29;" k="47" />
<hkern u1="&#xeb;" u2="&#x2122;" k="51" />
<hkern u1="&#xeb;" u2="&#xba;" k="33" />
<hkern u1="&#xeb;" u2="&#x7d;" k="58" />
<hkern u1="&#xeb;" u2="]" k="65" />
<hkern u1="&#xeb;" u2="\" k="83" />
<hkern u1="&#xeb;" u2="&#x3f;" k="51" />
<hkern u1="&#xeb;" u2="&#x2a;" k="49" />
<hkern u1="&#xeb;" u2="&#x29;" k="47" />
<hkern u1="&#xec;" u2="&#xef;" k="-12" />
<hkern u1="&#xed;" u2="&#x2122;" k="-71" />
<hkern u1="&#xed;" u2="&#x201d;" k="-49" />
<hkern u1="&#xed;" u2="&#x201c;" k="-25" />
<hkern u1="&#xed;" u2="&#x2019;" k="-49" />
<hkern u1="&#xed;" u2="&#x2018;" k="-25" />
<hkern u1="&#xed;" u2="&#xfe;" k="-36" />
<hkern u1="&#xed;" u2="&#xef;" k="-12" />
<hkern u1="&#xed;" u2="&#xdf;" k="-36" />
<hkern u1="&#xed;" u2="&#xaa;" k="-13" />
<hkern u1="&#xed;" u2="&#x7d;" k="-81" />
<hkern u1="&#xed;" u2="&#x7c;" k="-25" />
<hkern u1="&#xed;" u2="l" k="-36" />
<hkern u1="&#xed;" u2="k" k="-36" />
<hkern u1="&#xed;" u2="h" k="-36" />
<hkern u1="&#xed;" u2="b" k="-36" />
<hkern u1="&#xed;" u2="]" k="-84" />
<hkern u1="&#xed;" u2="\" k="-76" />
<hkern u1="&#xed;" u2="&#x2a;" k="-8" />
<hkern u1="&#xed;" u2="&#x29;" k="-77" />
<hkern u1="&#xed;" u2="&#x27;" k="-53" />
<hkern u1="&#xed;" u2="&#x22;" k="-53" />
<hkern u1="&#xed;" u2="&#x21;" k="-24" />
<hkern u1="&#xee;" u2="&#x2122;" k="-27" />
<hkern u1="&#xee;" u2="&#x201d;" k="-18" />
<hkern u1="&#xee;" u2="&#x201c;" k="-66" />
<hkern u1="&#xee;" u2="&#x2019;" k="-18" />
<hkern u1="&#xee;" u2="&#x2018;" k="-66" />
<hkern u1="&#xee;" u2="&#xfe;" k="-30" />
<hkern u1="&#xee;" u2="&#xef;" k="-12" />
<hkern u1="&#xee;" u2="&#xdf;" k="-30" />
<hkern u1="&#xee;" u2="&#xba;" k="-71" />
<hkern u1="&#xee;" u2="&#xaa;" k="-15" />
<hkern u1="&#xee;" u2="&#x7c;" k="-17" />
<hkern u1="&#xee;" u2="l" k="-30" />
<hkern u1="&#xee;" u2="k" k="-30" />
<hkern u1="&#xee;" u2="h" k="-30" />
<hkern u1="&#xee;" u2="b" k="-30" />
<hkern u1="&#xee;" u2="\" k="-8" />
<hkern u1="&#xee;" u2="&#x3f;" k="-19" />
<hkern u1="&#xee;" u2="&#x2a;" k="-23" />
<hkern u1="&#xee;" u2="&#x29;" k="-22" />
<hkern u1="&#xee;" u2="&#x27;" k="-54" />
<hkern u1="&#xee;" u2="&#x22;" k="-54" />
<hkern u1="&#xee;" u2="&#x21;" k="-19" />
<hkern u1="&#xef;" u2="&#x2122;" k="-53" />
<hkern u1="&#xef;" u2="&#x201d;" k="-15" />
<hkern u1="&#xef;" u2="&#x201c;" k="-40" />
<hkern u1="&#xef;" u2="&#x2019;" k="-15" />
<hkern u1="&#xef;" u2="&#x2018;" k="-40" />
<hkern u1="&#xef;" u2="&#xfe;" k="-9" />
<hkern u1="&#xef;" u2="&#xef;" k="-12" />
<hkern u1="&#xef;" u2="&#xee;" k="-23" />
<hkern u1="&#xef;" u2="&#xed;" k="-23" />
<hkern u1="&#xef;" u2="&#xec;" k="-23" />
<hkern u1="&#xef;" u2="&#xdf;" k="-9" />
<hkern u1="&#xef;" u2="&#xba;" k="-41" />
<hkern u1="&#xef;" u2="&#xaa;" k="-20" />
<hkern u1="&#xef;" u2="&#x7d;" k="-38" />
<hkern u1="&#xef;" u2="l" k="-9" />
<hkern u1="&#xef;" u2="k" k="-9" />
<hkern u1="&#xef;" u2="j" k="-23" />
<hkern u1="&#xef;" u2="i" k="-23" />
<hkern u1="&#xef;" u2="h" k="-9" />
<hkern u1="&#xef;" u2="b" k="-9" />
<hkern u1="&#xef;" u2="]" k="-38" />
<hkern u1="&#xef;" u2="\" k="-19" />
<hkern u1="&#xef;" u2="&#x3f;" k="-47" />
<hkern u1="&#xef;" u2="&#x2a;" k="-49" />
<hkern u1="&#xef;" u2="&#x29;" k="-37" />
<hkern u1="&#xef;" u2="&#x27;" k="-36" />
<hkern u1="&#xef;" u2="&#x22;" k="-36" />
<hkern u1="&#xf0;" g2="uniFB04" k="6" />
<hkern u1="&#xf0;" g2="uniFB03" k="6" />
<hkern u1="&#xf0;" g2="uniFB02" k="6" />
<hkern u1="&#xf0;" g2="uniFB01" k="6" />
<hkern u1="&#xf0;" u2="&#x2122;" k="12" />
<hkern u1="&#xf0;" u2="&#xff;" k="10" />
<hkern u1="&#xf0;" u2="&#xfd;" k="10" />
<hkern u1="&#xf0;" u2="&#x7d;" k="45" />
<hkern u1="&#xf0;" u2="z" k="12" />
<hkern u1="&#xf0;" u2="y" k="10" />
<hkern u1="&#xf0;" u2="x" k="30" />
<hkern u1="&#xf0;" u2="v" k="9" />
<hkern u1="&#xf0;" u2="f" k="6" />
<hkern u1="&#xf0;" u2="]" k="52" />
<hkern u1="&#xf0;" u2="\" k="42" />
<hkern u1="&#xf0;" u2="&#x3f;" k="15" />
<hkern u1="&#xf0;" u2="&#x2f;" k="24" />
<hkern u1="&#xf0;" u2="&#x29;" k="43" />
<hkern u1="&#xf1;" u2="&#x2122;" k="48" />
<hkern u1="&#xf1;" u2="&#xba;" k="18" />
<hkern u1="&#xf1;" u2="&#x7d;" k="52" />
<hkern u1="&#xf1;" u2="]" k="60" />
<hkern u1="&#xf1;" u2="\" k="85" />
<hkern u1="&#xf1;" u2="&#x3f;" k="47" />
<hkern u1="&#xf1;" u2="&#x2a;" k="43" />
<hkern u1="&#xf1;" u2="&#x29;" k="36" />
<hkern u1="&#xf2;" u2="&#x2122;" k="55" />
<hkern u1="&#xf2;" u2="&#xba;" k="40" />
<hkern u1="&#xf2;" u2="&#xaa;" k="17" />
<hkern u1="&#xf2;" u2="&#x7d;" k="62" />
<hkern u1="&#xf2;" u2="]" k="71" />
<hkern u1="&#xf2;" u2="\" k="93" />
<hkern u1="&#xf2;" u2="&#x3f;" k="61" />
<hkern u1="&#xf2;" u2="&#x2a;" k="55" />
<hkern u1="&#xf2;" u2="&#x29;" k="61" />
<hkern u1="&#xf3;" u2="&#x2122;" k="55" />
<hkern u1="&#xf3;" u2="&#xba;" k="40" />
<hkern u1="&#xf3;" u2="&#xaa;" k="17" />
<hkern u1="&#xf3;" u2="&#x7d;" k="62" />
<hkern u1="&#xf3;" u2="]" k="71" />
<hkern u1="&#xf3;" u2="\" k="93" />
<hkern u1="&#xf3;" u2="&#x3f;" k="61" />
<hkern u1="&#xf3;" u2="&#x2a;" k="55" />
<hkern u1="&#xf3;" u2="&#x29;" k="61" />
<hkern u1="&#xf4;" u2="&#x2122;" k="55" />
<hkern u1="&#xf4;" u2="&#xba;" k="40" />
<hkern u1="&#xf4;" u2="&#xaa;" k="17" />
<hkern u1="&#xf4;" u2="&#x7d;" k="62" />
<hkern u1="&#xf4;" u2="]" k="71" />
<hkern u1="&#xf4;" u2="\" k="93" />
<hkern u1="&#xf4;" u2="&#x3f;" k="61" />
<hkern u1="&#xf4;" u2="&#x2a;" k="55" />
<hkern u1="&#xf4;" u2="&#x29;" k="61" />
<hkern u1="&#xf5;" u2="&#x2122;" k="55" />
<hkern u1="&#xf5;" u2="&#xba;" k="40" />
<hkern u1="&#xf5;" u2="&#xaa;" k="17" />
<hkern u1="&#xf5;" u2="&#x7d;" k="62" />
<hkern u1="&#xf5;" u2="]" k="71" />
<hkern u1="&#xf5;" u2="\" k="93" />
<hkern u1="&#xf5;" u2="&#x3f;" k="61" />
<hkern u1="&#xf5;" u2="&#x2a;" k="55" />
<hkern u1="&#xf5;" u2="&#x29;" k="61" />
<hkern u1="&#xf6;" u2="&#x2122;" k="55" />
<hkern u1="&#xf6;" u2="&#xba;" k="40" />
<hkern u1="&#xf6;" u2="&#xaa;" k="17" />
<hkern u1="&#xf6;" u2="&#x7d;" k="62" />
<hkern u1="&#xf6;" u2="]" k="71" />
<hkern u1="&#xf6;" u2="\" k="93" />
<hkern u1="&#xf6;" u2="&#x3f;" k="61" />
<hkern u1="&#xf6;" u2="&#x2a;" k="55" />
<hkern u1="&#xf6;" u2="&#x29;" k="61" />
<hkern u1="&#xf8;" u2="&#x2122;" k="55" />
<hkern u1="&#xf8;" u2="&#x201c;" k="27" />
<hkern u1="&#xf8;" u2="&#x2018;" k="27" />
<hkern u1="&#xf8;" u2="&#xba;" k="13" />
<hkern u1="&#xf8;" u2="&#xaa;" k="17" />
<hkern u1="&#xf8;" u2="&#x7d;" k="62" />
<hkern u1="&#xf8;" u2="]" k="71" />
<hkern u1="&#xf8;" u2="\" k="93" />
<hkern u1="&#xf8;" u2="&#x3f;" k="61" />
<hkern u1="&#xf8;" u2="&#x2a;" k="55" />
<hkern u1="&#xf8;" u2="&#x29;" k="61" />
<hkern u1="&#xf8;" u2="&#x27;" k="32" />
<hkern u1="&#xf8;" u2="&#x22;" k="32" />
<hkern u1="&#xf9;" u2="&#x2122;" k="30" />
<hkern u1="&#xf9;" u2="&#x7d;" k="47" />
<hkern u1="&#xf9;" u2="]" k="55" />
<hkern u1="&#xf9;" u2="\" k="56" />
<hkern u1="&#xf9;" u2="&#x3f;" k="18" />
<hkern u1="&#xf9;" u2="&#x29;" k="16" />
<hkern u1="&#xfa;" u2="&#x2122;" k="30" />
<hkern u1="&#xfa;" u2="&#x7d;" k="47" />
<hkern u1="&#xfa;" u2="]" k="55" />
<hkern u1="&#xfa;" u2="\" k="56" />
<hkern u1="&#xfa;" u2="&#x3f;" k="18" />
<hkern u1="&#xfa;" u2="&#x29;" k="16" />
<hkern u1="&#xfb;" u2="&#x2122;" k="30" />
<hkern u1="&#xfb;" u2="&#x7d;" k="47" />
<hkern u1="&#xfb;" u2="]" k="55" />
<hkern u1="&#xfb;" u2="\" k="56" />
<hkern u1="&#xfb;" u2="&#x3f;" k="18" />
<hkern u1="&#xfb;" u2="&#x29;" k="16" />
<hkern u1="&#xfc;" u2="&#x2122;" k="30" />
<hkern u1="&#xfc;" u2="&#x7d;" k="47" />
<hkern u1="&#xfc;" u2="]" k="55" />
<hkern u1="&#xfc;" u2="\" k="56" />
<hkern u1="&#xfc;" u2="&#x3f;" k="18" />
<hkern u1="&#xfc;" u2="&#x29;" k="16" />
<hkern u1="&#xfd;" u2="&#xf0;" k="45" />
<hkern u1="&#xfd;" u2="&#x7d;" k="49" />
<hkern u1="&#xfd;" u2="]" k="56" />
<hkern u1="&#xfd;" u2="&#x2f;" k="77" />
<hkern u1="&#xfd;" u2="&#x2c;" k="77" />
<hkern u1="&#xfd;" u2="&#x29;" k="44" />
<hkern u1="&#xfd;" u2="&#x20;" k="50" />
<hkern u1="&#xfe;" u2="&#x2122;" k="57" />
<hkern u1="&#xfe;" u2="&#xba;" k="38" />
<hkern u1="&#xfe;" u2="&#xaa;" k="28" />
<hkern u1="&#xfe;" u2="&#x7d;" k="60" />
<hkern u1="&#xfe;" u2="]" k="70" />
<hkern u1="&#xfe;" u2="\" k="88" />
<hkern u1="&#xfe;" u2="&#x3f;" k="62" />
<hkern u1="&#xfe;" u2="&#x2f;" k="18" />
<hkern u1="&#xfe;" u2="&#x2a;" k="56" />
<hkern u1="&#xfe;" u2="&#x29;" k="59" />
<hkern u1="&#xff;" u2="&#xf0;" k="45" />
<hkern u1="&#xff;" u2="&#x7d;" k="49" />
<hkern u1="&#xff;" u2="]" k="56" />
<hkern u1="&#xff;" u2="&#x2f;" k="77" />
<hkern u1="&#xff;" u2="&#x2c;" k="77" />
<hkern u1="&#xff;" u2="&#x29;" k="44" />
<hkern u1="&#xff;" u2="&#x20;" k="50" />
<hkern u1="&#x152;" u2="&#xf0;" k="48" />
<hkern u1="&#x152;" u2="&#xef;" k="-15" />
<hkern u1="&#x152;" u2="&#xee;" k="-28" />
<hkern u1="&#x152;" u2="&#xed;" k="11" />
<hkern u1="&#x152;" u2="&#xec;" k="-22" />
<hkern u1="&#x152;" u2="&#xdf;" k="27" />
<hkern u1="&#x153;" u2="&#x2122;" k="51" />
<hkern u1="&#x153;" u2="&#xba;" k="33" />
<hkern u1="&#x153;" u2="&#x7d;" k="58" />
<hkern u1="&#x153;" u2="]" k="65" />
<hkern u1="&#x153;" u2="\" k="83" />
<hkern u1="&#x153;" u2="&#x3f;" k="51" />
<hkern u1="&#x153;" u2="&#x2a;" k="49" />
<hkern u1="&#x153;" u2="&#x29;" k="47" />
<hkern u1="&#x178;" g2="uniFB02" k="109" />
<hkern u1="&#x178;" u2="&#x2122;" k="-31" />
<hkern u1="&#x178;" u2="&#xf9;" k="158" />
<hkern u1="&#x178;" u2="&#xf6;" k="213" />
<hkern u1="&#x178;" u2="&#xf2;" k="188" />
<hkern u1="&#x178;" u2="&#xf0;" k="161" />
<hkern u1="&#x178;" u2="&#xef;" k="-36" />
<hkern u1="&#x178;" u2="&#xed;" k="72" />
<hkern u1="&#x178;" u2="&#xec;" k="-78" />
<hkern u1="&#x178;" u2="&#xeb;" k="206" />
<hkern u1="&#x178;" u2="&#xe8;" k="181" />
<hkern u1="&#x178;" u2="&#xe4;" k="176" />
<hkern u1="&#x178;" u2="&#xe3;" k="212" />
<hkern u1="&#x178;" u2="&#xe0;" k="152" />
<hkern u1="&#x178;" u2="&#xdf;" k="99" />
<hkern u1="&#x178;" u2="&#xae;" k="61" />
<hkern u1="&#x178;" u2="&#xa9;" k="62" />
<hkern u1="&#x178;" u2="&#x7d;" k="-14" />
<hkern u1="&#x178;" u2="]" k="-17" />
<hkern u1="&#x178;" u2="\" k="-9" />
<hkern u1="&#x178;" u2="&#x40;" k="95" />
<hkern u1="&#x178;" u2="&#x3b;" k="75" />
<hkern u1="&#x178;" u2="&#x3a;" k="77" />
<hkern u1="&#x178;" u2="&#x2f;" k="151" />
<hkern u1="&#x178;" u2="&#x2c;" k="166" />
<hkern u1="&#x178;" u2="&#x29;" k="-13" />
<hkern u1="&#x178;" u2="&#x26;" k="82" />
<hkern u1="&#x178;" u2="&#x20;" k="81" />
<hkern u1="&#x2018;" u2="&#xf0;" k="53" />
<hkern u1="&#x2018;" u2="&#xee;" k="-17" />
<hkern u1="&#x2018;" u2="&#xec;" k="-47" />
<hkern u1="&#x2018;" u2="&#xce;" k="-24" />
<hkern u1="&#x2018;" u2="&#x2c;" k="219" />
<hkern u1="&#x2019;" u2="&#xf0;" k="46" />
<hkern u1="&#x2019;" u2="&#xef;" k="-40" />
<hkern u1="&#x2019;" u2="&#xee;" k="-52" />
<hkern u1="&#x2019;" u2="&#xec;" k="-32" />
<hkern u1="&#x2019;" u2="&#x40;" k="23" />
<hkern u1="&#x2019;" u2="&#x2f;" k="116" />
<hkern u1="&#x2019;" u2="&#x2c;" k="218" />
<hkern u1="&#x2019;" u2="&#x26;" k="40" />
<hkern u1="&#x2019;" u2="&#x20;" k="18" />
<hkern u1="&#x201c;" u2="&#xf0;" k="53" />
<hkern u1="&#x201c;" u2="&#xee;" k="-17" />
<hkern u1="&#x201c;" u2="&#xec;" k="-47" />
<hkern u1="&#x201c;" u2="&#xce;" k="-24" />
<hkern u1="&#x201c;" u2="&#x2c;" k="219" />
<hkern u1="&#x201d;" u2="&#xf0;" k="46" />
<hkern u1="&#x201d;" u2="&#xef;" k="-40" />
<hkern u1="&#x201d;" u2="&#xee;" k="-52" />
<hkern u1="&#x201d;" u2="&#xec;" k="-32" />
<hkern u1="&#x201d;" u2="&#x40;" k="23" />
<hkern u1="&#x201d;" u2="&#x2f;" k="116" />
<hkern u1="&#x201d;" u2="&#x2c;" k="218" />
<hkern u1="&#x201d;" u2="&#x26;" k="40" />
<hkern u1="&#x201d;" u2="&#x20;" k="18" />
<hkern g1="uniFB01" u2="&#xef;" k="-1" />
<hkern g1="uniFB02" u2="&#xee;" k="-19" />
<hkern g1="uniFB02" u2="&#xb7;" k="67" />
<hkern g1="uniFB03" u2="&#xef;" k="-1" />
<hkern g1="uniFB04" u2="&#xee;" k="-19" />
<hkern g1="uniFB04" u2="&#xb7;" k="67" />
<hkern g1="B" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="22" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="47" />
<hkern g1="B" 	g2="V" 	k="28" />
<hkern g1="B" 	g2="t" 	k="31" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="26" />
<hkern g1="B" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="30" />
<hkern g1="B" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="22" />
<hkern g1="B" 	g2="m,n,p,r,ntilde" 	k="23" />
<hkern g1="B" 	g2="l" 	k="20" />
<hkern g1="B" 	g2="T" 	k="31" />
<hkern g1="B" 	g2="W" 	k="13" />
<hkern g1="B" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="B" 	g2="d,q" 	k="15" />
<hkern g1="B" 	g2="v" 	k="25" />
<hkern g1="B" 	g2="w" 	k="11" />
<hkern g1="B" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="15" />
<hkern g1="B" 	g2="J" 	k="16" />
<hkern g1="B" 	g2="X" 	k="22" />
<hkern g1="B" 	g2="g" 	k="38" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="B" 	g2="s" 	k="42" />
<hkern g1="B" 	g2="AE" 	k="39" />
<hkern g1="B" 	g2="x" 	k="38" />
<hkern g1="B" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="B" 	g2="z" 	k="34" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,ntilde" 	k="6" />
<hkern g1="C,Ccedilla" 	g2="d,q" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="34" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="24" />
<hkern g1="C,Ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="24" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="13" />
<hkern g1="D,Eth" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="80" />
<hkern g1="D,Eth" 	g2="V" 	k="43" />
<hkern g1="D,Eth" 	g2="t" 	k="5" />
<hkern g1="D,Eth" 	g2="y,yacute,ydieresis" 	k="6" />
<hkern g1="D,Eth" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="8" />
<hkern g1="D,Eth" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="D,Eth" 	g2="m,n,p,r,ntilde" 	k="22" />
<hkern g1="D,Eth" 	g2="l" 	k="19" />
<hkern g1="D,Eth" 	g2="T" 	k="68" />
<hkern g1="D,Eth" 	g2="W" 	k="22" />
<hkern g1="D,Eth" 	g2="b,h,k,germandbls,thorn" 	k="19" />
<hkern g1="D,Eth" 	g2="d,q" 	k="14" />
<hkern g1="D,Eth" 	g2="v" 	k="4" />
<hkern g1="D,Eth" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="D,Eth" 	g2="J" 	k="75" />
<hkern g1="D,Eth" 	g2="X" 	k="58" />
<hkern g1="D,Eth" 	g2="g" 	k="25" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="D,Eth" 	g2="s" 	k="17" />
<hkern g1="D,Eth" 	g2="AE" 	k="71" />
<hkern g1="D,Eth" 	g2="x" 	k="33" />
<hkern g1="D,Eth" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="D,Eth" 	g2="z" 	k="23" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="D,Eth" 	g2="period,ellipsis" 	k="46" />
<hkern g1="D,Eth" 	g2="Z" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="5" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="43" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="44" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="m,n,p,r,ntilde" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="l" 	k="9" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="b,h,k,germandbls,thorn" 	k="9" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d,q" 	k="43" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="32" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="47" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="36" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="30" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="17" />
<hkern g1="F" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="4" />
<hkern g1="F" 	g2="t" 	k="45" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="32" />
<hkern g1="F" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="47" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="77" />
<hkern g1="F" 	g2="m,n,p,r,ntilde" 	k="88" />
<hkern g1="F" 	g2="l" 	k="18" />
<hkern g1="F" 	g2="b,h,k,germandbls,thorn" 	k="18" />
<hkern g1="F" 	g2="d,q" 	k="91" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="13" />
<hkern g1="F" 	g2="v" 	k="33" />
<hkern g1="F" 	g2="w" 	k="28" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="89" />
<hkern g1="F" 	g2="J" 	k="190" />
<hkern g1="F" 	g2="g" 	k="99" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="119" />
<hkern g1="F" 	g2="s" 	k="93" />
<hkern g1="F" 	g2="AE" 	k="174" />
<hkern g1="F" 	g2="x" 	k="59" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="145" />
<hkern g1="F" 	g2="z" 	k="81" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="183" />
<hkern g1="F" 	g2="period,ellipsis" 	k="195" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="46" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="42" />
<hkern g1="F" 	g2="S" 	k="34" />
<hkern g1="F" 	g2="hyphen,emdash" 	k="30" />
<hkern g1="G" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="8" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="34" />
<hkern g1="G" 	g2="V" 	k="26" />
<hkern g1="G" 	g2="t" 	k="33" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="39" />
<hkern g1="G" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="33" />
<hkern g1="G" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="17" />
<hkern g1="G" 	g2="m,n,p,r,ntilde" 	k="16" />
<hkern g1="G" 	g2="l" 	k="9" />
<hkern g1="G" 	g2="T" 	k="6" />
<hkern g1="G" 	g2="W" 	k="8" />
<hkern g1="G" 	g2="b,h,k,germandbls,thorn" 	k="9" />
<hkern g1="G" 	g2="d,q" 	k="12" />
<hkern g1="G" 	g2="v" 	k="36" />
<hkern g1="G" 	g2="w" 	k="22" />
<hkern g1="G" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="G" 	g2="g" 	k="23" />
<hkern g1="G" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="12" />
<hkern g1="G" 	g2="s" 	k="13" />
<hkern g1="G" 	g2="AE" 	k="16" />
<hkern g1="G" 	g2="x" 	k="17" />
<hkern g1="G" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="6" />
<hkern g1="G" 	g2="z" 	k="17" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="15" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="t" 	k="30" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="y,yacute,ydieresis" 	k="24" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="31" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="18" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="m,n,p,r,ntilde" 	k="15" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="l" 	k="15" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="b,h,k,germandbls,thorn" 	k="15" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="d,q" 	k="31" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="v" 	k="25" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="w" 	k="17" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="32" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="g" 	k="34" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="s" 	k="31" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="x" 	k="7" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="z" 	k="23" />
<hkern g1="J" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="17" />
<hkern g1="J" 	g2="t" 	k="24" />
<hkern g1="J" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="J" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="26" />
<hkern g1="J" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="29" />
<hkern g1="J" 	g2="m,n,p,r,ntilde" 	k="24" />
<hkern g1="J" 	g2="l" 	k="18" />
<hkern g1="J" 	g2="b,h,k,germandbls,thorn" 	k="18" />
<hkern g1="J" 	g2="d,q" 	k="36" />
<hkern g1="J" 	g2="v" 	k="16" />
<hkern g1="J" 	g2="w" 	k="6" />
<hkern g1="J" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="J" 	g2="J" 	k="35" />
<hkern g1="J" 	g2="g" 	k="44" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="19" />
<hkern g1="J" 	g2="s" 	k="43" />
<hkern g1="J" 	g2="AE" 	k="37" />
<hkern g1="J" 	g2="x" 	k="22" />
<hkern g1="J" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="J" 	g2="z" 	k="35" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="12" />
<hkern g1="J" 	g2="period,ellipsis" 	k="17" />
<hkern g1="K" 	g2="t" 	k="74" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="106" />
<hkern g1="K" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="50" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="52" />
<hkern g1="K" 	g2="d,q" 	k="63" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="88" />
<hkern g1="K" 	g2="v" 	k="105" />
<hkern g1="K" 	g2="w" 	k="81" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="78" />
<hkern g1="K" 	g2="s" 	k="7" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="6" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="58" />
<hkern g1="K" 	g2="hyphen,emdash" 	k="59" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="194" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="53" />
<hkern g1="L" 	g2="V" 	k="180" />
<hkern g1="L" 	g2="t" 	k="90" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="191" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="202" />
<hkern g1="L" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="67" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="58" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="205" />
<hkern g1="L" 	g2="T" 	k="193" />
<hkern g1="L" 	g2="W" 	k="132" />
<hkern g1="L" 	g2="d,q" 	k="58" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="69" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="205" />
<hkern g1="L" 	g2="v" 	k="188" />
<hkern g1="L" 	g2="w" 	k="144" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="75" />
<hkern g1="L" 	g2="s" 	k="12" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="95" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="30" />
<hkern g1="L" 	g2="hyphen,emdash" 	k="145" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="19" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="41" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t" 	k="4" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="y,yacute,ydieresis" 	k="6" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="8" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="15" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="l" 	k="19" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="66" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="22" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="b,h,k,germandbls,thorn" 	k="19" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="d,q" 	k="14" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="v" 	k="4" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="13" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="75" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="56" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="g" 	k="24" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="39" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="s" 	k="16" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="69" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="32" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="39" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="period,ellipsis" 	k="48" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="34" />
<hkern g1="P" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="6" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="P" 	g2="V" 	k="16" />
<hkern g1="P" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="9" />
<hkern g1="P" 	g2="m,n,p,r,ntilde" 	k="19" />
<hkern g1="P" 	g2="l" 	k="6" />
<hkern g1="P" 	g2="b,h,k,germandbls,thorn" 	k="6" />
<hkern g1="P" 	g2="d,q" 	k="55" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="54" />
<hkern g1="P" 	g2="J" 	k="168" />
<hkern g1="P" 	g2="X" 	k="26" />
<hkern g1="P" 	g2="g" 	k="42" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="P" 	g2="s" 	k="26" />
<hkern g1="P" 	g2="AE" 	k="168" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="33" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="198" />
<hkern g1="P" 	g2="period,ellipsis" 	k="206" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="24" />
<hkern g1="P" 	g2="hyphen,emdash" 	k="47" />
<hkern g1="R" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="14" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="56" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="8" />
<hkern g1="R" 	g2="V" 	k="38" />
<hkern g1="R" 	g2="t" 	k="35" />
<hkern g1="R" 	g2="y,yacute,ydieresis" 	k="35" />
<hkern g1="R" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="31" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="42" />
<hkern g1="R" 	g2="m,n,p,r,ntilde" 	k="14" />
<hkern g1="R" 	g2="l" 	k="13" />
<hkern g1="R" 	g2="T" 	k="48" />
<hkern g1="R" 	g2="W" 	k="23" />
<hkern g1="R" 	g2="b,h,k,germandbls,thorn" 	k="13" />
<hkern g1="R" 	g2="d,q" 	k="53" />
<hkern g1="R" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="R" 	g2="v" 	k="34" />
<hkern g1="R" 	g2="w" 	k="26" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="67" />
<hkern g1="R" 	g2="g" 	k="7" />
<hkern g1="R" 	g2="s" 	k="16" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="15" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="S" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="9" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="S" 	g2="V" 	k="27" />
<hkern g1="S" 	g2="t" 	k="46" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="53" />
<hkern g1="S" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="47" />
<hkern g1="S" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="S" 	g2="m,n,p,r,ntilde" 	k="23" />
<hkern g1="S" 	g2="l" 	k="17" />
<hkern g1="S" 	g2="W" 	k="6" />
<hkern g1="S" 	g2="b,h,k,germandbls,thorn" 	k="17" />
<hkern g1="S" 	g2="d,q" 	k="6" />
<hkern g1="S" 	g2="v" 	k="51" />
<hkern g1="S" 	g2="w" 	k="35" />
<hkern g1="S" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="6" />
<hkern g1="S" 	g2="J" 	k="5" />
<hkern g1="S" 	g2="X" 	k="6" />
<hkern g1="S" 	g2="g" 	k="32" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="27" />
<hkern g1="S" 	g2="s" 	k="28" />
<hkern g1="S" 	g2="AE" 	k="44" />
<hkern g1="S" 	g2="x" 	k="49" />
<hkern g1="S" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="S" 	g2="z" 	k="35" />
<hkern g1="T" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="6" />
<hkern g1="T" 	g2="t" 	k="126" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="177" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="115" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="214" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="225" />
<hkern g1="T" 	g2="l" 	k="17" />
<hkern g1="T" 	g2="b,h,k,germandbls,thorn" 	k="17" />
<hkern g1="T" 	g2="d,q" 	k="248" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="66" />
<hkern g1="T" 	g2="v" 	k="178" />
<hkern g1="T" 	g2="w" 	k="179" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="250" />
<hkern g1="T" 	g2="J" 	k="141" />
<hkern g1="T" 	g2="g" 	k="251" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="T" 	g2="s" 	k="234" />
<hkern g1="T" 	g2="AE" 	k="185" />
<hkern g1="T" 	g2="x" 	k="182" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="233" />
<hkern g1="T" 	g2="z" 	k="199" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="134" />
<hkern g1="T" 	g2="period,ellipsis" 	k="144" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="164" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="152" />
<hkern g1="T" 	g2="S" 	k="6" />
<hkern g1="T" 	g2="hyphen,emdash" 	k="125" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="22" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="t" 	k="23" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="26" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="32" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="m,n,p,r,ntilde" 	k="31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="l" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="b,h,k,germandbls,thorn" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="d,q" 	k="39" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="v" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="w" 	k="6" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="56" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="49" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="34" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="49" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="59" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="24" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="49" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="37" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="34" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="period,ellipsis" 	k="41" />
<hkern g1="V" 	g2="t" 	k="57" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="47" />
<hkern g1="V" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="55" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="104" />
<hkern g1="V" 	g2="m,n,p,r,ntilde" 	k="115" />
<hkern g1="V" 	g2="l" 	k="6" />
<hkern g1="V" 	g2="b,h,k,germandbls,thorn" 	k="6" />
<hkern g1="V" 	g2="d,q" 	k="147" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="V" 	g2="v" 	k="49" />
<hkern g1="V" 	g2="w" 	k="49" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="154" />
<hkern g1="V" 	g2="J" 	k="141" />
<hkern g1="V" 	g2="g" 	k="153" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="111" />
<hkern g1="V" 	g2="s" 	k="141" />
<hkern g1="V" 	g2="AE" 	k="163" />
<hkern g1="V" 	g2="x" 	k="59" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="151" />
<hkern g1="V" 	g2="z" 	k="91" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="139" />
<hkern g1="V" 	g2="period,ellipsis" 	k="151" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="103" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="68" />
<hkern g1="V" 	g2="S" 	k="32" />
<hkern g1="V" 	g2="hyphen,emdash" 	k="63" />
<hkern g1="W" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="7" />
<hkern g1="W" 	g2="t" 	k="46" />
<hkern g1="W" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="W" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="43" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="78" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="87" />
<hkern g1="W" 	g2="l" 	k="10" />
<hkern g1="W" 	g2="b,h,k,germandbls,thorn" 	k="10" />
<hkern g1="W" 	g2="d,q" 	k="110" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="W" 	g2="v" 	k="32" />
<hkern g1="W" 	g2="w" 	k="31" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="116" />
<hkern g1="W" 	g2="J" 	k="119" />
<hkern g1="W" 	g2="g" 	k="115" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="88" />
<hkern g1="W" 	g2="s" 	k="109" />
<hkern g1="W" 	g2="AE" 	k="140" />
<hkern g1="W" 	g2="x" 	k="39" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="116" />
<hkern g1="W" 	g2="z" 	k="67" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="99" />
<hkern g1="W" 	g2="period,ellipsis" 	k="109" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="73" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="W" 	g2="S" 	k="11" />
<hkern g1="W" 	g2="hyphen,emdash" 	k="34" />
<hkern g1="X" 	g2="t" 	k="78" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="95" />
<hkern g1="X" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="61" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="65" />
<hkern g1="X" 	g2="m,n,p,r,ntilde" 	k="23" />
<hkern g1="X" 	g2="l" 	k="7" />
<hkern g1="X" 	g2="b,h,k,germandbls,thorn" 	k="7" />
<hkern g1="X" 	g2="d,q" 	k="67" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="59" />
<hkern g1="X" 	g2="v" 	k="94" />
<hkern g1="X" 	g2="w" 	k="84" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="X" 	g2="g" 	k="12" />
<hkern g1="X" 	g2="s" 	k="14" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="58" />
<hkern g1="X" 	g2="hyphen,emdash" 	k="49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="99" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="114" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="172" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="173" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="l" 	k="6" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="b,h,k,germandbls,thorn" 	k="6" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d,q" 	k="222" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="114" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="109" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="224" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="156" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="204" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="218" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="198" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="109" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="227" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="148" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="period,ellipsis" 	k="168" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="116" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,emdash" 	k="132" />
<hkern g1="Z" 	g2="i,j,igrave,iacute,icircumflex,idieresis" 	k="4" />
<hkern g1="Z" 	g2="t" 	k="52" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="62" />
<hkern g1="Z" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="49" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="47" />
<hkern g1="Z" 	g2="m,n,p,r,ntilde" 	k="29" />
<hkern g1="Z" 	g2="l" 	k="7" />
<hkern g1="Z" 	g2="b,h,k,germandbls,thorn" 	k="7" />
<hkern g1="Z" 	g2="d,q" 	k="43" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="34" />
<hkern g1="Z" 	g2="v" 	k="63" />
<hkern g1="Z" 	g2="w" 	k="53" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="Z" 	g2="g" 	k="25" />
<hkern g1="Z" 	g2="s" 	k="16" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="7" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="Z" 	g2="hyphen,emdash" 	k="13" />
<hkern g1="b,p,thorn" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="16" />
<hkern g1="b,p,thorn" 	g2="w" 	k="12" />
<hkern g1="b,p,thorn" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="7" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="43" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ydieresis" 	k="208" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="b,p,thorn" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="30" />
<hkern g1="b,p,thorn" 	g2="W" 	k="111" />
<hkern g1="b,p,thorn" 	g2="S" 	k="29" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="50" />
<hkern g1="b,p,thorn" 	g2="V" 	k="147" />
<hkern g1="b,p,thorn" 	g2="v" 	k="24" />
<hkern g1="b,p,thorn" 	g2="X" 	k="78" />
<hkern g1="b,p,thorn" 	g2="Z" 	k="51" />
<hkern g1="b,p,thorn" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="38" />
<hkern g1="b,p,thorn" 	g2="T" 	k="248" />
<hkern g1="b,p,thorn" 	g2="t" 	k="13" />
<hkern g1="b,p,thorn" 	g2="z" 	k="17" />
<hkern g1="b,p,thorn" 	g2="x" 	k="41" />
<hkern g1="b,p,thorn" 	g2="J" 	k="66" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="c,ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="22" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="11" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="179" />
<hkern g1="c,ccedilla" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="11" />
<hkern g1="c,ccedilla" 	g2="W" 	k="83" />
<hkern g1="c,ccedilla" 	g2="S" 	k="9" />
<hkern g1="c,ccedilla" 	g2="V" 	k="118" />
<hkern g1="c,ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="37" />
<hkern g1="c,ccedilla" 	g2="T" 	k="219" />
<hkern g1="c,ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="c,ccedilla" 	g2="d,q" 	k="14" />
<hkern g1="c,ccedilla" 	g2="hyphen,emdash" 	k="16" />
<hkern g1="d" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="d" 	g2="Y,Yacute,Ydieresis" 	k="6" />
<hkern g1="d" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="15" />
<hkern g1="d" 	g2="W" 	k="10" />
<hkern g1="d" 	g2="S" 	k="10" />
<hkern g1="d" 	g2="V" 	k="6" />
<hkern g1="d" 	g2="X" 	k="7" />
<hkern g1="d" 	g2="Z" 	k="15" />
<hkern g1="d" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="d" 	g2="T" 	k="17" />
<hkern g1="d" 	g2="J" 	k="6" />
<hkern g1="d" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="36" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="36" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="231" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="30" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="122" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="30" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="43" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="154" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="34" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Z" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="38" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="238" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="13" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="22" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="f" 	g2="Y,Yacute,Ydieresis" 	k="-36" />
<hkern g1="f" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="5" />
<hkern g1="f" 	g2="W" 	k="-18" />
<hkern g1="f" 	g2="S" 	k="6" />
<hkern g1="f" 	g2="V" 	k="-35" />
<hkern g1="f" 	g2="X" 	k="-16" />
<hkern g1="f" 	g2="J" 	k="137" />
<hkern g1="f" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="92" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="34" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="f" 	g2="d,q" 	k="34" />
<hkern g1="f" 	g2="hyphen,emdash" 	k="30" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="6" />
<hkern g1="f" 	g2="g" 	k="10" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="59" />
<hkern g1="f" 	g2="period,ellipsis" 	k="47" />
<hkern g1="g" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="141" />
<hkern g1="g" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="22" />
<hkern g1="g" 	g2="W" 	k="52" />
<hkern g1="g" 	g2="S" 	k="8" />
<hkern g1="g" 	g2="V" 	k="79" />
<hkern g1="g" 	g2="X" 	k="5" />
<hkern g1="g" 	g2="Z" 	k="6" />
<hkern g1="g" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="26" />
<hkern g1="g" 	g2="T" 	k="204" />
<hkern g1="g" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="g" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="g" 	g2="d,q" 	k="14" />
<hkern g1="g" 	g2="g" 	k="4" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="19" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="15" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="W" 	k="7" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="S" 	k="11" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="Z" 	k="13" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="22" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="T" 	k="6" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="J" 	k="6" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="k" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="42" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="109" />
<hkern g1="k" 	g2="W" 	k="42" />
<hkern g1="k" 	g2="V" 	k="66" />
<hkern g1="k" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="26" />
<hkern g1="k" 	g2="T" 	k="182" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="58" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="k" 	g2="d,q" 	k="52" />
<hkern g1="k" 	g2="hyphen,emdash" 	k="47" />
<hkern g1="h,m,n,ntilde" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="5" />
<hkern g1="h,m,n,ntilde" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="32" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="213" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="26" />
<hkern g1="h,m,n,ntilde" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="19" />
<hkern g1="h,m,n,ntilde" 	g2="W" 	k="94" />
<hkern g1="h,m,n,ntilde" 	g2="S" 	k="19" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="37" />
<hkern g1="h,m,n,ntilde" 	g2="V" 	k="134" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="22" />
<hkern g1="h,m,n,ntilde" 	g2="X" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="Z" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="38" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="254" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="15" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="44" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="43" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Y,Yacute,Ydieresis" 	k="225" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="32" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="W" 	k="115" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="S" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="50" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="V" 	k="153" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="X" 	k="80" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Z" 	k="50" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="40" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="T" 	k="253" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="46" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="J" 	k="65" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="g" 	k="5" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="101" />
<hkern g1="r" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="18" />
<hkern g1="r" 	g2="W" 	k="23" />
<hkern g1="r" 	g2="S" 	k="7" />
<hkern g1="r" 	g2="V" 	k="39" />
<hkern g1="r" 	g2="X" 	k="108" />
<hkern g1="r" 	g2="Z" 	k="61" />
<hkern g1="r" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="5" />
<hkern g1="r" 	g2="T" 	k="178" />
<hkern g1="r" 	g2="J" 	k="158" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="113" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="42" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="32" />
<hkern g1="r" 	g2="d,q" 	k="40" />
<hkern g1="r" 	g2="hyphen,emdash" 	k="42" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="r" 	g2="g" 	k="24" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="96" />
<hkern g1="r" 	g2="period,ellipsis" 	k="81" />
<hkern g1="r" 	g2="s" 	k="6" />
<hkern g1="s" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="19" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="13" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="199" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="s" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="30" />
<hkern g1="s" 	g2="W" 	k="97" />
<hkern g1="s" 	g2="S" 	k="5" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="11" />
<hkern g1="s" 	g2="V" 	k="121" />
<hkern g1="s" 	g2="v" 	k="15" />
<hkern g1="s" 	g2="X" 	k="33" />
<hkern g1="s" 	g2="Z" 	k="18" />
<hkern g1="s" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="39" />
<hkern g1="s" 	g2="T" 	k="231" />
<hkern g1="s" 	g2="J" 	k="12" />
<hkern g1="s" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="13" />
<hkern g1="s" 	g2="g" 	k="6" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="115" />
<hkern g1="t" 	g2="W" 	k="31" />
<hkern g1="t" 	g2="V" 	k="58" />
<hkern g1="t" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="12" />
<hkern g1="t" 	g2="T" 	k="182" />
<hkern g1="t" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="5" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="19" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="172" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="15" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="84" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="S" 	k="10" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="111" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="20" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Z" 	k="22" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="30" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="222" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="J" 	k="6" />
<hkern g1="v" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="v" 	g2="Y,Yacute,Ydieresis" 	k="114" />
<hkern g1="v" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="25" />
<hkern g1="v" 	g2="W" 	k="32" />
<hkern g1="v" 	g2="S" 	k="10" />
<hkern g1="v" 	g2="V" 	k="49" />
<hkern g1="v" 	g2="X" 	k="94" />
<hkern g1="v" 	g2="Z" 	k="59" />
<hkern g1="v" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="v" 	g2="T" 	k="178" />
<hkern g1="v" 	g2="J" 	k="150" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="82" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="v" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="v" 	g2="d,q" 	k="29" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="23" />
<hkern g1="v" 	g2="g" 	k="25" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="78" />
<hkern g1="v" 	g2="period,ellipsis" 	k="69" />
<hkern g1="v" 	g2="s" 	k="20" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="109" />
<hkern g1="w" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="17" />
<hkern g1="w" 	g2="W" 	k="31" />
<hkern g1="w" 	g2="S" 	k="7" />
<hkern g1="w" 	g2="V" 	k="49" />
<hkern g1="w" 	g2="X" 	k="84" />
<hkern g1="w" 	g2="Z" 	k="47" />
<hkern g1="w" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="6" />
<hkern g1="w" 	g2="T" 	k="179" />
<hkern g1="w" 	g2="J" 	k="127" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="60" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="w" 	g2="d,q" 	k="15" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="12" />
<hkern g1="w" 	g2="g" 	k="13" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="50" />
<hkern g1="w" 	g2="period,ellipsis" 	k="44" />
<hkern g1="w" 	g2="s" 	k="8" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="32" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="109" />
<hkern g1="x" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="7" />
<hkern g1="x" 	g2="W" 	k="39" />
<hkern g1="x" 	g2="S" 	k="5" />
<hkern g1="x" 	g2="V" 	k="59" />
<hkern g1="x" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="24" />
<hkern g1="x" 	g2="T" 	k="182" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="46" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="52" />
<hkern g1="x" 	g2="d,q" 	k="41" />
<hkern g1="x" 	g2="hyphen,emdash" 	k="33" />
<hkern g1="y,yacute,ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="109" />
<hkern g1="y,yacute,ydieresis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="23" />
<hkern g1="y,yacute,ydieresis" 	g2="W" 	k="28" />
<hkern g1="y,yacute,ydieresis" 	g2="S" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="V" 	k="44" />
<hkern g1="y,yacute,ydieresis" 	g2="X" 	k="93" />
<hkern g1="y,yacute,ydieresis" 	g2="Z" 	k="56" />
<hkern g1="y,yacute,ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="8" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="172" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="153" />
<hkern g1="y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="85" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="19" />
<hkern g1="y,yacute,ydieresis" 	g2="d,q" 	k="29" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="23" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="81" />
<hkern g1="y,yacute,ydieresis" 	g2="period,ellipsis" 	k="72" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="18" />
<hkern g1="z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="137" />
<hkern g1="z" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="23" />
<hkern g1="z" 	g2="W" 	k="56" />
<hkern g1="z" 	g2="S" 	k="5" />
<hkern g1="z" 	g2="V" 	k="78" />
<hkern g1="z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="32" />
<hkern g1="z" 	g2="T" 	k="191" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="19" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="22" />
<hkern g1="z" 	g2="d,q" 	k="16" />
<hkern g1="z" 	g2="hyphen,emdash" 	k="15" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="50" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="46" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="135" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="quoteright,quotedblright" 	g2="d,q" 	k="80" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="197" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="52" />
<hkern g1="quoteright,quotedblright" 	g2="period,ellipsis" 	k="218" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="142" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,emdash" 	k="89" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="17" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="83" />
<hkern g1="quoteright,quotedblright" 	g2="quotesinglbase,quotedblbase" 	k="214" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="99" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="39" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="79" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="157" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="209" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="214" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="134" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="34" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="88" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="138" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="23" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="115" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="153" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="68" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="23" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="86" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="73" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="158" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="165" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="103" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="57" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="40" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="52" />
<hkern g1="hyphen,emdash" 	g2="AE" 	k="33" />
<hkern g1="hyphen,emdash" 	g2="J" 	k="112" />
<hkern g1="hyphen,emdash" 	g2="W" 	k="34" />
<hkern g1="hyphen,emdash" 	g2="Y,Yacute,Ydieresis" 	k="132" />
<hkern g1="hyphen,emdash" 	g2="quoteright,quotedblright" 	k="66" />
<hkern g1="hyphen,emdash" 	g2="T" 	k="125" />
<hkern g1="hyphen,emdash" 	g2="V" 	k="63" />
<hkern g1="hyphen,emdash" 	g2="Z" 	k="12" />
<hkern g1="hyphen,emdash" 	g2="X" 	k="49" />
<hkern g1="hyphen,emdash" 	g2="z" 	k="20" />
<hkern g1="hyphen,emdash" 	g2="x" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="30" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="17" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="116" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="42" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="176" />
<hkern g1="quotedbl,quotesingle" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="30" />
<hkern g1="quotedbl,quotesingle" 	g2="period,ellipsis" 	k="209" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="144" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="quotesinglbase,quotedblbase" 	k="209" />
<hkern g1="l,uniFB02,uniFB04" 	g2="V" 	k="6" />
<hkern g1="l,uniFB02,uniFB04" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="l,uniFB02,uniFB04" 	g2="X" 	k="7" />
<hkern g1="l,uniFB02,uniFB04" 	g2="W" 	k="10" />
<hkern g1="l,uniFB02,uniFB04" 	g2="S" 	k="10" />
<hkern g1="l,uniFB02,uniFB04" 	g2="T" 	k="17" />
<hkern g1="l,uniFB02,uniFB04" 	g2="J" 	k="6" />
<hkern g1="l,uniFB02,uniFB04" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="15" />
<hkern g1="l,uniFB02,uniFB04" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="l,uniFB02,uniFB04" 	g2="Y,Yacute,Ydieresis" 	k="6" />
<hkern g1="l,uniFB02,uniFB04" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="l,uniFB02,uniFB04" 	g2="Z" 	k="15" />
</font>
</defs></svg> 