{"name": "node", "version": "1.0.0", "description": "", "main": "gulpfile.js", "scripts": {"coverage": "vitest run --config ./vitest-coverage.config.ts", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui --config ./vitest-coverage.config.ts", "start": "node ./build/index.js", "lint": "eslint \"**/*.{js,ts,tsx}\" --quiet"}, "author": "CardCastle", "license": "UNLISCENCED", "private": true, "dependencies": {"@bugsnag/core": "^7.16.1", "@bugsnag/js": "^7.16.1", "@bugsnag/plugin-express": "^7.16.1", "@bugsnag/plugin-react": "^7.16.1", "@faker-js/faker": "^8.4.1", "@iconify/icons-ic": "^1.2.7", "@iconify/react": "^3.2.2", "@paypal/react-paypal-js": "^7.8.1", "@types/accounting": "^0.3.29", "@types/clamp": "^1.0.1", "@types/classnames": "^2.2.9", "@types/clipboard": "^1.5.36", "@types/cookie-parser": "^1.4.1", "@types/ejs": "^2.6.0", "@types/express": "^4.16.0", "@types/express-serve-static-core": "^4.16.0", "@types/facebook-pixel": "0.0.18", "@types/fb": "0.0.21", "@types/fbemitter": "^2.0.32", "@types/fixed-data-table": "^0.6.34", "@types/flux": "0.0.32", "@types/google.analytics": "0.0.28", "@types/history": "^4.7.2", "@types/jquery": "^2.0.49", "@types/jqueryui": "^1.12.4", "@types/jspdf": "^1.1.31", "@types/keymaster": "^1.6.28", "@types/mime": "0.0.29", "@types/moment": "^2.13.0", "@types/moment-timezone": "^0.2.35", "@types/morgan": "^1.7.35", "@types/node": "^6.0.117", "@types/nprogress": "0.0.29", "@types/plotly.js": "^1.54.10", "@types/react": "16.9.11", "@types/react-dom": "^16.0.3", "@types/react-helmet": "^5.0.3", "@types/react-image-crop": "^8.1.2", "@types/react-input-autosize": "^2.0.0", "@types/react-router-dom": "^4.2.3", "@types/react-select": "^4.0.18", "@types/react-sticky": "^6.0.3", "@types/serve-static": "^1.13.2", "@types/set-interval-async": "^1.0.0", "@types/stripe-checkout": "^1.0.1", "@types/superagent": "^4.1.3", "accounting": "^0.4.1", "browserify": "^13.1.1", "bubleify": "^2.0.0", "chai": "^3.5.0", "chai-http": "^3.0.0", "clamp": "^1.0.1", "classnames": "^2.2.6", "clipboard": "^1.5.16", "cookie-parser": "^1.4.6", "create-react-class": "^15.6.3", "currency-symbol-map": "^3.1.0", "del": "^2.2.2", "ejs": "^2.6.1", "express": "^4.16.3", "fb": "^1.1.1", "flux": "^2.1.1", "glslify": "^7.0.0", "gulp": "^5.0.0", "gulp-autoprefixer": "^9.0.0", "gulp-clean-css": "^4.3.0", "gulp-nodemon": "^2.5.0", "gulp-sass": "^6.0.1", "gulp-sourcemaps": "^3.0.0", "gulp-streamify": "^1.0.2", "gulp-typescript": "^6.0.0-alpha.1", "gulp-uglify": "^3.0.2", "history": "^4.7.2", "http-proxy": "^1.18.1", "immutable": "^3.8.2", "isemail": "^3.2.0", "jspdf": "^1.4.1", "moment": "^2.22.2", "moment-timezone": "^0.5.21", "morgan": "^1.9.1", "node-jsx": "^0.13.3", "path": "^0.12.7", "plotly.js": "^1.58.4", "prettier-plugin-organize-imports": "^3.2.4", "raf": "^3.4.1", "react": "16.9", "react-currency-input-field": "^3.4.2", "react-dom": "16.9", "react-dropzone": "^10.0.4", "react-helmet": "^5.2.0", "react-image-crop": "^8.4.0", "react-input-autosize": "^2.2.1", "react-pay-icons": "^1.1.0", "react-router-dom": "^4.2.2", "react-select": "4.3.0", "react-sticky": "^6.0.3", "react-truncate-markup": "^5.1.0", "react-waypoint": "^10.3.0", "sass": "^1.87.0", "set-interval-async": "^2.0.3", "source-map": "^0.7.4", "stack-trace": "0.0.10", "static-asset": "^0.6.0", "stripe-checkout": "^1.0.9", "superagent": "^6.1.0", "typescript": "^5.8.3", "uglify-es": "^3.3.9", "vinyl-buffer": "^1.0.1", "vinyl-source-stream": "^2.0.0"}, "resolutions": {"@types/react": "16.9.11", "@types/istanbul-lib-report": "3.0.0", "colors": "1.4.0"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "12.1.3", "@testing-library/user-event": "^14.6.1", "@types/http-proxy": "^1.17.16", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.13.0", "@vitest/coverage-v8": "3.1.4", "@vitest/ui": "^3.1.4", "dotenv": "^16.0.0", "eslint": "^8.50.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.3", "jsdom": "^26.1.0", "prettier": "^2.5.1", "tslint": "^6.1.3", "vitest": "^3.1.4", "vitest-dom": "^0.1.1", "yarn-check": "^0.0.3"}}