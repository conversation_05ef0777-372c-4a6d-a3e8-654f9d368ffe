# CardCastle - Node

## Installation

- Install Yarn `brew install yarn`
- Install global dependencies `yarn global add gulp`
- Install dependencies `yarn install`
- Add `cardcastle.test` to `/etc/hosts`

```
127.0.0.1 cardcastle.test
::1 cardcastle.test
```

## Configuring your environment

- Copy the `.env.template` file to `.env`
- Fill out the values
- If your application is crashing due to memory allocation issues, consider setting `NODE_OPTIONS="--max-old-space-size=SIZE"`
  - <https://nodejs.org/api/cli.html#--max-old-space-sizesize-in-megabytes>
  - Check current value with `node -e "console.log(require('v8').getHeapStatistics().heap_size_limit / (1024 * 1024))"`

## Running

- Development `yarn gulp`
- Production `yarn start`

**Note:** If cannot run a local backend instance, use `export USE_STAGING=true` to point to <PERSON><PERSON>

## Testing

- Run test suite `yarn test`