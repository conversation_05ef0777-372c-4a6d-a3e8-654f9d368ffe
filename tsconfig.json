{"compilerOptions": {"target": "es6", "module": "commonjs", "moduleResolution": "node", "isolatedModules": false, "jsx": "react", "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": false, "noImplicitAny": true, "removeComments": true, "noLib": false, "preserveConstEnums": true, "outDir": "build/", "strictNullChecks": true, "noErrorTruncation": true, "esModuleInterop": true, "skipLibCheck": true}, "include": ["src/**/*", "tests"], "exclude": ["node_modules"], "compileOnSave": false, "buildOnSave": false, "atom": {"rewriteTsconfig": false}}