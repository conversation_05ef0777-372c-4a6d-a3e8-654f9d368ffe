/// <reference types="vitest" />
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [],
  test: {
    clearMocks: true,
    globals: true,
    include: ['./tests/**/*.{test,spec}.{js,ts,jsx,tsx}', './src/**/*.{test,spec}.{js,ts,jsx,tsx}'],
    exclude: ['./tests/setup.ts'],
    environment: 'jsdom',
    setupFiles: './tests/setup.ts',
  },
});
