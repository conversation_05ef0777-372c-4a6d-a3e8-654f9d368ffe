module.exports = {
  parser: '@typescript-eslint/parser',
  extends: ['plugin:react/recommended', 'plugin:@typescript-eslint/recommended', 'plugin:prettier/recommended'],
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: 'module',
  },
  rules: {
    '@typescript-eslint/ban-ts-comment': 'off', // Only used for Records.ts to circumvent messy generic typing
    '@typescript-eslint/explicit-function-return-type': 'off', // disable explicit function return types
    '@typescript-eslint/interface-name-prefix': 'off', // allow interface prefixes like IProps
    '@typescript-eslint/no-empty-interface': 'off', // allow empty interfaces
    '@typescript-eslint/no-explicit-any': 'off', // allow explicit any
    '@typescript-eslint/no-non-null-assertion': 'off', // allow use of ! to hint at not null
    '@typescript-eslint/no-unused-vars': 'off', // ignore unused vars
    '@typescript-eslint/no-empty-function': 'off', // ignore empty function defs
    '@typescript-eslint/no-use-before-define': 'off', // ignore function use before definition
    '@typescript-eslint/camelcase': 'off', // ignore non camel case
    '@typescript-eslint/no-var-requires': 'off', // allow require syntax
    '@typescript-eslint/no-this-alias': 'off', // allow alias of 'this'
    'react/prop-types': 'off', // turn off proptypes
    'react/no-deprecated': 'off', // hide react deprecations
    'react/no-unescaped-entities': 'off', // warn for unescaped html
    'react/jsx-no-target-blank': 'off', // turn of security error
    'react/display-name': 'off', // requirement for display name
    'react/no-find-dom-node': 'off', // allow use of React.findDOMNode
    'react/no-string-refs': 'off', // string refs are deprecated
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
};
